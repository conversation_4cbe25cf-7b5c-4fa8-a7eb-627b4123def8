package assumerole

import (
	"errors"
	"log"
)

func GetAptAssumeRole(purpose string, env string, role string) (string, error) {
	switch env {
	case "cx-uat":
		return "arn:aws:iam::743500974312:role/nightly-maintenance-role", nil
	case "risk-uat":
		return "arn:aws:iam::974376127827:role/nightly-maintenance-role", nil
	default:
		log.Printf("Env not supported: %s", env)
		return "", errors.New("unsupported env for assume role")
	}
}
