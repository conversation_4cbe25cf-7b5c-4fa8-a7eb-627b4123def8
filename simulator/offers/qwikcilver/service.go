package qwikcilver

import (
	"net/http"
	"time"

	vendorsPb "github.com/epifi/gamma/api/vendors/qwikcilver"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
)

const (
	qwikcilverCompleteOrderStatus = "COMPLETE"
)

type Service struct{}

func NewService() *Service {
	return &Service{}
}

func (s Service) GetCategoryDetails(w http.ResponseWriter, _ *http.Request) {
	res := &vendorsPb.GetCategoryResponse{
		Name: "simulator-category-1",
	}
	marshalledRes, err := protojson.Marshal(res)
	if err != nil {
		logger.ErrorNoCtx("error in marshalling res", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(marshalledRes)
}

func (s Service) GetProductList(w http.ResponseWriter, _ *http.Request) {
	res := &vendorsPb.GetProductListResponse{
		Products: []*vendorsPb.GetProductListResponse_Product{
			{
				Sku:      "CNPIN",
				Name:     "Simulator EGV for testing",
				MinPrice: "0",
				MaxPrice: "100000",
			},
		},
	}
	marshalledRes, err := protojson.Marshal(res)
	if err != nil {
		logger.ErrorNoCtx("error in marshalling res", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(marshalledRes)
}

func (s Service) GetProductDetails(w http.ResponseWriter, _ *http.Request) {
	res := &vendorsPb.GetProductResponse{
		Sku:  "CNPIN",
		Name: "Simulator EGV for testing",
	}
	marshalledRes, err := protojson.Marshal(res)
	if err != nil {
		logger.ErrorNoCtx("error in marshalling res", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(marshalledRes)
}

func (s Service) CreateOrder(w http.ResponseWriter, _ *http.Request) {
	res := &vendorsPb.CreateOrderResponse{
		Status:  qwikcilverCompleteOrderStatus,
		OrderId: uuid.NewString(),
		Cards: []*vendorsPb.Card{
			{
				CardNumber: "8090920011482020",
				CardPin:    "168371",
				Validity:   time.Now().AddDate(2, 0, 0).Format(time.RFC3339),
			},
		},
	}
	marshalledRes, err := protojson.Marshal(res)
	if err != nil {
		logger.ErrorNoCtx("error in marshalling res", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(marshalledRes)
}

// GetOrderStatus will only return response corresponding to "NotFound" VG status
func (s Service) GetOrderStatus(w http.ResponseWriter, _ *http.Request) {
	res := &vendorsPb.GetOrderStatusResponse{
		Code: 5320,
	}
	marshalledRes, err := protojson.Marshal(res)
	if err != nil {
		logger.ErrorNoCtx("error in marshalling res", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusBadRequest)
	_, _ = w.Write(marshalledRes)
}

func (s Service) GetActivatedCardDetails(w http.ResponseWriter, _ *http.Request) {
	res := &vendorsPb.GetActivatedCardsResponse{
		Cards: []*vendorsPb.Card{
			{
				CardNumber: "8090920011482020",
				CardPin:    "168371",
				Validity:   time.Now().AddDate(2, 0, 0).Format(time.RFC3339),
			},
		},
		Status: qwikcilverCompleteOrderStatus,
	}
	marshalledRes, err := protojson.Marshal(res)
	if err != nil {
		logger.ErrorNoCtx("error in marshalling res", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(marshalledRes)
}

func (s Service) GetAccessToken(w http.ResponseWriter, _ *http.Request) {
	res := &vendorsPb.BearerTokenResponse{
		BearerToken: "simulated-bearer-token",
	}
	marshalledRes, err := protojson.Marshal(res)
	if err != nil {
		logger.ErrorNoCtx("error in marshalling res", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(marshalledRes)
}

func (s Service) GetAuthorizationCode(w http.ResponseWriter, _ *http.Request) {
	res := &vendorsPb.AuthorizationCodeResponse{
		AuthorizationCode: "simulated-authorization-code",
	}
	marshalledRes, err := protojson.Marshal(res)
	if err != nil {
		logger.ErrorNoCtx("error in marshalling res", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(marshalledRes)
}
