package federal

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	cmap "github.com/orcaman/concurrent-map"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	authFederal "github.com/epifi/gamma/api/simulator/openbanking/auth/federal"
	"github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/be-common/pkg/logger"
	federalPkg "github.com/epifi/gamma/pkg/vendors/federal"
	"github.com/epifi/gamma/simulator"
	"github.com/epifi/gamma/simulator/config"
	"github.com/epifi/gamma/simulator/dao"
	"github.com/epifi/gamma/simulator/dao/model"
	simulatorOpbProcessor "github.com/epifi/gamma/simulator/helper"
)

func NewAuthService(config *config.Config, custSvc *simulator.CustomerService, client *http.Client, accountDao dao.AccountDao, simulatorOpbProcessor simulatorOpbProcessor.SimulatorOpenbankingProcessor) *AuthService {
	return &AuthService{
		CustSvc:               custSvc,
		config:                config,
		client:                client,
		accountDao:            accountDao,
		simulatorOpbProcessor: simulatorOpbProcessor,
	}
}

const (
	DeviceRegistrationSuccess        = "000"
	DeviceRegistrationInvalidInput   = "OBE0003"
	StatusDeviceAlreadyRegistered    = "OBE0031"
	DeviceReRegCallbackDelayDuration = 1 * time.Second
	suspectStateReRegRequestId       = "suspectStateReRegRequestId"
	testDeviceIdForWrongAtmPin       = "testDeviceIdForWrongAtmPin"
	testDeviceIdForInactiveCard      = "testDeviceIdForInactiveCard"
	testDeviceIdForAutoRetries       = "testDeviceIdForAutoRetries"

	// phone number for which "Device Already Registered" response has to be returned.
	deviceAlreadyRegPhoneFixture = "************"

	DevRegPayloadExpired  = "OBE0042"
	DevRegSMSNotReceived  = "OBE0005"
	DevRegPayloadMismatch = "OBE0158"
	DevRegMobileMismatch  = "OBE0157"

	// Phone numbers for simulating failure cases
	phForWrongPin    = "************"
	phForCardTxnReqd = "************"
	phForSMSError    = "************"

	DevReRegPayloadMismatch = "OBE0158"
	DevReRegNoSMSReceived   = "OBE0005"

	DevReRegCardInactive = "OBE0118"
	DevReRegWrongATMPin  = "OBE0119"
)

var (
	noOfRetryForDeviceIdMapping = cmap.New() // map[string]int32
	RetryForReRegistration      = cmap.New()

	// assert unique request ids in RegisterDevice request
	regDeviceUniqReqIds = cmap.New()
	devReRegReqIDs      = cmap.New()
)

type AuthService struct {
	config                *config.Config
	CustSvc               *simulator.CustomerService
	client                *http.Client
	accountDao            dao.AccountDao
	simulatorOpbProcessor simulatorOpbProcessor.SimulatorOpenbankingProcessor
}

//nolint:dupl,funlen
func (s *AuthService) DeviceReRegistration(ctx context.Context, in *federal.DeviceReRegistrationRequest) (*federal.DeviceReRegistrationResponse, error) {
	changeEntities := strings.Split(in.GetChangeEntity(), ",")
	if exist := devReRegReqIDs.Has(in.GetRequestId()); exist {
		logger.InfoNoCtx("duplicate call received", zap.String(logger.REQUEST_ID, in.GetRequestId()))
		return &federal.DeviceReRegistrationResponse{
			ResponseCode:   "OBE0003",
			ResponseReason: "duplicate requestID",
		}, nil
	}
	devReRegReqIDs.Set(in.GetRequestId(), true)

	if isInvalidCredblock(in) {
		return &federal.DeviceReRegistrationResponse{
			ResponseCode:   "OBE0066",
			ResponseReason: fmt.Sprintf("Invalid Cred Block"),
		}, nil
	}

	for _, e := range changeEntities {
		if strings.EqualFold(strings.ToLower(e), "mobilenumber") {
			if err := s.CustSvc.UpdateCustomer(model.Customer{
				ID:      in.GetCustomerId(),
				PhoneNo: in.GetMobileNumber(),
			}, []model.CustomerMask{
				model.PHONE_NO,
			}); err != nil {
				return &federal.DeviceReRegistrationResponse{
					ResponseCode:   "OBE0007",
					ResponseReason: fmt.Sprintf("error in update cust: %v", err),
				}, nil

			}
		}
	}

	// create some new TPAP accounts for the updated phone number of the customer.
	if err := s.simulatorOpbProcessor.CreateTPAPAccounts(ctx, in.GetMobileNumber(), 8); err != nil {
		logger.Error(ctx, "error in creating new TPAP accounts for the updated phone number of the customer",
			zap.String(logger.CUSTOMER_ID, in.GetCustomerId()), zap.Error(err))
		return &federal.DeviceReRegistrationResponse{
			ResponseCode:   "OBE0007",
			ResponseReason: fmt.Sprintf("error in creating new TPAP accounts for the updated phone number of the customer: %v", err),
		}, nil
	}

	accountRes, err := s.accountDao.GetAccountByCustomerID(context.Background(), in.GetCustomerId())
	if err != nil {
		logger.Error(ctx, "error in getting account by customer id", zap.String(logger.CUSTOMER_ID, in.GetCustomerId()), zap.Error(err))
		return &federal.DeviceReRegistrationResponse{
			ResponseCode:   "OBE0007",
			ResponseReason: fmt.Sprintf("error in getting customer account: %v", err),
		}, nil
	}

	if err := s.accountDao.UpdateUpiPinSetForAccountId(ctx, accountRes.AccountId, false); err != nil {
		logger.Error(ctx, "error in updating upi pin set status for account ", zap.String(logger.ACCOUNT_ID, accountRes.AccountId), zap.Error(err))
		return &federal.DeviceReRegistrationResponse{
			ResponseCode:   "OBE0007",
			ResponseReason: fmt.Sprintf("error in getting update account pin set state: %v", err),
		}, nil
	}

	if in.GetRequestId() == suspectStateReRegRequestId {
		return &federal.DeviceReRegistrationResponse{
			ResponseCode:   "OBE0095",
			ResponseReason: "Mobile change request submitted to CBS",
			DeviceToken:    in.GetDeviceToken(),
			ResponseAction: "SUSPECT",
		}, nil
	}
	deviceToken := uuid.New().String()
	callbackReq := &federal.DeviceReRegistrationCallback{
		ResponseCode:   "OBI0001",
		ResponseReason: "Registered Successfully",
		DeviceToken:    deviceToken,
		RequestId:      in.RequestId,
		ResponseAction: "SUCCESS",
	}

	if in.DeviceId == testDeviceIdForWrongAtmPin {
		var attempt int32
		if val, err := RetryForReRegistration.Get(in.DeviceId); !err {
			RetryForReRegistration.Set(in.DeviceId, attempt) // Set attempt with its Zeroth value
			logger.InfoNoCtx("0th try for ", zap.String("device", in.DeviceId))
		} else {
			attempt = val.(int32) + 1
			RetryForReRegistration.Set(in.DeviceId, attempt)
			logger.InfoNoCtx("Number of retries for device id", zap.Int32("retrycount", attempt))
		}
		switch attempt {
		case 0:
			callbackReq = &federal.DeviceReRegistrationCallback{
				ResponseCode:   DevReRegWrongATMPin,
				ResponseReason: "Incorrect personal identification number",
				DeviceToken:    deviceToken,
				RequestId:      in.RequestId,
				ResponseAction: federalPkg.FailureResponseAction,
			}
		default:
			break
		}
	}

	if in.DeviceId == testDeviceIdForInactiveCard {
		var attempt int32
		if val, err := RetryForReRegistration.Get(in.DeviceId); !err {
			RetryForReRegistration.Set(in.DeviceId, attempt) // Set attempt with its Zeroth value
			logger.InfoNoCtx("0th try for ", zap.String("device", in.DeviceId))
		} else {
			attempt = val.(int32) + 1
			RetryForReRegistration.Set(in.DeviceId, attempt)
			logger.InfoNoCtx("Number of retries for device id", zap.Int32("retrycount", attempt))
		}
		switch attempt {
		case 0:
			callbackReq = &federal.DeviceReRegistrationCallback{
				ResponseCode:   DevReRegCardInactive,
				ResponseReason: "User card is inactive",
				DeviceToken:    deviceToken,
				RequestId:      in.RequestId,
				ResponseAction: federalPkg.FailureResponseAction,
			}
		default:
			break
		}
	}

	if in.DeviceId == testDeviceIdForAutoRetries {
		var attempt int32
		if val, err := RetryForReRegistration.Get(in.DeviceId); !err {
			RetryForReRegistration.Set(in.DeviceId, attempt) // Set attempt with its Zeroth value
			logger.InfoNoCtx("0th try for ", zap.String("device", in.DeviceId))
		} else {
			attempt = val.(int32) + 1
			RetryForReRegistration.Set(in.DeviceId, attempt)
			logger.InfoNoCtx("Number of retries for device id", zap.Int32("retrycount", attempt))
		}
		res := &federal.DeviceReRegistrationResponse{}
		switch attempt {
		case 0, 1:
			res.ResponseReason = "No SMS Received"
			res.ResponseCode = DevReRegNoSMSReceived
			return res, nil
		default:
			logger.InfoNoCtx("escaping error trap", zap.String("reRegRetry", strconv.Itoa(int(attempt))))
		}
	}

	if in.MobileNumber == phForWrongPin {
		callbackReq = &federal.DeviceReRegistrationCallback{
			ResponseCode:   "OBE0119",
			ResponseReason: "Incorrect personal identification number",
			DeviceToken:    deviceToken,
			RequestId:      in.RequestId,
			ResponseAction: federalPkg.FailureResponseAction,
		}
	}
	if in.MobileNumber == phForSMSError {
		var attempt int32
		if val, err := RetryForReRegistration.Get(in.DeviceId); !err {
			RetryForReRegistration.Set(in.DeviceId, attempt) // Set attempt with its Zeroth value
			logger.InfoNoCtx("0th try for ", zap.String("device", in.DeviceId))
		} else {
			attempt = val.(int32) + 1
			RetryForReRegistration.Set(in.DeviceId, attempt)
			logger.InfoNoCtx("Number of retries for device id", zap.Int32("retrycount", attempt))
		}
		res := &federal.DeviceReRegistrationResponse{}
		switch attempt {
		case 0:
			res.ResponseReason = "Encrypted Payload Mismatch"
			res.ResponseCode = DevReRegPayloadMismatch
			return res, nil
		case 1:
			res.ResponseReason = "No SMS received from the specific mob number or with the given payload"
			res.ResponseCode = DevReRegNoSMSReceived
			return res, nil
		case 2:
			res.ResponseReason = "Encrypted Payload Mismatch"
			res.ResponseCode = DevReRegPayloadMismatch
			return res, nil
		default:
			logger.InfoNoCtx("escaping error trap", zap.String("reRegRetry", strconv.Itoa(int(attempt))))
		}
	}
	if in.MobileNumber == phForCardTxnReqd {
		callbackReq = &federal.DeviceReRegistrationCallback{
			ResponseCode:   "OBE0130",
			ResponseReason: "Pin Validation not allowed after Pin Reset (Should do some other card txn before this)",
			DeviceToken:    deviceToken,
			RequestId:      in.RequestId,
			ResponseAction: federalPkg.FailureResponseAction,
		}
	}
	go func() {
		time.Sleep(DeviceReRegCallbackDelayDuration)
		s.performDeviceReRegCallbackReq(ctx, in.RespUrl, callbackReq)
	}()
	fmt.Println(fmt.Sprintf("Device Token : %v", deviceToken))
	return &federal.DeviceReRegistrationResponse{
		ResponseCode:   "OBE0000",
		ResponseReason: "Successfully updated",
		DeviceToken:    deviceToken,
	}, nil
}

func (s *AuthService) performDeviceReRegCallbackReq(ctx context.Context, respUrl string, callbackReq *federal.DeviceReRegistrationCallback) {
	reqBody, err := protojson.Marshal(callbackReq)
	if err != nil {
		logger.Error(ctx, "error marshaling callback request", zap.String("requestId", callbackReq.RequestId), zap.Error(err))
	}
	res, err := s.client.Post(respUrl, "application/json", bytes.NewBuffer(reqBody))
	if err != nil {
		logger.Error(ctx, "error encountered calling http endpoint", zap.String("requestId", callbackReq.RequestId), zap.Error(err))
	}
	defer func() {
		err = res.Body.Close()
		if err != nil {
			logger.Error(ctx, "failed to close response body", zap.Error(err))
		}
	}()
}

func (s *AuthService) DeviceDeRegistration(ctx context.Context, in *federal.DeviceDeRegistrationRequest) (*federal.DeviceDeRegistrationResponse, error) {
	return &federal.DeviceDeRegistrationResponse{
		Response: "000",
		Reason:   "User Deactivated Successfully",
	}, nil
}

func (s *AuthService) GenerateOTP(ctx context.Context, req *federal.GenerateOTPRequest) (*federal.GenerateOTPResponse, error) {
	return &federal.GenerateOTPResponse{
		RequestId: req.GetRequestId(),
		Response:  "000",
		Reason:    "RECEIVED SUCCESSFULLY",
	}, nil
}

//nolint:funlen
func (s *AuthService) RegisterDevice(ctx context.Context, req *federal.RegisterDeviceRequest) (*federal.RegisterDeviceResponse, error) {
	res := &federal.RegisterDeviceResponse{
		SenderCode:    req.SenderCode,
		RequestId:     req.RequestId,
		DeviceToken:   "",
		TranTimeStamp: timestamppb.Now().String(),
		ResponseCode:  DeviceRegistrationSuccess,
		Reason:        "",
	}
	if strings.HasSuffix(req.GetEncryptedPayload(), "alreadyExists") {
		res.ResponseCode = StatusDeviceAlreadyRegistered
		res.Reason = "device already registered"
		return res, nil
	}
	if req.MobileNumber == "" || req.EncryptedPayload == "" || req.DeviceId == "" || req.UserProfileId == "" {
		logger.Error(ctx, "Bad request. All fields should be non empty in the request.", zap.String(logger.DEVICE_ID, req.GetDeviceId()))
		res.ResponseCode = DeviceRegistrationInvalidInput
		return res, nil
	}
	// TODO (keerthana): update db here, and check for duplicate request for DeviceId

	// validate duplicate request id
	if regDeviceUniqReqIds.Has(req.GetRequestId()) {
		logger.Error(ctx, "duplicate request id in register device", zap.String("reqId", req.GetRequestId()))
		res.Reason = "Duplicate RequestId"
		res.ResponseCode = DeviceRegistrationInvalidInput
		return res, nil
	}
	regDeviceUniqReqIds.Set(req.GetRequestId(), true)

	// Device already registered
	if req.MobileNumber == deviceAlreadyRegPhoneFixture {
		return &federal.RegisterDeviceResponse{
			SenderCode:    req.SenderCode,
			RequestId:     req.RequestId,
			TranTimeStamp: timestamppb.Now().String(),
			ResponseCode:  StatusDeviceAlreadyRegistered,
			Reason:        "device already registered",
		}, nil
	}

	// creates TPAP accounts for user, kept here so that it can be used for both fi lite flow and normal flow.
	// for using tpap, device registration is mandatory so a fi lite user also has to do device registration for using payments
	// In case of normal flow, device registration is mandatory while onboarding itself so it honors that as well.
	err := s.simulatorOpbProcessor.CreateTPAPAccounts(ctx, req.GetMobileNumber(), 8)
	if err != nil {
		logger.Error(ctx, "failed to create TPAP accounts", zap.Error(err))
		return nil, err
	}

	var attempt int32
	if val, err := noOfRetryForDeviceIdMapping.Get(req.DeviceId); !err {
		noOfRetryForDeviceIdMapping.Set(req.DeviceId, attempt) // Set attempt with its Zeroth value
		logger.InfoNoCtx("0th try for ", zap.String("device", req.DeviceId))
	} else {
		attempt = val.(int32) + 1
		noOfRetryForDeviceIdMapping.Set(req.DeviceId, attempt)
		logger.InfoNoCtx("Number of retries for device id", zap.Int32("retrycount", attempt))
	}

	switch attempt {
	case 0:
		res.Reason = "Success"
		res.ResponseCode = DeviceRegistrationSuccess
		token := uuid.New().String()
		res.DeviceToken = token
		return res, nil
	case 1:
		res.Reason = "Phone number mismatch"
		res.ResponseCode = DevRegPayloadMismatch
		return res, nil
	case 2:
		res.Reason = "No SMS received"
		res.ResponseCode = DevRegSMSNotReceived
		return res, nil
	default:
		res.Reason = "Success"
		res.ResponseCode = DeviceRegistrationSuccess
		token := uuid.New().String()
		res.DeviceToken = token
		return res, nil
	}
}

func (s *AuthService) DeviceReactivation(_ context.Context, _ *federal.DeviceReactivationRequest) (*federal.DeviceReactivationResponse, error) {
	return &federal.DeviceReactivationResponse{
		Response: "000",
		Reason:   "User Reactivated Successfully",
	}, nil
}

func (s *AuthService) AcknowledgeDeviceRegistrationSMS(ctx context.Context, req *authFederal.AcknowledgeDeviceRegistrationSMSRequest) (*authFederal.AcknowledgeDeviceRegistrationSMSResponse, error) {
	logger.Debug(ctx, "trying to trigger sms ack...")
	reqBody, err := protojson.Marshal(req.GetDeviceRegistrationSMSAcknowledgement())
	if err != nil {
		logger.Error(ctx, "error marshalling sms notification request", zap.Error(err))
	}
	logger.Debug(ctx, "sending sms ack notification", zap.String(logger.PHONE_NUMBER, req.GetDeviceRegistrationSMSAcknowledgement().GetMobileNumber()))
	postURL := s.config.DeviceRegistration.SMSAcknowledgementURL
	res, err := s.client.Post(postURL, "application/json", bytes.NewBuffer(reqBody))
	if err != nil {
		logger.Error(ctx, "error encountered calling http endpoint", zap.Error(err))
	}
	defer func() {
		err = res.Body.Close()
		if err != nil {
			logger.Error(ctx, "failed to close response body", zap.Error(err))
		}
	}()
	return &authFederal.AcknowledgeDeviceRegistrationSMSResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func isInvalidCredblock(in *federal.DeviceReRegistrationRequest) bool {
	return in.GetPinValidationFlag() == "Y" && in.GetCredBlock() == ""
}
