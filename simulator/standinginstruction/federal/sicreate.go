package federal

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/golang/protobuf/ptypes"
	"go.uber.org/zap"

	standingInstructionPb "github.com/epifi/gamma/api/simulator/standinginstruction/federal"
	"github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/simulator"
)

// nolint: funlen
func (s *StandingInstructionService) SICreate(ctx context.Context, req *federal.SICreateRequest) (*federal.SICreateResponse, error) {
	res := &federal.SICreateResponse{
		SenderCode:     req.GetSenderCode(),
		RequestId:      req.GetRequestId(),
		DeviceToken:    req.GetDeviceToken(),
		TransTimeStamp: time.Now().Format("2006-01-02 15:04:05.000000"),
	}
	if !s.ValidateRemitterCredentials(req.GetSenderCode(), req.GetServiceAccessCode(), req.GetServiceAccessId()) {
		res.ResponseCode = invalidSenderDetailsResponseCode
		res.Reason = invalidSenderDetailsResponseDesc
		return res, nil
	}
	if !s.ValidateRemitterDeviceCredentials(req.GetDeviceId(), req.GetDeviceToken()) {
		res.ResponseCode = invalidDeviceDetailsResponseCode
		res.Reason = invalidDeviceDetailsResponseDesc
		return res, nil
	}
	amount, err := simulator.GetAmountInPaisa(req.GetRecurrencePayment().GetAmountMaxLimit())
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to parse amount to paisa, %s", req.GetRecurrencePayment().GetAmountMaxLimit()), zap.Error(err))
		res.ResponseCode = invalidAmountErrCode
		res.Reason = invalidAmountErrDesc
		return res, nil
	}
	numberOfOcurrance, err := strconv.ParseInt(req.GetRecurrencePayment().GetNumberOfOccurrence(), 10, 32)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to parse number of occurrence, %s", req.GetRecurrencePayment().GetNumberOfOccurrence()), zap.Error(err))
		res.ResponseCode = invalidNumberOfOcuuranceCode
		res.Reason = invalidNumberOfOcuuranceDesc
		return res, nil
	}

	SIToken := getSIToken("SITKN")
	beneficiary := req.GetBeneficiary()

	standingInstruction := standingInstructionPb.StandingInstructions{
		SiToken:               SIToken,
		TransactionType:       standingInstructionPb.TransactionType(standingInstructionPb.TransactionType_value[req.GetTransactionType()]),
		RemitterAccountNumber: req.GetRemitter().GetAccountNumber(),
		BeneficiaryDetails: &standingInstructionPb.BeneficiaryDetails{
			Name:          beneficiary.GetName(),
			AccountNumber: beneficiary.GetAccountNumber(),
			UpiVirtualId:  beneficiary.GetUpiVirtualId(),
			Ifsc:          beneficiary.GetIfsc(),
			Mobile:        beneficiary.GetMobile(),
			Email:         beneficiary.GetEmail(),
		},
		AmountMaxLimit:     amount,
		TransactionAmount:  0,
		RecurrencePattern:  standingInstructionPb.RecurrencePattern(standingInstructionPb.RecurrencePattern_value[req.GetRecurrencePayment().GetRecurrencePattern()]),
		StartDate:          datetime.DateFromString(req.GetRecurrencePayment().GetValidityStartDate()),
		EndDate:            datetime.DateFromString(req.GetRecurrencePayment().GetValidityEndDate()),
		NumberOfOccurrence: int32(numberOfOcurrance),
		CurrentOccurrence:  0,
		CreatedAt:          ptypes.TimestampNow(),
		UpdatedAt:          ptypes.TimestampNow(),
		CallbackUrl:        req.GetCallbackUrl(),
		IsRevoked:          false,
	}
	if err := s.standingInstructionDao.Create(ctx, &standingInstruction); err != nil {
		logger.Error(ctx, "Error Creating Standing Instruction", zap.Error(err))
		res.ResponseCode = databaseErrorCode
		res.Reason = databaseErrorDesc
		return res, nil
	}
	res.SiToken = SIToken
	res.ResponseCode = successfulStandingInstructionCreateCode
	res.Reason = successfulStandingInstructionCreateDesc
	return res, nil
}

func getSIToken(prefix string) string {
	return fmt.Sprintf("%v%v", prefix, idgen.RandSeqDigits(12))
}
