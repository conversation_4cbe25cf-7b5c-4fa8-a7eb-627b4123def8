package userattributes

import (
	"context"

	"github.com/epifi/gamma/api/acquisition/crossattach"
)

//go:generate mockgen -source=getter.go -destination=./mocks/getter_mocks.go -package=mocks
type UserAttributesGetter interface {
	GetUserAttributesFromSource(context.Context, *GetUserAttributesFromSourceRequest) (*GetUserAttributesFromSourceResponse, error)
}

type GetUserAttributesFromSourceRequest struct {
	ActorId       string
	UserAttribute crossattach.UserAttribute
}

type GetUserAttributesFromSourceResponse struct {
	// It is possible that we may need to fetch multiple user attributes at once to avoid additional calls, so using a map to return multiple attributes
	UserAttributeValueMap map[crossattach.UserAttribute]*crossattach.UserAttributeValue
}

func (r *GetUserAttributesFromSourceRequest) GetActorId() string {
	if r != nil {
		return r.ActorId
	}
	return ""
}

func (r *GetUserAttributesFromSourceRequest) GetUserAttribute() crossattach.UserAttribute {
	if r != nil {
		return r.UserAttribute
	}
	return crossattach.UserAttribute_USER_ATTRIBUTE_UNSPECIFIED
}

func (r *GetUserAttributesFromSourceResponse) GetUserAttributeValueMap() map[crossattach.UserAttribute]*crossattach.UserAttributeValue {
	if r != nil {
		return r.UserAttributeValueMap
	}
	return nil
}
