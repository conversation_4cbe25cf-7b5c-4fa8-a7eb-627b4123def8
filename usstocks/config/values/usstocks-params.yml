Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/investment/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/investment/secure.log"
  MaxSizeInMBs: 100
  MaxBackups: 20

InstantPurchaseAmount: 0

IgnoreCanceledTxnsForInwardRemittance: true

MaxBatchSizeToCreateOrUpdateAccountActivities: 500

Flags:
  TrimDebugMessageFromStatus: true
  DisableMorningStarDataUsages: true
  EnableInwardRemittanceGstReportingFlowUsingApi: true
  # Note: This flag should be changed only when all pending inward remittance batch files are processed completely
  EnableAggregatedInwardRemittance: true
  CreateCustomDividendActivities: false
  UseBankApiForTcsCalculation: true
  EnableCatalogSearchSync: true
  EnableImpendingSavingsAccountFreezeValidation: false
  EnableModifiedTransactionRemarkForInwardRemittance: false

Tracing:
  Enable: false

USStocksAlpacaPGDB:
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

USStocksAlpacaDb:
  AppName: "usstocks"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AccountManagerConfig:
  W8BenFormVersion: "10-2021"
  MaxRetriesToInitiateAccountCreation: 3

MaxPageSize: 250

AggRemittanceTxnsMaxPageSize: 1000

AccountActivitySyncConfig:
  IsEnabled: true
  PageSize: 100
  Duration: 1080h # 45 days
  FirstActivityTs: ********** # Monday, 15 August 2022 0:00:00 GMT, First activity on Sandbox is on 24th Aug 2022
  FirmAccountIDs:
    - "ee7dd170-8000-39c2-80a2-6ce3b946e518"
    - "adeeff78-0a9f-35f8-87d2-bfe4eafe2777"

# Note: If changing this, change the corresponding min/max values in US stocks frontend config
AddFundsPreReqChecksData:
  Enabled: true
  MinAllowedAmountForAnOrder:
    CurrencyCode: "USD"
    Units: 15
    # Max amount is kept close to but less than 10L INR
  MaxAllowedAmountForAnOrder:
    CurrencyCode: "USD"
    Units: 4500

# for each slab have upper limit and associate percentage to be charged
TaxCalculationLogic:
  TCSCalculationLogic:
    TCSPercentage: 20
    TCSSlabAmount:
      CurrencyCode: "INR"
      Units: 1000000 # 10L

Zinc:
  Host: "localhost"
  Port: "4080"

CatalogSearchConfig:
  IndexName: "us_stocks_catalog"
  MappingFilePath: "mappingJson/zinc/us_stocks_catalog_index_mapping.json"
  CatalogFetchDefaultPageSize: 100

StockUniverseFilePath: "mappingCsv/stock_universe.csv"

ETFUniverseFilePath: "mappingCsv/etf_universe.csv"

MarketCalendarFilePath: "mappingJson/usstock_market_calendar.json"

PriceUpdateRateLimiterConfig:
  ResourceMap:
    price_updates_per_symbol:
      Rate: 1
      Period: 1s
  Namespace: "usstocks"

EmailBatchSize: 50

ShouldUseSellLock: true


InwardPoolAccountNumber: "**************"

InwardsRemittancePoolAccountSolId: "5555"

PriceUpdatesConnectionInfo:
  Enable: false
  VGConnectionRetryInterval: "30s" # retry after 30 seconds of VG connection failure
  # no of retries are intentionally kept higher for handling first prod push where gap between vg and investment server deployment can be more than 30 mins.
  # this should be revisited and changed to some smaller number eg: 20
  VGConnectionMaxRetries: 100  # retry for max 100 times after every 30 seconds. connection retry happens for ~ 50 mins

CommsNotification:
  NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
  BuyOrderCancelled:
    Title: "Purchase request cancelled! 👍"
    Body: "Your request to buy %s stock has been cancelled successfully"
  SellOrderCancelled:
    Title: "Sell order cancelled 👍"
    Body: "Your request to sell %s stock has been cancelled successfully"

# Configurations for Historical Stock Prices
HSPConfig:
  - PeriodName: "1D"
    AggregationTimeFrame: "1Min"
    AggregationTimeFrameDuration: "1m"
    DisplayRank: 0
    RemoveNonRegularMarketHourPrices: true
    ShouldUpdateRealtime: true
  - PeriodName: "1W"
    AggregationTimeFrame: "5Min"
    AggregationTimeFrameDuration: "5m"
    DisplayRank: 1
    RemoveNonRegularMarketHourPrices: true
    ShouldUpdateRealtime: false
  - PeriodName: "1M"
    AggregationTimeFrame: "30Min"
    AggregationTimeFrameDuration: "30m"
    DisplayRank: 2
    RemoveNonRegularMarketHourPrices: true
    ShouldUpdateRealtime: false
  - PeriodName: "1Y"
    AggregationTimeFrame: "1Day"
    AggregationTimeFrameDuration: "24h"
    DisplayRank: 3
    RemoveNonRegularMarketHourPrices: false
    ShouldUpdateRealtime: false
  - PeriodName: "5Y"
    AggregationTimeFrame: "1Week"
    AggregationTimeFrameDuration: "168h" # 7 * 24 * time.Hour
    DisplayRank: 4
    RemoveNonRegularMarketHourPrices: false
    ShouldUpdateRealtime: false

IsETFEnabled: false

BrokerFirmAccountDetailsForForeignRemittance:
  OutwardRemittanceAccount:
    AccountId: "4f53eb9f-b13d-3748-912b-a14b6fa4ed9e"
  InwardRemittanceAccount:
    AccountId: "36e23240-3cd3-3f53-a1ba-e4d06eab9866"
    BankRelationshipId: "46e23240-3cd3-3f53-a1ba-e4d06eab9866"
  RewardsFirmAccount:
    AccountId: "a5759c5a-8147-3d74-9d0c-94f78bf4bbd4"



DayTradesLimit: 3

WalletOrderEta:
  WithdrawFundsPaymentSettlement: 5
  AddFundsPaymentSettlement: 1

JournalUpdateEventsConnectionInfo:
  Enable: false
  VGConnectionRetryInterval: "10s" # retry after 10 seconds of VG connection failure
  VGConnectionMaxRetries: 100

FundTransferUpdateEventsConnectionInfo:
  Enable: false
  VGConnectionRetryInterval: "10s" # retry after 10 seconds of VG connection failure
  VGConnectionMaxRetries: 100

PgdbMigrationConf:
  UsePgdb: true
  PgdbConnAlias: "usstocks_alpaca_pgdb"

EnableZincIngestionInSync: true

SuitabilityQuestionConfigFilePath: "mappingJson/suitability_question_config.json"

GetStocksPriceSnapshotsBatchConfig:
  BatchSize: 100
  JitterSeconds: 4


USStocksRewardDetails:
  Option1StockName: "Apple"
  Amount:
    CurrencyCode: "INR"
    Units: 100000
  BgLottieUrl: "https://epifi-icons.pointz.in/usstocks_images/USSRewardsAppleStock.json"

UsStocksOpsAlertSlackChannelId: "C058MNMBMMK" # save-reports-testing

MarketIndexConfig:
  - Nasdaq100:
      DisplayName: "Nasdaq 100"
      ProxyEtf: "QQQM"
      ConstituentSymbols:
        - "PANW"
        - "CEG"
        - "ZS"
        - "MRVL"
        - "TTD"
        - "NXPI"
        - "FTNT"
        - "CRWD"
        - "NFLX"
        - "WBA"
        - "MU"
        - "PDD"
        - "ON"
        - "DASH"
        - "TSLA"
        - "AEP"
        - "ORLY"
        - "DDOG"
        - "QCOM"
        - "PYPL"
        - "EXC"
        - "ASML"
        - "TEAM"
        - "MDB"
        - "MCHP"
        - "BKNG"
        - "ABNB"
        - "DLTR"
        - "CHTR"
        - "AZN"
        - "META"
        - "XEL"
        - "ANSS"
        - "INTU"
        - "MELI"
        - "GOOG"
        - "GOOGL"
        - "CSGP"
        - "IDXX"
        - "ADSK"
        - "MDLZ"
        - "TXN"
        - "MRNA"
        - "LULU"
        - "FAST"
        - "GFS"
        - "ADI"
        - "ROST"
        - "SNPS"
        - "GEHC"
        - "BKR"
        - "MAR"
        - "KHC"
        - "CTSH"
        - "FANG"
        - "SPLK"
        - "CDW"
        - "AMAT"
        - "SBUX"
        - "AAPL"
        - "KDP"
        - "COST"
        - "PAYX"
        - "LRCX"
        - "PCAR"
        - "ISRG"
        - "VRTX"
        - "AMZN"
        - "MSFT"
        - "INTC"
        - "AVGO"
        - "CSCO"
        - "GILD"
        - "DXCM"
        - "PEP"
        - "VRSK"
        - "TMUS"
        - "MNST"
        - "CDNS"
        - "CPRT"
        - "ODFL"
        - "SIRI"
        - "ROP"
        - "CSX"
        - "CMCSA"
        - "AMD"
        - "ADP"
        - "KLAC"
        - "BIIB"
        - "HON"
        - "NVDA"
        - "ILMN"
        - "TTWO"
        - "WBD"
        - "CTAS"
        - "REGN"
        - "CCEP"
        - "ADBE"
        - "EA"
        - "WDAY"
        - "AMGN"
  - S&P500:
      DisplayName: "S&P 500"
      ProxyEtf: "VOO"
      ConstituentSymbols:
        - 'MMM'
        - 'AOS'
        - 'ABT'
        - 'ABBV'
        - 'ACN'
        - 'ADBE'
        - 'AMD'
        - 'AES'
        - 'AFL'
        - 'A'
        - 'APD'
        - 'ABNB'
        - 'AKAM'
        - 'ALB'
        - 'ARE'
        - 'ALGN'
        - 'ALLE'
        - 'LNT'
        - 'ALL'
        - 'GOOGL'
        - 'GOOG'
        - 'MO'
        - 'AMZN'
        - 'AMCR'
        - 'AEE'
        - 'AAL'
        - 'AEP'
        - 'AXP'
        - 'AIG'
        - 'AMT'
        - 'AWK'
        - 'AMP'
        - 'AME'
        - 'AMGN'
        - 'APH'
        - 'ADI'
        - 'ANSS'
        - 'AON'
        - 'APA'
        - 'AAPL'
        - 'AMAT'
        - 'APTV'
        - 'ACGL'
        - 'ADM'
        - 'ANET'
        - 'AJG'
        - 'AIZ'
        - 'T'
        - 'ATO'
        - 'ADSK'
        - 'ADP'
        - 'AZO'
        - 'AVB'
        - 'AVY'
        - 'AXON'
        - 'BKR'
        - 'BALL'
        - 'BAC'
        - 'BK'
        - 'BBWI'
        - 'BAX'
        - 'BDX'
        - 'BRK.B'
        - 'BBY'
        - 'BIO'
        - 'TECH'
        - 'BIIB'
        - 'BLK'
        - 'BX'
        - 'BA'
        - 'BKNG'
        - 'BWA'
        - 'BXP'
        - 'BSX'
        - 'BMY'
        - 'AVGO'
        - 'BR'
        - 'BRO'
        - 'BF.B'
        - 'BLDR'
        - 'BG'
        - 'CDNS'
        - 'CZR'
        - 'CPT'
        - 'CPB'
        - 'COF'
        - 'CAH'
        - 'KMX'
        - 'CCL'
        - 'CARR'
        - 'CTLT'
        - 'CAT'
        - 'CBOE'
        - 'CBRE'
        - 'CDW'
        - 'CE'
        - 'COR'
        - 'CNC'
        - 'CNP'
        - 'CF'
        - 'CHRW'
        - 'CRL'
        - 'SCHW'
        - 'CHTR'
        - 'CVX'
        - 'CMG'
        - 'CB'
        - 'CHD'
        - 'CI'
        - 'CINF'
        - 'CTAS'
        - 'CSCO'
        - 'C'
        - 'CFG'
        - 'CLX'
        - 'CME'
        - 'CMS'
        - 'KO'
        - 'CTSH'
        - 'CL'
        - 'CMCSA'
        - 'CMA'
        - 'CAG'
        - 'COP'
        - 'ED'
        - 'STZ'
        - 'CEG'
        - 'COO'
        - 'CPRT'
        - 'GLW'
        - 'CPAY'
        - 'CTVA'
        - 'CSGP'
        - 'COST'
        - 'CTRA'
        - 'CCI'
        - 'CSX'
        - 'CMI'
        - 'CVS'
        - 'DHR'
        - 'DRI'
        - 'DVA'
        - 'DAY'
        - 'DECK'
        - 'DE'
        - 'DAL'
        - 'DVN'
        - 'DXCM'
        - 'FANG'
        - 'DLR'
        - 'DFS'
        - 'DG'
        - 'DLTR'
        - 'D'
        - 'DPZ'
        - 'DOV'
        - 'DOW'
        - 'DHI'
        - 'DTE'
        - 'DUK'
        - 'DD'
        - 'EMN'
        - 'ETN'
        - 'EBAY'
        - 'ECL'
        - 'EIX'
        - 'EW'
        - 'EA'
        - 'ELV'
        - 'LLY'
        - 'EMR'
        - 'ENPH'
        - 'ETR'
        - 'EOG'
        - 'EPAM'
        - 'EQT'
        - 'EFX'
        - 'EQIX'
        - 'EQR'
        - 'ESS'
        - 'EL'
        - 'ETSY'
        - 'EG'
        - 'EVRG'
        - 'ES'
        - 'EXC'
        - 'EXPE'
        - 'EXPD'
        - 'EXR'
        - 'XOM'
        - 'FFIV'
        - 'FDS'
        - 'FICO'
        - 'FAST'
        - 'FRT'
        - 'FDX'
        - 'FIS'
        - 'FITB'
        - 'FSLR'
        - 'FE'
        - 'FI'
        - 'FMC'
        - 'F'
        - 'FTNT'
        - 'FTV'
        - 'FOXA'
        - 'FOX'
        - 'BEN'
        - 'FCX'
        - 'GRMN'
        - 'IT'
        - 'GE'
        - 'GEHC'
        - 'GEV'
        - 'GEN'
        - 'GNRC'
        - 'GD'
        - 'GIS'
        - 'GM'
        - 'GPC'
        - 'GILD'
        - 'GPN'
        - 'GL'
        - 'GS'
        - 'HAL'
        - 'HIG'
        - 'HAS'
        - 'HCA'
        - 'DOC'
        - 'HSIC'
        - 'HSY'
        - 'HES'
        - 'HPE'
        - 'HLT'
        - 'HOLX'
        - 'HD'
        - 'HON'
        - 'HRL'
        - 'HST'
        - 'HWM'
        - 'HPQ'
        - 'HUBB'
        - 'HUM'
        - 'HBAN'
        - 'HII'
        - 'IBM'
        - 'IEX'
        - 'IDXX'
        - 'ITW'
        - 'ILMN'
        - 'INCY'
        - 'IR'
        - 'PODD'
        - 'INTC'
        - 'ICE'
        - 'IFF'
        - 'IP'
        - 'IPG'
        - 'INTU'
        - 'ISRG'
        - 'IVZ'
        - 'INVH'
        - 'IQV'
        - 'IRM'
        - 'JBHT'
        - 'JBL'
        - 'JKHY'
        - 'J'
        - 'JNJ'
        - 'JCI'
        - 'JPM'
        - 'JNPR'
        - 'K'
        - 'KVUE'
        - 'KDP'
        - 'KEY'
        - 'KEYS'
        - 'KMB'
        - 'KIM'
        - 'KMI'
        - 'KLAC'
        - 'KHC'
        - 'KR'
        - 'LHX'
        - 'LH'
        - 'LRCX'
        - 'LW'
        - 'LVS'
        - 'LDOS'
        - 'LEN'
        - 'LIN'
        - 'LYV'
        - 'LKQ'
        - 'LMT'
        - 'L'
        - 'LOW'
        - 'LULU'
        - 'LYB'
        - 'MTB'
        - 'MRO'
        - 'MPC'
        - 'MKTX'
        - 'MAR'
        - 'MMC'
        - 'MLM'
        - 'MAS'
        - 'MA'
        - 'MTCH'
        - 'MKC'
        - 'MCD'
        - 'MCK'
        - 'MDT'
        - 'MRK'
        - 'META'
        - 'MET'
        - 'MTD'
        - 'MGM'
        - 'MCHP'
        - 'MU'
        - 'MSFT'
        - 'MAA'
        - 'MRNA'
        - 'MHK'
        - 'MOH'
        - 'TAP'
        - 'MDLZ'
        - 'MPWR'
        - 'MNST'
        - 'MCO'
        - 'MS'
        - 'MOS'
        - 'MSI'
        - 'MSCI'
        - 'NDAQ'
        - 'NTAP'
        - 'NFLX'
        - 'NEM'
        - 'NWSA'
        - 'NWS'
        - 'NEE'
        - 'NKE'
        - 'NI'
        - 'NDSN'
        - 'NSC'
        - 'NTRS'
        - 'NOC'
        - 'NCLH'
        - 'NRG'
        - 'NUE'
        - 'NVDA'
        - 'NVR'
        - 'NXPI'
        - 'ORLY'
        - 'OXY'
        - 'ODFL'
        - 'OMC'
        - 'ON'
        - 'OKE'
        - 'ORCL'
        - 'OTIS'
        - 'PCAR'
        - 'PKG'
        - 'PANW'
        - 'PARA'
        - 'PH'
        - 'PAYX'
        - 'PAYC'
        - 'PYPL'
        - 'PNR'
        - 'PEP'
        - 'PFE'
        - 'PCG'
        - 'PM'
        - 'PSX'
        - 'PNW'
        - 'PNC'
        - 'POOL'
        - 'PPG'
        - 'PPL'
        - 'PFG'
        - 'PG'
        - 'PGR'
        - 'PLD'
        - 'PRU'
        - 'PEG'
        - 'PTC'
        - 'PSA'
        - 'PHM'
        - 'QRVO'
        - 'PWR'
        - 'QCOM'
        - 'DGX'
        - 'RL'
        - 'RJF'
        - 'RTX'
        - 'O'
        - 'REG'
        - 'REGN'
        - 'RF'
        - 'RSG'
        - 'RMD'
        - 'RVTY'
        - 'RHI'
        - 'ROK'
        - 'ROL'
        - 'ROP'
        - 'ROST'
        - 'RCL'
        - 'SPGI'
        - 'CRM'
        - 'SBAC'
        - 'SLB'
        - 'STX'
        - 'SRE'
        - 'NOW'
        - 'SHW'
        - 'SPG'
        - 'SWKS'
        - 'SJM'
        - 'SNA'
        - 'SOLV'
        - 'SO'
        - 'LUV'
        - 'SWK'
        - 'SBUX'
        - 'STT'
        - 'STLD'
        - 'STE'
        - 'SYK'
        - 'SMCI'
        - 'SYF'
        - 'SNPS'
        - 'SYY'
        - 'TMUS'
        - 'TROW'
        - 'TTWO'
        - 'TPR'
        - 'TRGP'
        - 'TGT'
        - 'TEL'
        - 'TDY'
        - 'TFX'
        - 'TER'
        - 'TSLA'
        - 'TXN'
        - 'TXT'
        - 'TMO'
        - 'TJX'
        - 'TSCO'
        - 'TT'
        - 'TDG'
        - 'TRV'
        - 'TRMB'
        - 'TFC'
        - 'TYL'
        - 'TSN'
        - 'USB'
        - 'UBER'
        - 'UDR'
        - 'ULTA'
        - 'UNP'
        - 'UAL'
        - 'UPS'
        - 'URI'
        - 'UNH'
        - 'UHS'
        - 'VLO'
        - 'VTR'
        - 'VLTO'
        - 'VRSN'
        - 'VRSK'
        - 'VZ'
        - 'VRTX'
        - 'VTRS'
        - 'VICI'
        - 'V'
        - 'VMC'
        - 'WRB'
        - 'WAB'
        - 'WBA'
        - 'WMT'
        - 'DIS'
        - 'WBD'
        - 'WM'
        - 'WAT'
        - 'WEC'
        - 'WFC'
        - 'WELL'
        - 'WST'
        - 'WDC'
        - 'WRK'
        - 'WY'
        - 'WMB'
        - 'WTW'
        - 'GWW'
        - 'WYNN'
        - 'XEL'
        - 'XYL'
        - 'YUM'
        - 'ZBRA'
        - 'ZBH'
        - 'ZTS'

HomeUsStocksCollection:
  IsHomeUsStocksCollectionReleased: true
  CollectionId: "COLLECTION_NAME_USER_PREFERRED_STOCKS"

SBIMonthlyBuyExchangeRate:
  "11-2022": 81.24
  "12-2022": 82.30
  "01-2023": 81.20
  "02-2023": 82.30
  "03-2023": 81.72
  "04-2023": 81.35
  "05-2023": 82.32
  "06-2023": 81.63
  "07-2023": 81.82
  "08-2023": 82.25
  "09-2023": 82.75
  "10-2023": 82.86
  "11-2023": 82.92
  "12-2023": 82.78
  "01-2024": 82.72
  "02-2024": 82.49
  "03-2024": 82.95
  "04-2024": 82.90
  "05-2024": 83.05
  "06-2024": 83.00
  "07-2024": 83.32
  "08-2024": 83.50
  "09-2024": 83.30
  "10-2024": 83.68
  "11-2024": 84.15
  "12-2024": 85.20
  "01-2025": 86.20
  "02-2025": 86.95
  "03-2025": 85.10
  "04-2025": 84.25

MinAndroidVersionToSupportTabbedCard: 346
MinIOSVersionToSupportTabbedCard: 495
MinAndroidVersionWithSupportForEmptyTabbedSection: 346
MinIOSVersionWithSupportForEmptyTabbedSection: 505

CostInflationIndexMap:
  "2001-2002": 100
  "2002-2003": 105
  "2003-2004": 109
  "2004-2005": 113
  "2005-2006": 117
  "2006-2007": 122
  "2007-2008": 129
  "2008-2009": 137
  "2009-2010": 148
  "2010-2011": 167
  "2011-2012": 184
  "2012-2013": 200
  "2013-2014": 220
  "2014-2015": 240
  "2015-2016": 254
  "2016-2017": 264
  "2017-2018": 272
  "2018-2019": 280
  "2019-2020": 289
  "2020-2021": 301
  "2021-2022": 317
  "2022-2023": 331
  "2023-2024": 348

Brokerage:
  Enabled: true
  BrokerageInPercentage: 0.25

FeatureReleaseConfig:
  FeatureConstraints:
    FEATURE_USS_TABBED_CARD_RTSP:
      AppVersionConstraintConfig:
        MinAndroidVersion: 352
        MinIOSVersion: 495
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

AuthFactorUpdateCoolOffPeriod: "24h"

MinAndroidAppVersionToDirectToAddFundRetryBugFix: 370

TaxDocumentGenerationParams:
  CorporateActionsOverride:
    "GAMR":
      "DateToCorporateActionsMap":
        "2024-01-30":
          "NameChanges":
            - "NewSymbol": "GAMR"
              "OldSymbol": "GAMR"
              "ProcessDate":
                "Year": 2024
                "Month": 01
                "Day": 30

IsNewStockUniverseEnabled: true

PortfolioConfig:
  FilteredSymbols:
    - "609CVR022"
  EnableSymbolFiltering: true

IndexationBenefitNotApplicableFrom: "2024-07-22T00:00:00+05:30"
