//go:generate mockgen -source=wallet_order_processor_factory.go -destination=./mocks/mock_processor_factory.go -package=mocks
package walletorderprocessor

import (
	"errors"

	"github.com/google/wire"

	"github.com/epifi/gamma/api/usstocks"
	ussOrderMgPb "github.com/epifi/gamma/api/usstocks/order"
)

var WireSet = wire.NewSet(
	NewWalletOrderProcessorFactory, wire.Bind(new(IFactory), new(*Factory)),
)

type IFactory interface {
	GetOrderProcessor(order *ussOrderMgPb.WalletOrder) (IWalletOrderProcessor, error)
}

type Factory struct {
	addFundsOrderProcessor      *AddFundsOrderProcessor
	withdrawFundsOrderProcessor *WithdrawFundsOrderProcessor
	sipOrderProcessor           *SipOrderProcessor
}

func NewWalletOrderProcessorFactory(
	addFundsOrderProcessor *AddFundsOrderProcessor,
	withdrawFundsOrderProcessor *WithdrawFundsOrderProcessor,
	sipOrderProcessor *SipOrderProcessor,
) *Factory {
	return &Factory{
		addFundsOrderProcessor:      addFundsOrderProcessor,
		withdrawFundsOrderProcessor: withdrawFundsOrderProcessor,
		sipOrderProcessor:           sipOrderProcessor,
	}
}

func (v *Factory) GetOrderProcessor(order *ussOrderMgPb.WalletOrder) (IWalletOrderProcessor, error) {

	switch order.GetOrderType() {
	case usstocks.WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS:
		return v.getAddFundProcessor(order)
	case usstocks.WalletOrderType_WALLET_ORDER_TYPE_WITHDRAW_FUNDS:
		return v.withdrawFundsOrderProcessor, nil
	default:
		return nil, errors.New("order not supported")
	}
}

func (v *Factory) getAddFundProcessor(order *ussOrderMgPb.WalletOrder) (IWalletOrderProcessor, error) {
	if order.GetOrderSubType() == usstocks.WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_SIP {
		return v.sipOrderProcessor, nil
	}
	return v.addFundsOrderProcessor, nil
}
