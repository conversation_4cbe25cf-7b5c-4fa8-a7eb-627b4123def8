package order

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/usstocks/order/dao"
)

// GetAggregateWalletOrderAmountInUsd runs aggregation query on primary db as the number of records in us stocks are less
// will need to migrate to aggregation optimised db in future
func (s *Service) GetAggregateWalletOrderAmountInUsd(ctx context.Context, req *orderPb.GetAggregateWalletOrderAmountInUsdRequest) (*orderPb.GetAggregateWalletOrderAmountInUsdResponse, error) {
	var filterOptions []storageV2.FilterOption
	if !req.GetEndTime().IsValid() || !req.GetStartTime().IsValid() {
		return &orderPb.GetAggregateWalletOrderAmountInUsdResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("start_time and end_time are required"),
		}, nil
	}

	if req.GetEndTime().AsTime().Sub(req.GetStartTime().AsTime()) > 2*60*24*time.Hour {
		return &orderPb.GetAggregateWalletOrderAmountInUsdResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("time range should be less than 60 days"),
		}, nil
	}

	if req.GetStartTime().IsValid() {
		filterOptions = append(filterOptions, dao.WithCreatedAtAfter(req.GetStartTime().AsTime()))
	}

	if len(req.GetOrderStatus()) > 0 {
		filterOptions = append(filterOptions, dao.WithOrderStatusesFilter(req.GetOrderStatus()))
	}

	if len(req.GetOrderType()) > 0 {
		filterOptions = append(filterOptions, dao.WithOrderTypesFilter(req.GetOrderType()))
	}

	totalAmount, totalCount, err := s.walletOrderDao.GetAggregateOrderAmountInUsd(ctx, req.GetActorId(), filterOptions...)
	if err != nil {
		logger.Error(ctx, "error in getting orders aggregate", zap.Error(err), zap.Any(logger.ORDER_TYPE, req.GetOrderType()),
			zap.Any(logger.STATE, req.GetOrderStatus()), zap.Any(logger.ACTOR_ID, req.GetActorId()),
			zap.Any(logger.START_TIME, req.GetStartTime()), zap.Any(logger.END_TIME, req.GetEndTime()))
		return &orderPb.GetAggregateWalletOrderAmountInUsdResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// todo[obed]: remove after testing
	logger.Info(ctx, "fetched aggregate wallet order amount", zap.Any(logger.ACTOR_ID, req.GetActorId()), zap.Any(logger.COUNT, totalCount))

	return &orderPb.GetAggregateWalletOrderAmountInUsdResponse{
		Status:      rpc.StatusOk(),
		TotalAmount: totalAmount,
		TotalCount:  totalCount,
	}, nil
}
