package list_selector

import (
	"reflect"
	"testing"

	accountPb "github.com/epifi/gamma/api/usstocks/account"
)

func TestMultiOptionSelector_CalculateScore(t *testing.T) {
	type args struct {
		answer       *accountPb.SuitabilityAnswer
		questionType accountPb.SuitabilityQuestionType
	}
	tests := []struct {
		name    string
		args    args
		want    int32
		wantErr bool
	}{
		{
			name: "if we are passing multiple option in expected case",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{"fixed-recurring-deposit-investment", "other-debtmf-sgbs-reits-investment"},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_INVESTED_INSTRUMENTS,
			},
			want:    4,
			wantErr: false,
		},
		{
			name: "if we are single expected select option",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{"other-debtmf-sgbs-reits-investment"},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_INVESTED_INSTRUMENTS,
			},
			want:    3,
			wantErr: false,
		},
		{
			name: "if we are sending ineligible option",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{"less-than-1yr-investments"},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_INVESTED_INSTRUMENTS,
			},
			wantErr: true,
		},
		{
			name: "if we are sending empty options",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_INVESTED_INSTRUMENTS,
			},
			wantErr: false,
		},
		{
			name: "if we are sending invalid options",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{"invalid"},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_INVESTED_INSTRUMENTS,
			},
			wantErr: true,
		},
		{
			name: "if we are empty non multi select",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_EMPLOYMENT_TYPE,
			},
			want:    0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewMultiOptionSelector(conf)
			got, err := s.CalculateScore(tt.args.questionType, tt.args.answer)
			if (err != nil) != tt.wantErr {
				t.Errorf("CalculateScore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CalculateScore() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMultiOptionSelector_ValidateAnswer(t *testing.T) {
	type args struct {
		answer       *accountPb.SuitabilityAnswer
		questionType accountPb.SuitabilityQuestionType
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "if we are passing multiple option in expected case",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{"fixed-recurring-deposit-investment", "other-debtmf-sgbs-reits-investment"},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_INVESTED_INSTRUMENTS,
			},
			wantErr: false,
		},
		{
			name: "if we are single expected select option",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{"other-debtmf-sgbs-reits-investment"},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_INVESTED_INSTRUMENTS,
			},
			wantErr: false,
		},
		{
			name: "if we are sending ineligible option",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{"less-than-1yr-investments"},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_INVESTED_INSTRUMENTS,
			},
			wantErr: true,
		},
		{
			name: "if we are sending empty options",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_INVESTED_INSTRUMENTS,
			},
			wantErr: false,
		},
		{
			name: "if we are sending invalid options",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{"invalid"},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_INVESTED_INSTRUMENTS,
			},
			wantErr: true,
		},
		{
			name: "if we are empty non multi select",
			args: args{
				answer: &accountPb.SuitabilityAnswer{AnswerValue: &accountPb.SuitabilityAnswer_SelectionId{SelectionId: &accountPb.SelectionIdList{
					SelectionId: []string{},
				}}},
				questionType: accountPb.SuitabilityQuestionType_SUITABILITY_QUESTION_TYPE_EMPLOYMENT_TYPE,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewMultiOptionSelector(conf)
			err := s.ValidateAnswer(tt.args.questionType, tt.args.answer)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateAnswers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
