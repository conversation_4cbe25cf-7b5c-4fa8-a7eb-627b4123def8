package catalog

import (
	"context"

	"go.uber.org/zap"

	catalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) getCompanyFinancialInfo(ctx context.Context, exchangeCode, stockSymbol string) (*catalogPb.FinancialInfo, error) {
	var (
		quarterlyBalanceSheets       []*catalogPb.BalanceSheet
		yearlyBalanceSheets          []*catalogPb.BalanceSheet
		quarterlyCashFlowStatements  []*catalogPb.CashFlowStatement
		yearlyCashFlowStatements     []*catalogPb.CashFlowStatement
		quarterlyIncomeStatements    []*catalogPb.IncomeStatement
		yearlyIncomeStatements       []*catalogPb.IncomeStatement
		quarterlyProfitabilityRatios []*catalogPb.ProfitabilityRatio
		yearlyProfitabilityRatios    []*catalogPb.ProfitabilityRatio
		quarterlyEfficiencyRatios    []*catalogPb.EfficiencyRatio
		yearlyEfficiencyRatios       []*catalogPb.EfficiencyRatio
		quarterlyHealthRatios        []*catalogPb.FinancialHealthRatio
		yearlyHealthRatios           []*catalogPb.FinancialHealthRatio
		quarterlyGrowthRatios        []*catalogPb.GrowthRatio
		yearlyGrowthRatios           []*catalogPb.GrowthRatio
		yearlyValuationRatios        []*catalogPb.ValuationRatio
		latestValuationRatio         *catalogPb.ValuationRatio
	)

	g, _ := errgroup.WithContext(ctx)

	// balance sheets
	g.Go(func() error {
		bss, err := getCompanyBalanceSheetsForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_QUARTERLY)
		if err != nil {
			return err
		}
		for _, bs := range bss {
			quarterlyBalanceSheets = append(quarterlyBalanceSheets, &catalogPb.BalanceSheet{
				TotalAssets:      bs.GetTotalAssets(),
				TotalLiabilities: bs.GetTotalLiabilities(),
				WorkingCapital:   bs.GetWorkingCapital(),
				PeriodEndingDate: bs.GetPeriodEndingDate(),
				ReportDate:       bs.GetReportDate(),
			})
		}
		return nil
	})

	g.Go(func() error {
		bss, err := getCompanyBalanceSheetsForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_ANNUAL)
		if err != nil {
			return err
		}
		for _, bs := range bss {
			yearlyBalanceSheets = append(yearlyBalanceSheets, &catalogPb.BalanceSheet{
				TotalAssets:      bs.GetTotalAssets(),
				TotalLiabilities: bs.GetTotalLiabilities(),
				WorkingCapital:   bs.GetWorkingCapital(),
				PeriodEndingDate: bs.GetPeriodEndingDate(),
				ReportDate:       bs.GetReportDate(),
			})
		}
		return nil
	})

	// cash flow statements
	g.Go(func() error {
		cfss, err := getCompanyCashFlowStatementsForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_QUARTERLY)
		if err != nil {
			return err
		}
		for _, cfs := range cfss {
			quarterlyCashFlowStatements = append(quarterlyCashFlowStatements, &catalogPb.CashFlowStatement{
				FreeCashFlow:      cfs.GetFreeCashFlow(),
				OperatingCashFlow: cfs.GetOperatingCashFlow(),
				InvestingCashFlow: cfs.GetInvestingCashFlow(),
				FinancingCashFlow: cfs.GetFinancingCashFlow(),
				PeriodEndingDate:  cfs.GetPeriodEndingDate(),
				ReportDate:        cfs.GetReportDate(),
			})
		}
		return nil
	})

	g.Go(func() error {
		cfss, err := getCompanyCashFlowStatementsForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_ANNUAL)
		if err != nil {
			return err
		}
		for _, cfs := range cfss {
			yearlyCashFlowStatements = append(yearlyCashFlowStatements, &catalogPb.CashFlowStatement{
				FreeCashFlow:      cfs.GetFreeCashFlow(),
				OperatingCashFlow: cfs.GetOperatingCashFlow(),
				InvestingCashFlow: cfs.GetInvestingCashFlow(),
				FinancingCashFlow: cfs.GetFinancingCashFlow(),
				PeriodEndingDate:  cfs.GetPeriodEndingDate(),
				ReportDate:        cfs.GetReportDate(),
			})
		}
		return nil
	})

	// income statements
	g.Go(func() error {
		iss, err := getCompanyIncomeStatementsForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_QUARTERLY)
		if err != nil {
			return err
		}
		for _, is := range iss {
			quarterlyIncomeStatements = append(quarterlyIncomeStatements, &catalogPb.IncomeStatement{
				// TODO(Brijesh): Add total expenses and gross profit after fixing in VG
				TotalRevenue:     is.GetTotalRevenue(),
				NetIncome:        is.GetNetIncome(),
				Ebitda:           is.GetEbitda(),
				PeriodEndingDate: is.GetPeriodEndingDate(),
				ReportDate:       is.GetReportDate(),
			})
		}
		return nil
	})

	g.Go(func() error {
		iss, err := getCompanyIncomeStatementsForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_ANNUAL)
		if err != nil {
			return err
		}
		for _, is := range iss {
			yearlyIncomeStatements = append(yearlyIncomeStatements, &catalogPb.IncomeStatement{
				// TODO(Brijesh): Add total expenses and gross profit after fixing in VG
				TotalRevenue:     is.GetTotalRevenue(),
				NetIncome:        is.GetNetIncome(),
				Ebitda:           is.GetEbitda(),
				PeriodEndingDate: is.GetPeriodEndingDate(),
				ReportDate:       is.GetReportDate(),
			})
		}
		return nil
	})

	// profitability ratios
	g.Go(func() error {
		prs, err := getStockProfitabilityRatiosForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_QUARTERLY)
		if err != nil {
			return err
		}
		for _, pr := range prs {
			quarterlyProfitabilityRatios = append(quarterlyProfitabilityRatios, &catalogPb.ProfitabilityRatio{
				GrossMargin:      pr.GetGrossMargin(),
				EbitdaMargin:     pr.GetEbitdaMargin(),
				NetMargin:        pr.GetNetMargin(),
				PeriodEndingDate: pr.GetPeriodEndingDate(),
				ReportDate:       pr.GetReportDate(),
			})
		}
		return nil
	})

	g.Go(func() error {
		prs, err := getStockProfitabilityRatiosForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_ANNUAL)
		if err != nil {
			return err
		}
		for _, pr := range prs {
			yearlyProfitabilityRatios = append(yearlyProfitabilityRatios, &catalogPb.ProfitabilityRatio{
				GrossMargin:      pr.GetGrossMargin(),
				EbitdaMargin:     pr.GetEbitdaMargin(),
				NetMargin:        pr.GetNetMargin(),
				PeriodEndingDate: pr.GetPeriodEndingDate(),
				ReportDate:       pr.GetReportDate(),
			})
		}
		return nil
	})

	// efficiency ratios
	g.Go(func() error {
		ers, err := getStockEfficiencyRatiosForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_QUARTERLY)
		if err != nil {
			return err
		}
		for _, er := range ers {
			quarterlyEfficiencyRatios = append(quarterlyEfficiencyRatios, &catalogPb.EfficiencyRatio{
				Roe:              er.GetRoe(),
				Roic:             er.GetRoic(),
				PeriodEndingDate: er.GetPeriodEndingDate(),
				ReportDate:       er.GetReportDate(),
			})
		}
		return nil
	})

	g.Go(func() error {
		ers, err := getStockEfficiencyRatiosForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_ANNUAL)
		if err != nil {
			return err
		}
		for _, er := range ers {
			yearlyEfficiencyRatios = append(yearlyEfficiencyRatios, &catalogPb.EfficiencyRatio{
				Roe:              er.GetRoe(),
				Roic:             er.GetRoic(),
				PeriodEndingDate: er.GetPeriodEndingDate(),
				ReportDate:       er.GetReportDate(),
			})
		}
		return nil
	})

	// Health ratios
	g.Go(func() error {
		hrs, err := getStockFinancialHealthRatiosForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_QUARTERLY)
		if err != nil {
			return err
		}
		for _, hr := range hrs {
			quarterlyHealthRatios = append(quarterlyHealthRatios, &catalogPb.FinancialHealthRatio{
				TotalDebtToEquity: hr.GetTotalDebtToEquity(),
				PeriodEndingDate:  hr.GetPeriodEndingDate(),
				ReportDate:        hr.GetReportDate(),
			})
		}
		return nil
	})

	g.Go(func() error {
		hrs, err := getStockFinancialHealthRatiosForPeriod(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_ANNUAL)
		if err != nil {
			return err
		}
		for _, hr := range hrs {
			yearlyHealthRatios = append(yearlyHealthRatios, &catalogPb.FinancialHealthRatio{
				TotalDebtToEquity: hr.GetTotalDebtToEquity(),
				PeriodEndingDate:  hr.GetPeriodEndingDate(),
				ReportDate:        hr.GetReportDate(),
			})
		}
		return nil
	})

	// Growth ratios
	g.Go(func() error {
		grs, err := getStockQuarterlyGrowthRatios(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_QUARTERLY)
		if err != nil {
			return err
		}
		for _, gr := range grs {
			quarterlyGrowthRatios = append(quarterlyGrowthRatios, &catalogPb.GrowthRatio{
				DilutedEpsGrowth: gr.GetDilutedEps3MonthSamePeriodGrowth(),
				RevenueGrowth:    gr.GetRevenue3MonthSamePeriodGrowth(),
				PeriodEndingDate: gr.GetPeriodEndingDate(),
				ReportDate:       gr.GetReportDate(),
			})
		}
		return nil
	})

	g.Go(func() error {
		grs, err := getStockYearlyGrowthRatios(ctx, s.vgCatalogClient, exchangeCode, stockSymbol, vgCatalogPb.PeriodicDataFrequency_PERIODIC_DATA_FREQUENCY_ANNUAL)
		if err != nil {
			return err
		}
		for _, gr := range grs {
			yearlyGrowthRatios = append(yearlyGrowthRatios, &catalogPb.GrowthRatio{
				DilutedEpsGrowth: gr.GetDilutedEps1YearGrowth(),
				RevenueGrowth:    gr.GetRevenue1YearGrowth(),
				PeriodEndingDate: gr.GetPeriodEndingDate(),
				ReportDate:       gr.GetReportDate(),
			})
		}
		return nil
	})

	g.Go(func() error {
		vrs, err := getStockYearlyValuationRatios(ctx, s.vgCatalogClient, exchangeCode, stockSymbol)
		if err != nil {
			return err
		}
		for _, vr := range vrs {
			yearlyValuationRatios = append(yearlyValuationRatios, &catalogPb.ValuationRatio{
				PriceToEps:    vr.GetPriceToEps(),
				PriceToBook:   vr.GetPriceToBook(),
				DividendYield: vr.GetDividendYield(),
				EvToEbitda:    vr.GetEvToEbitda(),
				AsOfDate:      vr.GetAsOfDate(),
			})
		}
		return nil
	})

	g.Go(func() error {
		vrs, err := getStockLatestValuationRatios(ctx, s.vgCatalogClient, exchangeCode, stockSymbol)
		if err != nil {
			return err
		}
		if len(vrs) > 0 {
			latestValuationRatio = &catalogPb.ValuationRatio{
				PriceToEps:    vrs[0].GetPriceToEps(),
				PriceToBook:   vrs[0].GetPriceToBook(),
				DividendYield: vrs[0].GetDividendYield(),
				EvToEbitda:    vrs[0].GetEvToEbitda(),
				AsOfDate:      vrs[0].GetAsOfDate(),
			}
		}
		return nil
	})
	err := g.Wait()
	if err != nil {
		logger.Error(ctx, "error in one of the go-routines for fetching  data", zap.Error(err))
		return nil, err
	}

	return &catalogPb.FinancialInfo{
		QuarterlyBalanceSheets:         quarterlyBalanceSheets,
		YearlyBalanceSheets:            yearlyBalanceSheets,
		QuarterlyCashFlowStatements:    quarterlyCashFlowStatements,
		YearlyCashFlowStatements:       yearlyCashFlowStatements,
		QuarterlyIncomeStatements:      quarterlyIncomeStatements,
		YearlyIncomeStatements:         yearlyIncomeStatements,
		QuarterlyProfitabilityRatios:   quarterlyProfitabilityRatios,
		YearlyProfitabilityRatios:      yearlyProfitabilityRatios,
		QuarterlyEfficiencyRatios:      quarterlyEfficiencyRatios,
		YearlyEfficiencyRatios:         yearlyEfficiencyRatios,
		QuarterlyFinancialHealthRatios: quarterlyHealthRatios,
		YearlyFinancialHealthRatios:    yearlyHealthRatios,
		QuarterlyGrowthRatios:          quarterlyGrowthRatios,
		YearlyGrowthRatios:             yearlyGrowthRatios,
		QuarterlyValuationRatios:       nil,
		YearlyValuationRatios:          yearlyValuationRatios,
		LatestValuationRatio:           latestValuationRatio,
	}, nil
}
