package param_generator

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/api/usstocks/tax"
	"github.com/epifi/gamma/api/usstocks/tax/documentparams"
	"github.com/epifi/gamma/api/usstocks/tax/moneywrapper"
	usstocksPkg "github.com/epifi/gamma/pkg/usstocks"
	"github.com/epifi/gamma/usstocks/tax/transactionprocessor"
	"github.com/epifi/gamma/usstocks/utils"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/datetime"
)

const (
	CountryUSA = "United States of America"
	// CountryCodeUSA ref: https://incometaxindia.gov.in/Supporting%20Files/ITR2023/Instructions_ITR5_AY_2023_24.pdf
	// Pages 75-81
	CountryCodeUSA = "2"
)

var (
	ErrHistoricalStockPriceNotFound = fmt.Errorf("historical stock price not found for peak value of investment")
)

type foreignSecurityReq struct {
	buySellPair        *transactionprocessor.BuySellPair
	stockEntityDetails *tax.StockEntityDetails
	fromTime           time.Time
	toTime             time.Time
}

type ScheduleFaDocParamsGenerator struct {
	transactionProcessor    transactionprocessor.ITransactionProcessor
	sbiExchangeRateProvider utils.SBIExchangeRateProvider
	usStocksCache           cache.CacheStorage
}

func NewScheduleFaDocParamsGenerator(transactionProcessor transactionprocessor.ITransactionProcessor, sbiExchangeRateProvider utils.SBIExchangeRateProvider, usStocksCache cache.CacheStorage) *ScheduleFaDocParamsGenerator {
	return &ScheduleFaDocParamsGenerator{
		transactionProcessor:    transactionProcessor,
		sbiExchangeRateProvider: sbiExchangeRateProvider,
		usStocksCache:           usStocksCache,
	}
}

func (g *ScheduleFaDocParamsGenerator) GetDocumentParams(ctx context.Context, req *ParamsGenerateReq) (*documentparams.DocumentParams, error) {
	if err := g.validateRequest(req); err != nil {
		return nil, fmt.Errorf("validation failure for schedule fa: %w", err)
	}
	foreignSecurityDetails, err := g.getForeignSecurityDetails(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get foreign securities details for schedule fa: %w", err)
	}

	return &documentparams.DocumentParams{
		Params: &documentparams.DocumentParams_ScheduleFaParams{
			ScheduleFaParams: &documentparams.ScheduleFAParams{
				SecuritiesDetails: foreignSecurityDetails,
			},
		},
	}, nil
}

func (g *ScheduleFaDocParamsGenerator) PrepareDataForDocumentParams(ctx context.Context, txns []*tax.TransactionWrapper, fromTime time.Time, toTime time.Time) (*transactionprocessor.DocumentParamsInfo, error) {
	fromTime, toTime = g.getStartAndEndTimeForScheduleFA(fromTime, toTime)

	// get buy sell pairs for schedule FA, this is different from normal buy sell pairs
	// this pairs includes all the buys which are there in the given calendar year
	buySellPairsMap, buySellPairFetchErr := g.transactionProcessor.GetBuySellPairForScheduleFA(ctx, txns, fromTime, toTime)
	if buySellPairFetchErr != nil {
		return nil, fmt.Errorf("failed to get buy sell pairs for schedule FA: %w", buySellPairFetchErr)
	}
	buySellPairs := getAllBuySellPairsFromMap(buySellPairsMap)
	return &transactionprocessor.DocumentParamsInfo{
		BuySellPairs: &transactionprocessor.BuySellPairs{
			Pairs: buySellPairs,
		},
	}, nil
}

func (g *ScheduleFaDocParamsGenerator) validateRequest(req *ParamsGenerateReq) error {
	switch {
	case req == nil:
		return fmt.Errorf("param_gen_req cannot be nil")
	case req.AccountId == "":
		return fmt.Errorf("actor id cannot be empty")
	case req.FromTime.IsZero():
		return fmt.Errorf("from time is not specified")
	case req.ToTime.IsZero():
		return fmt.Errorf("to time is not specified")
	case req.BuySellPairs == nil:
		return fmt.Errorf("buy-sell pairs cannot be nil")
	}
	return nil
}

func (g *ScheduleFaDocParamsGenerator) getForeignSecurityDetails(ctx context.Context, req *ParamsGenerateReq) ([]*documentparams.ForeignSecurityDetails, error) {
	fromTime, toTime := g.getStartAndEndTimeForScheduleFA(req.FromTime, req.ToTime)
	var foreignSecurityDetails []*documentparams.ForeignSecurityDetails
	stockEntityDetailsMap := make(map[string]*tax.StockEntityDetails)
	for _, buySellPair := range req.BuySellPairs.Pairs {
		stockEntityDetails, err := g.getStockEntityDetails(ctx, buySellPair, stockEntityDetailsMap)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "stock price history not found in redis for symbol", zap.String("symbol", buySellPair.Symbol), zap.Error(err))
				return nil, fmt.Errorf("historical stock prices not found for symbol %s: %w", buySellPair.Symbol, ErrHistoricalStockPriceNotFound)
			}
			return nil, fmt.Errorf("failed to get stock entity details for symbol %s: %w", buySellPair.Symbol, err)
		}

		initialValueOfInvestmentINR, err := g.getInitialValueOfInvestmentINR(buySellPair)
		if err != nil {
			return nil, fmt.Errorf("failed to get initial value of investment INR for buy sell pair: %w", err)
		}

		peakValueOfInvestment, err := g.getPeakValueOfInvestment(ctx, &foreignSecurityReq{buySellPair: buySellPair, stockEntityDetails: stockEntityDetails, fromTime: fromTime, toTime: toTime})
		if err != nil {
			// if historical stock price not found for calculating peak value of investment
			if errors.Is(err, ErrHistoricalStockPriceNotFound) {
				logger.Error(ctx, "historical stock price not found for calculating peak value of investment", zap.String("symbol", buySellPair.Symbol), zap.Error(err))
				return nil, fmt.Errorf("historical stock price not found for calculating peak value of investment for symbol %s: %w", buySellPair.Symbol, ErrHistoricalStockPriceNotFound)
			}
			return nil, fmt.Errorf("failed to get peak value of investment for buy sell pair: %w", err)
		}

		closingBalanceINR, err := g.getClosingInvestmentBalanceINR(ctx, &foreignSecurityReq{buySellPair: buySellPair, stockEntityDetails: stockEntityDetails, fromTime: fromTime, toTime: toTime})
		if err != nil {
			return nil, fmt.Errorf("failed to get closing investment INR for buy sell pair: %w", err)
		}

		foreignSecurityDetails = append(foreignSecurityDetails, &documentparams.ForeignSecurityDetails{
			CountryOfInvestment:                CountryUSA,
			CountryCode:                        CountryCodeUSA,
			NameOfEntity:                       fmt.Sprintf("%s (%s)", buySellPair.DisplayName, buySellPair.Symbol),
			AddressOfEntity:                    stockEntityDetails.GetEntityAddress(),
			NatureOfEntity:                     "Company",
			DateOfAcquisition:                  datetime.TimeToDateInLoc(buySellPair.BuyTime, datetime.IST),
			InitialValueOfInvestmentInInr:      initialValueOfInvestmentINR,
			PeakValueOfInvestmentDuringTheYear: peakValueOfInvestment,
			ClosingInvestmentBalanceInInr:      closingBalanceINR,
			TotalGrossAmountCreditedWrtHolding: initialValueOfInvestmentINR,
			TotalGrossProceedsFromHoldingSale:  g.getTotalGrossSellAmountINR(buySellPair),
		})
	}
	return foreignSecurityDetails, nil
}

func (g *ScheduleFaDocParamsGenerator) getStockEntityDetails(ctx context.Context, buySellPair *transactionprocessor.BuySellPair, stockEntityDetailsMap map[string]*tax.StockEntityDetails) (*tax.StockEntityDetails, error) {
	stockEntityDetails, found := stockEntityDetailsMap[buySellPair.Symbol]
	if found {
		return stockEntityDetails, nil
	}
	// if not found in map, try fetching from Redis
	key := usstocksPkg.GetUssTaxHistoricalPricesCacheKey(buySellPair.Symbol)
	cachedVal, err := g.usStocksCache.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get stock entity details from redis for symbol %s: %w", buySellPair.Symbol, err)
	}
	entityDetails := &tax.StockEntityDetails{}
	if err = proto.Unmarshal([]byte(cachedVal), entityDetails); err != nil {
		return nil, fmt.Errorf("failed to unmarshal stock entity details from redis for symbol %s: %w", buySellPair.Symbol, err)
	}
	stockEntityDetailsMap[buySellPair.Symbol] = entityDetails
	return entityDetails, nil
}

func (g *ScheduleFaDocParamsGenerator) getInitialValueOfInvestmentINR(buySellPair *transactionprocessor.BuySellPair) (*moneyPb.Money, error) {
	// getting conversion rate for date of acquisition
	prevMonthToBuy := datetime.AddNMonths(&buySellPair.BuyTime, -1)
	conversionRateForBuy, err := g.sbiExchangeRateProvider.GetMonthEndBuyRate(*prevMonthToBuy)
	if err != nil {
		return nil, fmt.Errorf("failed to get last month sbi exchange rate for buy : %w", err)
	}
	return money.ConvertUSDToINR(buySellPair.BuyValueInUsd, conversionRateForBuy.GetPb()), nil
}

func (g *ScheduleFaDocParamsGenerator) getPeakValueOfInvestment(ctx context.Context, req *foreignSecurityReq) (*moneywrapper.UsdInrWrapper, error) {
	// get adjusted buy and sell dates for the reporting period
	buyTimeAdjusted, sellTimeAdjusted := g.getAdjustedBuySellTimeForScheduleFA(req.buySellPair, req.fromTime, req.toTime)
	dateBuyTimeAdjusted := datetime.TimeToDateInLoc(buyTimeAdjusted, datetime.UTC)
	dateSellTimeAdjusted := datetime.TimeToDateInLoc(sellTimeAdjusted, datetime.UTC)

	peakClosingPriceDetail := &tax.DailyClosingPrice{
		ClosingPrice: money.ZeroUSD(),
	}

	// iterate over historical prices to find the peak closing price within the adjusted date range
	for _, dailyClosingPrice := range req.stockEntityDetails.GetDailyClosingPrices() {
		closingPriceTime := dailyClosingPrice.GetClosingPriceTime().AsTime()
		// check if the date is within the adjusted buy and sell dates (inclusive)
		dateClosingPriceTime := datetime.TimeToDateInLoc(closingPriceTime, datetime.UTC)
		if !datetime.IsDateAfter(dateBuyTimeAdjusted, dateClosingPriceTime) && !datetime.IsDateAfter(dateClosingPriceTime, dateSellTimeAdjusted) {
			if dailyClosingPrice.GetClosingPrice() != nil {
				// if it's the first price found or greater than the current peak, update peak
				isGreaterThan, err := money.IsGreaterThan(dailyClosingPrice.GetClosingPrice(), peakClosingPriceDetail.GetClosingPrice())
				if err != nil {
					logger.Error(ctx, "error comparing money values", zap.Error(err))
					continue
				}
				if peakClosingPriceDetail.GetClosingPrice() == nil || isGreaterThan {
					peakClosingPriceDetail = dailyClosingPrice
				}
			}
		}
	}

	// if no relevant price history found in the date range or no peak price found
	if peakClosingPriceDetail.GetClosingPrice() == nil || peakClosingPriceDetail.GetClosingPriceTime() == nil {
		// this case might happen if there are no prices for the given date range in cache.
		// return custom error, this can be ignored while document creation for schedule fa
		logger.Info(ctx, "no peak price found for buy sell pair within the date range", zap.String("symbol", req.buySellPair.Symbol))
		return nil, ErrHistoricalStockPriceNotFound
	}

	peakPriceTime := peakClosingPriceDetail.GetClosingPriceTime().AsTime().In(time.Local)
	peakValueUSD := money.Multiply(peakClosingPriceDetail.GetClosingPrice(), req.buySellPair.Quantity)
	// conversion rate for the month end preceding the peak price date
	prevMonthToPeakDate := datetime.AddNMonths(&peakPriceTime, -1)
	conversionRateForPeak, err := g.sbiExchangeRateProvider.GetMonthEndBuyRate(*prevMonthToPeakDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get month end sbi exchange rate for peak date: %w", err)
	}
	peakValueINR := money.ConvertUSDToINR(peakValueUSD, conversionRateForPeak.GetPb())
	return &moneywrapper.UsdInrWrapper{Usd: peakValueUSD, Inr: peakValueINR}, nil
}

// getAdjustedBuySellTimeForScheduleFA adjusts the buy and sell times for a transaction pair for Schedule FA reporting.
// it ensures that the time fall within the specified calendar year range.
// if buy time is before from time then adjusted buy time will be from time,
// if sell time is after to time or sell amount is nil (stock not sold) then sell time is adjusted to toTime
func (g *ScheduleFaDocParamsGenerator) getAdjustedBuySellTimeForScheduleFA(buySellPair *transactionprocessor.BuySellPair, fromTime time.Time, toTime time.Time) (time.Time, time.Time) {
	// if buy time is before the reporting period start, use the start date.
	buyTime := buySellPair.BuyTime
	if buySellPair.BuyTime.Before(fromTime) {
		buyTime = fromTime
	}
	// if sell time is after the reporting period end or there was no actual sale (indicated by nil SellValueInUsd),
	// use the end date. Otherwise, use the actual sell time.
	sellTime := buySellPair.SellTime
	if buySellPair.SellTime.After(toTime) || buySellPair.SellValueInUsd == nil {
		sellTime = toTime
	}
	return buyTime, sellTime
}

func (g *ScheduleFaDocParamsGenerator) getClosingInvestmentBalanceINR(ctx context.Context, req *foreignSecurityReq) (*moneyPb.Money, error) {
	if req.buySellPair.SellValueInUsd != nil {
		return nil, nil
	}

	var stockPriceOnToTime *moneyPb.Money
	for _, dailyClosingPrice := range req.stockEntityDetails.GetDailyClosingPrices() {
		closingPriceTime := dailyClosingPrice.GetClosingPriceTime().AsTime()
		// check if the closing price date matches the toTime
		if datetime.DateEquals(datetime.TimeToDateInLoc(closingPriceTime, datetime.IST), datetime.TimeToDateInLoc(req.toTime, datetime.IST)) {
			stockPriceOnToTime = dailyClosingPrice.GetClosingPrice()
			break
		}
	}
	if stockPriceOnToTime == nil {
		logger.Info(ctx, "no historical stock price found for closing balance on date", zap.String("symbol", req.buySellPair.Symbol), zap.Time("date", req.toTime))
		return nil, nil
	}
	toTime := req.toTime
	stockValueOnToTimeUSD := money.Multiply(stockPriceOnToTime, req.buySellPair.Quantity)
	prevMonthToToTime := datetime.AddNMonths(&toTime, -1)
	conversionRateForEndOfCY, err := g.sbiExchangeRateProvider.GetMonthEndBuyRate(*prevMonthToToTime)
	if err != nil {
		return nil, fmt.Errorf("failed to get last month sbi exchange rate for end of CY: %w", err)
	}
	return money.ConvertUSDToINR(stockValueOnToTimeUSD, conversionRateForEndOfCY.GetPb()), nil
}

func (g *ScheduleFaDocParamsGenerator) getTotalGrossSellAmountINR(buySellPair *transactionprocessor.BuySellPair) *moneyPb.Money {
	var totalGrossSellAmountINR *moneyPb.Money
	if buySellPair.SellValueInUsd != nil {
		totalGrossSellAmountINR = money.ConvertUSDToINR(buySellPair.SellValueInUsd, buySellPair.ConversionRate)
	}
	return totalGrossSellAmountINR
}

func (g *ScheduleFaDocParamsGenerator) getStartAndEndTimeForScheduleFA(fromTime time.Time, toTime time.Time) (time.Time, time.Time) {
	// change fromTime and toTime for schedule-FA form
	// this is changed because schedule FA is calculated on calendar year
	// if FY is April 2024 - March 2025 then, calendar year will be Jan 2024 - Dec 2024
	fromTime = datetime.StartOfYear(fromTime.Year(), datetime.IST)
	toTime = datetime.StartOfYear(toTime.Year(), datetime.IST).Add(-1 * time.Duration(1))
	return fromTime, toTime
}
