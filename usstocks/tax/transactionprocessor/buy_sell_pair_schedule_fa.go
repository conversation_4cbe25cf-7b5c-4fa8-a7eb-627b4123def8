package transactionprocessor

import (
	"context"
	"fmt"
	"time"

	"github.com/mohae/deepcopy"
	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/api/usstocks/tax"
)

// nolint:dupl
func (p *TransactionProcessor) GetBuySellPairForScheduleFA(ctx context.Context, txns []*tax.TransactionWrapper, fromTime time.Time, toTime time.Time) (map[string][]*BuySellPair, error) {
	// creating copy of transactions as this method will be updating the values in txns, and
	// we don't want this values to be updated out of scope of GetBuySellPairForScheduleFA method
	deepCopyTxns := p.createDeepCopyOfTxns(txns)
	filteredTxns, symbolToStockDetailsMap, stockDetailsFetchErr := p.filterTransactionsAndGetStockDetails(ctx, deepCopyTxns)
	if stockDetailsFetchErr != nil {
		return nil, fmt.Errorf("failed to filter transactions and get stock symbol details : %w", stockDetailsFetchErr)
	}

	buySellPairsMap := make(map[string][]*BuySellPair)
	buyTxnsMap := make(map[string][]*tax.TransactionWrapper)
	for _, txn := range filteredTxns {
		symbol := txn.GetSymbol()
		switch txn.GetTransactionType() {
		case tax.TransactionType_TRANSACTION_TYPE_BUY:
			if txn.GetQuantity().IsZero() {
				return nil, fmt.Errorf("buy transaction cannot have 0 units for symbol %s, executed at: %v", symbol, txn.GetExecutedAt().AsTime())
			}
			buyTxnsMap[symbol] = append(buyTxnsMap[symbol], txn)
		case tax.TransactionType_TRANSACTION_TYPE_SELL:
			if txn.GetQuantity().IsZero() {
				return nil, fmt.Errorf("sell transaction cannot have 0 units for symbol %s, executed at: %v", symbol, txn.GetExecutedAt().AsTime())
			}
			if _, found := buyTxnsMap[symbol]; !found {
				return nil, fmt.Errorf("no buy found in buyTxnMap for Symbol : %s", symbol)
			}
			buySellPairs, err := p.getBuySellPairsForScheduleFA(buyTxnsMap, txn, symbolToStockDetailsMap, fromTime, toTime)
			if err != nil {
				return nil, fmt.Errorf("failed to get buy-sell pairs : %w", err)
			}
			buySellPairsMap[symbol] = append(buySellPairsMap[symbol], buySellPairs...)
		}
	}

	// Process any remaining buy transactions that were not paired with a sell.
	// These represent open positions that occurred within the calendar year or in previous calendar year.
	// Filter these transactions to include only those executed on or before toTime
	// and add them as buy pairs without a sell to the result map.
	for symbol, symbolTxns := range buyTxnsMap {
		// process buy transactions executed at or before toTime
		var remainingSymbolTxns []*tax.TransactionWrapper
		for _, txn := range symbolTxns {
			if !txn.GetExecutedAt().AsTime().After(toTime) {
				buyPairWithoutSell, err := p.getBuyPairWithoutSell(txn, symbolToStockDetailsMap)
				if err != nil {
					return nil, fmt.Errorf("failed to get buy pair without sell : %w", err)
				}
				buySellPairsMap[symbol] = append(buySellPairsMap[symbol], buyPairWithoutSell)
			} else {
				remainingSymbolTxns = append(remainingSymbolTxns, txn)
			}
		}
		buyTxnsMap[symbol] = remainingSymbolTxns
	}

	// assert that no buy transactions executed on or before toTime remain in the map.
	// any remaining transactions would be those after toTime.
	for symbol, symbolTxns := range buyTxnsMap {
		// filter transactions to include only those on or before toTime for the assertion.
		remainingTxns := lo.Filter(symbolTxns, func(txn *tax.TransactionWrapper, _ int) bool {
			return !txn.GetExecutedAt().AsTime().After(toTime)
		})
		// Assert that there are no remaining buy transactions within the time window.
		if len(remainingTxns) > 0 {
			return nil, fmt.Errorf("unexpected remaining buy transactions for symbol %s within the time window after processing", symbol)
		}
	}
	return buySellPairsMap, nil
}

func (p *TransactionProcessor) getBuySellPairsForScheduleFA(buyTxnMap map[string][]*tax.TransactionWrapper, sellTxn *tax.TransactionWrapper, symbolToStockDetailsMap map[string]*catalog.Stock, fromTime time.Time, toTime time.Time) ([]*BuySellPair, error) {
	var buySellPairs []*BuySellPair
	symbol := sellTxn.GetSymbol()
	buyTxns := buyTxnMap[symbol]
	for !sellTxn.GetQuantity().IsZero() {
		if len(buyTxns) == 0 {
			return nil, fmt.Errorf("no buy txns to match the sell txn : %s", symbol)
		}
		buySellPair, err := p.getBuySellPair(buyTxns[0], sellTxn, symbolToStockDetailsMap)
		if err != nil {
			return nil, fmt.Errorf("failed to get buy-sell pair for schedule-FA for %s : %w", symbol, err)
		}
		// buy sell pair is valid only if user holds any units between [from_time, to_time]
		// this is equivalent to checking if the interval [buy_time, sell_time] intersects with [from_time, to_time]
		if !buySellPair.BuyTime.After(toTime) && !fromTime.After(buySellPair.SellTime) {
			buySellPairs = append(buySellPairs, buySellPair)
		}
		if buyTxns[0].GetQuantity().IsZero() {
			buyTxns = buyTxns[1:]
		}
	}
	// update the map with the new buys post fifo change
	buyTxnMap[symbol] = buyTxns
	return buySellPairs, nil
}

func (p *TransactionProcessor) getBuyPairWithoutSell(buyTxn *tax.TransactionWrapper, symbolToStockDetailsMap map[string]*catalog.Stock) (*BuySellPair, error) {
	minQty := buyTxn.GetQuantity()
	if minQty.IsZero() {
		return nil, fmt.Errorf("buy quantity cannot be 0 for buy pair without sell")
	}

	ratioBuyAmount, ratioBuyExpense := p.getRatioAmountAndExpense(buyTxn, minQty)
	totalBuyValue, err := money.Sum(ratioBuyAmount.GetPb(), ratioBuyExpense.GetPb())
	if err != nil {
		return nil, fmt.Errorf("failed to compute total buy value : %w", err)
	}

	stockDetails, found := symbolToStockDetailsMap[buyTxn.GetSymbol()]
	if !found {
		return nil, fmt.Errorf("failed to find stock details for buy symbol in details map: %s", buyTxn.GetSymbol())
	}

	// update current buy and sell transactions since a buy-sell pair is extracted from these
	updateErr := p.updateTxnAfterBuySellPairExtraction(buyTxn, minQty, ratioBuyAmount, ratioBuyExpense)
	if updateErr != nil {
		return nil, fmt.Errorf("failed to update buy txn detail post buy-sell pair extraction : %w", updateErr)
	}

	return &BuySellPair{
		Symbol:        buyTxn.GetSymbol(),
		StockType:     stockDetails.GetStockType(),
		DisplayName:   stockDetails.GetStockBasicDetails().GetName().GetStandardName(),
		Quantity:      minQty,
		BuyTime:       buyTxn.GetExecutedAt().AsTime(),
		BuyValueInUsd: totalBuyValue,
	}, nil
}

func (p *TransactionProcessor) createDeepCopyOfTxns(txns []*tax.TransactionWrapper) []*tax.TransactionWrapper {
	var txnCopy []*tax.TransactionWrapper
	for _, txn := range txns {
		// The `Quantity` field of `TransactionWrapper` is a pointer to a `decimal.Decimal` object.
		// When `deepcopy.Copy` is used, it performs a shallow copy of this pointer, meaning both the original `txn` and the `newTxn`
		// would reference the same underlying `decimal.Decimal` value. To ensure that subsequent modifications to `newTxn.Quantity`
		// do not alter the original `txn.Quantity`, we explicitly reassign `newTxn.Quantity` with the value obtained from `txn.GetQuantity()`.
		// `txn.GetQuantity()` returns a new `decimal.Decimal` value, effectively performing a deep copy of the quantity.
		qty := txn.GetQuantity()
		newTxn := deepcopy.Copy(txn).(*tax.TransactionWrapper)
		newTxn.Quantity = qty
		txnCopy = append(txnCopy, newTxn)
	}
	return txnCopy
}
