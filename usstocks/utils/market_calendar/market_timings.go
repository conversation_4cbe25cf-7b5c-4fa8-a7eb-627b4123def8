package market_calendar

import (
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/usstocks/config"
)

// TODO(Brijesh): Add unit tests for below utils

// GetLatestRegularTradingOpenedMarketDay returns the latest day for which regular trading in market was open on/before a given time
func GetLatestRegularTradingOpenedMarketDay(t time.Time) (*config.MarketDay, error) {
	marketDays, err := config.GetMarketDaysForTimeRange(time.Time{}, t)
	if err != nil {
		return nil, fmt.Errorf("error getting market days for period ending: %s", t.String())
	}
	marketDay := marketDays[len(marketDays)-1]
	if t.Before(marketDay.MarketTimings.RegularTradingOpenAt) {
		marketDay = marketDays[len(marketDays)-2]
	}
	return marketDay, nil
}

// GetLatestMarketDay returns the latest market day on/before a given time
func GetLatestMarketDay(t time.Time) (*config.MarketDay, error) {
	marketDays, err := config.GetMarketDaysForTimeRange(time.Time{}, t)
	if err != nil {
		return nil, fmt.Errorf("error getting market days for period ending: %s", t.String())
	}
	latestMarketDay := marketDays[len(marketDays)-1]
	return latestMarketDay, nil
}

// GetStartEndMarketTimings returns the market open (for regular trading) time on/before start time
// and close time (for regular trading) on/before end time
func GetStartEndMarketTimings(start, end time.Time) (*timestamppb.Timestamp, *timestamppb.Timestamp, error) {
	startMarketDay, endMarketDay, err := GetStartEndMarketDays(start, end)
	if err != nil {
		return nil, nil, fmt.Errorf("error getting start and end market days: %w", err)
	}
	startTime := timestamppb.New(startMarketDay.MarketTimings.RegularTradingOpenAt)
	endTime := timestamppb.New(endMarketDay.MarketTimings.RegularTradingCloseAt)
	return startTime, endTime, nil
}

// GetStartEndMarketDays returns 2 market days:
// 1. market day that was open (for regular trading) on/before start time
// 2. market day that was open (for regular trading) on/before end time
func GetStartEndMarketDays(start, end time.Time) (*config.MarketDay, *config.MarketDay, error) {
	startMarketDay, err := GetLatestRegularTradingOpenedMarketDay(start)
	if err != nil {
		return nil, nil, fmt.Errorf("error getting start market day: %w", err)
	}
	endMarketDay, err := GetLatestRegularTradingOpenedMarketDay(end)
	if err != nil {
		return nil, nil, fmt.Errorf("error getting end market day: %w", err)
	}
	return startMarketDay, endMarketDay, nil
}

func IsMarketOpen(t time.Time) (bool, error) {
	marketDay, err := GetLatestMarketDay(t)
	if err != nil {
		return false, err
	}
	if t.Before(marketDay.MarketTimings.RegularTradingOpenAt) ||
		t.Equal(marketDay.MarketTimings.RegularTradingCloseAt) ||
		t.After(marketDay.MarketTimings.RegularTradingCloseAt) {
		return false, nil
	}
	return true, nil
}

// GetNextRegularTradingOpenCloseTime returns true if the market is open, else false
// It also returns the open and close timings of the next market regular trading session.
func GetNextRegularTradingOpenCloseTime(t time.Time) (bool, *timestamppb.Timestamp, *timestamppb.Timestamp, error) {
	marketDays, err := config.GetMarketDaysForTimeRange(t, t.AddDate(0, 1, 0))
	if err != nil {
		return false, nil, nil, err
	}
	if len(marketDays) == 0 {
		return false, nil, nil, fmt.Errorf("cannot find any market day for next 1 month")
	}
	if t.Equal(marketDays[0].MarketTimings.RegularTradingCloseAt) || t.After(marketDays[0].MarketTimings.RegularTradingCloseAt) {
		marketDays = marketDays[1:]
	}

	// If market open time is in the past, the market is open
	if marketDays[0].MarketTimings.RegularTradingOpenAt.Before(t) || marketDays[0].MarketTimings.RegularTradingOpenAt.Equal(t) {
		if len(marketDays) < 2 {
			return false, nil, nil, fmt.Errorf("market is open, cannot find next market day for next 1 month")
		}
		// returning next market opening time if the market is currently open
		return true, timestamppb.New(marketDays[1].MarketTimings.RegularTradingOpenAt),
			timestamppb.New(marketDays[1].MarketTimings.RegularTradingCloseAt), nil
	}

	return false, timestamppb.New(marketDays[0].MarketTimings.RegularTradingOpenAt),
		timestamppb.New(marketDays[0].MarketTimings.RegularTradingCloseAt), nil
}

// IsMarketHoliday returns true if market is closed for the day
func IsMarketHoliday(t time.Time) (bool, error) {
	y, m, d := t.In(datetime.EST5EDT).Date()
	timeInEST5EDT := time.Date(y, m, d, 0, 0, 0, 0, datetime.EST5EDT)
	isMarketOpen, nextMarketOpenTime, _, err := GetNextRegularTradingOpenCloseTime(timeInEST5EDT)
	if err != nil {
		return false, err
	}

	dateLayout := "2006-01-02"
	if isMarketOpen || timeInEST5EDT.Format(dateLayout) == nextMarketOpenTime.AsTime().Format(dateLayout) {
		return false, nil
	}
	return true, nil
}

func IsPreMarketTradingOpen(t time.Time) (bool, error) {
	marketDay, err := GetLatestMarketDay(t)
	if err != nil {
		return false, err
	}
	isTimeEqualOrAfterPreMarketOpen := t.After(marketDay.MarketTimings.PreMarketTradingOpenAt) || t.Equal(marketDay.MarketTimings.PreMarketTradingOpenAt)
	isPreMarketTradingOpen := isTimeEqualOrAfterPreMarketOpen && t.Before(marketDay.MarketTimings.RegularTradingOpenAt)
	return isPreMarketTradingOpen, nil
}

func IsAfterMarketTradingOpen(t time.Time) (bool, error) {
	marketDay, err := GetLatestMarketDay(t)
	if err != nil {
		return false, err
	}
	isTimeEqualOrAfterRegularMarketClose := t.Equal(marketDay.MarketTimings.RegularTradingCloseAt) || t.After(marketDay.MarketTimings.RegularTradingCloseAt)
	isAfterMarketTradingOpen := isTimeEqualOrAfterRegularMarketClose && t.Before(marketDay.MarketTimings.AfterMarketTradingCloseAt)
	return isAfterMarketTradingOpen, nil
}

func IsExtendedHoursTradingOpen(t time.Time) (bool, error) {
	isPreMarketTradingOpen, err := IsPreMarketTradingOpen(t)
	if err != nil {
		return false, err
	}
	isAfterMarketTradingOpen, err := IsAfterMarketTradingOpen(t)
	if err != nil {
		return false, err
	}
	if !isPreMarketTradingOpen && !isAfterMarketTradingOpen {
		return false, nil
	}
	return true, nil
}

// GetPreviousMarketDay returns the latest market day before the NewYork-day on which time t falls
func GetPreviousMarketDay(t time.Time) (*config.MarketDay, error) {
	t = t.Add(-24 * time.Hour)
	marketDays, err := config.GetMarketDaysForTimeRange(t.AddDate(0, -1, 0), t)
	if err != nil {
		return nil, err
	}
	if len(marketDays) == 0 {
		return nil, fmt.Errorf("no market day found in past 1 month")
	}
	return marketDays[len(marketDays)-1], nil
}
