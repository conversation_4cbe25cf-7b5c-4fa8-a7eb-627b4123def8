package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
	userintelPb "github.com/epifi/gamma/api/userintel"
)

type TransactionCompleted struct {
	ActorId      string
	SessionId    string
	ProspectId   string
	Timestamp    time.Time
	EventId      string
	EventType    string
	EventName    string
	ServiceName  string
	UserDeviceId string
	MerchantName string
	CountryCode  string
	OnApp        bool
	Protocol     string
	Workflow     string
	Tags         []string
}

func NewTransactionCompletedEvent(actorId string, timestampJitter time.Duration, deviceId string, merchantName, countryCode string, onApp bool, protocol, workflow string, tags []string) *TransactionCompleted {
	return &TransactionCompleted{
		ActorId:      actorId,
		Timestamp:    time.Now().Add(timestampJitter), // add jitter
		EventId:      uuid.New().String(),
		EventType:    events.EventTrack,
		ServiceName:  InAppReferralServiceName,
		UserDeviceId: deviceId,
		MerchantName: merchantName,
		CountryCode:  countryCode,
		OnApp:        onApp,
		Protocol:     protocol,
		Workflow:     workflow,
		Tags:         tags,
	}
}

func (t *TransactionCompleted) GetEventType() string {
	return t.EventType
}

func (t *TransactionCompleted) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(t, properties)
	return properties
}

func (t *TransactionCompleted) GetEventTraits() map[string]interface{} {
	return nil
}

func (t *TransactionCompleted) GetEventId() string {
	return t.EventId
}

func (t *TransactionCompleted) GetUserId() string {
	return t.ActorId
}

func (t *TransactionCompleted) GetProspectId() string {
	return t.ProspectId
}

func (t *TransactionCompleted) GetEventName() string {
	return EventTransactionCompleted
}

type FirstAddFundsCompleted struct {
	ActorId     string
	SessionId   string
	ProspectId  string
	Timestamp   time.Time
	EventId     string
	EventType   string
	EventName   string
	ServiceName string
	// field to denote after how many days funds was added
	PostXDays    int
	UserDeviceId string
}

func NewFirstAddFundsCompleted(actorId string, timeElapsedBetweenAccountCreationAndFirstAddFunds time.Duration, eventTimestamp time.Time, timestampJitter time.Duration, deviceId string) *FirstAddFundsCompleted {
	return &FirstAddFundsCompleted{
		ActorId:      actorId,
		Timestamp:    eventTimestamp.Add(timestampJitter), // add jitter
		EventId:      uuid.New().String(),
		EventType:    events.EventTrack,
		ServiceName:  InAppReferralServiceName,
		PostXDays:    int(timeElapsedBetweenAccountCreationAndFirstAddFunds.Hours() / 24.0),
		UserDeviceId: deviceId,
	}
}

func (t *FirstAddFundsCompleted) GetEventType() string {
	return t.EventType
}

func (t *FirstAddFundsCompleted) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(t, properties)
	return properties
}

func (t *FirstAddFundsCompleted) GetEventTraits() map[string]interface{} {
	return nil
}

func (t *FirstAddFundsCompleted) GetEventId() string {
	return t.EventId
}

func (t *FirstAddFundsCompleted) GetUserId() string {
	return t.ActorId
}

func (t *FirstAddFundsCompleted) GetProspectId() string {
	return t.ProspectId
}

func (t *FirstAddFundsCompleted) GetEventName() string {
	return EventFirstAddFundsCompleted
}

// FirstAddFundsCompletedMoE is a copy of FirstAddFundsCompleted, but for Moengage only.
// This is required so that no sensitive fields are passed to moengage for this particular event.
type FirstAddFundsCompletedMoE struct {
	ActorId    string
	SessionId  string
	ProspectId string
	Timestamp  time.Time
	EventId    string
	EventType  string
	EventName  string
	// field to denote after how many days funds was added
	PostXDays int
}

func NewFirstAddFundsCompletedMoE(actorId string, timeElapsedBetweenAccountCreationAndFirstAddFunds time.Duration, eventTimestamp time.Time, timestampJitter time.Duration) *FirstAddFundsCompletedMoE {
	return &FirstAddFundsCompletedMoE{
		ActorId:   actorId,
		Timestamp: eventTimestamp.Add(timestampJitter), // add jitter
		EventId:   uuid.New().String(),
		EventType: events.EventTrack,
		PostXDays: int(timeElapsedBetweenAccountCreationAndFirstAddFunds.Hours() / 24.0),
	}
}

func (t *FirstAddFundsCompletedMoE) GetEventType() string {
	return t.EventType
}

func (t *FirstAddFundsCompletedMoE) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(t, properties)
	return properties
}

func (t *FirstAddFundsCompletedMoE) GetEventTraits() map[string]interface{} {
	return nil
}

func (t *FirstAddFundsCompletedMoE) GetEventId() string {
	return t.EventId
}

func (t *FirstAddFundsCompletedMoE) GetUserId() string {
	return t.ActorId
}

func (t *FirstAddFundsCompletedMoE) GetProspectId() string {
	return t.ProspectId
}

func (t *FirstAddFundsCompletedMoE) GetEventName() string {
	return EventFirstAddFundsCompletedMoE
}

type FirstAddFundsCompletedRevenueOptimisation struct {
	ActorId     string
	SessionId   string
	ProspectId  string
	Timestamp   time.Time
	EventId     string
	EventType   string
	EventName   string
	ServiceName string
	// field to denote after how many days funds was added
	PostXDays int
	// field to denote range of the amount being added
	// amount added range - bucket value
	// 1 to 100 - 100
	// 101 to 500 - 101
	// 501 to 1000 - 501
	// 1001 to 2500 - 1001
	// 2500+ - 2501
	ADFstack     int
	UserDeviceId string
}

func NewFirstAddFundsCompletedRevenueOptimisation(actorId string, timeElapsedBetweenAccountCreationAndFirstAddFunds time.Duration, eventTimestamp time.Time,
	timestampJitter time.Duration, amountBucket int, deviceId string) *FirstAddFundsCompletedRevenueOptimisation {
	return &FirstAddFundsCompletedRevenueOptimisation{
		ActorId:      actorId,
		Timestamp:    eventTimestamp.Add(timestampJitter), // add jitter
		EventId:      uuid.New().String(),
		EventType:    events.EventTrack,
		ServiceName:  InAppReferralServiceName,
		PostXDays:    int(timeElapsedBetweenAccountCreationAndFirstAddFunds.Hours() / 24.0),
		ADFstack:     amountBucket,
		UserDeviceId: deviceId,
	}
}

func (t *FirstAddFundsCompletedRevenueOptimisation) GetEventType() string {
	return t.EventType
}

func (t *FirstAddFundsCompletedRevenueOptimisation) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(t, properties)
	return properties
}

func (t *FirstAddFundsCompletedRevenueOptimisation) GetEventTraits() map[string]interface{} {
	return nil
}

func (t *FirstAddFundsCompletedRevenueOptimisation) GetEventId() string {
	return t.EventId
}

func (t *FirstAddFundsCompletedRevenueOptimisation) GetUserId() string {
	return t.ActorId
}

func (t *FirstAddFundsCompletedRevenueOptimisation) GetProspectId() string {
	return t.ProspectId
}

func (t *FirstAddFundsCompletedRevenueOptimisation) GetEventName() string {
	return EventFirstAddFundsCompletedRevenueOptimised
}

type FirstAddFundsAffluentCompleted struct {
	ActorId     string
	SessionId   string
	ProspectId  string
	Timestamp   time.Time
	EventId     string
	EventType   string
	EventName   string
	ServiceName string
	// field to denote after how many days funds was added
	PostXDays    int
	Class        int
	UserDeviceId string
}

func NewFirstAddFundsAffluentCompleted(actorId string, timeElapsedBetweenAccountCreationAndFirstAddFunds time.Duration, eventTimestamp time.Time,
	timestampJitter time.Duration, affluenceClass userintelPb.AffluenceClass, deviceId string) *FirstAddFundsAffluentCompleted {
	return &FirstAddFundsAffluentCompleted{
		ActorId:      actorId,
		Timestamp:    eventTimestamp.Add(timestampJitter), // add jitter
		EventId:      uuid.New().String(),
		EventType:    events.EventTrack,
		ServiceName:  InAppReferralServiceName,
		PostXDays:    int(timeElapsedBetweenAccountCreationAndFirstAddFunds.Hours() / 24.0),
		Class:        int(affluenceClass),
		UserDeviceId: deviceId,
	}
}

func (f *FirstAddFundsAffluentCompleted) GetEventType() string {
	return f.EventType
}

func (f *FirstAddFundsAffluentCompleted) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(f, properties)
	return properties
}

func (f *FirstAddFundsAffluentCompleted) GetEventTraits() map[string]interface{} {
	return nil
}

func (f *FirstAddFundsAffluentCompleted) GetEventId() string {
	return f.EventId
}

func (f *FirstAddFundsAffluentCompleted) GetUserId() string {
	return f.ActorId
}

func (f *FirstAddFundsAffluentCompleted) GetProspectId() string {
	return f.ProspectId
}

func (f *FirstAddFundsAffluentCompleted) GetEventName() string {
	return EventFirstAddFundsAffluentCompleted
}

type FirstAddFundsNonAffluentCompleted struct {
	ActorId     string
	SessionId   string
	ProspectId  string
	Timestamp   time.Time
	EventId     string
	EventType   string
	EventName   string
	ServiceName string
	// field to denote after how many days funds was added
	PostXDays    int
	Class        int
	UserDeviceId string
}

func NewFirstAddFundsNonAffluentCompleted(actorId string, timeElapsedBetweenAccountCreationAndFirstAddFunds time.Duration, eventTimestamp time.Time,
	timestampJitter time.Duration, affluenceClass userintelPb.AffluenceClass, deviceId string) *FirstAddFundsNonAffluentCompleted {
	return &FirstAddFundsNonAffluentCompleted{
		ActorId:      actorId,
		Timestamp:    eventTimestamp.Add(timestampJitter), // add jitter
		EventId:      uuid.New().String(),
		EventType:    events.EventTrack,
		ServiceName:  InAppReferralServiceName,
		PostXDays:    int(timeElapsedBetweenAccountCreationAndFirstAddFunds.Hours() / 24.0),
		Class:        int(affluenceClass),
		UserDeviceId: deviceId,
	}
}

func (f *FirstAddFundsNonAffluentCompleted) GetEventType() string {
	return f.EventType
}

func (f *FirstAddFundsNonAffluentCompleted) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(f, properties)
	return properties
}

func (f *FirstAddFundsNonAffluentCompleted) GetEventTraits() map[string]interface{} {
	return nil
}

func (f *FirstAddFundsNonAffluentCompleted) GetEventId() string {
	return f.EventId
}

func (f *FirstAddFundsNonAffluentCompleted) GetUserId() string {
	return f.ActorId
}

func (f *FirstAddFundsNonAffluentCompleted) GetProspectId() string {
	return f.ProspectId
}

func (f *FirstAddFundsNonAffluentCompleted) GetEventName() string {
	return EventFirstAddFundsNonAffluentCompleted
}
