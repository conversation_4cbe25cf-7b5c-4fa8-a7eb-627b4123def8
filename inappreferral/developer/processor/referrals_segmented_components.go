package processor

import (
	"context"
	"encoding/json"
	"errors"
	"strings"

	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	"github.com/epifi/gamma/inappreferral/dao/model"

	"github.com/epifi/gamma/inappreferral/dao"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/inappreferral/developer"
)

type DevReferralsSegmentedComponents struct {
	dao dao.IReferralsSegmentedComponentDao
}

func NewDevReferralsSegmentedComponents(dao dao.IReferralsSegmentedComponentDao) *DevReferralsSegmentedComponents {
	return &DevReferralsSegmentedComponents{
		dao: dao,
	}
}

func (d *DevReferralsSegmentedComponents) FetchParamList(ctx context.Context, entity developer.InAppReferralEntity) ([]*db_state.ParameterMeta, error) {
	var componentsList, appFeatures []string
	for _, component := range inAppReferralEnumPb.Component_name {
		if component != inAppReferralEnumPb.Component_COMPONENT_UNSPECIFIED.String() {
			componentsList = append(componentsList, component)
		}
	}

	for _, appFeatureString := range inAppReferralEnumPb.AppFeature_name {
		if appFeatureString != inAppReferralEnumPb.AppFeature_APP_FEATURE_UNSPECIFIED.String() {
			appFeatures = append(appFeatures, appFeatureString)
		}
	}

	paramList := []*db_state.ParameterMeta{
		{
			Name:            FetchOnlyIDs,
			Label:           "Fetch only IDs (Y/N)?",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            Id,
			Label:           "Referrals Segmented Component ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            SegmentId,
			Label:           "Segment ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            Component,
			Label:           "Component",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         componentsList,
		},
		{
			Name:            AppFeature,
			Label:           "App Feature",
			Type:            db_state.ParameterDataType_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         appFeatures,
		},
		{
			Name:            ActiveFrom,
			Label:           "Active From",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ActiveTill,
			Label:           "Active Till",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            CreatedAtSince,
			Label:           "Created At Since",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            CreatedAtTill,
			Label:           "Created At Till",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevReferralsSegmentedComponents) FetchData(ctx context.Context, entity developer.InAppReferralEntity, filters []*db_state.Filter) (string, error) {

	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	searchFilters := &model.FetchReferralsSegmentedComponentsFilters{}

	var fieldMasks []model.ReferralsSegmentedComponentFieldMask
	var fetchOnlyIDs bool

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case FetchOnlyIDs:
			if strings.ToUpper(filter.GetStringValue()) == "Y" {
				fetchOnlyIDs = true
				fieldMasks = append(fieldMasks, model.ID)
			}
		case Id:
			searchFilters.Ids = []string{filter.GetStringValue()}
		case SegmentId:
			searchFilters.SegmentIds = []string{filter.GetStringValue()}
		case Component:
			var chosenComponents []inAppReferralEnumPb.Component
			for _, chosenComponentString := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				chosenComponent := inAppReferralEnumPb.Component(inAppReferralEnumPb.Component_value[chosenComponentString])
				chosenComponents = append(chosenComponents, chosenComponent)
			}
			searchFilters.Components = chosenComponents

		case AppFeature:
			searchFilters.AppFeature = inAppReferralEnumPb.AppFeature(inAppReferralEnumPb.AppFeature_value[filter.GetDropdownValue()])

		case ActiveFrom:
			searchFilters.ActiveSince = filter.GetTimestamp().AsTime()
		case ActiveTill:
			searchFilters.ActiveTill = filter.GetTimestamp().AsTime()
		case CreatedAtSince:
			searchFilters.CreatedAtSince = filter.GetTimestamp().AsTime()
		case CreatedAtTill:
			searchFilters.CreatedAtTill = filter.GetTimestamp().AsTime()
		}
	}

	ReferralsSegmentedComponents, err := d.dao.FetchReferralsSegmentedComponents(ctx, searchFilters, fieldMasks)
	if err != nil {
		logger.Error(ctx, "failed to get ReferralsSegmentedComponent by id",
			zap.Error(err),
		)
		return "", err
	}

	if fetchOnlyIDs {
		var resIds []string
		for _, referralsSegmentedComponent := range ReferralsSegmentedComponents {
			resIds = append(resIds, referralsSegmentedComponent.Id)
		}

		e, marshalErr := json.Marshal(resIds)
		if marshalErr != nil {
			logger.Error(ctx, "cannot marshal struct to json")
			return "", marshalErr
		}
		return string(e), nil
	}

	e, err := json.Marshal(ReferralsSegmentedComponents)
	if err != nil {
		logger.Error(ctx, "cannot marshal struct to json")
		return "", err
	}
	return string(e), nil
}
