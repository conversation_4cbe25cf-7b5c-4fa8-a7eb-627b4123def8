package triggerhandler

import (
	"fmt"

	"github.com/google/wire"

	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
)

var FactoryWireSet = wire.NewSet(NewFactory, wire.Bind(new(IFactory), new(*Factory)))

type IFactory interface {
	GetHandler(trigger inAppReferralEnumPb.Trigger) (ITriggerHandler, error)
}

type Factory struct {
	referralUnlockedHandler        *ReferralUnlockedHandler
	performQualifyingActionHandler *PerformQualifyingActionHandler
	refereeAccountCreatedHandler   *RefereeAccountCreatedHandler
}

func NewFactory(
	referralUnlockedHandler *ReferralUnlockedHandler,
	performQualifyingActionHandler *PerformQualifyingActionHandler,
	refereeAccountCreatedHandler *RefereeAccountCreatedHandler,
) *Factory {
	return &Factory{
		referralUnlockedHandler:        referralUnlocked<PERSON><PERSON><PERSON>,
		performQualifyingActionHandler: performQualifyingAction<PERSON><PERSON><PERSON>,
		refereeAccountCreatedHandler:   refereeAccount<PERSON>reated<PERSON>and<PERSON>,
	}
}

func (f *Factory) GetHandler(trigger inAppReferralEnumPb.Trigger) (ITriggerHandler, error) {
	switch trigger {
	case inAppReferralEnumPb.Trigger_REFERRAL_UNLOCKED_TRIGGER:
		return f.referralUnlockedHandler, nil
	case inAppReferralEnumPb.Trigger_PERFORM_QUALIFYING_ACTION_TRIGGER:
		return f.performQualifyingActionHandler, nil
	case inAppReferralEnumPb.Trigger_REFEREE_ACCOUNT_CREATED_REMIND_REFERRER_TRIGGER:
		return f.refereeAccountCreatedHandler, nil
	default:
		return nil, fmt.Errorf("error getting handler for trigger. trigger received: %s", trigger.String())
	}
}
