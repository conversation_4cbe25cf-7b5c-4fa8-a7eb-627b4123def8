package consumer

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"

	queuePb "github.com/epifi/be-common/api/queue"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	accountsPb "github.com/epifi/gamma/api/accounts"
	enumsPb "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	productPb "github.com/epifi/gamma/api/product"
	savingsPb "github.com/epifi/gamma/api/savings"
	consumerPb "github.com/epifi/gamma/api/savings/consumer"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/pkg/accessrevoke"
	"github.com/epifi/gamma/savings/config/genconf"
	"github.com/epifi/gamma/savings/data"
	savingsSvc "github.com/epifi/gamma/savings/service"
	wireTypes "github.com/epifi/gamma/savings/wire/types"

	errPkg "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	updatedByConsumer   = "account status change consumer"
	AccountClosedRemark = "account closed at vendor"
	FullFreezeRemark    = "account full freeze at vendor"
	DebitFreezeRemark   = "account debit freeze at vendor"
	CreditFreezeRemark  = "account credit freeze at vendor"
	AccountActiveRemark = "account active with no constraints at vendor"
)

var accountOpsStatusToSavingsState = map[enumsPb.OperationalStatus]savingsPb.State{
	enumsPb.OperationalStatus_OPERATIONAL_STATUS_ACTIVE:   savingsPb.State_CREATED,
	enumsPb.OperationalStatus_OPERATIONAL_STATUS_INACTIVE: savingsPb.State_CREATED,
	enumsPb.OperationalStatus_OPERATIONAL_STATUS_DORMANT:  savingsPb.State_CREATED,
	enumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED:   savingsPb.State_CLOSED,
}

var savingsFreezeToOpsFreezeStatus = map[savingsPb.Restriction]enumsPb.FreezeStatus{
	savingsPb.Restriction_RESTRICTION_CREDIT_FREEZE: enumsPb.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE,
	savingsPb.Restriction_RESTRICTION_DEBIT_FREEZE:  enumsPb.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE,
}

type OperStatusUpdateConsumer struct {
	config                 *genconf.Config
	savingsClient          savingsPb.SavingsClient
	actorClient            actorPb.ActorClient
	accountStatePub        wireTypes.AccountStatePublisher
	saClosureDataProcessor data.ISaClosureDataProcessor
	usersClient            user.UsersClient
	productClient          productPb.ProductClient
}

func NewOperStatusUpdateConsumer(config *genconf.Config, savingsClient savingsPb.SavingsClient,
	actorClient actorPb.ActorClient, accountStatePub wireTypes.AccountStatePublisher,
	saClosureDataProcessor data.ISaClosureDataProcessor, usersClient user.UsersClient, productClient productPb.ProductClient) *OperStatusUpdateConsumer {
	return &OperStatusUpdateConsumer{
		config:                 config,
		savingsClient:          savingsClient,
		actorClient:            actorClient,
		accountStatePub:        accountStatePub,
		saClosureDataProcessor: saClosureDataProcessor,
		usersClient:            usersClient,
		productClient:          productClient,
	}
}

func (o *OperStatusUpdateConsumer) UpdateSavingsAccountStatus(ctx context.Context, event *operstatus.OperationalStatusUpdateEvent) (*consumerPb.UpdateSavingsAccountStatusResponse, error) {
	ops := event.GetOperationalStatusInfo()

	if ops.GetAccountType() != accountsPb.Type_SAVINGS || ops.GetPartnerBank() != commonvgpb.Vendor_FEDERAL_BANK ||
		ops.GetAccountId() == "" {
		logger.Error(ctx, "account identifier not mentioned", zap.String(logger.ACCOUNT_ID, ops.GetAccountId()),
			zap.String(logger.ACCOUNT_TYPE, ops.GetAccountType().String()), zap.String(logger.VENDOR, ops.GetPartnerBank().String()))
		return getPermanentFailureResponse(), nil
	}

	savResp, err := o.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_Id{
			Id: ops.GetAccountId(),
		},
	})
	if err != nil || savResp == nil {
		logger.Error(ctx, "could not get savings account by id", zap.Error(err), zap.String(logger.ACCOUNT_ID, ops.GetAccountId()))
		return getPermanentFailureResponse(), nil
	}

	upErr := o.handleStatusChange(ctx, ops, savResp.GetAccount())
	if upErr != nil {
		// all errors are permanent error
		return getPermanentFailureResponse(), nil
	}

	return getSuccessResponse(), nil
}

func (o *OperStatusUpdateConsumer) handleStatusChange(ctx context.Context, ops *operstatus.OperationalStatusInfo, sav *savingsPb.Account) error {
	var (
		constraints = sav.GetConstraints()
		state       = sav.GetState()
		skuInfo     = sav.GetSkuInfo()
		ok          bool
		updateMask  []savingsPb.AccountFieldMask
	)

	isOperStatusSame := DoesOperStatusMatch(ctx, ops.GetOperationalStatus(), sav.GetState(), sav.GetConstraints().GetAccessLevel())
	isFreezeStatusSame := DoesFreezeStatusMatch(ctx, ops.GetOperationalStatus(), ops.GetFreezeStatus(), sav.GetConstraints())

	if !isOperStatusSame {
		updateMask = append(updateMask, savingsPb.AccountFieldMask_STATE)
		state, ok = accountOpsStatusToSavingsState[ops.GetOperationalStatus()]
		if !ok {
			logger.Error(ctx, "could not match operational status to savings state", zap.String(logger.STATUS, ops.GetOperationalStatus().String()))
			return fmt.Errorf("could not match operational status to savings state")
		}
	}
	if !isFreezeStatusSame {
		updateMask = append(updateMask, savingsPb.AccountFieldMask_CONSTRAINTS)
		constraints = &savingsPb.AccountConstraints{
			AccessLevel:   getAccessLevelFromOperStatuses(ops.GetOperationalStatus(), ops.GetFreezeStatus()),
			Restrictions:  getRestrictionsFromOperStatuses(ops.GetFreezeStatus()),
			UpdateDetails: getConstraintsUpdateDtlsFromOperStatus(ops.GetOperationalStatus(), ops.GetFreezeStatus()),
		}
	}

	// Check for SKU changes
	updatedSkuInfo, err := o.checkSkuUpdates(ctx, ops.GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetSchemeCode(), sav)
	switch {
	case err != nil:
		return errPkg.Wrap(err, "failed to check SKU updates")
	case updatedSkuInfo != nil:
		updateMask = append(updateMask, savingsPb.AccountFieldMask_SKU_INFO_AND_METADATA)
		skuInfo = updatedSkuInfo
	}

	if len(updateMask) == 0 {
		return nil
	}

	if state == savingsPb.State_CLOSED {
		shouldBlock, err := o.shouldBlockUserAccess(ctx, sav)
		if err != nil {
			logger.Error(ctx, "error in user access block pre-checks", zap.Error(err))
		} else if shouldBlock {
			if err := o.revokeAppAccess(ctx, sav); err != nil {
				logger.Error(ctx, "failed to update access revoke details", zap.Error(err))
			}
		}
	}

	updateResp, updateErr := o.savingsClient.UpdateAccount(ctx, &savingsPb.UpdateAccountRequest{
		Identifier: &savingsPb.UpdateAccountRequest_Id{
			Id: sav.GetId(),
		},
		Constraints: constraints,
		State:       state,
		SkuInfo:     skuInfo,
		UpdateMask:  updateMask,
	})
	if err := epifigrpc.RPCError(updateResp, updateErr); err != nil {
		logger.Error(ctx, "error while updating saving account statuses", zap.Error(err), zap.String(logger.ACCOUNT_ID, sav.GetId()))
		return err
	}

	if !isOperStatusSame {
		o.publishSavingsStateChangeAsync(ctx, updateResp.GetAccount())

		closureReqUpdErr := o.handleSaClosureRequestUpdate(ctx, sav, state)
		if closureReqUpdErr != nil {
			logger.Error(ctx, "failed to handle sa closure request update", zap.Error(closureReqUpdErr))
			return closureReqUpdErr
		}
	}
	return nil
}

func (o *OperStatusUpdateConsumer) getActiveProducts(ctx context.Context, actorId string) ([]productPb.ProductType, error) {
	productStatusRes, err := o.productClient.GetProductsStatus(ctx, &productPb.GetProductsStatusRequest{
		ActorId:      actorId,
		ProductTypes: getAllProductTypes(),
	})
	if rpcErr := epifigrpc.RPCError(productStatusRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting product status", zap.Error(rpcErr))
		return nil, rpcErr
	}
	activeProducts := make([]productPb.ProductType, 0)
	for k, v := range productStatusRes.GetProductInfoMap() {
		if v.GetProductStatus() == productPb.ProductStatus_PRODUCT_STATUS_ACTIVE {
			activeProducts = append(activeProducts, productPb.ProductType(productPb.ProductType_value[k]))
		}
	}
	return activeProducts, nil
}

func getAllProductTypes() []productPb.ProductType {
	productTypes := make([]productPb.ProductType, 0)
	for k, v := range productPb.ProductType_name {
		if v != productPb.ProductType_PRODUCT_TYPE_UNSPECIFIED.String() {
			productTypes = append(productTypes, productPb.ProductType(k))
		}
	}
	return productTypes
}

// checkSkuUpdates checks if the SKU needs to be updated based on the scheme code from vendor response.
// It ensures that SKU updates don't interfere with ongoing scheme change workflows by checking ClientReqIds.
//
// The function will:
// - Skip update if scheme code is empty
// - Skip update if there are 2 ClientReqIds (indicating an ongoing workflow)
// - Map scheme code to corresponding SKU
// - Update SKU only if it differs from current SKU
//
// Returns:
//   - *savingsPb.SKUInfo: updated SKU info if changes needed, nil if no update required
//   - error: if there was an error in the process (e.g., scheme code mapping error)
func (o *OperStatusUpdateConsumer) checkSkuUpdates(ctx context.Context, schemeCode string, sav *savingsPb.Account) (*savingsPb.SKUInfo, error) {
	if schemeCode == "" {
		return nil, nil
	}

	// If there are 2 ClientReqIds, a workflow is in progress, skip update
	if len(sav.GetSkuInfo().GetFederal().GetClientReqIds()) == 2 {
		logger.Info(ctx, "skipping SKU update as workflow is in progress",
			zap.String(logger.ACCOUNT_ID, sav.GetId()))
		return nil, nil
	}

	newSku, skuErr := savingsSvc.GetSkuFromSchemeCode(o.config.VendorSKUMapping(), schemeCode)
	if skuErr != nil {
		logger.Error(ctx, "failed to get SKU from scheme code",
			zap.String("schemeCode", schemeCode),
			zap.String(logger.ACCOUNT_ID, sav.GetId()),
			zap.Error(skuErr))
		return nil, fmt.Errorf("failed to get SKU from scheme code %s: %w", schemeCode, skuErr)
	}

	currentSku := sav.GetSkuInfo().GetSku()
	if newSku == currentSku {
		return nil, nil
	}

	logger.Info(ctx, "SKU change detected",
		zap.String("oldSku", currentSku.String()),
		zap.String("newSku", newSku.String()),
		zap.String("schemeCode", schemeCode))

	// Update only the relevant fields in existing SKU info
	skuInfo := &savingsPb.SKUInfo{
		AccountProductOffering: sav.GetSkuInfo().GetAccountProductOffering(),
		Sku:                    newSku,
		VendorSkuInfo: &savingsPb.SKUInfo_Federal{
			Federal: &savingsPb.FederalSKUInfo{
				Sku:          schemeCode,
				UpdatedAt:    timestamppb.Now(),
				ClientReqIds: sav.GetSkuInfo().GetFederal().GetClientReqIds(),
			},
		},
	}

	return skuInfo, nil
}

func (o *OperStatusUpdateConsumer) handleSaClosureRequestUpdate(ctx context.Context, sav *savingsPb.Account, state savingsPb.State) error {
	if state != savingsPb.State_CLOSED {
		return nil
	}

	closureReq, getErr := o.saClosureDataProcessor.GetActiveSaClosureRequest(ctx, sav.GetActorId(), []savingsPb.SavingsAccountClosureRequestFieldMask{
		savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_ID,
		savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_SAVINGS_ACCOUNT_ID,
		savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_STATUS,
	})
	if getErr != nil {
		if errors.Is(getErr, epifierrors.ErrRecordNotFound) {
			return nil
		}

		return fmt.Errorf("failed to get active sa closure request for actor, %w", getErr)
	}

	if closureReq.GetSavingsAccountId() != sav.GetId() {
		// this should not happen ideally
		logger.Error(ctx, "mismatch in account id in closure request and savings account",
			zap.String(logger.SA_CLOSURE_ID, closureReq.GetId()),
			zap.String(logger.ACCOUNT_ID, sav.GetId()),
			zap.String("closure_req_acc_id", closureReq.GetSavingsAccountId()),
		)

		return fmt.Errorf("mismatch in account id in closure request and savings account")
	}

	if closureReq.GetStatus() != savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_SUBMITTED &&
		closureReq.GetStatus() != savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_SENT_TO_OPS {
		return nil
	}

	updateErr := o.saClosureDataProcessor.UpdateStatus(ctx, closureReq.GetId(),
		savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_COMPLETED_SUCCESSFULLY,
		savingsPb.SAClosureRequestStatusReason_SA_CLOSURE_REQUEST_STATUS_REASON_UNSPECIFIED,
	)
	if updateErr != nil {
		return fmt.Errorf("failed to update status of sa closure request, %w", updateErr)
	}

	return nil
}

func (o *OperStatusUpdateConsumer) publishSavingsStateChangeAsync(ctx context.Context, sav *savingsPb.Account) {
	// TODO: https://monorail.pointz.in/p/fi-app/issues/detail?id=46107
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		actorResp, actorErr := o.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{
			Id: sav.GetActorId(),
		})
		if err := epifigrpc.RPCError(actorResp, actorErr); err != nil {
			logger.Error(ctx, "error while getting actor by id", zap.Error(err), zap.String(logger.ACTOR_ID_V2, sav.GetActorId()))
			return
		}
		if messageId, err := o.accountStatePub.Publish(ctx, &savingsPb.AccountStateUpdateEvent{
			Actor:   actorResp.GetActor(),
			Account: sav,
		}); err != nil {
			logger.Error(ctx, "error in publishing account state change", zap.Error(err),
				zap.String(logger.ACCOUNT_ID, sav.GetId()), zap.String("message id", messageId),
			)
		}
	})
}

// DoesOperStatusMatch checks if the operational status at savings service matches with accounts service data
// The only comparison made here is of savings account state, which can be either open / closed.
func DoesOperStatusMatch(ctx context.Context, opsStatus enumsPb.OperationalStatus, savState savingsPb.State, level savingsPb.AccessLevel) bool {
	opsState, ok := accountOpsStatusToSavingsState[opsStatus]
	if !ok {
		logger.Error(ctx, "could not match operational status to savings state", zap.String(logger.STATUS, opsStatus.String()))
		return true
	}

	if opsState == savingsPb.State_CLOSED {
		return savState == savingsPb.State_CLOSED && level == savingsPb.AccessLevel_ACCESS_LEVEL_NO_ACCESS
	}
	// not making comparison on level as that is also influenced by freeze state
	return savState == savingsPb.State_CREATED
}

// DoesFreezeStatusMatch checks if freeze status at savings service matches with accounts service data
//  1. savings.AccessLevel_ACCESS_LEVEL_NO_ACCESS + savings.ConstraintsUpdateReason +
//     ConstraintsUpdateReason.Remarks together map to full freeze or account closed
//  2. savings.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS + savings.Restriction maps to credit or debit freeze
//  3. savings.AccessLevel_ACCESS_LEVEL_FULL_ACCESS/savings.AccessLevel_ACCESS_LEVEL_UNSCPECIFIED maps to no freeze active account
func DoesFreezeStatusMatch(ctx context.Context, opsStatus enumsPb.OperationalStatus, opsFreeze enumsPb.FreezeStatus, savConstraints *savingsPb.AccountConstraints) bool {
	switch savConstraints.GetAccessLevel() {
	case savingsPb.AccessLevel_ACCESS_LEVEL_NO_ACCESS:
		return opsFreeze == enumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE || isAccountClosedAlready(opsStatus, savConstraints)
	case savingsPb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS:
		if len(savConstraints.GetRestrictions()) != 1 {
			return false
		}
		restriction := savConstraints.GetRestrictions()[0]
		savFreeze, ok := savingsFreezeToOpsFreezeStatus[restriction]
		if !ok {
			logger.Error(ctx, "could not match savings restriction to operational freeze status", zap.String("restriction", restriction.String()))
			return false
		}
		return opsFreeze == savFreeze
	default:
		if opsStatus == enumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED {
			return false
		}
		return opsFreeze == enumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED
	}
}

func isAccountClosedAlready(opsStatus enumsPb.OperationalStatus, savConstraints *savingsPb.AccountConstraints) bool {
	isClosedByEnquiry := opsStatus == enumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED &&
		savConstraints.GetUpdateDetails().GetReason().IsAccountClosedReason()

	isClosedElsewhere := opsStatus == enumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED &&
		savConstraints.GetUpdateDetails().GetReason() == savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_OTHER &&
		strings.EqualFold(savConstraints.GetUpdateDetails().GetRemarks(), AccountClosedRemark)

	return isClosedByEnquiry || isClosedElsewhere
}

func getAccessLevelFromOperStatuses(status enumsPb.OperationalStatus, freezeStatus enumsPb.FreezeStatus) savingsPb.AccessLevel {
	switch {
	case status == enumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED, freezeStatus == enumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE:
		return savingsPb.AccessLevel_ACCESS_LEVEL_NO_ACCESS
	case freezeStatus == enumsPb.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE, freezeStatus == enumsPb.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE:
		return savingsPb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS
	default:
		return savingsPb.AccessLevel_ACCESS_LEVEL_FULL_ACCESS
	}
}

func getRestrictionsFromOperStatuses(freezeStatus enumsPb.FreezeStatus) []savingsPb.Restriction {
	switch {
	case freezeStatus == enumsPb.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE:
		return []savingsPb.Restriction{savingsPb.Restriction_RESTRICTION_CREDIT_FREEZE}
	case freezeStatus == enumsPb.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE:
		return []savingsPb.Restriction{savingsPb.Restriction_RESTRICTION_DEBIT_FREEZE}
	default:
		return nil
	}
}

func getConstraintsUpdateDtlsFromOperStatus(status enumsPb.OperationalStatus, freezeStatus enumsPb.FreezeStatus) *savingsPb.ConstraintsUpdateDetails {
	switch {
	case status == enumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED:
		return &savingsPb.ConstraintsUpdateDetails{
			Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_OTHER,
			Remarks:   AccountClosedRemark,
			UpdatedBy: updatedByConsumer,
			UpdatedAt: timestampPb.Now(),
		}
	case freezeStatus == enumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE:
		return &savingsPb.ConstraintsUpdateDetails{
			Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_FRAUDULENT_ACCOUNT,
			Remarks:   FullFreezeRemark,
			UpdatedBy: updatedByConsumer,
			UpdatedAt: timestampPb.Now(),
		}
	case freezeStatus == enumsPb.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE:
		return &savingsPb.ConstraintsUpdateDetails{
			Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_FRAUDULENT_ACCOUNT,
			Remarks:   CreditFreezeRemark,
			UpdatedBy: updatedByConsumer,
			UpdatedAt: timestampPb.Now(),
		}
	case freezeStatus == enumsPb.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE:
		return &savingsPb.ConstraintsUpdateDetails{
			Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_FRAUDULENT_ACCOUNT,
			Remarks:   DebitFreezeRemark,
			UpdatedBy: updatedByConsumer,
			UpdatedAt: timestampPb.Now(),
		}
	default:
		return &savingsPb.ConstraintsUpdateDetails{
			Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_OTHER,
			Remarks:   AccountActiveRemark,
			UpdatedBy: updatedByConsumer,
			UpdatedAt: timestampPb.Now(),
		}
	}
}

func getPermanentFailureResponse() *consumerPb.UpdateSavingsAccountStatusResponse {
	return &consumerPb.UpdateSavingsAccountStatusResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
		},
	}
}

func getSuccessResponse() *consumerPb.UpdateSavingsAccountStatusResponse {
	return &consumerPb.UpdateSavingsAccountStatusResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		},
	}
}

// shouldBlockUserAccess checks if the user's app access should be blocked based on current status and active products.
func (o *OperStatusUpdateConsumer) shouldBlockUserAccess(ctx context.Context, sav *savingsPb.Account) (bool, error) {
	minUserReq := &user.GetMinimalUserRequest{
		Identifier: &user.GetMinimalUserRequest_Id{
			Id: sav.GetPrimaryAccountHolder(),
		},
	}
	minUserRes, minUserErr := o.usersClient.GetMinimalUser(ctx, minUserReq)
	if err := epifigrpc.RPCError(minUserRes, minUserErr); err != nil {
		logger.Error(ctx, "error in getting minimal user", zap.Error(err))
		return false, err
	}
	if minUserRes.GetMinimalUser().GetAccessRevokeStatus() == user.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED {
		logger.Info(ctx, "app access is already revoked")
		return false, nil
	}
	activeProducts, err := o.getActiveProducts(ctx, sav.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting active products", zap.Error(err))
		return false, err
	}
	switch {
	case len(activeProducts) == 0:
		return true, nil
	case lo.Contains(activeProducts, productPb.ProductType_PRODUCT_TYPE_CREDIT_CARD) ||
		lo.Contains(activeProducts, productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS):
		return false, nil
	}
	return true, nil
}

// revokeAppAccess blocks the user's app access by updating access revoke details.
func (o *OperStatusUpdateConsumer) revokeAppAccess(ctx context.Context, sav *savingsPb.Account) error {
	updateReq := &user.UpdateAccessRevokeDetailsRequest_UserIdentifier{
		Identifier: &user.UpdateAccessRevokeDetailsRequest_UserIdentifier_ActorId{
			ActorId: sav.GetActorId(),
		},
	}
	accessRevokeDetails := &user.AccessRevokeDetails{
		AccessRevokeStatus: user.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED,
		Reason:             user.AccessRevokeReason_ACCESS_REVOKE_REASON_ACCOUNT_DELETION_REQUEST,
		UpdatedAt:          timestampPb.Now(),
		UpdatedBy:          AccountClosedRemark,
	}
	return accessrevoke.UpdateAppAccessRevokeDetails(ctx, o.usersClient, updateReq, accessRevokeDetails, true)
}
