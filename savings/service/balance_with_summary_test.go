package service_test

import (
	"context"
	json2 "encoding/json"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/now"
	"github.com/shopspring/decimal"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/money"
	testPkg "github.com/epifi/be-common/pkg/test"

	accountsPb "github.com/epifi/gamma/api/accounts"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/balance/enums"
	bePaymentPb "github.com/epifi/gamma/api/order/payment"
	reconPb "github.com/epifi/gamma/api/order/recon"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	searchPb "github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/search/txn"
	userPb "github.com/epifi/gamma/api/user"
	txnAggregate "github.com/epifi/gamma/savings/service/transactionAggregate"
)

type txnAggrArgMatcher struct {
	want *txnAggregate.TxnAggregateRequestParams
}

func newTxnAggrArgMatcher(want *txnAggregate.TxnAggregateRequestParams) *txnAggrArgMatcher {
	return &txnAggrArgMatcher{want: want}
}

func (s *txnAggrArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*txnAggregate.TxnAggregateRequestParams)
	if !ok {
		return false
	}

	s.want.ToTime = got.ToTime
	s.want.FromTime = got.FromTime
	return reflect.DeepEqual(s.want, got)
}

func (s *txnAggrArgMatcher) String() string {
	payload, err := json2.Marshal(s.want)
	if err != nil {
		return ""
	}
	return string(payload)
}

type balanceUpdateArgumentMatcher struct {
	wantBalance *moneyPb.Money
}

func newBalanceUpdateArgumentMatcher(wantBalance *moneyPb.Money) *balanceUpdateArgumentMatcher {
	return &balanceUpdateArgumentMatcher{wantBalance: wantBalance}
}

func (b *balanceUpdateArgumentMatcher) Matches(x interface{}) bool {
	got, ok := x.(*savingsPb.Account)
	if !ok {
		return false
	}

	return proto.Equal(b.wantBalance, got.BalanceFromPartner)
}

func (b *balanceUpdateArgumentMatcher) String() string {
	return b.wantBalance.String()
}

type openingBalanceUpdateArgumentMatcher struct {
	want *savingsPb.OpeningBalanceInfo
}

func newOpeningBalanceUpdateArgumentMatcher(want *savingsPb.OpeningBalanceInfo) *openingBalanceUpdateArgumentMatcher {
	return &openingBalanceUpdateArgumentMatcher{want: want}
}

func (o *openingBalanceUpdateArgumentMatcher) Matches(x interface{}) bool {
	got, ok := x.(*savingsPb.Account)
	if !ok {
		return false
	}

	return proto.Equal(o.want, got.OpeningBalanceInfo)
}

func (o *openingBalanceUpdateArgumentMatcher) String() string {
	return o.want.String()
}

func TestSavingsService_GetAccountBalanceWithSummary(t *testing.T) {
	var (
		testActor          = "actor-1"
		testSavingsId      = "savings-1"
		testUserId         = "user-1"
		testSavingsAccount = &savingsPb.Account{
			Id:                   testSavingsId,
			AccountNo:            "acct-1",
			IfscCode:             "ifsc",
			PrimaryAccountHolder: testUserId,
			PhoneNumber: &commontypes.PhoneNumber{
				CountryCode:    91,
				NationalNumber: ***********,
			},
			PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
			BalanceFromPartner: money.ParseDecimal(decimal.NewFromInt(3000), money.RupeeCurrencyCode),
		}
		testBalanceUpdatedAt = timestamppb.Now()
		bomTime              = now.With(time.Now().In(datetime.IST)).BeginningOfMonth()
		bomDate              = datetime.TimeToDate(&bomTime)
		// bomTs                = timestamppb.New(bomTime)
		testPiIds = []string{"pi-1", "pi-2"}
	)

	accountOpeningTime := bomTime.AddDate(0, -1, 0)
	testSavingsAccount.CreatedAt = timestamppb.New(accountOpeningTime)
	openingDate := datetime.TimeToDate(&accountOpeningTime)

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	type mockGetAccountById struct {
		enable bool
		id     string
		res    *savingsPb.Account
		err    error
	}
	type mockGetUser struct {
		enable bool
		req    *userPb.GetUserRequest
		res    *userPb.GetUserResponse
		err    error
	}
	type mockGetOpeningBalance struct {
		enable bool
		req    *accountBalancePb.GetOpeningBalanceRequest
		res    *accountBalancePb.GetOpeningBalanceResponse
		err    error
	}
	type mockGetPiByAccountId struct {
		enable bool
		req    *accountPiPb.GetPiByAccountIdRequest
		res    *accountPiPb.GetPiByAccountIdResponse
		err    error
	}
	type mockGetTxnAggregates struct {
		req *searchPb.GetTransactionAggregateRequest
		res *searchPb.GetTransactionAggregateResponse
		err error
	}

	type mockGetPayTxnAggregates struct {
		req *payPb.GetTransactionAggregatesRequest
		res *payPb.GetTransactionAggregatesResponse
		err error
	}

	type mockCalculateTransactionAggregate struct {
		req    *txnAggregate.TxnAggregateRequestParams
		credit *moneyPb.Money
		debit  *moneyPb.Money
		err    error
	}

	type mockGetReconStatus struct {
		enable bool
		req    *reconPb.GetAccountLedgerReconciliationStatusRequest
		res    *reconPb.GetAccountLedgerReconciliationStatusResponse
		err    error
	}
	type mockReconcileAccount struct {
		enable bool
		req    *reconPb.ReconcileAccountLedgerRequest
		res    *reconPb.ReconcileAccountLedgerResponse
		err    error
	}
	type mockGetBalance struct {
		req *accountBalancePb.GetAccountBalanceRequest
		res *accountBalancePb.GetAccountBalanceResponse
		err error
	}
	var tests = []struct {
		name                              string
		mockGetAccountById                mockGetAccountById
		mockGetUser                       mockGetUser
		mockGetOpeningBalance             mockGetOpeningBalance
		mockGetPiByAccountId              mockGetPiByAccountId
		mockGetTxnAggregates              []*mockGetTxnAggregates
		mockGetPayTxnAggregates           []*mockGetPayTxnAggregates
		mockCalculateTransactionAggregate []*mockCalculateTransactionAggregate
		mockGetReconStatus                mockGetReconStatus
		mockReconcileAccount              mockReconcileAccount
		mockGetBalance                    []*mockGetBalance
		req                               *savingsPb.GetAccountBalanceWithSummaryRequest
		want                              *savingsPb.GetAccountBalanceWithSummaryResponse
		wantErr                           bool
	}{
		{
			name: "Should successfully fetch account balance and summary with vendor and epiFi balance matching",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res:    testSavingsAccount,
				err:    nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
					Date:       bomDate,
					ActorId:    testActor,
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:           rpc.StatusOk(),
				OpeningBalance:   money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				TotalSpent:       money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:      money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:       money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				BalanceAt: testBalanceUpdatedAt,
			},
		},
		{
			name: "Should successfully fetch account balance and fallback to vendor as epiFi balance mis-matching",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					CreatedAt:   testSavingsAccount.GetCreatedAt(),
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockGetTxnAggregates: []*mockGetTxnAggregates{
				{
					req: &searchPb.GetTransactionAggregateRequest{
						ActorId:      testActor,
						ToTime:       nil,
						AggField:     []txn.TransactionType{txn.TransactionType_TRANSACTION_TYPE_CREDIT, txn.TransactionType_TRANSACTION_TYPE_DEBIT},
						AggFieldType: searchPb.GetTransactionAggregateRequest_PI_ID,
						PiIdList:     testPiIds,
					},
					res: &searchPb.GetTransactionAggregateResponse{
						Status: rpc.StatusOk(),
						Credit: &txn.CreditAggr{Amount: money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode)},
						Debit:  &txn.DebitAggr{Amount: money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode)},
						Saved:  &txn.SavedAggr{Amount: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode)},
					},
					err: nil,
				},
			},
			mockGetPayTxnAggregates: []*mockGetPayTxnAggregates{
				{
					req: &payPb.GetTransactionAggregatesRequest{
						ActorId:             testActor,
						ToTime:              nil,
						AccountingEntryType: bePaymentPb.AccountingEntryType_CREDIT,
						AccountTypes:        []accountsPb.Type{accountsPb.Type_SAVINGS},
						PiIds:               testPiIds,
					},
					res: &payPb.GetTransactionAggregatesResponse{
						Status: rpc.StatusOk(),
						TransactionAggregates: &payPb.TransactionAggregates{
							SumAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
							Count:     0,
						},
					},
					err: nil,
				},
				{
					req: &payPb.GetTransactionAggregatesRequest{
						ActorId:             testActor,
						ToTime:              nil,
						AccountingEntryType: bePaymentPb.AccountingEntryType_DEBIT,
						AccountTypes:        []accountsPb.Type{accountsPb.Type_SAVINGS},
						PiIds:               testPiIds,
					},
					res: &payPb.GetTransactionAggregatesResponse{
						Status: rpc.StatusOk(),
						TransactionAggregates: &payPb.TransactionAggregates{
							SumAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 600},
							Count:     0,
						},
					},
					err: nil,
				},
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			mockReconcileAccount: mockReconcileAccount{
				enable: true,
				req: &reconPb.ReconcileAccountLedgerRequest{
					SavingsAccountId:     testSavingsId,
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
					AccountNo:            testSavingsAccount.GetAccountNo(),
					PrimaryAccountHolder: testSavingsAccount.GetPrimaryAccountHolder(),
					AccOpeningDate:       openingDate,
					ActorId:              testActor,
				},
				res: &reconPb.ReconcileAccountLedgerResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:                 rpc.StatusOk(),
				OpeningBalance:         money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				AvailableBalance:       money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:          money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
				TotalSpent:             money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:            money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:             money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				IsComputedBalanceStale: true,
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				BalanceAt: testBalanceUpdatedAt,
			},
		},
		{
			name: "Should return balance fetched from vendor when failed to fetch balance from db but vendor call succeeded",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					CreatedAt:   testSavingsAccount.GetCreatedAt(),
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status: rpc.StatusInternal(),
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockGetTxnAggregates: []*mockGetTxnAggregates{
				{
					req: &searchPb.GetTransactionAggregateRequest{
						ActorId:      testActor,
						ToTime:       nil,
						AggField:     []txn.TransactionType{txn.TransactionType_TRANSACTION_TYPE_CREDIT, txn.TransactionType_TRANSACTION_TYPE_DEBIT},
						AggFieldType: searchPb.GetTransactionAggregateRequest_PI_ID,
						PiIdList:     testPiIds,
					},
					res: &searchPb.GetTransactionAggregateResponse{
						Status: rpc.StatusOk(),
						Credit: &txn.CreditAggr{Amount: money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode)},
						Debit:  &txn.DebitAggr{Amount: money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode)},
						Saved:  &txn.SavedAggr{Amount: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode)},
					},
					err: nil,
				},
			},
			mockGetPayTxnAggregates: []*mockGetPayTxnAggregates{
				{
					req: &payPb.GetTransactionAggregatesRequest{
						ActorId:             testActor,
						ToTime:              nil,
						AccountingEntryType: bePaymentPb.AccountingEntryType_CREDIT,
						AccountTypes:        []accountsPb.Type{accountsPb.Type_SAVINGS},
						PiIds:               testPiIds,
					},
					res: &payPb.GetTransactionAggregatesResponse{
						Status: rpc.StatusOk(),
						TransactionAggregates: &payPb.TransactionAggregates{
							SumAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
							Count:     0,
						},
					},
					err: nil,
				},
				{
					req: &payPb.GetTransactionAggregatesRequest{
						ActorId:             testActor,
						ToTime:              nil,
						AccountingEntryType: bePaymentPb.AccountingEntryType_DEBIT,
						AccountTypes:        []accountsPb.Type{accountsPb.Type_SAVINGS},
						PiIds:               testPiIds,
					},
					res: &payPb.GetTransactionAggregatesResponse{
						Status: rpc.StatusOk(),
						TransactionAggregates: &payPb.TransactionAggregates{
							SumAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 600},
							Count:     0,
						},
					},
					err: nil,
				},
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			mockReconcileAccount: mockReconcileAccount{
				enable: true,
				req: &reconPb.ReconcileAccountLedgerRequest{
					SavingsAccountId:     testSavingsId,
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
					AccountNo:            testSavingsAccount.GetAccountNo(),
					PrimaryAccountHolder: testSavingsAccount.GetPrimaryAccountHolder(),
					AccOpeningDate:       openingDate,
					ActorId:              testActor,
				},
				res: &reconPb.ReconcileAccountLedgerResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:                 rpc.StatusOk(),
				OpeningBalance:         money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				AvailableBalance:       money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:          money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
				TotalSpent:             money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:            money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:             money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				IsComputedBalanceStale: true,
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				BalanceAt: testBalanceUpdatedAt,
			},
		},
		{
			name: "Should return balance fetched from db even when vendor call to fetch fails",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					CreatedAt:   testSavingsAccount.GetCreatedAt(),
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status: rpc.StatusInternal(),
					},
					err: nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockGetTxnAggregates: []*mockGetTxnAggregates{
				{
					req: &searchPb.GetTransactionAggregateRequest{
						ActorId:      testActor,
						ToTime:       nil,
						AggField:     []txn.TransactionType{txn.TransactionType_TRANSACTION_TYPE_CREDIT, txn.TransactionType_TRANSACTION_TYPE_DEBIT},
						AggFieldType: searchPb.GetTransactionAggregateRequest_PI_ID,
						PiIdList:     testPiIds,
					},
					res: &searchPb.GetTransactionAggregateResponse{
						Status: rpc.StatusOk(),
						Credit: &txn.CreditAggr{Amount: money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode)},
						Debit:  &txn.DebitAggr{Amount: money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode)},
						Saved:  &txn.SavedAggr{Amount: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode)},
					},
					err: nil,
				},
			},
			mockGetPayTxnAggregates: []*mockGetPayTxnAggregates{
				{
					req: &payPb.GetTransactionAggregatesRequest{
						ActorId:             testActor,
						ToTime:              nil,
						AccountingEntryType: bePaymentPb.AccountingEntryType_CREDIT,
						AccountTypes:        []accountsPb.Type{accountsPb.Type_SAVINGS},
						PiIds:               testPiIds,
					},
					res: &payPb.GetTransactionAggregatesResponse{
						Status: rpc.StatusOk(),
						TransactionAggregates: &payPb.TransactionAggregates{
							SumAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
							Count:     0,
						},
					},
					err: nil,
				},
				{
					req: &payPb.GetTransactionAggregatesRequest{
						ActorId:             testActor,
						ToTime:              nil,
						AccountingEntryType: bePaymentPb.AccountingEntryType_DEBIT,
						AccountTypes:        []accountsPb.Type{accountsPb.Type_SAVINGS},
						PiIds:               testPiIds,
					},
					res: &payPb.GetTransactionAggregatesResponse{
						Status: rpc.StatusOk(),
						TransactionAggregates: &payPb.TransactionAggregates{
							SumAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 600},
							Count:     0,
						},
					},
					err: nil,
				},
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:           rpc.StatusOk(),
				OpeningBalance:   money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
				TotalSpent:       money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:      money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:       money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				IsBalanceStale:   true,
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				BalanceAt: testBalanceUpdatedAt,
			},
		},
		{
			name: "Should return internal server error when failed to fetch balance from db and vendor",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					CreatedAt:   testSavingsAccount.GetCreatedAt(),
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status: rpc.StatusInternal(),
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status: rpc.StatusInternal(),
					},
					err: nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockGetTxnAggregates: []*mockGetTxnAggregates{
				{
					req: &searchPb.GetTransactionAggregateRequest{
						ActorId:      testActor,
						ToTime:       nil,
						AggField:     []txn.TransactionType{txn.TransactionType_TRANSACTION_TYPE_CREDIT, txn.TransactionType_TRANSACTION_TYPE_DEBIT},
						AggFieldType: searchPb.GetTransactionAggregateRequest_PI_ID,
						PiIdList:     testPiIds,
					},
					res: &searchPb.GetTransactionAggregateResponse{
						Status: rpc.StatusOk(),
						Credit: &txn.CreditAggr{Amount: money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode)},
						Debit:  &txn.DebitAggr{Amount: money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode)},
						Saved:  &txn.SavedAggr{Amount: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode)},
					},
					err: nil,
				},
			},
			mockGetPayTxnAggregates: []*mockGetPayTxnAggregates{
				{
					req: &payPb.GetTransactionAggregatesRequest{
						ActorId:             testActor,
						ToTime:              nil,
						AccountingEntryType: bePaymentPb.AccountingEntryType_CREDIT,
						AccountTypes:        []accountsPb.Type{accountsPb.Type_SAVINGS},
						PiIds:               testPiIds,
					},
					res: &payPb.GetTransactionAggregatesResponse{
						Status: rpc.StatusOk(),
						TransactionAggregates: &payPb.TransactionAggregates{
							SumAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
							Count:     0,
						},
					},
					err: nil,
				},
				{
					req: &payPb.GetTransactionAggregatesRequest{
						ActorId:             testActor,
						ToTime:              nil,
						AccountingEntryType: bePaymentPb.AccountingEntryType_DEBIT,
						AccountTypes:        []accountsPb.Type{accountsPb.Type_SAVINGS},
						PiIds:               testPiIds,
					},
					res: &payPb.GetTransactionAggregatesResponse{
						Status: rpc.StatusOk(),
						TransactionAggregates: &payPb.TransactionAggregates{
							SumAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 600},
							Count:     0,
						},
					},
					err: nil,
				},
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:      rpc.StatusInternal(),
				TotalSpent:  money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit: money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:  money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
			},
		},
		{
			name: "Should fail as failed to fetch PIs for the account",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "Should succeed even if fetch balance APIs fails",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
				err: nil,
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(3000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(3000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
						Status:           rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status: rpc.StatusInternal(),
					},
					err: nil,
				},
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:           rpc.StatusOk(),
				OpeningBalance:   money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(3000), money.RupeeCurrencyCode),
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(3000), money.RupeeCurrencyCode),
				TotalSpent:       money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:      money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:       money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				BalanceAt:        testBalanceUpdatedAt,
				IsBalanceStale:   true,
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
			},
		},
		{
			name: "Should fallback to last known balance from db in case computed balance is not synchronised and balance API fails",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank:                 commonvgpb.Vendor_FEDERAL_BANK,
					BalanceFromPartnerUpdatedAt: testBalanceUpdatedAt,
				},
				err: nil,
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 2000},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_DISPUTED,
					},
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(3000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(3000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
						Status:           rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status: rpc.StatusInternal(),
					},
					err: nil,
				},
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:           rpc.StatusOk(),
				OpeningBalance:   money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(3000), money.RupeeCurrencyCode),
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(3000), money.RupeeCurrencyCode),
				TotalSpent:       money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:      money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:       money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
				IsBalanceStale:   true,
				BalanceAt:        testBalanceUpdatedAt,
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
			},
		},
		{
			name: "Should fetch opening balance from the cache",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					OpeningBalanceInfo: &savingsPb.OpeningBalanceInfo{
						OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
						UpdatedAt:      timestamppb.New(bomTime),
					},
				},
				err: nil,
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
					Date:       bomDate,
					ActorId:    testActor,
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:           rpc.StatusOk(),
				OpeningBalance:   money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				TotalSpent:       money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:      money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:       money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				BalanceAt: testBalanceUpdatedAt,
			},
		},
		{
			name: "Should update cached opening balance in case updated at is stale",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					OpeningBalanceInfo: &savingsPb.OpeningBalanceInfo{
						OpeningBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						UpdatedAt:      testSavingsAccount.GetCreatedAt(),
					},
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:           rpc.StatusOk(),
				OpeningBalance:   money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				TotalSpent:       money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:      money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:       money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				BalanceAt: testBalanceUpdatedAt,
			},
		},
		{
			name: "handle opening balance unavailable gracefully",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					CreatedAt:   testSavingsAccount.GetCreatedAt(),
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status: rpc.StatusUnavailable(),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:           rpc.StatusOk(),
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
				TotalSpent:       money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:      money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:       money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				BalanceAt: testBalanceUpdatedAt,
			},
		},
		{
			name: "handle opening balance deadline exceeded gracefully",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					CreatedAt:   testSavingsAccount.GetCreatedAt(),
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				err: context.Canceled,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:           rpc.StatusOk(),
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
				TotalSpent:       money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:      money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:       money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				BalanceAt: testBalanceUpdatedAt,
			},
		},
		{
			name: "handle opening balance non-ok error gracefully",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					CreatedAt:   testSavingsAccount.GetCreatedAt(),
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status: rpc.StatusInternal(),
				},
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:           rpc.StatusOk(),
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(2000), money.RupeeCurrencyCode),
				TotalSpent:       money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:      money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:       money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				BalanceAt: testBalanceUpdatedAt,
			},
		},
		{
			name: "Should bypass opening bal call in case account is opened in the same month",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					CreatedAt:   timestamppb.New(bomTime),
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1600},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
					Date:       bomDate,
					ActorId:    testActor,
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:           rpc.StatusOk(),
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				OpeningBalance:   money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalSpent:       money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit:      money.ParseDecimal(decimal.NewFromInt(1600), money.RupeeCurrencyCode),
				TotalDebit:       money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1600), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				BalanceAt: testBalanceUpdatedAt,
			},
		},
		{
			name: "return opening balance from DB if BkodDate isn't recent enough to provide opening balance of queried date",
			mockGetAccountById: mockGetAccountById{
				enable: true,
				id:     testSavingsId,
				res: &savingsPb.Account{
					Id:                   testSavingsId,
					AccountNo:            "acct-1",
					IfscCode:             "ifsc",
					PrimaryAccountHolder: testUserId,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: ***********,
					},
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					OpeningBalanceInfo: &savingsPb.OpeningBalanceInfo{
						OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
						UpdatedAt:      timestamppb.Now(),
					},
				},
				err: nil,
			},
			mockGetBalance: []*mockGetBalance{
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
				{
					req: &accountBalancePb.GetAccountBalanceRequest{
						Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: testSavingsId},
						ActorId:       testActor,
						DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
					},
					res: &accountBalancePb.GetAccountBalanceResponse{
						Status:           rpc.StatusOk(),
						AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
						BalanceAt:        testBalanceUpdatedAt,
					},
					err: nil,
				},
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   testSavingsId,
					AccountType: accountsPb.Type_SAVINGS,
				},
				res: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
								},
							},
						},
						{
							Id:   "pi-2",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									AccountType: accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
				err: nil,
			},
			mockCalculateTransactionAggregate: []*mockCalculateTransactionAggregate{
				{
					req: &txnAggregate.TxnAggregateRequestParams{
						ActorId: testActor,
						ToTime:  nil,
						PiIds:   testPiIds,
					},
					credit: &moneyPb.Money{CurrencyCode: "INR", Units: 1500},
					debit:  &moneyPb.Money{CurrencyCode: "INR", Units: 600},
					err:    nil,
				},
			},
			mockGetOpeningBalance: mockGetOpeningBalance{
				enable: true,
				req: &accountBalancePb.GetOpeningBalanceRequest{
					Date:       bomDate,
					ActorId:    testActor,
					Identifier: &accountBalancePb.GetOpeningBalanceRequest_Id{Id: testSavingsId},
				},
				res: &accountBalancePb.GetOpeningBalanceResponse{
					Status:         rpc.StatusOk(),
					OpeningBalance: money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				},
				err: nil,
			},
			mockGetReconStatus: mockGetReconStatus{
				enable: true,
				req:    &reconPb.GetAccountLedgerReconciliationStatusRequest{SavingsAccountId: testSavingsId},
				res: &reconPb.GetAccountLedgerReconciliationStatusResponse{
					Status: rpc.StatusOk(),
					SavingsLedgerRecon: &reconPb.SavingsLedgerRecon{
						Id:               testSavingsId,
						SavingsAccountId: testSavingsId,
						Status:           reconPb.ReconStatus_SYNCHRONISED,
					},
				},
				err: nil,
			},
			req: &savingsPb.GetAccountBalanceWithSummaryRequest{
				Identifier: &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: testSavingsId},
				ActorId:    testActor,
				TimeRange:  savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			},
			want: &savingsPb.GetAccountBalanceWithSummaryResponse{
				Status:      rpc.StatusOk(),
				TotalSpent:  money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
				TotalCredit: money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
				TotalDebit:  money.ParseDecimal(decimal.NewFromInt(600), money.RupeeCurrencyCode),
				BucketDetails: []*savingsPb.AccountSummaryBucketDetails{
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_CREDIT,
						Value:         money.ParseDecimal(decimal.NewFromInt(1500), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
					{
						Bucket:        savingsPb.AccountSummaryBucketDetails_SPENT,
						Value:         money.ParseDecimal(decimal.NewFromInt(0), money.RupeeCurrencyCode),
						PercentChange: 0,
					},
				},
				OpeningBalance:   money.ParseDecimal(decimal.NewFromInt(100), money.RupeeCurrencyCode),
				BalanceAt:        testBalanceUpdatedAt,
				AvailableBalance: money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
				LedgerBalance:    money.ParseDecimal(decimal.NewFromInt(1000), money.RupeeCurrencyCode),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			savingsSvc, md := newSavingsServiceWithMocks(ctr)

			err := genconf.SetIsTxnAggregateCalculatedViaPinot(false, false, nil)
			if err != nil {
				return
			}

			for _, mock := range tt.mockGetBalance {
				md.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), testPkg.NewProtoArgMatcher(mock.req), gomock.Any()).
					Return(mock.res, mock.err)
			}

			if tt.mockGetAccountById.enable {
				md.dao.EXPECT().GetAccountById(gomock.Any(), tt.mockGetAccountById.id).
					Return(tt.mockGetAccountById.res, tt.mockGetAccountById.err)
			}

			if tt.mockGetOpeningBalance.enable {
				md.accountBalanceClient.EXPECT().GetOpeningBalance(gomock.Any(), testPkg.NewProtoArgMatcher(tt.mockGetOpeningBalance.req)).
					Return(tt.mockGetOpeningBalance.res, tt.mockGetOpeningBalance.err)
			}
			if tt.mockGetPiByAccountId.enable {
				md.accountPiClient.EXPECT().GetPiByAccountId(gomock.Any(), tt.mockGetPiByAccountId.req).
					Return(tt.mockGetPiByAccountId.res, tt.mockGetPiByAccountId.err)
			}
			for _, mock := range tt.mockCalculateTransactionAggregate {
				md.txnAggregate.EXPECT().CalculateTransactionAggregate(gomock.Any(), newTxnAggrArgMatcher(mock.req)).
					Return(mock.credit, mock.debit, mock.err)
			}
			if tt.mockGetReconStatus.enable {
				md.reconClient.EXPECT().GetAccountLedgerReconciliationStatus(gomock.Any(), tt.mockGetReconStatus.req).
					Return(tt.mockGetReconStatus.res, tt.mockGetReconStatus.err)
			}
			if tt.mockReconcileAccount.enable {
				md.reconClient.EXPECT().ReconcileAccountLedger(gomock.Any(), testPkg.NewProtoArgMatcher(tt.mockReconcileAccount.req)).
					Return(tt.mockReconcileAccount.res, tt.mockReconcileAccount.err)
			}
			got, err := savingsSvc.GetAccountBalanceWithSummary(epificontext.CtxWithActorId(context.Background(), testActor), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountBalanceWithSummary() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if (got.BalanceAt != nil && tt.want.BalanceAt == nil) || (got.BalanceAt == nil && tt.want.BalanceAt != nil) {
				t.Errorf("GetAccountBalanceWithSummary() \n got = %v \n want %v", got, tt.want)
				return
			}

			tt.want.BalanceAt = got.BalanceAt
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetAccountBalanceWithSummary() \n got = %v \n want %v", got, tt.want)
				return
			}
		})
	}
}
