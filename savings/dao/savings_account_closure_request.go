package storage

import (
	"context"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/savings/dao/model"
)

var selectFieldMaskMap = map[savingsPb.SavingsAccountClosureRequestFieldMask]string{
	savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_ID:                 "id",
	savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_ACTOR_ID:           "actor_id",
	savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_SAVINGS_ACCOUNT_ID: "savings_account_id",
	savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_STATUS:             "status",
	savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_STATUS_REASON:      "status_reason",
	savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_ENTRY_POINT:        "entry_point",
	savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_USER_FEEDBACK:      "user_feedback",
	savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_CREATED_AT:         "created_at",
	savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_UPDATED_AT:         "updated_at",
	savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_DELETED_AT:         "deleted_at",
}

var updateFieldMaskMap = map[savingsPb.SavingsAccountClosureRequestUpdateFieldMask]string{
	savingsPb.SavingsAccountClosureRequestUpdateFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_UPDATE_FIELD_MASK_STATUS_REASON: "status_reason",
	savingsPb.SavingsAccountClosureRequestUpdateFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_UPDATE_FIELD_MASK_ENTRY_POINT:   "entry_point",
	savingsPb.SavingsAccountClosureRequestUpdateFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_UPDATE_FIELD_MASK_USER_FEEDBACK: "user_feedback",
}

var _ SaClosureRequestDao = &SaClosureRequestImpl{}

type SaClosureRequestImpl struct {
	db          types.EpifiCRDB
	maxPageSize uint32
}

var SaClosureRequestDaoWireSet = wire.NewSet(NewSaClosureRequestImpl, wire.Bind(new(SaClosureRequestDao), new(*SaClosureRequestImpl)))

func NewSaClosureRequestImpl(db types.EpifiCRDB, maxPageSize uint32) *SaClosureRequestImpl {
	return &SaClosureRequestImpl{
		db:          db,
		maxPageSize: maxPageSize,
	}
}

func (s *SaClosureRequestImpl) Create(ctx context.Context, saClosureReq *savingsPb.SavingsAccountClosureRequest) (*savingsPb.SavingsAccountClosureRequest, error) {
	defer metric_util.TrackDuration("savings/dao", "SaClosureRequestImpl", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	saClosureModel := model.NewSavingsAccountClosureRequest(saClosureReq)
	res := db.Create(saClosureModel)
	if res.Error != nil {
		return nil, res.Error
	}

	return saClosureModel.GetProto(), nil
}

func (s *SaClosureRequestImpl) GetById(ctx context.Context, id string, fieldMasks []savingsPb.SavingsAccountClosureRequestFieldMask) (*savingsPb.SavingsAccountClosureRequest, error) {
	defer metric_util.TrackDuration("savings/dao", "SaClosureRequestImpl", "GetById", time.Now())
	if id == "" {
		return nil, epifierrors.ErrInvalidArgument
	}

	if len(fieldMasks) == 0 {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "field mask array cannot be empty")
	}

	var saClosureModel model.SavingsAccountClosureRequest
	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	res := db.Model(&model.SavingsAccountClosureRequest{}).Where("id = ?", id).Select(getSelectFieldMasks(fieldMasks)).First(&saClosureModel)
	if err := res.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}

		return nil, res.Error
	}

	return saClosureModel.GetProto(), nil
}

func (s *SaClosureRequestImpl) GetActiveRequestsForActor(ctx context.Context, actorId string, fieldMasks []savingsPb.SavingsAccountClosureRequestFieldMask) ([]*savingsPb.SavingsAccountClosureRequest, error) {
	defer metric_util.TrackDuration("savings/dao", "SaClosureRequestImpl", "GetActiveRequestsForActor", time.Now())
	if actorId == "" {
		return nil, epifierrors.ErrInvalidArgument
	}

	if len(fieldMasks) == 0 {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "field mask array cannot be empty")
	}

	nonTerminalStatuses := savingsPb.GetSaClosureNonTerminalStatuses()

	var saClosureRequestsModel []*model.SavingsAccountClosureRequest
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	res := db.Model(&model.SavingsAccountClosureRequest{}).
		Where("actor_id = ?", actorId).
		Where("status IN ?", nonTerminalStatuses).
		Select(getSelectFieldMasks(fieldMasks)).
		Order("updated_at DESC").
		Find(&saClosureRequestsModel)
	if err := res.Error; err != nil {
		return nil, res.Error
	}

	if len(saClosureRequestsModel) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	var saClosureRequestsProto []*savingsPb.SavingsAccountClosureRequest
	for _, model := range saClosureRequestsModel {
		saClosureRequestsProto = append(saClosureRequestsProto, model.GetProto())
	}

	return saClosureRequestsProto, nil
}

func (s *SaClosureRequestImpl) Update(ctx context.Context, saClosureReq *savingsPb.SavingsAccountClosureRequest, fieldMasks []savingsPb.SavingsAccountClosureRequestUpdateFieldMask) (*savingsPb.SavingsAccountClosureRequest, error) {
	defer metric_util.TrackDuration("savings/dao", "SaClosureRequestImpl", "Update", time.Now())
	if saClosureReq.GetId() == "" {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "id cannot be empty for update")
	}

	if len(fieldMasks) == 0 {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "field mask array cannot be empty")
	}

	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	saClosureModel := model.NewSavingsAccountClosureRequest(saClosureReq)
	res := db.Model(&model.SavingsAccountClosureRequest{}).
		Where("id = ?", saClosureReq.GetId()).
		Select(getUpdateFieldMasks(fieldMasks)).
		Updates(saClosureModel)
	if err := res.Error; err != nil {
		return nil, err
	}

	if res.RowsAffected == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	return saClosureModel.GetProto(), nil
}

func (s *SaClosureRequestImpl) UpdateStatus(ctx context.Context, closureRequestId string, fromStatus, toStatus savingsPb.SAClosureRequestStatus, statusReason savingsPb.SAClosureRequestStatusReason) error {
	defer metric_util.TrackDuration("savings/dao", "SaClosureRequestImpl", "UpdateStatus", time.Now())
	if closureRequestId == "" {
		return errors.Wrap(epifierrors.ErrInvalidArgument, "closure request id cannot be empty")
	}

	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	saClosureModel := model.NewSavingsAccountClosureRequest(&savingsPb.SavingsAccountClosureRequest{
		Id:           closureRequestId,
		Status:       toStatus,
		StatusReason: statusReason,
	})
	res := db.Model(&model.SavingsAccountClosureRequest{}).
		Where("id = ?", closureRequestId).
		Where("status = ?", fromStatus).
		Select("status", "status_reason").
		Updates(saClosureModel)
	if res.Error != nil {
		return res.Error
	}

	if res.RowsAffected == 0 {
		return epifierrors.ErrRecordNotFound
	}

	return nil
}

func (s *SaClosureRequestImpl) GetPaginatedSaClosureRequests(ctx context.Context, pageToken *pagination.PageToken, pageSize uint32, fieldMasks []savingsPb.SavingsAccountClosureRequestFieldMask, options ...storagev2.FilterOption) ([]*savingsPb.SavingsAccountClosureRequest, *rpc.PageContextResponse,
	error) {
	defer metric_util.TrackDuration("savings/dao", "SaClosureRequestImpl", "GetPaginatedSaClosureRequests", time.Now())
	if len(fieldMasks) == 0 {
		return nil, nil, errors.Wrap(epifierrors.ErrInvalidArgument, "field mask array cannot be empty")
	}

	d := gormctxv2.FromContextOrDefault(ctx, s.db)
	d = d.Model(&model.SavingsAccountClosureRequest{})

	for _, opts := range options {
		d = opts.ApplyInGorm(d)
	}

	d = d.Select(getSelectFieldMasks(fieldMasks))

	// fetch pageSize + 1 extra row to compute next page availability.
	if pageSize > s.maxPageSize || pageSize == 0 {
		pageSize = s.maxPageSize
	}

	d = d.Limit(int(pageSize) + 1)

	if pageToken != nil {
		if pageToken.IsReverse {
			d = d.Where("created_at <= ?", pageToken.Timestamp.AsTime()).Order("created_at" + " DESC")
		} else {
			d = d.Where("created_at >= ?", pageToken.Timestamp.AsTime()).Order("created_at" + " ASC")
		}
		d = d.Offset(int(pageToken.Offset))
	} else {
		d = d.Order("created_at" + " ASC")
	}

	saClosureReqModels := make([]*model.SavingsAccountClosureRequest, 0)
	err := d.Scan(&saClosureReqModels).Error
	if err != nil {
		return nil, nil, err
	}

	if len(saClosureReqModels) == 0 {
		return nil, nil, epifierrors.ErrRecordNotFound
	}

	rows, pageCtxResp, err := pagination.NewPageCtxResp(pageToken, int(pageSize), model.SavingsAccountClosureRequests(saClosureReqModels))
	if err != nil {
		return nil, nil, err
	}

	saClosureReqModels = rows.(model.SavingsAccountClosureRequests)
	saClosureReqProtos := make([]*savingsPb.SavingsAccountClosureRequest, len(saClosureReqModels))
	for i, saClosureReqModel := range saClosureReqModels {
		saClosureReqProtos[i] = saClosureReqModel.GetProto()
	}

	return saClosureReqProtos, pageCtxResp, nil
}

func getSelectFieldMasks(fieldMasks []savingsPb.SavingsAccountClosureRequestFieldMask) []string {
	var selectFieldMasks []string
	for _, fieldMask := range fieldMasks {
		selectFieldMasks = append(selectFieldMasks, selectFieldMaskMap[fieldMask])
	}

	return selectFieldMasks
}

func getUpdateFieldMasks(fieldMasks []savingsPb.SavingsAccountClosureRequestUpdateFieldMask) []string {
	var updateFieldMasks []string
	for _, fieldMask := range fieldMasks {
		updateFieldMasks = append(updateFieldMasks, updateFieldMaskMap[fieldMask])
	}

	return updateFieldMasks
}
