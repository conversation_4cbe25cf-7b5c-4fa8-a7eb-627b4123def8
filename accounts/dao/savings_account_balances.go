package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	gormv2 "gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/accounts/dao/model"
	saPb "github.com/epifi/gamma/api/accounts/balance"
)

type SavingsAccountBalancesDao struct {
	db types.PayPGDB
}

// Factory method for creating an instance of savings account balances dao. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewSavingsAccountBalancesDao(db types.PayPGDB) *SavingsAccountBalancesDao {
	return &SavingsAccountBalancesDao{db: db}
}

var (
	savingsAccountBalancesFieldMaskToDBColumnMap = map[saPb.SavingsAccountBalancesFieldMask]string{
		saPb.SavingsAccountBalancesFieldMask_AVAILABLE_BALANCE_FROM_PARTNER:  "available_balance_from_partner",
		saPb.SavingsAccountBalancesFieldMask_OPENING_BALANCE_FROM_PARTNER:    "opening_balance_from_partner",
		saPb.SavingsAccountBalancesFieldMask_LEDGER_BALANCE_FROM_PARTNER:     "ledger_balance_from_partner",
		saPb.SavingsAccountBalancesFieldMask_BALANCE_FROM_PARTNER_UPDATED_AT: "balance_from_partner_updated_at",
		saPb.SavingsAccountBalancesFieldMask_ACCOUNT_ID:                      "account_id",
		saPb.SavingsAccountBalancesFieldMask_CREATED_AT:                      "created_at",
		saPb.SavingsAccountBalancesFieldMask_UPDATE_AT:                       "updated_at",
	}
)

func (s *SavingsAccountBalancesDao) GetAccountBalance(ctx context.Context, accountId string) (*saPb.SavingsAccountBalances, error) {
	defer metric_util.TrackDuration("accounts/dao", "SavingsAccountBalancesDao", "GetAccountBalance", time.Now())

	if accountId == "" {
		return nil, epifierrors.ErrInvalidArgument
	}

	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	savingsAccountBalanceModel := &model.SavingsAccountBalances{}

	err := db.Where("account_id = ?", accountId).Take(savingsAccountBalanceModel).Error

	if errors.Is(err, gormv2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}

	if err != nil {
		return nil, fmt.Errorf("error in getting data from DB: %v", err)
	}

	return savingsAccountBalanceModel.GetProto(), nil
}

// Upert will first try to create the entry in table
// if theere will be conflict over account_id, it will update the data
// as per given fieldMask
func (s *SavingsAccountBalancesDao) Upsert(ctx context.Context, req *saPb.SavingsAccountBalances, fieldMask []saPb.SavingsAccountBalancesFieldMask) error {
	defer metric_util.TrackDuration("accounts/dao", "SavingsAccountBalancesDao", "Upsert", time.Now())
	updateColumns := getSelectedColumn(fieldMask)

	if req == nil || len(updateColumns) == 0 || lo.Contains(updateColumns, savingsAccountBalancesFieldMaskToDBColumnMap[saPb.SavingsAccountBalancesFieldMask_ACCOUNT_ID]) {
		return epifierrors.ErrInvalidArgument
	}

	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	savingsAccountBalanceModel := model.ConvertToSavingsAccountBalancesModel(req)

	// Generate the assignments for the upsert operation
	assignments := buildAssignmentsForUpsert(savingsAccountBalanceModel, fieldMask)

	db = db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: savingsAccountBalancesFieldMaskToDBColumnMap[saPb.SavingsAccountBalancesFieldMask_ACCOUNT_ID]},
		},
		DoUpdates: assignments,
	}).Create(savingsAccountBalanceModel)

	if db.Error != nil {
		return fmt.Errorf("error in upsert to targeted comms mapping in db: %w", db.Error)
	}

	return nil
}

func buildAssignmentsForUpsert(mapping *model.SavingsAccountBalances, updateMask []saPb.SavingsAccountBalancesFieldMask) clause.Set {
	var assignments []clause.Assignment
	// adding updated_at column explicitly as OnConflict clause does not update the column
	assignments = append(assignments, clause.Assignment{
		Column: clause.Column{Name: "updated_at"},
		Value:  time.Now(),
	})

	for _, column := range updateMask {
		switch column {
		case saPb.SavingsAccountBalancesFieldMask_AVAILABLE_BALANCE_FROM_PARTNER:
			assignments = append(assignments, clause.Assignment{
				Column: clause.Column{Name: savingsAccountBalancesFieldMaskToDBColumnMap[column]},
				Value:  mapping.AvailableBalanceFromPartner,
			})
		case saPb.SavingsAccountBalancesFieldMask_LEDGER_BALANCE_FROM_PARTNER:
			assignments = append(assignments, clause.Assignment{
				Column: clause.Column{Name: savingsAccountBalancesFieldMaskToDBColumnMap[column]},
				Value:  mapping.LedgerBalanceFromPartner,
			})
		case saPb.SavingsAccountBalancesFieldMask_BALANCE_FROM_PARTNER_UPDATED_AT:
			assignments = append(assignments, clause.Assignment{
				Column: clause.Column{Name: savingsAccountBalancesFieldMaskToDBColumnMap[column]},
				Value:  mapping.BalanceFromPartnerUpdatedAt,
			})
		case saPb.SavingsAccountBalancesFieldMask_OPENING_BALANCE_FROM_PARTNER:
			assignments = append(assignments, clause.Assignment{
				Column: clause.Column{Name: savingsAccountBalancesFieldMaskToDBColumnMap[column]},
				Value:  mapping.OpeningBalanceFromPartner,
			})
		default:
			// ignore unsupported fields
		}
	}

	return assignments
}

func getSelectedColumn(fieldMasks []saPb.SavingsAccountBalancesFieldMask) []string {
	var columns []string
	for _, fieldMask := range fieldMasks {
		if fieldMask != saPb.SavingsAccountBalancesFieldMask_SAVINGS_ACCOUNT_BALANCES_UNSPECIFIED {
			columns = append(columns, savingsAccountBalancesFieldMaskToDBColumnMap[fieldMask])
		}
	}

	return columns
}
