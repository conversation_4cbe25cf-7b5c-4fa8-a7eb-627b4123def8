package external

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
)

func (s *Service) DeleteAllMfExternalDetails(ctx context.Context, req *mfExternalPb.DeleteAllMfExternalDetailsRequest) (*mfExternalPb.DeleteAllMfExternalDetailsResponse, error) {
	actorId := req.GetActorId()

	err := s.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// Delete MF external orders
		if err := s.mfExtOrderDao.DeleteByActorId(txnCtx, actorId); err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(txnCtx, "no mf external orders found to delete", zap.String("actor_id", actorId))
			} else {
				logger.Error(txnCtx, "failed to delete mf external orders", zap.String("actor_id", actorId), zap.Error(err))
				return fmt.Errorf("failed to delete mf external orders: %w", err)
			}
		}

		// Delete MF holdings summary
		if err := s.mfHoldingsSummaryDao.DeleteByActorId(txnCtx, actorId); err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(txnCtx, "no mf holdings summary found to delete", zap.String("actor_id", actorId))
			} else {
				logger.Error(txnCtx, "failed to delete mf holdings summary", zap.String("actor_id", actorId), zap.Error(err))
				return fmt.Errorf("failed to delete mf external holdings summary: %w", err)
			}
		}

		// Delete MF holdings import requests tracker
		if err := s.mFHoldingsImportRequestTrackerDao.DeleteByActorId(txnCtx, actorId); err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(txnCtx, "no mf holdings import requests found to delete", zap.String("actor_id", actorId))
			} else {
				logger.Error(txnCtx, "failed to delete mf holdings import requests", zap.String("actor_id", actorId), zap.Error(err))
				return fmt.Errorf("failed to delete mf holdings import requests: %w", err)
			}
		}

		return nil
	})

	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "no mf external details found to delete", zap.String("actor_id", actorId))
			return &mfExternalPb.DeleteAllMfExternalDetailsResponse{
				Status: rpc.StatusOk(),
			}, nil
		}
		logger.Error(ctx, "failed to delete mf external details", zap.String("actor_id", actorId), zap.Error(err))
		return &mfExternalPb.DeleteAllMfExternalDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &mfExternalPb.DeleteAllMfExternalDetailsResponse{
		Status: rpc.StatusOk(),
	}, nil
}
