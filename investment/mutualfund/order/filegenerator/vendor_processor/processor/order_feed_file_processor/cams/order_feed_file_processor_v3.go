package cams

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/vendors/cams/scheme_code_converter"

	"github.com/epifi/gamma/api/investment/auth"
	"github.com/epifi/gamma/api/investment/mutualfund"
	"github.com/epifi/gamma/api/investment/mutualfund/order"
	fgPb "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	"github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	types "github.com/epifi/gamma/api/typesv2"
	wob "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/accessors"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/constants"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/cams/mapper"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/type_converter"
)

func (c *OrderFeedFileProcessor) ConvertToCamsModelV3(ctx context.Context, orderIds []string) ([]*OrderFeedV3, []string, map[string]fgPb.EntitySubStatus, error) {
	var successfulOrderFeedDetails []*OrderFeedV3
	var successfulOrderIDs []string
	var failedOrderIDsMap map[string]fgPb.EntitySubStatus
	var err error

	successfulOrderFeedDetails, successfulOrderIDs, failedOrderIDsMap, err = c.fetchAndPopulateOrderFeedDataV3(ctx, orderIds)
	if err != nil {
		return nil, nil, nil, err
	}

	return successfulOrderFeedDetails, successfulOrderIDs, failedOrderIDsMap, nil

}

//nolint:funlen
func (c *OrderFeedFileProcessor) fetchAndPopulateOrderFeedDataV3(ctx context.Context, orderIds []string) ([]*OrderFeedV3, []string, map[string]fgPb.EntitySubStatus, error) {

	var successfulOrderFeedDetails []*OrderFeedV3
	var successfulOrderIDs []string
	failedOrderIDsMap := make(map[string]fgPb.EntitySubStatus)

	orderIdToDetailsMap, err := c.orderManagerAccessor.GetOrders(ctx, orderIds)
	if err != nil {
		return nil, nil, nil, err
	}

	actorIdToPreInvestmentDetailMap, err := c.fetchPreInvestmentDetail(ctx, orderIdToDetailsMap)
	if err != nil {
		return nil, nil, nil, err
	}

	mutualFundIdToMutualFundDetailsMap, err := c.fetchMutualFundDetails(ctx, orderIdToDetailsMap)
	if err != nil {
		return nil, nil, nil, err
	}

	sipRegIdToSipDetailsMap, err := c.fetchSipLedgerDetails(ctx, orderIdToDetailsMap)
	if err != nil {
		return nil, nil, nil, err
	}

	orderIdToAuthDetailsMap, err := c.fetchAuthDetails(ctx, orderIdToDetailsMap, sipRegIdToSipDetailsMap)
	if err != nil {
		return nil, nil, nil, err
	}

	var getPaymentDetailsRequests []*accessors.GetPaymentDetailsRequest
	for orderId, orderDetail := range orderIdToDetailsMap {
		payMode := payment_handler.PaymentMode(orderDetail.Order.PaymentMode)
		getPaymentDetailsRequests = append(getPaymentDetailsRequests, &accessors.GetPaymentDetailsRequest{OrderId: orderId,
			PaymentMode: payMode})
	}

	var orderIdToPaymentFailureMap map[string]fgPb.EntitySubStatus
	var paymentDetails *payment_handler.GetBatchPaymentDetailsResponse
	orderIdToPaymentFailureMap, paymentDetails, err = c.paymentAccessor.GetPaymentDetailsWithFailureReason(ctx, getPaymentDetailsRequests)
	if err != nil {
		return nil, nil, nil, err
	}

	timeZoneLocation, err := time.LoadLocation(constants.ISTTimeZoneLocation)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("error while trying to load IST time zone: %w", err)
	}

	for _, orderId := range orderIds {

		if orderIdToDetailsMap[orderId] == nil {
			failedOrderIDsMap[orderId] = fgPb.EntitySubStatus_ENTITY_SUB_STATUS_FAILED_MISSING_ORDER_MANAGER_DATA
			continue
		}

		orderDetails := orderIdToDetailsMap[orderId].Order
		if !c.validateOnboardingFields(ctx, orderDetails.OrderType, actorIdToPreInvestmentDetailMap[orderDetails.ActorId], orderDetails.ActorId) {
			failedOrderIDsMap[orderId] = fgPb.EntitySubStatus_ENTITY_SUB_STATUS_FAILED_MISSING_ON_BOARDING_DATA
			continue
		}
		if mutualFundIdToMutualFundDetailsMap[orderDetails.MutualFundId] == nil {
			failedOrderIDsMap[orderId] = fgPb.EntitySubStatus_ENTITY_SUB_STATUS_FAILED_MISSING_CATALOGUE_DATA
			continue
		}

		if orderDetails.GetOrderType() == order.OrderType_BUY && !c.paymentAccessor.IsPaymentSuccessful(orderId, paymentDetails) {
			failedOrderIDsMap[orderId] = fgPb.EntitySubStatus_ENTITY_SUB_STATUS_PAYMENT_FAILURE
			continue
		}
		if reason, ok := orderIdToPaymentFailureMap[orderId]; ok {
			failedOrderIDsMap[orderId] = reason
			continue
		}

		orderFeedDetail := NewOrderFeedFileV3Model()
		orderFeedDetail, err = c.fillOrderRelatedFieldsV3(ctx, orderFeedDetail, orderDetails, timeZoneLocation, orderIdToAuthDetailsMap[orderId], mutualFundIdToMutualFundDetailsMap[orderDetails.MutualFundId])
		if err != nil {
			failedOrderIDsMap[orderId] = fgPb.EntitySubStatus_ENTITY_SUB_STATUS_FAILED_MISSING_ORDER_MANAGER_DATA
			continue
		}
		orderFeedDetail, err = c.fillActorRelatedFieldsV3(ctx, orderFeedDetail, actorIdToPreInvestmentDetailMap[orderDetails.ActorId],
			orderDetails.Amc, timeZoneLocation, orderDetails.ActorId)
		if err != nil {
			failedOrderIDsMap[orderId] = fgPb.EntitySubStatus_ENTITY_SUB_STATUS_FAILED_MISSING_ON_BOARDING_DATA
			continue
		}
		orderFeedDetail.SchemaCode = scheme_code_converter.GetSchemeCode(orderDetails.GetAmc(), mutualFundIdToMutualFundDetailsMap[orderDetails.MutualFundId].SchemeCode)

		orderFeedDetail, err = c.fillSipRelatedDataV3(ctx, orderFeedDetail, orderDetails, sipRegIdToSipDetailsMap[orderDetails.GetSipRegistrationNumber()], timeZoneLocation)
		if err != nil {
			failedOrderIDsMap[orderId] = fgPb.EntitySubStatus_ENTITY_SUB_STATUS_FAILED_MISSING_SIP_LEDGER_DATA
			continue
		}

		orderFeedDetail = c.fillAuthRelatedFieldsForBuyOrderV3(orderFeedDetail, orderDetails)

		successfulOrderIDs = append(successfulOrderIDs, orderId)
		successfulOrderFeedDetails = append(successfulOrderFeedDetails, orderFeedDetail)
	}

	return successfulOrderFeedDetails, successfulOrderIDs, failedOrderIDsMap, nil
}

// nolint: dupl
func (c *OrderFeedFileProcessor) fillSipRelatedDataV3(ctx context.Context, orderFeedDetailV3 *OrderFeedV3, orderData *order.Order, sipLedger *mutualfund.SIPLedger, timeZoneLocation *time.Location) (*OrderFeedV3, error) {
	if orderData.OrderSubType != order.OrderSubType_BUY_SIP {
		return orderFeedDetailV3, nil
	}
	if sipLedger == nil {
		logger.Error(ctx, "sip ledger info not present for sip order", zap.String(logger.ORDER_ID, orderData.Id),
			zap.String("sip_reg_number", orderData.SipRegistrationNumber))
		return nil, fmt.Errorf("sip ledger info not found")
	}
	orderFeedDetailV3.SubTransactionType = "S"
	orderFeedDetailV3.SIPRegisteredDate = sipLedger.GetStartDate().AsTime().In(timeZoneLocation).Format(MMDDYYYYDateFormatWithSlashDelimiter)
	orderFeedDetailV3.SIPRegistrationNumber = sipLedger.GetSipRegistrationNumber()
	orderFeedDetailV3.SIPNumberOfInstallments = fmt.Sprintf("%d", sipLedger.GetTotalInstallments())
	sipFreq, err := c.getSipFrequency(ctx, sipLedger.GetSipGranularity())
	if err != nil {
		return nil, err
	}
	orderFeedDetailV3.SIPFrequency = sipFreq
	orderFeedDetailV3.SIPStartDate = sipLedger.GetStartDate().AsTime().In(timeZoneLocation).Format(MMDDYYYYDateFormatWithSlashDelimiter)
	orderFeedDetailV3.SIPEndDate = sipLedger.GetEndDate().AsTime().In(timeZoneLocation).Format(MMDDYYYYDateFormatWithSlashDelimiter)
	orderFeedDetailV3.SIPInstallmentNumber = fmt.Sprintf("%d", orderData.GetSipInstallmentNumber())
	orderFeedDetailV3.SIPNewFlag = c.getSipNewFlag(orderData)
	orderFeedDetailV3.SIPAmount = moneyPb.ToDecimal(orderData.GetAmount()).String()
	return orderFeedDetailV3, nil
}

func (c *OrderFeedFileProcessor) fillOrderRelatedFieldsV3(ctx context.Context, orderFeedDetailV3 *OrderFeedV3,
	orderData *order.Order, timeZoneLocation *time.Location, auth *auth.AuthAttempt, fundDetails *mutualfund.MutualFund) (*OrderFeedV3, error) {
	var err error
	orderFeedDetailV3.UserCode = c.conf.Cams().UserCode()

	orderFeedDetailV3.BrokerCode = c.conf.Cams().BrokerCode()

	orderFeedDetailV3.AMCCode = mapper.AmcMapper[orderData.Amc]

	orderFeedDetailV3.UserTransactionNumber = orderData.VendorOrderId

	if len(orderData.FolioId) > 0 {
		// If folio number has a check digit part, then add it, otherwise CheckDigitNumber should be empty.
		folioArray := strings.Split(orderData.FolioId, "/")
		orderFeedDetailV3.FolioNumber = folioArray[0]
		if len(folioArray) > 1 {
			orderFeedDetailV3.CheckDigitNumber = folioArray[1]
		}
	}

	orderFeedDetailV3.TransactionType = mapper.OrderTypeMapper[orderData.OrderType]

	orderFeedDetailV3.TransactionDate = orderData.CreatedAt.AsTime().In(timeZoneLocation).Format(MMDDYYYYDateFormatWithSlashDelimiter)
	orderFeedDetailV3.TransactionTime = orderData.CreatedAt.AsTime().In(timeZoneLocation).Format(HHMMSSTimeFormatWithColonDelimiter)

	// For sell orders, we will always place orders by units and not by amount. Amount field should be empty for sell orders
	if orderData.OrderType == order.OrderType_SELL {
		orderFeedDetailV3, err = c.fillSellOrderRelatedFieldsV3(ctx, orderFeedDetailV3, orderData, auth)
		if err != nil {
			return nil, err
		}
	} else {
		// For buy orders, we will always place orders by amount and not by units. Units field should be empty for buy orders
		orderFeedDetailV3, err = c.fillBuyOrderRelatedFieldsV3(ctx, orderFeedDetailV3, orderData)
		if err != nil {
			return nil, err
		}
	}

	if fundDetails.OptionType == mutualfund.OptionType_GROWTH {
		orderFeedDetailV3.ReinvestOption = "Z"
	} else {
		orderFeedDetailV3.ReinvestOption = mapper.ReInvestOptionMapper[orderData.ReinvType]
	}

	return orderFeedDetailV3, nil
}

// nolint: dupl
func (c *OrderFeedFileProcessor) fillSellOrderRelatedFieldsV3(ctx context.Context, orderFeedDetailV3 *OrderFeedV3,
	orderData *order.Order, authDetails *auth.AuthAttempt) (*OrderFeedV3, error) {

	if orderData.GetOrderSubType() == order.OrderSubType_SELL_FULL_REDEMPTION_WITH_FOLIO_CLOSURE {
		orderFeedDetailV3.CloseAccountCH = "Y"
	} else {
		if orderData.Units <= 0 {
			logger.Error(ctx, fmt.Sprintf("orderUnits: %v cannot be less than or equal to zero for sell orders", orderData.Units),
				zap.String(logger.ORDER_ID, orderData.Id))
			return nil, fmt.Errorf("orderUnits: %v cannot be less than or equal to zero for sell orders", orderData.Units)
		}
		orderFeedDetailV3.TransactionUnits = fmt.Sprintf(UNITS_PRECISION, orderData.Units)
	}

	if authDetails == nil || authDetails.AuthStatus != auth.AuthStatus_AUTH_SUCCESS {
		logger.Error(ctx, fmt.Sprintf("authDetails: %s in not in success state", authDetails),
			zap.String(logger.ORDER_ID, orderData.Id))
		return nil, fmt.Errorf("authDetails: %s in not in success state", authDetails)
	}

	if _, ok := mapper.AuthMapper[authDetails.AuthMode]; !ok {
		logger.Error(ctx, fmt.Sprintf("authMode: %s in not supported", authDetails.AuthMode),
			zap.String(logger.ORDER_ID, orderData.Id))
		return nil, fmt.Errorf("authMode: %s in not supported", authDetails.AuthMode)
	}

	orderFeedDetailV3.OTPFlag = mapper.AuthMapper[authDetails.AuthMode]

	return orderFeedDetailV3, nil
}

//nolint:dupl
func (c *OrderFeedFileProcessor) fillBuyOrderRelatedFieldsV3(ctx context.Context, orderFeedDetailV3 *OrderFeedV3, orderData *order.Order) (*OrderFeedV3, error) {
	if moneyPb.IsPositive(orderData.Amount) {
		amount := float64(orderData.Amount.Units) + (float64(orderData.Amount.Nanos) / constants.TEN_TO_THE_POWER_NINE)
		orderFeedDetailV3.TransactionAmount = fmt.Sprintf(UNITS_PRECISION, amount)
	} else {
		logger.Error(ctx, fmt.Sprintf("orderAmount: %s cannot be zero for buy orders", orderData.Amount),
			zap.String(logger.ORDER_ID, orderData.Id))
		return nil, fmt.Errorf("orderAmount: %s cannot be zero for buy orders", orderData.Amount)
	}
	return orderFeedDetailV3, nil
}

// nolint:funlen,dupl
func (c *OrderFeedFileProcessor) fillActorRelatedFieldsV3(ctx context.Context, orderFeedV3 *OrderFeedV3, preInvestmentDetail *wob.PreInvestmentDetail,
	amc mutualfund.Amc, timeZoneLocation *time.Location, actorId string) (*OrderFeedV3, error) {

	orderFeedV3.InvestorName = type_converter.GenerateFullName(preInvestmentDetail.CustomerName)

	var err error
	orderFeedV3.InvestorAddressLine1, orderFeedV3.InvestorAddressLine2, orderFeedV3.InvestorAddressLine3, err = type_converter.GenerateAddress(preInvestmentDetail.Address)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("address is invalid for orderId: %s, cause: %v", orderFeedV3.UserTransactionNumber, err))

		return nil, err
	}

	orderFeedV3.InvestorAddressCity = pruneStringToDesiredLength(preInvestmentDetail.Address.Locality, 34)
	orderFeedV3.InvestorAddressPinCode = preInvestmentDetail.Address.PostalCode

	orderFeedV3.InvestorAddressState = mapper.GetStateCodeForAMC(amc, preInvestmentDetail.Address.AdministrativeArea)
	if len(orderFeedV3.InvestorAddressState) == 0 {
		logger.Error(ctx, fmt.Sprintf("state %s is invalid for orderId: %s", preInvestmentDetail.Address.AdministrativeArea, orderFeedV3.UserTransactionNumber))
		return nil, fmt.Errorf("state %s is invalid", preInvestmentDetail.Address.AdministrativeArea)
	}
	orderFeedV3.LocationCode = mapper.LocationCodeMapper[amc]

	orderFeedV3.InvestorDateOfBirth = type_converter.ConvertDateTODDMMYYYFormatWithSlashDelimiter(preInvestmentDetail.Dob)

	orderFeedV3.TaxNumber = preInvestmentDetail.Pan.Id
	orderFeedV3.InvestorEmail = preInvestmentDetail.EmailId
	if preInvestmentDetail.MobileNo != nil && preInvestmentDetail.MobileNo.NationalNumber != 0 {
		orderFeedV3.MobileNumber = preInvestmentDetail.MobileNo.ToString()
	} else {
		logger.Error(ctx, "mobile number is empty",
			zap.String(logger.MF_AMC, amc.String()), zap.String(logger.ORDER_ID, orderFeedV3.UserTransactionNumber))
	}

	orderFeedV3.InvestorAccountNumber = preInvestmentDetail.BankDetails.AccountNumber
	orderFeedV3.IFSCCode = preInvestmentDetail.BankDetails.IfscCode

	orderFeedV3.InvestorAccountNumberV2 = preInvestmentDetail.BankDetails.AccountNumber
	orderFeedV3.InvestorAccountIFSCCodeV2 = preInvestmentDetail.BankDetails.IfscCode

	if !c.checkAllBankDetailsPresent(preInvestmentDetail.BankDetails) {
		if c.conf.Cams().FallbackDefaultBankDetails() {
			orderFeedV3.InvestorAccountType = constants.SAVINGS_ACCOUNT_TYPE
			orderFeedV3.InvestorBankName = constants.FEDERAL_BANK_NAME
			orderFeedV3.InvestorBankBranch = constants.FEDERAL_BANK_BRANCH
			orderFeedV3.InvestorBankCity = constants.FEDERAL_BANK_CITY

			orderFeedV3.InvestorAccountTypeV2 = constants.SAVINGS_ACCOUNT_TYPE
			orderFeedV3.InvestorAccountBankNameV2 = constants.FEDERAL_BANK_NAME
		} else {
			return nil, fmt.Errorf("general bank details are missing")
		}
	} else {
		orderFeedV3.InvestorAccountType = preInvestmentDetail.BankDetails.AccountType
		orderFeedV3.InvestorBankName = preInvestmentDetail.BankDetails.BankName
		orderFeedV3.InvestorBankBranch = preInvestmentDetail.BankDetails.BranchName
		orderFeedV3.InvestorBankCity = preInvestmentDetail.BankDetails.BankCity

		orderFeedV3.InvestorAccountTypeV2 = preInvestmentDetail.BankDetails.AccountType
		orderFeedV3.InvestorAccountBankNameV2 = preInvestmentDetail.BankDetails.BankName
	}

	orderFeedV3.OccupationCode = mapper.OccupationCodeMapper[preInvestmentDetail.EmploymentData.EmploymentType]

	declaredNomineeErr := c.fillNomineeDetailsV3(ctx, orderFeedV3, preInvestmentDetail, actorId)
	if declaredNomineeErr != nil {
		logger.Error(ctx, "error filling nominee details", zap.Error(declaredNomineeErr), zap.String(logger.VENDOR_ORDER_ID, orderFeedV3.UserTransactionNumber), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, fmt.Errorf("error filling nominee details")
	}

	orderFeedV3.InvestorGender = mapper.GenderMapper[preInvestmentDetail.Gender]

	userCode := c.conf.Cams().UserCode()
	currentTime := time.Now().In(timeZoneLocation)
	orderFeedV3.LOG_WT = fmt.Sprintf("%s$%s$%s$%s", preInvestmentDetail.CustomerIpAddress, userCode,
		currentTime.Format(MMDDYYYYDateFormatWithNoDelimiter), currentTime.Format(HHMMSSTimeFormatWithHyphenDelimiter))

	if preInvestmentDetail.CustomerConsent {
		orderFeedV3.InvestorConsent = "Y"
	} else {
		orderFeedV3.InvestorConsent = "N"
	}

	return orderFeedV3, nil
}

func (c *OrderFeedFileProcessor) fillNomineeDetailsV3(ctx context.Context,
	orderFeed *OrderFeedV3, preInvestmentDetail *wob.PreInvestmentDetail, actorId string) error {
	switch preInvestmentDetail.GetNomineeDeclarationDetails().GetChoice() {
	default:
		// for existing folios (before Oct 1 2022) nominee choice need not be explicitly declared
		if orderFeed.FolioNumber == "" {
			return fmt.Errorf("nominee not declared explicitly for new folio")
		} else {
			orderFeed.NominationNotOpted = "Y"
			return nil
		}
	case commontypes.BooleanEnum_FALSE:
		orderFeed.NominationNotOpted = "Y"
		return nil
	case commontypes.BooleanEnum_TRUE:
		if len(preInvestmentDetail.GetNomineeDeclarationDetails().GetWealthAccountNominees()) == 0 {
			return fmt.Errorf("error in expected data from wealth onboarding")
		}
		orderFeed.NominationNotOpted = "N"
		orderFeed.NomineeCount = "1"
		nomineeId := preInvestmentDetail.GetNomineeDeclarationDetails().GetWealthAccountNominees()[0].GetNomineeId()
		nomineeDetails, nomineeErr := c.nomineeAccessor.GetNomineeDetails(ctx, actorId, nomineeId)
		if nomineeErr != nil {
			return fmt.Errorf("error getting nominee details")
		}
		if len(preInvestmentDetail.GetNomineeDeclarationDetails().GetWealthAccountNominees()) > 0 {
			orderFeed.NomineeCount = "1"
			orderFeed.NomineeSOA = "N" // List all nominees in SOA
			orderFeed.Nominee1Name = type_converter.RemoveSpecialCharacters(nomineeDetails.GetName())
			orderFeed.Nominee1DOB = nomineeDetails.GetDob().AsTime().Format(MMDDYYYYDateFormatWithSlashDelimiter)
			orderFeed.Nominee1Percentage = "100" // hardcoded to 100 until multiple nominee feature is supported
			if nomineeDetails.GetNomineeDocument().GetDocumentType() != types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_UNSPECIFIED &&
				nomineeDetails.GetNomineeDocument().GetDocumentNumber() != "" {
				orderFeed.Nominee1IdType = strconv.Itoa(int(nomineeDetails.GetNomineeDocument().GetDocumentType().Number()))
				orderFeed.Nominee1IdNumber = nomineeDetails.GetNomineeDocument().GetDocumentNumber()
			}

			// Fill email from nomineeDetails, if email is absent use investor's email
			if nomineeDetails.GetContactInfo().GetEmailId() != "" {
				orderFeed.Nominee1Email = nomineeDetails.GetContactInfo().GetEmailId()
			} else {
				orderFeed.Nominee1Email = preInvestmentDetail.GetEmailId()
			}

			// Fill mobile number from nomineeDetails, if mobile is absent use investor's mobile number
			if nomineeDetails.GetContactInfo().GetPhoneNumber().ToString() != "" && nomineeDetails.GetContactInfo().GetPhoneNumber().ToString() != "0" &&
				nomineeDetails.GetContactInfo().GetPhoneNumber().GetNationalNumber() != 0 {
				orderFeed.Nominee1Mobile = nomineeDetails.GetContactInfo().GetPhoneNumber().ToString()
			} else {
				orderFeed.Nominee1Mobile = preInvestmentDetail.GetMobileNo().ToString()
			}

			// Fill address from nomineeDetails, if not present use investor's address
			address1, address2, address3, err := type_converter.GenerateAddress(nomineeDetails.GetContactInfo().GetAddress())
			if err != nil {
				addressLines := c.TruncateAddress([]string{orderFeed.InvestorAddressLine1, orderFeed.InvestorAddressLine2, orderFeed.InvestorAddressLine3})
				orderFeed.Nominee1Address1, orderFeed.Nominee1Address2, orderFeed.Nominee1Address3 = addressLines[0], addressLines[1], addressLines[2]
				orderFeed.Nominee1City, orderFeed.Nominee1Pin, orderFeed.Nominee1Country = orderFeed.InvestorAddressCity, orderFeed.InvestorAddressPinCode, "India"
			} else {
				addressLines := c.TruncateAddress([]string{address1, address2, address3})
				orderFeed.Nominee1Address1, orderFeed.Nominee1Address2, orderFeed.Nominee1Address3 = addressLines[0], addressLines[1], addressLines[2]
				orderFeed.Nominee1City = pruneStringToDesiredLength(nomineeDetails.GetContactInfo().GetAddress().GetLocality(), 34)
				orderFeed.Nominee1Pin = nomineeDetails.GetContactInfo().GetAddress().GetPostalCode()
				orderFeed.Nominee1Country = "India"
			}

			if nomineeDetails.GetRelationship() != types.RelationType_RELATION_TYPE_UNSPECIFIED {
				if c.conf.EnableCamsNomineeRelationshipToNumericCode() {
					orderFeed.Nominee1Relation = type_converter.GetNomineeRelationCode(ctx, nomineeDetails.GetRelationship(), actorId)
				} else {
					orderFeed.Nominee1Relation = nomineeDetails.GetRelationship().String()
				}
			}
			if datetime.Age(nomineeDetails.GetDob().AsTime()) < 18 {
				// This is not the perfect check, ideally we should throw errors if we don't have a minor nominee's guardian details
				if nomineeDetails.GetGuardianInfo() != nil {
					orderFeed.Nominee1MinorFlag = "Y"
					orderFeed.Nominee1GuardianName = type_converter.RemoveSpecialCharacters(nomineeDetails.GetGuardianInfo().GetName())
					// PAN is not required for minors
					if orderFeed.Nominee1IdNumber == "" {
						orderFeed.Nominee1IdType = ""
						orderFeed.Nominee1IdNumber = ""
					}
				} else {
					orderFeed.Nominee1MinorFlag = "N"
				}
			} else {
				orderFeed.Nominee1MinorFlag = "N"
			}
		}
		return nil
	}
}

func (c *OrderFeedFileProcessor) fillAuthRelatedFieldsForBuyOrderV3(orderFeedDetailV3 *OrderFeedV3, orderData *order.Order) *OrderFeedV3 {
	if orderData.OrderType != order.OrderType_BUY {
		return orderFeedDetailV3
	}

	if orderData.OrderSubType == order.OrderSubType_BUY_ONE_TIME_INVEST || orderData.OrderSubType == order.OrderSubType_BUY_SIP {
		orderFeedDetailV3.OTPFlag = mapper.AuthMapper[auth.AuthMode_OTP_SMS]
	}

	return orderFeedDetailV3
}

func (c *OrderFeedFileProcessor) CreateFileContentV3(orders []*OrderFeedV3) string {
	var sb strings.Builder

	for _, orderDetails := range orders {
		value := reflect.ValueOf(orderDetails).Elem()
		for i := 0; i < value.NumField(); i++ {
			sb.WriteString(value.Field(i).String())
			if i < value.NumField()-1 {
				sb.WriteString(constants.PipeDelimiter)
			}
		}
		sb.WriteString(constants.UnixNewLine)
	}
	return sb.String()
}

// TruncateAddress truncates address line to 40 characters and transfers buffer to next line
func (c *OrderFeedFileProcessor) TruncateAddress(addressLines []string) []string {
	var buffer string
	for i, addressLine := range addressLines {
		if len(addressLine) > 40 {
			curLine := buffer + addressLine
			addressLines[i] = curLine[:40]
			buffer += addressLine[40:]
		}
	}
	return addressLines
}
