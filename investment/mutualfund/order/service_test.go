// nolint
package order

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"google.golang.org/genproto/googleapis/type/dayofweek"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/golang/protobuf/ptypes/timestamp"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/jinzhu/copier"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	authMock "github.com/epifi/gamma/api/investment/auth/mocks"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	mockPh "github.com/epifi/gamma/api/investment/mutualfund/payment_handler/mocks"
	"github.com/epifi/gamma/api/pan/mocks"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	userPb "github.com/epifi/gamma/api/user"
	userClientMock "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/investment/mutualfund/order/order_type_processor"
	updOrder "github.com/epifi/gamma/investment/mutualfund/order/update_order"
	"github.com/epifi/gamma/investment/mutualfund/order/validator"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	queueMock "github.com/epifi/be-common/pkg/queue/mocks"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/employment"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	pb "github.com/epifi/gamma/api/investment/mutualfund/order"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	wealthOBPb "github.com/epifi/gamma/api/wealthonboarding"
	wealthOBMocks "github.com/epifi/gamma/api/wealthonboarding/mocks"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	daoMocks "github.com/epifi/gamma/investment/mutualfund/dao/mocks"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/vendor_processor/type_converter"
	ltp "github.com/epifi/gamma/investment/mutualfund/order/lockin_type_processor"
	wec "github.com/epifi/gamma/investment/mutualfund/order/lockin_type_processor/withdrawable_entity_calculator"
)

// service test suite
type svcTestSuite struct {
	conf           *genConf.Config
	orderMgrServer pb.OrderManagerServer
}

func newSvcTestSuite(conf *genConf.Config, orderMgrServer pb.OrderManagerServer) *svcTestSuite {
	return &svcTestSuite{
		conf:           conf,
		orderMgrServer: orderMgrServer,
	}
}

// nolint
var (
	svcTS       *svcTestSuite
	txnExecutor storagev2.IdempotentTxnExecutor
	ctx         = context.Background()

	sampleOrderId              = "MFO12345567889"
	sampleVendorOrderId        = "vendor-id"
	sampleRtaTransactionNumber = "123"
	sampleOrder                = &pb.Order{
		ActorId:      "ac1",
		MutualFundId: "mf1",
		ReinvType:    mfPb.DividendReinvestmentOptionType_REINVESTMENT_ONY,
		Units:        10,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        100,
			Nanos:        45,
		},
		OrderType:     pb.OrderType_BUY,
		FolioId:       "",
		Client:        pb.OrderClient_FIT,
		OrderSubType:  pb.OrderSubType_BUY_AUTO_INVEST,
		ClientOrderId: "cid",
		Rta:           commonvgpb.Vendor_CAMS,
		PaymentInfo:   &pb.PaymentInfo{RecurringPaymentId: "ABCD1234"},
	}

	sampleFund = &mfPb.MutualFund{
		Id:                 "mf1",
		Amc:                mfPb.Amc_ICICI_PRUDENTIAL,
		NameData:           nil,
		PlanType:           mfPb.PlanType_DIRECT,
		InvestmentType:     mfPb.InvestmentType_OPEN,
		OptionType:         mfPb.OptionType_GROWTH,
		DivReinvOptionType: mfPb.DividendReinvestmentOptionType_REINVESTMENT_ONY,
		Nav:                &moneyPb.Money{CurrencyCode: "INR", Units: 10},
		TxnConstraints:     &mfPb.TransactionConstraints{},
		AssetClass:         mfPb.AssetClass_DEBT,
		SipAllowed:         false,
		SwpAllowed:         false,
		StpAllowed:         false,
		IsinNumber:         "12345",
	}

	sampleFundAmcNotTakingAnyInvestment = &mfPb.MutualFund{
		Id:                   "mf1",
		Amc:                  mfPb.Amc_ICICI_PRUDENTIAL,
		NameData:             nil,
		PlanType:             mfPb.PlanType_DIRECT,
		InvestmentType:       mfPb.InvestmentType_OPEN,
		OptionType:           mfPb.OptionType_GROWTH,
		DivReinvOptionType:   mfPb.DividendReinvestmentOptionType_REINVESTMENT_ONY,
		Nav:                  &moneyPb.Money{CurrencyCode: "INR", Units: 10},
		TxnConstraints:       &mfPb.TransactionConstraints{},
		AssetClass:           mfPb.AssetClass_DEBT,
		SipAllowed:           false,
		SwpAllowed:           false,
		StpAllowed:           false,
		IsinNumber:           "12345",
		FundInvestmentStatus: mfPb.FundInvestmentStatus_UNAVAILABLE_FOR_INVESTMENT,
	}

	sampleFundAmcNotTakingNewInvestment = &mfPb.MutualFund{
		Id:                   "mf1",
		Amc:                  mfPb.Amc_ICICI_PRUDENTIAL,
		NameData:             nil,
		PlanType:             mfPb.PlanType_DIRECT,
		InvestmentType:       mfPb.InvestmentType_OPEN,
		OptionType:           mfPb.OptionType_GROWTH,
		DivReinvOptionType:   mfPb.DividendReinvestmentOptionType_REINVESTMENT_ONY,
		Nav:                  &moneyPb.Money{CurrencyCode: "INR", Units: 10},
		TxnConstraints:       &mfPb.TransactionConstraints{},
		AssetClass:           mfPb.AssetClass_DEBT,
		SipAllowed:           false,
		SwpAllowed:           false,
		StpAllowed:           false,
		IsinNumber:           "12345",
		FundInvestmentStatus: mfPb.FundInvestmentStatus_AVAILABLE_ONLY_FOR_EXISTING_INVESTORS,
	}

	sampleFolio = &mfPb.FolioLedger{
		Id:              "f1",
		FolioId:         "123456",
		ActorId:         "ac1",
		MutualFundId:    "mf1",
		InvestmentGoal:  "NO_GOAL",
		BalanceUnits:    100.0,
		RedeemableUnits: 100.0,
		InvestedValue:   &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
		AvgPurchaseNav:  &moneyPb.Money{CurrencyCode: "INR", Units: 1},
	}

	sampleAmc = &mfPb.AmcInfo{
		Id:      "1234",
		Amc:     1,
		AmcCode: "21",
		AmcName: "Aditya Birla",
		CreditAccount: &mfPb.BankAccountDetails{
			AccountName:         "Aditya Birla Amc",
			AccountNumber:       "",
			MaskedAccountNumber: "",
			AccountType:         0,
			Ifsc:                "",
		},
		Rta:               27,
		CreatedAt:         nil,
		UpdatedAt:         nil,
		DeletedAt:         nil,
		CreditAccountPiId: "",
		AmcActorId:        "12345",
	}
	sampleSipLedger = &mfPb.SIPLedger{
		ActorId:                "ac1",
		MutualFundId:           "mf1",
		SipRegistrationNumber:  "sipReg1",
		FitSubscriptionId:      "fitSub1",
		SipAmount:              &moneyPb.Money{CurrencyCode: "INR", Units: 500},
		SipGranularity:         mfPb.SIPGranularity_SIP_GRANULARITY_MONTHLY,
		SipStatus:              mfPb.SIPStatus_SIP_STATUS_ACTIVE,
		StartDate:              &timestamp.Timestamp{Seconds: **********}, // 1 Aug 2022
		EndDate:                &timestamp.Timestamp{Seconds: **********}, // 1 Aug 2032
		TotalInstallments:      1000,
		SuccessfulInstallments: 0,
	}
)

func TestService_CreateBuyOrder(t *testing.T) {

	type dep struct {
		mockOrderDao                          *daoMocks.MockOrderDao
		mockAMCDao                            *daoMocks.MockAmcInfoDao
		mockFolioDao                          *daoMocks.MockFolioLedgerDao
		mockSipLedgerDao                      *daoMocks.MockSIPLedgerDao
		mockMutualFundDao                     *daoMocks.MockMutualFundDao
		mockFileStateDao                      *daoMocks.MockFileStateDao
		wealthOBClient                        *wealthOBMocks.MockWealthOnboardingClient
		userClient                            *userClientMock.MockUsersClient
		mockPanClient                         *mocks.MockPanClient
		mockOrderDelayedNotificationPublisher *queueMock.MockDelayPublisher
		mockInvestmentAuthClient              *authMock.MockAuthClient
		mockPaymentHandlerClient              *mockPh.MockPaymentHandlerClient
		mockOrderEtaDelayPublisher            *queueMock.MockDelayPublisher
	}

	req := &pb.CreateOrderRequest{
		ActorId:        sampleOrder.ActorId,
		MutualFundId:   sampleOrder.MutualFundId,
		ReinvType:      sampleOrder.ReinvType,
		Units:          sampleOrder.Units,
		Amount:         sampleOrder.Amount,
		OrderType:      sampleOrder.OrderType,
		Client:         sampleOrder.Client,
		ClientOrderId:  sampleOrder.ClientOrderId,
		PaymentMode:    sampleOrder.PaymentMode,
		Rta:            sampleOrder.Rta,
		PayInfo:        &pb.PaymentRequestInfo{RecurringPaymentId: sampleOrder.PaymentInfo.RecurringPaymentId},
		InvestmentGoal: "NO_GOAL",
	}

	sampleGetPaymentDetailsResp := &phPb.GetPaymentDetailsResponse{
		Status:          rpc.StatusOk(),
		PaymentStatus:   phPb.PaymentStatus_PAYMENT_STATUS_SUCCESSFUL,
		UtrRefNumber:    "1234",
		TransactionTime: timestampPb.New(time.Now()),
	}
	sampleWoRes := &wealthOBPb.GetInvestmentDataV2Response{
		Status: rpc.StatusOk(),
		InvestmentDetailInfo: map[string]*wealthOBPb.PreInvestmentDetailsV2{
			sampleOrder.GetActorId(): {
				InvestmentDetails: &wealthOBPb.PreInvestmentDetail{
					CustomerName: &commontypes.Name{
						FirstName: "Batman",
					},
					Dob: &date.Date{
						Year:  2022,
						Month: 8,
						Day:   01,
					},
					AddressType: types.AddressType_PERMANENT,
					Address: &types.PostalAddress{
						Locality:     "gotham",
						AddressLines: []string{"line1"},
					},
					PoliticallyExposedStatus: types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE,
					TaxResidentialStatus:     types.ResidentialStatus_RESIDENT_INDIVIDUAL,
					Gender:                   types.Gender_FEMALE,
					Nationality:              types.Nationality_NATIONALITY_INDIAN,
					Pan: &types.DocumentProof{
						Id: "123",
					},
					EmploymentData: &wealthOBPb.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					BankDetails: &wealthOBPb.BankDetails{
						AccountNumber: "123",
						IfscCode:      "123123",
					},
					NomineeDeclarationDetails: &wealthOBPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_FALSE},
				},
				FailureType:   wealthOBPb.FailureType_FAILURE_TYPE_UNSPECIFIED,
				FailureReason: wealthOBPb.FailureReason_FAILURE_REASON_UNSPECIFIED,
			},
		},
	}

	var order pb.Order
	_ = copier.Copy(&order, &sampleOrder)
	order.OrderStatus = pb.OrderStatus_CREATED
	order.Amc = mfPb.Amc_ICICI_PRUDENTIAL
	order.Client = pb.OrderClient_FIT
	order.FolioId = "123456"

	// successful created order
	orderResp := &pb.Order{}
	_ = copier.Copy(orderResp, sampleOrder)
	orderResp.Id = sampleOrderId

	type args struct {
		ctx context.Context
		req *pb.CreateOrderRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(d *dep)
		want           *pb.CreateOrderResponse
		wantErr        bool
	}{
		{
			name: "create success",
			args: args{
				ctx: ctx,
				req: req,
			},
			setupMockCalls: func(d *dep) {
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Return(sampleFund, nil).AnyTimes()
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFund.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(orderResp, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
				d.mockPaymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(sampleGetPaymentDetailsResp, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.StatusOk(),
				Order:  orderResp,
			},
		},
		{
			name: "duplicate order error",
			args: args{
				ctx: ctx,
				req: req,
			},
			setupMockCalls: func(d *dep) {
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(sampleFund, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFund.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				d.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(nil, epifierrors.ErrDuplicateEntry)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.NewStatus(uint32(pb.CreateOrderResponse_DUPLICATE_ORDER), "error in createOrder for buy", "Order already exists: duplicate entry"),
				Order:  nil,
			},
		},
		{
			name: "missing recurring payment id",
			args: args{
				ctx: ctx,
				req: &pb.CreateOrderRequest{
					ActorId:        sampleOrder.ActorId,
					MutualFundId:   sampleOrder.MutualFundId,
					ReinvType:      sampleOrder.ReinvType,
					Units:          sampleOrder.Units,
					Amount:         sampleOrder.Amount,
					OrderType:      sampleOrder.OrderType,
					OrderSubType:   sampleOrder.OrderSubType,
					Client:         sampleOrder.Client,
					ClientOrderId:  sampleOrder.ClientOrderId,
					PaymentMode:    sampleOrder.PaymentMode,
					Rta:            sampleOrder.Rta,
					InvestmentGoal: "NO_GOAL",
				},
			},
			setupMockCalls: func(d *dep) {
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(sampleFund, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFund.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{
					Code:         13,
					ShortMessage: "error in createOrder for buy",
					DebugMessage: "invalid create order request: recurring payment id is mandatory for orders from FIT",
				},
				Order: nil,
			},
		},
		{
			name: "actor ineligible case",
			args: args{
				ctx: ctx,
				req: &pb.CreateOrderRequest{
					ActorId:        sampleOrder.ActorId,
					MutualFundId:   sampleOrder.MutualFundId,
					ReinvType:      sampleOrder.ReinvType,
					Units:          sampleOrder.Units,
					Amount:         sampleOrder.Amount,
					OrderType:      sampleOrder.OrderType,
					Client:         sampleOrder.Client,
					ClientOrderId:  sampleOrder.ClientOrderId,
					PaymentMode:    sampleOrder.PaymentMode,
					Rta:            sampleOrder.Rta,
					InvestmentGoal: "NO_GOAL",
				},
			},
			setupMockCalls: func(d *dep) {
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Return(&wealthOBPb.GetInvestmentDataV2Response{
					Status: rpc.StatusOk(),
					InvestmentDetailInfo: map[string]*wealthOBPb.PreInvestmentDetailsV2{
						sampleOrder.GetActorId(): {
							InvestmentDetails: &wealthOBPb.PreInvestmentDetail{
								CustomerName: &commontypes.Name{
									FirstName: "Batman",
								},
								Dob: &date.Date{
									Year:  2022,
									Month: 8,
									Day:   01,
								},
								AddressType: types.AddressType_PERMANENT,
								Address: &types.PostalAddress{
									Locality:     "gotham",
									AddressLines: []string{"line1"},
								},
								PoliticallyExposedStatus: types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE,
								TaxResidentialStatus:     types.ResidentialStatus_RESIDENT_INDIVIDUAL,
								Nationality:              types.Nationality_NATIONALITY_INDIAN,
								Gender:                   types.Gender_FEMALE,
								Pan: &types.DocumentProof{
									Id: "123",
								},
								EmploymentData: &wealthOBPb.EmploymentData{
									EmploymentType: employment.EmploymentType_SALARIED,
								},
								BankDetails: &wealthOBPb.BankDetails{
									AccountNumber: "123",
									IfscCode:      "123123",
								},
							},
							FailureType:   wealthOBPb.FailureType_FAILURE_TYPE_NOT_RETRYABLE,
							FailureReason: wealthOBPb.FailureReason_FAILURE_REASON_DOB_MISMATCH,
						},
					},
				}, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{
					Code:         uint32(pb.CreateOrderResponse_ACTOR_INELIGIBLE_DOB_MISMATCH),
					ShortMessage: "Actor not eligible due to DOB_MISMATCH",
					DebugMessage: "",
				},
				Order: nil,
			},
		},
		{
			name: "order fail - fund not taking any investment",
			args: args{
				ctx: ctx,
				req: req,
			},
			setupMockCalls: func(d *dep) {
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(sampleFundAmcNotTakingAnyInvestment, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFundAmcNotTakingAnyInvestment.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.NewStatus(uint32(pb.CreateOrderResponse_FUND_UNAVAILABLE_FOR_INVESTMENT_BY_AMC),
					"error in createOrder for buy",
					"fund is not available for investors by amc, fund_investment_status: UNAVAILABLE_FOR_INVESTMENT: FundEligibilityValidationError"),
				Order: nil,
			},
		},
		{
			name: "order fail - fund not taking new investment - new investor",
			args: args{
				ctx: ctx,
				req: req,
			},
			setupMockCalls: func(d *dep) {
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(sampleFundAmcNotTakingNewInvestment, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFundAmcNotTakingNewInvestment.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{}, nil)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.NewStatus(uint32(pb.CreateOrderResponse_FUND_UNAVAILABLE_FOR_INVESTMENT_BY_AMC),
					"error in createOrder for buy",
					"fund is not available for investors by amc, fund_investment_status: AVAILABLE_ONLY_FOR_EXISTING_INVESTORS: FundEligibilityValidationError"),
				Order: nil,
			},
		},
		{
			name: "create success - fund not taking new investment but old investor",
			args: args{
				ctx: ctx,
				req: req,
			},
			setupMockCalls: func(d *dep) {
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(sampleFundAmcNotTakingNewInvestment, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFundAmcNotTakingNewInvestment.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
				// mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(orderResp, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.NewStatus(uint32(pb.CreateOrderResponse_FUND_UNAVAILABLE_FOR_INVESTMENT_BY_AMC),
					"error in createOrder for buy",
					"fund is not available for investors by amc, fund_investment_status: AVAILABLE_ONLY_FOR_EXISTING_INVESTORS: FundEligibilityValidationError"),
				Order: nil,
			},
		},
		{
			name: "create sip order success, new sip creation",
			args: args{
				ctx: ctx,
				req: &pb.CreateOrderRequest{
					ActorId:        sampleOrder.ActorId,
					MutualFundId:   sampleOrder.MutualFundId,
					ReinvType:      sampleOrder.ReinvType,
					Units:          sampleOrder.Units,
					Amount:         sampleOrder.Amount,
					OrderType:      sampleOrder.OrderType,
					OrderSubType:   pb.OrderSubType_BUY_SIP,
					Client:         sampleOrder.Client,
					ClientOrderId:  sampleOrder.ClientOrderId,
					PaymentMode:    sampleOrder.PaymentMode,
					Rta:            sampleOrder.Rta,
					PayInfo:        &pb.PaymentRequestInfo{RecurringPaymentId: sampleOrder.PaymentInfo.RecurringPaymentId},
					InvestmentGoal: "NO_GOAL",
					SipExecutionInfo: &pb.SIPExecutionInfo{
						SipGranularity:      mfPb.SIPGranularity_SIP_GRANULARITY_MONTHLY,
						FitttSubscriptionId: "fitSub1",
						ExecutionDate:       &timestamp.Timestamp{Seconds: **********}, // 1 Aug 2022,
					},
				},
			},
			setupMockCalls: func(d *dep) {
				fund := &mfPb.MutualFund{}
				_ = copier.Copy(fund, sampleFund)
				fund.TxnConstraints = &mfPb.TransactionConstraints{
					SipMetadata: &mfPb.SipMetadata{
						SiDetails: map[string]*mfPb.AipDetail{
							mfPb.AipFrequency_WEEKLY.String(): {
								AllowedDays: []dayofweek.DayOfWeek{
									dayofweek.DayOfWeek_MONDAY,
								},
							},
							mfPb.AipFrequency_MONTHLY.String(): {
								AllowedDates: []*date.Date{
									{
										Day: int32(1),
									},
								},
							},
						},
					},
					AllowedSipFrequencies: []mfPb.AipFrequency{mfPb.AipFrequency_MONTHLY},
				}
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(fund, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFund.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockSipLedgerDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				d.mockSipLedgerDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(sampleSipLedger, nil)
				d.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(orderResp, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
				d.mockPaymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(sampleGetPaymentDetailsResp, nil)

			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.StatusOk(),
				Order:  orderResp,
			},
		},
		{
			name: "create sip order success, existing sip",
			args: args{
				ctx: ctx,
				req: &pb.CreateOrderRequest{
					ActorId:        sampleOrder.ActorId,
					MutualFundId:   sampleOrder.MutualFundId,
					ReinvType:      sampleOrder.ReinvType,
					Units:          sampleOrder.Units,
					Amount:         sampleOrder.Amount,
					OrderType:      sampleOrder.OrderType,
					OrderSubType:   pb.OrderSubType_BUY_SIP,
					Client:         sampleOrder.Client,
					ClientOrderId:  sampleOrder.ClientOrderId,
					PaymentMode:    sampleOrder.PaymentMode,
					Rta:            sampleOrder.Rta,
					PayInfo:        &pb.PaymentRequestInfo{RecurringPaymentId: sampleOrder.PaymentInfo.RecurringPaymentId},
					InvestmentGoal: "NO_GOAL",
					SipExecutionInfo: &pb.SIPExecutionInfo{
						SipGranularity:      mfPb.SIPGranularity_SIP_GRANULARITY_MONTHLY,
						FitttSubscriptionId: "fitSub1",
						ExecutionDate:       &timestamp.Timestamp{Seconds: **********}, // 1 Aug 2022,
					},
				},
			},
			setupMockCalls: func(d *dep) {
				fund := &mfPb.MutualFund{}
				_ = copier.Copy(fund, sampleFund)
				fund.TxnConstraints = &mfPb.TransactionConstraints{
					SipMetadata: &mfPb.SipMetadata{
						SiDetails: map[string]*mfPb.AipDetail{
							mfPb.AipFrequency_WEEKLY.String(): {
								AllowedDays: []dayofweek.DayOfWeek{
									dayofweek.DayOfWeek_MONDAY,
								},
							},
							mfPb.AipFrequency_MONTHLY.String(): {
								AllowedDates: []*date.Date{
									{
										Day: int32(1),
									},
								},
							},
						},
					},
					AllowedSipFrequencies: []mfPb.AipFrequency{mfPb.AipFrequency_MONTHLY},
				}
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(fund, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFund.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockSipLedgerDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mfPb.SIPLedger{sampleSipLedger}, nil)
				d.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(orderResp, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
				d.mockPaymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(sampleGetPaymentDetailsResp, nil)

			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.StatusOk(),
				Order:  orderResp,
			},
		},
		{
			name: "create sip order failed, sip not allowed",
			args: args{
				ctx: ctx,
				req: &pb.CreateOrderRequest{
					ActorId:        sampleOrder.ActorId,
					MutualFundId:   sampleOrder.MutualFundId,
					ReinvType:      sampleOrder.ReinvType,
					Units:          sampleOrder.Units,
					Amount:         sampleOrder.Amount,
					OrderType:      sampleOrder.OrderType,
					OrderSubType:   pb.OrderSubType_BUY_SIP,
					Client:         sampleOrder.Client,
					ClientOrderId:  sampleOrder.ClientOrderId,
					PaymentMode:    sampleOrder.PaymentMode,
					Rta:            sampleOrder.Rta,
					PayInfo:        &pb.PaymentRequestInfo{RecurringPaymentId: sampleOrder.PaymentInfo.RecurringPaymentId},
					InvestmentGoal: "NO_GOAL",
					SipExecutionInfo: &pb.SIPExecutionInfo{
						SipGranularity:      mfPb.SIPGranularity_SIP_GRANULARITY_WEEKLY,
						FitttSubscriptionId: "fitSub1",
						ExecutionDate:       &timestamp.Timestamp{Seconds: **********}, // 1 Aug 2022,
					},
				},
			},
			setupMockCalls: func(d *dep) {
				fund := &mfPb.MutualFund{}
				_ = copier.Copy(fund, sampleFund)
				fund.TxnConstraints = &mfPb.TransactionConstraints{
					SipMetadata: &mfPb.SipMetadata{
						SiDetails: map[string]*mfPb.AipDetail{
							mfPb.AipFrequency_WEEKLY.String(): {
								AllowedDays: []dayofweek.DayOfWeek{
									dayofweek.DayOfWeek_MONDAY,
								},
							},
							mfPb.AipFrequency_MONTHLY.String(): {
								AllowedDates: []*date.Date{
									{
										Day: int32(1),
									},
								},
							},
						},
					},
					AllowedSipFrequencies: []mfPb.AipFrequency{mfPb.AipFrequency_MONTHLY},
				}
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(fund, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFund.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockSipLedgerDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.NewStatus(uint32(pb.CreateOrderResponse_SIP_CONSTRAINTS_FAILED),
					"error in createOrder for buy",
					"sip is not allowed for this fund: SipValidationError"),
			},
		},
		{
			name: "create sip order failed, sip amount less than min. allowed",
			args: args{
				ctx: ctx,
				req: &pb.CreateOrderRequest{
					ActorId:        sampleOrder.ActorId,
					MutualFundId:   sampleOrder.MutualFundId,
					ReinvType:      sampleOrder.ReinvType,
					Units:          sampleOrder.Units,
					Amount:         sampleOrder.Amount,
					OrderType:      sampleOrder.OrderType,
					OrderSubType:   pb.OrderSubType_BUY_SIP,
					Client:         sampleOrder.Client,
					ClientOrderId:  sampleOrder.ClientOrderId,
					PaymentMode:    sampleOrder.PaymentMode,
					Rta:            sampleOrder.Rta,
					PayInfo:        &pb.PaymentRequestInfo{RecurringPaymentId: sampleOrder.PaymentInfo.RecurringPaymentId},
					InvestmentGoal: "NO_GOAL",
					SipExecutionInfo: &pb.SIPExecutionInfo{
						SipGranularity:      mfPb.SIPGranularity_SIP_GRANULARITY_MONTHLY,
						FitttSubscriptionId: "fitSub1",
						ExecutionDate:       &timestamp.Timestamp{Seconds: **********}, // 1 Aug 2022,
					},
				},
			},
			setupMockCalls: func(d *dep) {
				fund := &mfPb.MutualFund{}
				_ = copier.Copy(fund, sampleFund)
				fund.TxnConstraints = &mfPb.TransactionConstraints{
					SipMetadata: &mfPb.SipMetadata{
						SiDetails: map[string]*mfPb.AipDetail{
							mfPb.AipFrequency_MONTHLY.String(): {
								MinAmount: 500,
								AllowedDates: []*date.Date{
									{
										Day: int32(1),
									},
								},
							},
						},
					},
					AllowedSipFrequencies: []mfPb.AipFrequency{mfPb.AipFrequency_MONTHLY},
				}
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(fund, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFund.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockSipLedgerDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.NewStatus(uint32(pb.CreateOrderResponse_TRANSACTION_CONSTRAINTS_FAILED),
					"error in createOrder for buy",
					"order amount is less than the minimum amount for SIP: PurchaseConstraintsValidationError"),
			},
		},
		{
			name: "create sip order failed, sip schedule invalid",
			args: args{
				ctx: ctx,
				req: &pb.CreateOrderRequest{
					ActorId:        sampleOrder.ActorId,
					MutualFundId:   sampleOrder.MutualFundId,
					ReinvType:      sampleOrder.ReinvType,
					Units:          sampleOrder.Units,
					Amount:         sampleOrder.Amount,
					OrderType:      sampleOrder.OrderType,
					OrderSubType:   pb.OrderSubType_BUY_SIP,
					Client:         sampleOrder.Client,
					ClientOrderId:  sampleOrder.ClientOrderId,
					PaymentMode:    sampleOrder.PaymentMode,
					Rta:            sampleOrder.Rta,
					PayInfo:        &pb.PaymentRequestInfo{RecurringPaymentId: sampleOrder.PaymentInfo.RecurringPaymentId},
					InvestmentGoal: "NO_GOAL",
					SipExecutionInfo: &pb.SIPExecutionInfo{
						SipGranularity:      mfPb.SIPGranularity_SIP_GRANULARITY_MONTHLY,
						FitttSubscriptionId: "fitSub1",
						ExecutionDate:       &timestamp.Timestamp{Seconds: **********}, // 1 Aug 2022,
					},
				},
			},
			setupMockCalls: func(d *dep) {
				fund := &mfPb.MutualFund{}
				_ = copier.Copy(fund, sampleFund)
				fund.TxnConstraints = &mfPb.TransactionConstraints{
					SipMetadata: &mfPb.SipMetadata{
						SiDetails: map[string]*mfPb.AipDetail{
							mfPb.AipFrequency_MONTHLY.String(): {
								AllowedDates: []*date.Date{
									{
										Day: int32(10),
									},
								},
							},
						},
					},
					AllowedSipFrequencies: []mfPb.AipFrequency{mfPb.AipFrequency_MONTHLY},
				}
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(fund, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFund.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockSipLedgerDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.NewStatus(uint32(pb.CreateOrderResponse_TRANSACTION_CONSTRAINTS_FAILED),
					"error in createOrder for buy",
					"schedule is invalid for monthly SIP. requested sip start date: 2022-08-01 00:00:00 +0000 UTC: PurchaseConstraintsValidationError"),
			},
		},
		{
			name: "fail order if nominee not declared",
			args: args{
				ctx: ctx,
				req: &pb.CreateOrderRequest{
					ActorId:        sampleOrder.ActorId,
					MutualFundId:   sampleOrder.MutualFundId,
					ReinvType:      sampleOrder.ReinvType,
					Units:          sampleOrder.Units,
					Amount:         sampleOrder.Amount,
					OrderType:      sampleOrder.OrderType,
					OrderSubType:   pb.OrderSubType_BUY_SIP,
					Client:         sampleOrder.Client,
					ClientOrderId:  sampleOrder.ClientOrderId,
					PaymentMode:    sampleOrder.PaymentMode,
					Rta:            sampleOrder.Rta,
					PayInfo:        &pb.PaymentRequestInfo{RecurringPaymentId: sampleOrder.PaymentInfo.RecurringPaymentId},
					InvestmentGoal: "NO_GOAL",
					SipExecutionInfo: &pb.SIPExecutionInfo{
						SipGranularity:      mfPb.SIPGranularity_SIP_GRANULARITY_MONTHLY,
						FitttSubscriptionId: "fitSub1",
						ExecutionDate:       &timestamp.Timestamp{Seconds: **********}, // 1 Aug 2022,
					},
				},
			},
			setupMockCalls: func(d *dep) {
				fund := &mfPb.MutualFund{}
				_ = copier.Copy(fund, sampleFund)
				fund.TxnConstraints = &mfPb.TransactionConstraints{
					SipMetadata: &mfPb.SipMetadata{
						SiDetails: map[string]*mfPb.AipDetail{
							mfPb.AipFrequency_WEEKLY.String(): {
								AllowedDays: []dayofweek.DayOfWeek{
									dayofweek.DayOfWeek_MONDAY,
								},
							},
							mfPb.AipFrequency_MONTHLY.String(): {
								AllowedDates: []*date.Date{
									{
										Day: int32(1),
									},
								},
							},
						},
					},
					AllowedSipFrequencies: []mfPb.AipFrequency{mfPb.AipFrequency_MONTHLY},
				}
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(fund, nil)
				d.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), sampleFund.Amc).Times(1).Return(&mfPb.AmcInfo{Rta: order.Rta}, nil)
				d.mockSipLedgerDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mfPb.SIPLedger{sampleSipLedger}, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_BALANCE_UNITS, true, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).
					Return(nil, epifierrors.ErrRecordNotFound)
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(sampleWoRes, nil)

				wealthOnbResWithoutNominee := &wealthOBPb.GetInvestmentDataV2Response{}
				err := copier.Copy(wealthOnbResWithoutNominee, sampleWoRes)
				if err != nil {
					t.Errorf("error copying wealth onboarding response")
					return
				}
				wealthOnbResWithoutNominee.GetInvestmentDetailInfo()[sampleOrder.GetActorId()].GetInvestmentDetails().NomineeDeclarationDetails = &wealthOBPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED}
				d.wealthOBClient.EXPECT().GetInvestmentDataV2(gomock.Any(), gomock.Any()).Times(1).Return(wealthOnbResWithoutNominee, nil)
				d.userClient.EXPECT().GetNominees(gomock.Any(), &userPb.GetNomineesRequest{
					ActorId: "ac1",
				}).Return(&userPb.GetNomineesResponse{
					Status:   rpc.StatusOk(),
					Nominees: []*types.Nominee{{}},
				}, nil)
			},
			want: &pb.CreateOrderResponse{
				Status: rpc.NewStatus(uint32(pb.CreateOrderResponse_WEALTH_ACCOUNT_NOMINEE_NOT_DECLARED),
					"error in createOrder for buy",
					"nominee not declared: NomineeDeclarationValidationErr"),
			},
		},
	}
	for _, tt := range tests {

		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockOrderDao := daoMocks.NewMockOrderDao(ctr)
			mockAMCDao := daoMocks.NewMockAmcInfoDao(ctr)
			mockFolioDao := daoMocks.NewMockFolioLedgerDao(ctr)
			mockSipLedgerDao := daoMocks.NewMockSIPLedgerDao(ctr)
			mockMutualFundDao := daoMocks.NewMockMutualFundDao(ctr)
			mockFileStateDao := daoMocks.NewMockFileStateDao(ctr)
			wealthOBClient := wealthOBMocks.NewMockWealthOnboardingClient(ctr)
			userClient := userClientMock.NewMockUsersClient(ctr)
			mockPanClient := mocks.NewMockPanClient(ctr)
			mockOrderDelayedNotificationPublisher := queueMock.NewMockDelayPublisher(ctr)
			mockOrderEtaDelayPublisher := queueMock.NewMockDelayPublisher(ctr)
			mockInvestmentAuthClient := authMock.NewMockAuthClient(ctr)
			mockOrderDelayedNotificationPublisher.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil).AnyTimes()
			mockOrderEtaDelayPublisher.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil).AnyTimes()

			mockPaymentHandlerClient := mockPh.NewMockPaymentHandlerClient(ctr)

			orderMgrSvc := &Service{
				orderDao:                          mockOrderDao,
				folioDao:                          mockFolioDao,
				fileStateDao:                      mockFileStateDao,
				mutualFundDao:                     mockMutualFundDao,
				amcInfoDao:                        mockAMCDao,
				idempotentTxnExecutor:             txnExecutor,
				wealthOnboardingClient:            wealthOBClient,
				userClient:                        userClient,
				cfg:                               conf,
				orderDelayedNotificationPublisher: mockOrderDelayedNotificationPublisher,
				orderStatusNotifier:               &updOrder.OrderStatusNotifierImpl{},
				buyOrderProcessor: order_type_processor.NewBuyOrderProcessor(
					mockFolioDao, mockOrderDao, mockSipLedgerDao,
					validator.NewPurchaseConstraintsValidator(),
					validator.NewFundEligibilityValidator(),
					validator.NewNomineeDeclarationValidator(wealthOBClient),
					validator.NewPanAadhaarLinkValidator(svcTS.conf, mockPanClient),
					svcTS.conf, mockInvestmentAuthClient,
				),
				paymentHandlerClient:   mockPaymentHandlerClient,
				orderETADelayPublisher: mockOrderEtaDelayPublisher,
			}
			a := &dep{
				mockOrderDao:                          mockOrderDao,
				mockAMCDao:                            mockAMCDao,
				mockFolioDao:                          mockFolioDao,
				mockSipLedgerDao:                      mockSipLedgerDao,
				mockMutualFundDao:                     mockMutualFundDao,
				mockFileStateDao:                      mockFileStateDao,
				wealthOBClient:                        wealthOBClient,
				userClient:                            userClient,
				mockPanClient:                         mockPanClient,
				mockOrderDelayedNotificationPublisher: mockOrderDelayedNotificationPublisher,
				mockInvestmentAuthClient:              mockInvestmentAuthClient,
				mockPaymentHandlerClient:              mockPaymentHandlerClient,
				mockOrderEtaDelayPublisher:            mockOrderEtaDelayPublisher,
			}
			tt.setupMockCalls(a)
			got, err := orderMgrSvc.CreateOrder(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("CreateOrder() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestService_CreateSellOrder(t *testing.T) {
	ctr := gomock.NewController(t)
	type dep struct {
		mockOrderDao         *daoMocks.MockOrderDao
		mockOrderStatusDao   *daoMocks.MockOrderStatusUpdateDao
		mockAMCDao           *daoMocks.MockAmcInfoDao
		mockFolioDao         *daoMocks.MockFolioLedgerDao
		mockMutualFundDao    *daoMocks.MockMutualFundDao
		mockSavingsClient    *mockSavings.MockSavingsClient
		mockPanClient        *mocks.MockPanClient
		etaProcessor         *queueMock.MockDelayPublisher
		paymentHandlerClient *mockPh.MockPaymentHandlerClient
	}
	order := &pb.Order{
		ActorId:       sampleOrder.ActorId,
		MutualFundId:  sampleOrder.MutualFundId,
		Amount:        sampleOrder.Amount,
		Units:         10.0000000045,
		OrderType:     pb.OrderType_SELL,
		OrderStatus:   pb.OrderStatus_CREATED,
		Client:        sampleOrder.Client,
		ClientOrderId: sampleOrder.ClientOrderId + "_0",
		Rta:           sampleOrder.Rta,
		PaymentMode:   sampleOrder.PaymentMode,
		Amc:           mfPb.Amc_ICICI_PRUDENTIAL,
		FolioId:       "123456",
	}
	req := &pb.CreateOrderRequest{
		ActorId:        sampleOrder.ActorId,
		MutualFundId:   sampleOrder.MutualFundId,
		ReinvType:      sampleOrder.ReinvType,
		Units:          sampleOrder.Units,
		Amount:         sampleOrder.Amount,
		OrderType:      pb.OrderType_SELL,
		Client:         sampleOrder.Client,
		ClientOrderId:  sampleOrder.ClientOrderId,
		PaymentMode:    sampleOrder.PaymentMode,
		Rta:            sampleOrder.Rta,
		PayInfo:        &pb.PaymentRequestInfo{RecurringPaymentId: sampleOrder.PaymentInfo.RecurringPaymentId},
		InvestmentGoal: "NO_GOAL",
	}
	req2 := &pb.CreateOrderRequest{}
	_ = copier.Copy(req2, req)
	req2.Amount = &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        1200,
	}

	req3 := &pb.CreateOrderRequest{}
	_ = copier.Copy(req3, req)
	req3.Amount = &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        201,
		Nanos:        123400000,
	}

	req4 := &pb.CreateOrderRequest{}
	_ = copier.Copy(req4, req)
	req4.Amount = &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        1212,
		Nanos:        779300000,
	}

	req5 := &pb.CreateOrderRequest{}
	_ = copier.Copy(req5, req)
	req5.IsWithdrawAll = true
	ltpFactory := ltp.NewLockInProcessorFactory(ltp.NewHardLockinProcessor(wec.NewHardLockInCalculator()),
		ltp.NewSoftLockinProcessor(wec.NewNoLockInCalculator()), ltp.NewNoLockinProcessor(wec.NewNoLockInCalculator()))

	// successful created order
	orderResp := &pb.Order{}
	_ = copier.Copy(orderResp, sampleOrder)
	orderResp.Id = sampleOrderId

	type args struct {
		ctx context.Context
		req *pb.CreateOrderRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(a *dep)
		want           *pb.CreateOrderResponse
		wantErr        bool
	}{
		{
			name: "create success for a single folio withdrawal with no pending orders",
			args: args{
				ctx: ctx,
				req: req,
			},
			setupMockCalls: func(a *dep) {
				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(sampleFund, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), order).Times(1).Return(orderResp, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any()).Times(1).Return([]*pb.Order{}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Times(1).Return([]*pb.Order{}, nil)

				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)
				a.paymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(&phPb.GetPaymentDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				a.etaProcessor.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: 0},
			},
		},
		{
			/*
				Input States:
					WithdrawalAmount = 201.1234
					Folio1 BalanceUnits = 100
					Nav: 173.7027
				Orders Created:
					Order1:
						Amount: 201.1234
						Units: 1.157859952666251
						Verification: (201.1234/173.7027) = 1.157859952666251 (Amount / Nav = Units)
			*/
			name: "create success for a single folio withdrawal with no pending orders for decimal nav",
			args: args{
				ctx: ctx,
				req: req3,
			},
			setupMockCalls: func(a *dep) {

				sampleFund2 := &mfPb.MutualFund{}
				_ = copier.Copy(sampleFund2, sampleFund)
				sampleFund2.Nav = &moneyPb.Money{CurrencyCode: "INR", Units: 173, Nanos: 702700000}

				order1 := &pb.Order{}
				_ = copier.Copy(order1, order)
				order1.Amount = req3.Amount
				order1.Units = 1.157859952666251 // Money/Nav

				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(sampleFund2, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), order1).Times(1).Return(orderResp, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any()).Times(1).Return([]*pb.Order{}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Times(1).Return([]*pb.Order{}, nil)
				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)
				a.paymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(&phPb.GetPaymentDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				a.etaProcessor.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: 0},
			},
		},
		{
			name: "create success for multiple folio withdrawal with no pending orders",
			args: args{
				ctx: ctx,
				req: req2,
			},
			setupMockCalls: func(a *dep) {
				order1 := &pb.Order{}
				_ = copier.Copy(order1, order)
				order1.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
				}
				order1.Units = 100
				order1.ClientOrderId = sampleOrder.ClientOrderId + "_0"

				order2 := &pb.Order{}
				_ = copier.Copy(order2, order)
				order2.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        200,
				}
				order2.Units = 20
				order2.ClientOrderId = sampleOrder.ClientOrderId + "_1"

				folio2 := &mfPb.FolioLedger{}
				_ = copier.Copy(folio2, sampleFolio)
				folio2.FolioId = "15681"
				order2.FolioId = "15681"

				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(sampleFund, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order1, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order2, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio, folio2}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any()).Times(1).Return([]*pb.Order{}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Times(1).Return([]*pb.Order{}, nil)

				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)
				a.paymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(&phPb.GetPaymentDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				a.etaProcessor.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)

			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: 0},
			},
		},
		{
			/*
				Input States:
					WithdrawalAmount = 1200
					Folio1 BalanceUnits = 100
					Folio2 BalanceUnits = 100
					Nav: 10.7027
				Orders Created:
					Order1:
						Amount: 1070.2700
						Units: 100
						Verification: (1070.2700/10.7027) = 100 (Amount / Nav = Units)
					Order 2:
						Amount: 129.7300
						Units: 12.121240434656675
						Verification: (129.7300/10.7027) = 12.121240434656675 (Amount / Nav = Units)
			*/
			name: "create success for multiple folio withdrawal with no pending orders with decimal nav",
			args: args{
				ctx: ctx,
				req: req2,
			},
			setupMockCalls: func(a *dep) {

				sampleFund2 := &mfPb.MutualFund{}
				_ = copier.Copy(sampleFund2, sampleFund)
				sampleFund2.Nav = &moneyPb.Money{CurrencyCode: "INR", Units: 10, Nanos: 702700000}

				order1 := &pb.Order{}
				_ = copier.Copy(order1, order)
				order1.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1070,
					Nanos:        270000000,
				}
				order1.Units = 100
				order1.ClientOrderId = sampleOrder.ClientOrderId + "_0"

				order2 := &pb.Order{}
				_ = copier.Copy(order2, order)
				order2.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        129,
					Nanos:        730000000,
				}
				order2.Units = 12.121240434656675
				order2.ClientOrderId = sampleOrder.ClientOrderId + "_1"

				folio2 := &mfPb.FolioLedger{}
				_ = copier.Copy(folio2, sampleFolio)
				folio2.FolioId = "15681"
				order2.FolioId = "15681"

				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(sampleFund2, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order1, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order2, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio, folio2}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any()).Times(1).Return([]*pb.Order{}, nil)

				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Times(1).Return([]*pb.Order{}, nil)

				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)
				a.paymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(&phPb.GetPaymentDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				a.etaProcessor.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: 0},
			},
		},
		{
			name: "create success for a single folio withdrawal with pending orders",
			args: args{
				ctx: ctx,
				req: req,
			},
			setupMockCalls: func(a *dep) {
				pendingOrder := &pb.Order{}
				_ = copier.Copy(pendingOrder, order)
				pendingOrder.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        200,
				}
				pendingOrder.Units = 20

				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(sampleFund, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), order).Times(1).Return(orderResp, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any()).Times(1).Return([]*pb.Order{pendingOrder}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Times(1).Return([]*pb.Order{pendingOrder}, nil)

				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)
				a.paymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(&phPb.GetPaymentDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				a.etaProcessor.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: 0},
			},
		},
		/*
			Input States:
				WithdrawalAmount = 201.1234
				Folio1 BalanceUnits = 100
				Pending orderUnits = 20.8964
				WithDrawableUnits = 100 - 20.8964 = 79.1036
				Nav: 179.7027
			Orders Created:
				Order1:
					Amount: 201.1234
					Units: 1.1192007688253989
					Verification: (201.1234/179.7027) = 1.1192007688253989 (Amount / Nav = Units)
		*/
		{
			name: "create success for a single folio withdrawal with pending orders",
			args: args{
				ctx: ctx,
				req: req3,
			},
			setupMockCalls: func(a *dep) {

				sampleFund2 := &mfPb.MutualFund{}
				_ = copier.Copy(sampleFund2, sampleFund)
				sampleFund2.Nav = &moneyPb.Money{CurrencyCode: "INR", Units: 179, Nanos: 702700000}

				order1 := &pb.Order{}
				_ = copier.Copy(order1, order)
				order1.Amount = req3.Amount
				order1.Units = 1.1192007688253989 // Money/Nav

				pendingOrder := &pb.Order{}
				_ = copier.Copy(pendingOrder, order)
				pendingOrder.Units = 20.8964

				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(sampleFund2, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), order1).Times(1).Return(orderResp, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any()).Times(1).Return([]*pb.Order{pendingOrder}, nil)

				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Times(1).Return([]*pb.Order{pendingOrder}, nil)

				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)
				a.paymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(&phPb.GetPaymentDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				a.etaProcessor.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: 0},
			},
		},
		{
			name: "create success for multiple folio withdrawal with pending orders",
			args: args{
				ctx: ctx,
				req: req2,
			},
			setupMockCalls: func(a *dep) {
				order1 := &pb.Order{}
				_ = copier.Copy(order1, order)
				order1.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        800,
				}
				order1.Units = 80
				order1.ClientOrderId = sampleOrder.ClientOrderId + "_0"

				order2 := &pb.Order{}
				_ = copier.Copy(order2, order)
				order2.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        400,
				}
				order2.Units = 40
				order2.ClientOrderId = sampleOrder.ClientOrderId + "_1"

				folio2 := &mfPb.FolioLedger{}
				_ = copier.Copy(folio2, sampleFolio)
				folio2.FolioId = "15681"
				order2.FolioId = "15681"

				pendingOrder := &pb.Order{}
				_ = copier.Copy(pendingOrder, order)
				pendingOrder.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        200,
				}
				pendingOrder.Units = 20

				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(sampleFund, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order1, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order2, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio, folio2}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any()).Times(1).Return([]*pb.Order{pendingOrder}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Times(1).Return([]*pb.Order{pendingOrder}, nil)
				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)
				a.paymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(&phPb.GetPaymentDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				a.etaProcessor.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: 0},
			},
		},
		/*
				Input States:
				WithdrawalAmount = 1212.779300000
				Folio1 BalanceUnits = 100
				Folio1 Pending orderUnits = 27.3565
				Folio1 WithDrawableUnits = 100 - 20.8964 = 72.3565
				Folio2 BalanceUnits = 100
				Folio1 Pending orderUnits = 0
				Folio1 WithDrawableUnits = 100
				Nav: 14.911312000
			Orders Created:
				Order1:
					Amount: 1083.209893272
					Units: 72.6435
					Verification: (1083.209893272/14.911312000) = 72.6435 (Amount / Nav = Units)
				Order2:
					Amount: 129.569406728
					Units: 8.689336439878664
					Verification: (129.569406728/14.911312000) = 8.689336439878664 (Amount / Nav = Units)
		*/
		{
			name: "create success for multiple folio withdrawal with pending orders",
			args: args{
				ctx: ctx,
				req: req4,
			},
			setupMockCalls: func(a *dep) {
				order1 := &pb.Order{}
				_ = copier.Copy(order1, order)
				order1.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1083,
					Nanos:        209893272,
				}
				order1.Units = 72.6435
				order1.ClientOrderId = sampleOrder.ClientOrderId + "_0"

				order2 := &pb.Order{}
				_ = copier.Copy(order2, order)
				order2.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        129,
					Nanos:        569406728,
				}
				order2.Units = 8.689336439878664
				order2.ClientOrderId = sampleOrder.ClientOrderId + "_1"

				folio2 := &mfPb.FolioLedger{}
				_ = copier.Copy(folio2, sampleFolio)
				folio2.FolioId = "14680"
				folio2.BalanceUnits = 0

				folio3 := &mfPb.FolioLedger{}
				_ = copier.Copy(folio3, sampleFolio)
				folio3.FolioId = "15681"
				order2.FolioId = "15681"

				pendingOrder := &pb.Order{}
				_ = copier.Copy(pendingOrder, order)
				pendingOrder.Units = 27.3565

				sampleFund2 := &mfPb.MutualFund{}
				_ = copier.Copy(sampleFund2, sampleFund)
				sampleFund2.Nav = &moneyPb.Money{CurrencyCode: "INR", Units: 14, Nanos: 911312000}

				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(sampleFund2, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order1, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order2, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio, folio2, folio3}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any()).Times(1).Return([]*pb.Order{pendingOrder}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Times(1).Return([]*pb.Order{pendingOrder}, nil)
				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)
				a.paymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(&phPb.GetPaymentDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				a.etaProcessor.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: 0},
			},
		},
		{
			name: "create failure because of pending orders",
			args: args{
				ctx: ctx,
				req: req2,
			},
			setupMockCalls: func(a *dep) {
				pendingOrder := &pb.Order{}
				_ = copier.Copy(pendingOrder, order)
				pendingOrder.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        990,
				}
				pendingOrder.Units = 99

				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(sampleFund, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any()).Times(1).Return([]*pb.Order{pendingOrder}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Times(1).Return([]*pb.Order{pendingOrder}, nil)
				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: uint32(pb.CreateOrderResponse_WITHDRAWAL_AMOUNT_EXCEEDS_REDEEMABLE_AMOUNT),
					ShortMessage: "error in createOrder for sell",
					DebugMessage: "order exceeded withdrawal limit: unitsToSell is greater than the totalRedeemableUnits",
				},
			},
		},
		{
			name: "duplicate order error",
			args: args{
				ctx: ctx,
				req: req,
			},
			setupMockCalls: func(a *dep) {
				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(1).Return(sampleFund, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any()).Times(1).Return([]*pb.Order{}, nil)
				a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Times(1).Return([]*pb.Order{}, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Eq(order)).Times(1).Return(nil, epifierrors.ErrDuplicateEntry)

				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: rpc.NewStatus(13, "error in createOrder for sell", "error while creating new sell orders: duplicate entry"),
				Order:  nil,
			},
		},
		/*
				Input States:
				WithdrawalAmount = 1212.779300000
				Folio1 BalanceUnits = 100
				Folio1 Pending orderUnits = 27.3565
				Folio1 WithDrawableUnits = 100 - 20.8964 = 72.3565
				Folio2 BalanceUnits = 100
				Folio2 Pending orderUnits = 0
				Folio2 WithDrawableUnits = 100
				Nav: 14.911312000
			Orders Created:
				Order1:
					Units: 72.3565
					Amount:1083.209893272,
				Order2:
					Units: 100
					Units: 1491.1312,

		*/
		{
			name: "create success for multiple folio withdrawal with pending orders",
			args: args{
				ctx: ctx,
				req: req5,
			},
			setupMockCalls: func(a *dep) {
				order1 := &pb.Order{}
				_ = copier.Copy(order1, order)
				order1.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1083,
					Nanos:        209893272,
				}
				order1.Units = 72.6435
				order1.ClientOrderId = sampleOrder.ClientOrderId + "_0"
				order1.OrderSubType = pb.OrderSubType_SELL_FULL_REDEMPTION_WITH_FOLIO_CLOSURE

				order2 := &pb.Order{}
				_ = copier.Copy(order2, order)
				order2.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1491,
					Nanos:        131200000,
				}
				order2.Units = 100
				order2.ClientOrderId = sampleOrder.ClientOrderId + "_1"
				order2.OrderSubType = pb.OrderSubType_SELL_FULL_REDEMPTION_WITH_FOLIO_CLOSURE

				folio2 := &mfPb.FolioLedger{}
				_ = copier.Copy(folio2, sampleFolio)
				folio2.FolioId = "14680"
				folio2.BalanceUnits = 0

				folio3 := &mfPb.FolioLedger{}
				_ = copier.Copy(folio3, sampleFolio)
				folio3.FolioId = "15681"
				order2.FolioId = "15681"

				pendingOrder := &pb.Order{}
				_ = copier.Copy(pendingOrder, order)
				pendingOrder.Units = 27.3565

				sampleFund2 := &mfPb.MutualFund{}
				_ = copier.Copy(sampleFund2, sampleFund)
				sampleFund2.Nav = &moneyPb.Money{CurrencyCode: "INR", Units: 14, Nanos: 911312000}

				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(sampleFund2, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order1, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order2, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio, folio2, folio3}, nil)
				gomock.InOrder(
					a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
						gomock.Any()).Return([]*pb.Order{pendingOrder}, nil).Times(1),
					a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
						gomock.Any(), gomock.Any()).Return([]*pb.Order{pendingOrder}, nil).Times(1),
					a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
						gomock.Any(), gomock.Any(), gomock.Any()).Return([]*pb.Order{}, nil).AnyTimes(),
				)
				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)

				sampleFolioCopy := &mfPb.FolioLedger{}
				_ = copier.Copy(sampleFolioCopy, sampleFolio)
				sampleFolioCopy.Status = mfPb.FolioStatus_FolioStatus_PENDING_CLOSURE

				folio3Copy := &mfPb.FolioLedger{}
				_ = copier.Copy(folio3Copy, folio3)
				folio3Copy.Status = mfPb.FolioStatus_FolioStatus_PENDING_CLOSURE

				a.paymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(&phPb.GetPaymentDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				a.etaProcessor.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)

			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: 0},
			},
		},
		{
			name: "create success - multiple folio withdrawal with amount pending Order folio closure constraint failure",
			args: args{
				ctx: ctx,
				req: req5,
			},
			setupMockCalls: func(a *dep) {
				order1 := &pb.Order{}
				_ = copier.Copy(order1, order)
				order1.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
				}
				order1.Units = 100
				order1.ClientOrderId = sampleOrder.ClientOrderId + "_0"
				order1.OrderSubType = pb.OrderSubType_SELL_FULL_REDEMPTION_WITH_FOLIO_CLOSURE

				order2 := &pb.Order{}
				_ = copier.Copy(order2, order)
				order2.Amount = &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        500,
				}
				order2.Units = 50
				order2.ClientOrderId = sampleOrder.ClientOrderId + "_1"
				order2.OrderSubType = pb.OrderSubType_SELL_FULL_REDEMPTION_WITH_FOLIO_CLOSURE

				folio2 := &mfPb.FolioLedger{}
				_ = copier.Copy(folio2, sampleFolio)
				folio2.FolioId = "14680"
				folio2.BalanceUnits = 0

				folio3 := &mfPb.FolioLedger{}
				_ = copier.Copy(folio3, sampleFolio)
				folio3.FolioId = "15681"
				order2.FolioId = "15681"
				folio3.BalanceUnits = 50

				sampleFund2 := &mfPb.MutualFund{}
				_ = copier.Copy(sampleFund2, sampleFund)
				sampleFund2.Nav = &moneyPb.Money{CurrencyCode: "INR", Units: 10}
				sampleFund2.TxnConstraints.RedMnAmt = &moneyPb.Money{CurrencyCode: "INR", Units: 1000}

				a.mockMutualFundDao.EXPECT().GetById(gomock.Any(), order.MutualFundId).Times(2).Return(sampleFund2, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order1, nil)
				a.mockOrderDao.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(order2, nil)
				a.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), false,
					gomock.Any(), gomock.Any()).Times(1).Return([]*mfPb.FolioLedger{sampleFolio, folio2, folio3}, nil)
				gomock.InOrder(
					a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
						gomock.Any()).Return([]*pb.Order{}, nil).Times(1),
					a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
						gomock.Any(), gomock.Any()).Return([]*pb.Order{}, nil).Times(1),
					a.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
						gomock.Any(), gomock.Any(), gomock.Any()).Return([]*pb.Order{order}, nil).AnyTimes(),
				)
				a.mockAMCDao.EXPECT().GetByAmc(gomock.Any(), order.Amc).Return(sampleAmc, nil)
				a.mockSavingsClient.EXPECT().IsTxnAllowed(gomock.Any(), gomock.Any()).Return(&savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()}, nil)

				sampleFolioCopy := &mfPb.FolioLedger{}
				_ = copier.Copy(sampleFolioCopy, sampleFolio)
				sampleFolioCopy.Status = mfPb.FolioStatus_FolioStatus_PENDING_CLOSURE

				folio3Copy := &mfPb.FolioLedger{}
				_ = copier.Copy(folio3Copy, folio3)
				folio3Copy.Status = mfPb.FolioStatus_FolioStatus_PENDING_CLOSURE

				a.mockOrderDao.EXPECT().Update(gomock.Any(), &pb.Order{Id: order1.Id, OrderStatus: pb.OrderStatus_FAILURE,
					FailureReason: pb.FailureReason_REDEMPTION_FAILURE_FOLIO_CLOSURE_ORDER_PENDING_ORDERS_PRESENT},
					[]pb.OrderFieldMask{pb.OrderFieldMask_ORDER_STATUS, pb.OrderFieldMask_FAILURE_REASON}).Times(1)
				a.mockOrderDao.EXPECT().Update(gomock.Any(), &pb.Order{Id: order2.Id, OrderStatus: pb.OrderStatus_FAILURE,
					FailureReason: pb.FailureReason_REDEMPTION_FAILURE_FOLIO_CLOSURE_ORDER_PENDING_ORDERS_PRESENT},
					[]pb.OrderFieldMask{pb.OrderFieldMask_ORDER_STATUS, pb.OrderFieldMask_FAILURE_REASON}).Times(1)

				a.mockOrderStatusDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&pb.OrderStatusUpdateRecord{}, nil).Times(2)
				a.paymentHandlerClient.EXPECT().GetPaymentDetails(gomock.Any(), gomock.Any()).Return(&phPb.GetPaymentDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				a.etaProcessor.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)
			},
			wantErr: false,
			want: &pb.CreateOrderResponse{
				Status: &rpc.Status{Code: 0},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr = gomock.NewController(t)
			mockOrderDao := daoMocks.NewMockOrderDao(ctr)
			mockOrderStatusDao := daoMocks.NewMockOrderStatusUpdateDao(ctr)
			mockAMCDao := daoMocks.NewMockAmcInfoDao(ctr)
			mockFolioDao := daoMocks.NewMockFolioLedgerDao(ctr)
			mockMutualFundDao := daoMocks.NewMockMutualFundDao(ctr)
			mockSavingsClient := mockSavings.NewMockSavingsClient(ctr)
			mockPanClient := mocks.NewMockPanClient(ctr)
			etaProcessor := queueMock.NewMockDelayPublisher(ctr)
			paymentHandlerClient := mockPh.NewMockPaymentHandlerClient(ctr)
			orderStatusNotifier := &updOrder.OrderStatusNotifierImpl{}
			dep := &dep{
				mockOrderDao:         mockOrderDao,
				mockOrderStatusDao:   mockOrderStatusDao,
				mockAMCDao:           mockAMCDao,
				mockFolioDao:         mockFolioDao,
				mockMutualFundDao:    mockMutualFundDao,
				mockSavingsClient:    mockSavingsClient,
				mockPanClient:        mockPanClient,
				etaProcessor:         etaProcessor,
				paymentHandlerClient: paymentHandlerClient,
			}
			orderMgrSvc := &Service{
				orderDao:                   mockOrderDao,
				folioDao:                   mockFolioDao,
				mutualFundDao:              mockMutualFundDao,
				amcInfoDao:                 mockAMCDao,
				lockinTypeProcessorFactory: ltpFactory,
				idempotentTxnExecutor:      txnExecutor,
				cfg:                        svcTS.conf,
				savingsClient:              mockSavingsClient,
				sellOrderProcessor:         order_type_processor.NewSellOrderProcessor(ltpFactory, txnExecutor, mockFolioDao, mockOrderDao, conf, mockOrderStatusDao, mockSavingsClient, orderStatusNotifier, validator.NewPanAadhaarLinkValidator(svcTS.conf, mockPanClient), nil, nil, nil),
				orderETADelayPublisher:     etaProcessor,
				paymentHandlerClient:       paymentHandlerClient,
			}
			tt.setupMockCalls(dep)
			got, err := orderMgrSvc.CreateOrder(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateSellOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("CreateSellOrder() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestService_UpdateOrders(t *testing.T) {
	ctr := gomock.NewController(t)
	mockOrderDao := daoMocks.NewMockOrderDao(ctr)

	orderMgrSvc := &Service{
		orderDao:              mockOrderDao,
		idempotentTxnExecutor: txnExecutor,
	}

	// successful created order
	orderResp := &pb.Order{}
	_ = copier.Copy(orderResp, sampleOrder)
	orderResp.Id = sampleOrderId
	orderResp.OrderStatus = pb.OrderStatus_SETTLED
	orderResp.VendorOrderId = "12333444"

	orderResp2 := &pb.Order{}
	_ = copier.Copy(orderResp2, sampleOrder)
	orderResp2.Id = "MF0987654321"
	orderResp2.OrderStatus = pb.OrderStatus_SETTLED
	orderResp2.VendorOrderId = "123221"

	orderResp3 := &pb.Order{}
	_ = copier.Copy(orderResp3, sampleOrder)
	orderResp3.Id = "MFO3"
	orderResp3.OrderStatus = pb.OrderStatus_SETTLED
	orderResp3.VendorOrderId = "12221"

	type args struct {
		ctx context.Context
		req *pb.ProcessOrderConfirmationFromVendorRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *pb.ProcessOrderConfirmationFromVendorResponse
		wantErr        bool
	}{
		{
			name: "update success",
			args: args{
				ctx: ctx,
				req: &pb.ProcessOrderConfirmationFromVendorRequest{
					OrderConfirmationDetails: []*pb.OrderConfirmationDetail{
						{
							OrderId:        orderResp.GetId(),
							Amount:         orderResp.GetAmount(),
							Nav:            orderResp.GetNav(),
							UnitsAllocated: orderResp.GetUnits(),
							FolioId:        orderResp.GetFolioId(),
							VendorOrderId:  orderResp.GetVendorOrderId(),
						},
						{
							OrderId:        orderResp2.GetId(),
							Amount:         orderResp2.GetAmount(),
							Nav:            orderResp2.GetNav(),
							UnitsAllocated: orderResp2.GetUnits(),
							FolioId:        orderResp2.GetFolioId(),
							VendorOrderId:  orderResp2.GetVendorOrderId(),
						},
					},
					ClientRequestId: "updateSuccess",
				},
			},
			setupMockCalls: func() {
				mockOrderDao.EXPECT().GetByVendorOrderId(gomock.Any(), orderResp.VendorOrderId).Times(1).Return(orderResp, nil)
				mockOrderDao.EXPECT().GetByVendorOrderId(gomock.Any(), orderResp2.VendorOrderId).Times(1).Return(orderResp2, nil)
			},
			wantErr: false,
			want: &pb.ProcessOrderConfirmationFromVendorResponse{
				Status: &rpc.Status{
					Code: uint32(pb.ProcessCreditMISReportConfirmationResponse_OK),
				},
				UpdateResults: map[string]*pb.OrderUpdateResult{
					orderResp.GetVendorOrderId(): {
						VendorOrderId: orderResp.GetVendorOrderId(),
						UpdateStatus:  pb.OrderUpdateResult_SUCCESS,
					},
					orderResp2.GetVendorOrderId(): {
						VendorOrderId: orderResp2.GetVendorOrderId(),
						UpdateStatus:  pb.OrderUpdateResult_SUCCESS,
					},
				},
			},
		},
		{
			name: "update fail",
			args: args{
				ctx: ctx,
				req: &pb.ProcessOrderConfirmationFromVendorRequest{
					OrderConfirmationDetails: []*pb.OrderConfirmationDetail{
						{
							OrderId:        "",
							Amount:         orderResp2.GetAmount(),
							Nav:            orderResp2.GetNav(),
							UnitsAllocated: orderResp2.GetUnits(),
							FolioId:        orderResp2.GetFolioId(),
							VendorOrderId:  "",
						},
						{
							OrderId:        orderResp3.GetId(),
							Amount:         orderResp3.GetAmount(),
							Nav:            orderResp3.GetNav(),
							UnitsAllocated: orderResp3.GetUnits(),
							FolioId:        orderResp3.GetFolioId(),
							VendorOrderId:  orderResp3.GetVendorOrderId(),
						},
					},
					ClientRequestId: "updateFail",
				},
			},
			setupMockCalls: func() {
				mockOrderDao.EXPECT().GetByVendorOrderId(gomock.Any(), "").Times(1).Return(nil, epifierrors.ErrRecordNotFound)
				mockOrderDao.EXPECT().GetByVendorOrderId(gomock.Any(), orderResp3.VendorOrderId).Times(1).Return(orderResp3, nil)
			},
			wantErr: false,
			want: &pb.ProcessOrderConfirmationFromVendorResponse{
				Status: &rpc.Status{
					Code: uint32(pb.ProcessCreditMISReportConfirmationResponse_OK),
				},
				UpdateResults: map[string]*pb.OrderUpdateResult{
					"": {
						VendorOrderId: "",
						UpdateStatus:  pb.OrderUpdateResult_INVALID_ORDER_ID,
					},
					orderResp3.GetVendorOrderId(): {
						VendorOrderId: orderResp3.GetVendorOrderId(),
						UpdateStatus:  pb.OrderUpdateResult_SUCCESS,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			tt.setupMockCalls()
			got, err := orderMgrSvc.ProcessOrderConfirmationFromVendor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateOrders() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("UpdateOrders() got = %v,\n want %v diff %v", got, tt.want, diff)
			}
		})
	}
}

func TestService_GetOrder(t *testing.T) {
	ctr := gomock.NewController(t)
	mockOrderDao := daoMocks.NewMockOrderDao(ctr)
	mockAMCDao := daoMocks.NewMockAmcInfoDao(ctr)
	mockFolioDao := daoMocks.NewMockFolioLedgerDao(ctr)
	mutualFundDao := daoMocks.NewMockMutualFundDao(ctr)

	ltpFactory := ltp.NewLockInProcessorFactory(ltp.NewHardLockinProcessor(wec.NewHardLockInCalculator()),
		ltp.NewSoftLockinProcessor(wec.NewNoLockInCalculator()), ltp.NewNoLockinProcessor(wec.NewNoLockInCalculator()))
	orderMgrSvc := &Service{
		orderDao:                   mockOrderDao,
		folioDao:                   mockFolioDao,
		mutualFundDao:              mutualFundDao,
		amcInfoDao:                 mockAMCDao,
		lockinTypeProcessorFactory: ltpFactory,
		idempotentTxnExecutor:      txnExecutor,
	}

	type args struct {
		ctx context.Context
		req *pb.GetOrderRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *pb.GetOrderResponse
		wantErr        bool
	}{
		{
			name: "getbyOrderId success",
			args: args{
				ctx: context.Background(),
				req: &pb.GetOrderRequest{Id: &pb.GetOrderRequest_OrderId{OrderId: "success-order-id"}},
			},
			setupMockCalls: func() {
				mockOrderDao.EXPECT().GetById(gomock.Any(), "success-order-id").Return(sampleOrder, nil)
			},
			want: &pb.GetOrderResponse{
				Status: rpc.StatusOk(),
				Order:  sampleOrder,
			},
			wantErr: false,
		},
		{
			name: "getbyOrderId DB failure",
			args: args{
				ctx: context.Background(),
				req: &pb.GetOrderRequest{Id: &pb.GetOrderRequest_OrderId{OrderId: "failure-order-id"}},
			},
			setupMockCalls: func() {
				mockOrderDao.EXPECT().GetById(gomock.Any(), "failure-order-id").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &pb.GetOrderResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg(epifierrors.ErrRecordNotFound.Error()),
				Order:  nil,
			},
			wantErr: false,
		},
		{
			name: "getbyClientOrderId success",
			args: args{
				ctx: context.Background(),
				req: &pb.GetOrderRequest{Id: &pb.GetOrderRequest_ClientOrderId{ClientOrderId: "success-client-order-id"}},
			},
			setupMockCalls: func() {
				mockOrderDao.EXPECT().GetByClientOrderId(gomock.Any(), "success-client-order-id").Return(sampleOrder, nil)
			},
			want: &pb.GetOrderResponse{
				Status: rpc.StatusOk(),
				Order:  sampleOrder,
			},
			wantErr: false,
		},
		{
			name: "getbyClientOrderId DB failure",
			args: args{
				ctx: context.Background(),
				req: &pb.GetOrderRequest{Id: &pb.GetOrderRequest_ClientOrderId{ClientOrderId: "failure-client-order-id"}},
			},
			setupMockCalls: func() {
				mockOrderDao.EXPECT().GetByClientOrderId(gomock.Any(), "failure-client-order-id").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &pb.GetOrderResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg(epifierrors.ErrRecordNotFound.Error()),
				Order:  nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			tt.setupMockCalls()
			got, err := orderMgrSvc.GetOrder(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetOrder() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestRemoveSpecialCharacters(t *testing.T) {
	tests := []struct {
		name string
		arg  string
		want string
	}{
		{
			name: "string with no special characters",
			arg:  "Karthik Kumar",
			want: "Karthik Kumar",
		},
		{
			name: "string with special characters",
			arg:  "Karthik *#!$^Kumar",
			want: "Karthik Kumar",
		},
		{
			name: "string with special characters in between spaces",
			arg:  "Karthik *#!$^ Kumar $@#!",
			want: "Karthik Kumar",
		},
		{
			name: "string with special characters everywhere",
			arg:  ".Karthik. .*#!$^ .Kumar. $@#!",
			want: "Karthik Kumar",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got := type_converter.RemoveSpecialCharacters(tt.arg)
			if strings.Compare(got, tt.want) != 0 {
				t.Errorf("GetOrder() got = %v,\n want %v", got, tt.want)
			}
		})
	}

}

func TestService_GetWithdrawalConstraints(t *testing.T) {
	ctr := gomock.NewController(t)

	req := &pb.GetWithdrawalConstraintsRequest{
		MfId:    "1234",
		ActorId: "1235",
	}

	sellOrder := &pb.Order{
		ActorId:       sampleOrder.ActorId,
		MutualFundId:  sampleOrder.MutualFundId,
		Amount:        &moneyPb.Money{CurrencyCode: "INR", Units: 100},
		Units:         10,
		OrderType:     pb.OrderType_SELL,
		OrderStatus:   pb.OrderStatus_CREATED,
		Client:        sampleOrder.Client,
		ClientOrderId: sampleOrder.ClientOrderId + "_0",
		Rta:           sampleOrder.Rta,
		PaymentMode:   sampleOrder.PaymentMode,
		Amc:           mfPb.Amc_ICICI_PRUDENTIAL,
		FolioId:       "123456",
	}

	folio2 := &mfPb.FolioLedger{}
	_ = copier.Copy(folio2, sampleFolio)
	folio2.FolioId = "15681"

	type args struct {
		ctx context.Context
		req *pb.GetWithdrawalConstraintsRequest
	}

	tests := []struct {
		name           string
		args           args
		setupMockCalls func(d *dep)
		want           *pb.GetWithdrawalConstraintsResponse
		wantErr        bool
	}{
		{
			name: "withdrawal constraints for actor with no pending orders",
			args: args{
				ctx: ctx,
				req: req,
			},
			setupMockCalls: func(d *dep) {
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), req.MfId).Return(sampleFund, nil)
				d.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(),
					gomock.Any()).Return([]*pb.Order{}, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_CREATED_AT, false,
					gomock.Any(), gomock.Any()).Return([]*mfPb.FolioLedger{sampleFolio, folio2}, nil)
			},
			wantErr: false,
			want: &pb.GetWithdrawalConstraintsResponse{
				Status:                      &rpc.Status{Code: uint32(pb.GetWithdrawalConstraintsResponse_OK)},
				TotalAmount:                 &moneyPb.Money{CurrencyCode: "INR", Units: 2000},
				MinimumWithdrawalAmount:     sampleFund.TxnConstraints.RedMnAmt,
				MaximumWithdrawalAmount:     sampleFund.TxnConstraints.RedMxAmt,
				IncrementalWithdrawalAmount: sampleFund.TxnConstraints.RedIncr,
				AmountsLike:                 nil,
				AssetClass:                  sampleFund.AssetClass,
				WithdrawableAmount:          &moneyPb.Money{CurrencyCode: "INR", Units: 2000},
			},
		},
		{
			name: "withdrawal constraints for actor with pending orders",
			args: args{
				ctx: ctx,
				req: req,
			},
			setupMockCalls: func(d *dep) {
				d.mockMutualFundDao.EXPECT().GetById(gomock.Any(), req.MfId).Return(sampleFund, nil)
				d.mockOrderDao.EXPECT().GetNonTerminalOrdersByFilterOptions(gomock.Any(), gomock.Any(),
					gomock.Any(),
					gomock.Any()).Return([]*pb.Order{sellOrder, sellOrder}, nil)
				d.mockFolioDao.EXPECT().GetByFilterOptions(gomock.Any(), mfPb.FolioLedgerMask_CREATED_AT, false,
					gomock.Any(), gomock.Any()).Return([]*mfPb.FolioLedger{sampleFolio, folio2}, nil)
			},
			wantErr: false,
			want: &pb.GetWithdrawalConstraintsResponse{
				Status:                      &rpc.Status{Code: uint32(pb.GetWithdrawalConstraintsResponse_OK)},
				TotalAmount:                 &moneyPb.Money{CurrencyCode: "INR", Units: 2000},
				MinimumWithdrawalAmount:     sampleFund.TxnConstraints.RedMnAmt,
				MaximumWithdrawalAmount:     sampleFund.TxnConstraints.RedMxAmt,
				IncrementalWithdrawalAmount: sampleFund.TxnConstraints.RedIncr,
				AmountsLike:                 nil,
				AssetClass:                  sampleFund.AssetClass,
				WithdrawableAmount:          &moneyPb.Money{CurrencyCode: "INR", Units: 1800},
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			orderMgrSvc, d := setupService(ctr)
			tt.setupMockCalls(d)
			got, err := orderMgrSvc.GetWithdrawalConstraints(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(tt.want)
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Update() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestService_OrderUpdates(t *testing.T) {

	ctr := gomock.NewController(t)
	mockOrderDao := daoMocks.NewMockOrderDao(ctr)
	mockOrderStatusDao := daoMocks.NewMockOrderStatusUpdateDao(ctr)

	orderMgrSvc := &Service{
		orderDao:              mockOrderDao,
		orderStatusDao:        mockOrderStatusDao,
		idempotentTxnExecutor: txnExecutor,
		orderStatusNotifier:   &updOrder.OrderStatusNotifierImpl{},
	}

	orderResp := &pb.Order{}
	_ = copier.Copy(orderResp, sampleOrder)
	orderResp.Id = sampleOrderId
	orderResp.OrderStatus = pb.OrderStatus_CONFIRMED_BY_RTA
	orderResp.VendorOrderId = "12333444"

	type args struct {
		ctx context.Context
		req *pb.UpdateOrdersRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *pb.UpdateOrdersResponse
		wantErr        bool
	}{
		{
			name: "update success",
			args: args{
				ctx: ctx,
				req: &pb.UpdateOrdersRequest{OrderUpdateDetails: []*pb.OrderUpdateDetail{
					{Order: orderResp, Masks: []pb.OrderFieldMask{pb.OrderFieldMask_ORDER_STATUS, pb.OrderFieldMask_RTA_CONFIRMED_AMOUNT, pb.OrderFieldMask_RTA_CONFIRMED_UNITS}},
				}},
			},
			setupMockCalls: func() {
				mockOrderDao.EXPECT().Update(gomock.Any(), orderResp, gomock.Any()).Return(orderResp, nil)
				mockOrderStatusDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&pb.OrderStatusUpdateRecord{}, nil)
			},
			wantErr: false,
			want: &pb.UpdateOrdersResponse{
				Status: rpc.StatusOk(),
				UpdateResults: map[string]*pb.OrderUpdateResult{
					"MFO12345567889": {UpdateStatus: pb.OrderUpdateResult_SUCCESS},
				},
			},
		},
		{
			name: "update failed invalid mask",
			args: args{
				ctx: ctx,
				req: &pb.UpdateOrdersRequest{OrderUpdateDetails: []*pb.OrderUpdateDetail{
					{Order: orderResp, Masks: []pb.OrderFieldMask{pb.OrderFieldMask_UNITS_ALLOCATED, pb.OrderFieldMask_RTA_CONFIRMED_AMOUNT, pb.OrderFieldMask_RTA_CONFIRMED_UNITS}},
				}},
			},
			setupMockCalls: func() {},
			wantErr:        false,
			want: &pb.UpdateOrdersResponse{
				Status: rpc.StatusOk(),
				UpdateResults: map[string]*pb.OrderUpdateResult{
					"MFO12345567889": {UpdateStatus: pb.OrderUpdateResult_FAILURE},
				},
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			tt.setupMockCalls()
			got, err := orderMgrSvc.UpdateOrders(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateOrders() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("UpdateOrders() got = %v,\n want %v diff %v", got, tt.want, diff)
			}
		})
	}

}

func TestService_HandleFitttSubscriptionUpdate(t *testing.T) {
	ctr := gomock.NewController(t)
	mockSipDao := daoMocks.NewMockSIPLedgerDao(ctr)
	orderMgrSvc := &Service{
		sipLedgerDao:          mockSipDao,
		idempotentTxnExecutor: txnExecutor,
		orderStatusNotifier:   &updOrder.OrderStatusNotifierImpl{},
	}

	type args struct {
		ctx context.Context
		req *pb.HandleFitttSubscriptionUpdateRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *pb.HandleFitttSubscriptionUpdateResponse
		wantErr        bool
	}{
		{
			name: "update success",
			args: args{
				ctx: ctx,
				req: &pb.HandleFitttSubscriptionUpdateRequest{
					OrderSubType:        pb.OrderSubType_BUY_SIP,
					FitttSubscriptionId: "testFittSub",
				},
			},
			setupMockCalls: func() {
				mockSipDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]*mfPb.SIPLedger{
						{
							SipRegistrationNumber: "testSip",
							FitSubscriptionId:     "testFitttSub",
							SipStatus:             mfPb.SIPStatus_SIP_STATUS_ACTIVE},
					}, nil)
				mockSipDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			},
			wantErr: false,
			want: &pb.HandleFitttSubscriptionUpdateResponse{
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "get sip ledger, no sip exists for fittt subscription",
			args: args{
				ctx: ctx,
				req: &pb.HandleFitttSubscriptionUpdateRequest{
					OrderSubType:        pb.OrderSubType_BUY_SIP,
					FitttSubscriptionId: "testFittSub",
				},
			},
			setupMockCalls: func() {
				mockSipDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr: false,
			want: &pb.HandleFitttSubscriptionUpdateResponse{
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "get sip ledger, update error",
			args: args{
				ctx: ctx,
				req: &pb.HandleFitttSubscriptionUpdateRequest{
					OrderSubType:        pb.OrderSubType_BUY_SIP,
					FitttSubscriptionId: "testFittSub",
				},
			},
			setupMockCalls: func() {
				mockSipDao.EXPECT().GetByFilterOptions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]*mfPb.SIPLedger{
						{
							SipRegistrationNumber: "testSip",
							FitSubscriptionId:     "testFitttSub",
							SipStatus:             mfPb.SIPStatus_SIP_STATUS_ACTIVE},
					}, nil)
				mockSipDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("update error"))
			},
			wantErr: false,
			want: &pb.HandleFitttSubscriptionUpdateResponse{
				Status: rpc.StatusInternal(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := orderMgrSvc.HandleFitttSubscriptionUpdate(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("HandleFitttSubscriptionUpdate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("HandleFitttSubscriptionUpdate() got = %v,\n want %v diff %v", got, tt.want, diff)
			}
		})
	}
}
