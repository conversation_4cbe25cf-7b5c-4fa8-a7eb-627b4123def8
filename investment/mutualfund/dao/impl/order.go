package impl

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/wire"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	pb "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/gamma/investment/mutualfund/dao/model"
)

const (
	COLLISSION_RETRIES           = 3
	EXTERNAL_ORDER_ID_INDEX_NAME = "mf_orders_external_order_id_unique_idx"
	VENDOR_ORDER_ID_INDEX_NAME   = "mf_orders_vendor_order_id_unique_idx"
)

var (
	orderFieldMaskToColumn = map[pb.OrderFieldMask]string{
		pb.OrderFieldMask_FOLIO_ID:                     "folio_id",
		pb.OrderFieldMask_NAV:                          "nav",
		pb.OrderFieldMask_ORDER_STATUS:                 "order_status",
		pb.OrderFieldMask_PAYMENT_STATUS:               "payment_status",
		pb.OrderFieldMask_UNITS_ALLOCATED:              "units",
		pb.OrderFieldMask_FAILURE_REASON:               "failure_reason",
		pb.OrderFieldMask_OMS_ORDERS:                   "oms_orders",
		pb.OrderFieldMask_Order_CONFIRMATION_META_DATA: "order_confirmation_meta_data",
		pb.OrderFieldMask_RTA_CONFIRMED_AMOUNT:         "rta_confirmed_amount",
		pb.OrderFieldMask_RTA_CONFIRMED_UNITS:          "rta_confirmed_units",
		pb.OrderFieldMask_VENDOR_GENERATED_REF_NUMBER:  "vendor_generated_ref_number",
		pb.OrderFieldMask_FAILURE_DEBUG_REASON:         "failure_debug_reason",
	}
	maskUnspecifiedFieldFilter = func(field pb.OrderFieldMask) bool {
		return pb.OrderFieldMask_ORDER_FIELD_MASK_UNSPECIFIED != field
	}
	orderFieldFilterToColumn = map[pb.OrderFieldFilter]string{
		pb.OrderFieldFilter_ORDER_FIELD_FILTER_AMC:          "amc",
		pb.OrderFieldFilter_ORDER_FIELD_FILTER_RTA:          "rta",
		pb.OrderFieldFilter_ORDER_FIELD_FILTER_ORDER_STATUS: "order_status",
	}
	terminalOrderStatus = []pb.OrderStatus{
		pb.OrderStatus_FAILURE,
		pb.OrderStatus_EXPIRED,
		pb.OrderStatus_IN_SETTLEMENT,
		pb.OrderStatus_SETTLED,
		pb.OrderStatus_CONFIRMED_BY_RTA,
	}
	// Statuses for which order shouldn't be displayed to the user
	dormantOrderStatus = []pb.OrderStatus{
		pb.OrderStatus_CREATION_ON_HOLD,
	}
	randomNumberGenerator       = idgen.RandSeqDigitsWithoutLeadingZeroes
	randomAlphaNumericGenerator = idgen.RandAlphaNumericString

	CollisionError = errors.New("Collision  error after all retries")
)

const (
	NON_TERMINAL_STATUS_QUERY = "order_status NOT IN (?)"
	ORDER_EXTERNAL_ID_LENGTH  = 12
	// ToDo(Junaid): Vendor Order ID length is set to 10 for testing only. Reach out to cams to know the length in prod and update accordingly
	ORDER_VENDOR_ID_LENGTH = 10
)

type OrderCrdb struct {
	db    *gormv2.DB
	idGen idgen.IdGenerator
}

func NewOrderCrdb(db *gormv2.DB, idGen idgen.IdGenerator) *OrderCrdb {
	return &OrderCrdb{db: db, idGen: idGen}
}

var OrderWireSet = wire.NewSet(NewOrderCrdb, wire.Bind(new(dao.OrderDao), new(*OrderCrdb)))

// nolint:dupl
func (d *OrderCrdb) Create(ctx context.Context, order *pb.Order) (*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "Create", time.Now())
	var err error
	var id string
	var orderModel *model.Order

	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	id, err = d.idGen.Get(idgen.MutualFundOrders)

	if err != nil {
		return nil, fmt.Errorf("model id generation failed: %w", err)
	}
	orderModel, err = model.NewOrder(order)

	if err != nil {
		return nil, err
	}
	orderModel.ID = id

	inputVendorOrderID := order.VendorOrderId
	for i := 0; i < COLLISSION_RETRIES; i++ {
		orderModel.ExternalOrderId = randomAlphaNumericGenerator(ORDER_EXTERNAL_ID_LENGTH)
		// If there is an input vendor order id, then we don't generate a new vendor order id.
		if len(inputVendorOrderID) == 0 {
			orderModel.VendorOrderId = randomNumberGenerator(ORDER_VENDOR_ID_LENGTH)
		}

		if err = db.Create(orderModel).Error; err != nil {
			if storagev2.IsDuplicateRowError(err) {
				if len(inputVendorOrderID) == 0 {
					if strings.Contains(err.Error(), EXTERNAL_ORDER_ID_INDEX_NAME) || strings.Contains(err.Error(), VENDOR_ORDER_ID_INDEX_NAME) {
						continue
					} else {
						return nil, epifierrors.ErrDuplicateEntry
					}
				} else {
					// If input vendorOrderID is there, and unique constraint error happens on external_order_id column err, we need to retry
					if strings.Contains(err.Error(), EXTERNAL_ORDER_ID_INDEX_NAME) {
						continue
					}
					return nil, epifierrors.ErrDuplicateEntry
				}
			}
			return nil, err
		}

		logger.Debug(ctx, "investment order entry created!", zap.String(logger.ORDER_ID, orderModel.ID))
		return orderModel.ToProto(), nil
	}

	logger.Error(ctx, fmt.Sprintf("error in creating order after %d retries", COLLISSION_RETRIES), zap.Error(err),
		zap.String(logger.ACTOR_ID, order.ActorId), zap.String(logger.VENDOR_ORDER_ID, order.VendorOrderId),
		zap.String(logger.EXTERNAL_ORDER_ID, order.ExternalOrderId))

	return nil, CollisionError

}

// nolint:dupl
func (d *OrderCrdb) GetById(ctx context.Context, id string) (*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetById", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	orderModel := &model.Order{}
	if res := db.Where("id=?", id).First(orderModel); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for order by id: %s: %w", id, res.Error)
	}
	return orderModel.ToProto(), nil
}

// nolint:dupl
func (d *OrderCrdb) GetByExternalOrderId(ctx context.Context, id string) (*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetByExternalOrderId", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	orderModel := &model.Order{}
	if res := db.Where("external_order_id=?", id).First(orderModel); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for order by external_order_id: %s: %w", id, res.Error)
	}
	return orderModel.ToProto(), nil
}

// nolint:dupl
func (d *OrderCrdb) GetByVendorOrderId(ctx context.Context, id string) (*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetByVendorOrderId", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	orderModel := &model.Order{}
	if res := db.Where("vendor_order_id=?", id).First(orderModel); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for order by vendor_order_id: %s: %w", id, res.Error)
	}
	return orderModel.ToProto(), nil
}

func (d *OrderCrdb) GetNonTerminalOrdersByFilterOptions(ctx context.Context, filterOptions ...storagev2.FilterOption) ([]*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetNonTerminalOrdersByFilterOptions", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var orderMdls []*model.Order

	// Apply all provided filters and ignore dormant order status
	for _, opt := range filterOptions {
		db = opt.ApplyInGorm(db)
	}
	db = ignoreDormantOrderStatus(db)

	if err := db.Where(NON_TERMINAL_STATUS_QUERY, terminalOrderStatus).
		Find(&orderMdls).Error; err != nil {
		logger.Error(ctx, "Error when fetching non terminal list of orders", zap.Error(err))
		return nil, err
	}
	orders := make([]*pb.Order, 0)
	for _, order := range orderMdls {
		orders = append(orders, order.ToProto())
	}
	return orders, nil
}

// nolint:dupl
func (d *OrderCrdb) Update(ctx context.Context, order *pb.Order, updateMasks []pb.OrderFieldMask) (*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "Update", time.Now())
	if order.Id == "" {
		return nil, fmt.Errorf("id cannot be empty for the update operation")
	}
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	updateMasks = filterOrderFieldMaskSlice(updateMasks, maskUnspecifiedFieldFilter)
	if len(updateMasks) == 0 {
		return nil, fmt.Errorf("update mask can't be empty")
	}
	updatedColumns := getSelectColumnsForOrderUpdate(updateMasks)
	orderModel, err := model.NewOrder(order)
	if err != nil {
		return nil, fmt.Errorf("error in converting to model, %w", err)
	}
	resp := db.Model(orderModel).Select(updatedColumns).Updates(orderModel)
	if resp.Error != nil {
		return nil, fmt.Errorf("unable to update order model for id %s, %w", order.Id, resp.Error)
	}
	if resp.RowsAffected == 0 {
		return nil, epifierrors.ErrRowNotUpdated
	}
	return orderModel.ToProto(), nil
}

// nolint: dupl
func (d *OrderCrdb) GetByOrderIds(ctx context.Context, orderIds []string) ([]*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetByOrderIds", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var orderMdls []*model.Order
	if err := db.Where("id IN (?)", orderIds).
		Find(&orderMdls).Error; err != nil {
		logger.Error(ctx, "Error when fetching list of orders", zap.Error(err))
		return nil, err
	}
	var orderPbs []*pb.Order
	for _, order := range orderMdls {
		orderPbs = append(orderPbs, order.ToProto())
	}
	return orderPbs, nil
}

// nolint: dupl
func (d *OrderCrdb) GetByClientOrderIds(ctx context.Context, clientOrderIds []string) ([]*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetByClientOrderIds", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var orderMdls []*model.Order
	if res := db.Where("client_order_id IN (?)", clientOrderIds).Find(&orderMdls); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "Error when fetching list of orders", zap.Error(res.Error))
		return nil, res.Error
	}
	if len(orderMdls) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	var orderPbs []*pb.Order
	for _, order := range orderMdls {
		orderPbs = append(orderPbs, order.ToProto())
	}
	return orderPbs, nil
}

// nolint: dupl
func (d *OrderCrdb) GetByExternalOrderIds(ctx context.Context, externalOrderIds []string) ([]*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetByExternalOrderIds", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var orderMdls []*model.Order
	if res := db.Where("external_order_id IN (?)", externalOrderIds).Find(&orderMdls); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "Error when fetching list of orders", zap.Error(res.Error))
		return nil, res.Error
	}
	if len(orderMdls) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	var orderPbs []*pb.Order
	for _, order := range orderMdls {
		orderPbs = append(orderPbs, order.ToProto())
	}
	return orderPbs, nil
}

// nolint:dupl
func (d *OrderCrdb) GetByVendorOrderIds(ctx context.Context, vendorOrderIds []string) ([]*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetByVendorOrderIds", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var orderMdls []*model.Order
	if res := db.Where("vendor_order_id IN (?)", vendorOrderIds).
		Find(&orderMdls); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "Error when fetching list of orders", zap.Error(res.Error))
		return nil, res.Error
	}
	if len(orderMdls) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	var orderPbs []*pb.Order
	for _, order := range orderMdls {
		orderPbs = append(orderPbs, order.ToProto())
	}
	return orderPbs, nil
}

// nolint: dupl
func (d *OrderCrdb) GetByClientOrderId(ctx context.Context, clienOrderId string) (*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetByClientOrderId", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	orderModel := &model.Order{}
	if res := db.Where("client_order_id=?", clienOrderId).First(orderModel); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch record for order by client order id: %s: %w", clienOrderId, res.Error)
	}
	return orderModel.ToProto(), nil
}

func (d *OrderCrdb) UpdateWithStatusCheck(ctx context.Context, order *pb.Order, currentStatus pb.OrderStatus,
	updateMasks []pb.OrderFieldMask) error {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "UpdateWithStatusCheck", time.Now())

	if order.Id == "" {
		return fmt.Errorf("id cannot be empty for the update operation")
	}
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	updateMasks = filterOrderFieldMaskSlice(updateMasks, maskUnspecifiedFieldFilter)
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}
	updatedColumns := getSelectColumnsForOrderUpdate(updateMasks)

	orderModel, err := model.NewOrder(order)
	if err != nil {
		return err
	}
	resp := db.Model(&model.Order{}).Select(updatedColumns).
		Where("id = ? and order_status = ?", order.Id, currentStatus).
		Updates(orderModel)
	if resp.Error != nil {
		return fmt.Errorf("unable to update order status for id %s, %w", order.Id, resp.Error)
	}
	if resp.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}

func (d *OrderCrdb) GetOrdersByFilters(ctx context.Context, pageToken *pagination.PageToken, pageSize uint32,
	filters ...storagev2.FilterOption) ([]*pb.Order, *rpcPb.PageContextResponse, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetOrdersByFilters", time.Now())

	if pageSize < 1 {
		return nil, nil, errors.New("page size should be at least 1")
	}

	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	// Apply all provided filters and ignore dormant order status
	for _, opt := range filters {
		db = opt.ApplyInGorm(db)
	}
	db = ignoreDormantOrderStatus(db)

	db = d.getPaginatedQuery(db, pageSize, pageToken)

	var orderModels []*model.Order
	resp := db.Find(&orderModels)

	if resp.Error != nil {
		logger.Error(ctx, "Error when fetching list of orders", zap.Error(resp.Error))
		return nil, nil, resp.Error
	}

	if len(orderModels) == 0 {
		return nil, nil, epifierrors.ErrRecordNotFound
	}

	var pageCtxResp *rpcPb.PageContextResponse
	rows, res, err := pagination.NewPageCtxResp(pageToken, int(pageSize), model.OrderModelRows(orderModels))

	if err != nil {
		return nil, nil, err
	}

	orderModels = rows.(model.OrderModelRows)
	pageCtxResp = res

	var orderProtoList []*pb.Order
	for _, orderModel := range orderModels {
		orderProtoList = append(orderProtoList, orderModel.ToProto())
	}

	return orderProtoList, pageCtxResp, nil
}

func (d *OrderCrdb) GetOrderCountByMutualFundAndActorId(ctx context.Context, actorId string, mutualFundId string) (int32, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetOrderCountByMutualFundAndActorId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	// Ignore dormant order status
	db = ignoreDormantOrderStatus(db)

	var totalOrders int64
	resp := db.Model(&model.Order{}).Where("actor_id = ? and mutual_fund_id = ?", actorId, mutualFundId).Count(&totalOrders)
	if resp.Error != nil {
		return 0, fmt.Errorf("error while fetching total orders by mf_id: %w", resp.Error)
	}

	return int32(totalOrders), nil
}

func (d *OrderCrdb) GetOrderIdsByUpdatedTillAndFilters(ctx context.Context, updatedTill *timestamppb.Timestamp,
	filters map[pb.OrderFieldFilter]interface{}, limit int) ([]string, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetOrderIdsByUpdatedTillAndFilters", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	queryKey, queryValue, err := validateOrderFiltersAndGetQuery(updatedTill, filters)
	if err != nil {
		return nil, err
	}
	// Ignore dormant order status
	db = ignoreDormantOrderStatus(db)

	rows, err := db.Model(&model.Order{}).
		Limit(limit).
		Where(queryKey, queryValue...).
		Rows()
	if err != nil {
		return nil, fmt.Errorf("error in GetOrderIdsByUpdatedTillAndFilters: %w", err)
	}
	defer func() {
		_ = rows.Close()
	}()
	var orderIds []string
	for rows.Next() {
		orderDetail := &model.Order{}
		err2 := db.ScanRows(rows, orderDetail)
		if err2 != nil {
			return nil, fmt.Errorf("error in scanning in GetOrderIdsByUpdatedTillAndFilters: %w", err)
		}
		orderIds = append(orderIds, orderDetail.ID)
	}
	return orderIds, nil
}

func (d *OrderCrdb) GetOrdersByCreatedAt(ctx context.Context, startTime time.Time, endTime time.Time,
	pageToken *pagination.PageToken, pageSize uint32, filterOptions ...storagev2.FilterOption) ([]*pb.Order, *rpcPb.PageContextResponse, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetOrdersByCreatedAt", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	for _, opt := range filterOptions {
		db = opt.ApplyInGorm(db)
	}

	db = d.getPaginatedQuery(db, pageSize, pageToken)

	var orderModels []*model.Order
	resp := db.Where("created_at >= ? and created_at <= ?", startTime, endTime).Find(&orderModels)
	if resp.Error != nil {
		logger.Error(ctx, "Error when fetching list of orders", zap.Error(resp.Error))
		return nil, nil, resp.Error
	}

	if len(orderModels) == 0 {
		return nil, nil, epifierrors.ErrRecordNotFound
	}

	var pageCtxResp *rpcPb.PageContextResponse
	rows, res, err := pagination.NewPageCtxResp(pageToken, int(pageSize), model.OrderModelRows(orderModels))

	if err != nil {
		return nil, nil, err
	}

	orderModels = rows.(model.OrderModelRows)
	pageCtxResp = res

	var orderProtoList []*pb.Order
	for _, orderModel := range orderModels {
		orderProtoList = append(orderProtoList, orderModel.ToProto())
	}

	return orderProtoList, pageCtxResp, nil
}

func (d *OrderCrdb) GetInvestedMfIdsByActorIdAndMfIds(ctx context.Context, actorId string, mfIds []string) ([]string, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetInvestedMfIdsByActorIdAndMfIds", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	// Ignore dormant order status
	db = ignoreDormantOrderStatus(db)

	var orderModels []*model.Order
	resp := db.Select("distinct(mutual_fund_id)").Where("actor_id = ? and mutual_fund_id in (?)", actorId, mfIds).Find(&orderModels)
	if resp.Error != nil {
		return nil, fmt.Errorf("error while fetching mutual fund IDs by actor_id: %w", resp.Error)
	}

	var investedMfIds []string
	for _, result := range orderModels {
		investedMfIds = append(investedMfIds, result.MutualFundId)
	}

	return investedMfIds, nil
}

func (d *OrderCrdb) getPaginatedQuery(db *gormv2.DB, pageSize uint32, pageToken *pagination.PageToken) *gormv2.DB {
	if pageToken != nil {
		if pageToken.Timestamp != nil {
			if pageToken.IsReverse {
				db = db.Where("created_at >= ?", pageToken.Timestamp.AsTime()).
					Order("created_at" + " ASC")
			} else {
				db = db.Where("created_at <= ?", pageToken.Timestamp.AsTime()).
					Order("created_at" + " DESC")
			}
		} else {
			if pageToken.IsReverse {
				db = db.Order("created_at" + " ASC")
			} else {
				db = db.Order("created_at" + " DESC")
			}
		}
		db = db.Offset(int(pageToken.Offset))
	} else {
		db = db.Order("created_at" + " DESC")
	}
	// fetch pageSize + 1 extra row to compute next page availability.
	db = db.Limit(int(pageSize + 1))
	return db
}

// filterOrderFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
// NOTE- func returns a new copy of the slice. No changes are made to the existing slice
func filterOrderFieldMaskSlice(fieldMasks []pb.OrderFieldMask,
	check func(field pb.OrderFieldMask) bool) []pb.OrderFieldMask {
	var ret []pb.OrderFieldMask
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			ret = append(ret, fieldMask)
		}
	}
	return ret
}

// getSelectColumnsForOrderUpdate converts update mask to string slice with column name
// corresponding to field name enums
func getSelectColumnsForOrderUpdate(updateMask []pb.OrderFieldMask) []string {
	var selectColumns []string

	for _, field := range updateMask {
		selectColumns = append(selectColumns, orderFieldMaskToColumn[field])
	}
	return selectColumns
}

func (d *OrderCrdb) UpdateBatch(ctx context.Context, orderIds []string, maskValueMap map[pb.OrderFieldMask]interface{}) error {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "UpdateBatch", time.Now())
	if len(orderIds) == 0 {
		return fmt.Errorf("need atleast one order for update operation")
	}
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var updateMasks = getMasksFromMap(maskValueMap)
	updateMasks = filterOrderFieldMaskSlice(updateMasks, maskUnspecifiedFieldFilter)
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}
	updatedColumns := getSelectColumnsForOrderUpdate(updateMasks)
	fieldValueMap := getFieldValueMapFromMaskValue(maskValueMap)

	resp := db.Model(model.Order{}).Where("id IN (?)", orderIds).Select(updatedColumns).Updates(fieldValueMap)
	if resp.Error != nil {
		return fmt.Errorf("unable to update orders %w", resp.Error)
	}
	return nil
}

func getMasksFromMap(maskValueMap map[pb.OrderFieldMask]interface{}) []pb.OrderFieldMask {
	var res = make([]pb.OrderFieldMask, len(maskValueMap))
	i := 0
	for key := range maskValueMap {
		res[i] = key
		i++
	}
	return res
}

func getFieldValueMapFromMaskValue(valueMap map[pb.OrderFieldMask]interface{}) map[string]interface{} {
	respMap := make(map[string]interface{})
	for key, val := range valueMap {
		if maskUnspecifiedFieldFilter(key) {
			respMap[orderFieldMaskToColumn[key]] = val
		}
	}
	return respMap
}

/*
Validates the filters passed and builds query with the filters
*/
func validateOrderFiltersAndGetQuery(updatedTill *timestamppb.Timestamp, filters map[pb.OrderFieldFilter]interface{}) (string, []interface{}, error) {
	var filterQueryKey []string
	var filterQueryValue []interface{}
	filterQueryKey = append(filterQueryKey, "updated_at <= ?")
	filterQueryValue = append(filterQueryValue, updatedTill.AsTime())

	for key, value := range filters {
		column, ok := orderFieldFilterToColumn[key]
		if ok {
			filterQueryKey = append(filterQueryKey, fmt.Sprintf("%s = ?", column))
			filterQueryValue = append(filterQueryValue, value)
		} else {
			return "", nil, fmt.Errorf("invalid filters")
		}
	}
	return strings.Join(filterQueryKey, " and "), filterQueryValue, nil
}

// ignoreDormantOrderStatus applies the filter necessary for ignoring dormant order status
func ignoreDormantOrderStatus(db *gormv2.DB) *gormv2.DB {
	return db.Where("mf_orders.order_status NOT IN (?)", dormantOrderStatus)
}

func (d *OrderCrdb) GetByFolioIDAndFilterOptions(ctx context.Context, folioId string, filterOptions ...storagev2.FilterOption) (map[string]*pb.Order, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "OrderCrdb", "GetByFolioIDAndFilterOptions", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var orderMdls []*model.Order

	for _, opt := range filterOptions {
		db = opt.ApplyInGorm(db)
	}

	if err := db.Where("folio_id = ?", folioId).Find(&orderMdls).Error; err != nil {
		logger.Error(ctx, "Error when fetching list of orders", zap.Error(err))
		return nil, err
	}

	orderPbs := make(map[string]*pb.Order)
	for _, order := range orderMdls {
		orderPbs[order.ID] = order.ToProto()
	}
	return orderPbs, nil
}
