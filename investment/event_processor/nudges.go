package event_processor

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"go.uber.org/zap"

	eventProcessorPb "github.com/epifi/gamma/api/investment/event_processor"
	nudgePb "github.com/epifi/gamma/api/nudge"
	"github.com/epifi/gamma/investment/config"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) performNudgeEntryAndUpdateDb(
	ctx context.Context,
	req *eventProcessorPb.SendUserJourneyNotificationsRequest,
	journey *eventProcessorPb.UserJourney,
) error {
	// 1. Get the nudge templates for journey state
	var nudgeTemplates []*config.EventBasedNudgeTemplate
	for _, journeyStateNotifications := range s.conf.EventBasedNotifications() {
		journeyStateInConf := eventProcessorPb.JourneyState(eventProcessorPb.JourneyState_value[journeyStateNotifications.JourneyState])
		if journeyStateInConf == eventProcessorPb.JourneyState_JOURNEY_STATE_UNSPECIFIED {
			logger.Error(ctx, fmt.Sprintf("ignoring error casting journey state in conf: %s", journeyStateNotifications.JourneyState))
			continue
		}
		if journeyStateInConf == journey.GetLatestJourneyState() {
			nudgeTemplates = journeyStateNotifications.Nudges
			break
		}
	}
	// 2. Perform nudge entries
	activatedNudges, entryErr := s.performNudgeEntry(ctx, req, journey, nudgeTemplates)
	if entryErr != nil {
		return fmt.Errorf("error in nudge entry: %w", entryErr)
	}
	// 3. Add activated nudge info to journey object
	for _, nudge := range activatedNudges {
		journey.GetJourneyStateInfo().NudgeInfos = append(journey.GetJourneyStateInfo().GetNudgeInfos(), &eventProcessorPb.NudgeInfo{
			NudgeId:      nudge.nudgeId,
			NudgeStatus:  eventProcessorPb.NudgeStatus_NUDGE_STATUS_ACTIVE,
			JourneyState: journey.GetLatestJourneyState(),
			EntryEventId: nudge.entryEventId,
		})
	}
	// 4. Update journey state info in db
	updatedJourney := &eventProcessorPb.UserJourney{
		ActorId:          journey.ActorId,
		JourneyType:      journey.JourneyType,
		JourneyStateInfo: journey.JourneyStateInfo,
	}
	updateFieldMasks := []eventProcessorPb.UserJourneyFieldMask{eventProcessorPb.UserJourneyFieldMask_USER_JOURNEY_FIELD_MASK_JOURNEY_STATE_INFO}
	_, err := s.userJourneyDao.Update(ctx, updatedJourney, updateFieldMasks, req.GetOwnership())
	// in case of error, nudge will be activated but status will not be updated in the db
	// performing a nudge entry multiple times is ok since the nudge API is idempotent i.e., there is no effect in activating an already active nudge
	if err != nil {
		return fmt.Errorf("error in updating db: %w", err)
	}
	return nil
}

type activeNudgeInfo struct {
	nudgeId      string
	entryEventId string
}

func (s *Service) performNudgeEntry(
	ctx context.Context,
	req *eventProcessorPb.SendUserJourneyNotificationsRequest,
	journey *eventProcessorPb.UserJourney,
	nudgeTemplates []*config.EventBasedNudgeTemplate,
) ([]*activeNudgeInfo, error) {
	if journey.GetJourneyStateInfo() == nil {
		journey.JourneyStateInfo = &eventProcessorPb.JourneyStateInfo{}
	}
	var activatedNudges []*activeNudgeInfo
	for _, nudgeTemplate := range nudgeTemplates {
		isNudgeEntryPerformedAlready := false
		for _, nudgeInfo := range journey.GetJourneyStateInfo().GetNudgeInfos() {
			if nudgeInfo.GetJourneyState() == journey.GetLatestJourneyState() && nudgeInfo.NudgeId == nudgeTemplate.NudgeId {
				isNudgeEntryPerformedAlready = true
				break
			}
		}
		if isNudgeEntryPerformedAlready {
			logger.Info(ctx, "nudge entry already performed for journey state", zap.String("JOURNEY_STATE", journey.GetLatestJourneyState().String()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.NUDGE_ID, nudgeTemplate.NudgeId))
			continue
		}
		entryEventId := getEntryEventIdForUserJourney(journey)
		entryRes, entryErr := s.nudgeClient.PerformNudgeEntryForActor(ctx, &nudgePb.PerformNudgeEntryForActorRequest{
			ActorId: req.GetActorId(),
			NudgeId: nudgeTemplate.NudgeId,
			NudgeMetadata: &nudgePb.NudgeMetadata{
				TemplateData: nudgeTemplate.TemplateData,
			},
			DelayInSeconds: nudgeTemplate.DelayInSeconds,
			EntryEventId:   entryEventId,
		})
		if te := epifigrpc.RPCError(entryRes, entryErr); te != nil {
			logger.Error(ctx, "error performing entry for nudge", zap.Error(te), zap.String(logger.NUDGE_ID, nudgeTemplate.NudgeId))
			continue
		}
		activatedNudges = append(activatedNudges, &activeNudgeInfo{
			nudgeId:      nudgeTemplate.NudgeId,
			entryEventId: entryEventId,
		})
	}
	return activatedNudges, nil
}

func (s *Service) exitOldNudges(ctx context.Context, actorId string, storedJourney *eventProcessorPb.UserJourney) error {
	for _, nudge := range storedJourney.GetJourneyStateInfo().GetNudgeInfos() {
		if nudge.GetNudgeStatus() == eventProcessorPb.NudgeStatus_NUDGE_STATUS_EXITED {
			continue
		}
		exitEventId := uuid.NewString()
		exitRes, exitErr := s.nudgeClient.PerformNudgeExitForActor(ctx, &nudgePb.PerformNudgeExitForActorRequest{
			ActorId:      actorId,
			NudgeId:      nudge.GetNudgeId(),
			ExitEventId:  exitEventId,
			EntryEventId: nudge.GetEntryEventId(),
		})
		if te := epifigrpc.RPCError(exitRes, exitErr); te != nil {
			logger.Error(ctx, "error exiting nudge", zap.Error(te), zap.String(logger.NUDGE_ID, nudge.GetNudgeId()), zap.String(logger.EVENT_ID, exitEventId))
			continue
		}
		nudge.NudgeStatus = eventProcessorPb.NudgeStatus_NUDGE_STATUS_EXITED
	}
	return nil
}

func getEntryEventIdForUserJourney(storedJourney *eventProcessorPb.UserJourney) string {
	return fmt.Sprintf("JRNY_%s_%d_%d", storedJourney.GetActorId(), storedJourney.GetJourneyType(), storedJourney.GetLatestJourneyState())
}
