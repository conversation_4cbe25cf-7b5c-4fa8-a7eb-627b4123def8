package stageproc

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/inappreferral"
	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/user"
	userGrpPb "github.com/epifi/gamma/api/user/group"
	questsdk "github.com/epifi/gamma/quest/sdk"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	cpPb "github.com/epifi/gamma/api/card/provisioning"
	types "github.com/epifi/gamma/api/typesv2"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/feature/release"
	genConf "github.com/epifi/gamma/user/config/genconf"
)

type OrderPhysicalCardStage struct {
	cardProvisioningClient cpPb.CardProvisioningClient
	conf                   *genConf.Config
	releaseEvaluator       release.IEvaluator
	vkycClient             vkycPb.VKYCClient
	inAppReferralClient    inappreferral.InAppReferralClient
	usersClient            user.UsersClient
	userGroupClient        userGrpPb.GroupClient
	questSdkClient         *questsdk.Client
}

func NewOrderPhysicalCardStage(cardProvisioningClient cpPb.CardProvisioningClient, conf *genConf.Config, releaseEvaluator release.IEvaluator, vkycClient vkycPb.VKYCClient,
	inAppReferralClient inappreferral.InAppReferralClient, usersClient user.UsersClient, userGroupClient userGrpPb.GroupClient, questSdkClient *questsdk.Client) *OrderPhysicalCardStage {
	return &OrderPhysicalCardStage{
		cardProvisioningClient: cardProvisioningClient,
		conf:                   conf,
		releaseEvaluator:       releaseEvaluator,
		vkycClient:             vkycClient,
		inAppReferralClient:    inAppReferralClient,
		usersClient:            usersClient,
		userGroupClient:        userGroupClient,
		questSdkClient:         questSdkClient,
	}
}

var (
	physicalDispatchInProgressStatusCodes = []uint32{
		uint32(cpPb.GetPhysicalCardDispatchStatusResponse_IN_PROGRESS),
		uint32(cpPb.GetPhysicalCardDispatchStatusResponse_PAYMENT_IN_PROGRESS),
	}

	physicalCardDispatchSuccessStatusCodes = []uint32{
		rpc.StatusOk().GetCode(),
		uint32(cpPb.GetPhysicalCardDispatchStatusResponse_OK),
	}
)

func (s *OrderPhysicalCardStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	onb := req.GetOnb()
	actorId := onb.GetActorId()
	cardId := onb.GetCardInfo().GetCardDetails()[0].GetCardId()
	stage := onbPb.OnboardingStage_ORDER_PHYSICAL_CARD

	if getStageStatus(onb, stage).IsSuccessOrSkipped() {
		return nil, SkipStageError
	}
	if !s.isOnbOrderPhysicalCardStageEnabled(ctx, actorId) {
		return nil, SkipStageError
	}

	physicalCardDispatchStatus, err := s.cardProvisioningClient.GetPhysicalCardDispatchStatus(ctx, &cpPb.GetPhysicalCardDispatchStatusRequest{
		CardId: cardId,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in calling GetPhysicalCardDispatchStatus()", zap.Error(err))
		return nil, err

	case physicalCardDispatchStatus.GetStatus().IsInternal():
		logger.Error(ctx, "error in calling fetching physical dispatch status in onb", zap.Error(rpc.StatusAsError(physicalCardDispatchStatus.GetStatus())))
		return nil, rpc.StatusAsError(physicalCardDispatchStatus.GetStatus())

	case lo.Contains(physicalCardDispatchSuccessStatusCodes, physicalCardDispatchStatus.GetStatus().GetCode()):
		logger.Info(ctx, "physical dispatch already moved to success")
		return nil, NoActionError

	case lo.Contains(physicalDispatchInProgressStatusCodes, physicalCardDispatchStatus.GetStatus().GetCode()):
		logger.Info(ctx, "physical dispatch has been initiated", zap.String(logger.CARD_ID, cardId))
		return nil, NoActionError

	default:
	}

	// make physical card free for the user if eligible
	if err = s.makePhysicalCardFreeIfEligible(ctx, actorId, onb.GetUserId()); err != nil {
		return nil, err
	}

	fetchPhysicalCardChargesResp, err := s.cardProvisioningClient.FetchPhysicalCardChargesForUser(ctx, &cpPb.FetchPhysicalCardChargesForUserRequest{
		ActorId:               actorId,
		PostSuccessNextAction: deeplink.NewActionToGetNextAction(),
		OnSkipNextAction:      deeplink.NextActionToSkipOnbStageNextAction(stage),
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in calling FetchPhysicalCardChargesForUser() in onb", zap.Error(err))
		return nil, err
	case fetchPhysicalCardChargesResp.GetStatus().GetCode() == uint32(cpPb.FetchPhysicalCardChargesForUserResponse_VKYC_REJECTED),
		fetchPhysicalCardChargesResp.GetStatus().IsFailedPrecondition():
		return nil, SkipStageError
	case !fetchPhysicalCardChargesResp.GetStatus().IsSuccess():
		logger.Error(ctx, "non-success status while fetching physical card charges in onb", zap.Error(rpc.StatusAsError(fetchPhysicalCardChargesResp.GetStatus())))
		return nil, rpc.StatusAsError(fetchPhysicalCardChargesResp.GetStatus())
	}

	return &StageProcessorResponse{
		NextAction: fetchPhysicalCardChargesResp.GetNextAction(),
	}, nil
}

// isOnbOrderPhysicalCardStageEnabled checks whether the order physical card stage enabled or not during onboarding flow
func (s *OrderPhysicalCardStage) isOnbOrderPhysicalCardStageEnabled(ctx context.Context, actorId string) bool {
	commonConstraintsData := release.NewCommonConstraintData(types.Feature_FEATURE_ENABLE_ORDER_PHYSICAL_CARD_DURING_ONBOARDING).WithActorId(actorId)
	screenEnabled, err := s.releaseEvaluator.Evaluate(ctx, commonConstraintsData)
	if err != nil {
		logger.WarnWithCtx(ctx, "error evaluating AB variant for onboarding order physical card screen", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}
	return screenEnabled && s.conf.Onboarding().OrderPhysicalDebitCardConfig().EnableViaQuest(ctx)
}

// makePhysicalCardFreeIfEligible add the user to the free physical DC reward group if eligible
// Eligibility criteria:
// 1. User should have claimed a finite code of type REGULAR and channel IN_APP_REFERRAL
func (s *OrderPhysicalCardStage) makePhysicalCardFreeIfEligible(ctx context.Context, actorId, userId string) error {
	if !s.isFreePhysicalDCRefereeRewardEnabled(s.conf.Onboarding().OrderPhysicalDebitCardConfig().FreePhysicalDCRefereeRewardConfig()) {
		logger.Debug(ctx, "free physical DC referee reward is not enabled", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil
	}

	claimedFiniteCodeRes, err := s.inAppReferralClient.GetClaimedFiniteCodeForActor(ctx, &inappreferral.GetClaimedFiniteCodeForActorRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(claimedFiniteCodeRes, err); rpcErr != nil && !claimedFiniteCodeRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in calling GetClaimedFiniteCodeForActor rpc", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return rpcErr
	}
	// return if user has not onboarded via referral
	if claimedFiniteCodeRes.GetStatus().IsRecordNotFound() {
		logger.Info(ctx, "user has not onboarded via referral", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil
	}
	if claimedFiniteCodeRes.GetFiniteCode().GetChannel() != inAppReferralEnumPb.FiniteCodeChannel_IN_APP_REFERRAL ||
		claimedFiniteCodeRes.GetFiniteCode().GetType() != inAppReferralEnumPb.FiniteCodeType_REGULAR {
		return nil
	}
	referrerActorId := claimedFiniteCodeRes.GetFiniteCode().GetActorId()
	isReferrerPartOfFreeDCExpVariant, err := s.isReferrerPartOfFreeDCExpVariant(ctx, referrerActorId)
	if err != nil {
		return fmt.Errorf("error checking if referrer is part of free DC exp variant: %w", err)
	}
	if !isReferrerPartOfFreeDCExpVariant {
		logger.Info(ctx, "referrer is not part of free DC exp variant", zap.String("referrerActorId", referrerActorId))
		return nil
	}

	userRes, err := s.usersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: userId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		logger.Error(ctx, "error in calling GetUser rpc", zap.String(logger.USER_ID, userId), zap.Error(rpcErr))
		return rpcErr
	}

	groupMappingResponse, err := s.userGroupClient.AddEmailGroupMapping(ctx, &userGrpPb.AddEmailGroupMappingRequest{
		UserGroup: commontypes.UserGroup_FREE_PHYSICAL_DC_REWARD,
		Emails:    []string{userRes.GetUser().GetProfile().GetEmail()},
	})
	if rpcErr := epifigrpc.RPCError(groupMappingResponse, err); rpcErr != nil {
		logger.Error(ctx, "error in calling AddEmailGroupMapping rpc", zap.String(logger.USER_ID, userId), zap.Error(rpcErr))
		return rpcErr
	}
	logger.Info(ctx, "added user to FREE_PHYSICAL_DC_REWARD group", zap.String(logger.USER_ID, userId), zap.String(logger.ACTOR_ID_V2, actorId))

	return nil
}

func (s *OrderPhysicalCardStage) isReferrerPartOfFreeDCExpVariant(ctx context.Context, referrerActorId string) (bool, error) {
	freeDCRewardConf := s.conf.Onboarding().OrderPhysicalDebitCardConfig().FreePhysicalDCRefereeRewardConfig()
	noOverrideVal, val, err := s.questSdkClient.EvaluateForActor(ctx, referrerActorId, freeDCRewardConf.QuestExpVariablePath())
	if err != nil {
		logger.Error(ctx, "error in evaluating quest variable for actor", zap.String(logger.ACTOR_ID_V2, referrerActorId), zap.Error(err))
		return false, err
	}
	if noOverrideVal {
		logger.Debug(ctx, "quest variable has no override value", zap.String(logger.ACTOR_ID_V2, referrerActorId))
		return false, nil
	}
	value, ok := val.(string)
	if !ok {
		logger.Error(ctx, "quest variable value is not of type string", zap.String(logger.ACTOR_ID_V2, referrerActorId))
		return false, fmt.Errorf("quest variable value is not of type string")
	}
	if value != freeDCRewardConf.QuestExpVariableValue() {
		logger.Debug(ctx, "quest variable value is not VARIANT_1", zap.String(logger.ACTOR_ID_V2, referrerActorId), zap.String("value", value))
		return false, nil
	}

	logger.Debug(ctx, "referrer is part of free DC exp variant", zap.String("value", value), zap.String(logger.ACTOR_ID_V2, referrerActorId))
	return true, nil
}

func (s *OrderPhysicalCardStage) isFreePhysicalDCRefereeRewardEnabled(conf *genConf.FreePhysicalDCRefereeRewardConfig) bool {
	var (
		currTime = time.Now()
	)

	if !conf.IsEnabled() {
		return false
	}

	if currTime.Before(conf.ActiveFrom()) || currTime.After(conf.ActiveTill()) {
		return false
	}

	return true
}
