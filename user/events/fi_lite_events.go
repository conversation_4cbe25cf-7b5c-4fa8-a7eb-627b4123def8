// nolint
package events

import (
	"time"

	"github.com/epifi/be-common/pkg/events"

	userPb "github.com/epifi/gamma/api/user"

	"github.com/fatih/structs"
	"github.com/google/uuid"
)

const (
	EventLoadedSAIntroScreenV1Server   = "LoadedSAIntroScreenV1Server"
	EventLoadedIntentScreenServer      = "LoadedIntentScreenServer"
	EventSetOnboardingIntentServer     = "SetOnboardingIntentServer"
	EventSetOnboardingAutoIntentServer = "SetOnboardingAutoIntentServer"
	EventSetOnboardingSoftIntentServer = "SetOnboardingSoftIntentServer"
	EventStartedDirectToHomeServer     = "StartedDirectToHomeServer"
	EventLoadedDCIntroScreenServer     = "LoadedDCIntroScreenServer"
)

type LoadedDCIntroScreenServer struct {
	ActorId      string
	ProspectId   string
	EventName    string
	SessionId    string
	EventId      string
	Timestamp    time.Time
	EventType    string
	UserDeviceId string
}

func NewLoadedDCIntroScreenServer(actorId, deviceId string) *LoadedDCIntroScreenServer {
	return &LoadedDCIntroScreenServer{
		ActorId:      actorId,
		EventId:      uuid.New().String(),
		Timestamp:    time.Now(),
		EventType:    events.EventTrack,
		UserDeviceId: deviceId,
		EventName:    EventLoadedDCIntroScreenServer,
	}
}

func (c *LoadedDCIntroScreenServer) GetEventType() string {
	return c.EventType
}

func (c *LoadedDCIntroScreenServer) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *LoadedDCIntroScreenServer) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *LoadedDCIntroScreenServer) GetEventId() string {
	return c.EventId
}

func (c *LoadedDCIntroScreenServer) GetUserId() string {
	return c.ActorId
}

func (c *LoadedDCIntroScreenServer) GetProspectId() string {
	return c.ProspectId
}

func (c *LoadedDCIntroScreenServer) GetEventName() string {
	return EventLoadedDCIntroScreenServer
}

type LoadedSAIntroScreenV1Server struct {
	ActorId      string
	ProspectId   string
	EventName    string
	SessionId    string
	EventId      string
	Timestamp    time.Time
	EventType    string
	UserDeviceId string
}

func NewLoadedSAIntroScreenV1Server(actorId, deviceId string) *LoadedSAIntroScreenV1Server {
	return &LoadedSAIntroScreenV1Server{
		ActorId:      actorId,
		EventId:      uuid.New().String(),
		Timestamp:    time.Now(),
		EventType:    events.EventTrack,
		UserDeviceId: deviceId,
	}
}

func (c *LoadedSAIntroScreenV1Server) GetEventType() string {
	return c.EventType
}

func (c *LoadedSAIntroScreenV1Server) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *LoadedSAIntroScreenV1Server) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *LoadedSAIntroScreenV1Server) GetEventId() string {
	return c.EventId
}

func (c *LoadedSAIntroScreenV1Server) GetUserId() string {
	return c.ActorId
}

func (c *LoadedSAIntroScreenV1Server) GetProspectId() string {
	return c.ProspectId
}

func (c *LoadedSAIntroScreenV1Server) GetEventName() string {
	return EventLoadedSAIntroScreenV1Server
}

type LoadedIntentScreenServer struct {
	ActorId      string
	ProspectId   string
	EventName    string
	SessionId    string
	EventId      string
	Timestamp    time.Time
	EventType    string
	UserDeviceId string
	FlowsLoaded  []string
}

func NewLoadedIntentScreenServer(actorId, deviceId string, flowsLoaded []string) *LoadedIntentScreenServer {
	return &LoadedIntentScreenServer{
		ActorId:      actorId,
		EventId:      uuid.New().String(),
		Timestamp:    time.Now(),
		EventType:    events.EventTrack,
		UserDeviceId: deviceId,
		FlowsLoaded:  flowsLoaded,
	}
}

func (c *LoadedIntentScreenServer) GetEventType() string {
	return c.EventType
}

func (c *LoadedIntentScreenServer) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *LoadedIntentScreenServer) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *LoadedIntentScreenServer) GetEventId() string {
	return c.EventId
}

func (c *LoadedIntentScreenServer) GetUserId() string {
	return c.ActorId
}

func (c *LoadedIntentScreenServer) GetProspectId() string {
	return c.ProspectId
}

func (c *LoadedIntentScreenServer) GetEventName() string {
	return EventLoadedIntentScreenServer
}

type SetOnboardingIntentServer struct {
	ActorId          string
	ProspectId       string
	EventName        string
	SessionId        string
	EventId          string
	Timestamp        time.Time
	EventType        string
	UserDeviceId     string
	OnboardingIntent string
}

func NewSetOnboardingIntentServer(actorId, deviceId, onbIntent string) *SetOnboardingIntentServer {
	return &SetOnboardingIntentServer{
		ActorId:          actorId,
		EventId:          uuid.New().String(),
		Timestamp:        time.Now(),
		EventType:        events.EventTrack,
		UserDeviceId:     deviceId,
		OnboardingIntent: onbIntent,
	}
}

func (c *SetOnboardingIntentServer) GetEventType() string {
	return c.EventType
}

func (c *SetOnboardingIntentServer) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *SetOnboardingIntentServer) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *SetOnboardingIntentServer) GetEventId() string {
	return c.EventId
}

func (c *SetOnboardingIntentServer) GetUserId() string {
	return c.ActorId
}

func (c *SetOnboardingIntentServer) GetProspectId() string {
	return c.ProspectId
}

func (c *SetOnboardingIntentServer) GetEventName() string {
	return EventSetOnboardingIntentServer
}

type SetOnboardingAutoIntentServer struct {
	ActorId          string
	ProspectId       string
	EventName        string
	SessionId        string
	EventId          string
	Timestamp        time.Time
	EventType        string
	UserDeviceId     string
	OnboardingIntent string
}

func NewSetOnboardingAutoIntentServer(actorId, deviceId, onbIntent string) *SetOnboardingAutoIntentServer {
	return &SetOnboardingAutoIntentServer{
		ActorId:          actorId,
		EventId:          uuid.New().String(),
		Timestamp:        time.Now(),
		EventType:        events.EventTrack,
		UserDeviceId:     deviceId,
		OnboardingIntent: onbIntent,
	}
}

func (c *SetOnboardingAutoIntentServer) GetEventType() string {
	return c.EventType
}

func (c *SetOnboardingAutoIntentServer) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *SetOnboardingAutoIntentServer) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *SetOnboardingAutoIntentServer) GetEventId() string {
	return c.EventId
}

func (c *SetOnboardingAutoIntentServer) GetUserId() string {
	return c.ActorId
}

func (c *SetOnboardingAutoIntentServer) GetProspectId() string {
	return c.ProspectId
}

func (c *SetOnboardingAutoIntentServer) GetEventName() string {
	return EventSetOnboardingAutoIntentServer
}

type StartedDirectToHomeServer struct {
	ActorId      string
	ProspectId   string
	EventName    string
	SessionId    string
	EventId      string
	Timestamp    time.Time
	EventType    string
	UserDeviceId string
	Variant      string
	AcqChannel   string
	AcqIntent    string
}

func NewStartedDirectToHomeServer(actorId string, acqIntent userPb.AcquisitionIntent, acqChannel userPb.AcquisitionChannel, variant string) *StartedDirectToHomeServer {
	return &StartedDirectToHomeServer{
		ActorId:    actorId,
		EventId:    uuid.New().String(),
		Timestamp:  time.Now(),
		EventType:  events.EventTrack,
		AcqIntent:  acqIntent.String(),
		AcqChannel: acqChannel.String(),
		Variant:    variant,
	}
}

func (c *StartedDirectToHomeServer) GetEventType() string {
	return c.EventType
}

func (c *StartedDirectToHomeServer) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *StartedDirectToHomeServer) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *StartedDirectToHomeServer) GetEventId() string {
	return c.EventId
}

func (c *StartedDirectToHomeServer) GetUserId() string {
	return c.ActorId
}

func (c *StartedDirectToHomeServer) GetProspectId() string {
	return c.ProspectId
}

func (c *StartedDirectToHomeServer) GetEventName() string {
	return EventStartedDirectToHomeServer
}

type SetOnboardingSoftIntentServer struct {
	ActorId                 string
	ProspectId              string
	EventName               string
	SessionId               string
	EventId                 string
	Timestamp               time.Time
	TimestampISO8601        string
	EventType               string
	CategoryToSoftIntentMap map[string][]string
	P0SoftIntentCategory    string
	P1SoftIntentCategory    string
	P2SoftIntentCategory    string
	P3SoftIntentCategory    string
}

func NewSetOnboardingSoftIntentServer(actorId string, categoryToSoftIntentMap map[string][]string, p0SoftIntentCategory, p1SoftIntentCategory, p2SoftIntentCategory, p3SoftIntentCategory string) *SetOnboardingSoftIntentServer {
	return &SetOnboardingSoftIntentServer{
		ActorId:                 actorId,
		EventId:                 uuid.New().String(),
		Timestamp:               time.Now(),
		TimestampISO8601:        time.Now().Format("2006-01-02T15:04:05.000Z07:00"),
		EventType:               events.EventTrack,
		CategoryToSoftIntentMap: categoryToSoftIntentMap,
		P0SoftIntentCategory:    p0SoftIntentCategory,
		P1SoftIntentCategory:    p1SoftIntentCategory,
		P2SoftIntentCategory:    p2SoftIntentCategory,
		P3SoftIntentCategory:    p3SoftIntentCategory,
	}
}

func (c *SetOnboardingSoftIntentServer) GetEventType() string {
	return c.EventType
}

func (c *SetOnboardingSoftIntentServer) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *SetOnboardingSoftIntentServer) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *SetOnboardingSoftIntentServer) GetEventId() string {
	return c.EventId
}

func (c *SetOnboardingSoftIntentServer) GetUserId() string {
	return c.ActorId
}

func (c *SetOnboardingSoftIntentServer) GetProspectId() string {
	return c.ProspectId
}

func (c *SetOnboardingSoftIntentServer) GetEventName() string {
	return EventSetOnboardingSoftIntentServer
}
