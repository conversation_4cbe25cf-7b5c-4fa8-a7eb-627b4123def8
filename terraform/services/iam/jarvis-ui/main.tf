terraform {
  backend "s3" {
    # Pass configuration from file through command line
  }
}

variable "service" {
  type    = string
  default = "jarvis-ui"
}

data "aws_iam_policy" "s3-ssm-logging" {
  name = "ssm_s3_logging_policy"
}

module "jarvis-ui" {
  source                  = "../../../modules/grpc-service/v3/iam"
  env                     = var.env
  owner                   = var.owner
  service_name            = var.service
  k8_role                 = true
  eks_cluster_oidc_issuer = var.eks_cluster_oidc_issuer
  s3-ssm-logging          = data.aws_iam_policy.s3-ssm-logging.arn

  secretmanagerread = []

  s3readwrite = []

}
