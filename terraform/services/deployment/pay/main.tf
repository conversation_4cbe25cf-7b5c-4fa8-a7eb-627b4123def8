# Refers requirements from: https://github.com/epiFi/gamma/tree/master/cmd/order

terraform {
  backend "s3" {
    # Pass configuration from file through command line
  }
}

module "pay" {
  source                  = "../../../modules/grpc-service/v3/deployment"
  image_id                = var.image_id
  min                     = var.min
  max                     = var.max
  desired                 = var.desired
  tenant                  = var.tenant
  env                     = var.env
  region                  = "ap-south-1"
  service_name            = "pay"
  target_port             = ["9536", "9992"]
  listener_port           = [9536, 9992]
  health_protocol         = ["HTTPS", "HTTP"]
  health_check_path       = "/health-engine/v1/_health"
  health_check_port       = 9992
  aws_vpc                 = var.aws_vpc
  protocol                = ["HTTPS", "HTTP"]
  protocol_version        = ["GRPC", "HTTP1"]
  instance_profile_name   = "${var.env}-pay"
  volume_size             = 20
  ec2_instance_type       = "t3a.medium"
  fallback_instance_types = ["t3a.large", "m5a.large", "c5a.large"]
  lb_name                 = "${var.env}-private-backend"
  swap                    = var.swap
  clean                   = var.clean
  current_color           = var.current_color
  subnets                 = var.private_subnets
  env_wildcard_cert_arn   = var.env_wildcard_cert_arn
  pointz_zone_id          = var.pointz_zone_id
  private_hosted_zone_id  = var.private_hosted_zone_id
  private_env_zone_id     = var.private_env_zone_id
  dns                     = var.dns
  active_launch_template  = var.active_launch_template
  warm_pool_min           = var.warm_pool_min

  # Tagging variables
  bu-name             = "pay"
  service             = "pay"
  compliance          = ""
  data-classification = ""
  feature             = ""

}
