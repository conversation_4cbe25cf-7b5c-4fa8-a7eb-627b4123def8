# Refers requirements from: https://github.com/epiFi/
#todo(shubhra): add path to specs

terraform {
  backend "s3" {
    # Pass configuration from file through command line
  }
}


module "nlu-engine" {
  source                     = "../../../modules/service-deployment/v2"
  service_name               = "nlu-engine"
  service_short_name         = "nlu-engine"
  alb_target_grpc_port       = 9000
  lb_listener_port           = 9000
  allow_outbound_to_internet = true
  image_id                   = var.image_id
  min                        = var.min
  max                        = var.max
  desired                    = var.desired
  health_check_port          = 9000
  health_check_path          = "/ping"
  ec2_instance_type          = "c6a.2xlarge"
  env                        = var.env
  region                     = var.region
  resource_owner             = var.resource_owner
  aws_vpc                    = var.aws_vpc
  volume_size                = 100
  vpc_cidr                   = var.vpc_cidr
  #iam_role_for_terraform     = var.iam_role_for_terraform
  private_hosted_zone_id   = var.private_hosted_zone_id
  pointz_zone_id           = var.pointz_zone_id
  owner                    = var.owner
  ingress_main_vpc_cidr    = var.ingress_main_vpc_cidr
  allow_ingress_main_vpc   = true
  allow_egress_main_vpc    = true
  private_subnets          = var.private_subnets
  pci_subnets              = var.pci_subnets
  public_subnets           = var.public_subnets
  blue_version             = var.blue_version
  green_version            = var.green_version
  desired_subnets_for_lb   = var.private_subnets
  pci_scoped_service       = false
  rollback                 = var.rollback
  swap                     = var.swap
  clean                    = var.clean
  ssm_agent_log_policy_arn = var.ssm_agent_log_policy_arn
  secrets_to_read          = ["yes"]
  data_vpc_cidr            = var.data_vpc_cidr
  private_env_zone_id      = var.private_env_zone_id
}

#Access s3 bucket epifi-prod-dna
data "aws_iam_policy_document" "epifi-prod-dna-s3-access-document" {
  statement {
    sid = "epifiDevDnas3Access"
    actions = [
      "s3:*",
    ]
    resources = [
      "arn:aws:s3:::epifi-prod-dna",
      "arn:aws:s3:::epifi-prod-dna/*"
    ]
  }
}

resource "aws_iam_policy" "epifi-prod-dna-s3-access" {
  name        = "epifi-prod-dna-s3-access"
  description = "Allow nlu-engine to have access to epifi prod dna bucket"
  path        = "/"
  policy      = data.aws_iam_policy_document.epifi-prod-dna-s3-access-document.json
}

resource "aws_iam_role_policy_attachment" "allow-epifi-prod-dna-s3-access" {
  role       = module.nlu-engine.role_name
  policy_arn = "arn:aws:iam::${var.owner}:policy/epifi-prod-dna-s3-access"
  depends_on = [module.nlu-engine.role_name, aws_iam_policy.epifi-prod-dna-s3-access]
}
