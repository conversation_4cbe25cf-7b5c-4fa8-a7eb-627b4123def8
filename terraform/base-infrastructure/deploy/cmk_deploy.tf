data "aws_iam_policy_document" "cmk_key_policy_document" {
  statement {
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::632884248997:root"
      ]
    }
    sid       = "Enable IAM User Permissions"
    actions   = ["kms:*"]
    resources = ["*"]
  }

  statement {
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::632884248997:role/AdminRole",
      ]
    }
    sid = "Allow access for Key Administrators"
    actions = [
      "kms:Create*",
      "kms:Describe*",
      "kms:Enable*",
      "kms:List*",
      "kms:Put*",
      "kms:Update*",
      "kms:Revoke*",
      "kms:Disable*",
      "kms:Get*",
      "kms:Delete*",
      "kms:TagResource",
      "kms:UntagResource",
      "kms:ScheduleKeyDeletion",
      "kms:CancelKeyDeletion"
    ]
    resources = ["*"]
  }

  statement {
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::632884248997:role/jenkins-iam-role",
        "arn:aws:iam::632884248997:role/AdminRole",

        "arn:aws:iam::406142726310:root",
        "arn:aws:iam::785240676363:root",
        "arn:aws:iam::571894593668:root",
        "arn:aws:iam::240673475153:root",
        "arn:aws:iam::366873573630:root",
        "arn:aws:iam::854002675954:root",
        "arn:aws:iam::************:root",
        "arn:aws:iam::************:root",
        "arn:aws:iam::************:root",

        "arn:aws:iam::406142726310:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling",
        "arn:aws:iam::785240676363:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling",
        "arn:aws:iam::571894593668:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling",
        "arn:aws:iam::240673475153:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling",
        "arn:aws:iam::632884248997:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling",
        "arn:aws:iam::366873573630:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling",
        "arn:aws:iam::854002675954:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling",
        "arn:aws:iam::************:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling",
        "arn:aws:iam::************:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling",
      ]
    }
    sid = "Allow use of the key"
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey"
    ]
    resources = ["*"]
  }

  statement {
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::632884248997:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling",
        "arn:aws:iam::632884248997:role/jenkins-iam-role",
        "arn:aws:iam::632884248997:role/AdminRole",
        "arn:aws:iam::406142726310:root",
        "arn:aws:iam::571894593668:root",
        "arn:aws:iam::785240676363:root",
        "arn:aws:iam::240673475153:root",
        "arn:aws:iam::366873573630:root",
        "arn:aws:iam::854002675954:root",
        "arn:aws:iam::************:root",
        "arn:aws:iam::************:root",
        "arn:aws:iam::************:root",
      ]
    }
    sid = "Allow attachment of persistent resources"
    actions = [
      "kms:CreateGrant",
      "kms:ListGrants",
      "kms:RevokeGrant"
    ]
    resources = ["*"]
  }
}

resource "aws_kms_key" "key_deploy" {
  customer_master_key_spec = "SYMMETRIC_DEFAULT"
  description              = "encrypt/decrypt key to promote EBS volumes from deploy to different AWS accounts; e.g. staging, uat, prod etc"
  enable_key_rotation      = false
  is_enabled               = true
  key_usage                = "ENCRYPT_DECRYPT"
  tags = {
    "Name"   = "cmk/deploy",
    "Team"   = "devops",
    "Source" = "deploy",
    "Target" = "demo/staging/uat/production/QA"
  }
  policy = data.aws_iam_policy_document.cmk_key_policy_document.json
}

resource "aws_kms_alias" "alias_deploy" {
  name          = "alias/cmk/deploy"
  target_key_id = aws_kms_key.key_deploy.key_id
}
