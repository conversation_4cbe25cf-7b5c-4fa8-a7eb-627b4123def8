package consumer_test

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/gamma/analyser/investment/consumer"
	consumerPb "github.com/epifi/gamma/api/analyser/investment/consumer"
	"github.com/epifi/gamma/api/analyser/investment/model"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func TestService_ProcessAnalysisTasksEvent(t *testing.T) {
	tests := []struct {
		name    string
		event   *model.AnalysisTasksEvent
		before  func(f *mockFields, event *model.AnalysisTasksEvent)
		want    *consumerPb.ProcessAnalysisTaskEventResponse
		wantErr bool
	}{
		{
			name: "Successfully process analysis tasks event",
			event: &model.AnalysisTasksEvent{
				ActorId: "actorId",
				Tasks: []*model.AnalysisTask{
					{
						ActorId: "actorId2",
						Name:    model.TaskName_TASK_NAME_MF_PORTFOLIO_HISTORY_ANALYTICS,
						Status:  model.TaskStatus_TASK_STATUS_SCHEDULED,
						Params: &model.TaskParams{
							Params: &model.TaskParams_MfPortfolioHistoryAnalysis{
								MfPortfolioHistoryAnalysis: &model.MFPortfolioHistoryAnalysisParams{
									Mode: model.TaskMode_TASK_MODE_FULL_REFRESH,
								},
							},
						},
					},
				},
			},
			before: func(f *mockFields, event *model.AnalysisTasksEvent) {
				f.mockInvestmentEventProcessor.EXPECT().ProcessAnalysisTasksEvent(gomock.Any(), event).Return(nil)
			},
			want: &consumerPb.ProcessAnalysisTaskEventResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS}},
		},
		{
			name: "Error in processing event should return transient failure",
			event: &model.AnalysisTasksEvent{
				ActorId: "actorId",
				Tasks: []*model.AnalysisTask{
					{
						ActorId: "actorId2",
						Name:    model.TaskName_TASK_NAME_MF_PORTFOLIO_HISTORY_ANALYTICS,
						Status:  model.TaskStatus_TASK_STATUS_SCHEDULED,
						Params: &model.TaskParams{
							Params: &model.TaskParams_MfPortfolioHistoryAnalysis{
								MfPortfolioHistoryAnalysis: &model.MFPortfolioHistoryAnalysisParams{
									Mode: model.TaskMode_TASK_MODE_FULL_REFRESH,
								},
							},
						},
					},
				},
			},
			before: func(f *mockFields, event *model.AnalysisTasksEvent) {
				f.mockInvestmentEventProcessor.EXPECT().ProcessAnalysisTasksEvent(gomock.Any(), event).Return(fmt.Errorf("some err"))
			},
			want: &consumerPb.ProcessAnalysisTaskEventResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE}},
		},
		{
			name: "Permanent error in processing event should return permanent failure",
			event: &model.AnalysisTasksEvent{
				ActorId: "actorId",
				Tasks: []*model.AnalysisTask{
					{
						ActorId: "actorId2",
						Name:    model.TaskName_TASK_NAME_MF_PORTFOLIO_HISTORY_ANALYTICS,
						Status:  model.TaskStatus_TASK_STATUS_SCHEDULED,
						Params: &model.TaskParams{
							Params: &model.TaskParams_MfPortfolioHistoryAnalysis{
								MfPortfolioHistoryAnalysis: &model.MFPortfolioHistoryAnalysisParams{
									Mode: model.TaskMode_TASK_MODE_FULL_REFRESH,
								},
							},
						},
					},
				},
			},
			before: func(f *mockFields, event *model.AnalysisTasksEvent) {
				f.mockInvestmentEventProcessor.EXPECT().ProcessAnalysisTasksEvent(gomock.Any(), event).Return(epifierrors.ErrPermanent)
			},
			want: &consumerPb.ProcessAnalysisTaskEventResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE}},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := InitMocks(ctrl)
			tt.before(f, tt.event)

			s := consumer.NewService(f.mockInvestmentEventProcessor, f.mockAnalysisTaskDao, f.mockMfSchemeAnalyticsCache)
			got, err := s.ProcessAnalysisTasksEvent(context.Background(), tt.event)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessAnalysisTasksEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessAnalysisTasksEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}
