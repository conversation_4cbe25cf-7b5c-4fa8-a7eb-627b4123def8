package dao

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	gormv2 "gorm.io/gorm"

	caPb "github.com/epifi/gamma/api/connected_account"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/connectedaccount/config"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

type AaDataProcessAttemptTestSuite struct {
	db                  *gormv2.DB
	dataFetchAttemptDao DataProcessAttemptDao
	conf                *config.Config
	dbName              string
	gconf               *genconf.Config
}

var (
	adpats                 AaDataProcessAttemptTestSuite
	dataProcessAttemptFix1 = &caPb.DataProcessAttempt{
		Id:                "10eb3969-d153-4642-befd-e7fe45343a18",
		AttemptId:         "d60237fa-d347-4aca-90e3-703f4b9feaaf",
		FipId:             "finsharebank",
		LinkRefNumber:     "a438ec5b-652e-4fa8-ba9d-2b6999055809",
		DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_SUCCESS,
	}
	dataProcessAttemptModel1 = &caPb.DataProcessAttempt{
		AttemptId:         "d60237fa-d347-4aca-90e3-703f4b9feaaf",
		FipId:             "finsharebank",
		LinkRefNumber:     "account-reference-link-num",
		DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_SUCCESS,
	}
)

func TestDataProcessAttemptDaoCrdb_Create(t *testing.T) {
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx context.Context
		dpa *caPb.DataProcessAttempt
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "create record failed due to missing mandatory fields",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx: context.Background(),
				dpa: &caPb.DataProcessAttempt{
					AttemptId: dataProcessAttemptModel1.GetAttemptId(),
					FipId:     dataProcessAttemptModel1.GetId(),
				},
			},
			wantErr: true,
		},
		{
			name: "create record failed due to missing mandatory fields",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx: context.Background(),
				dpa: &caPb.DataProcessAttempt{
					AttemptId: dataProcessAttemptModel1.GetAttemptId(),
				},
			},
			wantErr: true,
		},
		{
			name: "create record failed due to missing mandatory fields",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx: context.Background(),
				dpa: &caPb.DataProcessAttempt{},
			},
			wantErr: true,
		},
		{
			name: "create record successfully",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx: context.Background(),
				dpa: dataProcessAttemptModel1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, adpats.db, adpats.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			d := &DataProcessAttemptDaoImpl{
				Pgdb: tt.fields.DB,
			}
			got, err := d.Create(tt.args.ctx, tt.args.dpa)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestDataProcessAttemptDaoCrdb_CreateOrGet(t *testing.T) {
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx       context.Context
		attemptId string
		fipId     string
		refNm     string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.DataProcessAttempt
		wantErr bool
	}{
		{
			name: "got record successfully",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				attemptId: "d60237fa-d347-4aca-90e3-703f4b9feaaf",
				fipId:     "finsharebank",
				refNm:     "a438ec5b-652e-4fa8-ba9d-2b6999055809",
			},
			wantErr: false,
		},
		{
			name: "create successful",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				attemptId: "d60237fa-d347-4aca-90e3-703f4b9feaaf",
				fipId:     "random-new-fipId",
				refNm:     "random-new-reference",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, adpats.db, adpats.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			d := &DataProcessAttemptDaoImpl{
				Pgdb: tt.fields.DB,
			}
			got, err := d.CreateOrGet(tt.args.ctx, tt.args.attemptId, tt.args.fipId, tt.args.refNm)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrGet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestDataProcessAttemptDaoCrdb_GetByAttemptFipAndRef(t *testing.T) {
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx        context.Context
		attemptId  string
		fipId      string
		linkRefNum string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "missing attribute in get",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				fipId:      dataProcessAttemptFix1.GetFipId(),
				linkRefNum: dataProcessAttemptFix1.GetLinkRefNumber(),
			},
			wantErr: true,
		},
		{
			name: "missing attribute in get",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				attemptId:  dataProcessAttemptFix1.GetAttemptId(),
				linkRefNum: dataProcessAttemptFix1.GetLinkRefNumber(),
			},
			wantErr: true,
		},
		{
			name: "missing attribute in get",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				attemptId: dataProcessAttemptFix1.GetAttemptId(),
				fipId:     dataProcessAttemptFix1.GetFipId(),
			},
			wantErr: true,
		},
		{
			name: "get record successfully",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				attemptId:  dataProcessAttemptFix1.GetAttemptId(),
				fipId:      dataProcessAttemptFix1.GetFipId(),
				linkRefNum: dataProcessAttemptFix1.GetLinkRefNumber(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, adpats.db, adpats.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			d := &DataProcessAttemptDaoImpl{
				Pgdb: tt.fields.DB,
			}
			got, err := d.GetByAttemptFipAndRef(tt.args.ctx, tt.args.attemptId, tt.args.fipId, tt.args.linkRefNum)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAttemptFipAndRef() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestDataProcessAttemptDaoCrdb_UpdateById(t *testing.T) {
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx        context.Context
		id         string
		dpa        *caPb.DataProcessAttempt
		updateMask []caPb.DataProcessAttemptFieldMask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "empty id passed in update",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				dpa:        &caPb.DataProcessAttempt{DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_SUCCESS},
				updateMask: []caPb.DataProcessAttemptFieldMask{caPb.DataProcessAttemptFieldMask_DATA_PROCESS_ATTEMPT_FIELD_MASK_DATA_PROCESS_STATUS},
			},
			wantErr: true,
		},
		{
			name: "update mask empty",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx: context.Background(),
				id:  dataFetchAttemptFix1.GetId(),
				dpa: &caPb.DataProcessAttempt{DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_SUCCESS},
			},
			wantErr: true,
		},
		{
			name: "update data process status successfully",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				id:         dataFetchAttemptFix1.GetId(),
				dpa:        &caPb.DataProcessAttempt{DataProcessStatus: caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_SUCCESS},
				updateMask: []caPb.DataProcessAttemptFieldMask{caPb.DataProcessAttemptFieldMask_DATA_PROCESS_ATTEMPT_FIELD_MASK_DATA_PROCESS_STATUS},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, adpats.db, adpats.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			d := &DataProcessAttemptDaoImpl{
				Pgdb: tt.fields.DB,
			}
			if err := d.UpdateById(tt.args.ctx, tt.args.id, tt.args.dpa, tt.args.updateMask); (err != nil) != tt.wantErr {
				t.Errorf("UpdateById() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDataProcessAttemptDaoCrdb_GetByFetchAttemptId(t *testing.T) {
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx       context.Context
		attemptId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*caPb.DataProcessAttempt
		wantErr bool
	}{
		{
			name: "parameters missing : empty attemptID",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "got record successfully",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				attemptId: "d60237fa-d347-4aca-90e3-703f4b9feaaf",
			},
			wantErr: false,
		},
		{
			name: "failed to get record",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:       context.Background(),
				attemptId: "random-attemptID",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, adpats.db, adpats.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			d := &DataProcessAttemptDaoImpl{
				Pgdb: tt.fields.DB,
			}
			got, err := d.GetByFetchAttemptId(tt.args.ctx, tt.args.attemptId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByFetchAttemptId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestDataProcessAttemptDaoCrdb_GetById(t *testing.T) {
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx              context.Context
		processAttemptId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.DataProcessAttempt
		wantErr bool
	}{
		{
			name: "parameters missing : empty ProcessAttemptId",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "got record successfully",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:              context.Background(),
				processAttemptId: "10eb3969-d153-4642-befd-e7fe45343a18",
			},
			wantErr: false,
		},
		{
			name: "failed to get record",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:              context.Background(),
				processAttemptId: "random-id",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, adpats.db, adpats.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			d := &DataProcessAttemptDaoImpl{
				Pgdb: tt.fields.DB,
			}
			got, err := d.GetById(tt.args.ctx, tt.args.processAttemptId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestDataProcessAttemptDaoCrdb_GetByAttemptFipAndRef1(t *testing.T) {
	type fields struct {
		DB    *gormv2.DB
		gconf *genconf.Config
	}
	type args struct {
		ctx        context.Context
		attemptId  string
		fipId      string
		linkRefNum string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caPb.DataProcessAttempt
		wantErr bool
	}{
		{
			name: "got record successfully",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				attemptId:  "d60237fa-d347-4aca-90e3-703f4b9feaaf",
				fipId:      "finsharebank",
				linkRefNum: "a438ec5b-652e-4fa8-ba9d-2b6999055809",
			},
			wantErr: false,
		},
		{
			name: "failed to get record",
			fields: fields{
				DB:    adpats.db,
				gconf: adadts.gconf,
			},
			args: args{
				ctx:        context.Background(),
				attemptId:  "random-attemptId",
				fipId:      "random-fipId",
				linkRefNum: "random-linkRefNum",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, adpats.db, adpats.conf.ConnectedAccountDb.GetName(), AffectedTestTables)
			d := &DataProcessAttemptDaoImpl{
				Pgdb: tt.fields.DB,
			}
			got, err := d.GetByAttemptFipAndRef(tt.args.ctx, tt.args.attemptId, tt.args.fipId, tt.args.linkRefNum)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAttemptFipAndRef() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}
