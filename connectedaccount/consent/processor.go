//go:generate mockgen -source=processor.go -destination=../test/mocks/mock_consent_manager/mock_consent_manager.go -package=mock_consent_manager
package consent

import (
	"context"

	caPb "github.com/epifi/gamma/api/connected_account"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
)

type Manager interface {
	// Request Consent for an actor given the VUA of the actor
	// Returns error in case of any error encountered while requesting consent
	RequestConsent(ctx context.Context, actorId string, vua string, aaEntity caEnumPb.AaEntity, purpose caEnumPb.ConsentRequestPurpose, caFlowName caEnumPb.CAFlowName) (*caPb.ConsentRequest, error)
	// Fetch consent using consent handle
	// In case the consent handle status moves to READY state, we return the consent id generated by the vendor
	// In case the consent handle is still in PENDING state, consent id is not returned and err is nil
	// In case any error is encountered while calling VG api, error is thrown
	// Returned consent id is AA generated consent id for the consent handle
	FetchConsent(ctx context.Context, consentHandle string, aaEntity caEnumPb.AaEntity) (string, caEnumPb.ConsentHandleStatus, error)
	// Store the consent artefact in DB
	// consent request id is the primary key of consent_requests and consent id the AA generated consent id
	// In case any error is encountered while fetching consent artefact and storing it, error is returned
	StoreConsent(ctx context.Context, consentRequestId string, consentId string) (*caPb.Consent, error)
	// Refresh/Sync consent if any update is made in consent by the user
	// This update can be made outside epifi APP also
	RefreshConsent(ctx context.Context, con *caPb.Consent) (*caPb.Consent, error)
	// Get the Auth Handle if already present and return
	// If not create a new auth handle and return
	GetConsentRequestForAuth(ctx context.Context, actorId string, vua string, aaEntity caEnumPb.AaEntity, caFlowName caEnumPb.CAFlowName) (*caPb.ConsentRequest, error)
}

// For mocking in test cases, since we are calling method of same struct from outside as well from within the package
type ConsentRequestOrc func(context.Context, string, string, caEnumPb.AaEntity, caEnumPb.ConsentRequestPurpose, caEnumPb.CAFlowName) (*caPb.ConsentRequest, error)
