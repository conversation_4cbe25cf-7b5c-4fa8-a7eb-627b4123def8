{"db_conn": "pgdb", "database_type": "pgdb", "database": "<PERSON><PERSON>ock", "tables": {"disputes": {"mappers": "2", "columns": "id::text,ticket_id,source,channel,dispute_type,dispute_state,created_at,updated_at,actor_id,internal_transaction_id,escalation_mode,escalation_time,transaction_status,receiver,reverse_Processing_attempt_count", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "dispute_ticket_logs": {"mappers": "1", "columns": "id,ticket_id,dispute_id::text,ticket_state,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "payout_requests": {"mappers": "1", "columns": "id::text,requester_email,ticket_id,approval_state,approver_email,payout_status,created_at,updated_at,payout_timestamp,reason", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "audit_logs": {"mappers": "1", "columns": "id::text,agent_email,ticket_id,access_level,access_status,action,object,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "call_details": {"mappers": "2", "columns": "id::text,monitor_ucid,caller_id::text,campaign_did::text,call_type,call_start_time,call_end_time,call_duration,call_recording_link,disposition,fallback_rule,call_handle_status,agent_call_status,customer_status,dial_status,hang_up_by,phone_name,agent_id,agent_phone_number::text,freshdesk_ticket_id,product_category,call_drop_by_user,ticket_attachment_type,call_stage,created_at,updated_at,call_details_meta::text", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "customer_authentications": {"mappers": "1", "columns": "id::text,agent_email,ticket_id,user_id,created_at,updated_at,reset_count,customer_auth_identifier_type,customer_auth_identifier_value", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "customer_authentication_factors_states": {"mappers": "2", "columns": "auth_id,auth_category,auth_factor,auth_factor_status,verification_tries_count,verification_tries_limit,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "customer_authentication_factors_retry_logs": {"mappers": "2", "columns": "id::text,auth_id,auth_factor,auth_factor_status,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "customer_authentication_callback_responses": {"mappers": "2", "columns": "auth_id,external_id,email_hash_key,auth_factor,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "bulk_ticket_jobs": {"mappers": "1", "columns": "id,status,input_ticket_count,processed_ticket_count,successful_ticket_count,failed_ticket_count,is_killed,started_by_email,checker_email,description,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "ticket_failure_logs": {"mappers": "1", "columns": "job_id,ticket_id,failure_reason,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "issue_resolution_feedbacks": {"mappers": "1", "columns": "id::text,ticket_id,resolution_category,client_request_id,number_of_attempts,process_stage,last_tried_at,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "issue_resolution_user_response_logs": {"mappers": "1", "columns": "id::text,issue_resolution_feedback_id,resolution_feedback::text,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "ticket_details_transformations": {"mappers": "1", "columns": "id::text,product_category,product_category_details,subcategory,transformation_type,transformation_value::text,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "incidents": {"mappers": "1", "columns": "id::text,incident_category_id,client,actor_id,incident_state,reason,client_request_id,incident_data::text,created_at,updated_at,identified_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "incident_ticket_details": {"mappers": "1", "columns": "id::text,incident_id::text,ticket_id,ticket_status,resolution_type,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "incident_comms_details": {"mappers": "1", "columns": "id::text,incident_id::text,comms_msg_id::text,created_at,updated_at,comms_type,ticket_status,issue_category_id,comms_batch_id,comms_medium", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "support_tickets": {"mappers": "1", "columns": "id,status,source,product_category,product_category_details,identifier_type,ticket_created_at,ticket_updated_at,ticket::text,vendor,created_at,updated_at,agent_group,actor_id,expected_resolution_time", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "issue_categories": {"mappers": "1", "columns": "id::text,product_category,product_category_details,sub_category,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "sherlock_users": {"mappers": "1", "columns": "id::text,user_type,phone::text,email,user_name::text,status,ozonetel_id,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "sherlock_user_roles": {"mappers": "1", "columns": "id::text,user_id::text,user_role,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "in_app_csat_responses": {"mappers": "1", "columns": "id::text,actor_id,ticket_id,question_id,attempt_id,csat_score,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "actor_activities": {"mappers": "1", "columns": "id::text,actor_id,activity_area,activity_type,primary_properties::text,secondary_properties::text,activity_source,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "activity_metadata": {"mappers": "1", "columns": "id::text,activity_area,activity_type,primary_properties,secondary_properties,issue_category_id,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "event_configs": {"mappers": "1", "columns": "id::text,event_name,config_type,config_payload::text,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "issue_configs": {"mappers": "1", "columns": "issue_category_id::text,config_type,config_payload::text,config_version,created_at,updated_at,id::text,is_latest_version", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "call_ivr_details": {"mappers": "1", "columns": "id::text,current_ivr_type,previous_ivr_type,current_question_id,previous_question_id,wait_duration,monitor_ucid,actor_id,current_ivr_state,previous_ivr_state,created_at,updated_at,previous_question_response,preferred_language", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "user_query_logs": {"mappers": "1", "columns": "id::text,actor_id,user_query,user_context,model_response,created_at,updated_at,model_raw_response::text,model_metrics::text", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "escalations": {"mappers": "1", "columns": "id::text,ticket_id,external_reference_id,escalation_type,status,actor_id,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "escalation_attachments": {"mappers": "1", "columns": "id::text,escalation_id::text,attachment_id,s3_path,uploaded_by,status,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}, "escalation_updates": {"mappers": "1", "columns": "id::text,escalation_id::text,payload_type,payload::text,created_at,updated_at", "split_by_column": "updated_at", "incremental_filter_column": "updated_at"}}}