package idfc

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palCommsPb "github.com/epifi/gamma/api/preapprovedloan/comms"
	"github.com/epifi/gamma/api/typesv2/account"
	vgPalIdfcPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/preapprovedloan/config/common"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/helper"
	loanDataProvider "github.com/epifi/gamma/preapprovedloan/loan_data_provider"
	"github.com/epifi/gamma/preapprovedloan/prepay/providers"
)

type BaseProvider struct {
	loanAccountDao        dao.LoanAccountsDao
	loanPaymentRequestDao dao.LoanPaymentRequestsDao
	installmentInfoDao    dao.LoanInstallmentInfoDao
	loanApplicantDao      dao.LoanApplicantDao
	loanDataProvider      loanDataProvider.IFactory
	rpcHelper             *helper.RpcHelper
	prePayConf            *common.Prepay
	vgPalIdfcClient       vgPalIdfcPb.IdfcClient
	piClient              piPb.PiClient
	orderClient           orderPb.OrderServiceClient
}

func NewBaseProvider(
	loanAccountDao dao.LoanAccountsDao,
	loanPaymentRequestDao dao.LoanPaymentRequestsDao,
	installmentInfoDao dao.LoanInstallmentInfoDao,
	loanApplicantDao dao.LoanApplicantDao,
	loanDataProvider loanDataProvider.IFactory,
	rpcHelper *helper.RpcHelper,
	prePayConf *common.Prepay,
	vgPalIdfcClient vgPalIdfcPb.IdfcClient,
	piClient piPb.PiClient,
	orderClient orderPb.OrderServiceClient,
) *BaseProvider {
	return &BaseProvider{
		loanAccountDao:        loanAccountDao,
		loanPaymentRequestDao: loanPaymentRequestDao,
		installmentInfoDao:    installmentInfoDao,
		loanApplicantDao:      loanApplicantDao,
		loanDataProvider:      loanDataProvider,
		rpcHelper:             rpcHelper,
		prePayConf:            prePayConf,
		vgPalIdfcClient:       vgPalIdfcClient,
		piClient:              piClient,
		orderClient:           orderClient,
	}
}

func (p *BaseProvider) GetOrderRequestForPrePayment(
	ctx context.Context,
	orchId, payerActorId, payeeActorId, payeePi string,
	nextAction *deeplinkPb.Deeplink,
	amount *moneyPb.Money, payerUserIdentifier *palPb.PrePayLoanRequest_UserIdentifier, hardPreferredPaymentProtocol paymentPb.PaymentProtocol) (*providers.GetOrderRequestForPrePaymentResponse, error) {
	var payerIdentifier *payPb.UserIdentifier

	// if payer user identifier is empty, get savings account identifier
	if payerUserIdentifier == nil {
		savingsAccount, err := p.rpcHelper.GetSavingsAccountDetails(ctx, payerActorId, commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR)
		if err != nil {
			return nil, fmt.Errorf("error getting savings account by actor id: %w", err)
		}

		savingsPi, err := p.getPi(ctx, savingsAccount.GetAccountNo(), savingsAccount.GetIfscCode())
		if err != nil {
			return nil, fmt.Errorf("error getting pi for savings account: %w", err)
		}

		payerIdentifier = &payPb.UserIdentifier{
			ActorId: payerActorId,
			PiId:    savingsPi.GetId(),
		}
	} else {
		payerIdentifier = &payPb.UserIdentifier{
			ActorId:     payerUserIdentifier.GetActorId(),
			AccountId:   payerUserIdentifier.GetAccountId(),
			AccountType: payerUserIdentifier.GetAccountType(),
		}
	}

	orderReq := &payPb.CreateFundTransferOrderRequest{
		Amount:          amount,
		Provenance:      orderPb.OrderProvenance_INTERNAL,
		Workflow:        orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		Tags:            []orderPb.OrderTag{orderPb.OrderTag_LOAN},
		ClientRequestId: orchId,
		Remarks:         "PrePay Loan",
		PayerIdentifier: payerIdentifier,
		PayeeIdentifier: &payPb.UserIdentifier{
			ActorId: payeeActorId,
			PiId:    payeePi,
		},
		PostAuthorisationAction:      nextAction,
		HardPreferredPaymentProtocol: hardPreferredPaymentProtocol,
	}

	return &providers.GetOrderRequestForPrePaymentResponse{OrderRequest: orderReq}, nil
}

func (p *BaseProvider) getPi(ctx context.Context, accountNumber, ifscCode string) (*piPb.PaymentInstrument, error) {
	piRes, err := p.piClient.GetPi(ctx, &piPb.GetPiRequest{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.GetPiRequest_AccountRequestParams_{AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
			ActualAccountNumber: accountNumber,
			IfscCode:            ifscCode,
		}},
	})
	if te := epifigrpc.RPCError(piRes, err); te != nil {
		return nil, fmt.Errorf("failed to fetch pi by acount number, %w", te)
	}
	return piRes.GetPaymentInstrument(), nil
}

// nolint: funlen
func (p *BaseProvider) ReconPrePaymentAtLender(ctx context.Context, reconReq *providers.ReconPrepaymentAtLenderRequest) (*providers.ReconPrepaymentAtLenderResponse, error) {
	loanPaymentReq, err := p.loanPaymentRequestDao.GetById(ctx, reconReq.PaymentRequestId)
	if err != nil {
		return nil, fmt.Errorf("unable to get loan payment request by id %s: %w", reconReq.PaymentRequestId, err)
	}

	loanAccount, err := p.loanAccountDao.GetById(ctx, loanPaymentReq.GetAccountId())
	if err != nil {
		return nil, fmt.Errorf("unable to get loan account by id %s: %w", loanPaymentReq.GetAccountId(), err)
	}

	lii, err := p.installmentInfoDao.GetByActiveAccountId(ctx, loanAccount.GetId())
	if err != nil {
		return nil, fmt.Errorf("failed to fetch LII by account ID, %w", err)
	}

	isLoanCancellation := loanPaymentReq.Type ==
		palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE &&
		helper.IsLoanInGracePeriod(loanAccount, lii)

	shouldUseIDFCLoanCancellationV2 := isLoanCancellation && p.prePayConf.UseIDFCLoanCancellationV2

	txnDetails := reconReq.PrepaymentTxnDetails

	// skip calling IDFC's payment posting API for loan cancellation v2 flow
	// this was requirement from IDFC team
	if !shouldUseIDFCLoanCancellationV2 {
		var paymentPostingRes *vgPalIdfcPb.PaymentPostingResponse
		paymentPostingRes, err = p.vgPalIdfcClient.PaymentPosting(ctx, &vgPalIdfcPb.PaymentPostingRequest{
			Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IDFC},
			CorrelationId: loanPaymentReq.GetOrchId(),
			// using payment UTR as unique identifier
			EntityReqId:      txnDetails.Utr,
			SourceSystemName: vgPalIdfcPb.SourceSystemName_SOURCE_SYSTEM_NAME_DIGITAL,
			LoanId:           loanAccount.GetAccountNumber(),
			TransactionDate:  datetimePkg.TimeToDateInLoc(txnDetails.TxnInitiationTime, datetimePkg.IST),
			ReceiptNo:        txnDetails.Utr,
			ChequeNo:         txnDetails.Utr,
			ChequeAmount:     txnDetails.TxnAmount,
			EmiAmount:        txnDetails.TxnAmount,
		})
		if err = epifigrpc.RPCError(paymentPostingRes, err); err != nil {
			return nil, err
		}
	}

	// sending pre-pay push notification to user
	notifReq := helper.SendNotificationRequest{
		Amount:            txnDetails.TxnAmount,
		ActorId:           loanAccount.GetActorId(),
		NotificationType:  palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_PRE_PAY,
		LoanAccountNumber: loanAccount.GetAccountNumber(),
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_DETAILS_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanDetailsScreenOptions{
				PreApprovedLoanDetailsScreenOptions: &deeplinkPb.PreApprovedLoanDetailsScreenOptions{
					LoanId: loanAccount.GetId(),
				},
			},
		},
	}
	p.rpcHelper.SendNotificationWithGoRoutine(ctx, notifReq)

	return &providers.ReconPrepaymentAtLenderResponse{}, nil
}
