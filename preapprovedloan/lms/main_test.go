package lms

import (
	"os"
	"testing"

	"github.com/epifi/gamma/preapprovedloan/config"
	"github.com/epifi/gamma/preapprovedloan/test"
)

var (
	conf *config.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	// nolint : dogsled
	var teardown func()
	conf, _, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
