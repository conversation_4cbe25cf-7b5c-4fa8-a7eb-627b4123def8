package impl_test

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/preapprovedloan/config"
	"github.com/epifi/gamma/preapprovedloan/test"
)

var (
	affectedTestTables = []string{
		"loan_requests",
		"loan_accounts",
		"loan_offers",
		"loan_step_executions",
		"loan_offer_eligibility_criteria",
		"loan_installment_info",
		"loan_installment_payout",
		"loan_applicants",
		"loan_activities",
		"loan_payment_requests",
		"collection_leads",
		"collection_allocations",
		"collection_communications",
		"fetched_assets",
		"mandate_requests",
	}

	conf                   *config.Config
	dbResourceProviderPool *test.DbResourceProviderPool
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown, dbProviderResourcePoolCleanup func()

	conf, _, teardown = test.InitTestServer()
	// Remove the databases not used in tests to reduce DB start time, thereby reducing overall test execution time
	for ownership := range conf.PgDbConfigMap {
		if ownership != common.Ownership_name[int32(common.Ownership_FEDERAL_BANK)] {
			delete(conf.PgDbConfigMap, ownership)
		}
	}
	dbResourceProviderPool, dbProviderResourcePoolCleanup = test.NewDbResourceProviderPool(conf.PgDbConfigMap.GetOwnershipToDbConfigMap(), affectedTestTables, 1)

	exitCode := m.Run()
	teardown()
	dbProviderResourcePoolCleanup()

	os.Exit(exitCode)
}
