package config

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/frontend/app"

	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
	typesPkg "github.com/epifi/be-common/pkg/types"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	deeplinkcfgv2 "github.com/epifi/gamma/pkg/deeplink/cfg/v2"
	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
	"github.com/epifi/gamma/preapprovedloan/config/common"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

const (
	RudderWriteKey         = "RudderWriteKey"
	Bucket_Preapprovedloan = "preapprovedloan"
	// Federal
	EpifiFederalPgpPrivateKey = "EpifiFederalPgpPrivateKey"
	FederalPgpPublicKey       = "FederalPgpPublicKey"
	// Adding #nosec to suppress G101: Potential hardcoded credentials
	EpifiFederalPgpPassphrase = "EpifiFederalPgpPassphrase" // #nosec
	federalDbCredentials      = "FederalDbCredentials"      // #nosec
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(false)
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig(onlyStaticFiles bool) (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err1 := cfg.LoadConfigUsingKoanf(configDirPath, cfg.PRE_APPROVED_LOAN_SERVICE)
	if err1 != nil {
		return nil, fmt.Errorf("failed to load config %w", err1)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	if onlyStaticFiles {
		return conf, nil
	}

	keyToSecret, err := cfg.LoadAllSecretsV2(conf.FederalDb, conf.FederalPgDb, conf.Secrets.Ids, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}

	dbOwnershipMap := conf.DbConfigMap.GetOwnershipToDbConfigMap()
	if err = updateDefaultDbConfigMap(conf, dbOwnershipMap); err != nil {
		return nil, fmt.Errorf("failed to update secret values in the config: %w", err)
	}

	if err = updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}

	if err = updateDefaultPgDbConfigMap(conf, conf.PgDbConfigMap.GetOwnershipToDbConfigMap()); err != nil {
		return nil, err
	}
	if err = updateFederalPgdbConfig(conf, keyToSecret); err != nil {
		return nil, err
	}

	// checking if vendor and loan program values in the config are valid.
	for _, lh := range conf.LoanOfferPrioritisation.LoanHeaders {
		_, ok := palPb.Vendor_value[lh.Vendor]
		if !ok {
			return nil, errors.New(fmt.Sprintf("unsupported vendor in loan prioritisation: %s", lh.Vendor))
		}
		_, ok = palPb.LoanProgram_value[lh.LoanProgram]
		if !ok {
			return nil, errors.New(fmt.Sprintf("unsupported loan program in loan prioritisation: %s", lh.LoanProgram))
		}
	}
	return conf, nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultDbConfigMap(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointsInConfigMap(dbOwnershipMap, dbServerEndpoint)
	var err error
	if _, err = cfg.LoadAndUpdateSecretValues(dbOwnershipMap, nil, c.Application.Environment, c.AWS.Region); err != nil {
		return fmt.Errorf("failed to load and update secret values %w", err)
	}

	return nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultPgDbConfigMap(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointsInConfigMap(dbOwnershipMap, dbServerEndpoint)
	var err error
	if _, err = cfg.LoadAndUpdateSecretValues(dbOwnershipMap, nil, c.Application.Environment, c.AWS.Region); err != nil {
		return fmt.Errorf("failed to load and update secret values %w", err)
	}

	return nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.FederalDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(c.FederalDb, c.Secrets, keyToSecret)
	return nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateFederalPgdbConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.FederalPgDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdatePGDBSecretValues(c.FederalPgDb, c.Secrets, keyToSecret)
	// update db username and password in db config
	// if env is development keys are not fetched from SM hence update from yml file itself
	if c.Application.Environment == cfg.TestEnv || c.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c.FederalPgDb, c.Secrets.Ids[federalDbCredentials])
		return nil
	}
	if _, ok := keyToSecret[federalDbCredentials]; !ok {
		return fmt.Errorf("db username password not fetched from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.FederalPgDb, keyToSecret[federalDbCredentials])
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.FederalDb.Host = val
		cfg.OverwriteDbHost(c.DbConfigMap.GetOwnershipToDbConfigMap(), val)
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		c.FederalPgDb.Host = val
		cfg.OverwriteDbHost(c.PgDbConfigMap.GetOwnershipToDbConfigMap(), val)
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

//go:generate conf_gen github.com/epifi/gamma/preapprovedloan/config Config
type Config struct {
	Application   *Application
	Logging       *cfg.Logging
	SecureLogging *cfg.SecureLogging
	Flags         *common.Flags `dynamic:"true"`
	Server        *Server
	FederalDb     *cfg.DB
	FederalPgDb   *cfg.DB
	AWS           *Aws
	RudderStack   *cfg.RudderStackBroker
	Secrets       *cfg.Secrets
	Tracing       *cfg.Tracing
	Vendor        *Vendor
	KfsExitUrl    string

	DbConfigMap   cfg.DbConfigMap
	PgDbConfigMap cfg.DbConfigMap

	VendorsSupported []palPb.Vendor
	VendorUsed       palPb.Vendor

	ProcessLoanInboundTransactionSqsSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	OrderUpdatePreApprovedLoanSqsSubscriber    *cfg.SqsSubscriber `dynamic:"true"`
	ProcessLoansFiftyfinCallbackSqsSubscriber  *cfg.SqsSubscriber `dynamic:"true"`

	SignalWorkflowPublisher *cfg.SqsPublisher

	Notification            *common.Notification
	NudgeExitEventPublisher *cfg.SqsPublisher

	EarlySalary *EarlySalary

	Prepay *common.Prepay `dynamic:"true"`

	// flag to enable or disable dynamic elements for all users
	// this will typically be used to allow testing in non prod envs
	EnableDynamicElementForAllUsers bool
	// flag to decide whether a campaign related dynamic element for home screen is enabled or not, if enabled then campaign one would override the default one.
	// use-case is to show a custom loan banner on home for marketing campaign use-cases e.g. showing a custom loan banner on home during Diwali festival,
	// this banner can be disabled from dynamic config once the campaign is over.
	IsCampaignRelatedHomeDynamicElementEnabled bool `dynamic:"true"`
	// list of segments to whom the LAMF related dynamic element (instead of the default loan one) should be shown on home screen.
	AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement []string `dynamic:"true"`

	// this will have the id of the segment created for the users who will see the "Instant Cash" title
	InstantCashSegmentId string

	// config for loan home widget
	HomeWidgetV1Config *HomeWidgetV1Config `dynamic:"true"`

	PgdbMigrationFlag bool `dynamic:"true"`

	LamfConfig *LamfConfig `dynamic:"true"`

	Downtime *Downtime `dynamic:"true"`

	// specifies common deeplink variables which should be config driven
	DeeplinkConfig *common.DeeplinkConfig `dynamic:"true" ,quest:"component,area:PersonalLoan"`

	PreApprovedLoanBucketName string `iam:"s3-readwrite"`

	QuestSdk *sdkconfig.Config `dynamic:"true"`

	CreditReportConfig *common.CreditReportConfig `dynamic:"true"`

	// category format <vendor>:<loan_program>
	CategoryToEmiComms map[string]*EmiComms `dynamic:"true"`

	FeatureReleaseConfig *releaseConfig.FeatureReleaseConfig `dynamic:"true"`

	Lms *common.Lms

	LoanCalculator *common.LoanCalculator

	EsignLinkExpirationInSecs int64 `dynamic:"true"`

	EnableDataExistenceManager bool `dynamic:"true"`

	// stores mandate related configuration
	MandateConfig *common.MandateConfig `dynamic:"true"`
	// config for collecting wealth data from different screens.
	WealthToTechDataSharingConsentConfig *ConsentConfig `dynamic:"true"`
	// config for fetching sms consent from user
	SmsFetchingConsentConfig *ConsentConfig `dynamic:"true"`

	VendorProgramLevelFeature *common.VendorProgramLevelFeature `dynamic:"true"`

	LopeOverrideConfig *common.LopeOverrideConfig `dynamic:"true" ,quest:"component,area:PersonalLoan"`

	Lendability *Lendability `dynamic:"true" ,quest:"component,area:PersonalLoan"`

	// Contains loan offer prioritisation where first element is highest priority and last element is lest priority loan offer.
	LoanOfferPrioritisation *LoanOfferPrioritisation
	// This is a feature flag which will be used to enable or disable the new eligibility flow for SG ETB users
	// The new flow migrates from LoanEligibility to LoanDataCollection Workflow.
	SgEtbNewEligibilityFlow *common.FeatureConstraint `dynamic:"true"`

	// allows user to cancel current LR and start a new one in a single click
	AutoCancelCurrentLrConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
}

type LoanOfferPrioritisation struct {
	LoanHeaders []LoanHeader
}

type LoanHeader struct {
	LoanProgram string
	Vendor      string
}

type Lendability struct {
	// quest experimentation enabled matrix which is used to define the percent of users who will be passed
	// based on the PD category and Affinity Category (can be extended)
	// each value represents a percent of users that will be passed for the given PD and Affinity category (which are the joint key)
	EvaluationRuleMatrix map[string]*EvaluationRuleMatrixValue `dynamic:"true,readonlymap" ,quest:"component"`
	Url                  string                                `dynamic:"true"`
	// to define each category of the pd, affinity based on the respective score
	// each map key is the category name and the value is the score threshold below which this category falls
	// [IMP] the map should always be defined in asc order of threshold value as category is calculated based on the first match
	ScoreCategoryDetails *ScoreCategoryDetails
}

type EvaluationRuleMatrixValue struct {
	Value int `dynamic:"true" ,quest:"variable"`
}

type ScoreCategoryDetails struct {
	// upgrade version whenever there is a change in any of the score category threshold maps below
	Version                           string
	PdScoreCategoryThresholdMap       map[string]float64
	AffinityScoreCategoryThresholdMap map[string]float64
}

type EmiComms struct {
	LowBalComms        map[string]*EmiCommsPhase `dynamic:"true"`
	SufficientBalComms map[string]*EmiCommsPhase `dynamic:"true"`
}

type EmiCommsPhase struct {
	// needed for supporting arrays in dynamic config.
	ArrayElement *cfg.DynamicArrayElement `dynamic:"true"`
	// start (inclusive) and end (exclusive) date of the phase relative to the due date
	// Example1: For the phase of 8,7,6,5 days before due date StartDaysPastDueDate should be -8 and EndDaysPastDueDate should be -4
	// Example2: For the phase of 2,3,4 days after due date StartDaysPastDueDate should be 2 and EndDaysPastDueDate should be 5
	StartDaysPastDueDate int32              `dynamic:"true"`
	EndDaysPastDueDate   int32              `dynamic:"true"`
	CommsChannelDetails  *ChannelLevelComms `dynamic:"true"`
}

type ChannelLevelComms struct {
	GtmPopUp *GtmPopUpConfig `dynamic:"true"`
}

type GtmPopUpConfig struct {
	IsEnabled      bool                         `dynamic:"true"`
	Title          *typesPkg.Text               `dynamic:"true"`
	Body           *typesPkg.Text               `dynamic:"true"`
	VisualElement  *typesPkg.VisualElementImage `dynamic:"true"`
	RadialGradient *typesPkg.RadialGradient     `dynamic:"true"`
	CtaList        map[string]*CTA              `dynamic:"true"`
}

type Downtime struct {
	// palPb.Vendor -> *VendorDowntime
	Vendors map[string]*VendorDowntime `dynamic:"true"`
}

type VendorDowntime struct {
	Daily *DailyVendorDowntime `dynamic:"true"`
}

type HomeWidgetV1Config struct {
	// denotes whether the v1 home widget is enabled or not
	IsEnabled bool `dynamic:"true"`
	// would be used if personal loan product needs to be promoted on the home widget.
	// **Note** : decision to promote which loan product (amongst many) on home widget is based on some biz logic which is subject to change from time to time.
	PersonalLoanDisplayConfig *LoanProductLevelHomeWidgetV1DisplayConfig `dynamic:"true"`
	// would be used if LAMF product needs to be promoted on the home widget.
	// **Note** : decision to promote which loan product (amongst many) on home widget is based on some biz logic which is subject to change from time to time.
	LAMFDisplayConfig *LoanProductLevelHomeWidgetV1DisplayConfig `dynamic:"true"`
}

type LoanProductLevelHomeWidgetV1DisplayConfig struct {
	// denotes the home widget config for cases where an active offer exists for the loan product.
	ActiveOfferDisplayConfig *HomeWidgetV1DisplayConfig `dynamic:"true"`
	// denotes the home widget config for cases where no active offer exists for the loan product.
	NoActiveOfferDisplayConfig *HomeWidgetV1DisplayConfig `dynamic:"true"`
	// denotes the config to be used in case we are running some campaign for the loan product.
	// if this is configured then this would be used over other display configs.
	CampaignDisplayConfig *HomeWidgetV1DisplayConfig `dynamic:"true"`
}

type HomeWidgetV1DisplayConfig struct {
	// denotes the heading for widget section like "Get cash today"
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7899-62631&mode=design&t=sU6gvaCI9rGHdd6l-0
	Heading *typesPkg.Text `dynamic:"true"`
	// denotes the image to be used for the vertically aligned primary banner on home widget.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7899-62631&mode=design&t=sU6gvaCI9rGHdd6l-0
	VerticalPrimaryBannerImageUrl string `dynamic:"true"`
	// denotes the image to be used for the horizontally aligned primary banner on home widget.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7548-34721&mode=design&t=sU6gvaCI9rGHdd6l-0
	HorizontalPrimaryBannerImageUrl string `dynamic:"true"`
	// denotes the list of tiles to be displayed on the widget,
	// **Note** : using a map instead of list DS for configuring multiple tiles as dynamic config does not support list DS
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7899-62631&mode=design&t=sU6gvaCI9rGHdd6l-0
	WidgetTiles map[string]*HomeWidgetV1TileConfig `dynamic:"true"`
	// denotes the CTA to be displayed on the home widget.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7548-34721&mode=design&t=sU6gvaCI9rGHdd6l-0
	PrimaryCta *CTA `dynamic:"true"`
}

type CTA struct {
	// needed for supporting arrays in dynamic config.
	ArrayElement *cfg.DynamicArrayElement `dynamic:"true"`
	Text         *typesPkg.Text           `dynamic:"true"`
	BgColor      string                   `dynamic:"true"`
	Deeplink     *deeplinkcfgv2.Deeplink  `dynamic:"true"`
}

type HomeWidgetV1TileConfig struct {
	// needed for supporting arrays in dynamic config.
	ArrayElement *cfg.DynamicArrayElement `dynamic:"true"`
	// denotes the tile heading template like "Low interest starting at"
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7612-61407&mode=design&t=sU6gvaCI9rGHdd6l-0
	HeadingTemplate string `dynamic:"true"`
	// denotes the tile subheading template like "INTEREST_AMOUNT %",
	// variable params like INTEREST_AMOUNT would be updated with appropriate value from offer during the config usage.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7612-61407&mode=design&t=sU6gvaCI9rGHdd6l-0
	SubHeadingTemplate string `dynamic:"true"`
	// denotes the left icon which would be displayed on the tile.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7612-61407&mode=design&t=sU6gvaCI9rGHdd6l-0
	LeftIconUrl string `dynamic:"true"`
	// denotes the right icon which would be displayed on the tile.
	// figma : https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7899-62631&mode=design&t=sU6gvaCI9rGHdd6l-0
	RightIconUrl string `dynamic:"true"`
	// denotes the background color of the tile
	BgColor string `dynamic:"true"`
}

type DailyVendorDowntime struct {
	IsEnable bool `dynamic:"true"`
	// time of the day in IST
	// e.g: "03:00"
	StartTime string `dynamic:"true"`
	// time of the day in IST
	// e.g: "04:00"
	EndTime string `dynamic:"true"`
}

type Application struct {
	Environment string
	Name        string
	ServerName  string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type NotificationMessage struct {
	Title string
	Desc  string
	Icon  string
}

type Aws struct {
	Region string
}

type Vendor struct {
	SftpUploadRemotePath string
}

type EarlySalary struct {
	MinSalaryCredits        int
	MinDaysPostSalaryCredit int
}

type LamfConfig struct {
	FiftyFinConfig        *FiftyFinConfig
	ReleaseSegmentDetails *ReleaseSegmentDetails `dynamic:"true"`
}

type ReleaseSegmentDetails struct {
	Expression string `dynamic:"true"`
}

type FiftyFinConfig struct {
	LoanOfferConfig *LoanOfferConfig
}

type LoanOfferConfig struct {
	MinLoanAmount             float64
	MaxLoanAmount             float64
	MinTenureMonths           int32
	MaxTenureMonths           int32
	GstInPercentage           float64
	InterestRateInPercentage  float64
	MinimumProcessingFee      float64
	MaximumProcessingFee      float64
	ProcessingFeeInPercentage float64
	ValidityDuration          time.Duration
	DiscountFactor            float64
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(true)
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}
	return config, nil
}

type MandateConfig struct {
	// storing LL specific mandate config
	LiquiloansMandateConfig *LiquiloansMandateConfig `dynamic:"true"`
}

type LiquiloansMandateConfig struct {
	// denotes if cool off check is enabled between mandate attempts
	IsMandateCoolOffCheckEnabled bool `dynamic:"true"`
	// denotes minimum cool off duration which has to be adhered between mandate attempts
	MinCoolOffMinutesBetweenMandateAttempts int64
}

type ConsentConfig struct {
	Enabled       bool
	FeatureConfig *app.FeatureConfig `dynamic:"true"`
	// config as deeplink screen name level
	ScreenConfig map[string]*ConsentScreenConfig
}

type ConsentScreenConfig struct {
	Enabled                          bool
	CollectionAttemptCooloffDuration time.Duration
}
