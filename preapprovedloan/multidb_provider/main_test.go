package multidb_provider_test

import (
	"context"
	"flag"
	"os"
	"testing"

	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/idgen"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	defaultValueCalculator "github.com/epifi/gamma/preapprovedloan/calculator/defaultvalue"
	calculatorProvidersWrapper "github.com/epifi/gamma/preapprovedloan/calculator/providers/wrapper"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao/impl"
	"github.com/epifi/gamma/preapprovedloan/multidb_provider"
	de "github.com/epifi/gamma/preapprovedloan/multidb_provider/decision_engine"
	"github.com/epifi/gamma/preapprovedloan/test"
)

var (
	affectedTestTables = []string{
		"loan_offers", "collection_allocations", "collection_leads", "loan_requests", "loan_accounts", "loan_installment_info", "loan_step_executions", "loan_activities", "loan_payment_requests", "loan_offer_eligibility_criteria",
		"loan_installment_payout", "loan_applicants", "fetched_assets",
	}
	dynConf                *genconf.Config
	dbResourceProviderPool *test.DbResourceProviderPool
)

type mockCalculatorProvider struct {
}

func (p *mockCalculatorProvider) GetDefaultValueCalculator(
	ctx context.Context,
	loanOffer *palPb.LoanOffer,
) calculatorTypes.DefaultValueCalculator {
	return defaultValueCalculator.NewCalculator(ctx, loanOffer)
}

func (p *mockCalculatorProvider) GetCalculator(
	ctx context.Context,
	req *calculatorTypes.Request,
) (calculatorTypes.Calculator, error) {
	return calculatorProvidersWrapper.NewProvider().GetCalculator(ctx, req)
}

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	conf, dynConfLocal, teardown := test.InitTestServer()
	dynConf = dynConfLocal
	var dbProviderResourcePoolCleanup func()
	dbResourceProviderPool, dbProviderResourcePoolCleanup = test.NewDbResourceProviderPool(conf.PgDbConfigMap.GetOwnershipToDbConfigMap(), affectedTestTables, 1)

	exitCode := m.Run()

	teardown()
	dbProviderResourcePoolCleanup()

	os.Exit(exitCode)
}

func getMultiDbProviderWithMockGrpcClient(dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB]) multidb_provider.IMultiDbProvider {
	loanAccountsDao := impl.NewCrdbLoanAccountsDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)
	loanOfferDao := impl.NewCrdbLoanOfferDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)
	loanRequestDao := impl.NewCrdbLoanRequestsDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)
	loecDao := impl.NewCrdbLoanOfferEligibilityCriteriaDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)
	provider := multidb_provider.NewMultiDBProvider(de.NewSimpleOffersDecisionEngine(dynConf.Flags()), loanAccountsDao,
		loanOfferDao, loanRequestDao, loecDao)
	return provider
}
