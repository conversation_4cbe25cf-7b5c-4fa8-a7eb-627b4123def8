package test

import (
	"log"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/preapprovedloan/config"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	palWorkerConfig "github.com/epifi/gamma/preapprovedloan/config/worker"
	palWorkerGenConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
)

// InitTestServer instantiates necessary config needed for unit tests.
func InitTestServer() (*config.Config, *genconf.Config, func()) {
	// Init config
	conf, err := config.Load()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	// Init dynamic config
	genConf, err := genconf.Load()
	if err != nil {
		log.Fatal("failed to load safe config", err)
	}

	return conf, genConf, func() {
		_ = logger.Log.Sync()
	}
}

// InitTestWorker initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
// nolint: dupl
func InitTestWorker() (*palWorkerConfig.Config, *palWorkerGenConf.Config, func()) {
	var err error
	// Init config
	conf, err := palWorkerConfig.LoadConfig()
	if err != nil {
		log.Fatal("failed to load config", err)
	}

	// Init dynamic config
	dynConf, err := palWorkerGenConf.Load()
	if err != nil {
		log.Fatal("failed to load safe config", err)
	}

	// Setup logger
	logger.Init(conf.Application.Environment)
	err = epifitemporal.InitWorkflowParams(conf.WorkflowParamsList.GetWorkflowParamsMap())
	if err != nil {
		logger.Panic("failed to load workflow params", zap.Error(err))
	}

	err = epifitemporal.InitDefaultActivityParams(conf.DefaultActivityParamsList.GetActivityParamsMap())
	if err != nil {
		logger.Panic("failed to load default activity params", zap.Error(err))
	}

	return conf, dynConf, func() {
		_ = logger.Log.Sync()
	}
}
