// nolint:goimports
package stock_guardian

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common"
	commonVgPb "github.com/epifi/be-common/api/vendorgateway"

	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	brePb "github.com/epifi/gamma/api/bre"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/typesv2"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

// nolint:funlen
func (p *Processor) SgRtDistFiBreCheck(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palpb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)

		user, userErr := p.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
		if userErr != nil {
			lg.Error("failed to fetch user by actor ID", zap.Error(userErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch user by actor ID, err: %v", userErr))
		}

		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("failed to fetch loan request by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request by id, err: %v", lrErr))
		}

		var loecs []*palPb.LoanOfferEligibilityCriteria
		var loecsErr error
		ctx = palActivity.RunWithOwnershipAndReset(ctx, common.Ownership_LOANS_STOCK_GUARDIAN_LSP, func(ctx context.Context) {
			loecs, loecsErr = p.loecDao.GetByActorIdLoanProgramsAndStatuses(ctx, lse.GetActorId(), []palpb.LoanProgram{palpb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION},
				[]palpb.LoanOfferEligibilityCriteriaStatus{palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED},
				0, true)
		})
		if loecsErr != nil {
			lg.Error("failed to fetch latest loec", zap.Error(loecsErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch latest loec, err: %v", loecsErr))
		}
		latestLoec := loecs[0]

		addressLse, err := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
			palpb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY, palpb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS)
		if err != nil {
			lg.Error("failed to fetch add details lse", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch address details lse, err: %v", err))
		}

		employmentLse, employmentLseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
			palpb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY, palpb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT)
		if employmentLseErr != nil {
			lg.Error("failed to fetch add details lse", zap.Error(employmentLseErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch employment details lse, err: %v", employmentLseErr))
		}

		isFullKyc, err := p.rpcHelper.IsActorFullKyc(ctx, req.GetLoanStep().GetActorId(), commonVgPb.Vendor_FEDERAL_BANK)
		if err != nil {
			lg.Error("failed to get the IsActorFullKyc resp in RtDistFiBreCheck", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get the IsActorFullKyc resp in RtDistFiBreCheck, err: %v", err))
		}

		breResp, breErr := p.breClient.GetLoanDecisioning(ctx, &brePb.GetLoanDecisioningRequest{
			ActorId:        lse.GetActorId(),
			Vendor:         commonVgPb.Vendor_STOCK_GUARDIAN_LSP,
			Dob:            user.GetProfile().GetDateOfBirth(),
			EmploymentType: employmentLse.GetDetails().GetOnboardingData().GetEmploymentDetails().GetOccupation(),
			MonthlyIncome:  employmentLse.GetDetails().GetOnboardingData().GetEmploymentDetails().GetMonthlyIncome(),
			Name:           user.GetProfile().GetGivenName(),
			Gender:         user.GetProfile().GetGivenGender(),
			Pan:            user.GetProfile().GetPAN(),
			Address:        addressLse.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails(),
			EmployerName:   employmentLse.GetDetails().GetOnboardingData().GetEmploymentDetails().GetOrganizationName(),
			WorkEmail:      employmentLse.GetDetails().GetOnboardingData().GetEmploymentDetails().GetWorkEmail(),
			LoanProgram:    palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			SchemeId:       latestLoec.GetLoanScheme(),
			BatchId:        latestLoec.GetBatchId(),
			KycLevel:       lo.Ternary(isFullKyc, typesv2.KYCLevel_FULL_KYC, typesv2.KYCLevel_MIN_KYC),
			PolicyParams:   latestLoec.GetPolicyParams(),
		})
		if te := epifigrpc.RPCError(breResp, breErr); te != nil {
			lg.Error("failed to call GetLoanDecisioning from bre client", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to call GetLoanDecisioning from bre client, err:%v", te))
		}

		if latestLoec.GetVendorResponse() == nil {
			latestLoec.VendorResponse = &palpb.VendorResponse{
				VendorResponseTimestamp: timestampPb.New(time.Now().In(datetime.IST)),
				RawFinalBreResponse:     breResp.GetRawBreResponse(),
			}
		} else {
			latestLoec.GetVendorResponse().VendorResponseTimestamp = timestampPb.New(time.Now().In(datetime.IST))
			latestLoec.GetVendorResponse().RawFinalBreResponse = breResp.GetRawBreResponse()
		}
		latestLoec.LoanScheme = breResp.GetSchemeId()
		latestLoec.BatchId = breResp.GetBatchId()
		latestLoec.PolicyParams = breResp.GetPolicyParams()

		switch breResp.GetLoanDecision() {
		case brePb.Decision_DECISION_APPROVED:
			var oldOffer []*palpb.LoanOffer
			var oldOfferErr error
			ctx = palActivity.RunWithOwnershipAndReset(ctx, common.Ownership_LOANS_STOCK_GUARDIAN_LSP, func(ctx context.Context) {
				oldOffer, oldOfferErr = p.loanOfferDao.GetActiveOffersByActorIdAndLoanPrograms(ctx, lse.GetActorId(), []palpb.LoanProgram{palpb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION})
			})
			if oldOfferErr != nil && !errors.Is(oldOfferErr, epifierrors.ErrRecordNotFound) {
				lg.Error("failed to fetch old loan offer for actor and loan program", zap.Error(oldOfferErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch old loan offer for actor and loan program, err: %v", oldOfferErr))
			}
			var alreadyActiveOffer *palpb.LoanOffer
			if len(oldOffer) > 0 {
				alreadyActiveOffer = oldOffer[0]
			}
			loanOffer := makeLoanOfferFromBreOutput(breResp, latestLoec.GetId())

			// set the next action in loan request to eligibility success screen
			deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetVendor(), LoanProgram: req.GetLoanProgram()})
			lr.NextAction = deeplinkProvider.GetEligibilitySuccessScreen(ctx, &palEnumFePb.LoanHeader{
				LoanProgram: palEnumFePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
				Vendor:      palEnumFePb.Vendor_STOCK_GUARDIAN_LSP,
			},
				loanOffer.GetOfferConstraints().GetMaxLoanAmount(),
				loanOffer.GetProcessingInfo().GetInterestRate()[0].GetPercentage(),
				loanOffer.GetId(), loanOffer)

			var txnErr error
			// txn to make changes for loans_stock_guardian ownership
			txnErr = p.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
				// deactivate old offer
				if alreadyActiveOffer != nil {
					if err := p.loanOfferDao.DeactivateLoanOffer(txnCtx, alreadyActiveOffer.GetId()); err != nil {
						return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to deactivate already existing offer, err: %v", err))
					}
				}
				lo, createErr := p.loanOfferDao.Create(txnCtx, loanOffer)
				if createErr != nil {
					return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to create loan offer, err: %v", createErr))
				}

				if latestLoec.GetVendorResponse() == nil {
					latestLoec.VendorResponse = &palpb.VendorResponse{
						VendorResponseTimestamp: timestampPb.New(time.Now().In(datetime.IST)),
						RawFinalBreResponse:     breResp.GetRawBreResponse(),
					}
				} else {
					latestLoec.GetVendorResponse().VendorResponseTimestamp = timestampPb.New(time.Now().In(datetime.IST))
					latestLoec.GetVendorResponse().RawFinalBreResponse = breResp.GetRawBreResponse()
				}

				latestLoec.OfferId = lo.GetId()
				latestLoec.LoanScheme = breResp.GetSchemeId()
				latestLoec.BatchId = breResp.GetBatchId()
				latestLoec.PolicyParams = breResp.GetPolicyParams()
				latestLoec.SubStatus = palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI
				latestLoec.Status = palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED
				if err := p.loecDao.Update(txnCtx, latestLoec, []palpb.LoanOfferEligibilityCriteriaFieldMask{
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_OFFER_ID,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_BATCH_ID,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_LOAN_SCHEME,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_RESPONSE,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_POLICY_PARAMS,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
				}); err != nil {
					return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loec for approved, err: %v", err))
				}
				return nil
			})
			if txnErr != nil {
				lg.Error("failed to update the lo and loec in transaction", zap.Error(txnErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update the lo and loec in transaction, err: %v", txnErr))
			}

			txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, p.txnExecutorProvider)
			if txnExecErr != nil {
				lg.Error("failed to get txn executor by ownership", zap.Error(txnExecErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get txn executor by ownership, err: %v", txnExecErr))
			}

			// txn to make changes for epifi_tech ownership
			txnErr = txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
				lse.Status = palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
				if err = p.loanStepExecutionDao.Update(txnCtx, lse, []palpb.LoanStepExecutionFieldMask{
					palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				}); err != nil {
					return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lse for approved, err: %v", err))
				}

				updateErr := p.loanRequestDao.Update(txnCtx, lr, []palpb.LoanRequestFieldMask{palpb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION})
				if updateErr != nil {
					return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lr next action in txn, err: %v", updateErr))
				}
				return nil
			})
			if txnErr != nil {
				lg.Error("failed to update the lse and lr in transaction", zap.Error(txnErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update the lse and lr in transaction, err: %v", txnErr))
			}
			writeErr := p.writeRawResponseToS3(ctx, breResp.GetRawBreResponse(), latestLoec.GetId(), latestLoec.GetActorId(), false)
			if writeErr != nil {
				lg.Error("failed to upload approved bre data to s3", zap.Error(writeErr))
			}

		case brePb.Decision_DECISION_REJECTED:
			lse.Status = palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
			if lseUpdateErr := p.loanStepExecutionDao.Update(ctx, lse, []palpb.LoanStepExecutionFieldMask{
				palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			}); lseUpdateErr != nil {
				lg.Error("failed to update lse for rejected", zap.Error(lseUpdateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lse for rejected, err: %v", lseUpdateErr))
			}

			if latestLoec.GetVendorResponse() == nil {
				latestLoec.VendorResponse = &palpb.VendorResponse{
					VendorResponseTimestamp: timestampPb.New(time.Now().In(datetime.IST)),
					RawFinalBreResponse:     breResp.GetRawBreResponse(),
				}
			} else {
				latestLoec.GetVendorResponse().VendorResponseTimestamp = timestampPb.New(time.Now().In(datetime.IST))
				latestLoec.GetVendorResponse().RawFinalBreResponse = breResp.GetRawBreResponse()
			}
			latestLoec.LoanScheme = breResp.GetSchemeId()
			latestLoec.BatchId = breResp.GetBatchId()
			latestLoec.PolicyParams = breResp.GetPolicyParams()
			latestLoec.Status = palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
			latestLoec.SubStatus = palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI
			ctx = palActivity.RunWithOwnershipAndReset(ctx, common.Ownership_LOANS_STOCK_GUARDIAN_LSP, func(ctx context.Context) {
				err = p.loecDao.Update(ctx, latestLoec, []palpb.LoanOfferEligibilityCriteriaFieldMask{
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_BATCH_ID,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_LOAN_SCHEME,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_RESPONSE,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_POLICY_PARAMS,
				})
			})
			if err != nil {
				lg.Error("failed to update loec for approved", zap.Error(err))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loec for approved, err: %v", err))
			}
			writeErr := p.writeRawResponseToS3(ctx, breResp.GetRawBreResponse(), latestLoec.GetId(), latestLoec.GetActorId(), false)
			if writeErr != nil {
				lg.Error("failed to upload rejected bre data to s3", zap.Error(writeErr))
			}

		default:
			lg.Error("unhandled loan decisioning", zap.String("decision", breResp.GetLoanDecision().String()))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unhandled loan decisioning, decision: %s", breResp.GetLoanDecision().String()))
		}
		res.LoanStep = lse
		return res, nil
	})
	return actRes, actErr
}

func makeLoanOfferFromBreOutput(od *brePb.GetLoanDecisioningResponse, loecId string) *palpb.LoanOffer {
	return &palpb.LoanOffer{
		ActorId:       od.GetActorId(),
		VendorOfferId: uuid.New().String(),
		Vendor:        od.GetVendor(),
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount:   od.GetOfferDetails().GetMaxAmount(),
			MaxEmiAmount:    od.GetOfferDetails().GetMaxEmiAmount(),
			MaxTenureMonths: od.GetOfferDetails().GetMaxTenureInMonths(),
			MinLoanAmount:   od.GetOfferDetails().GetMinAmount(),
			MinTenureMonths: od.GetOfferDetails().GetMinTenureInMonths(),
		},
		ProcessingInfo: &palPb.OfferProcessingInfo{
			Gst: od.GetOfferDetails().GetGstPercentage(),
			InterestRate: []*palPb.RangeData{
				{
					Start: 0,
					End:   1000000,
					Value: &palPb.RangeData_Percentage{
						Percentage: od.GetOfferDetails().GetInterestPercentage(),
					},
				},
			},
			ProcessingFee: []*palPb.RangeData{
				{
					Start: 0,
					End:   1000000,
					Value: &palPb.RangeData_Percentage{
						Percentage: od.GetOfferDetails().GetProcessingFeePercentage(),
					},
				},
			},
		},
		ValidSince:                     timestampPb.New(time.Now().In(datetime.IST)),
		ValidTill:                      od.GetOfferDetails().GetValidTill(),
		LoanOfferEligibilityCriteriaId: loecId,
		LoanProgram:                    od.GetLoanProgram(),
	}
}
