package abfl_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
)

var lse3 = &preapprovedloan.LoanStepExecution{
	Id:      "lse-id-1",
	RefId:   "lr-id-1",
	ActorId: "actor-id-1",
	Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
	Details: &preapprovedloan.LoanStepExecutionDetails{
		VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
			Abfl: &preapprovedloan.AbflLoanDisbursementData{
				LoanNumber: "1234",
				DealNumber: "1234",
			},
		},
	},
}

func TestProcessor_AbflPerformDisbursement(t *testing.T) {
	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}

	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.PalActivityResponse
		mockFunc  func(md *processorMocks)
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should fail with transient error when dao call to loan requests fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse1.GetRefId()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when api call to perform LoanDisbursement",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse1.GetRefId()).Return(loanRequest1, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE).Return(lsemandate, nil)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						AccountNo: "12345",
						IfscCode:  "12345",
					},
				}, nil)
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanStepExecutionFieldMask{preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}).Return(nil)
				md.abflVgClient.EXPECT().LoanDisbursement(gomock.Any(), gomock.Any()).Return(
					&abfl.LoanDisbursementResponse{}, nil,
				)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when dao call to lse update fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber:   "1234",
								DealNumber:   "1234",
								LoanUniqueId: "u-id-1",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse1.GetRefId()).Return(loanRequest1, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE).Return(lsemandate, nil)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						AccountNo: "12345",
						IfscCode:  "12345",
					},
				}, nil)
				md.abflVgClient.EXPECT().LoanDisbursement(gomock.Any(), gomock.Any()).Return(
					&abfl.LoanDisbursementResponse{
						Status: rpc.StatusOk(),
					}, nil,
				)
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanStepExecutionFieldMask{
					preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS}).Return(epifierrors.ErrTransient)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should run successfully",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber:   "1234",
								DealNumber:   "1234",
								LoanUniqueId: "u-id-1",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse1.GetRefId()).Return(loanRequest1, nil)
				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), gomock.Any(), gomock.Any(), preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE).Return(lsemandate, nil)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						AccountNo: "12345",
						IfscCode:  "12345",
					},
				}, nil)
				md.abflVgClient.EXPECT().LoanDisbursement(gomock.Any(), gomock.Any()).Return(
					&abfl.LoanDisbursementResponse{
						Status: rpc.StatusOk(),
					}, nil,
				)
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanStepExecutionFieldMask{
					preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS}).Return(nil)
			},
			wantErr: false,
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber:   "1234",
								DealNumber:   "1234",
								LoanUniqueId: "u-id-1",
							},
						},
					},
				},
			},
			assertErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockFields := initProcessorMocks(ctrl)
			tt.mockFunc(mockFields)
			p := newActivityProcessorWithMocks(mockFields)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.AbflPerformDisbursement, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("AbflPerformDisbursement() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("AbflPerformDisbursement() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("AbflPerformDisbursement() error = %v assertion failed", err)
				return
			case !proto.Equal(result, tt.want):
				t.Errorf("AbflPerformDisbursement() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
func TestProcessor_AbflPollDisbursementStatus(t *testing.T) {
	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}

	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.PalActivityResponse
		mockFunc  func(md *processorMocks)
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should fail with transient error when dao call to loan requests fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(lse3, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse2.GetRefId()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when  call to loandisbursmentStatus gives code 51 fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(lse3, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse2.GetRefId()).Return(loanRequest1, nil)
				md.abflVgClient.EXPECT().LoanDisbursementStatus(gomock.Any(), &abfl.LoanDisbursementStatusRequest{
					Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ABFL},
					AccountId: loanRequest1.GetVendorRequestId(),
					UniqueId:  lse3.GetDetails().GetAbfl().GetLoanUniqueId(),
				}).Return(
					&abfl.LoanDisbursementStatusResponse{
						Status: rpc.ExtendedStatusInProgress(),
					}, nil,
				)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when call to loandisbursmentStatus fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(lse3, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse2.GetRefId()).Return(loanRequest1, nil)
				md.abflVgClient.EXPECT().LoanDisbursementStatus(gomock.Any(), &abfl.LoanDisbursementStatusRequest{
					Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ABFL},
					AccountId: loanRequest1.GetVendorRequestId(),
					UniqueId:  lse3.GetDetails().GetAbfl().GetLoanUniqueId(),
				}).Return(
					&abfl.LoanDisbursementStatusResponse{
						Status: rpc.StatusOk(),
					}, epifierrors.ErrTransient,
				)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when dao call to lse update fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(lse3, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse2.GetRefId()).Return(loanRequest1, nil)
				md.abflVgClient.EXPECT().LoanDisbursementStatus(gomock.Any(), &abfl.LoanDisbursementStatusRequest{
					Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ABFL},
					AccountId: loanRequest1.GetVendorRequestId(),
					UniqueId:  lse3.GetDetails().GetAbfl().GetLoanUniqueId(),
				}).Return(
					&abfl.LoanDisbursementStatusResponse{
						Status: rpc.StatusOk(),
					}, nil,
				)
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanStepExecutionFieldMask{preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}).Return(epifierrors.ErrTransient).AnyTimes()
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should run successfully",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(lse3, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse2.GetRefId()).Return(loanRequest1, nil)
				md.abflVgClient.EXPECT().LoanDisbursementStatus(gomock.Any(), &abfl.LoanDisbursementStatusRequest{
					Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ABFL},
					AccountId: loanRequest1.GetVendorRequestId(),
					UniqueId:  lse3.GetDetails().GetAbfl().GetLoanUniqueId(),
				}).Return(
					&abfl.LoanDisbursementStatusResponse{
						Status: rpc.StatusOk(),
					}, nil,
				)
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanStepExecutionFieldMask{preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}).Return(nil)
			},
			wantErr: false,
			want: &palActivityPb.PalActivityResponse{
				LoanStep: lse3,
			},
			assertErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockFields := initProcessorMocks(ctrl)
			tt.mockFunc(mockFields)
			p := newActivityProcessorWithMocks(mockFields)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.AbflPollDisbursementStatus, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("AbflPollDisbursementStatus() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("AbflPollDisbursementStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("AbflPollDisbursementStatus() error = %v assertion failed", err)
				return
			case !proto.Equal(result, tt.want):
				t.Errorf("AbflPollDisbursementStatus() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
func TestProcessor_AbflCreateLoanAccount(t *testing.T) {
	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}

	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.PalActivityResponse
		mockFunc  func(md *processorMocks)
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should fail with transient error when dao call to loan requests fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse3.GetRefId()).Return(nil, epifierrors.ErrRequestCanceled)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when dao call to perform GetorCreate offer fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse3.GetRefId()).Return(loanRequest1, nil)
				md.loanAccountDao.EXPECT().GetOrCreate(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRequestCanceled)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when dao call to loan installment info fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse3.GetRefId()).Return(loanRequest1, nil)
				md.loanAccountDao.EXPECT().GetOrCreate(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanAccount{Id: "1234"}, nil)
				md.loanInstallmentInfoDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRequestCanceled)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when dao call to loan installment info fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse3.GetRefId()).Return(loanRequest1, nil)
				md.loanAccountDao.EXPECT().GetOrCreate(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanAccount{Id: "1234"}, nil)
				md.loanInstallmentInfoDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrDowntimeExpected)

			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when dao call to loan request fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse3.GetRefId()).Return(loanRequest1, nil)
				md.loanAccountDao.EXPECT().GetOrCreate(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanAccount{Id: "1234"}, nil)
				md.loanInstallmentInfoDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanInstallmentInfo{}, nil)
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanRequestFieldMask{preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER}).Return(epifierrors.ErrTransient)

			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when dao call to loan offer deactive offer fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse3.GetRefId()).Return(loanRequest1, nil)
				md.loanAccountDao.EXPECT().GetOrCreate(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanAccount{Id: "1234"}, nil)
				md.loanInstallmentInfoDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanInstallmentInfo{
					Id: "12345",
				}, nil)
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanRequestFieldMask{preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER}).Return(nil)
				md.loanOfferDao.EXPECT().DeactivateLoanOffer(gomock.Any(), gomock.Any()).Return(epifierrors.ErrTransient)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when dao call to lse update fail",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse3.GetRefId()).Return(loanRequest1, nil)
				md.loanAccountDao.EXPECT().GetOrCreate(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanAccount{Id: "1234"}, nil)
				md.loanInstallmentInfoDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanInstallmentInfo{Id: "12345"}, nil)
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanRequestFieldMask{preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER}).Return(nil)
				md.loanOfferDao.EXPECT().DeactivateLoanOffer(gomock.Any(), gomock.Any()).Return(nil)
				md.loanActivityDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrTransient)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should run successfully",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_ABFL,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse3,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(&preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				}, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse3.GetRefId()).Return(loanRequest1, nil)
				md.loanAccountDao.EXPECT().GetOrCreate(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanAccount{Id: "1234"}, nil)
				md.loanInstallmentInfoDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanInstallmentInfo{Id: "12345"}, nil)
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanRequestFieldMask{preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER}).Return(nil)
				md.loanOfferDao.EXPECT().DeactivateLoanOffer(gomock.Any(), gomock.Any()).Return(nil)
				md.loanActivityDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanActivity{}, nil)
			},
			wantErr: false,
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
					Details: &preapprovedloan.LoanStepExecutionDetails{
						VendorSpecificDetails: &preapprovedloan.LoanStepExecutionDetails_Abfl{
							Abfl: &preapprovedloan.AbflLoanDisbursementData{
								LoanNumber: "1234",
								DealNumber: "1234",
							},
						},
					},
				},
			},
			assertErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockFields := initProcessorMocks(ctrl)
			tt.mockFunc(mockFields)
			p := newActivityProcessorWithMocks(mockFields)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.AbflCreateLoanAccount, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("AbflCreateLoanAccount() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("AbflCreateLoanAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("AbflCreateLoanAccount() error = %v assertion failed", err)
				return
			case !proto.Equal(result, tt.want):
				t.Errorf("AbflCreateLoanAccount() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
