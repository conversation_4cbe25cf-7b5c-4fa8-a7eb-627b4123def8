package provider

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/logger"

	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/preapprovedloan/landing_provider"

	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	dataExistenceManager "github.com/epifi/gamma/preapprovedloan/data_existence_manager"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

type HomeScreenBodySectionProvider struct {
	landingProvider      landing_provider.ILandingProvider
	rpcHelper            *helper.RpcHelper
	dynConf              *genconf.Config
	dataExistenceManager dataExistenceManager.Manager
}

func NewHomeScreenBodySectionProvider(
	landingProvider landing_provider.ILandingProvider,
	rpcHelper *helper.RpcHelper,
	dynConf *genconf.Config,
	dataExistenceManager dataExistenceManager.Manager,
) *HomeScreenBodySectionProvider {
	return &HomeScreenBodySectionProvider{
		landingProvider:      landingProvider,
		rpcHelper:            rpcHelper,
		dynConf:              dynConf,
		dataExistenceManager: dataExistenceManager,
	}
}

// compile time check to ensure HomeScreenBodySectionProvider implements IDynamicElementsProvider
var _ IDynamicElementsProvider = &HomeScreenBodySectionProvider{}

func (h *HomeScreenBodySectionProvider) GetDynamicElements(ctx context.Context, req *GetDynamicElementsRequest) ([]*dePb.DynamicElement, error) {
	dataExistenceCacheRes := h.dataExistenceManager.GetOrRefreshLoanDataExistenceCache(ctx, req.ActorId)
	dataRes, dataErr := h.landingProvider.GetLandingPageDataForActor(ctx,
		&landing_provider.GetLandingPageDataForActorRequest{
			ActorId:                    req.ActorId,
			OwnershipFilterMap:         dataExistenceCacheRes.GetDataExistenceMap(),
			OwnershipFilterMapForLoecs: dataExistenceCacheRes.GetLoecDataExistenceMap(),
		})
	if dataErr != nil {
		return nil, fmt.Errorf("failed to fetch landing page data for actor from multiDB provider, err : %w", dataErr)
	}

	if !h.dynConf.EnableDynamicElementForAllUsers() {
		// if dynamic element is not enabled for all users, return not found if
		// 1. user does not have an active loan offer
		// 2. user has already requested for a loan
		isAnyLoanOfferActiveNow := false
		for _, loanOption := range dataRes.LoanOptions {
			if loanOption.GetLoanOffer() != nil && loanOption.GetLoanOffer().IsActiveNow() {
				isAnyLoanOfferActiveNow = true
				break
			}
		}
		if !isAnyLoanOfferActiveNow || len(dataRes.LoanRequests) > 0 {
			return nil, nil
		}
	}

	isUserInLAMFBannerSegment, err := h.rpcHelper.IsActorInAnyOfTheSegments(ctx, req.ActorId, h.dynConf.AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement().ToStringArray())
	if err != nil {
		// intentionally muting the error as we would show a default banner in such a case to avoid loan's home banner from getting removed due to this failure.
		logger.WarnWithCtx(ctx, "error in checking if user is a part of LAMF banner segment", zap.String(logger.ACTOR_ID_V2, req.ActorId), zap.Error(err))
	}

	switch {
	case isUserInLAMFBannerSegment:
		return []*dePb.DynamicElement{h.getLAMFDynamicElement()}, nil
	case h.dynConf.IsCampaignRelatedHomeDynamicElementEnabled():
		return []*dePb.DynamicElement{h.getCampaignDynamicElement()}, nil
	default:
		return []*dePb.DynamicElement{h.getDefaultDynamicElement()}, nil
	}
}

// nolint : dupl
func (s *HomeScreenBodySectionProvider) getDefaultDynamicElement() *dePb.DynamicElement {
	return &dePb.DynamicElement{
		OwnerService:  typesPb.ServiceName_PRE_APPROVED_LOAN_SERVICE,
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_ScrollableBanner{
				ScrollableBanner: &dePb.ScrollableBannerElementContent{
					Header: &dePb.BannerHeader{
						Title: []*commontypes.Text{
							{
								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Get an",
								},
								FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
								FontColorOpacity: 60,
							},
							{
								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Instant",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
							},
							{
								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Loan",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
							},
						},
						Cta: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								{
									FontColor: "#FFFFFF",
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "In less \nthan 3 min",
									},
									FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
								},
							},
							RightIcon: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/banner-right-arrow.png",
							},
							Deeplink: getPlLandingScreenDeeplinkWithEntryPointData(),
						},
					},
					ScrollingElements: []*dePb.BannerSingleShapeElement{
						{
							Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_1,
							Image: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/banner-money-bag.png",
							},
							Title: &commontypes.Text{

								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Get up to\n5 Lakh",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#478295",
								},
							},
							Deeplink: getPlLandingScreenDeeplinkWithEntryPointData(),
						},
						{
							Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_4,
							Image: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/zero_pre_closure_fees.png",
							},
							Title: &commontypes.Text{

								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "No pre-closure\nfees",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#478295",
								},
							},
							Deeplink: getPlLandingScreenDeeplinkWithEntryPointData(),
						},
						{
							Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_2,
							Image: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/banner_percentage_sign.png",
							},
							Title: &commontypes.Text{

								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Low interest\n rate",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#478295",
								},
							},
							Deeplink: getPlLandingScreenDeeplinkWithEntryPointData(),
						},
					},
					BgColour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_RadialGradient{
							RadialGradient: &widget.RadialGradient{
								Colours: []string{"#7FBECE", "#478295"},
							},
						},
					},
				},
			},
		},
	}
}

// nolint : dupl
func (s *HomeScreenBodySectionProvider) getCampaignDynamicElement() *dePb.DynamicElement {
	return &dePb.DynamicElement{
		OwnerService:  typesPb.ServiceName_PRE_APPROVED_LOAN_SERVICE,
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_ScrollableBanner{
				ScrollableBanner: &dePb.ScrollableBannerElementContent{
					Header: &dePb.BannerHeader{
						Title: []*commontypes.Text{
							{
								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Get an",
								},
								FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
								FontColorOpacity: 60,
							},
							{
								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Instant",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
							},
							{
								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Loan",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
							},
						},
						Cta: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								{
									FontColor: "#FFFFFF",
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "In less \nthan 3 min",
									},
									FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
								},
							},
							Deeplink: getPlLandingScreenDeeplinkWithEntryPointData(),
						},
					},
					ScrollingElements: []*dePb.BannerSingleShapeElement{
						{
							Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_1,
							Image: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/goldenMoneyBag.png",
							},
							Title: &commontypes.Text{

								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Get up to\n5 Lakh",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#546196",
								},
							},
							Deeplink: getPlLandingScreenDeeplinkWithEntryPointData(),
						},
						{
							Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_4,
							Image: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/goldenZeroPreclosureFees.png",
							},
							Title: &commontypes.Text{

								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "No pre-closure\nfees",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#546196",
								},
							},
							Deeplink: getPlLandingScreenDeeplinkWithEntryPointData(),
						},
						{
							Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_2,
							Image: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/goldenPercentSymbol.png",
							},
							Title: &commontypes.Text{

								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Low interest\n rate",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#546196",
								},
							},
							Deeplink: getPlLandingScreenDeeplinkWithEntryPointData(),
						},
					},
					BgColour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_RadialGradient{
							RadialGradient: &widget.RadialGradient{
								Colours: []string{"#9AB0F2", "#2E3973"},
							},
						},
					},
				},
			},
		},
	}
}

// nolint : dupl
func (s *HomeScreenBodySectionProvider) getLAMFDynamicElement() *dePb.DynamicElement {
	return &dePb.DynamicElement{
		OwnerService:  typesPb.ServiceName_PRE_APPROVED_LOAN_SERVICE,
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_ScrollableBanner{
				ScrollableBanner: &dePb.ScrollableBannerElementContent{
					Header: &dePb.BannerHeader{
						Title: []*commontypes.Text{
							{
								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Loan",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
							},
							{
								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "against",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
							},
							{
								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "MFs",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
							},
						},
						Cta: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								{
									FontColor: "#FFFFFF",
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "Get in under\n3 mins",
									},
									FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
								},
							},
							Deeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
								ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
									PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
										LoanHeader: &pal_enums.LoanHeader{
											LoanProgram: pal_enums.LoanProgram_LOAN_PROGRAM_LAMF,
											Vendor:      pal_enums.Vendor_FIFTYFIN,
											LoanType:    pal_enums.LoanType_LOAN_TYPE_SECURED_LOAN,
										},
									},
								},
							},
						},
					},
					ScrollingElements: []*dePb.BannerSingleShapeElement{
						{
							Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_1,
							Image: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/coinsBlueBag.png",
							},
							Title: &commontypes.Text{

								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Instant Loan\nagainst MFs",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#478295",
								},
							},
							Deeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
								ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
									PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
										LoanHeader: &pal_enums.LoanHeader{
											LoanProgram: pal_enums.LoanProgram_LOAN_PROGRAM_LAMF,
											Vendor:      pal_enums.Vendor_FIFTYFIN,
											LoanType:    pal_enums.LoanType_LOAN_TYPE_SECURED_LOAN,
										},
									},
								},
							},
						},
						{
							Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_4,
							Image: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/green_percentage_image.png",
							},
							Title: &commontypes.Text{

								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Flat 10.5%\ninterest rate",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#478295",
								},
							},
							Deeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
								ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
									PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
										LoanHeader: &pal_enums.LoanHeader{
											LoanProgram: pal_enums.LoanProgram_LOAN_PROGRAM_LAMF,
											Vendor:      pal_enums.Vendor_FIFTYFIN,
											LoanType:    pal_enums.LoanType_LOAN_TYPE_SECURED_LOAN,
										},
									},
								},
							},
						},
						{
							Shape: dePb.BannerSingleShapeElement_SHAPE_STAMP_2,
							Image: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/riskometer_image.png",
							},
							Title: &commontypes.Text{

								FontColor: "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "No credit\nscore needed",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#478295",
								},
							},
							Deeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
								ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
									PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
										LoanHeader: &pal_enums.LoanHeader{
											LoanProgram: pal_enums.LoanProgram_LOAN_PROGRAM_LAMF,
											Vendor:      pal_enums.Vendor_FIFTYFIN,
											LoanType:    pal_enums.LoanType_LOAN_TYPE_SECURED_LOAN,
										},
									},
								},
							},
						},
					},
					BgColour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_RadialGradient{
							RadialGradient: &widget.RadialGradient{
								Colours: []string{"#7FBECE", "#478295"},
							},
						},
					},
				},
			},
		},
	}
}

func getPlLandingScreenDeeplinkWithEntryPointData() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
			PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
				LoanHeader: &pal_enums.LoanHeader{
					EventData: &pal_enums.EventData{EntryPoint: pal_enums.EntryPoint_ENTRY_POINT_HOME_BANNER},
				},
			},
		},
	}
}
