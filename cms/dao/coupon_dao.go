package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	gormv2 "gorm.io/gorm"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	cmsPb "github.com/epifi/gamma/api/cms"
	"github.com/epifi/gamma/cms/dao/model"
)

var _ CouponDao = &CouponDaoImpl{}

type CouponDaoImpl struct {
	DB *gormv2.DB
}

func NewCouponDao(db pkgTypes.CmsPGDB) *CouponDaoImpl {
	return &CouponDaoImpl{DB: db}
}

var couponColumnNames = map[cmsPb.CouponFieldMask]string{
	cmsPb.CouponFieldMask_COUPON_FIELD_MASK_INVENTORY_LEFT: "inventory_left",
}

func (c *CouponDaoImpl) GetAvailableCouponBySkuIdWithLock(ctx context.Context, skuId string) (*cmsPb.Coupon, error) {
	defer metric_util.TrackDuration("cms/dao", "CouponDaoImpl", "GetAvailableCouponBySkuIdWithLock", time.Now())
	db, ok := gormctxv2.FromContextWithLock(ctx)
	if !ok {
		return nil, fmt.Errorf("failed to take lock on coupons table")
	}
	coupon := &model.Coupon{}
	res := db.Where("sku_id=? and inventory_left>0", skuId).Order("created_at ASC").First(&coupon)
	if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch availabe coupon by sku id, err : %w", res.Error)
	}
	return coupon.GetCouponProto(), nil
}

func (c *CouponDaoImpl) UpdateCoupon(ctx context.Context, coupon *cmsPb.Coupon, couponFieldMask []cmsPb.CouponFieldMask) error {
	defer metric_util.TrackDuration("cms/dao", "CouponDaoImpl", "UpdateCoupon", time.Now())

	if coupon.GetId() == "" {
		return fmt.Errorf("id cannot be empty for update query")
	}
	updateColumns := getCouponColumnNames(couponFieldMask)
	if len(updateColumns) == 0 {
		return fmt.Errorf("no columns to update")
	}

	db := gormctxv2.FromContextOrDefault(ctx, c.DB)

	couponModel := model.NewCoupon(coupon)
	res := db.Model(couponModel).Where("id = ?", couponModel.Id).Select(updateColumns).Updates(couponModel)
	if res.Error != nil {
		return fmt.Errorf("error updating coupon entry, err : %w", res.Error)
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}

func getCouponColumnNames(fieldMask []cmsPb.CouponFieldMask) []string {
	var columns []string
	for _, field := range fieldMask {
		columns = append(columns, couponColumnNames[field])
	}
	return columns
}

func (c *CouponDaoImpl) GetCouponById(ctx context.Context, id string) (*cmsPb.Coupon, error) {
	defer metric_util.TrackDuration("cms/dao", "CouponDaoImpl", "GetCouponById", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, c.DB)
	coupon := &model.Coupon{}
	res := db.First(coupon, "id=?", id)
	if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch coupon by id, err : %w", res.Error)
	}
	return coupon.GetCouponProto(), nil
}

func (c *CouponDaoImpl) GetCouponsBySkuId(ctx context.Context, skuId string) ([]*cmsPb.Coupon, error) {
	defer metric_util.TrackDuration("cms/dao", "CouponDaoImpl", "GetCouponsBySkuId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, c.DB)
	var coupons []*model.Coupon
	res := db.Where("sku_id=?", skuId).Find(&coupons)
	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch coupon by skuId, err : %w", res.Error)
	}
	var couponsPb []*cmsPb.Coupon
	for _, coupon := range coupons {
		couponsPb = append(couponsPb, coupon.GetCouponProto())
	}
	return couponsPb, nil
}

func (c *CouponDaoImpl) CreateCouponsInBulk(ctx context.Context, coupons []*cmsPb.Coupon) ([]*cmsPb.Coupon, error) {
	defer metric_util.TrackDuration("cms/dao", "CouponDaoImpl", "CreateCouponsInBulk", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, c.DB)
	var couponModels []*model.Coupon
	for _, coupon := range coupons {
		couponModels = append(couponModels, model.NewCoupon(coupon))
	}
	if dbErr := db.Create(couponModels).Error; dbErr != nil {
		return nil, fmt.Errorf("error creating bulk coupon entry in db err: %w", dbErr)
	}
	var couponPbs []*cmsPb.Coupon
	for _, couponModel := range couponModels {
		couponPbs = append(couponPbs, couponModel.GetCouponProto())
	}
	return couponPbs, nil
}

// GetCouponInventoryDetailsForSkuId returns the count of coupons against that sku id and the total inventory and inventory left for a given skuId
func (c *CouponDaoImpl) GetCouponInventoryDetailsForSkuId(ctx context.Context, skuId string) (*cmsPb.SkuInventoryDetails, error) {
	defer metric_util.TrackDuration("cms/dao", "CouponDaoImpl", "GetCouponInventoryDetailsForSkuId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, c.DB)

	if skuId == "" {
		return nil, fmt.Errorf("skuId cannot be empty")
	}

	type Result struct {
		CouponCount       uint32
		SkuInventoryLeft  uint32
		SkuInventoryTotal uint32
	}
	result := &Result{}

	res := db.Model(&model.Coupon{}).
		Where("sku_id = ?", skuId).
		Select("count(*) as coupon_count, SUM(inventory_left) as sku_inventory_left, SUM(inventory_total) as sku_inventory_total").
		Scan(result)

	if res.Error != nil {
		return nil, fmt.Errorf("failed to fetch coupon count and inventory by skuId, err : %w", res.Error)
	} else if result.CouponCount == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	return &cmsPb.SkuInventoryDetails{
		SkuId:                   skuId,
		CouponCount:             result.CouponCount,
		CouponInventoryTotalSum: result.SkuInventoryTotal,
		CouponInventoryLeftSum:  result.SkuInventoryLeft,
	}, nil
}
