// nolint:protogetter,govet
package provisioning

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/mock"
	"google.golang.org/genproto/googleapis/type/money"
	json "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/testing/protocmp"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	mockEvents "github.com/epifi/be-common/pkg/events/mocks"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"
	mockRatelimiter "github.com/epifi/be-common/pkg/ratelimiter/mocks"

	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifitemporal"
	cardNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/card"

	bankCustPb "github.com/epifi/gamma/api/bankcust"
	mockBankCust "github.com/epifi/gamma/api/bankcust/mocks"
	cardPb "github.com/epifi/gamma/api/card"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	cardPayloadPb "github.com/epifi/gamma/api/card/workflow/payload"
	kycPb "github.com/epifi/gamma/api/kyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/api/segment"
	mockSegmentation "github.com/epifi/gamma/api/segment/mocks"
	tieringPb "github.com/epifi/gamma/api/tiering"
	externalPb "github.com/epifi/gamma/api/tiering/external"
	mockTiering "github.com/epifi/gamma/api/tiering/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userMock "github.com/epifi/gamma/api/user/mocks"
	shippingPreferencePb "github.com/epifi/gamma/api/user/shipping_preference"
	daoMocks "github.com/epifi/gamma/card/dao/mocks"
	cardHelperCelestialMocks "github.com/epifi/gamma/card/helper/celestial/mocks"
	"github.com/epifi/gamma/card/helper/savings/mocks"
	mockUserProc "github.com/epifi/gamma/card/helper/user/mocks"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseEvaluatorMocks "github.com/epifi/gamma/pkg/feature/release/mocks"
)

func TestService_RenewCard(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockRenewCardPublisher := queueMocks.NewMockPublisher(ctr)
	mockUserClient := userMock.NewMockUsersClient(ctr)
	mockRateLimitClient := mockRatelimiter.NewMockRateLimiter(ctr)
	mockCardDao := daoMocks.NewMockCardDao(ctr)
	mockSavingsClient := mockSavings.NewMockSavingsClient(ctr)
	mockSavingsProc := mocks.NewMockSavingsProcessor(ctr)
	mockCardRequestDao := daoMocks.NewMockCardRequestDao(ctr)
	mockCelestialProc := cardHelperCelestialMocks.NewMockCelestialProcessor(ctr)
	mockTieringClient := mockTiering.NewMockTieringClient(ctr)
	mockBankCustClient := mockBankCust.NewMockBankCustomerServiceClient(ctr)
	mockEventsBroker := mockEvents.NewMockBroker(ctr)
	mockSegmetClient := mockSegmentation.NewMockSegmentationServiceClient(ctr)
	mockUserGrpProcessor := mockUserProc.NewMockUserProcessor(ctr)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)

	svcTS.provisioningServer = NewService(mockCardDao, nil, nil, nil,
		nil, nil, nil, nil, nil,
		nil, nil, mockUserClient, mockEventsBroker, mockSavingsClient, mockRenewCardPublisher,
		nil, conf, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, mockRateLimitClient,
		nil, nil, dynamicConf, nil,
		nil, nil, nil, nil,
		nil, mockCelestialProc, nil, mockTieringClient, mockSavingsProc,
		nil, mockBankCustClient, nil, nil, nil, mockCardRequestDao, mockSegmetClient,
		nil, mockReleaseEvaluator, mockUserGrpProcessor, nil, nil, nil, nil, nil, nil, nil)

	mockEventsBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes()
	mockTieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&tieringPb.GetTieringPitchV2Response{
		Status:      rpc.StatusOk(),
		CurrentTier: externalPb.Tier_TIER_FI_SALARY,
	}, nil).AnyTimes()

	// todo(chandresh/atirek): this is a temporary, make this mock call test specific
	mockSegmetClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(
		&segment.IsMemberResponse{
			Status: rpc.StatusOk(),
			SegmentMembershipMap: map[string]*segment.SegmentMembership{
				mock.Anything: {
					SegmentStatus: segment.SegmentStatus_SEGMENT_INSTANCE_FOUND,
				},
			},
		}, nil).MaxTimes(2)

	cardRequest1 := &cpPb.CardRequest{
		Id:              "card-req-id-1",
		CardId:          "card-id-1",
		ActorId:         "actor-1",
		OrchestrationId: "client-req-id-1",
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		RequestDetails: &cpPb.CardRequestDetails{
			Data: &cpPb.CardRequestDetails_RenewCardRequestDetails{
				RenewCardRequestDetails: &cpPb.RenewCardRequestDetails{
					BlockCardReason:     "lost card",
					AddressType:         types.AddressType_MAILING,
					CardForm:            cardPb.CardForm_PHYSICAL,
					BlockCardProvenance: cardPb.Provenance_USER_APP,
				},
			},
		},
		StageDetails: &cpPb.StageDetails{},
		Workflow:     cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_RENEW_CARD,
		Status:       cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED,
		Provenance:   cardPb.Provenance_USER_APP,
	}

	cardRequest2 := &cpPb.CardRequest{
		Id:              "card-req-id-1",
		CardId:          "card-id-1",
		ActorId:         "actor-1",
		OrchestrationId: "client-req-id-1",
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		RequestDetails: &cpPb.CardRequestDetails{
			Data: &cpPb.CardRequestDetails_RenewCardRequestDetails{
				RenewCardRequestDetails: &cpPb.RenewCardRequestDetails{
					BlockCardReason:     "lost card",
					AddressType:         types.AddressType_MAILING,
					CardForm:            cardPb.CardForm_DIGITAL,
					BlockCardProvenance: cardPb.Provenance_USER_APP,
				},
			},
		},
		StageDetails: &cpPb.StageDetails{},
		Workflow:     cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_RENEW_CARD,
		Status:       cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_CREATED,
		Provenance:   cardPb.Provenance_USER_APP,
	}

	renewCardWorkflowPayload, err := json.Marshal(&cardPayloadPb.RenewCardPayload{
		CardId:  "card-id-1",
		ActorId: "actor-1",
	})
	if err != nil {
		t.Errorf("error while marshing renew card wf payload, error = %v", err)
	}

	type mockPublisher struct {
		enable bool
		req    *cpPb.ProcessCardRenewalEventRequest
		res    string
		err    error
	}

	type mockGetById struct {
		enable bool
		req    string
		res    *cardPb.Card
		err    error
	}

	type mockCreateShippingPreference struct {
		enable bool
		req    *userPb.CreateShippingPreferenceRequest
		res    *userPb.CreateShippingPreferenceResponse
		err    error
	}

	type mockMinBalanceCheck struct {
		enable                     bool
		enableTpapPaymentFlagCheck bool
		isPaymentViaTpapEnabled    bool
		actorId                    string
		availableBalance           *money.Money
		err                        error
	}

	type mockRateLimit struct {
		enable           bool
		actorId          string
		isRequestAllowed bool
		apiCallCount     int
		err              error
	}

	type mockGetCardRequest struct {
		enable  bool
		actorId string
		res     []*cpPb.CardRequest
		err     error
	}

	type mockCreateCardRequest struct {
		enable bool
		req    *cpPb.CardRequest
		res    *cpPb.CardRequest
		err    error
	}

	type mockInitiateRenewCardWorkflow struct {
		enable          bool
		client          workflow.Client
		actorId         string
		workflowType    epifitemporal.Workflow
		workflowVersion workflow.Version
		payload         []byte
		clientReqId     string
		workflowId      string
		err             error
	}

	type mockFetchRenewCardCharges struct {
		enable         bool
		actorId        string
		renewalCharges *types.Money
	}

	type mockGetTierOfUser struct {
		enable bool
		req    *tieringPb.GetTierAtTimeRequest
		resp   *tieringPb.GetTierAtTimeResponse
		err    error
	}

	type mockGetBankCustomer struct {
		enable bool
		req    *bankCustPb.GetBankCustomerRequest
		res    *bankCustPb.GetBankCustomerResponse
		err    error
	}

	type mockGetAccount struct {
		enable bool
		req    *savingsPb.GetAccountRequest
		res    *savingsPb.GetAccountResponse
		err    error
	}

	type mockGetUserGroupsByActor struct {
		enable  bool
		actorId string
		res     []commontypes.UserGroup
		err     error
	}

	type mockGetCardsForActor struct {
		enable       bool
		actorId      string
		cardForms    []cardPb.CardForm
		cardStates   []cardPb.CardState
		cardTypes    []cardPb.CardType
		vendors      []commonvgpb.Vendor
		networkTypes []cardPb.CardNetworkType
		sortByColumn cardPb.CardFieldMask
		limit        int32
		savedCards   []*cardPb.Card
		err          error
	}

	tests := []struct {
		name                          string
		req                           *cpPb.RenewCardRequest
		mockPublisher                 mockPublisher
		mockCreateShippingPreference  mockCreateShippingPreference
		mockRateLimit                 mockRateLimit
		mockMinBalanceCheck           mockMinBalanceCheck
		mockGetById                   mockGetById
		mockGetCardRequest            mockGetCardRequest
		mockCreateCardRequest         mockCreateCardRequest
		mockInitiateRenewCardWorkflow mockInitiateRenewCardWorkflow
		mockFetchRenewCardCharges     mockFetchRenewCardCharges
		mockGetTierOfUser             mockGetTierOfUser
		mockGetBankCustomer           mockGetBankCustomer
		want                          *cpPb.RenewCardResponse
		mockGetCardsForActor          mockGetCardsForActor
		mockGetAccount                mockGetAccount
		mockGetUserGroupsByActor      mockGetUserGroupsByActor
		enableRenewFlowV2             bool
		wantErr                       bool
	}{
		{
			name: "error while creating shipping preference",
			req: &cpPb.RenewCardRequest{
				CardId:      "card-id-1",
				ActorId:     "actor-id-1",
				AddressType: types.AddressType_SHIPPING,
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "card-id-1",
				res: &cardPb.Card{
					Form:  cardPb.CardForm_PHYSICAL,
					State: cardPb.CardState_CREATED,
				},
				err: nil,
			},
			mockRateLimit: mockRateLimit{
				enable:           true,
				actorId:          "actor-id-1",
				isRequestAllowed: true,
				apiCallCount:     3,
				err:              nil,
			},
			mockCreateShippingPreference: mockCreateShippingPreference{
				enable: true,
				req: &userPb.CreateShippingPreferenceRequest{Preference: &userPb.ShippingPreference{
					ActorId:      "actor-id-1",
					ShippingItem: types.ShippingItem_DEBIT_CARD,
					AddressType:  types.AddressType_SHIPPING,
				}},
				res: &userPb.CreateShippingPreferenceResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			want: &cpPb.RenewCardResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "error while publishing packet",
			req: &cpPb.RenewCardRequest{
				CardId:      "card-id-1",
				ActorId:     "actor-id-1",
				AddressType: types.AddressType_SHIPPING,
			},
			mockRateLimit: mockRateLimit{
				enable:           true,
				actorId:          "actor-id-1",
				isRequestAllowed: true,
				apiCallCount:     3,
				err:              nil,
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "card-id-1",
				res: &cardPb.Card{
					Form:  cardPb.CardForm_PHYSICAL,
					State: cardPb.CardState_CREATED,
				},
				err: nil,
			},
			mockCreateShippingPreference: mockCreateShippingPreference{
				enable: true,
				req: &userPb.CreateShippingPreferenceRequest{Preference: &userPb.ShippingPreference{
					ActorId:      "actor-id-1",
					ShippingItem: types.ShippingItem_DEBIT_CARD,
					AddressType:  types.AddressType_SHIPPING,
				}},
				res: &userPb.CreateShippingPreferenceResponse{
					Status: rpc.StatusOk(),
					Preference: &userPb.ShippingPreference{
						ActorId:      "actor-id-1",
						ShippingItem: types.ShippingItem_DEBIT_CARD,
						AddressType:  types.AddressType_SHIPPING,
					},
				},
				err: nil,
			},
			mockPublisher: mockPublisher{
				enable: true,
				req: &cpPb.ProcessCardRenewalEventRequest{
					CardId: "card-id-1",
				},
				res: "",
				err: fmt.Errorf("failed to publish packet"),
			},
			want: &cpPb.RenewCardResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "renew card packet published successfully",
			req: &cpPb.RenewCardRequest{
				CardId:      "card-id-1",
				ActorId:     "actor-id-1",
				AddressType: types.AddressType_SHIPPING,
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "card-id-1",
				res: &cardPb.Card{
					Form:  cardPb.CardForm_PHYSICAL,
					State: cardPb.CardState_CREATED,
				},
				err: nil,
			},
			mockRateLimit: mockRateLimit{
				enable:           true,
				actorId:          "actor-id-1",
				isRequestAllowed: true,
				apiCallCount:     3,
				err:              nil,
			},
			mockCreateShippingPreference: mockCreateShippingPreference{
				enable: true,
				req: &userPb.CreateShippingPreferenceRequest{Preference: &userPb.ShippingPreference{
					ActorId:      "actor-id-1",
					ShippingItem: types.ShippingItem_DEBIT_CARD,
					AddressType:  types.AddressType_SHIPPING,
				}},
				res: &userPb.CreateShippingPreferenceResponse{
					Status: rpc.StatusOk(),
					Preference: &userPb.ShippingPreference{
						ActorId:      "actor-id-1",
						ShippingItem: types.ShippingItem_DEBIT_CARD,
						AddressType:  types.AddressType_SHIPPING,
					},
				},
				err: nil,
			},
			mockPublisher: mockPublisher{
				enable: true,
				req: &cpPb.ProcessCardRenewalEventRequest{
					CardId: "card-id-1",
				},
				res: "",
				err: nil,
			},
			want: &cpPb.RenewCardResponse{Status: rpc.StatusOk()},
		},
		{
			name:              "renew card request initiated successfully for physical card (flowV2)",
			enableRenewFlowV2: true,
			want:              &cpPb.RenewCardResponse{Status: rpc.StatusOk()},
			req: &cpPb.RenewCardRequest{
				CardId:              cardRequest1.GetCardId(),
				BlockCardReason:     cardRequest1.GetRequestDetails().GetRenewCardRequestDetails().GetBlockCardReason(),
				BlockCardProvenance: cardRequest1.GetRequestDetails().GetRenewCardRequestDetails().GetBlockCardProvenance(),
				ActorId:             cardRequest1.GetActorId(),
				AddressType:         cardRequest1.GetRequestDetails().GetRenewCardRequestDetails().GetAddressType(),
				CardForm:            cardRequest1.GetRequestDetails().GetRenewCardRequestDetails().GetCardForm(),
			},
			mockGetById: mockGetById{
				enable: true,
				req:    cardRequest1.GetCardId(),
				res: &cardPb.Card{
					Form:  cardPb.CardForm_PHYSICAL,
					State: cardPb.CardState_CREATED,
				},
				err: nil,
			},
			mockRateLimit: mockRateLimit{
				enable:           true,
				actorId:          cardRequest1.GetActorId(),
				isRequestAllowed: true,
				apiCallCount:     3,
				err:              nil,
			},
			mockGetTierOfUser: mockGetTierOfUser{
				enable: false,
				req: &tieringPb.GetTierAtTimeRequest{
					ActorId:       cardRequest1.GetActorId(),
					TierTimestamp: timestamp.Now(),
				},
				resp: &tieringPb.GetTierAtTimeResponse{
					Status: rpc.StatusOk(),
					TierInfo: &externalPb.TierInfo{
						Tier: externalPb.Tier_TIER_FI_BASIC,
					},
				},
				err: nil,
			},
			mockGetBankCustomer: mockGetBankCustomer{
				enable: false,
				req: &bankCustPb.GetBankCustomerRequest{
					Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
						ActorId: cardRequest1.GetActorId(),
					},
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
				},
				res: &bankCustPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustPb.BankCustomer{
						DedupeInfo: &bankCustPb.DedupeInfo{
							KycLevel: kycPb.KYCLevel_MIN_KYC,
						},
					},
				},
				err: nil,
			},
			mockGetCardsForActor: mockGetCardsForActor{
				enable:       false,
				actorId:      cardRequest1.GetActorId(),
				cardForms:    []cardPb.CardForm{cardPb.CardForm_PHYSICAL},
				vendors:      []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
				sortByColumn: cardPb.CardFieldMask_CARD_CREATED_AT,
				limit:        0,
				savedCards:   []*cardPb.Card{{Form: cardPb.CardForm_PHYSICAL}},
				err:          nil,
			},
			mockMinBalanceCheck: mockMinBalanceCheck{
				enable:                     true,
				enableTpapPaymentFlagCheck: true,
				isPaymentViaTpapEnabled:    true,
				actorId:                    cardRequest1.GetActorId(),
				availableBalance:           moneyPkg.AmountINR(10000).GetPb(),
				err:                        nil,
			},
			mockGetAccount: mockGetAccount{
				enable: false,
				req: &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_ActorId{
					ActorId: cardRequest1.GetActorId(),
				}},
				res: &savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						Id: "sav-id-1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: 1,
						},
						OpeningBalanceInfo:    nil,
						CreationInfo:          nil,
						RetryInfo:             nil,
						BalanceFromPartenerV1: nil,
						ActorId:               "",
						SignInfo:              nil,
					},
				},
				err: nil,
			},
			mockGetUserGroupsByActor: mockGetUserGroupsByActor{
				enable:  false,
				actorId: cardRequest1.GetActorId(),
				res:     nil,
				err:     nil,
			},
			mockGetCardRequest: mockGetCardRequest{
				enable:  true,
				actorId: cardRequest1.GetActorId(),
				err:     epifierrors.ErrRecordNotFound,
			},
			mockCreateCardRequest: mockCreateCardRequest{
				enable: true,
				req:    cardRequest1,
				res:    cardRequest1,
				err:    nil,
			},
			mockInitiateRenewCardWorkflow: mockInitiateRenewCardWorkflow{
				enable:          true,
				client:          workflow.Client_DEBIT_CARD,
				actorId:         cardRequest1.GetActorId(),
				workflowType:    cardNs.RenewCard,
				workflowVersion: workflow.Version_V0,
				payload:         renewCardWorkflowPayload,
				clientReqId:     cardRequest1.GetOrchestrationId(),
				workflowId:      "wf-id-1",
				err:             nil,
			},
		},
		{
			name:              "renew card request initiated successfully for digital card (flowV2)",
			enableRenewFlowV2: true,
			req: &cpPb.RenewCardRequest{
				CardId:              cardRequest2.GetCardId(),
				BlockCardReason:     cardRequest2.GetRequestDetails().GetRenewCardRequestDetails().GetBlockCardReason(),
				BlockCardProvenance: cardRequest2.GetRequestDetails().GetRenewCardRequestDetails().GetBlockCardProvenance(),
				ActorId:             cardRequest2.GetActorId(),
				AddressType:         cardRequest2.GetRequestDetails().GetRenewCardRequestDetails().GetAddressType(),
				CardForm:            cardRequest2.GetRequestDetails().GetRenewCardRequestDetails().GetCardForm(),
			},
			want: &cpPb.RenewCardResponse{Status: rpc.StatusOk()},
			mockGetById: mockGetById{
				enable: true,
				req:    cardRequest2.GetCardId(),
				res: &cardPb.Card{
					Form:  cardPb.CardForm_PHYSICAL,
					State: cardPb.CardState_CREATED,
				},
				err: nil,
			},
			mockRateLimit: mockRateLimit{
				enable:           true,
				actorId:          cardRequest2.GetActorId(),
				isRequestAllowed: true,
				apiCallCount:     3,
				err:              nil,
			},
			mockGetCardRequest: mockGetCardRequest{
				enable:  true,
				actorId: cardRequest2.GetActorId(),
				err:     epifierrors.ErrRecordNotFound,
			},
			mockMinBalanceCheck: mockMinBalanceCheck{
				enable:                     true,
				enableTpapPaymentFlagCheck: true,
				isPaymentViaTpapEnabled:    true,
				actorId:                    cardRequest1.GetActorId(),
				availableBalance:           moneyPkg.AmountINR(10000).GetPb(),
				err:                        nil,
			},
			mockCreateCardRequest: mockCreateCardRequest{
				enable: true,
				req:    cardRequest2,
				res:    cardRequest2,
				err:    nil,
			},
			mockInitiateRenewCardWorkflow: mockInitiateRenewCardWorkflow{
				enable:          true,
				client:          workflow.Client_DEBIT_CARD,
				actorId:         cardRequest2.GetActorId(),
				workflowType:    cardNs.RenewCard,
				workflowVersion: workflow.Version_V0,
				payload:         renewCardWorkflowPayload,
				clientReqId:     cardRequest2.GetOrchestrationId(),
				workflowId:      "wf-id-1",
				err:             nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx = epificontext.CtxWithAppPlatform(ctx, commontypes.Platform_ANDROID)
			ctx = epificontext.CtxWithAppVersionCode(ctx, "300")

			err = svcTS.dynamicConf.Flags().EnableRenewCardFlowV2().Set(&cfg.PlatformVersionCheck{
				IsEnableOnAndroid: false,
				MinAndroidVersion: 0,
				IsEnableOnIos:     false,
				MinIosVersion:     0,
			}, false, nil)
			if err != nil {
				t.Errorf("error setting flag EnableRenewCardFlowV2 value = %v, error = %v", false, err)
			}

			if tt.enableRenewFlowV2 {
				err = svcTS.dynamicConf.Flags().EnableRenewCardFlowV2().Set(&cfg.PlatformVersionCheck{
					IsEnableOnAndroid: true,
					MinAndroidVersion: 1,
					IsEnableOnIos:     true,
					MinIosVersion:     1,
				}, false, nil)
				if err != nil {
					t.Errorf("error setting flag EnableRenewCardFlowV2 value = %v", true)
				}
			}

			if tt.mockGetCardRequest.enable {
				mockCardRequestDao.EXPECT().GetByActorIdAndWorkflow(ctx, tt.mockGetCardRequest.actorId, cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_RENEW_CARD, nil).
					Return(tt.mockGetCardRequest.res, tt.mockGetCardRequest.err)
			}

			if tt.mockCreateCardRequest.enable {
				mockCardRequestDao.EXPECT().Create(ctx, newCardRequestMatcher(tt.mockCreateCardRequest.req)).Return(tt.mockCreateCardRequest.res, tt.mockCreateCardRequest.err)
			}

			if tt.mockInitiateRenewCardWorkflow.enable {
				mockCelestialProc.EXPECT().InitiateWorkflow(ctx, newClientReqIdMatch(tt.mockInitiateRenewCardWorkflow.clientReqId), tt.mockInitiateRenewCardWorkflow.client,
					tt.mockInitiateRenewCardWorkflow.actorId, tt.mockInitiateRenewCardWorkflow.payload, tt.mockInitiateRenewCardWorkflow.workflowType, tt.mockInitiateRenewCardWorkflow.workflowVersion)
			}

			if tt.mockGetById.enable {
				mockCardDao.EXPECT().GetByID(ctx, tt.mockGetById.req).Return(tt.mockGetById.res, tt.mockGetById.err)
			}
			if tt.mockCreateShippingPreference.enable {
				mockUserClient.EXPECT().CreateShippingPreference(ctx, tt.mockCreateShippingPreference.req).Return(tt.mockCreateShippingPreference.res,
					tt.mockCreateShippingPreference.err)
			}

			if tt.mockMinBalanceCheck.enable {
				if tt.mockMinBalanceCheck.enableTpapPaymentFlagCheck {
					mockReleaseEvaluator.EXPECT().Evaluate(ctx, release.NewCommonConstraintData(types.Feature_PHYSICAL_DEBIT_CARD_CHARGES_PAYMENT_OPTIONS_SCREEN).
						WithActorId(tt.mockMinBalanceCheck.actorId)).Return(tt.mockMinBalanceCheck.isPaymentViaTpapEnabled, tt.mockMinBalanceCheck.err)
				}
				if !tt.mockMinBalanceCheck.isPaymentViaTpapEnabled {
					mockSavingsProc.EXPECT().GetSavingsAccountBalanceForActor(ctx, tt.mockMinBalanceCheck.actorId).Return(tt.mockMinBalanceCheck.availableBalance,
						tt.mockMinBalanceCheck.err)
				}
			}

			if tt.mockPublisher.enable {
				mockRenewCardPublisher.EXPECT().Publish(ctx, tt.mockPublisher.req).
					Return(tt.mockPublisher.res, tt.mockPublisher.err)
			}

			if tt.mockGetTierOfUser.enable {
				mockTieringClient.EXPECT().GetTierAtTime(ctx, gomock.Any()).
					Return(tt.mockGetTierOfUser.resp, tt.mockGetTierOfUser.err)
			}

			if tt.mockGetBankCustomer.enable {
				mockBankCustClient.EXPECT().GetBankCustomer(ctx, tt.mockGetBankCustomer.req).
					Return(tt.mockGetBankCustomer.res, tt.mockGetBankCustomer.err)
			}

			if tt.mockGetCardsForActor.enable {
				mockCardDao.EXPECT().GetCardsForActor(ctx, tt.mockGetCardsForActor.actorId, tt.mockGetCardsForActor.cardStates,
					tt.mockGetCardsForActor.cardTypes, tt.mockGetCardsForActor.networkTypes, tt.mockGetCardsForActor.cardForms,
					tt.mockGetCardsForActor.vendors, tt.mockGetCardsForActor.sortByColumn, tt.mockGetCardsForActor.limit).
					Return(tt.mockGetCardsForActor.savedCards, tt.mockGetCardsForActor.err)
			}

			if tt.mockGetAccount.enable {
				mockSavingsClient.EXPECT().GetAccount(ctx, tt.mockGetAccount.req).Return(tt.mockGetAccount.res, tt.mockGetAccount.err)
			}

			if tt.mockGetUserGroupsByActor.enable {
				mockUserGrpProcessor.EXPECT().GetUserGroupsByActorId(ctx, tt.mockGetUserGroupsByActor.actorId).
					Return(tt.mockGetUserGroupsByActor.res, tt.mockGetUserGroupsByActor.err)
			}

			got, err := svcTS.provisioningServer.RenewCard(ctx, tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RenewCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("RenewCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// custom matcher interface for card request
type cardRequestMatcher struct {
	want *cpPb.CardRequest
}

func newCardRequestMatcher(want *cpPb.CardRequest) *cardRequestMatcher {
	return &cardRequestMatcher{
		want: want,
	}
}

func (ce *cardRequestMatcher) Matches(x interface{}) bool {
	got, ok := x.(*cpPb.CardRequest)
	if !ok {
		return false
	}

	ce.want.Id = got.Id
	ce.want.OrchestrationId = got.OrchestrationId

	return reflect.DeepEqual(ce.want, got)
}

func (ce *cardRequestMatcher) String() string {
	return fmt.Sprintf("want: %v", ce.want)
}

func TestService_RenewCardStatus(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCardDao := daoMocks.NewMockCardDao(ctr)
	mockCardReqDao := daoMocks.NewMockCardRequestDao(ctr)
	mockUserClient := userMock.NewMockUsersClient(ctr)
	mockCCReqDao := daoMocks.NewMockCardCreationRequestDao(ctr)

	svcTS.provisioningServer = NewService(mockCardDao, mockCCReqDao, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		mockUserClient, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, dynamicConf,
		nil, nil, nil, nil,
		nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, mockCardReqDao,
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	actorId := "actor-id-1"

	card1 := &cardPb.Card{
		Id:         "card-id-1",
		ActorId:    actorId,
		IssuerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Form:       cardPb.CardForm_PHYSICAL,
	}

	blockedCard1 := &cardPb.Card{
		Id:         "blocked-card-id-1",
		ActorId:    actorId,
		State:      cardPb.CardState_BLOCKED,
		IssuerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Form:       cardPb.CardForm_PHYSICAL,
	}

	newCard1 := &cardPb.Card{
		Id:             "new-card-1",
		ActorId:        actorId,
		State:          cardPb.CardState_INITIATED,
		PreviousCardId: "blocked-card-id-1",
		IssuerBank:     commonvgpb.Vendor_FEDERAL_BANK,
		Form:           cardPb.CardForm_PHYSICAL,
	}

	cardRequest1 := &cpPb.CardRequest{
		Id:              "card-req-id-1",
		CardId:          blockedCard1.GetId(),
		ActorId:         actorId,
		OrchestrationId: "client-req-id-1",
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		RequestDetails: &cpPb.CardRequestDetails{
			Data: &cpPb.CardRequestDetails_RenewCardRequestDetails{
				RenewCardRequestDetails: &cpPb.RenewCardRequestDetails{
					BlockCardReason:     "lost card",
					AddressType:         types.AddressType_MAILING,
					CardForm:            cardPb.CardForm_PHYSICAL,
					BlockCardProvenance: cardPb.Provenance_USER_APP,
				},
			},
		},
		Workflow:   cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_RENEW_CARD,
		Status:     cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS,
		Provenance: cardPb.Provenance_USER_APP,
	}
	cardRequestFailedInCardCreationStage := &cpPb.CardRequest{
		Id:              "card-req-id-1",
		CardId:          blockedCard1.GetId(),
		ActorId:         actorId,
		OrchestrationId: "client-req-id-1",
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		RequestDetails: &cpPb.CardRequestDetails{
			Data: &cpPb.CardRequestDetails_RenewCardRequestDetails{
				RenewCardRequestDetails: &cpPb.RenewCardRequestDetails{
					BlockCardReason:     "lost card",
					AddressType:         types.AddressType_MAILING,
					CardForm:            cardPb.CardForm_PHYSICAL,
					BlockCardProvenance: cardPb.Provenance_USER_APP,
				},
			},
		},
		StageDetails: &cpPb.StageDetails{
			CardRequestStages: map[string]*cpPb.CardRequestStage{
				cardEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CREATE_CARD.String(): {
					StageName: cardEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CREATE_CARD,
					Status:    cardEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED,
				},
			},
		},
		Workflow:   cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_RENEW_CARD,
		Status:     cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED,
		Provenance: cardPb.Provenance_USER_APP,
	}
	cardRequestFailedInBlockCardnStage := &cpPb.CardRequest{
		Id:              "card-req-id-1",
		CardId:          blockedCard1.GetId(),
		ActorId:         actorId,
		OrchestrationId: "client-req-id-1",
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		RequestDetails: &cpPb.CardRequestDetails{
			Data: &cpPb.CardRequestDetails_RenewCardRequestDetails{
				RenewCardRequestDetails: &cpPb.RenewCardRequestDetails{
					BlockCardReason:     "lost card",
					AddressType:         types.AddressType_MAILING,
					CardForm:            cardPb.CardForm_PHYSICAL,
					BlockCardProvenance: cardPb.Provenance_USER_APP,
				},
			},
		},
		StageDetails: &cpPb.StageDetails{
			CardRequestStages: map[string]*cpPb.CardRequestStage{
				cardEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_BLOCK_CARD.String(): {
					StageName: cardEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_BLOCK_CARD,
					Status:    cardEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED,
				},
			},
		},
		Workflow:   cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_RENEW_CARD,
		Status:     cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED,
		Provenance: cardPb.Provenance_USER_APP,
	}
	cardRequestFailedInPhysicalStage := &cpPb.CardRequest{
		Id:              "card-req-id-1",
		CardId:          blockedCard1.GetId(),
		ActorId:         actorId,
		OrchestrationId: "client-req-id-1",
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		RequestDetails: &cpPb.CardRequestDetails{
			Data: &cpPb.CardRequestDetails_RenewCardRequestDetails{
				RenewCardRequestDetails: &cpPb.RenewCardRequestDetails{
					BlockCardReason:     "lost card",
					AddressType:         types.AddressType_MAILING,
					CardForm:            cardPb.CardForm_PHYSICAL,
					BlockCardProvenance: cardPb.Provenance_USER_APP,
				},
			},
		},
		StageDetails: &cpPb.StageDetails{
			CardRequestStages: map[string]*cpPb.CardRequestStage{
				cardEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PHYSICAL_CARD_DISPATCH.String(): {
					StageName: cardEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PHYSICAL_CARD_DISPATCH,
					Status:    cardEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED,
				},
			},
		},
		Workflow:   cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_RENEW_CARD,
		Status:     cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED,
		Provenance: cardPb.Provenance_USER_APP,
	}

	type mockGetById struct {
		enable bool
		req    string
		res    *cardPb.Card
		err    error
	}

	type mockGetByPreviousCardId struct {
		enable bool
		req    string
		res    *cardPb.Card
		err    error
	}

	type mockGetByByCardId struct {
		enable bool
		req    string
		res    *cpPb.CardCreationRequest
		err    error
	}

	type mockCheckShippingAddressUpdateStatus struct {
		enable bool
		req    *userPb.CheckShippingAddressUpdateStatusRequest
		res    *userPb.CheckShippingAddressUpdateStatusResponse
		err    error
	}

	type mockGetCardRequest struct {
		enable  bool
		actorId string
		res     []*cpPb.CardRequest
		err     error
	}

	tests := []struct {
		name                                 string
		req                                  *cpPb.RenewCardStatusRequest
		mockGetById                          mockGetById
		mockCheckShippingAddressUpdateStatus mockCheckShippingAddressUpdateStatus
		mockGetByPreviousCardId              mockGetByPreviousCardId
		mockGetByByCardId                    mockGetByByCardId
		mockGetCardRequest                   mockGetCardRequest
		enableRenewFlowV2                    bool
		want                                 *cpPb.RenewCardStatusResponse
		wantErr                              bool
	}{
		{
			name: "card not found for id",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "invalid-card-id",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "invalid-card-id",
				res:    nil,
				err:    gorm.ErrRecordNotFound,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_FAILED), "Shipping address update failed",
					"card not found"),
			},
		},
		{
			name: "error while fetching card by id",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "card-id-1",
				res:    nil,
				err:    fmt.Errorf("invalid sql"),
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_PENDING), "Shipping Address Update Pending",
					"db error"),
			},
		},
		{
			name: "error while fetching shipping address update status",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "card-id-1",
				res:    card1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: nil,
				err: fmt.Errorf("error whule fetching details"),
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_PENDING), "Shipping Address Update Pending",
					"error fetching shipping update status"),
			},
		},
		{
			name: "non success state while fetching shipping address update status",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "card-id-1",
				res:    card1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: &userPb.CheckShippingAddressUpdateStatusResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_PENDING), "Shipping Address Update Pending",
					"error fetching shipping update status"),
			},
		},
		{
			name: "failed to update shipping address",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "card-id-1",
				res:    card1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: &userPb.CheckShippingAddressUpdateStatusResponse{
					Status:        rpc.StatusOk(),
					RequestStatus: shippingPreferencePb.RequestState_FAILED,
				},
				err: nil,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_FAILED), "Shipping address update failed",
					""),
			},
		},
		{
			name: "shipping address update pending",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "card-id-1",
				res:    card1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: &userPb.CheckShippingAddressUpdateStatusResponse{
					Status:        rpc.StatusOk(),
					RequestStatus: shippingPreferencePb.RequestState_INITIATED,
				},
				err: nil,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_PENDING), "Shipping Address Update Pending",
					""),
			},
		},
		{
			name: "card not found for previous card id",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: &userPb.CheckShippingAddressUpdateStatusResponse{
					Status:        rpc.StatusOk(),
					RequestStatus: shippingPreferencePb.RequestState_SUCCESS,
				},
				err: nil,
			},
			mockGetByPreviousCardId: mockGetByPreviousCardId{
				enable: true,
				req:    "card-id-1",
				res:    nil,
				err:    gorm.ErrRecordNotFound,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_CARD_CREATION_PENDING), "Card creation pending",
					"db error"),
			},
		},
		{
			name: "error fetching card for previous card id",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: &userPb.CheckShippingAddressUpdateStatusResponse{
					Status:        rpc.StatusOk(),
					RequestStatus: shippingPreferencePb.RequestState_SUCCESS,
				},
				err: nil,
			},
			mockGetByPreviousCardId: mockGetByPreviousCardId{
				enable: true,
				req:    "card-id-1",
				res:    nil,
				err:    fmt.Errorf("invalid sql"),
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_CARD_CREATION_PENDING), "Card creation pending",
					"db error"),
			},
		},
		{
			name: "card creation request not found for previous card id",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "blocked-card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "blocked-card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: &userPb.CheckShippingAddressUpdateStatusResponse{
					Status:        rpc.StatusOk(),
					RequestStatus: shippingPreferencePb.RequestState_SUCCESS,
				},
				err: nil,
			},
			mockGetByPreviousCardId: mockGetByPreviousCardId{
				enable: true,
				req:    "blocked-card-id-1",
				res:    newCard1,
				err:    nil,
			},
			mockGetByByCardId: mockGetByByCardId{
				enable: true,
				req:    "new-card-1",
				res:    nil,
				err:    gorm.ErrRecordNotFound,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_CARD_CREATION_FAILED), "Card creation failed",
					"card creation request not found"),
			},
		},
		{
			name: "error while fetching card creation request for previous card id",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "blocked-card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "blocked-card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: &userPb.CheckShippingAddressUpdateStatusResponse{
					Status:        rpc.StatusOk(),
					RequestStatus: shippingPreferencePb.RequestState_SUCCESS,
				},
				err: nil,
			},
			mockGetByPreviousCardId: mockGetByPreviousCardId{
				enable: true,
				req:    "blocked-card-id-1",
				res:    newCard1,
				err:    nil,
			},
			mockGetByByCardId: mockGetByByCardId{
				enable: true,
				req:    "new-card-1",
				res:    nil,
				err:    fmt.Errorf("invalid sql"),
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_CARD_CREATION_PENDING), "Card creation pending",
					"db error"),
			},
		},
		{
			name: "new card creation request status queued",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "blocked-card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "blocked-card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: &userPb.CheckShippingAddressUpdateStatusResponse{
					Status:        rpc.StatusOk(),
					RequestStatus: shippingPreferencePb.RequestState_SUCCESS,
				},
				err: nil,
			},
			mockGetByPreviousCardId: mockGetByPreviousCardId{
				enable: true,
				req:    "blocked-card-id-1",
				res:    newCard1,
				err:    nil,
			},
			mockGetByByCardId: mockGetByByCardId{
				enable: true,
				req:    "new-card-1",
				res: &cpPb.CardCreationRequest{
					CardId:  "new-card-1",
					State:   cpPb.RequestState_QUEUED,
					Retries: 0,
				},
				err: nil,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_CARD_CREATION_PENDING), "Card creation pending",
					"card creation pending"),
			},
		},
		{
			name: "new card creation request status failed",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "blocked-card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "blocked-card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: &userPb.CheckShippingAddressUpdateStatusResponse{
					Status:        rpc.StatusOk(),
					RequestStatus: shippingPreferencePb.RequestState_SUCCESS,
				},
				err: nil,
			},
			mockGetByPreviousCardId: mockGetByPreviousCardId{
				enable: true,
				req:    "blocked-card-id-1",
				res:    newCard1,
				err:    nil,
			},
			mockGetByByCardId: mockGetByByCardId{
				enable: true,
				req:    "new-card-1",
				res: &cpPb.CardCreationRequest{
					CardId:  "new-card-1",
					State:   cpPb.RequestState_FAILED,
					Retries: 0,
				},
				err: nil,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_CARD_CREATION_FAILED), "Card creation failed",
					"failed to create card"),
			},
		},
		{
			name: "new card creation success",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "blocked-card-id-1",
			},
			mockGetById: mockGetById{
				enable: true,
				req:    "blocked-card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockCheckShippingAddressUpdateStatus: mockCheckShippingAddressUpdateStatus{
				enable: true,
				req: &userPb.CheckShippingAddressUpdateStatusRequest{
					ActorId:      actorId,
					Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
					ShippingItem: types.ShippingItem_DEBIT_CARD,
				},
				res: &userPb.CheckShippingAddressUpdateStatusResponse{
					Status:        rpc.StatusOk(),
					RequestStatus: shippingPreferencePb.RequestState_SUCCESS,
				},
				err: nil,
			},
			mockGetByPreviousCardId: mockGetByPreviousCardId{
				enable: true,
				req:    "blocked-card-id-1",
				res:    newCard1,
				err:    nil,
			},
			mockGetByByCardId: mockGetByByCardId{
				enable: true,
				req:    "new-card-1",
				res: &cpPb.CardCreationRequest{
					CardId:  "new-card-1",
					State:   cpPb.RequestState_SUCCESS,
					Retries: 0,
				},
				err: nil,
			},
			want: &cpPb.RenewCardStatusResponse{
				NewCardId: "new-card-1",
				Status:    rpc.StatusOk(),
			},
		},
		{
			name: "new card creation success (flowV2)",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "blocked-card-id-1",
			},
			enableRenewFlowV2: true,
			mockGetById: mockGetById{
				enable: true,
				req:    "blocked-card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockGetCardRequest: mockGetCardRequest{
				enable:  true,
				actorId: actorId,
				res:     []*cpPb.CardRequest{cardRequest1},
				err:     nil,
			},
			mockGetByPreviousCardId: mockGetByPreviousCardId{
				enable: true,
				req:    blockedCard1.GetId(),
				res:    newCard1,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status:     rpc.StatusOk(),
				NewCardId:  newCard1.GetId(),
				NextAction: nil,
			},
		},
		{
			name: "renew card failed in card creation (flowV2)",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "blocked-card-id-1",
			},
			enableRenewFlowV2: true,
			mockGetById: mockGetById{
				enable: true,
				req:    "blocked-card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockGetCardRequest: mockGetCardRequest{
				enable:  true,
				actorId: actorId,
				res:     []*cpPb.CardRequest{cardRequestFailedInCardCreationStage},
				err:     nil,
			},
			mockGetByPreviousCardId: mockGetByPreviousCardId{
				enable: true,
				req:    "blocked-card-id-1",
				res: &cardPb.Card{
					Id:             "new-card-1",
					ActorId:        actorId,
					State:          cardPb.CardState_INVALID,
					PreviousCardId: "blocked-card-id-1",
					IssuerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					Form:           cardPb.CardForm_PHYSICAL,
				},
				err: nil,
			},
			mockGetByByCardId: mockGetByByCardId{
				enable: true,
				req:    "new-card-1",
				res: &cpPb.CardCreationRequest{
					CardId:  "new-card-1",
					State:   cpPb.RequestState_FAILED,
					Retries: 2,
				},
				err: nil,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_CARD_CREATION_FAILED), "Card creation failed",
					"failed to create card"),
			},
		},
		{
			name: "renew card failed in block card stage (flowV2)",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "blocked-card-id-1",
			},
			enableRenewFlowV2: true,
			mockGetById: mockGetById{
				enable: true,
				req:    "blocked-card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockGetCardRequest: mockGetCardRequest{
				enable:  true,
				actorId: actorId,
				res:     []*cpPb.CardRequest{cardRequestFailedInBlockCardnStage},
				err:     nil,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_CARD_BLOCK_FAILED), "Block card failed",
					"failed to block card"),
			},
		},
		{
			name: "renew card failed in physical dispatch stage (flowV2)",
			req: &cpPb.RenewCardStatusRequest{
				CardId: "blocked-card-id-1",
			},
			enableRenewFlowV2: true,
			mockGetById: mockGetById{
				enable: true,
				req:    "blocked-card-id-1",
				res:    blockedCard1,
				err:    nil,
			},
			mockGetCardRequest: mockGetCardRequest{
				enable:  true,
				actorId: actorId,
				res:     []*cpPb.CardRequest{cardRequestFailedInPhysicalStage},
				err:     nil,
			},
			mockGetByPreviousCardId: mockGetByPreviousCardId{
				enable: true,
				req:    blockedCard1.GetId(),
				res:    newCard1,
			},
			want: &cpPb.RenewCardStatusResponse{
				Status: rpc.NewStatus(uint32(cpPb.RenewCardStatusResponse_INITIATE_PHYSICAL_DISPATCH_FAILED), "Physical card dispatch failed",
					""),
				NewCardId: newCard1.GetId(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx = epificontext.CtxWithAppPlatform(ctx, commontypes.Platform_ANDROID)
			ctx = epificontext.CtxWithAppVersionCode(ctx, "300")

			err = svcTS.dynamicConf.Flags().EnableRenewCardFlowV2().Set(&cfg.PlatformVersionCheck{
				IsEnableOnAndroid: false,
				MinAndroidVersion: 0,
				IsEnableOnIos:     false,
				MinIosVersion:     0,
			}, false, nil)
			if err != nil {
				t.Errorf("error setting flag EnableRenewCardFlowV2 value = %v, error = %v", false, err)
			}

			if tt.enableRenewFlowV2 {
				err = svcTS.dynamicConf.Flags().EnableRenewCardFlowV2().Set(&cfg.PlatformVersionCheck{
					IsEnableOnAndroid: true,
					MinAndroidVersion: 1,
					IsEnableOnIos:     true,
					MinIosVersion:     1,
				}, false, nil)
				if err != nil {
					t.Errorf("error setting flag EnableRenewCardFlowV2 value = %v", true)
				}
			}

			if tt.mockGetCardRequest.enable {
				mockCardReqDao.EXPECT().GetByActorIdAndWorkflow(ctx, tt.mockGetCardRequest.actorId, cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_RENEW_CARD, nil).
					Return(tt.mockGetCardRequest.res, tt.mockGetCardRequest.err)
			}

			if tt.mockCheckShippingAddressUpdateStatus.enable {
				mockUserClient.EXPECT().CheckShippingAddressUpdateStatus(ctx, tt.mockCheckShippingAddressUpdateStatus.req).
					Return(tt.mockCheckShippingAddressUpdateStatus.res, tt.mockCheckShippingAddressUpdateStatus.err)
			}

			if tt.mockGetById.enable {
				mockCardDao.EXPECT().GetByID(ctx, tt.mockGetById.req).
					Return(tt.mockGetById.res, tt.mockGetById.err)
			}
			if tt.mockGetByPreviousCardId.enable {
				mockCardDao.EXPECT().GetByPreviousCardId(ctx, tt.mockGetByPreviousCardId.req).
					Return(tt.mockGetByPreviousCardId.res, tt.mockGetByPreviousCardId.err)
			}
			if tt.mockGetByByCardId.enable {
				mockCCReqDao.EXPECT().GetByCardId(ctx, tt.mockGetByByCardId.req).
					Return(tt.mockGetByByCardId.res, tt.mockGetByByCardId.err)
			}
			got, err := svcTS.provisioningServer.RenewCardStatus(ctx, tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RenewCardStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("RenewCardStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}
