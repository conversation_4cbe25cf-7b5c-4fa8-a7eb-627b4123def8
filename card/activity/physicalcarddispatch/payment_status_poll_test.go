package activity_test

import (
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	cardNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/card"
	cardActivityPb "github.com/epifi/gamma/api/card/activity/physicalcarddispatch"
	pb "github.com/epifi/gamma/api/card/provisioning"
	orderPb "github.com/epifi/gamma/api/order"
	payPb "github.com/epifi/gamma/api/pay"
)

func TestProcessor_CheckPaymentStatus(t *testing.T) {
	act, md := newProcessorWithMocks(t)
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockGetFundTransferStatus struct {
		enable bool
		req    *payPb.GetFundTransferStatusRequest
		res    *payPb.GetFundTransferStatusResponse
		err    error
	}

	type mockGetOrder struct {
		enable bool
		req    *orderPb.GetOrderRequest
		res    *orderPb.GetOrderResponse
		err    error
	}

	tests := []struct {
		name                      string
		mockGetFundTransferStatus mockGetFundTransferStatus
		mockGetOrder              mockGetOrder
		req                       *cardActivityPb.CheckPaymentStatusRequest
		want                      *cardActivityPb.CheckPaymentStatusResponse
		wantErr                   bool
		setupMockCalls            func()
		assertErr                 func(err error) bool
	}{
		{
			name: "fund transfer pay rpc returns payment status as successful",
			mockGetFundTransferStatus: mockGetFundTransferStatus{
				enable: true,
				req: &payPb.GetFundTransferStatusRequest{
					Identifier: &payPb.GetFundTransferStatusRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "payment-client-req-id",
							Client: workflowPb.Client_DEBIT_CARD,
						},
					},
				},
				res: &payPb.GetFundTransferStatusResponse{
					Status: rpcPb.StatusOk(),
				},
				err: nil,
			},
			setupMockCalls: func() {
				md.physicalCardDispatchRequestDao.EXPECT().GetByCardId(gomock.Any(), "card-id", gomock.Any(), gomock.Any()).Return(
					[]*pb.PhysicalCardDispatchRequest{
						{
							Id:     "request-id",
							CardId: "card-id",
						},
					}, nil)
				md.physicalCardDispatchRequestDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &pb.PhysicalCardDispatchRequest{
					Id:           "request-id",
					CardId:       "card-id",
					SubStatus:    pb.RequestSubStatus_REQUEST_SUB_STATUS_IN_PROGRESS,
					CurrentStage: pb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS,
				}, []pb.PhysicalCardDispatchRequestFieldMask{
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CURRENT_STAGE,
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS,
				}, gomock.Any(), pb.RequestState_REQUEST_STATE_UNSPECIFIED).Return(nil)
			},
			req: &cardActivityPb.CheckPaymentStatusRequest{
				RequestHeader:      &activityPb.RequestHeader{IsLastAttempt: false},
				PaymentClientReqId: "payment-client-req-id",
				CardId:             "card-id",
			},
			want: &cardActivityPb.CheckPaymentStatusResponse{
				PaymentStatus: orderPb.OrderStatus_PAID,
			},
		},
		{
			name: "fund transfer pay rpc returns payment status as failed",
			mockGetFundTransferStatus: mockGetFundTransferStatus{
				enable: true,
				req: &payPb.GetFundTransferStatusRequest{
					Identifier: &payPb.GetFundTransferStatusRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "payment-client-req-id",
							Client: workflowPb.Client_DEBIT_CARD,
						},
					},
				},
				res: &payPb.GetFundTransferStatusResponse{
					Status: rpcPb.NewStatusWithoutDebug(uint32(payPb.GetFundTransferStatusResponse_FAILED), ""),
				},
				err: nil,
			},
			setupMockCalls: func() {
				md.physicalCardDispatchRequestDao.EXPECT().GetByCardId(gomock.Any(), "card-id", gomock.Any(), gomock.Any()).Return(
					[]*pb.PhysicalCardDispatchRequest{
						{
							Id:     "request-id",
							CardId: "card-id",
						},
					}, nil)
				md.physicalCardDispatchRequestDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &pb.PhysicalCardDispatchRequest{
					Id:           "request-id",
					CardId:       "card-id",
					SubStatus:    pb.RequestSubStatus_REQUEST_SUB_STATUS_IN_PROGRESS,
					CurrentStage: pb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS,
				}, []pb.PhysicalCardDispatchRequestFieldMask{
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CURRENT_STAGE,
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS,
				}, gomock.Any(), pb.RequestState_REQUEST_STATE_UNSPECIFIED).Return(nil)
				md.physicalCardDispatchRequestDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &pb.PhysicalCardDispatchRequest{
					Id:           "request-id",
					CardId:       "card-id",
					SubStatus:    pb.RequestSubStatus_REQUEST_SUB_STATUS_UNKNOWN,
					CurrentStage: pb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS,
				}, []pb.PhysicalCardDispatchRequestFieldMask{
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS,
				}, gomock.Any(), pb.RequestState_REQUEST_STATE_UNSPECIFIED).Return(nil)
			},
			req: &cardActivityPb.CheckPaymentStatusRequest{
				RequestHeader:      &activityPb.RequestHeader{IsLastAttempt: false},
				PaymentClientReqId: "payment-client-req-id",
				CardId:             "card-id",
			},
			want: &cardActivityPb.CheckPaymentStatusResponse{
				PaymentStatus: orderPb.OrderStatus_PAYMENT_FAILED,
			},
		},
		{
			name: "fund transfer pay rpc returns error due to connection issues",
			mockGetFundTransferStatus: mockGetFundTransferStatus{
				enable: true,
				req: &payPb.GetFundTransferStatusRequest{
					Identifier: &payPb.GetFundTransferStatusRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "payment-client-req-id",
							Client: workflowPb.Client_DEBIT_CARD,
						},
					},
				},
				res: nil,
				err: errors.New("error"),
			},
			setupMockCalls: func() {
				md.physicalCardDispatchRequestDao.EXPECT().GetByCardId(gomock.Any(), "card-id", gomock.Any(), gomock.Any()).Return(
					[]*pb.PhysicalCardDispatchRequest{
						{
							Id:     "request-id",
							CardId: "card-id",
						},
					}, nil)
				md.physicalCardDispatchRequestDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &pb.PhysicalCardDispatchRequest{
					Id:           "request-id",
					CardId:       "card-id",
					SubStatus:    pb.RequestSubStatus_REQUEST_SUB_STATUS_IN_PROGRESS,
					CurrentStage: pb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS,
				}, []pb.PhysicalCardDispatchRequestFieldMask{
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CURRENT_STAGE,
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS,
				}, gomock.Any(), pb.RequestState_REQUEST_STATE_UNSPECIFIED).Return(nil)
			},
			req: &cardActivityPb.CheckPaymentStatusRequest{
				RequestHeader:      &activityPb.RequestHeader{IsLastAttempt: false},
				PaymentClientReqId: "payment-client-req-id",
				CardId:             "card-id",
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "fund transfer pay rpc returns payment status as record not found",
			mockGetFundTransferStatus: mockGetFundTransferStatus{
				enable: true,
				req: &payPb.GetFundTransferStatusRequest{
					Identifier: &payPb.GetFundTransferStatusRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "payment-client-req-id",
							Client: workflowPb.Client_DEBIT_CARD,
						},
					},
				},
				res: &payPb.GetFundTransferStatusResponse{
					Status: rpcPb.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetOrder: mockGetOrder{
				enable: true,
				req:    &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ClientReqId{ClientReqId: "payment-client-req-id"}},
				res: &orderPb.GetOrderResponse{
					Status: rpcPb.StatusRecordNotFound(),
				},
				err: nil,
			},
			setupMockCalls: func() {
				md.physicalCardDispatchRequestDao.EXPECT().GetByCardId(gomock.Any(), "card-id", gomock.Any(), gomock.Any()).Return(
					[]*pb.PhysicalCardDispatchRequest{
						{
							Id:     "request-id",
							CardId: "card-id",
						},
					}, nil)
				md.physicalCardDispatchRequestDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &pb.PhysicalCardDispatchRequest{
					Id:           "request-id",
					CardId:       "card-id",
					SubStatus:    pb.RequestSubStatus_REQUEST_SUB_STATUS_IN_PROGRESS,
					CurrentStage: pb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS,
				}, []pb.PhysicalCardDispatchRequestFieldMask{
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CURRENT_STAGE,
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS,
				}, gomock.Any(), pb.RequestState_REQUEST_STATE_UNSPECIFIED).Return(nil)
				md.physicalCardDispatchRequestDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &pb.PhysicalCardDispatchRequest{
					Id:           "request-id",
					CardId:       "card-id",
					SubStatus:    pb.RequestSubStatus_REQUEST_SUB_STATUS_NO_DATA_FOUND,
					CurrentStage: pb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS,
				}, []pb.PhysicalCardDispatchRequestFieldMask{
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS,
				}, gomock.Any(), pb.RequestState_REQUEST_STATE_UNSPECIFIED).Return(nil)
			},
			req: &cardActivityPb.CheckPaymentStatusRequest{
				RequestHeader:      &activityPb.RequestHeader{IsLastAttempt: false},
				PaymentClientReqId: "payment-client-req-id",
				CardId:             "card-id",
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "fund transfer pay rpc returns non-success payment status",
			mockGetFundTransferStatus: mockGetFundTransferStatus{
				enable: true,
				req: &payPb.GetFundTransferStatusRequest{
					Identifier: &payPb.GetFundTransferStatusRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "payment-client-req-id",
							Client: workflowPb.Client_DEBIT_CARD,
						},
					},
				},
				res: &payPb.GetFundTransferStatusResponse{
					Status: rpcPb.StatusInternal(),
				},
				err: nil,
			},
			setupMockCalls: func() {
				md.physicalCardDispatchRequestDao.EXPECT().GetByCardId(gomock.Any(), "card-id", gomock.Any(), gomock.Any()).Return(
					[]*pb.PhysicalCardDispatchRequest{
						{
							Id:     "request-id",
							CardId: "card-id",
						},
					}, nil)
				md.physicalCardDispatchRequestDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &pb.PhysicalCardDispatchRequest{
					Id:           "request-id",
					CardId:       "card-id",
					SubStatus:    pb.RequestSubStatus_REQUEST_SUB_STATUS_IN_PROGRESS,
					CurrentStage: pb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS,
				}, []pb.PhysicalCardDispatchRequestFieldMask{
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CURRENT_STAGE,
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS,
				}, gomock.Any(), pb.RequestState_REQUEST_STATE_UNSPECIFIED).Return(nil)
			},
			req: &cardActivityPb.CheckPaymentStatusRequest{
				RequestHeader:      &activityPb.RequestHeader{IsLastAttempt: false},
				PaymentClientReqId: "payment-client-req-id",
				CardId:             "card-id",
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "fund transfer pay rpc returns payment status as in progress",
			mockGetFundTransferStatus: mockGetFundTransferStatus{
				enable: true,
				req: &payPb.GetFundTransferStatusRequest{
					Identifier: &payPb.GetFundTransferStatusRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "payment-client-req-id",
							Client: workflowPb.Client_DEBIT_CARD,
						},
					},
				},
				res: &payPb.GetFundTransferStatusResponse{
					Status: rpcPb.ExtendedStatusInProgress(),
				},
				err: nil,
			},
			setupMockCalls: func() {
				md.physicalCardDispatchRequestDao.EXPECT().GetByCardId(gomock.Any(), "card-id", gomock.Any(), gomock.Any()).Return(
					[]*pb.PhysicalCardDispatchRequest{
						{
							Id:     "request-id",
							CardId: "card-id",
						},
					}, nil)
				md.physicalCardDispatchRequestDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &pb.PhysicalCardDispatchRequest{
					Id:           "request-id",
					CardId:       "card-id",
					SubStatus:    pb.RequestSubStatus_REQUEST_SUB_STATUS_IN_PROGRESS,
					CurrentStage: pb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS,
				}, []pb.PhysicalCardDispatchRequestFieldMask{
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_CURRENT_STAGE,
					pb.PhysicalCardDispatchRequestFieldMask_PHYSICAL_CARD_DISPATCH_REQUEST_SUB_STATUS,
				}, gomock.Any(), pb.RequestState_REQUEST_STATE_UNSPECIFIED).Return(nil)
			},
			req: &cardActivityPb.CheckPaymentStatusRequest{
				RequestHeader:      &activityPb.RequestHeader{IsLastAttempt: false},
				PaymentClientReqId: "payment-client-req-id",
				CardId:             "card-id",
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			if tt.mockGetFundTransferStatus.enable {
				md.payClient.EXPECT().GetFundTransferStatus(gomock.Any(), tt.mockGetFundTransferStatus.req).
					Return(tt.mockGetFundTransferStatus.res, tt.mockGetFundTransferStatus.err)
			}
			if tt.mockGetOrder.enable {
				md.orderClient.EXPECT().GetOrder(gomock.Any(), tt.mockGetOrder.req).
					Return(tt.mockGetOrder.res, tt.mockGetOrder.err)
			}

			var result *cardActivityPb.CheckPaymentStatusResponse
			got, err := env.ExecuteActivity(cardNs.CheckPaymentStatus, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("CheckPaymentStatus() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("CheckPaymentStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("CheckPaymentStatus() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("CheckPaymentStatus() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
