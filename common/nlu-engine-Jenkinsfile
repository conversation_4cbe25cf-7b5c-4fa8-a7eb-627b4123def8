pipeline {
    agent { label 'jenkins-cloud-worker' }
    parameters
    {
        string(name: '<PERSON><PERSON><PERSON>', defaultValue: 'master', description: 'Enter branch')
		choice(name: 'TARGET', choices: ['nlu-engine'], description: 'Launching Pipeline for { nlu-engine }')
        extendedChoice(
            defaultValue: '',
            description: 'Select ENVs you want to deploy server to',
            multiSelectDelimiter: ',',
            name: '<PERSON>N<PERSON>',
            value:'uat',
            quoteValue: false,
            saveJSONParameterToFile: false,
            type: 'PT_CHECKBOX',
            visibleItemCount: 10
        )
        string(name: 'IMAGE_ID', defaultValue: '', description: 'If you provide Image Id e.g ami-xxxxxxxxxx, It will not build the binary.')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        TARGET="${params.TARGET}"
        ENV="${params.ENV}"
        BRANCH="${params.BRANCH}"
	    REPO_NAME="askfi-nlu-engine"
    }
    stages {
        stage('Infra clone') {
            steps {
                dir('infra') {
                    deleteDir()
                    git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/infra.git', branch: "master"
                }
                script {
                    if ( params.REFRESH == true ) {
                        echo "Jenkins file was loaded....... finish build now"
                        currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                        sleep(5)
                    }
                    IMAGE_ID = "${params.IMAGE_ID}"
                    currentBuild.displayName = "#${BUILD_NUMBER}-${ENV}-${TARGET}-${BRANCH}"
                    currentBuild.description = "#${BUILD_NUMBER}-${ENV}-${TARGET}-${BRANCH}"
                }
            }
        }
        stage('Build binary') {
            when {
                expression { IMAGE_ID == '' }
            }
            steps {
		    dir('askfi-nlu-engine') {
                    // Wipe the workspace so we are building completely clean
                    deleteDir()
		    git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/askfi-nlu-engine.git', branch: "${BRANCH}"
		    sh "chmod 777 ${env.WORKSPACE}/Jenkins/scripts/nlu-engine/nlu_engine.sh && ${env.WORKSPACE}/Jenkins/scripts/nlu-engine/nlu_engine.sh"
                }
            }
        }
        stage('Build packer') {
             environment {
                BASE_AMI="epifi-python-golden-image"
                AMI_NAME="${TARGET}"
                SERVICE_TYPE="python"
                SERVICE_NAME="${TARGET}"
                DEST_DIR_PATH="/home/<USER>/EmailParser"
                SYSTEMD_SERVICE_SOURCE="python-services/templates/${TARGET}-tmpl.service"
                SYSTEMD_SERVICE_DEST="${TARGET}-tmpl.service"
                SOURCE_DIR_PATH="${WORKSPACE}/EmailParser/output/EmailParser/"
                PACKER_LOG="${WORKSPACE}/EmailParser/output/${TARGET}_packer.log"
                IMAGE_ID_FILE="${WORKSPACE}/EmailParser/output/image_id.txt"
                MANIFEST_FILE_NAME="manifest_${TARGET}"
                TARGETAPP="${TARGET}"
                SETUP_SCRIPT_PATH="scripts/setup.sh"
            }
            when {
                expression { IMAGE_ID == '' }
            }
            steps {
                dir('infra') {
		    sh "chmod 777 ${env.WORKSPACE}/Jenkins/scripts/nlu-engine/packer.sh && ${env.WORKSPACE}/Jenkins/scripts/nlu-engine/packer.sh"
                }
                script {
                    IMAGE_ID = readFile(file: './askfi-nlu-engine/output/image_id.txt')
                    echo "${IMAGE_ID}"
                }
            }
        }
        stage('Deployment') {
			when {
                expression { return IMAGE_ID.startsWith('ami-') }
            }
            steps {
				script {
					def envs = [:]
					"$ENV".split(',').each {
						envs["${it}"] = {
							stage("Deploy to ${it}") {
								echo "Environment is: ${it}"
								dir ("${env.WORKSPACE}/${it}")
								{
									deleteDir()
									sh "cp -r ${env.WORKSPACE}/infra/terraform ${env.WORKSPACE}/${it}/"
								}
								dir("${env.WORKSPACE}/${it}/terraform/python-services/${env.TARGET}")
								{
									sh "mkdir -p $HOME/.terraform.d/plugin-cache"
									sh "export TF_PLUGIN_CACHE_DIR=$HOME/.terraform.d/plugin-cache"
									sh "terraform init -backend-config=s3-backend-${it}.conf"
									sh "terraform apply -input=false -auto-approve -var-file=../../env/${it}.tf -var min=1 -var max=1 -var desired=1 -var image_id=${IMAGE_ID}"
								}
							}
						}
					}
					parallel envs
				}
			}
		}
    }
}
