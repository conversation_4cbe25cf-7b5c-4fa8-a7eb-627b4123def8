package activity

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	jarvisactivitypb "github.com/epifi/gamma/api/jarvis/activity"
	jarvispb "github.com/epifi/gamma/api/jarvis/edith"
	"github.com/epifi/gamma/api/jarvis/frontend"
	jarvistypes "github.com/epifi/gamma/api/jarvis/types"
)

func (a *Processor) ExtractTicketInfo(ctx context.Context, req *jarvisactivitypb.ExtractTicketInfoRequest) (*jarvisactivitypb.ExtractTicketInfoResponse, error) {

	var ticketForm = req.GetTicketForm()
	var ticketType = req.GetTicketType()
	var email = req.GetEmail()
	var formVers *jarvistypes.Ticket
	var err error
	if ticketType == a.frontendService.ParseType(jarvistypes.FormType_FORM_TYPE_PROD_S3_DATA_ACCESS_REQUEST.String()) {
		formVers, err = a.frontendService.ExtractFromProdS3DataAccessTicket(ctx, ticketForm, req.GetIsNew(), email)
		if err != nil {
			logger.Error(ctx, "Error while ExtractFromProdS3DataAccessTicket", zap.Error(err))
			return nil, err
		}
	} else if ticketType == a.frontendService.ParseType(jarvistypes.FormType_FORM_TYPE_AWS_ACCESS_REQUEST.String()) {
		formVers, err = a.frontendService.ExtractFromAwsAccessTicket(ctx, ticketForm, req.GetIsNew(), email)
		if err != nil {
			logger.Error(ctx, "Error while ExtractFromAwsAccessTicket", zap.Error(err))
			return nil, err
		}
	}
	return &jarvisactivitypb.ExtractTicketInfoResponse{FormVer: formVers}, nil
}

func (a *Processor) ExecuteTicketBackend(ctx context.Context, req *jarvisactivitypb.ExecuteTicketBackendRequest) (*jarvisactivitypb.ExecuteTicketBackendResponse, error) {

	var err error
	logger.Debug(ctx, "ExecuteTicketBackend")
	if req.GetTicketForm().GetFormType().String() == jarvistypes.FormType_FORM_TYPE_PROD_S3_DATA_ACCESS_REQUEST.String() || a.frontendService.ParseType(req.GetTicketForm().GetFormType().String()) == jarvistypes.FormType_FORM_TYPE_PROD_S3_DATA_ACCESS_REQUEST.String() {
		logger.Debug(ctx, "Got Prod S3 Data Access Request backend activity")
		err = a.frontendService.ProdS3DataAccessBackend(ctx, req.GetTicketForm(), req.GetTicketVersId())
		if err != nil {
			logger.Error(ctx, "Error while ProdS3DataAccessRequest", zap.Error(err))
			return nil, err
		}
	} else if req.GetTicketForm().GetFormType().String() == jarvistypes.FormType_FORM_TYPE_AWS_ACCESS_REQUEST.String() || a.frontendService.ParseType(req.GetTicketForm().GetFormType().String()) == jarvistypes.FormType_FORM_TYPE_AWS_ACCESS_REQUEST.String() {
		logger.Debug(ctx, "Got Aws Access Request backend activity")
		err = a.frontendService.AwsAccessBackend(ctx, req.GetTicketForm(), req.GetTicketVersId())
		if err != nil {
			logger.Error(ctx, "Error while AwsAccessBackendRequest", zap.Error(err))
			return nil, err
		}
	}
	return &jarvisactivitypb.ExecuteTicketBackendResponse{}, nil
}

func (a *Processor) CreateNewTicket(ctx context.Context, req *jarvisactivitypb.CreateNewTicketActRequest) (*jarvisactivitypb.CreateNewTicketActResponse, error) {

	createFormVersReq := &jarvispb.CreateTicketRequest{
		FormVers: req.GetFormVer(),
	}

	createFormVersResp, err := a.edithService.CreateTicket(ctx, createFormVersReq)
	if err2 := epifigrpc.RPCError(createFormVersResp, err); err2 != nil {
		logger.Error(ctx, "failed to create ticket version", zap.Error(err))
		return nil, err
	}
	entityId := &frontend.EntityId{EntityId: &frontend.EntityId_FormVersId{FormVersId: createFormVersResp.GetFormVers().GetId()}}
	resp := &jarvisactivitypb.CreateNewTicketActResponse{
		EntityId: entityId,
		FormVer:  createFormVersResp.GetFormVers(),
	}
	return resp, nil
}

func (a *Processor) SendSlackNotif(ctx context.Context, req *jarvisactivitypb.SendSlackNotifRequest) (*jarvisactivitypb.SendSlackNotifResponse, error) {

	switch req.GetNotifType() {
	case "TicketCreated":
		err := a.frontendService.CreateTicketSlackMessage(ctx, req.GetFormVer().GetFormData(), req.GetFormVer().GetId())
		if err != nil {
			logger.Error(ctx, "failed to send create ticket slack message", zap.Error(err))
			return nil, err
		}
	case "TicketApproved", "TicketAutoApproved":
		err := a.frontendService.ApproveTicketSlackMessage(ctx, req.GetFormVer().GetFormData(), req.GetFormVer().GetId())
		if err != nil {
			logger.Error(ctx, "failed to send ticket approved slack message", zap.Error(err))
			return nil, err
		}
	case "TicketDeclined":
		err := a.frontendService.DeclineTicketSlackMessage(ctx, req.GetFormVer().GetFormData(), req.GetFormVer().GetId())
		if err != nil {
			logger.Error(ctx, "failed to send ticket declined slack message", zap.Error(err))
			return nil, err
		}
	}
	resp := &jarvisactivitypb.SendSlackNotifResponse{}
	return resp, nil
}

func (a *Processor) GetTicketLatestVersion(ctx context.Context, req *jarvisactivitypb.GetLatestTicketVersionRequest) (*jarvisactivitypb.GetLatestTicketVersionResponse, error) {

	// var ticketVersId string
	var getTicketVersResp *jarvispb.GetTicketsResponse
	var err error
	switch {
	case req.GetFormTicket() != nil:
		getTicketVersResp, err = a.edithService.GetTickets(ctx, &jarvispb.GetTicketsRequest{
			FieldMask:  a.ticketFM,
			FormVerIds: []string{req.GetFormTicket().GetEntityId().GetFormVersId()},
		})
	case req.GetTicketVersId() != "":
		getTicketVersResp, err = a.edithService.GetTickets(ctx, &jarvispb.GetTicketsRequest{
			FieldMask:  a.ticketFM,
			FormVerIds: []string{req.GetTicketVersId()},
		})
	case req.GetTicketId() != "":
		getTicketVersResp, err = a.edithService.GetTickets(ctx, &jarvispb.GetTicketsRequest{
			FieldMask: a.ticketFM,
			FormIds:   []string{req.GetTicketId()},
		})
	default:
		logger.Error(ctx, "ticket id not found", zap.Error(err))
		return nil, nil
	}
	if err2 := epifigrpc.RPCError(getTicketVersResp, err); err2 != nil {
		logger.Error(ctx, "failed to get latest ticket version", zap.Error(err2))
		return nil, err2
	}
	resp := &jarvisactivitypb.GetLatestTicketVersionResponse{
		FormVer: getTicketVersResp.GetVersions()[0],
	}
	return resp, nil
}

func (a *Processor) UpdateForm(ctx context.Context, req *jarvisactivitypb.UpdateFormActRequest) (*jarvisactivitypb.UpdateFormActResponse, error) {

	_, err := a.edithService.UpdateForm(ctx, req.GetFormVer())
	if err != nil {
		logger.Error(ctx, "failed to update latest updated by in form", zap.Error(err))
		return nil, err
	}
	resp := &jarvisactivitypb.UpdateFormActResponse{}

	return resp, nil
}

func (a *Processor) UpdateTicketStatus(ctx context.Context, req *jarvisactivitypb.UpdateTicketStatusActRequest) (*jarvisactivitypb.UpdateTicketStatusActResponse, error) {

	beResp, err := a.edithService.UpdateTicket(ctx, &jarvispb.UpdateTicketRequest{
		FormVers: &jarvistypes.Ticket{
			Id:     req.GetTicketVersId(),
			Status: req.GetStatus(),
		},
		JarvisHeader:    req.GetJarvisHeader(),
		UpdateFieldMask: a.formVersStatusUpdateFM,
	})
	if err2 := epifigrpc.RPCError(beResp, err); err2 != nil {
		logger.Error(ctx, "failed to update ticket status", zap.Error(err))
		return nil, err
	}
	resp := &jarvisactivitypb.UpdateTicketStatusActResponse{}
	return resp, nil
}

func (a *Processor) GetTicketFromEntityId(ctx context.Context, req *jarvisactivitypb.GetTicketFromEntityIdActRequest) (*jarvisactivitypb.GetTicketFromEntityIdActResponse, error) {

	ticket, err := a.frontendService.GetFormFromEntityID(ctx, req.GetTicketVersId(), "", "")
	if err != nil {
		logger.Error(ctx, "failed to get ticket from entity id", zap.Error(err))
		return nil, err
	}
	resp := &jarvisactivitypb.GetTicketFromEntityIdActResponse{
		TicketForm: ticket,
	}
	return resp, nil
}
