package processor

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	developerPb "github.com/epifi/gamma/api/salaryprogram/developer"
	"github.com/epifi/gamma/salaryprogram/dao"
)

type DynamicUiElementVariantProcessor struct {
	dUIElementVariantDao dao.IDynamicUIElementDao
}

func NewDynamicUiElementVariantProcessor(dUIElementVariantDao dao.IDynamicUIElementDao) *DynamicUiElementVariantProcessor {
	return &DynamicUiElementVariantProcessor{dUIElementVariantDao: dUIElementVariantDao}
}

func (r *DynamicUiElementVariantProcessor) FetchParamList(ctx context.Context, entity developerPb.SalaryProgramEntity) ([]*db_state.ParameterMeta, error) {

	paramList := []*db_state.ParameterMeta{
		{
			Name:            variantName,
			Label:           "Variant Name",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (r *DynamicUiElementVariantProcessor) FetchData(ctx context.Context, entity developerPb.SalaryProgramEntity, filters []*db_state.Filter) (string, error) {

	if len(filters) == 0 {
		return "", ErrNilFilter
	}

	varintName := filters[0].GetStringValue()
	resp, err := r.dUIElementVariantDao.GetByVariantName(ctx, varintName)
	if err != nil {
		logger.Error(ctx, "error while fetching dynamic ui element variant", zap.Error(err))
		return "", errors.Wrap(err, "error while fetching dynamic ui element variant")
	}

	marshalledRes, marshalledErr := protojson.Marshal(resp)
	if marshalledErr != nil {
		logger.Error(ctx, "error marshalling response", zap.Error(marshalledErr))
		return "", errors.Wrap(marshalledErr, "error marshalling response")
	}
	return string(marshalledRes), nil
}
