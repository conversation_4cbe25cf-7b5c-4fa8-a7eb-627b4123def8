Application:
  Environment: "prod"
  Name: "dreamfolks_recon"

CasperDb:
  Name: "casper"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  DbType: "PGDB"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbCredentials: "prod/rds/epifimetis/casper"

Tracing:
  Enable: true

SalaryProgramS3BucketName: "epifi-prod-salaryprogram"
