package main

import (
	"io/ioutil"
	"os"
	"strings"

	"gopkg.in/yaml.v2"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/pkg/cfg"
)

const (
	serverEndpointConfigFile = "endpoints"
)

var (
	envArg            string
	configsToPreserve []string
)

// copy endpoints-<env>.yml to endpoints-development.yml except any configs to be preserved
// command to execute
// 1. CONFIG_DIR=$CONFIG_DIR go run ./scripts/platform/gprc_replayer_server/dev_endpoint_helper/helper.go <envArg> <configsToPreserve>
// 2. CONFIG_DIR=$CONFIG_DIR go run ./scripts/platform/gprc_replayer_server/dev_endpoint_helper/helper.go <envArg>, by default configsToPreserve is `FrontendEndpoint`
// 3. CONFIG_DIR=$CONFIG_DIR go run ./scripts/platform/gprc_replayer_server/dev_endpoint_helper/helper.go,  by default envArg is `qa` and configsToPreserve is `FrontendEndpoint`
// nolint:funlen, gocritic, gosec
func main() {

	if len(os.Args) > 2 {
		envArg = os.Args[1]
		configsToPreserve = strings.Split(os.Args[2], ",")
	} else if len(os.Args) > 1 {
		envArg = os.Args[1]
		configsToPreserve = []string{"FrontendEndpoint"}
	} else {
		envArg = "qa"
		configsToPreserve = []string{"FrontendEndpoint"}
	}

	logger.Init(envArg)

	configDir, err := cfg.GetConfigDir()
	if err != nil {
		logger.Panic("error while getting config dir: %s", zap.Error(err))
	}

	// load YML config from source env file
	srcConfigPath := cfg.GetConfigPathFromConfigName(serverEndpointConfigFile, envArg, configDir)
	srcYaml, err := ioutil.ReadFile(srcConfigPath)
	if err != nil {
		logger.Panic("Error reading source YAML file: %v", zap.Error(err))
	}
	var srcConfigMap map[string]interface{}
	err = yaml.Unmarshal(srcYaml, &srcConfigMap)
	if err != nil {
		logger.Panic("Error unmarshaling source YAML: %v", zap.Error(err))
	}

	// load YML config from destination development env file
	destConfigPath := cfg.GetConfigPathFromConfigName(serverEndpointConfigFile, "development", configDir)
	destYaml, err := ioutil.ReadFile(destConfigPath)
	if err != nil {
		logger.Panic("Error reading destination development YAML file: %v", zap.Error(err))
	}
	var destConfigMap map[string]interface{}
	err = yaml.Unmarshal(destYaml, &destConfigMap)
	if err != nil {
		logger.Panic("Error unmarshaling destination development YAML: %v", zap.Error(err))
	}

	// store config values which needs to be preserved
	// and update source env config map
	for _, k := range configsToPreserve {
		val, ok := destConfigMap[k]
		if ok {
			srcConfigMap[k] = val
		}
	}

	// marshal final config from source config map
	finalConf, err := yaml.Marshal(srcConfigMap)
	if err != nil {
		logger.Panic("Error serializing final config: %s", zap.Error(err))
	}

	// write final config to destination config path, which is pkg/cfg/config/endpoints-development.yml
	if err = os.WriteFile(destConfigPath, finalConf, 0644); err != nil {
		logger.Panic("Error writing destination yaml file: %s", zap.Error(err))
	}

}
