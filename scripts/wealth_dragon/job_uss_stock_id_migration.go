package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/logger"
	// #nosec G304 -- we're using a validated path
	"github.com/epifi/gamma/usstocks/order/dao/model"
)

const (
	// SymbolsCSVPath is the path to the CSV file containing stock symbols and IDs
	SymbolsCSVPath = "testdata/symbols.csv"
)

// jobUssStockIdMigration handles updating stock_id in account activities based on symbols
type jobUssStockIdMigration struct {
	ussAlpacaDB *gorm.DB
}

// PerformJob implements the JobProcessor interface
func (j *jobUssStockIdMigration) PerformJob(ctx context.Context, req *JobRequest) error {
	logger.InfoNoCtx("Reading stock symbols and IDs from CSV...", zap.String("path", SymbolsCSVPath))

	// Get stock symbols and IDs from CSV
	symbolToStockID, err := getStocksMapFromCSV(SymbolsCSVPath)
	if err != nil {
		logger.ErrorNoCtx("Failed to read stock data from CSV", zap.Error(err))
		return err
	}

	logger.InfoNoCtx("Loaded stocks from CSV", zap.Int("count", len(symbolToStockID)))

	// Print some sample data to verify
	count := 0
	logger.InfoNoCtx("Sample stock data:")
	for symbol, stockID := range symbolToStockID {
		if count < 5 { // Print first 5 entries
			logger.InfoNoCtx("Stock mapping",
				zap.String("symbol", symbol),
				zap.String("stock_id", stockID))
			count++
		} else {
			break
		}
	}

	// Track statistics
	updatesSucceeded := 0
	updatesFailed := 0

	// Process each symbol-stockID pair
	for symbol, stockID := range symbolToStockID {
		// Using GORM to update all matching records
		result := j.ussAlpacaDB.Model(&model.AccountActivity{}).
			Where("symbol = ?", symbol).
			Where("stock_id IS NULL OR stock_id = ''").
			Update("stock_id", stockID)

		if result.Error != nil {
			logger.ErrorNoCtx("Failed to update activities with symbol",
				zap.String("symbol", symbol),
				zap.Error(result.Error))
			updatesFailed++
			continue
		}

		// Get the number of rows affected
		rowsAffected := result.RowsAffected
		logger.InfoNoCtx("Updated activities with symbol",
			zap.String("symbol", symbol),
			zap.Int64("count", rowsAffected))
		updatesSucceeded++
	}

	// Print final statistics
	logger.InfoNoCtx("Stock ID update completed",
		zap.Int("total_symbols", len(symbolToStockID)),
		zap.Int("updates_succeeded", updatesSucceeded),
		zap.Int("updates_failed", updatesFailed))

	return nil
}

// getStocksMapFromCSV reads the CSV file and creates a map of symbol -> stock_id
func getStocksMapFromCSV(filePath string) (map[string]string, error) {
	// Validate file path to prevent path traversal
	if !isValidFilePath(filePath) {
		return nil, fmt.Errorf("invalid file path: %s", filePath)
	}

	// Open the CSV file with a clean, validated path
	cleanPath := filepath.Clean(filePath)
	file, err := os.Open(cleanPath) // #nosec G304 -- path is validated above
	if err != nil {
		return nil, fmt.Errorf("failed to open CSV file: %w", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			fmt.Printf("failed to close file: %v\n", err)
		}
	}()

	// Create a new CSV reader
	reader := csv.NewReader(file)

	// Read all records
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("failed to read CSV records: %w", err)
	}

	// Create a map of symbol -> stock_id
	symbolToStockID := make(map[string]string)

	// Based on the actual CSV format we saw, the file has stockID in first column and symbol in second
	for _, record := range records {
		if len(record) >= 2 {
			stockID := record[0]
			symbol := record[1]
			symbolToStockID[symbol] = stockID
		}
	}
	return symbolToStockID, nil
}

func isValidFilePath(filePath string) bool {
	if filePath != SymbolsCSVPath {
		return false
	}
	cleanPath := filepath.Clean(filePath)
	if strings.Contains(cleanPath, "..") {
		return false
	}
	if filepath.IsAbs(cleanPath) {
		return false
	}
	return true
}
