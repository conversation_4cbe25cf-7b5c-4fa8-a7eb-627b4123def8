Application:
  Environment: "development"
  Name: "p2p_investment_non_prod_god_script"


EpifiDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi"
  SSLMode: "disable"
  EnableDebug: true
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

P2PInvLLDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "p2pinvestment_liquiloans"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Aws:
  Region: "ap-south-1"
