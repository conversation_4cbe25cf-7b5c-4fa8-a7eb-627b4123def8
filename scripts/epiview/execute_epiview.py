import os
import logging
from scripts.utils.commons import get_spark_config
from scripts.utils.commons import get_setting_ini_config
from scripts.utils.spark_submit import submit_spark_job
from scripts.utils.spark_submit import create_args_payload
from scripts.utils.job_info import set_job_id, get_job_id
import scripts.utils.slack_alert as slack_alerts
from datetime import datetime
import scripts.utils.commons as common
import scripts.utils.db_util as db_utils
from scripts.utils.db_util import update_query
import uuid
import airflow
"""
Executes wormhole silver -> gold pipeline
"""
def get_last_successful_run(use_case, table):
    key = use_case + '_' + table
    sql = "SELECT epiview_last_successful_run from watermarking_epiview_table where table_key = %s"
    epiview_last_read_datetime = db_utils.fetch_query(sql,[key])
    datetime_object = datetime.strptime(epiview_last_read_datetime[0][0], '%Y-%m-%d %H:%M:%S')
    return datetime_object


def set_success_time_epiview(use_case,table,success_time):
    key = use_case + '_' + table
    sql = f"SELECT epiview_last_successful_run from watermarking_epiview_table where table_key = '{key}'"
    events_last_read_datetime = db_utils.fetch_query(sql, [])
    if len(events_last_read_datetime) > 0:
        update_sql = f"UPDATE watermarking_epiview_table SET epiview_last_successful_run = '{success_time}' where table_key = '{key}'"
        logging.debug(update_sql)
        update_query(update_sql, [success_time])
    else:
        insert_sql = f"insert into watermarking_epiview_table (table_key, epiview_last_successful_run) VALUES ('{key}','{success_time}')"
        logging.debug(insert_sql)
        update_query(insert_sql, [])

def set_dag_monitoring_details(table_type, dag_node , dag_name, job_start_time, job_end_time, status, org, area):
    insert_sql = f"insert into dag_monitoring_details (id, table_type, dag_node, dag_name, job_start_time, job_end_time, node_status, org, area_name) VALUES ('{uuid.uuid4()}','{table_type}','{dag_node}','{dag_name}','{job_start_time}','{job_end_time}','{status}','{org}','{area}')"
    logging.debug(insert_sql)
    update_query(insert_sql, [])

def execute_epiview(**kwargs):
    pid = os.getpid()
    job_mode = kwargs['jobMode']
    dag_name = kwargs['dag_name']
    config_name = kwargs['config_name']
    is_sandbox_run = False
    job_start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    session_name = config_name + "_epiview"

    trace_id = common.generate_trace_id(session_name, job_start_time)
    logging.info(f"trace_id: {trace_id}")   # Log trace id in airflow logs. Can be used for finding logs in Kibana

    setting_init_config = common.get_setting_ini_config()
    env = setting_init_config['ENV']
    if env=="data-staging":
        session_name = config_name + "_epiview_" + env
    area_names = common.get_file_from_s3('{pipeline_config_path}monitoring/base_table_area_name.json'.format(pipeline_config_path = setting_init_config['PIPELINE_CONFIG_PATH']))
    area = area_names[config_name.upper()] if config_name.upper() in area_names else 'null'
    if kwargs['dag_run'].conf is not None:
        dag_run_conf = kwargs['dag_run'].conf
        logging.info("Dag Run Conf Dict found, using them")
        #logging.info(type(dag_run_conf['sandbox_run']))
        if 'sandbox_run' in dag_run_conf:
            if dag_run_conf['sandbox_run']:
                is_sandbox_run = True
                session_name = config_name + "_sandbox_epiview"
                logging.info("Running DNA in sandbox mode")

    if 'redis_host' in kwargs:
        redis_host_name = kwargs['redis_host']
    else:
        redis_host_name = 'redis'
    set_job_id(pid, dag_name, config_name)
    try:
        exec_date = kwargs['ds']
        try:
            last_successful_run = "2021-01-01 00:00:00"
            if kwargs['prev_start_date_success'] is not None:
                last_successful_run = get_last_successful_run("epiview", config_name).strftime("%Y-%m-%d %H:%M:%S")
                logging.info("last_successful_run from watermarking_epiview_table {0}".format(last_successful_run))
                #last_successful_run = kwargs['prev_start_date_success'].strftime("%Y-%m-%d %H:%M:%S")
            dag_run_conf = kwargs['dag_run'].conf
            if dag_run_conf is not None:
                logging.info(kwargs['dag_run'].conf)
                last_successful_run = dag_run_conf['incremental_time'] if 'incremental_time' in dag_run_conf.keys() else last_successful_run

            logging.info("Taking value of last successfull run as :")
            logging.info(last_successful_run)
        except Exception as e:
            logging.info("For the first Time run provide 'incremental_time' in dag run in 'yyyy-MM-dd HH:mm:ss' format.")
            logging.info("Setting Default value for Last successful run.")
            last_successful_run = "2021-01-01 00:00:00"

        spark_conf = get_spark_config()
        setting_init_config = get_setting_ini_config()
        conf_bucket = setting_init_config['CONF_BUCKET_ID']
        platform_conf_path = "s3://{conf_bucket}/{platform_conf_path}".format(conf_bucket=conf_bucket,platform_conf_path=setting_init_config['PLATFORM_CONFIG_PATH'])
        spark_config_id = kwargs['spark_config_id']
        parallelism_count = spark_conf[spark_config_id]['hudi_parallelization']
        jar_path = spark_conf['epiview_jar_file_path']
        if is_sandbox_run:
            jar_path = spark_conf['sandbox_epiview_jar_file_path']
        
        args = ['--env', env, '--jobMode', job_mode, '--configName', config_name, '--sparkConfigID', spark_config_id,
                '--jobID', get_job_id(pid), '--parallelism', parallelism_count, '--platfomConfigPath', platform_conf_path,
                '--lastSuccessfulRun', last_successful_run, '--execDate', exec_date, '--sandBoxRun', is_sandbox_run, '--traceId', trace_id]
        class_name = 'com.epifi.dataplatform.epiview.EpiviewDriver'
        spark_submit_payload = create_args_payload(args, jar_path, spark_conf, spark_config_id, class_name, session_name, redis_host_name)
        #submitting spark job to particular emr
        if 'emr_master_dns' in kwargs:
            submit_spark_job(spark_submit_payload,emr_master_dns=kwargs['emr_master_dns'])
        else:
            submit_spark_job(spark_submit_payload)
        job_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        set_success_time_epiview("epiview", config_name, job_start_time)
        set_dag_monitoring_details("dna", config_name , dag_name, job_start_time, job_end_time, "success", "epifi", area)

    except airflow.exceptions.AirflowException as e:
        job_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        set_dag_monitoring_details("dna", config_name, dag_name, job_start_time, job_end_time, "Killed", "epifi", area)
        message = '''
                :red_circle: Task Killed
                *Job ID* : {0}
                *Dag Name* : {1}
                *Task Name* : {2}
                '''.format(get_job_id(pid), dag_name, config_name)
        logging.error(str(e), extra={'jobID': get_job_id(pid), 'level': 'error'})
        slack_alerts.send_slack_alert(message)
        raise Exception(e)
    except Exception as e:
        job_end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        set_dag_monitoring_details("dna", config_name, dag_name, job_start_time, job_end_time, "failed", "epifi",area)
        message = '''
        :red_circle: Task Failed
        *Job ID* : {0}
        *Dag Name* : {1}
        *Task Name* : {2}
        '''.format(get_job_id(pid), dag_name, config_name)
        logging.error(str(e), extra={'jobID': get_job_id(pid), 'level': 'error'})
        slack_alerts.send_slack_alert(message)
        raise Exception(e)


