package main

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"

	"github.com/pkg/errors"
	temporalEnumsPb "go.temporal.io/api/enums/v1"
	"go.temporal.io/sdk/client"

	enachWorkflowPb "github.com/epifi/gamma/api/recurringpayment/enach/workflow"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/scripts/recurringpayments/schedule_enach_workflows/config"
)

// nolint: dupl
func schedulePresentEnachExecutionWorkflow(ctx context.Context, temporalClient client.Client, conf *config.Config) error {

	presentEnachExecutionWorkflowConfig := conf.WorkflowNameToConfigMap[presentEnachExecutionWorkflowName]
	// delete existing schedules for present enach execution workflow.
	lastVersion := presentEnachExecutionWorkflowConfig.CurrentVersion - 1
	if lastVersion != 0 {
		oldSchId := getSchId(presentEnachExecutionWorkflowName, lastVersion)
		err := deleteWorkflowSchedule(ctx, temporalClient, oldSchId)
		if err != nil {
			logger.Error(ctx, "error while deleting the previous deleting the existing schedules of the present enach execution workflow with id", zap.String("schId", oldSchId))
			return errors.Wrap(err, "failed to delete the existing workflow schedule for workflow: present enach execution")
		}
	}

	newSchId := getSchId(presentEnachExecutionWorkflowName, presentEnachExecutionWorkflowConfig.CurrentVersion)
	workflowId := getWorkflowId(newSchId)
	spec := client.ScheduleSpec{
		CronExpressions: []string{presentEnachExecutionWorkflowConfig.ScheduleCron},
		TimeZoneName:    datetime.IST_TIME_ZONE,
	}
	request := &enachWorkflowPb.PresentEnachExecutionWorkflowPayload{}

	_, err := temporalClient.ScheduleClient().Create(ctx, client.ScheduleOptions{
		ID:   newSchId,
		Spec: spec,
		Action: &client.ScheduleWorkflowAction{
			ID:        workflowId,
			Workflow:  rpNs.PresentEnachExecution,
			TaskQueue: conf.Application.TaskQueue,
			Args:      []interface{}{request},
		},
		CatchupWindow:  1 * time.Hour,
		Overlap:        temporalEnumsPb.SCHEDULE_OVERLAP_POLICY_SKIP,
		PauseOnFailure: false,
	})
	if err != nil {
		logger.Error(ctx, "failed to create the schedule for present enach execution workflow with id", zap.String("schId", newSchId))
		return errors.Wrap(err, "failed to create the schedule for present enach execution workflow")
	}
	logger.Info(ctx, "Created schedule for present enach execution workflow with id %s ", zap.String("schId", newSchId))

	return nil
}

// nolint: funlen
// nolint: dupl
func scheduleProcessEnachPresentationResponse(ctx context.Context, temporalClient client.Client, conf *config.Config) error {

	processEnachPresentationResponseWorkflowConfig := conf.WorkflowNameToConfigMap[processEnachPresentationResponseWorkflowName]
	// delete the existing schedules for present enach execution workflow.
	schedulePrefix := processEnachPresentationResponseWorkflowName + "_currentDay"
	lastVersion := processEnachPresentationResponseWorkflowConfig.CurrentVersion - 1
	if lastVersion != 0 {
		oldSchIdforProcessPresentationResponseCurrentDate := getSchId(schedulePrefix, lastVersion)
		err := deleteWorkflowSchedule(ctx, temporalClient, oldSchIdforProcessPresentationResponseCurrentDate)
		if err != nil {
			logger.Error(ctx, "error while deleting the previous deleting the existing schedules of the process enach presentation response workflow for current day with id ", zap.String("schId", oldSchIdforProcessPresentationResponseCurrentDate))
			return errors.Wrap(err, "failed to delete the existing workflow schedule for workflow: process enach presentation response")
		}
	}

	spec := client.ScheduleSpec{
		CronExpressions: []string{processEnachPresentationResponseWorkflowConfig.ScheduleCron},
		TimeZoneName:    datetime.IST_TIME_ZONE,
	}
	scheduleWorkflowForCurrentDateRequest := &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload{SettlementDate: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_RelativeDate_{
		RelativeDate: enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_CURRENT_DATE,
	}}
	newSchIdforProcessPresentationResponseCurrentDay := getSchId(schedulePrefix, processEnachPresentationResponseWorkflowConfig.CurrentVersion)
	_, err := temporalClient.ScheduleClient().Create(ctx, client.ScheduleOptions{
		ID:   newSchIdforProcessPresentationResponseCurrentDay,
		Spec: spec,
		Action: &client.ScheduleWorkflowAction{
			ID:        getWorkflowId(newSchIdforProcessPresentationResponseCurrentDay),
			Workflow:  rpNs.ProcessEnachPresentationResponse,
			TaskQueue: conf.Application.TaskQueue,
			Args:      []interface{}{scheduleWorkflowForCurrentDateRequest},
		},
		CatchupWindow:  1 * time.Hour,
		Overlap:        temporalEnumsPb.SCHEDULE_OVERLAP_POLICY_SKIP,
		PauseOnFailure: false,
	})
	if err != nil {
		logger.Error(ctx, "error while deleting the previous deleting the existing schedules of the process enach presentation response workflow for current day with id", zap.String("schId", newSchIdforProcessPresentationResponseCurrentDay))
		return errors.Wrap(err, "failed to create the schedule for process enach presentation response workflow for the current day")
	}
	logger.Info(ctx, "Created schedule for process enach presentation response workflow for current day with id", zap.String("schId", newSchIdforProcessPresentationResponseCurrentDay))

	schedulePrefix = processEnachPresentationResponseWorkflowName + "_currentDayMinusOne"
	// delete the existing schedules for present enach execution workflow.
	if lastVersion != 0 {
		oldSchIdforProcessPresentationResponseCurrentDayMinusOne := getSchId(schedulePrefix, lastVersion)
		err = deleteWorkflowSchedule(ctx, temporalClient, oldSchIdforProcessPresentationResponseCurrentDayMinusOne)
		if err != nil {
			logger.Error(ctx, "error while deleting the previous deleting the existing schedules of the process enach presentation response workflow for current day minus one with id", zap.String("schId", oldSchIdforProcessPresentationResponseCurrentDayMinusOne))
			return errors.Wrap(err, "failed to delete the existing workflow schedule for workflow: process enach presentation response")
		}
	}

	scheduleWorkflowForCurrentDateMinusOneRequest := &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload{SettlementDate: &enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_RelativeDate_{
		RelativeDate: enachWorkflowPb.ProcessEnachPresentationResponseWorkflowPayload_CURRENT_DATE_MINUS_ONE,
	}}
	newSchIdforProcessPresentationResponseCurrentDateMinusOne := getSchId(schedulePrefix, processEnachPresentationResponseWorkflowConfig.CurrentVersion)
	_, err = temporalClient.ScheduleClient().Create(ctx, client.ScheduleOptions{
		ID:   newSchIdforProcessPresentationResponseCurrentDateMinusOne,
		Spec: spec,
		Action: &client.ScheduleWorkflowAction{
			ID:        getWorkflowId(newSchIdforProcessPresentationResponseCurrentDateMinusOne),
			Workflow:  rpNs.ProcessEnachPresentationResponse,
			TaskQueue: conf.Application.TaskQueue,
			Args:      []interface{}{scheduleWorkflowForCurrentDateMinusOneRequest},
		},
		CatchupWindow:  1 * time.Hour,
		Overlap:        temporalEnumsPb.SCHEDULE_OVERLAP_POLICY_SKIP,
		PauseOnFailure: false,
	})
	if err != nil {
		logger.Error(ctx, "error while deleting the previous deleting the existing schedules of the process enach presentation response workflow for current day minus one with id", zap.String("schId", newSchIdforProcessPresentationResponseCurrentDateMinusOne))
		return errors.Wrap(err, "failed to create the schedule for process enach presentation response workflow for the T minus one day")
	}

	logger.Info(ctx, "Created schedule for process enach presentation response workflow for the T minus one day with id", zap.String("schId", newSchIdforProcessPresentationResponseCurrentDateMinusOne))

	return nil
}

// nolint: dupl
func scheduleProcessEnachPresentationAckFile(ctx context.Context, temporalClient client.Client, conf *config.Config) error {

	processEnachPresentationAckFileConfig := conf.WorkflowNameToConfigMap[processEnachPresentationAckFileWorkflowName]
	// delete existing schedules for present enach execution workflow.
	lastVersion := processEnachPresentationAckFileConfig.CurrentVersion - 1
	if lastVersion != 0 {
		oldSchId := getSchId(processEnachPresentationAckFileWorkflowName, lastVersion)
		err := deleteWorkflowSchedule(ctx, temporalClient, oldSchId)
		if err != nil {
			logger.Error(ctx, "error while deleting the previous deleting the existing schedules of the present enach execution workflow with id", zap.String("schId", oldSchId))
			return errors.Wrap(err, "failed to delete the existing workflow schedule for workflow: present enach execution")
		}
	}

	newSchId := getSchId(processEnachPresentationAckFileWorkflowName, processEnachPresentationAckFileConfig.CurrentVersion)
	workflowId := getWorkflowId(newSchId)
	spec := client.ScheduleSpec{
		CronExpressions: []string{processEnachPresentationAckFileConfig.ScheduleCron},
		TimeZoneName:    datetime.IST_TIME_ZONE,
	}
	request := &enachWorkflowPb.ProcessEnachPresentationAckFileWorkflowPayload{}

	_, err := temporalClient.ScheduleClient().Create(ctx, client.ScheduleOptions{
		ID:   newSchId,
		Spec: spec,
		Action: &client.ScheduleWorkflowAction{
			ID:        workflowId,
			Workflow:  rpNs.ProcessEnachPresentationAckFile,
			TaskQueue: conf.Application.TaskQueue,
			Args:      []interface{}{request},
		},
		CatchupWindow:  1 * time.Hour,
		Overlap:        temporalEnumsPb.SCHEDULE_OVERLAP_POLICY_SKIP,
		PauseOnFailure: false,
	})
	if err != nil {
		logger.Error(ctx, "failed to create the schedule for process enach presentation ack file workflow with id", zap.String("schId", newSchId))
		return errors.Wrap(err, "failed to create the schedule for process enach presentation ack file workflow")
	}
	logger.Info(ctx, "Created schedule for process enach presentation ack file workflow with id %s ", zap.String("schId", newSchId))

	return nil
}

func deleteWorkflowSchedule(ctx context.Context, temporalClient client.Client, schId string) error {
	scheduleHandle := temporalClient.ScheduleClient().GetHandle(ctx, schId)
	err := scheduleHandle.Pause(ctx, client.SchedulePauseOptions{
		Note: "Paused for termination",
	})
	if err != nil {
		return errors.Wrap(err, "Error while pausing scheduled workflow")
	}

	err = scheduleHandle.Delete(ctx)
	if err != nil {
		return errors.Wrap(err, "Error while deleting scheduled workflow")
	}

	logger.Info(ctx, "Cancelled schedule %s", zap.String("schId", schId))

	return nil
}

func getWorkflowId(scheduleId string) string {
	return "wf:" + scheduleId
}

func getSchId(schedulePrefix string, version int) string {
	return fmt.Sprintf("%s_V%d", schedulePrefix, version)
}
