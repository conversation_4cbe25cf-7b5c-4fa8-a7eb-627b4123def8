// nolint
package main

import (
	"context"
	"errors"
	"fmt"

	types "github.com/epifi/gamma/api/typesv2"

	"github.com/google/go-cmp/cmp"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/kyc/docs"
	"github.com/epifi/gamma/api/omegle"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

type EvaluateKYCMismatch struct {
	docClient    docs.DocExtractionClient
	omegleClient omegle.OmegleClient
	onbClient    onbPb.OnboardingClient
}

func (s *EvaluateKYCMismatch) DoJob(ctx context.Context, req *JobRequest) error {
	actorId := "AC3cgKkd9WmY240728"
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	onbDetailsRes, err := s.onbClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId:    actorId,
		CachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(onbDetailsRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting details", zap.Error(rpcErr))
		return rpcErr
	}

	applicationDetailsRes, err := s.omegleClient.GetApplicantDetails(ctx, &omegle.GetApplicantDetailsRequest{
		ApplicationId: onbDetailsRes.GetDetails().GetStageMetadata().GetVkycMetadata().GetInhouseVKYC().GetApplicationId(),
	})
	if rpcErr := epifigrpc.RPCError(applicationDetailsRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting application details", zap.Error(rpcErr))
		return rpcErr
	}

	// passport fetch
	passportRes, err := s.onbClient.FetchPassport(ctx, &onbPb.FetchPassportRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(passportRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting passport", zap.Error(rpcErr))
		return rpcErr
	}
	passportDataFromVKYC, err := getPassportData(applicationDetailsRes.GetDocumentDetails())
	if err != nil {
		logger.Error(ctx, "error in getting passport data from VKYC", zap.Error(err))
		return err
	}
	diffAndPrint(passportDataFromVKYC, passportRes.GetPassport())

	// country ID fetch
	extDoc, getErr := s.docClient.GetExtractedData(ctx, &docs.GetExtractedDataRequest{
		ActorId:         actorId,
		ClientRequestId: onbDetailsRes.GetDetails().GetStageMetadata().GetCountryIdVerificationMetadata().GetDocExtractClientReqId(),
	})
	if rpcErr := epifigrpc.RPCError(extDoc, getErr); rpcErr != nil {
		logger.Error(ctx, "error in getting country ID extracted data", zap.Error(getErr))
		return rpcErr
	}
	// compare country ID fetch
	countryIdDocumentDetails := getCountryIdDetails(extDoc.GetExtractedDocument().GetExtractedData().GetExtractedDetails().GetUqudoEmiratesIdExtractedDetails())
	countryIdDetailsFromVKYC, getErr := getEmiratesIDDetails(applicationDetailsRes.GetDocumentDetails())
	if err != nil {
		logger.Error(ctx, "error in getting country ID details from VKYC", zap.Error(err))
		return err
	}
	diffAndPrint(countryIdDetailsFromVKYC, countryIdDocumentDetails)
	return nil
}

func diffAndPrint(v1, v2 proto.Message) {
	r := &DiffReporter{}
	opts := []cmp.Option{
		protocmp.Transform(),
		cmp.Reporter(r),
	}
	if diff := cmp.Diff(v1, v2, opts...); diff != "" {
		fmt.Printf("print diff from report \n %v \n", diff)
	}
}

// VKYC helper method
func getEmiratesIDDetails(documentDetails []*types.DocumentDetails) (*types.CountryIdDetails, error) {
	for _, documentDetail := range documentDetails {
		if documentDetail.GetDocumentType() == types.DocumentType_DOCUMENT_TYPE_COUNTRY_ID || documentDetail.GetDocumentType() == types.DocumentType_DOCUMENT_TYPE_EMIRATES_ID {
			return documentDetail.GetCountryIdDetails(), nil
		}
	}
	return nil, errors.New("no country ID details found in the fetched documents")
}

// VKYC helper method
func getPassportData(documentDetails []*types.DocumentDetails) (*types.PassportData, error) {
	for _, documentDetail := range documentDetails {
		if documentDetail.GetDocumentType() == types.DocumentType_DOCUMENT_TYPE_PASSPORT {
			return documentDetail.GetPassportDetails(), nil
		}
	}
	return nil, errors.New("no country ID details found in the fetched documents")
}

func getCountryIdDetails(details *docs.UqudoEmiratesIdExtractedDetails) *types.CountryIdDetails {
	return &types.CountryIdDetails{
		Id:                   details.GetIdentityNumber(),
		IssueDate:            details.GetIssueDate(),
		DateOfExpiry:         details.GetDateOfExpiry(),
		Name:                 details.GetName(),
		MotherName:           details.GetMotherName(),
		DateOfBirth:          details.GetDob(),
		Gender:               details.GetGender(),
		MaritalStatus:        details.GetMaritalStatus(),
		CommunicationAddress: details.GetHomeAddress(),
	}
}
