package main

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"google.golang.org/protobuf/types/known/durationpb"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/google/uuid"

	schedulerpb "github.com/epifi/gamma/api/fittt/scheduler"
	vendorspb "github.com/epifi/gamma/api/vendors/roanuz/football"
)

// nolint: unused
func initFBSchedulesFile(prefix string) (*os.File, *bufio.Writer) {
	workDir, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	filename := prefix + "_schedules_jobs.sql"
	f, err := os.Create(filepath.Join(workDir, "db", "fittt", "fixtures", filename))
	if err != nil {
		logger.Fatal("failed to init fixture file", zap.Error(err))
	}
	w := bufio.NewWriter(f)

	return f, w
}

// nolint
func generateFBMatchSchedules(tournamentId string, filterRoundId string, tournamentTag string, matchesAfter time.Time) {
	b, err := fbTournament(tournamentId)
	if err != nil {
		logger.Fatal("failed to get tournament", zap.Error(err))
	}
	apiResp := &vendorspb.TournamentResponse{}
	unmarshaller := protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}
	err = unmarshaller.Unmarshal(b, apiResp)
	if err != nil {
		logger.Fatal("failed to unmarshall tournament response", zap.Error(err))
	}
	indiaLoc, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		logger.Fatal("failed to load location", zap.Error(err))
	}
	tournamentName := apiResp.Data.Tournament.Name
	filename := apiResp.Data.Tournament.ShortName
	if filterRoundId != "" {
		filename = filename + "_" + filterRoundId
	}
	f, w := initFBSchedulesFile(strings.ReplaceAll(filename, " ", "_"))
	defer logAndClose(f)
	defer logAndFlush(w)
	schedulesBuilder := &strings.Builder{}
	jobsBuilder := &strings.Builder{}
	var firstEntry = true
	teamsMap := apiResp.Data.Tournament.Teams
	for _, round := range apiResp.Data.Tournament.Rounds {
		if filterRoundId != "" && round.Key != filterRoundId {
			// skip other rounds if filterRoundId is set
			continue
		}
		b, err2 := fbRounds(tournamentId, round.Key)
		if err2 != nil {
			logger.Fatal("failed to get round", zap.Error(err2))
		}
		roundResp := &vendorspb.RoundResponse{}
		err = unmarshaller.Unmarshal(b, roundResp)
		if err != nil {
			logger.Fatal("failed to unmarshall round response", zap.Error(err))
		}

		for _, m := range roundResp.Data.Round.Matches {
			m := m.Match
			startDate := time.Unix(int64(m.StartDate.Timestamp), 0)
			if startDate.Before(matchesAfter) {
				continue
			}
			var teams []*schedulerpb.SportTeam
			h := teamsMap[m.HomeTeamId]
			teams = append(teams, &schedulerpb.SportTeam{
				Name: h.Name,
				Code: h.Code,
			})
			a := teamsMap[m.AwayTeamId]
			teams = append(teams, &schedulerpb.SportTeam{
				Name: a.Name,
				Code: a.Code,
			})

			// add a maximum time to match start so that the execution completes successfully.
			// even-though default match duration is 90 mins, adding 3 hours to include extra time, injury timeout and other
			// complex provisions in match duration.
			expectedEndTime := startDate.Add(3 * time.Hour)
			expectedEndTime = expectedEndTime.In(indiaLoc)
			// addfunds for non-kyc verified customers will fail from 12 am to 3am which may extend till 7am.
			// So setting a safe window within which match execution can be triggered.
			possibleSlotStart := time.Date(expectedEndTime.Year(), expectedEndTime.Month(), expectedEndTime.Day(), 7, 0, 0, 0, indiaLoc)
			possibleSlotEnd := time.Date(expectedEndTime.Year(), expectedEndTime.Month(), expectedEndTime.Day(), 23, 0, 0, 0, indiaLoc)
			fmt.Println(time.Unix(int64(m.StartDate.Timestamp), 0).In(indiaLoc).Format(time.RFC3339), expectedEndTime.Format(time.RFC3339), expectedEndTime.UTC().Format(time.RFC3339))
			if expectedEndTime.Before(possibleSlotStart) {
				expectedEndTime = possibleSlotStart
			} else if expectedEndTime.After(possibleSlotEnd) {
				// move to next day possible start
				expectedEndTime = possibleSlotStart.Add(24 * time.Hour)
			}
			if firstEntry {
				firstEntry = false
			} else {
				_, err = schedulesBuilder.WriteString(",\n")
				if err != nil {
					logger.Fatal("failed to write string", zap.Error(err))
				}
				_, err = jobsBuilder.WriteString(",\n")
				if err != nil {
					logger.Fatal("failed to write string", zap.Error(err))
				}
			}
			jobData := &schedulerpb.JobData{
				Data: &schedulerpb.JobData_FootballMatch{
					FootballMatch: &schedulerpb.FootballMatch{
						MatchId: m.Key,
						MatchDetails: &schedulerpb.SportsMatchDetails{
							OfficialStartTime: timestampPb.New(startDate),
							TournamentTag:     tournamentTag,
							Teams:             teams,
							// deposits for all rules will be aggregated and sent as single PN.
							// Setting 30 mins to accommodate retries and payment delays
							AggrNotifTriggerDelay: durationpb.New(time.Minute * 30),
							MatchId:               m.Key,
						},
					},
				}}
			jobDatab, err := protojson.Marshal(jobData)
			if err != nil {
				logger.Fatal("failed to marshall job data", zap.Error(err))
			}
			scheduleId := uuid.New().String()
			_, err = schedulesBuilder.WriteString(fmt.Sprintf(
				fbSchedulesRowInsertTemplate,
				scheduleId,
				string(jobDatab),
				expectedEndTime.UTC().Format(time.RFC3339),
				tournamentName+" - "+m.Name,
			))
			if err != nil {
				logger.Fatal("failed to write string", zap.Error(err))
			}
			_, err = jobsBuilder.WriteString(fmt.Sprintf(
				jobsRowInsertTemplate,
				uuid.New().String(),
				scheduleId,
				expectedEndTime.UTC().Format(time.RFC3339),
			))
			if err != nil {
				logger.Fatal("failed to write string", zap.Error(err))
			}
		}
	}
	_, err = w.WriteString(fbSchedulesTemplate)
	if err != nil {
		logger.Fatal("failed to write string", zap.Error(err))
	}
	_, err = w.WriteString(schedulesBuilder.String() + ";")
	if err != nil {
		logger.Fatal("failed to write string", zap.Error(err))
	}
	_, err = w.WriteString(jobsTemplate)
	if err != nil {
		logger.Fatal("failed to write string", zap.Error(err))
	}
	_, err = w.WriteString(jobsBuilder.String() + ";\n")
	if err != nil {
		logger.Fatal("failed to write string", zap.Error(err))
	}

}

// nolint: unused
var fbSchedulesTemplate = `
INSERT INTO SCHEDULES(ID, SCHEDULE_TYPE, STATUS, JOB_DATA, RECURRENCE, DESCRIPTION, RECURRENCE_LIMIT, STOP_RECURRENCE_ON_FAILURE) VALUES
`

// nolint: unused
var fbSchedulesRowInsertTemplate = `( '%s', 'FOOTBALL_MATCH', 'RUNNING', '%s', '{"once":"%s"}', '%s', 1, FALSE)`

// nolint: unused
var jobsTemplate = `
INSERT INTO JOBS (ID, SCHEDULE_ID, STATUS, SCHEDULED_AT, RECURRENCE_COUNT) VALUES
`

// nolint: unused
var jobsRowInsertTemplate = `('%s', '%s', 'SCHEDULED', ('%s')::timestamptz, 1)`
