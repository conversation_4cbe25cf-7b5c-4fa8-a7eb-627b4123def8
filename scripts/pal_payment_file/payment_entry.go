package main

import (
	paymentPb "github.com/epifi/gamma/api/order/payment"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
)

type PaymentEntry struct {
	loanPaymentRequest *palPb.LoanPaymentRequest
	loanApplicant      *palPb.LoanApplicant
	loanAccount        *palPb.LoanAccount
	transaction        *paymentPb.Transaction
}

func (e *PaymentEntry) GetLoanPaymentRequest() *palPb.LoanPaymentRequest {
	if e != nil {
		return e.loanPaymentRequest
	}
	return nil
}

func (e *PaymentEntry) GetLoanApplicant() *palPb.LoanApplicant {
	if e != nil {
		return e.loanApplicant
	}
	return nil
}

func (e *PaymentEntry) GetLoanAccount() *palPb.LoanAccount {
	if e != nil {
		return e.loanAccount
	}
	return nil
}

func (e *PaymentEntry) GetTransaction() *paymentPb.Transaction {
	if e != nil {
		return e.transaction
	}
	return nil
}
