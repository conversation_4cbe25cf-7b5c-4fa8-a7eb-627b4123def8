Application:
  Environment: "demo"

Aws:
  Region: "ap-south-1"

EpifiDb:
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "demo/cockroach/ca.crt"
  SSLClientCert: "demo/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "demo/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"

S3Conf:
  Bucket: "epifi-demo-wealth-onboarding"
  LivenessPath: "liveness"
  DownloadedKraDocS3Path: "downloaded_kra_doc"
