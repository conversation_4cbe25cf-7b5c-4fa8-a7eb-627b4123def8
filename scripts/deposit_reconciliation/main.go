package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"flag"
	"strings"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace/deposit"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	workflow2 "github.com/epifi/gamma/api/deposit/workflow"
	"github.com/epifi/gamma/scripts/deposit_reconciliation/config"
)

var actorIDsFlag = flag.String("actorIDs", "", "comma separated actorIDs")

func main() {
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load deposit script config", zap.Error(err))
	}

	// Connect to deposit db present in config
	dbConn, err := storageV2.NewCRDBWithConfig(conf.EpifiDb, true)
	if err != nil {
		panic(err)
	}
	sqlDb, err := dbConn.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() {
		_ = sqlDb.Close()
	}()

	storageV2.InitDefaultCRDBTransactionExecutor(dbConn)

	celestialConn := epifigrpc.NewConnByService(cfg.CELESTIAL_SERVICE)
	celestialClient := celestialPb.NewCelestialClient(celestialConn)

	if actorIDsFlag == nil || *actorIDsFlag == "" {
		processDepositRecon(context.Background(), celestialClient, dbConn)
		if err != nil {
			panic(err)
		}
	} else {
		triggerReconForActors(celestialClient)
	}
}

func triggerReconForActors(celestialClient celestialPb.CelestialClient) {

	actorIDs := strings.Split(*actorIDsFlag, ",")
	var trimmedActorIDs []string
	for _, actorID := range actorIDs {
		actorIDString := strings.TrimSpace(actorID)
		if len(actorIDString) > 0 {
			trimmedActorIDs = append(trimmedActorIDs, actorIDString)
		}
	}

	for _, trimmedActorID := range trimmedActorIDs {
		err := triggerDepositReconciliationWorkflow(trimmedActorID, nil, celestialClient)
		if err != nil {
			logger.ErrorNoCtx("error in triggerDepositReconciliationWorkflow", zap.String(logger.ACTOR_ID_V2, trimmedActorID), zap.Error(err))
			panic(err)
		}
	}
}

func triggerDepositReconciliationWorkflow(actorID string, depositIDs []string, celestialClient celestialPb.CelestialClient) error {
	ctx := context.Background()

	workflowReq := &workflow2.DepositReconciliationRequest{ActorId: actorID, DepositAccountIds: depositIDs}

	payload, err := protojson.Marshal(workflowReq)
	if err != nil {
		return err
	}

	clientRequestID := uuid.NewString()

	res, resErr := celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: actorID,
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(deposit.DepositReconciliationWorkflow),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     clientRequestID,
				Client: workflowPb.Client_DEPOSIT,
			},
			Ownership:        commontypes.Ownership_EPIFI_TECH,
			Payload:          payload,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		}})
	if grpcErr := epifigrpc.RPCError(res, resErr); grpcErr != nil {
		logger.Error(ctx, "error in celestialClient.InitiateWorkflow", zap.Error(grpcErr), zap.String(logger.ACTOR_ID_V2, actorID))
		return grpcErr
	}

	logger.Info(ctx, "workflow initiated for actor", zap.String(logger.ACTOR_ID_V2, actorID),
		zap.String(logger.WORKFLOW_REQ_ID, res.GetParams().GetWorkflowRequestId()), zap.String(logger.CLIENT_REQUEST_ID, clientRequestID))
	return nil
}
