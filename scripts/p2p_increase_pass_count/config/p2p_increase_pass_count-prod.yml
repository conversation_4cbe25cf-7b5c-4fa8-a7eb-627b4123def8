Application:
  Environment: "prod"
  Name: "p2p-mark-investment-approved-to-success"

EpifiDb:
  AppName: "p2pinvestment"
  StatementTimeout: 10s
  Username: "p2pinvestment_liquiloans_dev_user"
  Password: ""
  Name: "p2pinvestment_liquiloans"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.p2pinvestment_liquiloans_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.p2pinvestment_liquiloans_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"
