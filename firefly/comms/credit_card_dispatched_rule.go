// nolint
package comms

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commsPb "github.com/epifi/gamma/api/comms"
	ffCommsPb "github.com/epifi/gamma/api/firefly/comms"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	userPb "github.com/epifi/gamma/api/user"
	ffHelper "github.com/epifi/gamma/firefly/helper"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/be-common/pkg/logger"
)

type CreditCardDispatchedRule struct {
	commsDataHelper *ffHelper.CommsDataHelper
}

func NewCreditCardDispatchedRule(commsDataHelper *ffHelper.CommsDataHelper) *CreditCardDispatchedRule {
	return &CreditCardDispatchedRule{
		commsDataHelper: commsDataHelper,
	}
}

func (s *CreditCardDispatchedRule) GetComms(ctx context.Context, data IActionData) (res []commsPb.CommMessage, resErr error) {
	cardTrackingActionData, ok := data.(*ffCommsPb.ActionData_CardTrackingActionData)
	if !ok {
		return
	}

	if cardTrackingActionData.CardTrackingActionData.GetCarrier() != "" && cardTrackingActionData.GetActorId() != "" && cardTrackingActionData.CardTrackingActionData.GetAwb() != "" &&
		cardTrackingActionData.CardTrackingActionData.GetDeliveryStatus() == ffEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_DISPATCHED {

		userDetails, err := s.commsDataHelper.GetUserDetails(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{ActorId: cardTrackingActionData.GetActorId()},
		})
		if err != nil {
			logger.Error(ctx, "error while fetching user details", zap.Error(err))
			return nil, err
		}

		trackingUrl, err := s.commsDataHelper.GetTrackingUrlByCarrier(cardTrackingActionData.CardTrackingActionData.GetCarrier())
		if err != nil {
			return nil, fmt.Errorf("error getting tracking url for carrier: %s, error: %w", cardTrackingActionData.CardTrackingActionData.GetCarrier(), err)
		}
		// sms comms
		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_CreditCardDisptachedWithTrackingNumberSmsOption{
						CreditCardDisptachedWithTrackingNumberSmsOption: &commsPb.CreditCardDisptachedWithTrackingNumberSmsOption{
							SmsType: commsPb.SmsType_CREDIT_CARD_DISPTACHED_WITH_TRACKING_NUMBER,
							Option: &commsPb.CreditCardDisptachedWithTrackingNumberSmsOption_CreditCardDisptachedWithTrackingNumberSmsOptionV1{
								CreditCardDisptachedWithTrackingNumberSmsOptionV1: &commsPb.CreditCardDisptachedWithTrackingNumberSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									FirstName:       userDetails.GetUser().GetProfile().GetKycName(),
									CreditCardType:  ffPkg.CreditCardType,
									TrackingName:    fmt.Sprintf("%s AWB %s", cardTrackingActionData.CardTrackingActionData.GetCarrier(), cardTrackingActionData.CardTrackingActionData.GetAwb()),
								},
							},
						},
					},
				},
			},
			// whatsapp comm
		}, &commsPb.SendMessageRequest_Whatsapp{
			Whatsapp: &commsPb.WhatsappMessage{
				WhatsappOption: &commsPb.WhatsappOption{
					Option: &commsPb.WhatsappOption_CardDispatchedWhatsappOption{
						CardDispatchedWhatsappOption: &commsPb.CardDispatchedWhatsappOption{
							WhatsappType: commsPb.WhatsappType_WHATSAPP_TYPE_CARD_DISPATCHED,
							Option: &commsPb.CardDispatchedWhatsappOption_CardDispatchedWhatsappOptionV1{
								CardDispatchedWhatsappOptionV1: &commsPb.CardDispatchedWhatsappOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									CardHolderName:  userDetails.GetUser().GetProfile().GetKycName(),
									CardName_1:      "Credit",
									LastFourDigit:   cardTrackingActionData.CardTrackingActionData.GetCardLastFourDigit(),
									CardName_2:      "Credit",
									ShippedDate:     timestampPb.Now(),
									TrackingLink:    fmt.Sprintf("%s and Enter AWB: %s", trackingUrl, cardTrackingActionData.CardTrackingActionData.GetAwb()),
								},
							},
						},
					},
				},
			},
		})
	}
	return
}

func (s *CreditCardDispatchedRule) GetDoOnceTaskId(_ context.Context, data IActionData, medium commsPb.Medium) string {
	cardTrackingActionData, ok := data.(*ffCommsPb.ActionData_CardTrackingActionData)
	if !ok {
		return ""
	}
	return cardTrackingActionData.CardTrackingActionData.GetCardId() + "_" + "CARD_DISPATCHED" + "_" + medium.String()
}
