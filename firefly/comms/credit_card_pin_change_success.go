// nolint
package comms

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	commsPb "github.com/epifi/gamma/api/comms"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffCommsPb "github.com/epifi/gamma/api/firefly/comms"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	userPb "github.com/epifi/gamma/api/user"
	ffHelper "github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	pinChangeHeading          = "Pin successfully changed"
	pinChangeSuccessEventType = "Card PIN Changed for"
)

type CreditCardPinChangeSuccessRule struct {
	commsDataHelper *ffHelper.CommsDataHelper
}

func NewCreditCardPinChangeSuccessRule(commsDataHelper *ffHelper.CommsDataHelper) *CreditCardPinChangeSuccessRule {
	return &CreditCardPinChangeSuccessRule{
		commsDataHelper: commsDataHelper,
	}
}

func (s *CreditCardPinChangeSuccessRule) GetComms(ctx context.Context, data IActionData) (res []commsPb.CommMessage, resErr error) {
	cardRequestActionData, ok := data.(*ffCommsPb.ActionData_CardRequestActionData)
	if !ok {
		return
	}
	if cardRequestActionData.CardRequestActionData.GetCardRequest().GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PIN_RESET &&
		cardRequestActionData.CardRequestActionData.GetCardRequest().GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS {

		userDetails, err := s.commsDataHelper.GetUserDetails(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{ActorId: cardRequestActionData.GetActorId()},
		})
		if err != nil {
			logger.Error(ctx, "error while fetching user details", zap.Error(err))
			return nil, err
		}

		getCcReq := &ffPb.GetCreditCardRequest{
			GetBy:            &ffPb.GetCreditCardRequest_ActorId{ActorId: cardRequestActionData.GetActorId()},
			SelectFieldMasks: []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO},
		}

		if cardRequestActionData.CardRequestActionData.GetCardRequest().GetCardId() != "" {
			getCcReq.GetBy = &ffPb.GetCreditCardRequest_CreditCardId{CreditCardId: cardRequestActionData.CardRequestActionData.GetCardRequest().GetCardId()}
		}
		creditCardDetails, err := s.commsDataHelper.GetCreditCardDetails(ctx, getCcReq)
		if err != nil {
			logger.Error(ctx, "error while fetching credit card details", zap.Error(err))
			return nil, err
		}
		ccLastFourDigits, err := ffHelper.GetCreditCardLastKDigits(creditCardDetails.GetCreditCard().GetBasicInfo().GetMaskedCardNumber(), 4)
		if err != nil {
			logger.Error(ctx, "error while fetching credit card last four digits", zap.Error(err))
			return nil, err
		}

		// sms comms
		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_CreditCardPinChangeSuccessSmsOption{
						CreditCardPinChangeSuccessSmsOption: &commsPb.CreditCardPinChangeSuccessSmsOption{
							SmsType: commsPb.SmsType_CREDIT_CARD_PIN_CHANGE_SUCCESS,
							Option: &commsPb.CreditCardPinChangeSuccessSmsOption_CreditCardPinChangeSuccessSmsOptionV1{
								CreditCardPinChangeSuccessSmsOptionV1: &commsPb.CreditCardPinChangeSuccessSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									FirstName:       userDetails.GetUser().GetProfile().GetKycName(),
									HelplineNumber:  helplineNumber,
								},
							},
						},
					},
				},
			},
		})

		res = append(res, getCardPinChangeSuccessEmail(ccLastFourDigits))
	}
	return
}

func getCardPinChangeSuccessEmail(ccLastFourDigits string) commsPb.CommMessage {
	commsEmailOption := &commsPb.CreditCardControlsEmailOption_CreditCardControlsEmailOptionV2{
		CreditCardControlsEmailOptionV2: &commsPb.CreditCardControlsEmailOptionV2{
			TemplateVersion:      commsPb.TemplateVersion_VERSION_V2,
			CardControlEventType: pinChangeSuccessEventType,
			LastFourDigits:       ccLastFourDigits,
			IconUrl:              greenTickIconUrl,
			Heading:              pinChangeHeading,
			Description:          fmt.Sprintf("The PIN for your Credit Card ending with xx%s has successfully changed! If not done by you, please contact Fi Care @ %s .", ccLastFourDigits, helplineNumber),
		},
	}
	return ffHelper.GetCardControlsEmailMessage(commsEmailOption)
}
