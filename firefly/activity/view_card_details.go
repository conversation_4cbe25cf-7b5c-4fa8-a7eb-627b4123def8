package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	ffActPb "github.com/epifi/gamma/api/firefly/activity"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

// ViewCardDetails activity updates the next action to the clear card details screen
func (p *Processor) ViewCardDetails(ctx context.Context, req *ffActPb.ViewCardDetailsRequest) (*ffActPb.ViewCardDetailsResponse, error) {
	res := &ffActPb.ViewCardDetailsResponse{}
	lg := activity.GetLogger(ctx)

	/*****  		Fetching the db entries for our card request stage and card request status/sub-status updates			*****/
	cardRequest, err := p.cardRequestDao.GetByOrchestrationId(ctx, req.GetRequestHeader().GetClientReqId(), []ccEnumsPb.CardRequestFieldMask{
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_CARD_ID,
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
	})

	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("no card request attached to client request id", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, epifierrors.ErrPermanent
	case err != nil:
		lg.Error("failed to read data from DB", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	// append actor-id to ctx
	ctx = epificontext.CtxWithActorId(ctx, cardRequest.GetActorId())
	if cardRequest.GetStatus() == ccEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED {
		return nil, errors.Wrap(epifierrors.ErrPermanent, "card request already failed")
	}
	cardDetailsDl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CREDIT_CARD_DETAILS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_CreditCardDetailsScreenOptions{
			CreditCardDetailsScreenOptions: &deeplinkPb.CreditCardDetailsScreenOptions{
				CardRequestId: cardRequest.GetId(),
			},
		},
	}
	if cardRequest.GetNextAction() == cardDetailsDl {
		return res, nil
	}
	cardRequest.NextAction = cardDetailsDl
	if updErr := p.cardRequestDao.Update(ctx, cardRequest, []ccEnumsPb.CardRequestFieldMask{
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
	}); updErr != nil {
		lg.Error("error in updating next action", zap.Error(updErr))
		return nil, errors.Wrap(epifierrors.ErrTransient, updErr.Error())
	}
	return res, nil
}
