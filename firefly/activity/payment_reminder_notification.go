// nolint: depguard,goimports
package activity

import (
	"context"
	"math"
	"strconv"
	"strings"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	gdate "google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/api/comms"
	ffpb "github.com/epifi/gamma/api/firefly"
	ffActivityPb "github.com/epifi/gamma/api/firefly/activity"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

var (
	notificationTypeToStringMap = map[ccEnumsPb.PaymentReminderNotificationType]string{
		ccEnumsPb.PaymentReminderNotificationType_PAYMENT_REMINDER_NOTIFICATION_TYPE_SOFT_DUE_DATE:                 "SOFT",
		ccEnumsPb.PaymentReminderNotificationType_PAYMENT_REMINDER_NOTIFICATION_TYPE_DEADLINE_PASSED:               "DEADLINE_OVER",
		ccEnumsPb.PaymentReminderNotificationType_PAYMENT_REMINDER_NOTIFICATION_TYPE_HARD_DUE_DATE:                 "HARD",
		ccEnumsPb.PaymentReminderNotificationType_PAYMENT_REMINDER_NOTIFICATION_TYPE_DEADLINE_PASSED_WITH_INTEREST: "DEADLINE_OVER_INTEREST",
		ccEnumsPb.PaymentReminderNotificationType_PAYMENT_REMINDER_NOTIFICATION_TYPE_BEFORE_DUE_DATE:               "BEFORE_DUE_DATE",
		ccEnumsPb.PaymentReminderNotificationType_PAYMENT_REMINDER_NOTIFICATION_TYPE_AFTER_DUE_DATE:                "AFTER_DUE_DATE",
		ccEnumsPb.PaymentReminderNotificationType_PAYMENT_REMINDER_NOTIFICATION_TYPE_ON_DUE_DATE:                   "ON_DUE_DATE",
	}
)

func (p *Processor) ProcessPaymentReminderNotification(ctx context.Context, req *ffActivityPb.PaymentReminderNotificationRequest) (*ffActivityPb.PaymentReminderNotificationResponse, error) {
	var (
		// nolint:ineffassign
		commsMessageId    = ""
		notificationError error
	)
	res := &ffActivityPb.PaymentReminderNotificationResponse{}
	lg := activity.GetLogger(ctx)

	if req.GetNotificationTypeAndTimestamp().GetMedium() != comms.Medium_NOTIFICATION {
		return res, nil
	}
	_, cardRequestStage, err := p.getCardReqAndCreateCardReqStage(ctx, req.GetRequestHeader().GetClientReqId(),
		ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PAYMENT_REMINDER_NOTIFICATION,
		ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED, ccEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED, "")
	if err != nil {
		lg.Error("error in getCardReqAndCreateCardReqStage", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(err, "error in getCardReqAndCreateCardReqStage")
	}

	finishActivity, err := p.checkIfStageAlreadyTerminated(cardRequestStage)
	switch {
	case err != nil:
		return nil, err
	case finishActivity:
		return res, nil
	default:
		// continue
	}

	dueDetails, dueFetchError := p.rpcHelper.GetDueFromVendor(ctx, req.GetEntityId())
	if dueFetchError != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(dueFetchError, "Error fetching dues").Error())
	}
	dueAmountGreaterThanZero, dueAmountCompareError := isDueGreaterThanZero(dueDetails)
	if dueAmountCompareError != nil {
		lg.Error("Error in fetching and comparing due amount:", zap.Error(dueFetchError))
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(dueAmountCompareError, "Error in comparing due").Error())
	}
	if dueAmountGreaterThanZero {
		commsMessageId, notificationError = p.SendPaymentReminderNotification(ctx, req, dueDetails.GetDueDate(), dueDetails.GetTotalDueAmount())
		if notificationError != nil {
			lg.Error("Error while sending notification to the user", zap.Error(notificationError))
			return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(notificationError, "Error sending notification").Error())
		}
		res.CardRequestStageExecutionDetails = appendCommsMessageIdToStageExecutionDetails(cardRequestStage.GetStageExecutionDetails(), commsMessageId)
		res.CardRequestStageId = cardRequestStage.GetId()
	} else {
		lg.Info("Due amount is zero, not sending notification")
		return res, nil
	}
	return res, nil
}

func appendCommsMessageIdToStageExecutionDetails(stageExecutionDetail *ffpb.CardRequestStageExecutionDetails, commsMessageId string) *ffpb.CardRequestStageExecutionDetails {
	switch {
	case stageExecutionDetail.GetPaymentReminderNotificationDetails() != nil:
		stageExecutionDetail.GetPaymentReminderNotificationDetails().CommsMessageIds = append(stageExecutionDetail.GetPaymentReminderNotificationDetails().GetCommsMessageIds(), commsMessageId)
		return stageExecutionDetail
	default:
		return &ffpb.CardRequestStageExecutionDetails{
			Data: &ffpb.CardRequestStageExecutionDetails_PaymentReminderNotificationDetails{
				PaymentReminderNotificationDetails: &ffpb.PaymentReminderNotificationDetails{CommsMessageIds: []string{commsMessageId}},
			},
		}
	}
}

func isDueGreaterThanZero(dueDetails *creditcard.FetchDueAmountResponse) (bool, error) {
	dueAmountGreaterThanZero, err := compareDueWithCurrency(dueDetails.GetTotalDueAmount())
	return dueAmountGreaterThanZero, err
}

func compareDueWithCurrency(dueAmount *money.Money) (bool, error) {
	zeroAmountMoney := moneyPkg.ZeroINR().GetPb()
	if dueAmount.GetCurrencyCode() != zeroAmountMoney.GetCurrencyCode() {
		return false, errors.Wrap(epifierrors.ErrTransient, "Currency code of due and zero amount is different")
	}
	return moneyPkg.Compare(dueAmount, zeroAmountMoney) == 1, nil
}

func (p *Processor) SendPaymentReminderNotification(ctx context.Context, payload *ffActivityPb.PaymentReminderNotificationRequest, dueDate *types.Date, dueAmount *money.Money) (string, error) {
	var (
		commsMessageId    = ""
		notificationError error
	)
	var creditAccountDetails, creditAccountFetchError = p.rpcHelper.GetCreditAccountDetails(ctx, payload.GetEntityId())
	if creditAccountFetchError != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, errors.Wrap(creditAccountFetchError, "Error fetching credit account").Error())
	}
	doOnceTaskId := getDoOnceTaskId(payload, creditAccountDetails.GetActorId())
	daysRemainingBeforeDueDate := getDaysRemainingBeforeDueDate(dueDate, payload.GetNotificationTypeAndTimestamp().GetTimestamp())
	userDetails, err := p.rpcHelper.GetUserDetailsFromActorId(ctx, creditAccountDetails.GetActorId())
	if err != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Error fetching user details from actor id").Error())
	}
	creditCardDetails, err := p.getValidCreditCardDetailByAccountId(ctx, creditAccountDetails.GetId(), []ccEnumsPb.CreditCardFieldMask{
		ccEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACTOR_ID,
		ccEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE,
		ccEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
	})
	if err != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "Unable to fetch credit card").Error())
	}
	notificationTemplate, notificationTemplateError := p.notificationHelper.GetPaymentReminderNotificationRequest(ctx,
		userDetails.GetUser(), payload.GetNotificationTypeAndTimestamp().GetNotificationType(), daysRemainingBeforeDueDate,
		dueAmount, creditCardDetails.GetBasicInfo().GetMaskedCardNumber(), dueDate)
	if notificationTemplateError != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, errors.Wrap(notificationTemplateError, "Error creating notification template").Error())
	}

	sendNotificationFunc := func() error {
		commsMessageId, notificationError = p.notificationHelper.SendMessage(ctx, notificationTemplate)
		if notificationError != nil {
			return notificationError
		}
		return nil
	}
	doOnceTaskError := p.doOnce.DoOnceFn(ctx, doOnceTaskId, sendNotificationFunc)
	if doOnceTaskError != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, doOnceTaskError.Error())
	}
	return commsMessageId, nil
}

func getDaysRemainingBeforeDueDate(date *types.Date, notificationTimeStamp *timestamp.Timestamp) int {
	dueDate := &gdate.Date{
		Year:  date.GetYear(),
		Month: date.GetMonth(),
		Day:   date.GetDay(),
	}
	endOfDueDate := datetime.EndOfDay(datetime.DateToTimeV2(dueDate, datetime.IST))
	diff := endOfDueDate.Sub(notificationTimeStamp.AsTime())
	var diffDays int
	if diff < 0 {
		diffDays = int(math.Ceil(diff.Hours() / 24))
	} else {
		diffDays = int(math.Floor(diff.Hours() / 24))
	}
	return diffDays
}

func getDoOnceTaskId(payload *ffActivityPb.PaymentReminderNotificationRequest, actorId string) string {
	timeStampString := strconv.Itoa(int(payload.GetNotificationTypeAndTimestamp().GetTimestamp().GetSeconds()))
	return strings.Join([]string{actorId, timeStampString, notificationTypeToStringMap[payload.GetNotificationTypeAndTimestamp().GetNotificationType()]}, "_")
}
