package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	ccFireflyBillingPb "github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/be-common/pkg/money"
)

type CreditCardBill struct {
	Id                     string `gorm:"primary_key"`
	ActorId                string
	AccountId              string
	LastStatementBalance   *money.Money
	CurrentStatementAmount *money.Money
	TotalCredit            *money.Money
	TotalDebit             *money.Money
	Cash                   *money.Money
	Purchase               *money.Money
	MinDue                 *money.Money
	TotalDue               *money.Money
	StatementDate          *time.Time
	SoftDueDate            *time.Time
	HardDueDate            *time.Time
	RewardsInfo            *ccFireflyBillingPb.RewardsInfo
	AnalyticsInfo          *ccFireflyBillingPb.AnalyticsInfo
	CreatedAt              *time.Time `gorm:"not null"`
	UpdatedAt              *time.Time `gorm:"not null"`
	DeletedAt              gorm.DeletedAt
	StatementSummary       *ccFireflyBillingPb.StatementSummary
	AvailableLimit         *money.Money
	S3Path                 string
	RewardId               string
}

// TableName implementation of Tabler interface to override the name of the table
func (*CreditCardBill) TableName() string {
	return "credit_card_bills"
}

func NewCreditCardBill(cardBill *ccFireflyBillingPb.CreditCardBill) *CreditCardBill {

	var statementDate, softDueDate, hardDueDate time.Time

	if cardBill.GetStatementDate() != nil {
		statementDate = cardBill.GetStatementDate().AsTime()
	}
	if cardBill.GetSoftDueDate() != nil {
		softDueDate = cardBill.GetSoftDueDate().AsTime()
	}
	if cardBill.GetHardDueDate() != nil {
		hardDueDate = cardBill.GetHardDueDate().AsTime()
	}

	return &CreditCardBill{
		Id:                     cardBill.GetId(),
		ActorId:                cardBill.GetActorId(),
		AccountId:              cardBill.GetAccountId(),
		LastStatementBalance:   money.NewMoney(cardBill.GetLastStatementBalance()),
		CurrentStatementAmount: money.NewMoney(cardBill.GetCurrentStatementAmount()),
		TotalCredit:            money.NewMoney(cardBill.GetTotalCredit()),
		TotalDebit:             money.NewMoney(cardBill.GetTotalDebit()),
		Cash:                   money.NewMoney(cardBill.GetCash()),
		Purchase:               money.NewMoney(cardBill.GetPurchase()),
		MinDue:                 money.NewMoney(cardBill.GetMinDue()),
		TotalDue:               money.NewMoney(cardBill.GetTotalDue()),
		StatementDate:          &statementDate,
		SoftDueDate:            &softDueDate,
		HardDueDate:            &hardDueDate,
		RewardsInfo:            cardBill.GetRewardsInfo(),
		AnalyticsInfo:          cardBill.GetAnalyticsInfo(),
		StatementSummary:       cardBill.GetStatementSummary(),
		AvailableLimit:         money.NewMoney(cardBill.GetAvailableLimit()),
		S3Path:                 cardBill.GetS3Path(),
		RewardId:               cardBill.GetRewardId(),
	}
}

func (c *CreditCardBill) GetProto() *ccFireflyBillingPb.CreditCardBill {
	res := &ccFireflyBillingPb.CreditCardBill{
		Id:                     c.Id,
		ActorId:                c.ActorId,
		AccountId:              c.AccountId,
		LastStatementBalance:   c.LastStatementBalance.GetPb(),
		CurrentStatementAmount: c.CurrentStatementAmount.GetPb(),
		TotalCredit:            c.TotalCredit.GetPb(),
		TotalDebit:             c.TotalDebit.GetPb(),
		Cash:                   c.Cash.GetPb(),
		Purchase:               c.Purchase.GetPb(),
		MinDue:                 c.MinDue.GetPb(),
		TotalDue:               c.TotalDue.GetPb(),
		RewardsInfo:            c.RewardsInfo,
		AnalyticsInfo:          c.AnalyticsInfo,
		StatementSummary:       c.StatementSummary,
		AvailableLimit:         c.AvailableLimit.GetPb(),
		S3Path:                 c.S3Path,
		RewardId:               c.RewardId,
	}
	if c.StatementDate != nil {
		res.StatementDate = timestamppb.New(*c.StatementDate)
	}
	if c.HardDueDate != nil {
		res.HardDueDate = timestamppb.New(*c.HardDueDate)
	}
	if c.SoftDueDate != nil {
		res.SoftDueDate = timestamppb.New(*c.SoftDueDate)
	}
	if c.CreatedAt != nil {
		res.CreatedAt = timestamppb.New(*c.CreatedAt)
	}
	if c.UpdatedAt != nil {
		res.UpdatedAt = timestamppb.New(*c.UpdatedAt)
	}
	if c.DeletedAt.Valid {
		res.DeletedAt = timestamppb.New(c.DeletedAt.Time)
	}
	return res
}
