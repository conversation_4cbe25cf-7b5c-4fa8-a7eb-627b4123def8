package impl_test

import (
	"context"
	"errors"
	"testing"

	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/plugin/dbresolver"

	"github.com/epifi/gamma/api/firefly/billing"
	billingEnums "github.com/epifi/gamma/api/firefly/billing/enums"
	"github.com/epifi/gamma/api/firefly/enums"
	billDao "github.com/epifi/gamma/firefly/billing/dao"
	"github.com/epifi/gamma/firefly/billing/dao/impl"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
)

var (
	createBillInfo = &billing.CreditCardPaymentInfo{
		Id:            "test-id",
		BillInfoId:    "test-bill-1",
		ExternalTxnId: "test-transaction-id",
		OrderId:       "test-order-id",
		PaymentDate: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        10000,
			Nanos:        0,
		},
		Status:    enums.TransactionStatus_COMPLETED,
		SubStatus: enums.TransactionSubStatus_TRANSACTION_SUB_STATUS_UNSPECIFIED,
	}
	createBillInfoEmptyForeignKey = &billing.CreditCardPaymentInfo{
		Id:            "test-id",
		BillInfoId:    "",
		ExternalTxnId: "test-transaction-id",
		OrderId:       "test-order-id",
		PaymentDate: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        10000,
			Nanos:        0,
		},
		Status:    enums.TransactionStatus_COMPLETED,
		SubStatus: enums.TransactionSubStatus_TRANSACTION_SUB_STATUS_UNSPECIFIED,
		AccountId: "account-id-1",
	}

	getByFieldPaymentInfoResponse = &billing.CreditCardPaymentInfo{
		Id:             "test-bill-payment-info-1",
		BillInfoId:     "test-bill-1",
		ExternalTxnId:  "test-transaction-id-1",
		VendorTxnRefNo: "test-vendor-txn-ref-no-1",
		OrderId:        "test-order-id-1",
		PaymentDate: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        10,
			Nanos:        0,
		},
		Status:    enums.TransactionStatus_COMPLETED,
		SubStatus: enums.TransactionSubStatus_TRANSACTION_SUB_STATUS_UNSPECIFIED,
		AccountId: "account-id-1",
	}

	defaultBillPaymentInfoFieldMask = []billingEnums.CreditCardPaymentInfoFieldMask{
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_ID,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_BILL_INFO_ID,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_EXTERNAL_TXN_ID,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_ORDER_ID,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_AMOUNT,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_PAYMENT_DATE,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_STATUS,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_SUB_STATUS,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_CREATED_AT,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_UPDATED_AT,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_DELETED_AT,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_VENDOR_TXN_REF_NO,
		billingEnums.CreditCardPaymentInfoFieldMask_CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_ACCOUNT_ID,
	}
)

func TestCreditCardBillPaymentDaoCRDB_Create(t *testing.T) {
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := impl.NewCrdbCreditCardPaymentDao(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"credit_card_payment_info"})

	tests := []struct {
		name                  string
		creditCardPaymentInfo *billing.CreditCardPaymentInfo
		want                  *billing.CreditCardPaymentInfo
		wantErr               bool
	}{
		{
			name:                  "Bill payment info successfully created for existing bill",
			creditCardPaymentInfo: createBillInfo,
			want:                  createBillInfo,
			wantErr:               false,
		},
		{
			name:                  "Foreign Key Null Test",
			creditCardPaymentInfo: createBillInfoEmptyForeignKey,
			wantErr:               false,
			want:                  createBillInfoEmptyForeignKey,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.Create(context.Background(), tt.creditCardPaymentInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				tt.want.Id = got.Id
				tt.want.CreatedAt = got.CreatedAt
				tt.want.UpdatedAt = got.UpdatedAt
				tt.want.DeletedAt = got.DeletedAt
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreditCardBillPaymentDaoCRDB_GetById(t *testing.T) {
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := impl.NewCrdbCreditCardPaymentDao(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"credit_card_payment_info"})
	tests := []struct {
		name    string
		id      string
		want    *billing.CreditCardPaymentInfo
		wantErr bool
		err     error
	}{
		{
			name:    "Id exists Case",
			id:      "test-bill-payment-info-1",
			want:    getByFieldPaymentInfoResponse,
			wantErr: false,
		},
		{
			name:    "Id does not exist Case",
			id:      "random-id",
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name:    "Id is empty case",
			id:      "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetById(context.Background(), tt.id, defaultBillPaymentInfoFieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.err)
				}
			}
			if tt.want != nil {
				tt.want.PaymentDate = got.GetPaymentDate()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.DeletedAt = got.GetDeletedAt()
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreditCardBillPaymentDaoCRDB_GetByOrderId(t *testing.T) {

	getByOrderIdFieldPaymentInfoResponse := &billing.CreditCardPaymentInfo{
		Id:             "test-bill-payment-info-1",
		BillInfoId:     "test-bill-1",
		ExternalTxnId:  "test-transaction-id-1",
		VendorTxnRefNo: "test-vendor-txn-ref-no-1",
		OrderId:        "test-order-id-1",
		PaymentDate: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        10,
			Nanos:        0,
		},
		Status:    enums.TransactionStatus_COMPLETED,
		SubStatus: enums.TransactionSubStatus_TRANSACTION_SUB_STATUS_UNSPECIFIED,
		AccountId: "account-id-1",
	}
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := impl.NewCrdbCreditCardPaymentDao(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"credit_card_payment_info"})
	tests := []struct {
		name    string
		orderId string
		want    *billing.CreditCardPaymentInfo
		wantErr bool
		err     error
	}{
		{
			name:    "Order Id exists Case",
			orderId: "test-order-id-1",
			want:    getByOrderIdFieldPaymentInfoResponse,
			wantErr: false,
		},
		{
			name:    "Order Id does not exist Case",
			orderId: "random-order-id",
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name:    "Order Id is empty case",
			orderId: "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByOrderId(context.Background(), tt.orderId, defaultBillPaymentInfoFieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.err)
				}
			}
			if tt.want != nil {
				tt.want.PaymentDate = got.GetPaymentDate()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.DeletedAt = got.GetDeletedAt()
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetById() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func TestCreditCardBillPaymentDaoCRDB_GetByExternalTxnId(t *testing.T) {

	getByExtTxnIdFieldPaymentInfoResponse := &billing.CreditCardPaymentInfo{
		Id:             "test-bill-payment-info-2",
		BillInfoId:     "test-bill-2",
		ExternalTxnId:  "test-transaction-id-2",
		VendorTxnRefNo: "test-vendor-txn-ref-no-2",
		OrderId:        "test-order-id-2",
		PaymentDate: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        10,
			Nanos:        0,
		},
		Status:    enums.TransactionStatus_COMPLETED,
		SubStatus: enums.TransactionSubStatus_TRANSACTION_SUB_STATUS_UNSPECIFIED,
		AccountId: "account-id-2",
	}

	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := impl.NewCrdbCreditCardPaymentDao(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"credit_card_payment_info"})
	tests := []struct {
		name          string
		ExternalTxnId string
		want          *billing.CreditCardPaymentInfo
		wantErr       bool
		err           error
	}{
		{
			name:          "Transaction Id exists Case",
			ExternalTxnId: "test-transaction-id-2",
			want:          getByExtTxnIdFieldPaymentInfoResponse,
			wantErr:       false,
		},
		{
			name:          "Transaction Id does not exist Case",
			ExternalTxnId: "random-transaction-id",
			wantErr:       true,
			err:           epifierrors.ErrRecordNotFound,
		},
		{
			name:          "Transaction Id is empty case",
			ExternalTxnId: "",
			wantErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByExternalTxnId(context.Background(), tt.ExternalTxnId, defaultBillPaymentInfoFieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.err)
				}
			}
			if tt.want != nil {
				tt.want.PaymentDate = got.GetPaymentDate()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.DeletedAt = got.GetDeletedAt()
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreditCardBillPaymentDaoCRDB_GetByExternalTxnIdAndAccountId(t *testing.T) {

	getByExtTxnIdFieldPaymentInfoResponse := &billing.CreditCardPaymentInfo{
		Id:             "test-bill-payment-info-2",
		BillInfoId:     "test-bill-2",
		ExternalTxnId:  "test-transaction-id-2",
		VendorTxnRefNo: "test-vendor-txn-ref-no-2",
		OrderId:        "test-order-id-2",
		PaymentDate: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        10,
			Nanos:        0,
		},
		Status:    enums.TransactionStatus_COMPLETED,
		SubStatus: enums.TransactionSubStatus_TRANSACTION_SUB_STATUS_UNSPECIFIED,
		AccountId: "account-id-2",
	}

	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := impl.NewCrdbCreditCardPaymentDao(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"credit_card_payment_info"})
	tests := []struct {
		name          string
		ExternalTxnId string
		AccountId     string
		want          *billing.CreditCardPaymentInfo
		wantErr       bool
		err           error
	}{
		{
			name:          "Transaction Id exists Case",
			ExternalTxnId: "test-transaction-id-2",
			want:          getByExtTxnIdFieldPaymentInfoResponse,
			wantErr:       false,
			AccountId:     "account-id-2",
		},
		{
			name:          "Transaction Id does not exist Case",
			ExternalTxnId: "random-transaction-id",
			wantErr:       true,
			err:           epifierrors.ErrRecordNotFound,
		},
		{
			name:          "Transaction Id is empty case",
			ExternalTxnId: "",
			wantErr:       true,
		},
		{
			name:          "Account Id empty case",
			ExternalTxnId: "test-transaction-id-2",
			want:          getByExtTxnIdFieldPaymentInfoResponse,
			wantErr:       false,
			err:           nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByExternalTxnIdAndAccountId(context.Background(), tt.ExternalTxnId, tt.AccountId, defaultBillPaymentInfoFieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.err)
				}
			}
			if tt.want != nil {
				tt.want.PaymentDate = got.GetPaymentDate()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.DeletedAt = got.GetDeletedAt()
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreditCardBillPaymentDaoCRDB_GetByBillInfoId(t *testing.T) {
	getByBillInfoIdFieldPaymentInfoResponse := &billing.CreditCardPaymentInfo{
		Id:             "test-bill-payment-info-3",
		BillInfoId:     "test-bill-3",
		ExternalTxnId:  "test-transaction-id-3",
		VendorTxnRefNo: "test-vendor-txn-ref-no-3",
		OrderId:        "test-order-id-3",
		PaymentDate: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        10,
			Nanos:        0,
		},
		Status:    enums.TransactionStatus_COMPLETED,
		SubStatus: enums.TransactionSubStatus_TRANSACTION_SUB_STATUS_UNSPECIFIED,
		AccountId: "account-id-3",
	}

	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := impl.NewCrdbCreditCardPaymentDao(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"credit_card_payment_info"})

	tests := []struct {
		name       string
		billInfoId string
		want       []*billing.CreditCardPaymentInfo
		wantErr    bool
		err        error
	}{
		{
			name:       "Bill Info Id exists Case",
			billInfoId: "test-bill-3",
			want:       []*billing.CreditCardPaymentInfo{getByBillInfoIdFieldPaymentInfoResponse},
			wantErr:    false,
		},
		{
			name:       "Bill info Id does not exist Case",
			billInfoId: "random-bill-id",
			wantErr:    true,
			err:        epifierrors.ErrRecordNotFound,
		},
		{
			name:       "Bill info Id is empty case",
			billInfoId: "",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByBillInfoId(context.Background(), tt.billInfoId, defaultBillPaymentInfoFieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByAccountId() error = %v, wantErr %v", err, tt.err)
				}
			}
			if len(tt.want) != len(got) && len(tt.want) != 0 {
				t.Errorf("GetByAccountId() got = %v, want %v", got, tt.want)
			}
			if tt.want != nil && len(tt.want) == len(got) && len(tt.want) != 0 {
				var idx int
				for idx = 0; idx < len(got); idx++ {
					tt.want[idx].CreatedAt = got[idx].GetCreatedAt()
					tt.want[idx].UpdatedAt = got[idx].GetUpdatedAt()
					tt.want[idx].DeletedAt = got[idx].GetDeletedAt()
					tt.want[idx].PaymentDate = got[idx].GetPaymentDate()
					if !proto.Equal(got[idx], tt.want[idx]) {
						t.Errorf("GetByAccountId() got = %v, want %v", got, tt.want)
					}
				}
			}
		})
	}
}

func TestCreditCardBillPaymentDaoCRDB_Update(t *testing.T) {
	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := impl.NewCrdbCreditCardPaymentDao(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"credit_card_payment_info"})

	creditCardPaymentInfoUpdatePayload := &billing.CreditCardPaymentInfo{
		Id:            "test-bill-payment-info-1",
		BillInfoId:    "test-bill-1",
		ExternalTxnId: "test-transaction-id",
		OrderId:       "test-order-id",
		PaymentDate:   timestampPb.Now(),
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        5000,
			Nanos:        0,
		},
		Status:    enums.TransactionStatus_COMPLETED,
		SubStatus: enums.TransactionSubStatus_TRANSACTION_SUB_STATUS_UNSPECIFIED,
		CreatedAt: timestampPb.Now(),
		UpdatedAt: timestampPb.Now(),
	}
	creditCardPaymentInfoUpdatePayload2 := &billing.CreditCardPaymentInfo{
		Id:            "test-random-bill-info",
		BillInfoId:    "test-bill-1",
		ExternalTxnId: "test-transaction-id",
		OrderId:       "test-order-id",
		PaymentDate:   timestampPb.Now(),
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        5000,
			Nanos:        0,
		},
		Status:    enums.TransactionStatus_COMPLETED,
		SubStatus: enums.TransactionSubStatus_TRANSACTION_SUB_STATUS_UNSPECIFIED,
	}

	tests := []struct {
		name                  string
		creditCardPaymentInfo *billing.CreditCardPaymentInfo
		wantErr               bool
	}{
		{
			name:                  "Credit Bill Info Update Test",
			creditCardPaymentInfo: creditCardPaymentInfoUpdatePayload,
			wantErr:               false,
		},
		{
			name:                  "Credit Bill Update fail test due to invalid id",
			creditCardPaymentInfo: creditCardPaymentInfoUpdatePayload2,
			wantErr:               true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := c.Update(context.Background(), tt.creditCardPaymentInfo, defaultBillPaymentInfoFieldMask); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCreditCardPaymentDaoCRDB_GetByVendorTxnRefNo(t *testing.T) {

	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := impl.NewCrdbCreditCardPaymentDao(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"credit_card_payment_info"})

	tests := []struct {
		name           string
		vendorTxnRefNo string
		want           *billing.CreditCardPaymentInfo
		wantErr        bool
		err            error
	}{
		{
			name:           "Vendor txn ref no exists case",
			vendorTxnRefNo: "test-vendor-txn-ref-no-1",
			want:           getByFieldPaymentInfoResponse,
			wantErr:        false,
		},
		{
			name:           "Vendor txn ref does not exist Case",
			vendorTxnRefNo: "random-vendor-txn-ref-no",
			wantErr:        true,
			err:            epifierrors.ErrRecordNotFound,
		},
		{
			name:           "Vendor txn ref is empty case",
			vendorTxnRefNo: "",
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByVendorTxnRefNo(context.Background(), tt.vendorTxnRefNo, defaultBillPaymentInfoFieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.err)
				}
			}
			if tt.want != nil {
				tt.want.PaymentDate = got.GetPaymentDate()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.DeletedAt = got.GetDeletedAt()
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreditCardPaymentDaoCRDB_GetByAccountId(t *testing.T) {
	getByAccountIdFieldPaymentInfoResponse := &billing.CreditCardPaymentInfo{
		Id:             "test-bill-payment-info-3",
		BillInfoId:     "test-bill-3",
		ExternalTxnId:  "test-transaction-id-3",
		VendorTxnRefNo: "test-vendor-txn-ref-no-3",
		OrderId:        "test-order-id-3",
		PaymentDate: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        10,
			Nanos:        0,
		},
		Status:    enums.TransactionStatus_COMPLETED,
		SubStatus: enums.TransactionSubStatus_TRANSACTION_SUB_STATUS_UNSPECIFIED,
		AccountId: "account-id-3",
	}

	dbConn := dbNameToDbInstancePoolMap[ffConf.CreditCardDb.Name]
	dbInstance, release := dbConn.GetDbInstance(t)
	dbCon := dbInstance.GetConnection().Clauses(dbresolver.Use(ffConf.DbConnectionAliases.CreditCardPgdbConnAlias))
	c := impl.NewCrdbCreditCardPaymentDao(dbCon, idgen.NewDomainIdGenerator(idgen.NewClock()))
	defer release([]string{"credit_card_payment_info"})

	tests := []struct {
		name      string
		accountId string
		want      []*billing.CreditCardPaymentInfo
		wantErr   bool
		err       error
	}{
		{
			name:      "Account Id exists Case",
			accountId: "account-id-3",
			want:      []*billing.CreditCardPaymentInfo{getByAccountIdFieldPaymentInfoResponse},
			wantErr:   false,
		},
		{
			name:      "Account Id does not exist Case",
			accountId: "random-account-id",
			wantErr:   true,
			err:       epifierrors.ErrRecordNotFound,
		},
		{
			name:      "Account info Id is empty case",
			accountId: "",
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.GetByAccountId(context.Background(), tt.accountId, defaultBillPaymentInfoFieldMask, billDao.WithOrderByUpdatedAtDescending(), billDao.WithLimit(1))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByAccountId() error = %v, wantErr %v", err, tt.err)
				}
			}
			if len(tt.want) != len(got) && len(tt.want) != 0 {
				t.Errorf("GetByAccountId() got = %v, want %v", got, tt.want)
			}
			if tt.want != nil && len(tt.want) == len(got) && len(tt.want) != 0 {
				var idx int
				for idx = 0; idx < len(got); idx++ {
					tt.want[idx].CreatedAt = got[idx].GetCreatedAt()
					tt.want[idx].UpdatedAt = got[idx].GetUpdatedAt()
					tt.want[idx].DeletedAt = got[idx].GetDeletedAt()
					tt.want[idx].PaymentDate = got[idx].GetPaymentDate()
					if !proto.Equal(got[idx], tt.want[idx]) {
						t.Errorf("GetByAccountId() got = %v, want %v", got, tt.want)
					}
				}
			}
		})
	}
}
