package common_test

import (
	"testing"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.temporal.io/sdk/testsuite"
	"go.temporal.io/sdk/workflow"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	ffActivityPb "github.com/epifi/gamma/api/firefly/activity"
	ffEnums "github.com/epifi/gamma/api/firefly/enums"
	celestialActivity "github.com/epifi/gamma/celestial/activity"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	"github.com/epifi/gamma/firefly/activity"
	"github.com/epifi/gamma/firefly/workflow/stages"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"
)

func TestPreProcessStageCommon_PreProcess(t *testing.T) {
	tests := []struct {
		name             string
		workFunc         func(workflow.Context, *stages.PreProcessRequest) error
		setupMockCalls   func(*testsuite.TestWorkflowEnvironment)
		wantErr          bool
		prePreprocessReq *stages.PreProcessRequest
	}{
		{
			name:     "Success, preprocess executed without error",
			workFunc: PerformPreprocessCommonTest,
			setupMockCalls: func(env *testsuite.TestWorkflowEnvironment) {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, mock.Anything).
					Return(nil)
				env.OnActivity(string(firefly.CreateCardRequestStage), mock.Anything, mock.Anything).
					Return(&ffActivityPb.CreateCardRequestStageResponse{
						CardRequest:      cardReq,
						CardRequestStage: cardReqStage,
					}, nil)
			},
			wantErr: false,
			prePreprocessReq: &stages.PreProcessRequest{
				CardRequest: cardReq,
				StageName:   ffEnums.CardRequestStageName_CARD_REQUEST_STAGE_NAME_UNSPECIFIED,
				WfProcessingParams: &workflowPb.ProcessingParams{ClientReqId: &workflowPb.ClientReqId{
					Id: clientReqId,
				}},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&activity.Processor{})
			env.RegisterActivity(&celestialActivity.Processor{})
			epificontext.WorkflowContextWithOwnership(nil, ownership)
			env.RegisterWorkflow(tt.workFunc)
			tt.setupMockCalls(env)
			env.ExecuteWorkflow(tt.workFunc, tt.prePreprocessReq)
			assert.True(t1, env.IsWorkflowCompleted())
			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t1.Errorf(" issue in workfunc error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}
		})
	}
}

func PerformPreprocessCommonTest(ctx workflow.Context, req *stages.PreProcessRequest) error {
	_, err := preprocessStageCommon.PreProcess(ctx, req)
	if err != nil {
		return errors.Wrap(err, "error in  preprocess stage")
	}
	return nil
}
