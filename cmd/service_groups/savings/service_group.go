package savings

import (
	"github.com/epifi/be-common/pkg/cmd/config"
	"github.com/epifi/be-common/tools/servergen/meta"

	"github.com/epifi/gamma/api/savings/consumer"
	savingsdeveloper "github.com/epifi/gamma/api/savings/developer"
	"github.com/epifi/gamma/api/savings/extacct"
	extAccCoPb "github.com/epifi/gamma/api/savings/extacct/consumer"
	savingsWatsonClientPb "github.com/epifi/gamma/api/savings/watson"
	savingsconf "github.com/epifi/gamma/savings/config"

	pb "github.com/epifi/gamma/api/savings"
	wire "github.com/epifi/gamma/savings/wire"
)

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer:     wire.InitializeService,
		GRPCRegisterMethods: []any{pb.RegisterSavingsServer},
	},
	{
		WireInitializer:     wire.InitializeDevSavingsService,
		GRPCRegisterMethods: []any{savingsdeveloper.RegisterSavingsDbStatesServer},
	},
	{
		WireInitializer:     wire.InitialiseExternalAccountsService,
		GRPCRegisterMethods: []any{extacct.RegisterExternalAccountsServer},
	},
	{
		WireInitializer:     wire.InitialiseWatsonClientService,
		GRPCRegisterMethods: []any{savingsWatsonClientPb.RegisterWatsonServer},
	},
	{
		WireInitializer: wire.InitializeSavingsConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  pb.ProcessAccountCreationMethod,
				ConfigField: "SavingsCreationSubscriber",
			},
		},
		InitCondition: func(conf *config.Config) bool {
			savingsConf, err := savingsconf.Load()
			if err != nil {
				panic(err)
			}
			return savingsConf.Server.EnablePoller
		},
	},
	{
		WireInitializer: wire.InitialiseSavingsCallbackConsumer,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  pb.ProcessAccountCreationCallbackMethod,
				ConfigField: "SavingsCallbackSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitialiseCreateVPAConsumer,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  pb.CreateVPAMethod,
				ConfigField: "CreateVPASubscriber",
			},
			{
				MethodName:  pb.ProcessSavingsAccountPICreationMethod,
				ConfigField: "SavingsAccountPICreationSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitialiseBalanceUpdateConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer.NotifyLowBalanceUsersMethod,
				ConfigField: "BalanceUpdateEventSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitialiseOperStatusUpdateConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer.UpdateSavingsAccountStatusMethod,
				ConfigField: "OperationalStatusUpdateSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitialiseEventsSubscriberService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer.ProcessTierUpdateEventConsumerMethod,
				ConfigField: "ProcessTierUpdateEventSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitialiseEventsSubscriberService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer.ProcessKycUpdateEventMethod,
				ConfigField: "ProcessKycUpdateEventSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitialiseSaClosureClosureConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer.ProcessBalanceUpdateEventMethod,
				ConfigField: "ProcessBalanceUpdateEventSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitialiseExtAcctConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  extAccCoPb.ProcessThirdPartyAccountSharingEventMethod,
				ConfigField: "ThirdPartyAccountSharingSubscriber",
			},
		},
	},
}
