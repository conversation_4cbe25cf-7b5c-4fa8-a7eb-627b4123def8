package servergenhook

import (
	"fmt"
	"net/http"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	payErrorPkg "github.com/epifi/gamma/pkg/pay/payerrorcode"

	vendorfedral "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/gamma/api/vendors/paystatusmapping"
	payResponseMapping "github.com/epifi/gamma/api/vendors/responsemapping/payment"
	heHelper "github.com/epifi/gamma/health_engine/serverhelper"
	orderconf "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/payment/decision_engine"
	"github.com/epifi/gamma/order/workflow"
	payConfig "github.com/epifi/gamma/pay/config/server"
	"github.com/epifi/gamma/pkg/pay/pgauthkeys"

	"github.com/epifi/be-common/pkg/cfg"
)

func InitOrderServiceGroupWorkflowConfigMap() (func(), error) {
	cleanupFn := func() {}

	orderConf, err := orderconf.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err), zap.String("serviceGroup", string(cfg.ORDER_SERVICE)))
		return cleanupFn, err
	}
	closeWorkflowConn, err := workflow.InitWorkflow(orderConf, cfg.ORDER_SERVER)
	if err != nil {
		logger.ErrorNoCtx("failed to initialize workflow config map", zap.String("serviceGroup", string(cfg.ORDER_SERVICE)), zap.Error(err))
		return cleanupFn, err
	}
	return func() {
		closeWorkflowConn()
	}, err
}

func InitPayServer(httpMux *http.ServeMux, epifiDb types.EpifiCRDB) (func(), error) {
	cleanupFn := func() {}

	storagev2.InitDefaultCRDBTransactionExecutor(epifiDb)

	err := payErrorPkg.LoadPayErrorView()
	if err != nil {
		logger.Panic("error loading error view", zap.Error(err))
	}

	orderConf, err := orderconf.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ORDER_SERVICE))
		return cleanupFn, err
	}

	err = paystatusmapping.LoadPayStatusCodes(orderConf.PayFundTransferStatusCodeJson(), orderConf.PayUpiStatusCodeJson(), orderConf.EnachTransactionStatusCodeJson())
	if err != nil {
		return cleanupFn, fmt.Errorf("error loading pay status code: %w", err)
	}

	err = decision_engine.LoadImpsIfscMapping(orderConf.ImpsIfscCodesCsv())
	if err != nil {
		return cleanupFn, fmt.Errorf("error loading imps supporting ifsc mapping from csv: %w", err)
	}

	err = decision_engine.LoadRtgsBlockListIfscCodes(orderConf.RtgsBlockListIfscCodesCsv())
	if err != nil {
		return cleanupFn, fmt.Errorf("error loading rtgs blocklist ifsc codes from csv: %w", err)
	}

	err = payResponseMapping.LoadCardTxnResponseStatusCodes(orderConf.CardTxnStatusCodeJson())
	if err != nil {
		return cleanupFn, fmt.Errorf("error loading card txn status code: %w", err)
	}

	heHelper.InitializeHealthEngineHttpServeMux(httpMux)

	err = vendorfedral.InitNotificationParserRules()
	if err != nil {
		return cleanupFn, fmt.Errorf("failed to load notification parser facts. %w", err)
	}

	return cleanupFn, err
}

func InitialisePgProgramToAuthParamsMap() (func(), error) {
	conf, err := payConfig.Load()
	if err != nil {
		return func() {}, err
	}
	err = pgauthkeys.InitialisePgProgramToAuthParams(conf.PgProgramToAuthSecretMap)
	if err != nil {
		return func() {}, err
	}
	return func() {}, nil
}
