GrpcRatelimiterParams:
  RateLimitConfig:
    RedisOptions:
      IsSecureRedis: true
      Options:
        Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
        Password: ""
      AuthDetails:
        SecretPath: "prod/redis/centralgrowth/prefixaccess"
        Environment: "prod"
        Region: "ap-south-1"
      ClientName: centralgrowth

Profiling:
  StackDriverProfiling:
    EnableStackDriver: true
  AutomaticProfiling:
    EnableAutoProfiling: false

Databases:
  EpifiCRDB:
    DbType: "CRDB"
    AppName: "savings"
    StatementTimeout: 10s
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 40
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false
  ConnectedAccountPGDB:
    DbType: "PGDB"
    AppName: "connectedaccount"
    StatementTimeout: 10s
    Name: "connected_account"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/connected-account"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  TieringPGDB:
    DbType: "PGDB"
    AppName: "tiering"
    StatementTimeout: 10s
    Name: "tiering"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/tiering_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  SalaryprogramPGDB:
    DbType: "PGDB"
    AppName: "salaryprogram"
    StatementTimeout: 10s
    Name: "salaryprogram"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 50
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/salaryprogram"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

  FeatureEngineeringPGDB:
    DbType: "PGDB"
    AppName: "connectedaccount"
    StatementTimeout: 5s
    Name: "feature_engineering"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 50
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/feature_engineering_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

DbConfigMap:
  CONNECTED_ACCOUNT_WEALTH:
    DbType: "PGDB"
    AppName: "connectedaccount"
    StatementTimeout: 10s
    Name: "connected_account"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/connected-account"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false
  FEATURE_ENGINEERING_TECH:
    DbType: "PGDB"
    AppName: "connected-account-worker"
    StatementTimeout: 5s
    Name: "feature_engineering"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 50
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/feature_engineering_dev_user"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false

RedisRateLimiterName: "ConnectedAccountRedisStore"
RedisClusters:
  BalHistoryRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
      Password: "" ## empty string for no password
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: balancehistory
    HystrixCommand:
      CommandName: "bal_history_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 1500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 30
  ConnectedAccountRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
      Password: "" ## empty string for no password
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: connectedaccount
    HystrixCommand:
      CommandName: "connectedaccount_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 5000
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 30
  TieringActorRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
      Password: "" ## empty string for no password
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: tiering
    HystrixCommand:
      CommandName: "tiering_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 1500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 30
  TieringConnectedAccountRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
      Password: "" ## empty string for no password
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: tiering
  UserSavingsRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: savings
    HystrixCommand:
      CommandName: "savings_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
  SalaryProgramRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
      Password: "" ## empty string for no password
      ClientName: salaryprogram
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"

RueidisRedisClients:
  SavingsRueidisRedisStore:
    Addrs:
      - "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    Hystrix:
      CommandName: "user_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
    EnableSecureRedis: true
    ClientName: savings
  CollapserRueidisRedisStore:
    Addrs:
      - "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: "central-growth-collapser"
    Hystrix:
      CommandName: "collapser_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
    EnableSecureRedis: true


Secrets:
  Ids:
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"

Collapser:
  RPCs:
    "/connected_account.ConnectedAccount/GetAccountDetailsBulk":
      EnableCollapse: true
      Timeout: "5s"
      CacheTTL: "1s"
    "/connected_account.ConnectedAccount/GetAccounts":
      EnableCollapse: true
      Timeout: "5s"
      CacheTTL: "1s"
    "/tiering.Tiering/GetTierAtTime":
      EnableCollapse: true
      Timeout: "5s"
      CacheTTL: "20s"
    "/tiering.Tiering/GetTieringPitchV2":
      EnableCollapse: true
      Timeout: "5s"
      CacheTTL: "20s"
    "/tiering.Tiering/FetchDynamicElements":
      EnableCollapse: true
      Timeout: "5s"
      CacheTTL: "20s"
    "/tiering.Tiering/GetAMBInfo":
      EnableCollapse: true
      Timeout: "5s"
      CacheTTL: "30s"
