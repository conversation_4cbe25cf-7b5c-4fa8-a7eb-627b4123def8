Databases:
  AuthPGDB:
    Password: auth_dev_user
    SecretName: ""
    Username: auth_dev_user
  EpifiCRDB:
    SSLClientCert: ""
    SSLClientKey: ""
    SSLMode: disable
    SS<PERSON>ootCert: ""
    SecretName: ""
Logging:
  EnableLoggingToFile: false
RedisClusters:
  AFURedisStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
    ReplicaAddr: redis:6379
  AuthDeviceRegistrationRedisStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
  AuthLocationRedisStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
  AuthRedisStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
    ReplicaAddr: redis:6379
  AuthTokenRedisMasterReplicaStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
    ReplicaAddr: redis:6379
  AuthTokenRedisStore:
    IsSecureRedis: false
    Options:
      Addr: redis:6379
