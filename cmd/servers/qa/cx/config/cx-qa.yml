Databases:
  SherlockPGDB:
    StatementTimeout: 5m
    Name: "sherlock"
    DbType: "PGDB"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/postgres/sherlock"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 1ms
      UseInsecureLog: true

  InapphelpPGDB:
    StatementTimeout: 5m
    Name: "inapphelp"
    DbType: "PGDB"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/postgres/inapphelp"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 1ms
      UseInsecureLog: true

  CasbinSherlockPGDBv1:
    DbType: "PGDB"
    StatementTimeout: 5m
    Name: "sherlock"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "qa/rds/postgres/sherlock"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 1ms
      UseInsecureLog: true

RedisRateLimiterName: "CxRedisStore"
RedisClusters:
  CxRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      # on qa we are using common redis cluster with a different db
      DB: 0
    HystrixCommand:
      CommandName: "cx_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80

RueidisRedisClients:
  CollapserRueidisRedisStore:
    Addrs:
      - "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    RedisDB: 0
    ClientName: "cx-collapser"
    Hystrix:
      CommandName: "collapser_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
    EnableSecureRedis: true
