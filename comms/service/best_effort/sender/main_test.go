package sender

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/comms/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, genConf, _, teardown := test.InitTestServerForPGDB()
	eTS = EmailTestSuite{conf: conf, genConf: genConf}
	sTS = SmsTestSuite{conf: conf, genConf: genConf}
	nTS = NotificationTestSuite{conf: conf, genConf: genConf}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
