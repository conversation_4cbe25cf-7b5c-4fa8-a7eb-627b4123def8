Application:
  Environment: "staging"
  Name: "nudge"

# Nudge service is actually initialized on the port defined in user-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Nudge service to be running on a
# different port in the user server
Server:
  Ports:
    GrpcPort: 8083
    GrpcSecurePort: 9521
    HttpPort: 9999

NudgeDb:
  AppName: "nudge"
  StatementTimeout: 5s
  Name: "nudge"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Secrets:
  Ids:
    KafkaCredentials: "staging/kafka/nudges"
    KafkaCaCertificate: "staging/kafka/cert"
    DbCredentials: "staging/rds/postgres14"
    RudderWriteKey: "staging/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "staging/gcloud/profiling-service-account-key"

AWS:
  Region: "ap-south-1"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
    ClientName: nudge

NudgeRankingMLModelConfig:
  Enabled: true
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 10
      ClientName: nudge-ranking

Tracing:
  Enable: true

KYCSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-kyc-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1000
        Period: 1m
    Namespace: "nudge"

InvestmentSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-investment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

OrderUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-order-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1000
        Period: 1m
    Namespace: "nudge"

CAAccountUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-ca-account-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1000
        Period: 1m
    Namespace: "nudge"

SalaryProgramStatusUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-salaryprogram-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1000
        Period: 1m
    Namespace: "nudge"

EntryEventCustomDelayPublisher:
  DestQueueName: "staging-nudge-entry-event-queue"
  OrchestratorSqsPublisher:
    QueueName: "staging-custom-delay-orchestrator-queue"

ExitEventSqsPublisher:
  QueueName: "staging-nudge-exit-event-queue"

ExitEvaluatorSqsPublisher:
  QueueName: "staging-nudge-exit-evaluator-queue"

EntryEvaluatorSqsPublisher:
  QueueName: "staging-nudge-entry-evaluator-queue"

EntryEvaluatorSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-entry-evaluator-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 200
        Period: 10s
    Namespace: "nudge"

ExitEvaluatorSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-exit-evaluator-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 200
        Period: 10s
    Namespace: "nudge"

EntryEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-entry-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 10s
    Namespace: "nudge"

ExitEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-exit-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 10s
    Namespace: "nudge"

DismissalEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-dismissal-feedback-info-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 10s
    Namespace: "nudge"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

UpiEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-upi-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 200
        Period: 10s
    Namespace: "nudge"

RewardGenerationEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-reward-generation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 200
        Period: 10s
    Namespace: "nudge"

RewardStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-reward-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 200
        Period: 10s
    Namespace: "nudge"

PALEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-preapprovedloan-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 200
        Period: 10s
    Namespace: "nudge"

IncomeUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-income-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 200
        Period: 10s
    Namespace: "nudge"

NonFinancialInvestmentSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-non-financial-investment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 200
        Period: 10s
    Namespace: "nudge"

DebitCardUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-debit-card-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 200
        Period: 10s
    Namespace: "nudge"

OfferRedemptionStatusUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-offer-redemption-status-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 200
        Period: 10s
    Namespace: "nudge"

RudderEventKafkaConsumerGroup:
  StartOnServerStart: false
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  GroupID: "nudges-consumer-group-id"
  Topic: "qa.events.tech"
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  Whitelist:
    Enabled: true
    KeyPath: "event"
  RateLimitConfig:
    RedisOptions:
      IsSecureRedis: true
      Options:
        Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
        Password: ""
        DB: 0
    ResourceMap:
      consumer_group:
        Rate: 2000
        Period: 2s
    Namespace: "nudge"

ManualSnoozeDuration: 2m # 2 minutes

NudgeDismissalConfig:
  EligibleFeedbackInfoQuestionIds:
    - "d4c8fbdf-ff42-4cd6-9dab-42418cae7869"
  EligibleFeedbackInfoAnswers:
    - "Yes"

ActorNudgeStatusUpdateEventSnsPublisher:
  TopicName: "staging-nudge-actor-nudge-status-update-event-topic"

ActorNudgeStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-actor-nudge-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "nudge"

JourneyEntryEventSqsPublisher:
  QueueName: "staging-nudge-journey-entry-evaluator-queue"

JourneyEntryEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-nudge-journey-entry-evaluator-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "nudge"
