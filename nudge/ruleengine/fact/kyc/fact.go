package kyc

import (
	"github.com/Knetic/govaluate"
	"github.com/epifi/gamma/rewards/helper"

	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/nudge/ruleengine/fact/common"
)

type KYCFact struct {
	*common.CommonFact
	KYCEvent *userPb.KycEvent
}

// Ensure KYC implements common.IFact interface
var _ common.IFact = &KYCFact{}

func (c *KYCFact) GetExpressionFunctionMap() map[string]govaluate.ExpressionFunction {
	expressionFunctionMap := map[string]govaluate.ExpressionFunction{}
	return helper.MergeExpressionFunctionMap(expressionFunctionMap, c.CommonFact.GetExpressionFunctionMap())
}

func (c *KYCFact) GetExpressionParametersMap() map[string]interface{} {
	return map[string]interface{}{
		// kyc event is received only when user is full kyc
		"USER_KYC_STATUS": "FULL_KYC",
	}
}
