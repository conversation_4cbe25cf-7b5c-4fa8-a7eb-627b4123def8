package model

import (
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	creditRiskPb "github.com/epifi/gamma/api/stockguardian/creditrisk"
)

type LoanOfferEligibilityCriteria struct {
	Id               string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	CustomerId       string
	ClientId         string
	Status           creditRiskPb.LOECStatus
	SubStatus        string
	ClientRequestId  string
	OfferId          string
	CompletedAt      *timestampPb.Timestamp
	ExpiresAt        *timestampPb.Timestamp
	PolicyParameters []byte
	CreatedAt        *timestampPb.Timestamp
	UpdatedAt        *timestampPb.Timestamp
	DeletedAt        *timestampPb.Timestamp
}

func (l *LoanOfferEligibilityCriteria) TableName() string {
	return "loan_offer_eligibility_criteria"
}

func (l *LoanOfferEligibilityCriteria) ToProto() *creditRiskPb.LoanOfferEligibilityCriteria {
	return &creditRiskPb.LoanOfferEligibilityCriteria{
		Id:               l.Id,
		CustomerId:       l.CustomerId,
		ClientId:         l.ClientId,
		Status:           l.Status,
		SubStatus:        l.SubStatus,
		ClientRequestId:  l.ClientRequestId,
		OfferId:          l.OfferId,
		CompletedAt:      l.CompletedAt,
		ExpiresAt:        l.ExpiresAt,
		PolicyParameters: l.PolicyParameters,
		CreatedAt:        l.CreatedAt,
		UpdatedAt:        l.UpdatedAt,
		DeletedAt:        l.DeletedAt,
	}
}

func GetLOECModel(proto *creditRiskPb.LoanOfferEligibilityCriteria) *LoanOfferEligibilityCriteria {
	return &LoanOfferEligibilityCriteria{
		Id:               proto.GetId(),
		CustomerId:       proto.GetCustomerId(),
		ClientId:         proto.GetClientId(),
		Status:           proto.GetStatus(),
		SubStatus:        proto.GetSubStatus(),
		ClientRequestId:  proto.GetClientRequestId(),
		OfferId:          proto.GetOfferId(),
		CompletedAt:      proto.GetCompletedAt(),
		ExpiresAt:        proto.GetExpiresAt(),
		PolicyParameters: proto.GetPolicyParameters(),
		CreatedAt:        proto.GetCreatedAt(),
		UpdatedAt:        proto.GetUpdatedAt(),
		DeletedAt:        proto.GetDeletedAt(),
	}
}
