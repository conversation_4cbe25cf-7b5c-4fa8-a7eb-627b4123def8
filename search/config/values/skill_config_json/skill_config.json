{"skill_config": [{"responseTypeOrder": ["QUICK_LINK"], "fiQuickLinkConfig": {"enable": true, "links": ["TRANSFER_IN"]}, "skillName": "ADD_FUNDS"}, {"responseTypeOrder": ["QUICK_LINK"], "fiQuickLinkConfig": {"enable": true}, "skillName": "AUTOPAY"}, {"fiSummaryConfig": {"enable": true, "summaryText": "{{ if eq .DeliveryState \"IN_TRANSIT\" }} Your card is in transit. Use AWB {{.Awb}} on {{.Carrier}} for more details. {{end -}}\n{{- if eq .DeliveryState \"OUT_FOR_DELIVERY\"}} Your card is out for delivery. Use AWB {{.Awb}} on {{.Carrier}} for more details. {{end -}}\n{{- if eq .DeliveryState \"RETURNED_TO_ORIGIN\"}} Your card has been delivered to origin. Use AWB {{.Awb}} on {{.Carrier}} for more details. {{end -}}\n{{- if eq .DeliveryState \"SHIPPED\"}} Your card was dispatched for delivery on {{.DeliveryDate}}. Use AWB {{.Awb}} on {{.Carrier}} for more details. {{end -}}\n{{- if eq .DeliveryState \"DELIVERED\"}} Your card was delivered on {{.DeliveryDate}} {{end -}}\n{{- if eq .DeliveryState \"CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED\" }} Your card will be dispatched for delivery soon. Use AWB {{.Awb}} on {{.Carrier}} for more details. {{end -}}{{- if ne .ErrText \"\" }}{{.ErrText}}.{{end -}}"}, "responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK", "SUPPORT"], "skillName": "DEBIT_CARD_DELIVERY_STATUS", "faqsConfig": {"enable": true, "searchTexts": ["delivery"]}, "debitCardDeliveryStatusParams": {}, "fiQuickLinkConfig": {"enable": true, "links": ["CARD_HOME_SCREEN"]}}, {"responseTypeOrder": ["QUICK_LINK"], "fiQuickLinkConfig": {"enable": true, "links": ["PROFILE_SETTINGS"]}, "skillName": "SETTINGS_INFO"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "CARD_OFFERS"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "JUMP"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "MUTUAL_FUNDS"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "TRANSACTION", "QUICK_LINK"], "fiSummaryConfig": {"enable": true}, "finActivityConfig": {"enable": true}, "fiQuickLinkConfig": {"enable": true, "links": ["MY_REWARDS_SCREEN"]}, "skillName": "SALARY_ACCOUNT"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "SMART_DEPOSIT"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "FIXED_DEPOSIT"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "INVESTMENT"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "DEPOSIT"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK"], "fiSummaryConfig": {"enable": true, "summaryText": "Currently, Fi does not allow in-app mobile recharging.\nBut, you can pay the recharge bill using our app!\nTry our super-fast UPI feature or scan & pay via QR code ⚡️"}, "fiQuickLinkConfig": {"enable": true, "links": ["PAY_QR_SCREEN"]}, "skillName": "RECHARGE"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK"], "fiSummaryConfig": {"enable": true}, "fiQuickLinkConfig": {"enable": true}, "skillName": "LOAN"}, {"responseTypeOrder": ["QUICK_LINK"], "fiSummaryConfig": {"enable": false}, "fiQuickLinkConfig": {"enable": true, "links": ["CREDIT_CARD_DASHBOARD_SCREEN"]}, "skillName": "CREDIT_CARD"}, {"responseTypeOrder": ["QUICK_LINK"], "fiSummaryConfig": {"enable": false}, "fiQuickLinkConfig": {"enable": true, "links": ["EPF_DASHBOARD"]}, "skillName": "EPF_STATIC"}, {"responseTypeOrder": ["QUICK_LINK"], "fiSummaryConfig": {"enable": false}, "fiQuickLinkConfig": {"enable": true, "links": ["NET_WORTH_HUB_SCREEN"]}, "skillName": "NETWORTH_STATIC"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK"], "fiSummaryConfig": {"enable": true, "summaryText": "To contact Fi Care:\n*Ping us via the in-app chat\n*Email us: <EMAIL>\n*Call us on: 080-********"}, "fiQuickLinkConfig": {"enable": true, "links": ["CHAT_WITH_US_SCREEN"]}, "skillName": "CUSTOMER_CARE_INFO"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK"], "fiSummaryConfig": {"enable": true, "summaryText": "{{if eq .<PERSON>yc<PERSON>tatus \"FULL_KYC\" }}You can now place a chequebook request/cancelled cheque on the Fi App.\n1. Charges for chequebook are 118 INR including GST\n2. Chequebook can be delivered only to your communication address.\n3. You can however download a cancelled cheque for free{{end -}}{{if eq .KycStatus \"MIN_KYC\"}}You can only get a chequebook if you've completed our full KYC procedure. Once done, contact Fi Care — via in-app chat or on 080-******** —  and place a chequebook request ✅{{end -}}"}, "fiQuickLinkConfig": {"enable": true}, "skillName": "CHEQUE_BOOK_INFO"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2"], "fiSummaryConfig": {"enable": true, "summaryText": "We're sorry to hear that you want to close your free account 😔\nHere's what you need to do:\nSend an email <NAME_EMAIL>\nUse 'Account Closure' in the subject line."}, "skillName": "CLOSE_ACCOUNT"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "SUPPORT"], "fiSummaryConfig": {"enable": true, "summaryText": "{{if eq .<PERSON><PERSON><PERSON><PERSON> \"FULL_KYC\" }}Good news! You are a fully-verified Fi user 😎\nSo, there is no need for you to complete a video KYC call.{{end -}}\n{{if eq .<PERSON>ycStatus \"MIN_KYC\"}}Your account is a minimum KYC account.\nComplete a 3 min Video KYC call to upgrade to a full Fi account.\nAfter getting a full account :\n- You get a Fi account with no deposit/transaction limits \n- Your savings account gains lifetime validity ✅️{{end -}}\n{{if eq .KycStatus \"\"}}We could not retrieve KYC detail at the moment. Please check again soon{{end -}}"}, "faqsConfig": {"enable": true, "searchTexts": ["kyc"]}, "skillName": "KYC_STATUS"}, {"responseTypeOrder": ["QUICK_LINK", "SUPPORT"], "fiQuickLinkConfig": {"enable": true, "links": ["PAY_QR_SCREEN", "PAY_VIA_UPI", "PAY_LANDING_SCREEN"]}, "faqsConfig": {"enable": true, "searchTexts": ["payments"]}, "skillName": "PAY"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK", "TRANSACTION"], "fiSummaryConfig": {"enable": true, "summaryText": "Your Fi Account number: {{ .AccNumber }}\nIFSC Code: {{.IfscCode }}\nBranch Name: {{.Branch }}"}, "fiQuickLinkConfig": {"enable": true, "links": ["QUICK_ACCOUNT_DETAILS_SCREEN"]}, "finActivityConfig": {"enable": true}, "faqsConfig": {}, "caFaqsConfig": {}, "skillName": "SAVINGS_ACCOUNT_INFO"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK", "TRANSACTION", "SUPPORT"], "fiSummaryConfig": {"enable": true, "summaryText": "Your UPI ID: {{ .UpiVpa }}"}, "fiQuickLinkConfig": {"enable": true, "links": ["PAY_QR_SCREEN", "PAY_VIA_UPI"]}, "faqsConfig": {"enable": true, "searchTexts": ["upi"]}, "finActivityConfig": {"enable": true}, "skillName": "UPI_ID"}, {"responseTypeOrder": ["QUICK_LINK", "TRANSACTION", "SUPPORT"], "fiQuickLinkConfig": {"enable": true, "links": ["CARD_HOME_SCREEN"]}, "finActivityConfig": {"enable": true}, "faqsConfig": {"enable": true, "searchTexts": ["debit card"]}, "skillName": "DEBIT_CARD_INFO"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK", "SUPPORT"], "fiSummaryConfig": {"enable": true, "summaryText": "Your UPI PIN can not be displayed here for security purposes. If you'd like to reset your PIN, click on the 'Reset UPI PIN' option."}, "fiQuickLinkConfig": {"enable": true, "links": ["UPI_PIN_SETUP", "FORGOT_PIN_SCREEN", "RESET_PIN_SCREEN"]}, "faqsConfig": {"enable": true, "searchTexts": ["upi forgot pin", "pin", "upi pin"]}, "skillName": "UPI_PIN_INFO"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK", "SUPPORT"], "fiSummaryConfig": {"enable": true, "summaryText": "Your Debit Card PIN can not be displayed here for security purposes. If you'd like to reset your PIN, click on the 'Card Setting' option."}, "fiQuickLinkConfig": {"enable": true, "links": ["CARD_SETTINGS_SCREEN"]}, "faqsConfig": {"enable": true, "searchTexts": ["card forgot pin", "pin", "debit card"]}, "skillName": "DEBIT_CARD_PIN_INFO"}, {"responseTypeOrder": ["QUICK_LINK", "TRANSACTION", "SUPPORT"], "fiQuickLinkConfig": {"enable": true, "links": ["STATEMENT_REQUEST_SCREEN"]}, "finActivityConfig": {"enable": true}, "faqsConfig": {"enable": true, "searchTexts": ["statement", "account statement", "savings account"]}, "skillName": "STATEMENT_INFO"}, {"responseTypeOrder": ["QUICK_LINK", "SUPPORT"], "fiQuickLinkConfig": {"enable": true, "links": ["DEPOSIT_OPEN_ACCOUNT"]}, "faqsConfig": {"enable": true, "searchTexts": ["open deposit", "deposit"]}, "skillName": "ADD_DEPOSIT"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK"], "fiSummaryConfig": {"enable": true, "summaryText": "Fi doesn't support Netbanking as of now. Alternatively, you can tap on the 'Pay' option below to make bank transfers. \n\nIf you still want to use Netbanking, use Federal Bank's Netbanking facility."}, "fiQuickLinkConfig": {"enable": true, "links": ["PAY_VIA_BANK_TRANSFER"]}, "skillName": "NET_BANKING"}, {"responseTypeOrder": ["QUICK_LINK"], "fiQuickLinkConfig": {"enable": true, "links": ["PAY_QR_SCREEN"]}, "skillName": "SCAN_QR"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK"], "fiSummaryConfig": {"enable": true, "summaryText": "Your Federal Bank Savings Account on Fi does not come with a passbook. But, don't worry! With Smart Statements, we'll send you a free snapshot of your account.\n \nTap the quick link below & get your 'Fi Account Statement'"}, "fiQuickLinkConfig": {"enable": true, "links": ["STATEMENT_REQUEST_SCREEN"]}, "skillName": "PASSBOOK_INFO"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2", "QUICK_LINK"], "fiSummaryConfig": {"enable": true, "summaryText": "Special discounts on Myntra, Spotify, Amazon and other top brands 🛍 Tap on 'Card Offers' below for more!"}, "fiQuickLinkConfig": {"enable": true, "links": ["OFFERS_LANDING_SCREEN", "DEBIT_CARD_OFFERS_HOME_SCREEN", "REDEEMED_OFFERS_SCREEN"]}, "skillName": "OFFERS_INFO"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "FINANCIAL_ABUSE"}, {"responseTypeOrder": ["SUMMARY", "SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "GPT"}, {"responseTypeOrder": ["SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "CONNECTED_ACCOUNT"}, {"responseTypeOrder": ["SUMMARY_V2"], "fiSummaryConfig": {"enable": true}, "skillName": "CREDIT_SCORE"}]}