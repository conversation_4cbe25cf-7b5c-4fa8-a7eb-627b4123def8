import json, argparse, logging

logging.basicConfig(level=logging.INFO)


def extract_data(path):
	data = []
	with open(path) as f:
		for line in f:
			tmp = json.loads(line)
			data.append(tmp["_source"])
	logging.info("len of data %s", len(data))
	return data


def write_to_file(path, data, index_name):
	t = {index_name: data}
	with open(path, "w") as f:
		f.write(json.dumps(t))


if __name__ == "__main__":
	parser = argparse.ArgumentParser()
	parser.add_argument('--input', help="input-path for index-dump file")
	parser.add_argument('--output', help="output-path for extracted data")
	parser.add_argument('--index', help="name of the index")

	args = parser.parse_args()
	# logging.info("input = %s, output = %s, index = %s", (args.input, args.output, args.index))
	data = extract_data(args.input)
	write_to_file(args.output, data, args.index)
