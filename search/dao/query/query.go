package query

/*
all queries are converted to struct from `json to golang struct`
*/

// filter query for es
type FilteredQueryTxnPage struct {
	Query struct {
		Bool struct {
			Filter []TermQuery `json:"filter"`
		} `json:"bool"`
	} `json:"query"`
}

/*
	currently used facet query for txn page

it contains 3 aggregations

	 -- user_id_bucket
		-- filters : when a option is selected by user, filters are applied
		-- business : this is master aggregation to show `PEOPLE` and `MERCHANT` facets
			-- user
			-- merchant
		-- tags : this aggregates all tags

** when filters are applied, aggregations are done on top of the filtered set
*/
type FacetedQueryTxnPage struct {
	Aggs struct {
		UserIDBucket struct {
			Filter struct {
				Bool struct {
					Must []TermQuery `json:"must,omitempty"`
				} `json:"bool,omitempty"`
			} `json:"filter,omitempty"`
			Aggs struct {
				Tags struct {
					Terms struct {
						Field string `json:"field"`
					} `json:"terms"`
				} `json:"tags"`
				Business struct {
					Terms struct {
						Field string `json:"field"`
					} `json:"terms"`
					Aggs struct {
						Names struct {
							Terms struct {
								Field string `json:"field"`
							} `json:"terms"`
						} `json:"names"`
					} `json:"aggs"`
				} `json:"business"`
			} `json:"aggs"`
		} `json:"user_id_bucket"`
	} `json:"aggs"`
}

/*
Ifsc suggestion query
-- It has 2 suggests, ifsc_fuzzy_suggest, ifsc_suggest
-- if ifsc_suggest length is < 5, it will append suggestions from ifsc_fuzzy_suggest
*/
type IfscSuggQuery struct {
	Suggest struct {
		IfscFuzzySuggest struct {
			Prefix     string `json:"prefix"`
			Completion struct {
				Field          string `json:"field"`
				SkipDuplicates bool   `json:"skip_duplicates"`
				Size           int    `json:"size"`
				Fuzzy          struct {
					Fuzziness    int `json:"fuzziness"`
					PrefixLength int `json:"prefix_length"`
				} `json:"fuzzy"`
				Contexts struct {
					FieldType []string `json:"field_type"`
				} `json:"contexts"`
			} `json:"completion"`
		} `json:"ifsc-fuzzy-suggest"`
		IfscSuggest struct {
			Prefix     string `json:"prefix"`
			Completion struct {
				Field          string `json:"field"`
				SkipDuplicates bool   `json:"skip_duplicates"`
				Size           int    `json:"size"`
				Contexts       struct {
					FieldType []string `json:"field_type"`
				} `json:"contexts"`
			} `json:"completion"`
		} `json:"ifsc-suggest"`
	} `json:"suggest"`
	Source []string `json:"_source"`
}

/*
company name suggestion query
-- It has 2 suggests, ifsc_fuzzy_suggest, ifsc_suggest
-- if ifsc_suggest length is < 5, it will append suggestions from ifsc_fuzzy_suggest
*/
type CompanyNameSuggQuery struct {
	Suggest struct {
		CompanyNameFuzzySuggest struct {
			Prefix     string `json:"prefix"`
			Completion struct {
				Field          string `json:"field"`
				SkipDuplicates bool   `json:"skip_duplicates"`
				Size           int    `json:"size"`
				Fuzzy          struct {
					Fuzziness    int `json:"fuzziness"`
					PrefixLength int `json:"prefix_length"`
				} `json:"fuzzy"`
				Contexts struct {
					FieldType []string `json:"field_type"`
				} `json:"contexts"`
			} `json:"completion"`
		} `json:"company-name-fuzzy-suggest"`
		CompanyNameSuggest struct {
			Prefix     string `json:"prefix"`
			Completion struct {
				Field          string `json:"field"`
				SkipDuplicates bool   `json:"skip_duplicates"`
				Size           int    `json:"size"`
				Contexts       struct {
					FieldType []string `json:"field_type"`
				} `json:"contexts"`
			} `json:"completion"`
		} `json:"company-name-suggest"`
	} `json:"suggest"`
	Source []string `json:"_source"`
}

type FilterQuery struct {
	Query struct {
		Bool struct {
			Filter []TermQuery `json:"filter"`
		} `json:"bool"`
	} `json:"query"`
	Sort []interface{} `json:"sort"`
}

type UsersSearchQuery struct {
	Query struct {
		Match struct {
			Name string `json:"name"`
		} `json:"match"`
	} `json:"query"`
	Size int `json:"size"`
}

type UserPastTxnQuery struct {
	FilterQuery
	Sort []struct {
		UpdatedAt struct {
			Order string `json:"order,omitempty"`
		} `json:"updated_at,omitempty"`
	} `json:"sort,omitempty"`
}

type ConnectionsSearchQuery struct {
	Query struct {
		Match struct {
			SecondaryActorName string `json:"secondary_actor_name"`
		} `json:"match"`
	} `json:"query"`
	Size int `json:"size"`
}

// TODO(shubhra): build this query from elastic lib
type TxnAggQuery struct {
	Query interface{} `json:"query"`
	Aggs  struct {
		Money interface{} `json:"money,omitempty"`
		Units interface{} `json:"units,omitempty"`
		Nano  interface{} `json:"nano,omitempty"`
	} `json:"aggs"`
	Size uint64 `json:"size"`
}

/*
EmployerSuggQuery Employer name suggestion query
-- It has 2 suggests, employer_fuzzy_suggest, employer_suggest
-- if employer_suggest length is < 5, it will append suggestions from employer_fuzzy_suggest
*/
type EmployerSuggQuery struct {
	Suggest struct {
		EmployerNameFuzzySuggest struct {
			Prefix     string `json:"prefix"`
			Completion struct {
				Field          string `json:"field"`
				SkipDuplicates bool   `json:"skip_duplicates"`
				Size           int    `json:"size"`
				Fuzzy          struct {
					Fuzziness    int `json:"fuzziness"`
					PrefixLength int `json:"prefix_length"`
				} `json:"fuzzy"`
				Contexts struct {
					SourceAndConstitution []string `json:"source_and_constitution"`
				} `json:"contexts"`
			} `json:"completion"`
		} `json:"employer-name-fuzzy-suggest"`
		EmployerNameSuggest struct {
			Prefix     string `json:"prefix"`
			Completion struct {
				Field          string `json:"field"`
				SkipDuplicates bool   `json:"skip_duplicates"`
				Size           int    `json:"size"`
				Contexts       struct {
					SourceAndConstitution []string `json:"source_and_constitution"`
				} `json:"contexts"`
			} `json:"completion"`
		} `json:"employer-name-suggest"`
	} `json:"suggest"`
	Source []string `json:"_source"`
}
