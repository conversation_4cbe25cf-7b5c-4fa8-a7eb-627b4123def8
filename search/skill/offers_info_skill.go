// nolint:dupl
package skill

import (
	"context"
	"fmt"

	searchWidgetPb "github.com/epifi/gamma/api/frontend/search/widget"
	summaryPb "github.com/epifi/gamma/api/search/summary"
	"github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/search/constant"
	"github.com/epifi/gamma/search/entity"

	searchPb "github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/search/actionbar"
	skillPb "github.com/epifi/gamma/api/search/skill"
	"github.com/epifi/gamma/search/common"
)

type OffersInfoSkill struct {
	configFetcher      SkillConfigGet
	searchResultConfig *skillPb.SearchResultConfig
}

func (o *OffersInfoSkill) AddConfigValues(ctx context.Context, config Config) error {
	var currSkillConfig *skillPb.SearchResultConfig
	var err error
	// fetch the config for skill
	if currSkillConfig, err = o.configFetcher.FetchSkillConfig(ctx, config.SkillName); err != nil {
		return err
	}
	o.searchResultConfig = currSkillConfig
	return nil
}

func (o *OffersInfoSkill) AddEntityData(ctx context.Context) {
}

func NewOffersInfoSkill(configFetcher SkillConfigGet) *OffersInfoSkill {
	return &OffersInfoSkill{
		configFetcher: configFetcher,
	}
}

//nolint:dupl
func (o *OffersInfoSkill) BuildFiLiteSearchResult(ctx context.Context) ([]*searchPb.SearchResultUnit, *entity.CurrActorSearchCtxImpl) {
	var summaryV2Row *summaryPb.SummaryV2

	fiLiteSummaryText := fmt.Sprintf(constant.FiLiteFeatureNotAvailableTemplate, "offers")

	// open savings account deeplink
	savingsAccountDeeplink, err := onboarding.GetSABenefitsScreen(ctx)
	if err != nil {
		summaryV2Row = common.GetSummaryV2FromText(fiLiteSummaryText, constant.FiTabName)
	} else {
		deeplinkElementList := []*searchWidgetPb.DeepLinkElement{
			{
				Link:     savingsAccountDeeplink,
				Text:     constant.AccountInfoOpenSavingsAccountCtaText,
				CtaTheme: searchWidgetPb.CtaDisplayTheme_PRIMARY,
				CtaType:  searchWidgetPb.CTAType_DEEPLINK,
			},
		}
		summaryV2Row = common.GetSummaryV2FromTextAndDeeplink(fiLiteSummaryText, constant.FiTabName, deeplinkElementList)
	}
	return []*searchPb.SearchResultUnit{common.GetSearchResultUnitFromSummaryV2(summaryV2Row, constant.FiTabName)}, nil
}

func (o *OffersInfoSkill) BuildSearchResult(ctx context.Context) ([]*searchPb.SearchResultUnit, *entity.CurrActorSearchCtxImpl) {
	return common.ReorderSearchResultUnits(o.searchResultConfig.GetResponseTypeOrder(), o.buildSearchResultForOffersInfo()), nil
}

func (o *OffersInfoSkill) GetFinancialActivity(ctx context.Context, getFinancialActivityRequest *GetFinancialActivityRequest) (*actionbar.QuickInfoResponse, error) {
	return nil, nil
}

func (o *OffersInfoSkill) buildSearchResultForOffersInfo() []*searchPb.SearchResultUnit {
	var searchResultUnits []*searchPb.SearchResultUnit
	if o.searchResultConfig.GetFiSummaryConfig().GetEnable() {
		searchResultUnits = append(searchResultUnits, o.getOffersInfoSummary())
	}
	if o.searchResultConfig.GetFiQuickLinkConfig().GetEnable() {
		qlUnit := getQuickLinks(o.searchResultConfig.GetFiQuickLinkConfig().GetLinks())
		if qlUnit != nil {
			searchResultUnits = append(searchResultUnits, qlUnit)
		}
	}
	return searchResultUnits
}

func (o *OffersInfoSkill) getOffersInfoSummary() *searchPb.SearchResultUnit {
	return common.GetSearchResultUnitFromSummary(&actionbar.SummaryResponse{
		Summary: o.searchResultConfig.GetFiSummaryConfig().GetSummaryText(),
		TabName: constant.FiTabName,
	}, constant.FiTabName)
}
