package product

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"k8s.io/apimachinery/pkg/util/waitgroup"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/product/productproc"
)

func TestService_GetProductsStatus(t *testing.T) {
	var (
		actorIdFixture = "actor-id"
		reqFixture     = func(productType []product.ProductType) *product.GetProductsStatusRequest {
			return &product.GetProductsStatusRequest{
				ActorId:      actorIdFixture,
				ProductTypes: productType,
			}
		}
	)
	tests := map[string]struct {
		req      *product.GetProductsStatusRequest
		want     *product.GetProductsStatusResponse
		wantErr  error
		mockFunc func(*mockDependencies, *waitgroup.SafeWaitGroup)
	}{
		"happy case with SA": {
			req: reqFixture([]product.ProductType{product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT}),
			want: &product.GetProductsStatusResponse{
				Status: rpc.StatusOk(),
				ProductInfoMap: map[string]*product.ProductInfo{
					product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT.String(): {
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				},
			},
			mockFunc: func(deps *mockDependencies, wg *waitgroup.SafeWaitGroup) {
				wg.Add(1)
				deps.productProc.EXPECT().GetProductStatus(gomock.Any(), &productproc.GetProductStatusRequest{
					ActorId:     actorIdFixture,
					ProductType: product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				}).Return(&productproc.GetProductStatusResponse{
					ProductInfo: &product.ProductInfo{
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				}, nil).Do(func(arg0, arg1 interface{}) {
					wg.Done()
				})
			},
		},
		"happy case with SA, CC": {
			req: reqFixture([]product.ProductType{product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT, product.ProductType_PRODUCT_TYPE_CREDIT_CARD}),
			want: &product.GetProductsStatusResponse{
				Status: rpc.StatusOk(),
				ProductInfoMap: map[string]*product.ProductInfo{
					product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT.String(): {
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
					product.ProductType_PRODUCT_TYPE_CREDIT_CARD.String(): {
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				},
			},
			mockFunc: func(deps *mockDependencies, wg *waitgroup.SafeWaitGroup) {
				wg.Add(2)
				deps.productProc.EXPECT().GetProductStatus(gomock.Any(), &productproc.GetProductStatusRequest{
					ActorId:     actorIdFixture,
					ProductType: product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				}).Return(&productproc.GetProductStatusResponse{
					ProductInfo: &product.ProductInfo{
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				}, nil).Do(func(arg0, arg1 interface{}) {
					wg.Done()
				})
				deps.productProc.EXPECT().GetProductStatus(gomock.Any(), &productproc.GetProductStatusRequest{
					ActorId:     actorIdFixture,
					ProductType: product.ProductType_PRODUCT_TYPE_CREDIT_CARD,
				}).Return(&productproc.GetProductStatusResponse{
					ProductInfo: &product.ProductInfo{
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				}, nil).Do(func(arg0, arg1 interface{}) {
					wg.Done()
				})
			},
		},
		"happy case with SA, CC, PL": {
			req: reqFixture([]product.ProductType{product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT, product.ProductType_PRODUCT_TYPE_CREDIT_CARD,
				product.ProductType_PRODUCT_TYPE_PERSONAL_LOANS}),
			want: &product.GetProductsStatusResponse{
				Status: rpc.StatusOk(),
				ProductInfoMap: map[string]*product.ProductInfo{
					product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT.String(): {
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
					product.ProductType_PRODUCT_TYPE_CREDIT_CARD.String(): {
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
					product.ProductType_PRODUCT_TYPE_PERSONAL_LOANS.String(): {
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				},
			},
			mockFunc: func(deps *mockDependencies, wg *waitgroup.SafeWaitGroup) {
				wg.Add(3)
				deps.productProc.EXPECT().GetProductStatus(gomock.Any(), &productproc.GetProductStatusRequest{
					ActorId:     actorIdFixture,
					ProductType: product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				}).Return(&productproc.GetProductStatusResponse{
					ProductInfo: &product.ProductInfo{
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				}, nil).Do(func(arg0, arg1 interface{}) {
					wg.Done()
				})
				deps.productProc.EXPECT().GetProductStatus(gomock.Any(), &productproc.GetProductStatusRequest{
					ActorId:     actorIdFixture,
					ProductType: product.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
				}).Return(&productproc.GetProductStatusResponse{
					ProductInfo: &product.ProductInfo{
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				}, nil).Do(func(arg0, arg1 interface{}) {
					wg.Done()
				})
				deps.productProc.EXPECT().GetProductStatus(gomock.Any(), &productproc.GetProductStatusRequest{
					ActorId:     actorIdFixture,
					ProductType: product.ProductType_PRODUCT_TYPE_CREDIT_CARD,
				}).Return(&productproc.GetProductStatusResponse{
					ProductInfo: &product.ProductInfo{
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				}, nil).Do(func(arg0, arg1 interface{}) {
					wg.Done()
				})
			},
		},
		"happy case with SA, CC & Failure PL": {
			req: reqFixture([]product.ProductType{product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT, product.ProductType_PRODUCT_TYPE_CREDIT_CARD,
				product.ProductType_PRODUCT_TYPE_PERSONAL_LOANS}),
			want: &product.GetProductsStatusResponse{
				Status: rpc.StatusOk(),
				ProductInfoMap: map[string]*product.ProductInfo{
					product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT.String(): {
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
					product.ProductType_PRODUCT_TYPE_CREDIT_CARD.String(): {
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				},
			},
			mockFunc: func(deps *mockDependencies, wg *waitgroup.SafeWaitGroup) {
				wg.Add(3)
				deps.productProc.EXPECT().GetProductStatus(gomock.Any(), &productproc.GetProductStatusRequest{
					ActorId:     actorIdFixture,
					ProductType: product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				}).Return(&productproc.GetProductStatusResponse{
					ProductInfo: &product.ProductInfo{
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				}, nil).Do(func(arg0, arg1 interface{}) {
					wg.Done()
				})
				deps.productProc.EXPECT().GetProductStatus(gomock.Any(), &productproc.GetProductStatusRequest{
					ActorId:     actorIdFixture,
					ProductType: product.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
				}).Return(nil, epifierrors.ErrPermissionDenied).Do(func(arg0, arg1 interface{}) {
					wg.Done()
				})
				deps.productProc.EXPECT().GetProductStatus(gomock.Any(), &productproc.GetProductStatusRequest{
					ActorId:     actorIdFixture,
					ProductType: product.ProductType_PRODUCT_TYPE_CREDIT_CARD,
				}).Return(&productproc.GetProductStatusResponse{
					ProductInfo: &product.ProductInfo{
						ProductStatus: product.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS,
					},
				}, nil).Do(func(arg0, arg1 interface{}) {
					wg.Done()
				})
			},
		},
	}
	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			s, md := setupServiceWithMocks(t)

			wg := &waitgroup.SafeWaitGroup{}
			if tt.mockFunc != nil {
				tt.mockFunc(md, wg)
			}
			got, err := s.GetProductsStatus(context.Background(), tt.req)
			wg.Wait()
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Unexpected error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetProductsStatus value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
