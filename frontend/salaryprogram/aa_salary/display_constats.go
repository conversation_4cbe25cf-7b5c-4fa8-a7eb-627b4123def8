package aa_salary

const (
	Retry = "Retry"
	Close = "Close"
)

const (
	rupeeSymbolGreen2   = "https://epifi-icons.pointz.in/aa-salary/transfer-funds-rupee-symbol-green-2.png"
	editIconGreen       = "https://epifi-icons.pointz.in/aa-salary/edit-icon-green.png"
	benefitsCash        = "https://epifi-icons.pointz.in/aa-salary/benefits-cash.png"
	leftChevron         = "https://epifi-icons.pointz.in/aa-salary/chevron-left.png"
	infoIcon            = "https://epifi-icons.pointz.in/aa-salary/info-icon.png"
	popupInfoIcon       = "https://epifi-icons.pointz.in/tiering/earned-benefits/popup-info-icon-green.png"
	googlePayIcon       = "https://epifi-icons.pointz.in/aa-salary/google-pay-icon.png"
	phonePeIcon         = "https://epifi-icons.pointz.in/aa-salary/pnonepe-icon.png"
	paytmIcon           = "https://epifi-icons.pointz.in/aa-salary/paytm-icon.png"
	whatsappIcon        = "https://epifi-icons.pointz.in/aa-salary/whatsapp-icon.png"
	copyIconGreen       = "https://epifi-icons.pointz.in/aa-salary/copy-icon-green.png"
	recentTimeIcon      = "https://epifi-icons.pointz.in/aa-salary/recent-time.png"
	lockIcon            = "https://epifi-icons.pointz.in/aa-salary/lock-icon.png"
	tickIcon            = "https://epifi-icons.pointz.in/tiering/aasalary/transfer_success.png"
	serialNo1           = "https://epifi-icons.pointz.in/aa-salary/serial-no-1.png"
	serialNo2           = "https://epifi-icons.pointz.in/aa-salary/serial-no-2.png"
	serialNo3           = "https://epifi-icons.pointz.in/aa-salary/serial-no-3.png"
	ThunderCloud        = "https://epifi-icons.pointz.in/aa-salary/thunder-cloud-transparent-bg.png"
	primeIcon           = "https://epifi-icons.pointz.in/aa-salary/prime_128_88.png"
	noAutoDebit         = "https://epifi-icons.pointz.in/aa-salary/no_auto_debit.png"
	federalBankBlue6416 = "https://epifi-icons.pointz.in/federal_bank_blue_64_16.png"
	rupeeSymbolGreen    = "https://epifi-icons.pointz.in/aa-salary/transfer-funds-rupee-symbol-green.png"
	benefitsBank        = "https://epifi-icons.pointz.in/aa-salary/benefits-bank.png"
	benefitsBank2       = "https://epifi-icons.pointz.in/aa-salary/benefits-bank-2.png"
	redWarningIcon      = "https://epifi-icons.pointz.in/aa-salary/red-warning-icon.png"
)

const (
	transferSetupScreenTitle    = "Add money to earn %s back"
	transferSetupSwipeCtaText   = "Add money"
	amountSelectionTitle        = "Amount to add"
	rupeeSymbolText             = "₹"
	sliderTagText               = "%s back"
	youEarnBack                 = "You earn <span style=\"color:#AFD2A2\">%s</span> back"
	benefit1Text                = "Rewards on any purchase"
	benefit2Text                = "No auto- debits from your a/c"
	minTransferAmountErr        = "Please add an amount over %s"
	maxTransferAmountErr        = "Please add an amount below %s"
	transferSetupInfoPopupTitle = "How you can earn salary benefits"
	transferSetupInfoPopupBody  = "Transfer a part of your money every month from your existing salary account to this account to earn salary benefits every month"
	poweredBy                   = "POWERED BY"
	youEarn                     = "You earn "
	backText                    = " back"
)

const (
	offAppTransferTitle   = "Transfer %s to your Federal Bank Savings Account on Fi"
	infoComponent1Title   = "Go to your preferred UPI app"
	infoComponent2Title   = "Transfer the amount to this UPI ID"
	infoComponent3Title   = "Once your payment is complete, wait a few minutes and open the Fi app. "
	recommendedApps       = "RECOMMENDED APPS"
	offAppTransferCtaText = "Ok, got it"
)

const (
	longerThanExpectedTitle = "It's taking a little longer than expected"
	longerThanExpectedDesc  = "When there's an update — we'll notify you!"
	okGotIt                 = "Ok, got it"
)

const (
	notEligibleForCashbackTitle   = "It appears that you don't qualify for 3% back"
	notEligibleForCashbackDesc    = "3% back offer is only open to those with a salary range over ₹75K. \n\nWe hope to provide this to all. If you're interested, tap the button below."
	notEligibleForCashbackCtaText = "Interested in 3% back"
)

const (
	calculatingBenefitsLoaderText = "Calculating your benefits"
)

const (
	notedYourInterestTitle   = "We've noted your interest"
	notedYourInterestDesc    = "We’ll work on making this feature available to you. In the meantime, explore other plans that could earn you more rewards"
	notedYourInterestCtaText = "Go back home"
)

const (
	ErrScreenTitle = "Something went wrong"
	ErrScreenDesc  = "We were unable to load this page.\nPlease try again."
)

const (
	cashbackActivatedTitle   = "%s back activated"
	cashbackActivatedDesc    = "You will now earn %s back for all UPI or Debit Card spends via Fi"
	cashbackActivatedCtaText = "See overall benefits"
)

const (
	aaSalaryImportDataSuccessScreenCta            = "View benefits"
	aaSalaryConnectAccountSuccessScreenHeading    = "Salary account successfully verified"
	aaSalaryConnectAccountSuccessScreenSubHeading = "Next, you’ll be able to view your eligible benefits"
	connectedAccount3dSuccessScreenIcon           = "https://epifi-icons.pointz.in/connectedaccounts/3d_tick.png"
	statementProgressTitle                        = "Fetching 1 year statement from "
	statementProgressSubTitle                     = "Downloading your documents"
	statementProgressIcon                         = "https://epifi-icons.pointz.in/aasalary/3d_statement.png"
	statementErrorTitle                           = "Couldn’t fetch your account statement"
	statementErrorSubTitle                        = "Please try again."
	statementErrorIcon                            = "https://epifi-icons.pointz.in/aasalary/statement_error.png"
	sourceOfSalaryScreenTitle                     = "Select your salary account"
	sourceOfSalaryScreenSubTitle                  = "This is required to verify your salary status and calculate your benefits."
	connectAccountTitleText                       = "Choose salary account to auto-fetch bank statement"
	connectSalaryAccountText                      = "Connect your salary bank account"
	poweredByEpifiWealthPng                       = "https://epifi-icons.pointz.in/aasalary/powered_by_epifi_wealth.png"
	plusIconPng                                   = "https://epifi-icons.pointz.in/aasalary/plus_icon.png"
	retryText                                     = "Retry"
	proceedText                                   = "Proceed"
	checkboxWealthTermsText                       = "I agree to download my decrypted account statement from Epifi Wealth and share it with Epifi Tech for the purposes of accessing services on Fi."
	comingSoonText                                = "COMING SOON"
	uploadItr                                     = "Upload your ITR to calculate benefits"
	closeText                                     = "Close"
)

const (
	threePercentsCashbackLottie = "https://epifi-icons.pointz.in/aasalary/3x_cashback.json"
)
