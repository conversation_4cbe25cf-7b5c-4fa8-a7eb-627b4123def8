package secrets

import (
	"github.com/epifi/be-common/pkg/datetime"
	actorPb "github.com/epifi/gamma/api/actor"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	insightsDeeplinkPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	portfolioTrackerBuilder "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder"
	assetWiseDistributionBuilder "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/portfolio_tracker_ui/asset_wise_distribution_builder"
	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	investmentAnalyserPb "github.com/epifi/gamma/api/analyser/investment"
	"github.com/epifi/gamma/api/connected_account"
	credit_report "github.com/epifi/gamma/api/creditreportv2"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	epfPb "github.com/epifi/gamma/api/insights/epf"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	nudgePb "github.com/epifi/gamma/api/nudge"
	analyserFilter "github.com/epifi/gamma/frontend/analyser/processor/filter"
	collectionPrcoessor "github.com/epifi/gamma/frontend/insights/secrets/collections/processor"
	"github.com/epifi/gamma/frontend/insights/secrets/config"
	secretBuilderFactory "github.com/epifi/gamma/frontend/insights/secrets/secret_builder/factory"
	"github.com/epifi/gamma/frontend/insights/secrets/secret_builder/secret_provider"
	wealthAnalyserFe "github.com/epifi/gamma/frontend/insights/secrets/wealth_analyser_report"
	"github.com/epifi/gamma/pkg/feature/release"
)

type Service struct {
	config               *config.Config
	secretBuilderFactory secretBuilderFactory.ISecretBuilderFactory
	filterValueGenerator analyserFilter.FilterValueGenerator
	wealthAnalyserReport wealthAnalyserFe.IWealthAnalyserReport
	secretsFePb.UnimplementedSecretsServer
	epfClient                    epfPb.EpfClient
	networthClient               networthBePb.NetWorthClient
	creditReportManagerClient    credit_report.CreditReportManagerClient
	connectedAccountClient       connected_account.ConnectedAccountClient
	releaseEvaluator             release.IEvaluator
	investAnalyticsClient        investmentAnalyserPb.InvestmentAnalyticsClient
	nudgeClient                  nudgePb.NudgeServiceClient
	timeImpl                     datetime.Time
	collectionProcessorFactory   collectionPrcoessor.ICollectionProcessor
	secretProvider               secret_provider.ISecretProvider
	onboardingClient             onbPb.OnboardingClient
	userGroupClient              userGroupPb.GroupClient
	actorClient                  actorPb.ActorClient
	deeplinkBuilder              insightsDeeplinkPb.IDeeplinkBuilder
	variableGeneratorClient      analyserVariablePb.VariableGeneratorClient
	portfolioTrackerBuilder      portfolioTrackerBuilder.IPortfolioTrackerBuilder
	assetWiseDistributionFactory assetWiseDistributionBuilder.IAssetWiseDistributionFactory
	dailyTracker                 *strategy.DailyTracker
	weeklyTracker                *strategy.WeeklyTracker
}

func NewService(
	config *config.Config,
	secretBuilderFactory secretBuilderFactory.ISecretBuilderFactory,
	filterValueGenerator analyserFilter.FilterValueGenerator,
	wealthAnalyserReport wealthAnalyserFe.IWealthAnalyserReport,
	epfClient epfPb.EpfClient,
	networthClient networthBePb.NetWorthClient,
	creditReportManagerClient credit_report.CreditReportManagerClient,
	connectedAccountClient connected_account.ConnectedAccountClient,
	releaseEvaluator release.IEvaluator,
	investAnalyticsClient investmentAnalyserPb.InvestmentAnalyticsClient,
	nudgeClient nudgePb.NudgeServiceClient,
	timeImpl datetime.Time,
	collectionProcessorFactory collectionPrcoessor.ICollectionProcessor,
	secretProvider secret_provider.ISecretProvider,
	onboardingClient onbPb.OnboardingClient,
	userGroupClient userGroupPb.GroupClient,
	actorClient actorPb.ActorClient,
	deeplinkBuilder insightsDeeplinkPb.IDeeplinkBuilder,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
	portfolioTrackerBuilder portfolioTrackerBuilder.IPortfolioTrackerBuilder,
	assetWiseDistributionFactory assetWiseDistributionBuilder.IAssetWiseDistributionFactory,
	dailyTracker *strategy.DailyTracker,
	weeklyTracker *strategy.WeeklyTracker,
) *Service {
	return &Service{
		config:                       config,
		secretBuilderFactory:         secretBuilderFactory,
		filterValueGenerator:         filterValueGenerator,
		wealthAnalyserReport:         wealthAnalyserReport,
		epfClient:                    epfClient,
		networthClient:               networthClient,
		creditReportManagerClient:    creditReportManagerClient,
		connectedAccountClient:       connectedAccountClient,
		releaseEvaluator:             releaseEvaluator,
		investAnalyticsClient:        investAnalyticsClient,
		nudgeClient:                  nudgeClient,
		timeImpl:                     timeImpl,
		collectionProcessorFactory:   collectionProcessorFactory,
		secretProvider:               secretProvider,
		onboardingClient:             onboardingClient,
		userGroupClient:              userGroupClient,
		actorClient:                  actorClient,
		deeplinkBuilder:              deeplinkBuilder,
		variableGeneratorClient:      variableGeneratorClient,
		portfolioTrackerBuilder:      portfolioTrackerBuilder,
		assetWiseDistributionFactory: assetWiseDistributionFactory,
		dailyTracker:                 dailyTracker,
		weeklyTracker:                weeklyTracker,
	}
}
