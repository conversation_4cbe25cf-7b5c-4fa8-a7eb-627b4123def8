package secrets

import (
	"context"
	"fmt"

	strategyImpl "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	goUtils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/logger"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"

	headerPb "github.com/epifi/gamma/api/frontend/header"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	"github.com/epifi/gamma/api/insights/networth"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"
)

func (s *Service) GetPortfolioTrackerAssetDetailsPage(ctx context.Context, req *secretsFePb.GetPortfolioTrackerAssetDetailsPageRequest) (*secretsFePb.GetPortfolioTrackerAssetDetailsPageResponse, error) {
	errRes := func(rpcStatus *rpcPb.Status, msg string) (*secretsFePb.GetPortfolioTrackerAssetDetailsPageResponse, error) {
		rpcStatus.SetDebugMessage(msg)
		return &secretsFePb.GetPortfolioTrackerAssetDetailsPageResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcStatus,
			},
		}, nil
	}

	actorId := req.GetReq().GetAuth().GetActorId()
	assetType := goUtils.Enum(req.GetRequestParams().GetAssetType(), networth.AssetType_value, networth.AssetType_ASSET_TYPE_UNSPECIFIED)

	assetDistributionBuilder, err := s.assetWiseDistributionFactory.GetAssetWiseDistributionBuilder(assetType)
	if err != nil {
		logger.Error(ctx, "failed to get asset wise distribution builder", zap.Any(logger.ASSET_TYPE, assetType), zap.Error(err))
		return errRes(rpcPb.StatusInternal(), err.Error())
	}

	// TODO(amrit): Will enable this only for Sunday but as of now we are enabling it for all days to test
	weeklyReportEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_WEEKLY_PORTFOLIO_TRACKER).WithActorId(actorId))
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate release config for %v: %w", types.Feature_FEATURE_WEEKLY_PORTFOLIO_TRACKER, err)
	}

	// Select strategy based on isWeeklyReport flag
	var strategy strategyImpl.PortfolioTrackerStrategy
	strategy = s.dailyTracker
	if weeklyReportEnabled {
		strategy = s.weeklyTracker
	}

	analysisVariables := strategy.GetComponentToVariableMap()[secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_ASSET_DISTRIBUTION]
	getAnalysisVariableResp, varErr := s.variableGeneratorClient.GetAnalysisVariables(ctx, &analyserVariablePb.GetAnalysisVariablesRequest{
		ActorId:               actorId,
		AnalysisVariableNames: analysisVariables,
	})
	if varErr != nil {
		logger.Error(ctx, "failed to get analysis variables", zap.Error(varErr))
		return errRes(rpcPb.StatusInternal(), varErr.Error())
	}

	assetWiseDistribution, assetWiseDistErr := assetDistributionBuilder.BuildAssetWiseDistribution(ctx, &strategyImpl.BuildAssetWiseDistributionRequest{
		ActorId:             actorId,
		AssetType:           assetType,
		AnalysisVariableMap: getAnalysisVariableResp.GetVariableEnumMap(),
		TrackingStrategy:    strategy,
	})
	if assetWiseDistErr != nil {
		logger.Error(ctx, "failed to build asset wise distribution for asset", zap.Any(logger.ASSET_TYPE, assetType), zap.Error(assetWiseDistErr))
		return errRes(rpcPb.StatusInternal(), assetWiseDistErr.Error())
	}

	return &secretsFePb.GetPortfolioTrackerAssetDetailsPageResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		AssetDetails: &secretsFePb.PortfolioTrackerAssetDetails{
			// TODO: change this title according to other asset types
			Title:            commontypes.GetTextFromStringFontColourFontStyle("Mutual Fund distribution", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
			AssetDetailsCard: assetWiseDistribution.PortfolioTrackerAssetDetailsCard,
		},
	}, nil
}
