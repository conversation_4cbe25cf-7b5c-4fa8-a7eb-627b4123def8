package portfoliotrackerbuilder

import (
	"context"
	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"github.com/google/wire"
)

var (
	WirePortfolioTrackerBuilderSet = wire.NewSet(NewPortfolioTrackerBuilder, wire.Bind(new(IPortfolioTrackerBuilder), new(*PortfolioTrackerBuilder)))
)

type IPortfolioTrackerBuilder interface {
	// BuildPortfolioTracker builds the detailed view for the portfolio tracker, returns a response containing fixed and scrollable components
	// fixed components will include title component and navigation toggles and scrollable components will be built for components in request
	BuildPortfolioTracker(ctx context.Context, req *strategy.BuildPortfolioTrackerRequest) (*strategy.BuildPortfolioTrackerResponse, error)
}
