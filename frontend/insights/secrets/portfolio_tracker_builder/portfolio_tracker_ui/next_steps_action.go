package portfoliotrackerui

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"

	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"github.com/samber/lo"

	networthBePb "github.com/epifi/gamma/api/insights/networth"

	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	netWorthScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	deeplinkpb "github.com/epifi/gamma/api/frontend/deeplink"
	secrets2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	"github.com/epifi/gamma/frontend/config"

	dynConf "github.com/epifi/gamma/frontend/config/genconf"

	"github.com/epifi/be-common/pkg/colors"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
)

type NextStepsActionComponent struct {
	config        *dynConf.Config
	ruleEvaluator *NextStepsRuleEvaluator
}

func NewNextStepsActionComponent(config *dynConf.Config) *NextStepsActionComponent {
	return &NextStepsActionComponent{
		config:        config,
		ruleEvaluator: NewNextStepsRuleEvaluator(),
	}
}

type DailyNetworthNextStepsRuleEnum string

const (
	DAILY_NETWORTH_NEXT_STEP_USSTOCKS_ACCOUNT            DailyNetworthNextStepsRuleEnum = "DAILY_NETWORTH_NEXT_STEP_USSTOCKS_ACCOUNT"
	DAILY_NETWORTH_NEXT_STEP_CONNECTED_ASSETS_COUNT      DailyNetworthNextStepsRuleEnum = "DAILY_NETWORTH_NEXT_STEP_ASSETS_COUNT"
	DAILY_NETWORTH_NEXT_STEP_MF_CATEGORY_DIVERSIFICATION DailyNetworthNextStepsRuleEnum = "DAILY_NETWORTH_NEXT_STEP_MF_CATEGORY_DIVERSIFICATION"
	DAILY_NETWORTH_NEXT_STEP_MF_INVESTEMENT_CONSISTENCY  DailyNetworthNextStepsRuleEnum = "DAILY_NETWORTH_NEXT_STEP_MF_INVESTEMENT_CONSISTENCY"
	DAILY_NETWORTH_NEXT_STEP_FUTURE_NET_WORTH            DailyNetworthNextStepsRuleEnum = "DAILY_NETWORTH_NEXT_STEP_FUTURE_NET_WORTH"
	DAILY_NETWORTH_NEXT_STEP_MF_INVESTMENT_STEPUP        DailyNetworthNextStepsRuleEnum = "DAILY_NETWORTH_NEXT_STEP_MF_INVESTMENT_STEPUP"
)

func (n *NextStepsActionComponent) BuildPortfolioTrackerComponent(ctx context.Context, req *strategy.BuildPortfolioTrackerComponentRequest) (*strategy.BuildPortfolioTrackerComponentResponse, error) {
	// Get networth details
	if n.config.DailyNetworthConfig() == nil {
		return nil, fmt.Errorf("failed to fetch daily networth config to build the UI")
	}
	networthDetails, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS]
	if !ok {
		return nil, fmt.Errorf("failed to fetch networth details from analyser variable map")
	}

	// Get MF investment activity
	mfInvestmentActivity, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES]
	if !ok {
		return nil, fmt.Errorf("failed to fetch mf investment activity from analyser variable map")
	}

	// Get MF asset category details
	mfAssetCategory, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS]
	if !ok {
		return nil, fmt.Errorf("failed to fetch mf asset category details from analyser variable map")
	}

	// Evaluate all rules and get applicable ones
	applicableRules := n.ruleEvaluator.EvaluateAllRules(
		networthDetails,
		mfInvestmentActivity,
		mfAssetCategory,
		n.config.DailyNetworthConfig().NextSteps(),
	)

	// Choose a random rule from applicable ones
	var selectedRule *DailyNetworthNextStepsEvaluatedRule
	if len(applicableRules) > 0 {
		selectedRuleIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(applicableRules))))
		if err != nil {
			return nil, fmt.Errorf("random number generation failed: %v", selectedRuleIndex)
		}
		selectedRule = applicableRules[selectedRuleIndex.Int64()]
	}

	// Build navigation toggle
	navigationToggle := secretsFePb.NewNavigationToggleBuilder().
		SetComponentType(req.ComponentType.String()).
		SetSelectedAndUnSelectedToggleIcon(&secretsFePb.ToggleIconParams{
			SelectedIcon:   "https://epifi-icons.pointz.in/secrets/daily-networth-next-steps-selected.png",
			UnselectedIcon: "https://epifi-icons.pointz.in/secrets/next-steps.png",
			Text:           "Next Steps",
		}).
		Build()

	// Build portfolio tracker component based on selected rule
	portfolioTrackerComponent, err := n.buildNextStepsComponent(selectedRule, req.ComponentType.String())
	if err != nil {
		return nil, fmt.Errorf("failed to build next steps component: %v", err)
	}

	return &strategy.BuildPortfolioTrackerComponentResponse{
		ComponentType:             req.ComponentType,
		NavigationToggle:          navigationToggle,
		PortfolioTrackerComponent: portfolioTrackerComponent,
	}, nil
}

func (n *NextStepsActionComponent) buildNextStepsComponent(selectedRule *DailyNetworthNextStepsEvaluatedRule, componentType string) (*secretsFePb.PortfolioTrackerComponent, error) {
	if selectedRule == nil {
		return &secretsFePb.PortfolioTrackerComponent{}, nil
	}

	ruleConfig := n.config.DailyNetworthConfig().NextSteps().Get(string(selectedRule.ruleType))
	if ruleConfig == nil {
		return nil, fmt.Errorf("config not found for rule: %s", string(selectedRule.ruleType))
	}

	portfolioTipsComponent := secretsFePb.NewPortfolioNextStepsSuggestionsComponentBuilder().
		SetComponentType(componentType).
		SetTitle(ruleConfig.Title()).
		SetDescription(ruleConfig.Subtitle()).
		SetDistributionChart(ruleConfig.ImageURL())

	// Add action cards from config
	for _, cardConfig := range ruleConfig.Cards() {
		actionCard := secretsFePb.NewActionCardBuilder().
			SetTitle(cardConfig.Title).
			SetDescription(cardConfig.Subtitle).
			SetIcon(cardConfig.ImageURL).
			SetCta(cardConfig.Cta, n.GetDeeplinkFromConfig(cardConfig.Deeplink, selectedRule)).
			SetBackgroundColor(widget.GetBlockBackgroundColour(colors.ColorDarkLayer2)).
			SetCornerRadius(16).
			SetBorderColor(widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
				{
					Color:          "#80636569",
					StopPercentage: 0,
				},
				{
					Color:          "#BF181818",
					StopPercentage: 100,
				},
			}))
		portfolioTipsComponent.AddActionCard(actionCard.Build())
	}

	return &secretsFePb.PortfolioTrackerComponent{
		Component: &secretsFePb.PortfolioTrackerComponent_PortfolioTipsComponent{
			PortfolioTipsComponent: portfolioTipsComponent.Build(),
		},
	}, nil
}

func (n *NextStepsActionComponent) GetDeeplinkFromConfig(config *config.DailyNetworthActionCardDeeplink, rule *DailyNetworthNextStepsEvaluatedRule) *deeplinkpb.Deeplink {
	switch config.ScreenName {
	case "SECRET_ANALYSER_SCREEN":
		return secrets2.GetSecretAnalyserScreenDeeplink(config.SecretAnalyserScreenOptions.SecretName)
	case "USSTOCKS_LANDING_SCREEN":
		return &deeplinkpb.Deeplink{
			Screen: deeplinkpb.Screen_USSTOCKS_LANDING_SCREEN,
		}
	case "WEALTH_BUILDER_LANDING_CONNECT_MORE_SCREEN":
		if rule.variable.GetNetworthDetails() != nil && len(rule.variable.GetNetworthDetails().NotConnectedAssets) > 0 {
			notConnectedAssets := lo.Map(rule.variable.GetNetworthDetails().GetNotConnectedAssets(), func(assetType networthBePb.AssetType, index int) string {
				return assetType.String()
			})
			return &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_WEALTH_BUILDER_LANDING_CONNECT_MORE_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&netWorthScreenOptions.ConnectMoreAssetsScreenOptions{
					ConnectMoreAssetsScreenRequestParams: &networthFePb.ConnectMoreAssetsScreenRequestParams{
						AssetTypes: notConnectedAssets,
					},
				}),
			}
		}
		return nil
	case "MUTUAL_FUND_LANDING_SCREEN":
		return &deeplinkpb.Deeplink{
			Screen: deeplinkpb.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN,
		}
	default:
		return nil
	}
}
