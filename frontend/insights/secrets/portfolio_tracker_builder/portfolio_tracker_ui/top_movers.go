package portfoliotrackerui

import (
	"context"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"
	mfUtils "github.com/epifi/gamma/pkg/networth"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/api/frontend/insights/secrets"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/common"
	secretColors "github.com/epifi/gamma/frontend/insights/secrets/colors"
	insightUtils "github.com/epifi/gamma/insights/networth/utils"
)

type TopMovers struct{}

func NewTopMovers() *TopMovers {
	return &TopMovers{}
}

func (t *TopMovers) BuildPortfolioTrackerComponent(ctx context.Context, req *strategy.BuildPortfolioTrackerComponentRequest) (*strategy.BuildPortfolioTrackerComponentResponse, error) {
	// Create TopMoversComponent builder
	topMoversComponentBuilder := secrets.NewTopMoversComponentBuilder()
	topMoversComponentBuilder.SetComponentType(secretFeEnums.PortfolioTrackerComponentType_name[int32(req.ComponentType)])
	topMoversComponentBuilder.SetTitle("Top Movers", "")
	topMoversComponentBuilder.SetSubTitle(getSubtitleText(ctx, req))

	topMoversSection, err := t.buildTopMoversSection(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("error building top movers section: %v", err)
	}

	topMoversComponentBuilder.SetTopMoversSection(topMoversSection)
	topMoversComponent := topMoversComponentBuilder.Build()
	navigationToggle := secrets.NewNavigationToggleBuilder().
		SetComponentType(req.ComponentType.String()).
		SetSelectedAndUnSelectedToggleIcon(&secrets.ToggleIconParams{
			SelectedIcon:   "https://epifi-icons.pointz.in/secrets/summary_toggle_selected.png",
			UnselectedIcon: "https://epifi-icons.pointz.in/secrets/summary_toggle.png",
			Text:           "Top movers",
		}).
		Build()

	return &strategy.BuildPortfolioTrackerComponentResponse{
		ComponentType:    req.ComponentType,
		NavigationToggle: navigationToggle,
		PortfolioTrackerComponent: &secrets.PortfolioTrackerComponent{
			Component: &secrets.PortfolioTrackerComponent_TopMoversComponent{
				TopMoversComponent: topMoversComponent,
			},
		},
	}, nil
}

func getSubtitleText(ctx context.Context, req *strategy.BuildPortfolioTrackerComponentRequest) string {
	mfSchemeAnalyticsResp, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !ok {
		return ""
	}
	// nolint: dogsled
	_, _, _, percentageChange := mfUtils.GetMfAggregatedValues(ctx, mfSchemeAnalyticsResp.GetMfSecretsSchemeAnalytics())

	switch {
	case percentageChange >= 2:
		return "It’s a volatile market. See how your portfolio moved."
	case percentageChange >= 1:
		return "Your investments are doing great! Check out your top movers."
	case percentageChange >= -1:
		return "Your investments are going steady. Check out how your investments did."
	case percentageChange >= -2:
		return "It’s a tough market, but the key is to stay consistent. Check which of your investments have been most impacted."
	case percentageChange < -2:
		return "It’s a volatile market. See how your portfolio moved."
	}
	return ""
}

func (t *TopMovers) buildTopMoversSection(ctx context.Context, req *strategy.BuildPortfolioTrackerComponentRequest) (*secrets.TopMoversSection, error) {
	// Create TopMoversSection builder
	sectionBuilder := secrets.NewTopMoversSectionBuilder()

	topMoverStrategyConfig := req.TrackingStrategy

	topGainers, topLosers, err := insightUtils.GetTopGainerAndTopLoser(ctx, &insightUtils.GetTopGainerAndTopLoserRequest{
		AnalyserVariableMap: req.AnalysisVariableMap,
		AnalysisVariables:   topMoverStrategyConfig.GetTopMoversConfig().AnalyserVariable,
		TopGainers:          3,
		TopLosers:           3,
	})
	if err != nil {
		return nil, fmt.Errorf("error building top gainers: %v", err)
	}

	if len(topGainers) > 0 {
		sectionBuilder.SetTopGainersAndTopLosersText("Top Gainers", "")
	}
	if len(topLosers) > 0 {
		sectionBuilder.SetTopGainersAndTopLosersText("", "Top Losers")
	}

	// Add top gainers (max 3)
	for i, assetMover := range topGainers {
		tile := t.buildAssetMoverTile(assetMover, i+1)
		sectionBuilder.AddTopGainerTile(tile)
	}

	// Add top losers (max 3)
	for i := len(topLosers) - 1; i >= 0; i-- {
		tile := t.buildAssetMoverTile(topLosers[i], i+1)
		sectionBuilder.AddTopLoserTile(tile)
	}
	return sectionBuilder.Build(), nil
}

func (t *TopMovers) buildAssetMoverTile(assetMover *insightUtils.AssetMoverTileInfo, tileNumber int) *secrets.TopMoversSection_AssetMoverTile {
	var assetMoverFontStyle, numberStyle commontypes.FontStyle
	var changeSign string
	// Determine effective position for styling based on whether it's a gainer or loser
	effectivePosition := tileNumber

	// Set font styles based on the effective position
	switch effectivePosition {
	case 1:
		assetMoverFontStyle = commontypes.FontStyle_HEADLINE_M
		numberStyle = commontypes.FontStyle_NUMBER_XL
	case 2:
		assetMoverFontStyle = commontypes.FontStyle_HEADLINE_S
		numberStyle = commontypes.FontStyle_NUMBER_L
	case 3:
		assetMoverFontStyle = commontypes.FontStyle_HEADLINE_XS
		numberStyle = commontypes.FontStyle_NUMBER_M
	default:
		assetMoverFontStyle = commontypes.FontStyle_HEADLINE_XS
		numberStyle = commontypes.FontStyle_NUMBER_M
	}

	// Set colors based on whether it's a gainer or loser
	changeAmountColor := secretColors.ColorSupportingAmber400
	changePercentColor := secretColors.ColorSupportingAmber200
	if assetMover.ReturnPercentage > 0 {
		changeAmountColor = colors.ColorMoss400
		changePercentColor = colors.SupportingMoss200
		changeSign = "+"
	}
	amountStr := money.ToDisplayStringWithSuffixAndPrecision(assetMover.ReturnAmount, false, true, 1, 0)
	changePercent := fmt.Sprintf(" %.2f%%", assetMover.ReturnPercentage)

	assetMoverbuilder := secrets.NewAssetMoverTileBuilder()
	assetMoverbuilder.SetAssetName(commontypes.GetTextFromStringFontColourFontStyle(assetMover.Name, colors.ColorOnDarkHighEmphasis, assetMoverFontStyle))
	changeValueTexts := ui.NewITC().
		WithTexts(
			commontypes.GetTextFromStringFontColourFontStyle(common.RupeeSymbol+" ", changeAmountColor, numberStyle),
			commontypes.GetTextFromStringFontColourFontStyle(changeSign+amountStr, changeAmountColor, numberStyle),
			commontypes.GetTextFromStringFontColourFontStyle(changePercent, changePercentColor, commontypes.FontStyle_NUMBER_2XS))

	assetMoverbuilder.SetChangeValueTexts(changeValueTexts)

	// Set background color
	bgColor := &widget.BackgroundColour{
		Colour: &widget.BackgroundColour_RadialGradient{
			RadialGradient: &widget.RadialGradient{
				Center: &widget.CenterCoordinates{
					CenterX: 0,
					CenterY: 5,
				},
				Colours:     []string{"#4A4A4A", "#242527"},
				OuterRadius: 104,
			},
		},
	}
	assetMoverbuilder.SetBgColour(bgColor)

	return assetMoverbuilder.Build()
}
