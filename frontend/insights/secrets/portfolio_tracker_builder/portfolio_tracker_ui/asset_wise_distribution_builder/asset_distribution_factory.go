package assetwisedistributionbuilder

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"github.com/google/wire"

	networthBePb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
)

var WireAssetWiseDistributionFactorySet = wire.NewSet(NewAssetWiseDistributionFactory, wire.Bind(new(IAssetWiseDistributionFactory), new(*AssetWiseDistributionFactory)))

type IAssetWiseDistributionFactory interface {
	GetAssetWiseDistributionBuilder(assetType networthBePb.AssetType) (AssetWiseDistributionBuilder, error)
}

type AssetWiseDistributionBuilder interface {
	BuildAssetWiseDistribution(ctx context.Context, req *strategy.BuildAssetWiseDistributionRequest) (*strategy.BuildAssetWiseDistributionResponse, error)
}

type AssetWiseDistributionFactory struct {
	mfDistribution          *MfDistribution
	epfDistribution         *EpfDistribution
	indiaStocksDistribution *IndianStocksDistribution
	npsDistribution         *NpsDistribution
	usStocksDistribution    *UsStocksDistribution
}

func NewAssetWiseDistributionFactory(
	gconf *genconf.Config,
	releaseEvaluator release.IEvaluator,
) *AssetWiseDistributionFactory {
	return &AssetWiseDistributionFactory{
		mfDistribution:          NewMfDistribution(gconf),
		epfDistribution:         NewEpfDistribution(),
		indiaStocksDistribution: NewIndianStocksDistribution(releaseEvaluator),
		npsDistribution:         NewNpsDistribution(),
		usStocksDistribution:    NewUsStocksDistribution(),
	}
}

func (p *AssetWiseDistributionFactory) GetAssetWiseDistributionBuilder(assetType networthBePb.AssetType) (AssetWiseDistributionBuilder, error) {
	switch assetType {
	case networthBePb.AssetType_ASSET_TYPE_MUTUAL_FUND:
		return p.mfDistribution, nil
	case networthBePb.AssetType_ASSET_TYPE_EPF:
		return p.epfDistribution, nil
	case networthBePb.AssetType_ASSET_TYPE_INDIAN_SECURITIES:
		return p.indiaStocksDistribution, nil
	case networthBePb.AssetType_ASSET_TYPE_NPS:
		return p.npsDistribution, nil
	case networthBePb.AssetType_ASSET_TYPE_US_SECURITIES:
		return p.usStocksDistribution, nil
	}
	return nil, fmt.Errorf("unhandled asset type for asset wise ditribution: %v", assetType)
}
