package assetwisedistributionbuilder

import (
	"context"

	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"github.com/epifi/be-common/pkg/epifierrors"
)

type UsStocksDistribution struct{}

func NewUsStocksDistribution() *UsStocksDistribution {
	return &UsStocksDistribution{}
}

func (t *UsStocksDistribution) BuildAssetWiseDistribution(ctx context.Context, req *strategy.BuildAssetWiseDistributionRequest) (*strategy.BuildAssetWiseDistributionResponse, error) {
	return nil, epifierrors.ErrUnimplemented
}
