package portfoliotrackerui

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"
	investmentPkg "github.com/epifi/gamma/pkg/investment"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"

	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	"github.com/epifi/gamma/api/typesv2/ui/charts"
	secretsColors "github.com/epifi/gamma/frontend/insights/secrets/colors"
)

const (
	borderRadialGradientStartColor = "#4DDCF3EE"
	borderRadialGradientEndColor   = "#0DDCF3EE"
)

type ComparisonAndNewsComponent struct{}

func NewComparisonAndNewsComponent() *ComparisonAndNewsComponent {
	return &ComparisonAndNewsComponent{}
}

func (c *ComparisonAndNewsComponent) BuildPortfolioTrackerComponent(ctx context.Context, req *strategy.BuildPortfolioTrackerComponentRequest) (*strategy.BuildPortfolioTrackerComponentResponse, error) {
	// Create MarketComparisonAndNewsComponent builder
	componentBuilder := secretsFePb.NewMarketComparisonAndNewsComponentBuilder()
	componentBuilder.SetComponentType(req.ComponentType.String())

	// Build market comparison section
	marketComparison, err := c.buildMarketComparisonSection(req)
	if err != nil {
		return nil, fmt.Errorf("error building market comparison section: %v", err)
	}
	componentBuilder.SetMarketComparison(marketComparison)

	marketComparisonAndNewsComponent := componentBuilder.Build()

	navigationToggle := secretsFePb.NewNavigationToggleBuilder().
		SetComponentType(req.ComponentType.String()).
		SetSelectedAndUnSelectedToggleIcon(&secretsFePb.ToggleIconParams{
			SelectedIcon:   "https://epifi-icons.pointz.in/secrets/market_comparison_toggle_selected.png",
			UnselectedIcon: "https://epifi-icons.pointz.in/secrets/market_comparison_toggle.png",
			Text:           "Your Funds v/s Market",
		}).
		Build()

	return &strategy.BuildPortfolioTrackerComponentResponse{
		ComponentType:    req.ComponentType,
		NavigationToggle: navigationToggle,
		PortfolioTrackerComponent: &secretsFePb.PortfolioTrackerComponent{
			Component: &secretsFePb.PortfolioTrackerComponent_MarketComparisonAndNewsComponent{
				MarketComparisonAndNewsComponent: marketComparisonAndNewsComponent,
			},
		},
	}, nil
}

func (c *ComparisonAndNewsComponent) buildMarketComparisonSection(req *strategy.BuildPortfolioTrackerComponentRequest) (*secretsFePb.MarketComparisonSection, error) {
	mfPortfolioPerformanceDetails, ok := req.AnalysisVariableMap[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO]
	if !ok {
		return nil, fmt.Errorf("MF scheme analytics not found in analysis variable map while building market comparision section")
	}

	sectionBuilder := secretsFePb.NewMarketComparisonSectionBuilder()
	// Set title and subtitle
	sectionBuilder.SetTitle("Your Mutual Funds v/s Market", "This graph gives you an idea of how your Mutual Funds and/or Stocks are performing compared to the Nifty50 index.")
	sectionBuilder.SetSubTitle("See where you stand compared to market trends")

	// Build comparison chart
	chart := c.buildComparisonChart(mfPortfolioPerformanceDetails)
	sectionBuilder.SetComparisonChart(chart)

	return sectionBuilder.Build(), nil
}

func (c *ComparisonAndNewsComponent) buildComparisonChart(details *analyserVariablePb.AnalysisVariable) *secretsFePb.ComparisonChart {
	mfPortfolioPerformanceDetails := details.GetMfPortfolioPerformanceDetails()

	chartBuilder := secretsFePb.NewComparisonChartBuilder()
	// Set background and border colors
	chartBuilder.SetBackgroundColour(widgetPb.GetBlockBackgroundColour(secretsColors.ColorDarkBase))
	chartBuilder.SetBorderColour(widgetPb.GetRadialGradientBackgroundColor(&widgetPb.CenterCoordinates{CenterX: 5, CenterY: 5}, 178, []string{borderRadialGradientStartColor, borderRadialGradientEndColor}))

	nifty50Returns := mfPortfolioPerformanceDetails.GetNifty50PercentageReturns()
	userPortfolioReturns := mfPortfolioPerformanceDetails.GetUserPercentageReturns()

	nifty50ReturnsTextColor := c.getReturnsColor(nifty50Returns)
	userPortfolioReturnsColor := c.getReturnsColor(userPortfolioReturns)

	// Add chart bars
	nifty50ReturnsBar := &charts.Bar{
		Value:             nifty50Returns,
		ShowStripes:       true,
		YAxisDisplayValue: commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%0.2f%%", nifty50Returns), nifty50ReturnsTextColor, commontypes.FontStyle_OVERLINE_2XS_CAPS),
	}

	userReturnsBar := &charts.Bar{
		Value:             userPortfolioReturns,
		BarColor:          userPortfolioReturnsColor,
		YAxisDisplayValue: commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%0.2f%%", userPortfolioReturns), userPortfolioReturnsColor, commontypes.FontStyle_OVERLINE_2XS_CAPS),
	}
	chartBuilder.AddBar(nifty50ReturnsBar)
	chartBuilder.AddBar(userReturnsBar)

	// Add Date
	chartBuilder.SetDateLabel(commontypes.GetTextFromStringFontColourFontStyle(c.getCurrentDate(), secretsColors.ColorOnlightLowEmphasis, commontypes.FontStyle_OVERLINE_2XS_CAPS))

	// Add legends
	chartBuilder.AddLegend("NIFTY50", secretsColors.ColorNeutralsIron)
	chartBuilder.AddLegend("YOU", userPortfolioReturnsColor)

	// Add Reference Line
	chartBuilder.SetReferenceLineConfig(&secretsFePb.ComparisonChart_ReferenceLineConfig{LineColour: widgetPb.GetBlockBackgroundColour(secretsColors.ColorNeutralsCharcoal800), LineHeight: 1, LeadingText: commontypes.GetTextFromStringFontColourFontStyle("YEST.", secretsColors.ColorNeutralsIron, commontypes.FontStyle_OVERLINE_2XS_CAPS)})

	return chartBuilder.Build()
}

func (c *ComparisonAndNewsComponent) getReturnsColor(returns float64) string {
	if returns < 0 {
		return secretsColors.ColorSupportingAmber700
	}
	return secretsColors.ColorSupportingMoss700
}

// getCurrentDate returns the current date in the format "1st Jan", "2nd Feb" etc.
// The function always returns yesterday's date by default.
// And if it's before 11 AM, it returns the day before yesterday's date.
// This is because before 11 AM, yesterday's data is not ready to be displayed.
func (c *ComparisonAndNewsComponent) getCurrentDate() string {
	// get the required date to display on the header
	reportTime := investmentPkg.GetLastTradingDayTime()
	// Return formatted date string
	return reportTime.Format("2 Jan'06")
}
