package actoractivity

import (
	"context"
	"fmt"
	"strings"
	"time"

	cmap "github.com/orcaman/concurrent-map"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	cardPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/frontend/search/widget"
	orderPb "github.com/epifi/gamma/api/order"
	actorActivityPb "github.com/epifi/gamma/api/order/actoractivity"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	rewardsPkg "github.com/epifi/gamma/pkg/rewards"

	feRewardsPkg "github.com/epifi/gamma/frontend/pkg/rewards"
	"github.com/epifi/gamma/pkg/feature/release"
)

const (
	// prefixes used in concurrent map key to differentiate between different types of fi-coins
	fiCoinsKeyPrefix      = "fi_coins"
	cashBacksKeyPrefix    = "cash_backs"
	forexRefundsKeyPrefix = "forex_refunds"
)

// getRewardSummaryWidgetsMap returns map of actorActivity id to reward summary widget.
// NOTE: we pass the total month's reward summary details widget in last day of the month's transaction activity id as key in map.
func (s *Service) getRewardSummaryWidgetsMap(ctx context.Context, actorId string, actorActivities []*actorActivityPb.GetActivitiesResponse_Activity) (map[string]*widget.RewardSummaryWidget, error) {
	// flag to enable rewards details section on order receipt page
	releaseConstraint := release.NewCommonConstraintData(typesPb.Feature_FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE).WithActorId(actorId)
	isEligible, releaseErr := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if releaseErr != nil {
		logger.Error(ctx, "error in release evaluator for feature Feature_FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE", zap.Error(releaseErr))
		return nil, releaseErr
	}

	if isEligible {
		// in this map we store the reward units of each type of rewards: cashbacks, fi-coins, & forex refunds
		// where the key is the time window's from time and value is the reward units.
		rewardSummaryMap := cmap.New()
		// fetch all time windows for which we need to get the rewards aggregates.
		timeWindowsFromActorActivities := getTimeWindowFromActorActivities(actorActivities)

		errGrp, grpCtx := errgroup.WithContext(ctx)
		for _, timeWindow := range timeWindowsFromActorActivities {
			tw := timeWindow
			errGrp.Go(func() error {
				rewardAggregateResp, rewardAggregateErr := s.rewardsAggregatesClient.GetRewardsAggregates(grpCtx, &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId:       actorId,
					FromCreatedAt: tw.GetFromTime(),
					ToCreatedAt:   tw.GetTillTime(),
					Filters: &rewardsPinotPb.Filters{
						ExcludeActionTypes: []rewardsPb.CollectedDataType{
							rewardsPb.CollectedDataType_OFFER_REDEMPTION_STATUS_UPDATE,
						},
					},
				})
				if rpcErr := epifigrpc.RPCError(rewardAggregateResp, rewardAggregateErr); rpcErr != nil {
					return fmt.Errorf("GetRewardsAggregates rpc failed, %w", rpcErr)
				}

				var rewardedFiCoins, rewardedCashBacks float64
				for _, aggregate := range rewardAggregateResp.GetRewardOptionAggregates() {
					switch aggregate.GetRewardType() {
					case rewardsPb.RewardType_CASH:
						rewardedCashBacks += aggregate.GetRewardUnits()
					case rewardsPb.RewardType_FI_COINS:
						rewardedFiCoins += aggregate.GetRewardUnits()
					}
				}

				if rewardedFiCoins > 0 {
					key := fmt.Sprintf("%s:%s", tw.GetFromTime().AsTime().In(datetime.IST).String(), fiCoinsKeyPrefix)
					rewardSummaryMap.Set(key, rewardedFiCoins)
				}

				if rewardedCashBacks > 0 {
					key := fmt.Sprintf("%s:%s", tw.GetFromTime().AsTime().In(datetime.IST).String(), cashBacksKeyPrefix)
					rewardSummaryMap.Set(key, rewardedCashBacks)
				}

				return nil
			})
			errGrp.Go(func() error {
				// Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=23357-47983&t=erstoC1PtcstBh8D-4
				// get forex refunds for the given time window
				forexRefundsResp, forexRefundsErr := s.cardProvisioningClient.FetchForexRefundAggregates(ctx, &cardPb.FetchForexRefundAggregatesRequest{
					ActorId:   actorId,
					StartTime: tw.GetFromTime(),
					EndTime:   tw.GetTillTime(),
					Tiers:     rewardsPkg.ForexRefundEligibleBeTierList,
				})
				if rpcErr := epifigrpc.RPCError(forexRefundsResp, forexRefundsErr); rpcErr != nil {
					logger.Error(ctx, "failed to fetch forex refunds", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
					return rpcErr
				}

				totalForexRefund := moneyPkg.GetZeroInrIfNil(nil)
				for _, tier := range rewardsPkg.ForexRefundEligibleBeTierList {
					if value, exists := forexRefundsResp.GetRefundAggregates()[tier.String()]; exists {
						var err error
						totalForexRefund, err = moneyPkg.Sum(totalForexRefund, value)
						if err != nil {
							logger.Error(ctx, "failed to sum forex refunds", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
							return err
						}
					}
				}

				forexRefundsUnits, _ := moneyPkg.ToDecimal(totalForexRefund).Float64()
				if forexRefundsUnits > 0 {
					key := fmt.Sprintf("%s:%s", tw.GetFromTime().AsTime().In(datetime.IST).String(), forexRefundsKeyPrefix)
					rewardSummaryMap.Set(key, forexRefundsUnits)
				}
				return nil
			})
		}
		if err := errGrp.Wait(); err != nil {
			return nil, err
		}

		monthWiseRewardSummaryWidgetsMap := make(map[string]*widget.RewardSummaryWidget)
		for _, timeWindow := range timeWindowsFromActorActivities {
			rewardedFiCoinsFloat64, rewardedCashBacksFloat64, forexRefundsUnitsFloat64 := 0.0, 0.0, 0.0
			fromTimeStr := timeWindow.GetFromTime().AsTime().In(datetime.IST).String()
			rewardedFiCoins, _ := rewardSummaryMap.Get(fmt.Sprintf("%s:%s", fromTimeStr, fiCoinsKeyPrefix))
			if rewardedFiCoins != nil {
				rewardedFiCoinsFloat64 = rewardedFiCoins.(float64)
			}
			rewardedCashBacks, _ := rewardSummaryMap.Get(fmt.Sprintf("%s:%s", fromTimeStr, cashBacksKeyPrefix))
			if rewardedCashBacks != nil {
				rewardedCashBacksFloat64 = rewardedCashBacks.(float64)
			}
			forexRefundsUnits, _ := rewardSummaryMap.Get(fmt.Sprintf("%s:%s", fromTimeStr, forexRefundsKeyPrefix))
			if forexRefundsUnits != nil {
				forexRefundsUnitsFloat64 = forexRefundsUnits.(float64)
			}
			if rewardedFiCoinsFloat64 > 0 || rewardedCashBacksFloat64 > 0 || forexRefundsUnitsFloat64 > 0 {
				monthWiseRewardSummaryWidgetsMap[timeWindow.GetFromTime().AsTime().In(datetime.IST).String()] = s.getRewardSummaryWidgetDetails(grpCtx, rewardedFiCoinsFloat64, rewardedCashBacksFloat64, forexRefundsUnitsFloat64, timeWindow.GetFromTime().AsTime().In(datetime.IST))
			}

		}
		return assignMonthWiseRewardSummaryWidgetToActorActivities(actorActivities, monthWiseRewardSummaryWidgetsMap), nil
	}

	return nil, nil
}

// assignMonthWiseRewardSummaryWidgetToActorActivities assigns the last txn activity id of month value as key to the map and value as its corresponding reward summary widget.
func assignMonthWiseRewardSummaryWidgetToActorActivities(actorActivities []*actorActivityPb.GetActivitiesResponse_Activity, monthWiseRewardSummaryWidgetsMap map[string]*widget.RewardSummaryWidget) map[string]*widget.RewardSummaryWidget {
	// create a map to track the first transaction of each month with its activity id
	firstDayOfMonthToActorActivityIdMap := make(map[time.Time]string)
	rewardSummaryWidgetsMap := make(map[string]*widget.RewardSummaryWidget)
	for _, actorActivity := range actorActivities {
		txnTime := actorActivity.GetActivityTimestamp().AsTime().In(datetime.IST)
		firstDayOfMonth := time.Date(txnTime.Year(), txnTime.Month(), 1, 0, 0, 0, 0, datetime.IST)

		// check if this is the first transaction of the month
		if _, exists := firstDayOfMonthToActorActivityIdMap[firstDayOfMonth]; !exists {
			firstDayOfMonthToActorActivityIdMap[firstDayOfMonth] = actorActivity.GetActivityId()
		}
	}

	// Assign values from the monthWiseRewardSummaryWidgetsMap to the first transaction of each month
	for firstOfMonth, actorActivityId := range firstDayOfMonthToActorActivityIdMap {
		if val, exists := monthWiseRewardSummaryWidgetsMap[firstOfMonth.String()]; exists {
			rewardSummaryWidgetsMap[actorActivityId] = val
		}
	}

	return rewardSummaryWidgetsMap
}

func getTimeWindowFromActorActivities(actorActivities []*actorActivityPb.GetActivitiesResponse_Activity) []*rewardsPb.TimeWindow {
	result := make([]*rewardsPb.TimeWindow, 0)
	computedMonthWiseMap := make(map[time.Time]bool, 0)
	for _, actorActivity := range actorActivities {
		t := actorActivity.GetActivityTimestamp().AsTime().In(datetime.IST)
		// Get the first date of the month
		firstOfMonth := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, datetime.IST)
		// Get the last date of the month
		lastOfMonth := firstOfMonth.AddDate(0, 1, -1).In(datetime.IST)

		if isComputed, ok := computedMonthWiseMap[firstOfMonth]; !(ok && isComputed) {
			result = append(result, &rewardsPb.TimeWindow{
				FromTime: timestamppb.New(firstOfMonth),
				TillTime: timestamppb.New(lastOfMonth),
			})
			// NOTE: here we are taking first day of month as unique key for all transactions for the month.
			computedMonthWiseMap[firstOfMonth] = true
		}
	}

	return result
}

func (s *Service) getRewardSummaryWidgetDetails(_ context.Context, rewardedFiCoins, rewardedCashBacks, forexRefundsUnits float64, monthDate time.Time) *widget.RewardSummaryWidget {
	var (
		rewardSummaryRows []*widget.RewardSummaryWidget_RewardSummaryRow
		heading           string
	)
	if rewardedFiCoins > 0 {
		rewardSummaryRows = append(rewardSummaryRows, feRewardsPkg.GetRewardSummaryWidgetRow(s.dynconf.RewardSummaryForActorActivitiesPageConfig().RewardedFiCoinsSummary(), rewardedFiCoins, nil))
	}
	if rewardedCashBacks > 0 {
		rewardSummaryRows = append(rewardSummaryRows, feRewardsPkg.GetRewardSummaryWidgetRow(s.dynconf.RewardSummaryForActorActivitiesPageConfig().RewardedCashbackSummary(), rewardedCashBacks, nil))
	}

	if forexRefundsUnits > 0 {
		rewardSummaryRows = append(rewardSummaryRows, feRewardsPkg.GetRewardSummaryWidgetRow(s.dynconf.RewardSummaryForActorActivitiesPageConfig().ForexRefundsSummary(), forexRefundsUnits, nil))
	}

	currTime := time.Now().In(datetime.IST)
	if currTime.Month() == monthDate.Month() && currTime.Year() == monthDate.Year() {
		heading = "EARNED THIS MONTH"
	} else {
		heading = fmt.Sprintf("EARNED ON %s", strings.ToUpper(monthDate.Format("January 2006")))
	}

	return &widget.RewardSummaryWidget{
		Heading:           commontypes.GetTextFromStringFontColourFontStyle(heading, "#333333", commontypes.FontStyle_OVERLINE_2),
		RewardSummaryRows: rewardSummaryRows,
		IndicatorInfo: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle("Earnings apply to Federal Bank Savings A/c spends only", "#929599", commontypes.FontStyle_OVERLINE_2XS_CAPS),
			},
			LeftImgTxtPadding: 6,
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/federal-icon.png", 16, 16),
		},
	}
}

// nolint: funlen
func (s *Service) getActorActivityIdToRewardsMap(ctx context.Context, actorId string, actorActivities []*actorActivityPb.GetActivitiesResponse_Activity) (map[string]*widget.RewardsEarnedValueChip, error) {
	// flag to enable rewards details section on order receipt page
	releaseConstraint := release.NewCommonConstraintData(typesPb.Feature_FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE).WithActorId(actorId)
	isEligible, releaseErr := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if releaseErr != nil {
		logger.Error(ctx, "error in release evaluator for feature FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT", zap.Error(releaseErr))
		return map[string]*widget.RewardsEarnedValueChip{}, releaseErr
	}

	if isEligible {
		// fetch all external ids from given actor activity ids
		var orderIds []*orderPb.OrderIdentifier
		for _, actorActivity := range actorActivities {
			switch actorActivity.GetActivityEntryPoint() {
			case actorActivityPb.ActivityEntryPoint_ORDER:
				orderIds = append(orderIds, &orderPb.OrderIdentifier{
					Identifier: &orderPb.OrderIdentifier_OrderId{OrderId: actorActivity.GetActivityId()},
				})
			default:
				// TODO: check for cc txn ids
			}
		}

		getOrdersResp, getOrdersErr := s.orderClient.GetOrders(ctx, &orderPb.GetOrdersRequest{GetOrderBy: orderIds})
		if rpcErr := epifigrpc.RPCError(getOrdersResp, getOrdersErr); rpcErr != nil {
			logger.Error(ctx, "error getting orders from order ids", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			return map[string]*widget.RewardsEarnedValueChip{}, rpcErr
		}

		refIds := make([]string, 0, len(getOrdersResp.GetOrders()))
		for _, order := range getOrdersResp.GetOrders() {
			refIds = append(refIds, order.GetExternalId())
		}

		refIdToOrderIdMap := make(map[string]string)
		for _, order := range getOrdersResp.GetOrders() {
			refIdToOrderIdMap[order.GetExternalId()] = order.GetId()
		}

		// fetch all rewards and projections generated for this order ids
		// Note: GetAllRewardsAndProjections API is not paginated, so we need to fetch in batches and max of 30 RefIds can be passed in one request.
		const batchSize = 30
		refIdToRewardEntitiesMap := cmap.New()

		errGrp, grpCtx := errgroup.WithContext(ctx)
		for i := 0; i < len(refIds); i += batchSize {
			batch := refIds[i:min(i+batchSize, len(refIds))]
			errGrp.Go(func() error {
				rewardsRes, err := s.rewardsGeneratorClient.GetAllRewardsAndProjections(grpCtx, &rewardsPb.GetAllRewardsAndProjectionRequest{
					ActorId:    actorId,
					ActionType: rewardsPb.CollectedDataType_ORDER,
					RefIds:     batch,
				})
				if rpcErr := epifigrpc.RPCError(rewardsRes, err); rpcErr != nil {
					return rpcErr
				}

				for refId, rewardEntities := range rewardsRes.GetRefIdToRewardEntitiesMap() {
					refIdToRewardEntitiesMap.Set(refId, rewardEntities)
				}
				return nil
			})
		}
		if err := errGrp.Wait(); err != nil {
			logger.Error(ctx, "error fetching rewards for ref ids", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return map[string]*widget.RewardsEarnedValueChip{}, err
		}

		// get rewards earned value for each order ids
		orderIdToRewardsEarnedMap := make(map[string]*widget.RewardsEarnedValueChip)
		for _, refId := range refIds {
			val, ok := refIdToRewardEntitiesMap.Get(refId)
			if !ok {
				// if the ref id is not present, ignore as no reward was generated for this ref id.
				continue
			}
			rewardEntities := val.(*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities)
			if len(rewardEntities.GetRewardEntities()) == 0 {
				// ignore as no reward was generated for this ref id.
				continue
			}
			rewardsEarnedValue := s.getRewardedValue(rewardEntities.GetRewardEntities())

			// map with order id to rewards earned value (taking order id using ref id from refIdToOrderIdMap)
			orderIdToRewardsEarnedMap[refIdToOrderIdMap[refId]] = rewardsEarnedValue
		}

		return orderIdToRewardsEarnedMap, nil
	}

	return map[string]*widget.RewardsEarnedValueChip{}, nil
}

func (s *Service) getRewardedValue(rewardEntities []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity) *widget.RewardsEarnedValueChip {
	earnedRewardUnits, projectedRewardUnits := getRewardAggregates(rewardEntities)
	for _, earnedRewards := range earnedRewardUnits {
		return s.getRewardsEarnedValueChip(earnedRewards, false, len(earnedRewardUnits)+len(projectedRewardUnits)-1)
	}
	for _, projectedRewards := range projectedRewardUnits {
		return s.getRewardsEarnedValueChip(projectedRewards, true, len(earnedRewardUnits)+len(projectedRewardUnits)-1)
	}
	return nil
}

func (s *Service) getRewardsEarnedValueChip(rewardUnits *rewardsPb.RewardOptionMinimal, showWashedOutChip bool, additionalRewardsCount int) *widget.RewardsEarnedValueChip {
	var (
		additionalValue *ui.IconTextComponent
		iconUrl         string
		conf            = s.dynconf.RewardSummaryForActorActivitiesPageConfig().RewardsEarnedValueChip()
	)
	if additionalRewardsCount > 0 {
		additionalValue = &ui.IconTextComponent{
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       conf.AdditionalValue().ContainerProperties().BgColor(),
				CornerRadius:  conf.AdditionalValue().ContainerProperties().CornerRadius(),
				Height:        conf.AdditionalValue().ContainerProperties().Height(),
				Width:         conf.AdditionalValue().ContainerProperties().Width(),
				LeftPadding:   2,
				RightPadding:  3,
				TopPadding:    2,
				BottomPadding: 3,
			},
			LeftImgTxtPadding: conf.AdditionalValue().LeftImgTxtPadding(),
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(conf.AdditionalValue().Texts()[0].Content, additionalRewardsCount), conf.Value().Texts()[0].FontColor, (commontypes.FontStyle)(commontypes.FontStyle_value[conf.AdditionalValue().Texts()[0].FontStyle])),
			},
		}
	}

	switch rewardUnits.GetRewardType() {
	case rewardsPb.RewardType_CASH:
		iconUrl = s.dynconf.RewardSummaryForActorActivitiesPageConfig().RupeeSymbolIconUrl()
	case rewardsPb.RewardType_FI_COINS:
		iconUrl = s.dynconf.RewardSummaryForActorActivitiesPageConfig().FiCoinSymbolIconUrl()
	}

	return &widget.RewardsEarnedValueChip{
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(decimal.NewFromFloat32(rewardUnits.GetUnits()).Round(0).String(), conf.Value().Texts()[0].FontColor, (commontypes.FontStyle)(commontypes.FontStyle_value[conf.Value().Texts()[0].FontStyle])),
			},
			LeftImgTxtPadding: conf.Value().LeftImgTxtPadding(),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      conf.Value().ContainerProperties().BgColor(),
				CornerRadius: conf.Value().ContainerProperties().CornerRadius(),
				Height:       conf.Value().ContainerProperties().Height(),
				Width:        conf.Value().ContainerProperties().Width(),
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, conf.Value().LeftVisualElementImage().Properties().Height(), conf.Value().LeftVisualElementImage().Properties().Width()),
		},
		ShowWashedOutChip: showWashedOutChip,
		AdditionalValue:   additionalValue,
	}
}

func getRewardAggregates(rewardEntities []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity) ([]*rewardsPb.RewardOptionMinimal, []*rewardsPb.RewardOptionMinimal) {
	var (
		aggregatesOfEarnedFiCoinsUnits     float32
		aggregatesOfEarnedCashbackUnits    float32
		aggregatesOfProjectedFiCoinsUnits  float32
		aggregatesOfProjectedCashbackUnits float32
		earnedRewardUnits                  []*rewardsPb.RewardOptionMinimal
		projectedRewardUnits               []*rewardsPb.RewardOptionMinimal
	)
	for _, rewardEntity := range rewardEntities {
		switch rewardEntity.GetRewardEntityType() {
		case rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD, rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD:
			for _, rewardOption := range rewardEntity.GetRewardOptions() {
				switch rewardOption.GetRewardType() {
				case rewardsPb.RewardType_FI_COINS:
					aggregatesOfEarnedFiCoinsUnits += rewardOption.GetUnits()
				case rewardsPb.RewardType_CASH:
					aggregatesOfEarnedCashbackUnits += rewardOption.GetUnits()
				}
			}
		case rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD:
			for _, rewardOption := range rewardEntity.GetRewardOptions() {
				switch rewardOption.GetRewardType() {
				case rewardsPb.RewardType_FI_COINS:
					aggregatesOfProjectedFiCoinsUnits += rewardOption.GetUnits()
				case rewardsPb.RewardType_CASH:
					aggregatesOfProjectedCashbackUnits += rewardOption.GetUnits()
				}
			}
		}
	}

	if aggregatesOfEarnedCashbackUnits > 0 {
		earnedRewardUnits = append(earnedRewardUnits, &rewardsPb.RewardOptionMinimal{
			RewardType: rewardsPb.RewardType_CASH,
			Units:      aggregatesOfEarnedCashbackUnits,
		})
	}

	if aggregatesOfEarnedFiCoinsUnits > 0 {
		earnedRewardUnits = append(earnedRewardUnits, &rewardsPb.RewardOptionMinimal{
			RewardType: rewardsPb.RewardType_FI_COINS,
			Units:      aggregatesOfEarnedFiCoinsUnits,
		})
	}

	// for projected, we want to show only default value (cashback)
	if aggregatesOfProjectedCashbackUnits > 0 {
		projectedRewardUnits = append(projectedRewardUnits, &rewardsPb.RewardOptionMinimal{
			RewardType: rewardsPb.RewardType_CASH,
			Units:      aggregatesOfProjectedCashbackUnits,
		})
	} else if aggregatesOfProjectedFiCoinsUnits > 0 {
		projectedRewardUnits = append(projectedRewardUnits, &rewardsPb.RewardOptionMinimal{
			RewardType: rewardsPb.RewardType_FI_COINS,
			Units:      aggregatesOfProjectedFiCoinsUnits,
		})
	}

	return earnedRewardUnits, projectedRewardUnits
}
