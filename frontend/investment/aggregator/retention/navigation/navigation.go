package navigation

import (
	"context"
	"errors"

	aggFePb "github.com/epifi/gamma/api/frontend/investment/aggregator"
	retentionFePb "github.com/epifi/gamma/api/frontend/investment/aggregator/retention_screen"
)

type NavigationParams struct {
	BackActionVisitedScreen []retentionFePb.RetentionScreen
	NextActionVisitedScreen []retentionFePb.RetentionScreen
	CurrentScreen           retentionFePb.RetentionScreen
}

//go:generate mockgen -source=navigation.go -destination=./mocks/navigation.go -package=mocks
type Navigation interface {
	// GetNewNavigationParams returns the new navigation params based on the current navigation params
	GetNewNavigationParams(ctx context.Context, instrumentType aggFePb.InstrumentType, stageHistory []retentionFePb.RetentionScreen, instrumentData *retentionFePb.InstrumentData) (*NavigationParams, error)
}

type NavigationImpl struct {
	navigators map[aggFePb.InstrumentType]Navigator
}

// Ensure NavigationImpl implements Navigation at compile time
var _ Navigation = &NavigationImpl{}

func NewNavigationImpl(
	navigators ...Navigator,
) *NavigationImpl {
	navigatorMap := make(map[aggFePb.InstrumentType]Navigator)
	for _, navigator := range navigators {
		navigatorMap[navigator.GetInstrumentType()] = navigator
	}
	return &NavigationImpl{
		navigators: navigatorMap,
	}
}

func (n *NavigationImpl) GetNavigationVariant(
	retentionParams *retentionFePb.RetentionScreenParams,
) aggFePb.InstrumentType {
	switch retentionParams.GetInstrumentData().GetData().(type) {
	case *retentionFePb.InstrumentData_FixedDepositInstrumentData:
		return aggFePb.InstrumentType_FIXED_DEPOSIT
	default:
		return aggFePb.InstrumentType_INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED
	}
}

func (n *NavigationImpl) GetNewNavigationParams(ctx context.Context, instrumentType aggFePb.InstrumentType, stageHistory []retentionFePb.RetentionScreen, instrumentData *retentionFePb.InstrumentData) (*NavigationParams, error) {
	navigator, ok := n.navigators[instrumentType]
	if !ok {
		return nil, errors.New("unimplemented instrument type")
	}
	prevScreen, err := navigator.PerformPrevScreen(stageHistory)
	if err != nil {
		return nil, err
	}
	nextScreen, err := navigator.PerformNextScreen(ctx, stageHistory, instrumentData)
	if err != nil {
		return nil, err
	}
	return &NavigationParams{
		BackActionVisitedScreen: prevScreen,
		NextActionVisitedScreen: nextScreen,
		CurrentScreen:           stageHistory[len(stageHistory)-1],
	}, nil

}
