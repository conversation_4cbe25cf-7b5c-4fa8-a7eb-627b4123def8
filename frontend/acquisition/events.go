package acquisition

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

// IntentAndSourceIdentification event is to be sent when intent and source are identified from the attribution details
// received from client
type IntentAndSourceIdentification struct {
	UserId                       string
	SessionId                    string
	ProspectId                   string
	AttemptId                    string
	Timestamp                    time.Time
	EventId                      string
	EventType                    string
	AfOnConversionDataSuccessMap map[string]any
	InstallReferrerMap           map[string]any
	AcquisitionSource            string
	AcquisitionIntent            string
	DeviceIdentifier             string
	IsRepeatUserByDevice         bool
}

func NewIntentAndSourceIdentification(
	userId string,
	afOnConversionDataSuccessMap, installReferrerMap map[string]any,
	source, intent string,
	deviceId string,
	isRepeatUserByDevice bool,
) *IntentAndSourceIdentification {
	return &IntentAndSourceIdentification{
		UserId:                       userId,
		SessionId:                    "",
		ProspectId:                   "",
		AttemptId:                    "",
		Timestamp:                    time.Now(),
		EventId:                      uuid.New().String(),
		EventType:                    events.EventTrack,
		AfOnConversionDataSuccessMap: afOnConversionDataSuccessMap,
		InstallReferrerMap:           installReferrerMap,
		AcquisitionSource:            source,
		AcquisitionIntent:            intent,
		DeviceIdentifier:             deviceId,
		IsRepeatUserByDevice:         isRepeatUserByDevice,
	}
}

func (f *IntentAndSourceIdentification) GetEventId() string {
	return f.EventId
}

func (f *IntentAndSourceIdentification) GetUserId() string {
	return f.UserId
}

func (f *IntentAndSourceIdentification) GetProspectId() string {
	return f.ProspectId
}

func (f *IntentAndSourceIdentification) GetEventName() string {
	return EventIntentAndSourceIdentification
}

func (f *IntentAndSourceIdentification) GetEventType() string {
	return events.EventTrack
}

func (f *IntentAndSourceIdentification) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(f, properties)
	return properties
}

func (f *IntentAndSourceIdentification) GetEventTraits() map[string]interface{} {
	return nil
}
