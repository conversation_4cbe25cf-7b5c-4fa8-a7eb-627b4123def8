package vkyc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	beVkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
	onboardingPkg "github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/pkg/vkyc"
)

// nolint:funlen
func getCallFailedDeeplink(ctx context.Context, entryPoint beVkycPb.EntryPoint, karzaCallStatuses []*beVkycPb.KarzaCallStatus,
	latestCustInfo *beVkycPb.VKYCKarzaCustomerInfo, preferredLanguage string, vkycConf *genconf.VKYC,
	releaseEvaluator release.IEvaluator, actorId string) *deeplinkPb.Deeplink {
	// only need to consume by IOS device
	hideBackButton := true
	var ctaList = []*deeplinkPb.Cta{
		{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         agentUnavailableCTA,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Deeplink: vkyc.GetVKYCNextActionDeeplink(&deeplinkPb.GetVKYCNextActionApiScreenOptions{
				EntryPoint:      entryPoint.String(),
				ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_PAN_EVALUATION.String(),
				ShowCtaLoader:   true,
			}),
		},
	}
	homeCta := &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         homeCtaTxt,
		DisplayTheme: deeplinkPb.Cta_TERTIARY,
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
		},
	}

	var retryCtaList = []*deeplinkPb.Cta{
		{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         vkyc.RetryCtaTxt,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Deeplink: vkyc.GetVKYCNextActionDeeplink(&deeplinkPb.GetVKYCNextActionApiScreenOptions{
				EntryPoint:      entryPoint.String(),
				ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_PAN_EVALUATION.String(),
				ShowCtaLoader:   true,
			}),
		},
	}

	var okCtaList = []*deeplinkPb.Cta{
		{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         vkyc.OkGotItCtaTxt,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
			},
		},
	}

	var knowMoreCtaList = []*deeplinkPb.Cta{
		{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         vkyc.KnowMoreCtaTxt,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_FAQ_CATEGORY,
				ScreenOptions: &deeplinkPb.Deeplink_FaqCategoryOptions{
					FaqCategoryOptions: &deeplinkPb.FaqCategoryOptions{
						CategoryId: vkycConf.FAQCategoryId(),
					},
				},
			},
		},
	}

	var faqWebPageCtaList = vkyc.FaqWebPageCtaList

	if !vkyc.IsNonHomeEntryPoint(entryPoint) {
		ctaList = append(ctaList, homeCta)
		// add home cta in vkyc error screen for ios only
		appPlatform, _ := epificontext.AppPlatformAndVersion(ctx)
		if appPlatform == commontypes.Platform_IOS {
			retryCtaList = append(retryCtaList, homeCta)
			knowMoreCtaList = append(knowMoreCtaList, homeCta)
		}
	}

	logger.Info(ctx, "vkyc new error screen is enabled")
	if len(karzaCallStatuses) == 0 {
		return vkyc.ConstructVkycErrorScreen(vesCallFailedTitle, vesCallFailedSubtitle, agentUnavailableImageUrl, retryCtaList, nil, hideBackButton)
	}

	title, ok := vkyc.CallFailSubStatusToTitle[karzaCallStatuses[0].GetSubStatus()]
	if !ok {
		title = vkyc.VesCallFailedTitle
	}
	subtitle, ok := vkyc.CallFailSubStatusToSubtitle[karzaCallStatuses[0].GetSubStatus()]
	if !ok {
		subtitle = vkyc.VesCallFailedSubtitle
	}
	imageUrl, ok := vkyc.CallFailSubStatusToImage[karzaCallStatuses[0].GetSubStatus()]
	if !ok {
		imageUrl = agentUnavailableImageUrl
	}
	if karzaCallStatuses[0].GetSubStatus() == beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_CUSTOMER_BLOCKED {
		timeAfterAllowed := latestCustInfo.GetTransactionMetadata().GetKycDate().AsTime().Add(vkycConf.PerformEkycAfter()).Add(1 * time.Hour).In(datetime.IST).Format("02 Jan, 3 PM")
		subtitle = fmt.Sprintf(subtitle, timeAfterAllowed)
	}
	if karzaCallStatuses[0].GetSubStatus() == beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_LANGUAGE_BARRIER {
		title = fmt.Sprintf(title, preferredLanguage)
	}
	instructionList, ok := vkyc.CallFailSubStatusToInstructionList[karzaCallStatuses[0].GetSubStatus()]
	var instructionItems []*deeplinkPb.InstructionItem
	for _, instruction := range instructionList {
		switch {
		case instruction.IsWeblink:
			weblinkExpiryText := vkyc.GetWeblink(latestCustInfo.GetTransactionMetadata().GetKycDate(), vkycConf.PerformEkycAfter())
			weblinkBlock := vkyc.PopulateWeblinkBlock(latestCustInfo.GetTransactionMetadata().GetWeblink(), weblinkExpiryText)
			instructionItems = append(instructionItems, vkyc.ConstructInstructionItemBlock(instruction.Text, weblinkBlock))
		case instruction.Link != "":
			weblinkBlock := vkyc.PopulateWeblinkBlock(instruction.Link, "")
			instructionItems = append(instructionItems, vkyc.ConstructInstructionItemBlock(instruction.Text, weblinkBlock))
		default:
			instructionItems = append(instructionItems, vkyc.ConstructInstructionItemBlock(instruction.Text, nil))
		}
	}

	if lo.Contains[beVkycPb.VKYCKarzaCallInfoSubStatus](vkyc.TerminalFailureReasons, karzaCallStatuses[0].GetSubStatus()) {
		return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, vkyc.HomeCtaList, instructionItems, true)
	}

	if vkyc.IsDuringOnbEntryPoint(entryPoint) {
		switch karzaCallStatuses[0].GetSubStatus() {
		case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FATHER_MOTHER_NAME_MISMATCH:
			dl := onboardingPkg.GetParentsNameGetterDL(onbPb.Feature_FEATURE_UNSPECIFIED, nil, nil)
			var editParentsNameCtaList = []*deeplinkPb.Cta{
				{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "edit your parents name",
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Deeplink:     dl,
				},
			}
			return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, editParentsNameCtaList, instructionItems, true)
		// during onboarding entry point return retry vkyc
		case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED_ON_AGENT_SIDE,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_INTERNET_WEAK,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FAILED_BLACK_SCREEN,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_FACE_NOT_VISIBLE,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_NOT_AUDIBLE,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_AGENT_DISCONNECTED,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_MOVING_CONSTANTLY,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FAILED_CAMERA_AUDIO,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_OCR_CAPTURE_FAILED,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_DURATION_TIMEOUT,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_WILL_RECALL,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_NUMBER_MISMATCH,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_OTHER,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CAMERA_RELATED_ISSUE,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_LOCATION_ACCESS_ISSUE,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_IP_RISK_DETECTED,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_LANGUAGE_BARRIER,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_VPN_PROXY_DETECTED:
			return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, retryCtaList, instructionItems, hideBackButton)
		case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_UNABLE_TO_FOLLOW_INSTRUCTIONS,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_BEHAVIOUR_ISSUES,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_UNAWARE_ABOUT_VKYC:
			return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, faqWebPageCtaList, nil, hideBackButton)
		// these are terminal error during onboarding we won't show cta
		case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_DUPLICATE_INVALID_TAMPERED_PAN,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_WITHOUT_PAN,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_HAS_EPAN_SOFTCOPY_DIGILOCKER,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FAILED_MINOR_PAN,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INVALID_OR_TAMPERED_PAN,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_DOB_MISMATCH,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_AADHAR_PHOTO_MATCH_PER_LOW,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PHOTO_UNABLE_TO_DETERMINE,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_PHOTO_MATCH_PER_LOW,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INCOMPATIBLE_CUSTOMER_DEVICE,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_PEN_PAPER_ISSUE,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FORGED_KYC_DOCUMENTS,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_IS_FRAUDULENT,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_CUSTOMER_BLOCKED,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_LOCATION_OUTSIDE_INDIA,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_IP_OUTSIDE_INDIA,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_AGENT_DECLINED_CALL,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_AGENT_DID_NOT_INITIATE_CALL,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INCOMPATIBLE_BROWSER,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_WAIT_TIME_EXCEEDS_THRESHOLD,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_CALL_ALREADY_COMPLETED,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_AADHAAR_VALIDITY_EXPIRED,
			beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SYSTEM_TIME_INACCURATE:
			return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, faqWebPageCtaList, instructionItems, hideBackButton)
		// for default error we will not show retry CTA
		default:
			return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, retryCtaList, nil, hideBackButton)
		}
	}

	switch karzaCallStatuses[0].GetSubStatus() {
	case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED_ON_AGENT_SIDE,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_INTERNET_WEAK,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FAILED_BLACK_SCREEN,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_FACE_NOT_VISIBLE,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_NOT_AUDIBLE,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_AGENT_DISCONNECTED,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_MOVING_CONSTANTLY,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FAILED_CAMERA_AUDIO,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_OCR_CAPTURE_FAILED,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_DURATION_TIMEOUT,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_WILL_RECALL,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CAMERA_RELATED_ISSUE,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_OTHER,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_LOCATION_ACCESS_ISSUE,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_LANGUAGE_BARRIER,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_IP_RISK_DETECTED,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_VPN_PROXY_DETECTED:
		return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, retryCtaList, instructionItems, hideBackButton)

	case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_DUPLICATE_INVALID_TAMPERED_PAN,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_WITHOUT_PAN,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_HAS_EPAN_SOFTCOPY_DIGILOCKER,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FAILED_MINOR_PAN,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INVALID_OR_TAMPERED_PAN,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_DOB_MISMATCH,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_AADHAR_PHOTO_MATCH_PER_LOW,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PHOTO_UNABLE_TO_DETERMINE,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_PAN_PHOTO_MATCH_PER_LOW,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_CUSTOMER_BLOCKED,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INCOMPATIBLE_CUSTOMER_DEVICE,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_LOCATION_OUTSIDE_INDIA,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_IP_OUTSIDE_INDIA,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_AGENT_DECLINED_CALL,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INCOMPATIBLE_BROWSER,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_AGENT_DID_NOT_INITIATE_CALL,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_WAIT_TIME_EXCEEDS_THRESHOLD,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SYSTEM_TIME_INACCURATE:
		return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, okCtaList, instructionItems, hideBackButton)

	case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FATHER_MOTHER_NAME_MISMATCH,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_PEN_PAPER_ISSUE,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_FORGED_KYC_DOCUMENTS,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_IS_FRAUDULENT,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_CALL_ALREADY_COMPLETED,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_AADHAAR_VALIDITY_EXPIRED:
		return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, okCtaList, nil, hideBackButton)

	case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_USER_UNABLE_TO_FOLLOW_INSTRUCTIONS,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_BEHAVIOUR_ISSUES,
		beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_UNAWARE_ABOUT_VKYC:
		return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, knowMoreCtaList, nil, hideBackButton)

	default:
		return vkyc.ConstructVkycErrorScreen(title, subtitle, imageUrl, retryCtaList, nil, hideBackButton)
	}
}

func getAgentNotAvailableDeeplink(ctx context.Context, entryPoint beVkycPb.EntryPoint, vkycConf *genconf.VKYC) *deeplinkPb.Deeplink {
	// only need to consume by IOS device
	hideBackButton := true
	ctas := []*deeplinkPb.Cta{
		{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         agentUnavailableCTA,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Deeplink: vkyc.GetVKYCNextActionDeeplink(&deeplinkPb.GetVKYCNextActionApiScreenOptions{
				EntryPoint:      entryPoint.String(),
				ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_PAN_EVALUATION.String(),
				ShowCtaLoader:   true,
			}),
		},
	}
	homeCta := &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         homeCtaTxt,
		DisplayTheme: deeplinkPb.Cta_TERTIARY,
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
		},
	}
	if !vkyc.IsNonHomeEntryPoint(entryPoint) {
		ctas = append(ctas, homeCta)
	}
	return vkyc.ConstructVkycErrorScreen(agentUnavailableTitle, agentUnavailableSubtitle, agentUnavailableImageUrl, ctas, nil, hideBackButton)
}
