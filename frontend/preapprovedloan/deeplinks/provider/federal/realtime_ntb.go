//nolint:dupl
package federal

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/frontend"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/helper"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

var fenEligibilityConsentPrivacyPolicyUrl = "https://www.federalbank.co.in/privacy-policy"

type RealTimeNtbFedProvider struct {
	*LoansProvider
}

var _ provider.IDeeplinkProvider = &RealTimeNtbFedProvider{}

func NewRealTimeNtbFedProvider(fedPlProvider *LoansProvider) *RealTimeNtbFedProvider {
	return &RealTimeNtbFedProvider{
		fedPlProvider,
	}
}

func (rtd *RealTimeNtbFedProvider) GetLoanHeader() *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB,
		Vendor:      palFeEnumsPb.Vendor_FEDERAL_BANK,
	}
}

func (rtd *RealTimeNtbFedProvider) GetLoanHeaderWithEventData(entryPoint palFeEnumsPb.EntryPoint) *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB,
		Vendor:      palFeEnumsPb.Vendor_FEDERAL_BANK,
		EventData:   &palFeEnumsPb.EventData{EntryPoint: entryPoint},
	}
}

func (rtd *RealTimeNtbFedProvider) GetLoanApplicationDetailsScreenDeepLink(ctx context.Context, actorId string, lh *palFeEnumsPb.LoanHeader, od *palBePb.GetOfferDetailsResponse) (*deeplinkPb.Deeplink, error) {
	dl, dlErr := rtd.GetLoanApplicationDetailsScreenV2DeepLink(ctx, actorId, lh, od, false)
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "error while getting base application details screen deeplink")
	}
	screenOptionsV2 := &palTypesPb.LoanApplicationDetailsScreen{}
	if err := dl.GetScreenOptionsV2().UnmarshalTo(screenOptionsV2); err != nil {
		return nil, errors.Wrap(err, "error while unmarshalling loan application details screen v2")
	}

	var components []*palTypesPb.LoanApplicationDetailsScreenComponent
	for _, v := range screenOptionsV2.GetComponents() {
		// we don't need consents in ntb hence appending everything else in the list
		if v.GetTncComponent() == nil {
			components = append(components, v)
		}
	}
	screenOptionsV2.Components = components

	newDl, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOAN_APPLICATION_DETAIL_SCREEN, screenOptionsV2)
	if err != nil {
		return nil, errors.Wrap(err, "error while creating fed ntb application details screen")
	}
	return newDl, nil
}

func (rtd *RealTimeNtbFedProvider) GetPreQualOfferCardMultiOfferScreenComponent(loanOffer *palBePb.LoanOffer, isFirstOffer bool, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error) {
	baseComp, err := rtd.BaseDeeplinkProvider.GetPreQualOfferCardMultiOfferScreenComponent(loanOffer, isFirstOffer, isLoansPreQualOfferFlowEnabled)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting pre qual offer card inside fed ntb provider")
	}
	if !isLoansPreQualOfferFlowEnabled {
		return baseComp, nil
	}
	consentDl, err := getPreBreLoanConsentScreen(rtd.GetLoanHeader(), loanOffer.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "error while creating pre bre loan consent screen")
	}
	baseComp.GetLoansCta().CtaAction = &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_Deeplink{Deeplink: consentDl}}
	return baseComp, nil
}

func (rtd *RealTimeNtbFedProvider) GetPreQualOfferLandingScreenLoansCta(loanOffer *palBePb.LoanOffer) (*palTypesPb.LoansCta, error) {
	consentDl, err := getPreBreLoanConsentScreen(rtd.GetLoanHeader(), loanOffer.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "error while creating pre bre loan consent screen")
	}
	return &palTypesPb.LoansCta{
		CtaContent: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Get approval",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		},
		CtaAction: &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_Deeplink{Deeplink: consentDl}},
	}, nil
}

func getPreBreLoanConsentScreen(lh *palFeEnumsPb.LoanHeader, loId string) (*deeplinkPb.Deeplink, error) {
	lh.DataOwner = palFeEnumsPb.Vendor_FEDERAL_BANK
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_CONSENT_V2_SCREEN, &palTypesPb.LoansConsentV2ScreenOptions{
		Header: &deeplink_screen_option.ScreenOptionHeader{FeedbackEngineInfo: &feHeaderPb.FeedbackEngineInfo{
			FlowIdDetails: &feHeaderPb.FlowIdentifierDetails{
				FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_CONSENT_V2_SCREEN.String(),
			},
		}},
		LoanHeader: lh,
		Flow:       palFeEnumsPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY,
		TopIcon:    commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/landing_federal_logo.png", 18, 72),
		Title:      helper.GetText("Confirm the following to apply for a loan", "#333333", "", commontypes.FontStyle_HEADLINE_L),
		Subtitle:   helper.GetText("Mandatory Step for Loan application evaluation", "#8D8D8D", "", commontypes.FontStyle_BODY_3),
		ConsentItems: []*widgetPb.CheckboxItem{
			{
				Id:          consent.ConsentType_CONSENT_TYPE_CREDIT_REPORT_PULL_BY_FEDERAL_LENDER.String(),
				DisplayText: helper.GetTextWithHtml(fmt.Sprintf("I hereby appoint Federal Bank as authorised representative to receive my credit information from CIBIL/Experian and agree to the <a href=\"%s\" style=\"color: #00B899\">privacy policy</a>\n</font> of Federal bank.", fenEligibilityConsentPrivacyPolicyUrl), "#333333", commontypes.FontStyle_BODY_4),
			},
			{
				Id:          consent.ConsentType_CONSENT_TYPE_DATA_SHARING_WITH_FEDERAL.String(),
				DisplayText: helper.GetText("I hereby provide my consent for collection of my personal data and sharing and storing the data with Federal Bank collected through Fi Money Application. The consent will allow Federal Bank and Epifi Technologies Pvt Ltd to preserve the audit trails and logs of entire onboarding journey for this process.", "#333333", "", commontypes.FontStyle_BODY_4),
			},
		},
		Cta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Confirm",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		},
		BgColor:        "#FFFFFF",
		ConsentBgColor: colors.ColorOnDarkHighEmphasis,
		LoansCta: &palTypesPb.LoansCta{
			CtaContent: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Confirm",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
			CtaAction: &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_CallRpc_{CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
				RpcName: palTypesPb.LoansCtaAction_RPC_NAME_APPLY_FOR_LOAN,
				CommonMetaData: &palTypesPb.LoansCtaAction_CallRpc_CommonMetaData{
					LoanHeader: lh,
					LoId:       loId,
				},
			}}},
		},
	})
}

func (rtd *RealTimeNtbFedProvider) GetLoanLandingScreenDeepLink(ctx context.Context, lh *palFeEnumsPb.LoanHeader, _ *provider.LandingInfoRequest) (*deeplinkPb.Deeplink, error) {
	// old landing screen is not supported for federal NTB program, returning force upgrade screen in such cases
	return rtd.GetForceUpgradeLandingResponse(epificontext.AppPlatformFromContext(ctx), "", "Please update your app to the latest version to continue your application", lh), nil
}

func (rtd *RealTimeNtbFedProvider) GetLoanDetailsScreenDeeplinkV2(ctx context.Context, lh *palFeEnumsPb.LoanHeader, res *palBePb.GetLoanDetailsResponse, dwnTimeResp *palBePb.GetDowntimeStatusResponse, prePayConfigMap *genConf.PrePayConfigMap, req *provider.GetLoanDetailsScreenDeeplinkV2Request) (*deeplinkPb.Deeplink, error) {
	baseDl, err := rtd.LoansProvider.GetLoanDetailsScreenDeeplinkV2(ctx, lh, res, dwnTimeResp, prePayConfigMap, req)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get Loan details base DL")
	}

	screenOptionsV2 := &palTypesPb.LoansOverviewScreenOptions{}
	if err = baseDl.GetScreenOptionsV2().UnmarshalTo(screenOptionsV2); err != nil {
		return nil, errors.Wrap(err, "error while unmarshalling loan details screen v2")
	}

	bottomSheetDl := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_BOTTOM_SHEET_INFO_VIEW, &frontend.BottomSheetInfoViewOptions{
		Header:   nil,
		Icon:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/fed_repayment_bottom_screen_icon.png", Width: 80, Height: 80},
		Title:    commontypes.GetTextFromStringFontColourFontStyle("Reach out to Federal Bank for repayments", "#313234", commontypes.FontStyle_HEADLINE_L),
		SubTitle: commontypes.GetTextFromStringFontColourFontStyle("Please reach out to Federal <NAME_EMAIL> for all repayment related queries", "#6A6D70", commontypes.FontStyle_BODY_S),
		Ctas: []*deeplinkPb.Cta{
			{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Ok, got it",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
		},
	})

	for _, div := range screenOptionsV2.GetTopSection().GetDivisions() {
		for _, comp := range div.GetComponents() {
			if comp.GetCardWithLineProgressAndCta() != nil {
				comp.GetCardWithLineProgressAndCta().Cta = uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Pre-pay EMI", colors.ColorLightPrimaryAction, commontypes.FontStyle_BUTTON_S)).
					WithContainerProperties(&uiPb.IconTextComponent_ContainerProperties{
						BgColor:       colors.ColorCharcoal,
						CornerRadius:  19,
						LeftPadding:   24,
						RightPadding:  24,
						TopPadding:    12,
						BottomPadding: 12,
					}).WithDeeplink(bottomSheetDl)
			}
		}
	}

	finalDl, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_OVERVIEW_SCREEN, screenOptionsV2)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create final dl")
	}

	return finalDl, nil
}
