package baseprovider

import (
	"github.com/mohae/deepcopy"
	"github.com/pkg/errors"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	commonTypes "github.com/epifi/be-common/api/typesv2/common"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	paltypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type GetApplyForLoanAlreadyExistScreenRequest struct {
	ActiveLr                *palBePb.LoanRequest
	ActiveLse               *palBePb.LoanStepExecution
	ActiveAccountId         string
	IsCancelSupported       bool
	ApplyForLoanReq         *palFePb.ApplyForLoanRequest
	CheckLoanEligibilityReq *palFePb.CheckLoanEligibilityRequest
}

func GetApplyForLoanAlreadyExistScreen(req *GetApplyForLoanAlreadyExistScreenRequest) (*deeplinkPb.Deeplink, error) {
	if req.IsCancelSupported && req.ActiveAccountId == "" {
		return getApplyForLoanAlreadyExistV1Screen(req)
	}
	ctaText := ""
	var infoItem *deeplinkPb.InfoItem
	if req.ActiveAccountId != "" {
		ctaText = "See active loan"
		infoItem = &deeplinkPb.InfoItem{
			Title: "You already have an active loan on Fi!",
			Desc:  "You will only be able to apply for this loan when your current loan is closed",
		}
	} else if req.ActiveLr != nil {
		ctaText = "See active application"
		infoItem = &deeplinkPb.InfoItem{
			Title: "You already have an active loan application!",
			Desc:  "In order to apply for this loan, please cancel any existing application and try again",
		}
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_ERROR_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanErrorScreenOptions{
			PreApprovedLoanErrorScreenOptions: &deeplinkPb.PreApprovedLoanErrorScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/vkyc-under-review.png",
				Details: []*deeplinkPb.InfoItem{
					infoItem,
				},
				Cta: &deeplinkPb.Cta{
					Type: deeplinkPb.Cta_CUSTOM,
					Text: ctaText,
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
					},
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
			},
		},
	}, nil
}

func getApplyForLoanAlreadyExistV1Screen(req *GetApplyForLoanAlreadyExistScreenRequest) (*deeplinkPb.Deeplink, error) {
	if req.ActiveLr == nil {
		return nil, errors.New("no active loan request found")
	}
	canCancel := false
	var continuePreviousDl *deeplinkPb.Deeplink
	if req.ActiveLr.GetType() == palBePb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY {
		canCancel = true
		continuePreviousDl = GetLoanApplicationStatusPollScreenDeepLink(helper.GetFeLoanHeaderByBeLoanHeader(&palBePb.LoanHeader{
			LoanProgram: req.ActiveLr.GetLoanProgram(),
			Vendor:      req.ActiveLr.GetVendor(),
		}), req.ActiveLr.GetId())
	} else {
		canCancel = IsApplicationCancellable(req.ActiveLr, req.ActiveLse)
		continuePreviousDl = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		}
	}

	if !canCancel {
		return GetApplicationNonCancellableBottomSheet(), nil
	}

	var rpcName paltypes.LoansCtaAction_RpcName
	var rpcReq *anyPb.Any
	var err error

	switch {
	case req.ApplyForLoanReq != nil:
		rpcName = paltypes.LoansCtaAction_RPC_NAME_APPLY_FOR_LOAN
		reqCopy := deepcopy.Copy(req.ApplyForLoanReq).(*palFePb.ApplyForLoanRequest)
		reqCopy.CancelCurrentLoanRequest = true
		rpcReq, err = anyPb.New(reqCopy)
		if err != nil {
			return nil, errors.Wrap(err, "cannot convert apply for loan request to any type")
		}
	case req.CheckLoanEligibilityReq != nil:
		rpcName = paltypes.LoansCtaAction_RPC_NAME_CHECK_LOAN_ELIGIBILITY
		reqCopy := deepcopy.Copy(req.CheckLoanEligibilityReq).(*palFePb.CheckLoanEligibilityRequest)
		reqCopy.CancelCurrentLoanRequest = true
		rpcReq, err = anyPb.New(reqCopy)
		if err != nil {
			return nil, errors.Wrap(err, "cannot convert apply for loan request to any type")
		}
	default:
		return nil, errors.New("unsupported api request")
	}

	icon := commonTypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/atl-pen-pad.png", 120, 120)
	title := commonTypes.GetTextFromStringFontColourFontStyle("Start a new loan application with a different loan offer?", "#313234", commonTypes.FontStyle_HEADLINE_XL)
	subTitle := commonTypes.GetTextFromStringFontColourFontStyle("Your previous application will be cancelled to start a new one with the offer you chose", "#929599", commonTypes.FontStyle_BODY_S)
	if req.ActiveLr.GetDetails().GetIsHardPullDone() {
		icon = commonTypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/credit-score.png", 160, 160)
		title = commonTypes.GetTextFromStringFontColourFontStyle("Start a new loan application with a different loan offer?", "#313234", commonTypes.FontStyle_HEADLINE_XL)
		subTitle = commonTypes.GetTextFromStringFontColourFontStyle("A hard inquiry has already been made on your credit report. Starting a new application may lead to multiple hard inquiries, which could impact your credit score", "#929599", commonTypes.FontStyle_BODY_S)
	}

	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_APPLICATION_CONFIRMATION_BOTTOM_SHEET, &paltypes.LoansApplicationConfirmationBottomSheetScreenOptions{
		VisualElement: icon,
		Title:         title,
		Subtitle:      subTitle,
		PrimaryCta: &paltypes.LoansCta{
			CtaContent: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Continue with previous application",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
			CtaAction: &paltypes.LoansCtaAction{Action: &paltypes.LoansCtaAction_Deeplink{Deeplink: continuePreviousDl}},
		},
		SecondaryCta: &paltypes.LoansCta{
			CtaContent: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Start new application",
				DisplayTheme: deeplinkPb.Cta_TEXT,
			},
			CtaAction: &paltypes.LoansCtaAction{Action: &paltypes.LoansCtaAction_CallRpc_{CallRpc: &paltypes.LoansCtaAction_CallRpc{
				RpcName:    rpcName,
				RpcRequest: rpcReq,
			}}},
		},
		BgColor: "#FFFFFF",
	})
}

func GetApplicationNonCancellableBottomSheet() *deeplinkPb.Deeplink {
	return deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_APPLICATION_CONFIRMATION_BOTTOM_SHEET, &paltypes.LoansApplicationConfirmationBottomSheetScreenOptions{
		VisualElement: commonTypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/callout-three-dots.png", 140, 140),
		Title:         commonTypes.GetTextFromStringFontColourFontStyle("Your loan application is under progress", "#313234", commonTypes.FontStyle_HEADLINE_XL),
		Subtitle:      commonTypes.GetTextFromStringFontColourFontStyle("Since you only have a few steps left, you cannot explore other offers at the moment", "#929599", commonTypes.FontStyle_BODY_S),
		PrimaryCta: &paltypes.LoansCta{
			CtaContent: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_DONE,
				Text:         "Ok, got it",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
		},
		BgColor: "#FFFFFF",
	})
}
