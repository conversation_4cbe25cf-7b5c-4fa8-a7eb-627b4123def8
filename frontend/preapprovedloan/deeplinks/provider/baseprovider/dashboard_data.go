package baseprovider

const ApplicationInProgress = "Application in progress"
const AutoPaySetupPending = "AutoPay setup pending. This is required to process your loan"

var (
	defaultEnrichData = &enrichData{
		title:   ApplicationStatusUnknownString,
		bgColor: ColorLightRed,
		message: "Unexpected error while processing your loan application. Please contact support.",
		icon:    IconCautionTriangle,
	}

	lrStatusCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to finish all the mandatory steps to get access to your pre-approved loan. Start now ➡️",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     false,
	}

	lrStatusCancelled = &enrichData{
		title:         ApplicationStatusCancelledString,
		bgColor:       ColorLightRed,
		message:       "Your loan application was cancelled. Please apply for a fresh one",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lrStatusManualIntervention = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Unexpected error while processing your loan application. Please contact support.",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusExpired = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "This loan application has expired. Don't worry, you can apply for loan again.",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lrStatusexpired = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "As you were unable to complete your Video KYC within 48 hours, this loan application has expired. Don't worry, you can apply for loan again.",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lrStatusKfsExpired = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "As you were unable to complete your E-sign within 48 hours, this loan application has expired. Don't worry, you can apply for loan again.",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseSubStatusApplicantCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Your applicant has been created. Click here to continue with your loan application",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseSubStatusEmploymentDetailsAdded = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Your employment details have been added. Click here to continue with your loan application",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseSubStatusAddressAdded = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to fill out employment details to proceed further.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseSubStatusMandateLinkFetched = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       AutoPaySetupPending,
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseSubStatusMandateFetchedFailed = &enrichData{
		title:         ApplicationStatusPausedString,
		bgColor:       ColorLimeYellow,
		message:       "The EMI auto-pay setup was not successful. This could be due to a technical reason. Please try again in some time.",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     true,
	}

	lseSubStatusMandateInitiatedAtVendor = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       "The auto-pay setup is in progress. We'll notify you once it is completed.",
		icon:          IconScheduled,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseSubStatusManualReview = &enrichData{
		title:         ApplicationStatusUnderReviewString,
		bgColor:       ColorLimeYellow,
		message:       "Your video is being reviewed. We need to make sure no one else is impersonating you. Give us some time to verify.",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusFailedSubStatusManualReview = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "We could not verify you in your video. You won't be able to access your pre-approved loan for now",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseBackgroundProcessInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseSubStatusRevisedLoan = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Please select amount and tenure as per your revised offer to continue",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseSubStatusCkycverified = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Please confirm your employment details",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseSubStatusCkycOtpVerificationTemporaryBlockUser = &enrichData{
		title:     ApplicationStatusPendingString,
		bgColor:   ColorLimeYellow,
		message:   "CKYC OTP verification failed. Please retry after 24 hours.",
		icon:      IconPending,
		canCancel: true,
	}

	lseStatusLivenessInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "We need to make sure it's you. Complete your verification and help us ensure your loan is not being taken by someone else",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusReferencesInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to fill out your references details to proceed further.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusLivenessCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "We need to make sure it's you. Complete your verification and help us ensure your loan is not being taken by someone else",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusLivenessFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Your verification was not approved. We could not verify that it was you in the video",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusLivenessSuccess = &enrichData{
		title:         ApplicationStatusUnderReviewString,
		bgColor:       ColorLimeYellow,
		message:       "We are verifying your video. We'll notify you once the verification is successful",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusCkycInProgress = &enrichData{
		title:         ApplicationStatusUnderReviewString,
		bgColor:       ColorLimeYellow,
		message:       "KYC details fetched successfully. You need to confirm these details to proceed.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusCkycCreated = &enrichData{
		title:         ApplicationStatusUnderReviewString,
		bgColor:       ColorLimeYellow,
		message:       "Your KYC check is in progress. We’ll notify you once it is complete.",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusCkycFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Your KYC record was not found. Hence we couldn't process this application.",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusCkycSuccess = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       "Your KYC has been approved! Congratulations! Go ahead and complete your application",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusMandateInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       AutoPaySetupPending,
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusMandateCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       AutoPaySetupPending,
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusMandateFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "The auto-pay setup could not be completed. Please try again.",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusMandateSuccess = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       "The auto-pay setup is in progress. We'll notify you once it is completed.",
		icon:          IconScheduled,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     true,
	}

	lseStatusApplicantCreationInProgress = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     true,
	}

	lseStatusApplicantCreationCreated = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     true,
	}

	lseStatusApplicantCreationFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Your application is no longer valid. To retain validity, you need to reapply for your loan",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusApplicantCreationSuccess = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusDrawdownInProgress = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     true,
	}

	lseStatusDrawdownCreated = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     true,
	}

	lseStatusDrawdownFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Your application is no longer valid. To retain validity, you need to reapply for your loan",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusDrawdownSuccess = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       "Your application is being processed",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusDisbursementInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Your application is being processed. We'll let you know once it's done",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusDisbursementCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Your application is being processed. We'll let you know once it's done",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusDisbursementFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Unexpected error while processing your loan application. Please contact support.",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusUploadFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Unexpected error while processing your loan application. Please try again!",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusDisbursementSuccess = &enrichData{
		title:         ApplicationStatusDisbursedString,
		bgColor:       ColorMintGreen,
		message:       "Your loan amount has been disbursed. You should receive the amount in your Federal Bank Savings Account on Fi. Tap for details",
		icon:          IconTick,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusAccountCreationInProgress = &enrichData{
		title:         ApplicationStatusDisbursedString,
		bgColor:       ColorMintGreen,
		message:       "Your loan amount has been disbursed. You should receive the amount in your Federal Bank Savings Account on Fi. Tap for details",
		icon:          IconTick,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusAccountCreationCreated = &enrichData{
		title:         ApplicationStatusDisbursedString,
		bgColor:       ColorMintGreen,
		message:       "Your loan amount has been disbursed. You should receive the amount in your Federal Bank Savings Account on Fi. Tap for details",
		icon:          IconTick,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusAccountCreationFailed = &enrichData{
		title:         ApplicationStatusUnknownString,
		bgColor:       ColorLightRed,
		message:       "Unexpected error while processing your loan application. Please contact support.",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusAccountCreationSuccess = &enrichData{
		title:         ApplicationStatusDisbursedString,
		bgColor:       ColorMintGreen,
		message:       "Hurray! Your loan account has been created.",
		icon:          IconTick,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusFetchOfferInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusKycCheckInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Review your KYC details to proceed",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusFetchOfferCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusBreCheckFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Unable to process your loan! Sorry, your application did not meet our partner regulated entity's requirements",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusFetchOfferFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Unable to process your loan! Sorry, your application did not meet our partner regulated entity's requirements",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusFetchOfferSuccess = &enrichData{
		title:         ApplicationStatusInProgressStringGreen,
		bgColor:       ColorMintGreen,
		message:       "Your application is being processed",
		icon:          IconTick,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusLatLongInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusLatLongCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusLatLongFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Unable to process your loan! Sorry, your application did not meet our partner regulated entity's requirements",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusLatLongSuccess = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusRiskCheckInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusRiskCheckCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusRiskCheckFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Unable to process your loan! Sorry, your application did not meet our partner regulated entity's requirements",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusRiskCheckSuccess = &enrichData{
		title:         ApplicationStatusInProgressStringGreen,
		bgColor:       ColorMintGreen,
		message:       "Your application is being processed",
		icon:          IconTick,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusKfsInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Your loan is ready, eSign the documents to get your cash",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
		ctaText:       "eSign Now",
	}

	lseStatusKfsCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Your loan is ready, eSign the documents to get your cash",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
		ctaText:       "eSign Now",
	}

	lseStatusKfsFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Your e-sign is no longer valid. To retain validity, you need to reapply for your loan",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusKfsSuccess = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       "Your application is being processed. We'll let you know once it's done.",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusAddDetailsInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to confirm your address details to proceed further.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusAddDetailsCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to confirm your address details to proceed further.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusAddDetailsFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Your application is no longer valid. To retain validity, you need to reapply for your loan",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusAddDetailsSuccess = &enrichData{
		title:         ApplicationStatusInProgressStringGreen,
		bgColor:       ColorMintGreen,
		message:       "Your details are completed! Your application is being processed",
		icon:          IconTick,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusProfileValidationInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to confirm your DOB to proceed further.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusProfileValidationCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to confirm your PAN details to proceed further.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusProfileValidationFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Our vendor couldn't verify your PAN details",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusProfileValidationSuccess = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to confirm your CKYC details to proceed further.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseSubStatusPanAdded = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to add your Date of Birth details to complete your loan application",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: true,
		canCancel:     false,
	}
	lseStatusAddBankingInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to confirm your banking details to proceed further.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}
	lseStatusAddAadhaarInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "You need to enter your aadhaar details to proceed further.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}
	lseSubStatusDifferentOffer = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Unable to process your loan! Sorry, your offer did not meet our requirements",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}
	lseStatusUpdateLeadInProgress = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     true,
	}
	lseStatusUpdateLeadCreated = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     true,
	}
	lseStatusUpdateLeadFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Your application is no longer valid. To retain validity, you need to reapply for your loan",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseSubStatusVkycMakerApproved = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Your VKyc approval is pending. We will inform you once it's approved",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseSubStatusVkycVerificationFailed = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Your VKyc check failed, please retry to continue with your application",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseSubStatusContactabilityCooloff = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       "Max limit reached for entering OTP. Retry in 24 hours.",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     true,
	}

	lseStatusVkycInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Next up: Video-KYC",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
		ctaText:       "Finish application",
	}

	lseSubStatusVkycInReview = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Please wait, our partner's best agents are reviewing your documents",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     true,
	}

	lseSubStatusVkycDataRedactionInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Your Documents are in review, It usually takes upto 2 hours to get reviewed.",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusVkycFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Your Vkyc check failed",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusCreditReportFetchCreated = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       "Please proceed with credit report fetch to complete your application",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusCreditReportFetchInProgress = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       "Your credit report fetch is in progress",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusContactabilityInProgress = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       "Get your loan NOW by adding alternate phone no.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: true,
		canCancel:     false,
		ctaText:       "Add Number",
	}

	lseStatusContactabilitySuccess = &enrichData{
		title:         ApplicationStatusInProgressString,
		bgColor:       ColorLimeYellow,
		message:       "Alternate number added successfully",
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusContactabilityFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLimeYellow,
		message:       "Your alternate number addition failed",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusPennyDropInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Verifying your banking details, please make sure the details are correct to proceed further",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: true,
		canCancel:     true,
	}

	lseStatusPennyDropCreated = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusPennyDropFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Unable to process your loan! Sorry, your application did not meet our partner regulated entity's requirements",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusPennyDropSuccess = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusDefaultAml = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLimeYellow,
		message:       "Your Documents are in review. It generally takes upto 8 hours to verify the documents.",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: false,
		canCancel:     true,
	}

	lseStatusCommonFailed = &enrichData{
		title:         ApplicationStatusFailedString,
		bgColor:       ColorLightRed,
		message:       "Unable to process your loan! Sorry, your application did not meet our partner regulated entity's requirements",
		icon:          IconCautionTriangle,
		statusPollDl:  false,
		showReloadCta: false,
		canCancel:     false,
	}

	lseStatusNoStatusPollInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLightBlue,
		message:       ApplicationInProgress,
		icon:          IconPending,
		statusPollDl:  false,
		showReloadCta: true,
		canCancel:     false,
	}

	lseStatusStatusPollInProgress = &enrichData{
		title:         ApplicationStatusPendingString,
		bgColor:       ColorLightBlue,
		message:       "Almost done! Complete the last few steps to get the loan",
		icon:          IconPending,
		statusPollDl:  true,
		showReloadCta: true,
		canCancel:     false,
		ctaText:       "Continue",
	}
)
