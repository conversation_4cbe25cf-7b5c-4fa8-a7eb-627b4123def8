// nolint:funlen,dupl
package fiftyfin

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"sort"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	palUi "github.com/epifi/gamma/frontend/preapprovedloan/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

func (lp *LamfProvider) offerInfoPage(ctx context.Context, lh *palPbFeEnums.LoanHeader, offer *palPb.LoanOffer, activeOffer bool) (*deeplink.Deeplink, error) {
	cmpVal, cmpErr := moneyPkg.CompareV2(offer.GetOfferConstraints().GetMaxLoanAmount(), offer.GetOfferConstraints().GetMinLoanAmount())
	if cmpErr != nil {
		return nil, errors.Wrap(cmpErr, "error while comparing portfolio max loan amt and min amt")
	}
	lowPortfolioOffer := false
	if cmpVal == -1 {
		lowPortfolioOffer = true
	}

	processingInfo := offer.GetProcessingInfo()
	offerConstraints := offer.GetOfferConstraints()

	maxLoanAmount := offerConstraints.GetMaxLoanAmount()
	interestRate := decimal.NewFromFloat(processingInfo.GetInterestRate()[0].GetPercentage()).Round(2)

	mfPortfolio := offerConstraints.GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint()

	allIsins := lp.getAllIsins(mfPortfolio)
	fundDetailsMap, err := lp.getMutualFundDetails(ctx, allIsins)
	if err != nil {
		return nil, err
	}
	pfEligibilityPage, totalFundsPresent, err := lp.portfolioEligibilityPage(ctx, lh, offerConstraints.GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint(), fundDetailsMap)
	if err != nil {
		return nil, fmt.Errorf("error while generating portfolio eligibility details page : %w", err)
	}

	activeLoan, err := lp.isActiveLoan(ctx, offer.GetActorId())
	if err != nil {
		return nil, fmt.Errorf("error while checking PL loan account status : %w", err)
	}

	activePlError := &widget.ImageTitleSubtitleElement{}
	if activeLoan {
		activePlError = &widget.ImageTitleSubtitleElement{
			IconImage:       commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/warning_i_button.png").WithHeight(24).WithWidth(24),
			TitleText:       commontypes.GetPlainStringText("Sorry, you cannot proceed since you already have an active loan on Fi. We’re working on enabling this for you soon").WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#6D3149"),
			BackgroundColor: "#F8E5EB",
		}
	}

	offerSelectionScreen, err := deeplinkv3.GetDeeplinkV3(deeplink.Screen_LOANS_USER_LOAN_DETAILS_SELECTION_SCREEN, &palTypesPb.LoanDetailsSelectionScreen{
		LoanHeader: lh,
		OfferId:    offer.GetId(),
	})
	if err != nil {
		return nil, fmt.Errorf("error while generating offer selection screen deeplink : %w", err)
	}
	cta := fullScreenButton("Choose loan amount", !activeLoan, offerSelectionScreen)

	var offerLottieDetails *palTypesPb.LoanOfferTicketViewComponent_TicketBox_LottieDetails
	var offerLottieUrl string
	var offerAnimation palTypesPb.LoanOfferTicketViewComponent_TicketBox_TextAnimationType
	var amountFontColor, interestFontColor, interestBgColor string
	switch {
	case lowPortfolioOffer:
		eligibilityScreen, err1 := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_CHECK_ELIGIBILITY_SCREEN, &palTypesPb.LoansCheckEligibilityScreen{
			LoanHeader: lh,
		})
		if err1 != nil {
			return nil, fmt.Errorf("error while generating check eligibility screen : %w", err1)
		}
		cta = fullScreenButton("Edit account details", !activeLoan, eligibilityScreen)
		offerLottieUrl = palUi.LamfLowPortfolioOfferLottie
		offerLottieDetails = &palTypesPb.LoanOfferTicketViewComponent_TicketBox_LottieDetails{
			StartFrame: 0,
			EndFrame:   2,
		}
		offerAnimation = palTypesPb.LoanOfferTicketViewComponent_TicketBox_TEXT_ANIMATION_TYPE_UNSPECIFIED
		amountFontColor = "#929599"
		interestFontColor = "#B2B5B9"
		interestBgColor = "#F6F9FD"
	case !activeOffer:
		eligibilityScreen, err1 := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_CHECK_ELIGIBILITY_SCREEN, &palTypesPb.LoansCheckEligibilityScreen{
			LoanHeader: lh,
		})
		if err1 != nil {
			return nil, fmt.Errorf("error while generating check eligibility screen : %w", err1)
		}
		cta = fullScreenButton("Refresh your portfolio", !activeLoan, eligibilityScreen)
		offerLottieUrl = palUi.LamfExpiredOfferLottie
		offerLottieDetails = &palTypesPb.LoanOfferTicketViewComponent_TicketBox_LottieDetails{
			StartFrame: 0,
			EndFrame:   2,
		}
		offerAnimation = palTypesPb.LoanOfferTicketViewComponent_TicketBox_TEXT_ANIMATION_TYPE_UNSPECIFIED
		amountFontColor = "#929599"
		interestFontColor = "#B2B5B9"
		interestBgColor = "#F6F9FD"
	default:
		offerLottieUrl = palUi.LamfOfferLottie
		offerLottieDetails = &palTypesPb.LoanOfferTicketViewComponent_TicketBox_LottieDetails{
			StartFrame: 0,
			EndFrame:   90,
		}
		offerAnimation = palTypesPb.LoanOfferTicketViewComponent_TicketBox_TEXT_ANIMATION_TYPE_BOTTOM_UP
		amountFontColor = "#313234"
		interestFontColor = "#2D5E6E"
		interestBgColor = "#E4F1F5"
	}
	loanEligibilityDl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_CHECK_ELIGIBILITY_SCREEN, &palTypesPb.LoansCheckEligibilityScreen{
		LoanHeader: lh,
	})

	if dlErr != nil {
		logger.Error(ctx, "failed to create loan eligibility deeplink", zap.Error(dlErr))
		return nil, fmt.Errorf("failed to create loan eligibility deeplink %w", dlErr)
	}
	var components []*palTypesPb.LoansScreenUiComponents
	components = append(components, []*palTypesPb.LoansScreenUiComponents{
		{
			Component: &palTypesPb.LoansScreenUiComponents_LoanOfferTicketViewComponent{
				LoanOfferTicketViewComponent: &palTypesPb.LoanOfferTicketViewComponent{
					OfferDetailsCard: &palTypesPb.LoanOfferTicketViewComponent_TicketBox{
						Title:       commontypes.GetPlainStringText("Maximum loan amount").WithFontStyle(commontypes.FontStyle_HEADLINE_XS).WithFontColor("#929599"),
						OfferAmount: types.GetFromBeMoney(maxLoanAmount),
						OfferAmountText: ui.NewITC().WithTexts(
							commontypes.GetPlainStringText("₹").WithFontStyle(commontypes.FontStyle_NUMBER_2XL).WithFontColor("#8D8D8D"),
							commontypes.GetPlainStringText(fmt.Sprintf("%s", moneyPkg.ToDisplayStringInIndianFormat(maxLoanAmount, 0, false))).WithFontStyle(commontypes.FontStyle_NUMBER_3XL).WithFontColor(amountFontColor),
						),
						Interest: commontypes.GetPlainStringText(fmt.Sprintf("@ %s%% interest", interestRate.String())).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(interestFontColor),
						InterestSection: ui.NewITC().WithTexts(
							commontypes.GetPlainStringText(fmt.Sprintf("@ %s%% interest", interestRate.String())).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#2D5E6E"),
						).WithContainer(0, 0, 30, interestBgColor).WithContainerPaddingSymmetrical(16, 6),
						LearnMoreButton: ui.NewITC().WithTexts(
							commontypes.GetPlainStringText("Learn how the loan value is calculated").WithFontStyle(commontypes.FontStyle_SUBTITLE_XS).WithFontColor("#00B899"),
						).WithLeftImageUrlHeightAndWidth(palUi.HelpCircleImage, 16, 16).WithLeftImagePadding(4).WithContainerPadding(0, 20, 0, 16).WithDeeplink(
							loanValueCalculationInfoPopup()).WithLeftVisualElementUrlHeightAndWidth(palUi.HelpCircleImage, 16, 16),
						TicketBoxBgColor: widget.GetBlockBackgroundColour("#FFFFFF"),
						LottieDetails:    offerLottieDetails,
						Animation:        offerAnimation,
					},
					TopMargin: 16,
					BgVisual: commontypes.GetVisualElementLottieFromUrl(offerLottieUrl).WithProperties(&commontypes.VisualElementProperties{
						Width:  394,
						Height: 436,
					}).WithRepeatCount(1),
				},
			},
		},
		{
			Component: &palTypesPb.LoansScreenUiComponents_ClickableTileComponent{
				ClickableTileComponent: &palTypesPb.ClickableTileComponent{
					Title: commontypes.GetPlainStringText("See eligible\nMutual Funds").WithFontStyle(commontypes.FontStyle_HEADLINE_S).WithFontColor("#313234"),
					Cta: ui.NewITC().WithTexts(
						commontypes.GetPlainStringText("See funds").WithFontStyle(commontypes.FontStyle_SUBTITLE_XS).WithFontColor("#00B899"),
					).WithRightImageUrlHeightAndWidth(palUi.ChevronImage, 20, 20).WithDeeplink(pfEligibilityPage).WithRightImagePadding(2).WithRightVisualElementUrlHeightAndWidth(palUi.ChevronImage, 20, 20),
					TopMargin: 32,
				},
			},
		},
		{
			Component: &palTypesPb.LoansScreenUiComponents_AddAccountComponent{
				AddAccountComponent: &palTypesPb.AddAccountComponent{
					FetchedFundsDetails: commontypes.GetPlainStringText(fmt.Sprintf("Total %d Mutual Funds found", totalFundsPresent)).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#313234"),
					FetchMoreFundsText:  commontypes.GetPlainStringText("Some funds are missing? ").WithFontStyle(commontypes.FontStyle_SUBTITLE_XS).WithFontColor("#929599"),
					AddPhoneNumber: ui.NewITC().WithTexts(
						commontypes.GetPlainStringText(fmt.Sprintf("Edit account details")).WithFontStyle(commontypes.FontStyle_SUBTITLE_XS).WithFontColor("#00B899"),
					).WithDeeplink(loanEligibilityDl),
					TopMargin: 24,
				},
			},
		},
		{
			Component: lp.getPartnerLogoVisualElementComponent(36),
		},
		{
			Component: &palTypesPb.LoansScreenUiComponents_VisualElement{
				VisualElement: &palTypesPb.VisualElementComponent{
					VisualElement: commontypes.GetVisualElementImageFromUrl(palUi.SeparatorLineImage).WithProperties(
						&commontypes.VisualElementProperties{Width: 60, Height: 2}),
					TopMargin: 40,
				},
			},
		},
		{
			Component: lp.getProductFeatureStackedComponent(40),
		},
		{
			Component: lp.getProductFeatureScrollableCardsComponent(48),
		},
		{
			Component: lp.getFaqScrollableCardViewComponent(48),
		},
	}...)

	return deeplinkv3.GetDeeplinkV3(deeplink.Screen_LOANS_VIEW_OFFER_SCREEN, &palTypesPb.LamfShowOfferScreen{
		LoanHeader:    lh,
		Components:    components,
		PrimaryButton: cta,
		ErrorMessage:  activePlError,
		ScreenBgColor: widget.GetBlockBackgroundColour("#E6E9ED"),
		RefreshOfferComponent: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText("REFRESH").WithFontColor("#313234").WithFontStyle(commontypes.FontStyle_HEADLINE_XS),
		).WithLeftVisualElementUrlHeightAndWidth(palUi.RefreshOfferIcon, 18, 18).
			WithLeftImagePadding(4).WithContainer(0, 0, 20, "").
			WithContainerPaddingSymmetrical(12, 8).WithBorder("#D6D9DD", 1).WithDeeplink(loanEligibilityDl),
	})
}

func (lp *LamfProvider) portfolioEligibilityPage(ctx context.Context, lh *palPbFeEnums.LoanHeader,
	pf *palPb.MfPortfolioConstraint, fundDetailsMap map[string]*mutualFund) (*deeplink.Deeplink, int, error) {
	totalPfValue, eligiblePfValue, inEligiblePfValue := moneyPkg.ZeroINR(), moneyPkg.ZeroINR(), moneyPkg.ZeroINR()
	eligibleFundsDetails, err := lp.getAggFundDetails(pf.GetApprovedHoldings(), fundDetailsMap)
	if err != nil {
		return nil, 0, fmt.Errorf("error while generating aggregated eligible funds details")
	}
	unapprovedFunds := append(pf.GetUnapprovedHoldings(), pf.GetLockedHoldings()...)
	ineligibleFundsDetails, err := lp.getAggFundDetails(unapprovedFunds, fundDetailsMap)
	if err != nil {
		return nil, 0, fmt.Errorf("error while generating aggregated ineligible funds details : %w", err)
	}
	for _, fundAggDetails := range eligibleFundsDetails {
		totalPfValue.Pb, err = moneyPkg.Sum(totalPfValue.GetPb(), fundAggDetails.totalMarketValue.GetPb())
		if err != nil {
			return nil, 0, fmt.Errorf("error while adding eligible pf value to total pf value")
		}
		eligiblePfValue.Pb, err = moneyPkg.Sum(eligiblePfValue.GetPb(), fundAggDetails.totalMarketValue.GetPb())
		if err != nil {
			return nil, 0, fmt.Errorf("error while calculating total eligible pf value")
		}
	}
	for _, fundAggDetails := range ineligibleFundsDetails {
		totalPfValue.Pb, err = moneyPkg.Sum(totalPfValue.GetPb(), fundAggDetails.totalMarketValue.GetPb())
		if err != nil {
			return nil, 0, fmt.Errorf("error while adding ineligible pf value to total pf value")
		}
		inEligiblePfValue.Pb, err = moneyPkg.Sum(inEligiblePfValue.GetPb(), fundAggDetails.totalMarketValue.GetPb())
		if err != nil {
			return nil, 0, fmt.Errorf("error while calculating total ineligible pf value")
		}
	}

	eligibleSchemeList := &palTypesPb.MfPortfolioEligibilityDetailsScreen_SchemeList{
		SchemeDetails:  make([]*palTypesPb.MfPortfolioEligibilityDetailsScreen_SchemeDetails, 0),
		NameColHeader:  commontypes.GetPlainStringText("Fund name").WithFontStyle(commontypes.FontStyle_SUBTITLE_XS).WithFontColor("#929599"),
		ValueColHeader: commontypes.GetPlainStringText("Market value").WithFontStyle(commontypes.FontStyle_SUBTITLE_XS).WithFontColor("#929599"),
		Heading: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(fmt.Sprintf("Eligible Funds • %s", moneyPkg.ToDisplayStringInIndianFormat(eligiblePfValue.GetPb(), 0, true))).
				WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#6A6D70"),
		).WithContainerPadding(0, 0, 0, 16),
	}

	ineligibleDetailsDl, err := lp.ineligibleFundInfoPage()
	if err != nil {
		return nil, 0, fmt.Errorf("error while generating ineligible funds info screen : %w", err)
	}

	ineligibleSchemeList := &palTypesPb.MfPortfolioEligibilityDetailsScreen_SchemeList{
		SchemeDetails:  make([]*palTypesPb.MfPortfolioEligibilityDetailsScreen_SchemeDetails, 0),
		NameColHeader:  commontypes.GetPlainStringText("Fund name").WithFontStyle(commontypes.FontStyle_SUBTITLE_XS).WithFontColor("#929599"),
		ValueColHeader: commontypes.GetPlainStringText("Market value").WithFontStyle(commontypes.FontStyle_SUBTITLE_XS).WithFontColor("#929599"),
		Heading: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(fmt.Sprintf("Ineligible Funds • %s", moneyPkg.ToDisplayStringInIndianFormat(inEligiblePfValue.GetPb(), 0, true))).
				WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#6A6D70"),
		).WithRightImageUrl(palUi.GreenQuestionInfoIcon).WithRightVisualElementUrlHeightAndWidth(palUi.GreenQuestionInfoIcon, 20, 20).WithRightImagePadding(4).
			WithContainerPadding(0, 0, 0, 16).WithDeeplink(ineligibleDetailsDl),
	}

	for _, fund := range eligibleFundsDetails {
		dl, err1 := lp.folioBreakupPage(ctx, lh, fund)
		if err1 != nil {
			return nil, 0, fmt.Errorf("error while generating folio breakup screen : %w", err1)
		}
		schemeDetails := &palTypesPb.MfPortfolioEligibilityDetailsScreen_SchemeDetails{
			Icon:       commontypes.GetImageFromUrl(fund.fundDetails.GetIcon()).WithHeight(44).WithWidth(44),
			Name:       commontypes.GetPlainStringText(fund.Name).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#313234"),
			Amount:     types.GetFromBeMoney(moneyPkg.TruncateNanos(fund.totalMarketValue.GetPb())),
			AmountText: commontypes.GetPlainStringText(fmt.Sprintf("%s", moneyPkg.ToDisplayStringInIndianFormat(moneyPkg.TruncateNanos(fund.totalMarketValue.GetPb()), 0, true))).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#313234"),
			Deeplink:   dl,
		}
		eligibleSchemeList.SchemeDetails = append(eligibleSchemeList.GetSchemeDetails(), schemeDetails)
	}

	for _, fund := range ineligibleFundsDetails {
		dl, err1 := lp.folioBreakupPage(ctx, lh, fund)
		if err1 != nil {
			return nil, 0, fmt.Errorf("error while generating folio breakup screen : %w", err1)
		}
		schemeDetails := &palTypesPb.MfPortfolioEligibilityDetailsScreen_SchemeDetails{
			Icon:     commontypes.GetImageFromUrl(fund.fundDetails.GetIcon()).WithHeight(44).WithWidth(44),
			Name:     commontypes.GetPlainStringText(fund.Name).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#B2B5B9"),
			Amount:   types.GetFromBeMoney(moneyPkg.TruncateNanos(fund.totalMarketValue.GetPb())),
			Deeplink: dl,
		}
		ineligibleSchemeList.SchemeDetails = append(ineligibleSchemeList.GetSchemeDetails(), schemeDetails)
	}
	// sort funds present in the eligible and ineligible funds lists
	sort.Slice(eligibleSchemeList.GetSchemeDetails(), func(i, j int) bool {
		return eligibleSchemeList.GetSchemeDetails()[i].GetAmount().GetUnits() > eligibleSchemeList.GetSchemeDetails()[j].GetAmount().GetUnits()
	})
	sort.Slice(ineligibleSchemeList.GetSchemeDetails(), func(i, j int) bool {
		return ineligibleSchemeList.GetSchemeDetails()[i].GetAmount().GetUnits() > ineligibleSchemeList.GetSchemeDetails()[j].GetAmount().GetUnits()
	})

	if len(ineligibleSchemeList.GetSchemeDetails()) == 0 {
		ineligibleSchemeList = nil
	}

	dl, err := deeplinkv3.GetDeeplinkV3(deeplink.Screen_LOANS_MF_PORTFOLIO_ELIGIBILITY_DETAILS_SCREEN, &palTypesPb.MfPortfolioEligibilityDetailsScreen{
		LoanHeader:     lh,
		PageTitle:      commontypes.GetPlainStringText("Your funds").WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#18191B"),
		PageHeading:    commontypes.GetPlainStringText("Total portfolio value").WithFontStyle(commontypes.FontStyle_SUBTITLE_XS).WithFontColor("#6A6D70"),
		PortfolioValue: types.GetFromBeMoney(moneyPkg.TruncateNanos(totalPfValue.GetPb())),
		PortfolioValueText: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText("₹").WithFontStyle(commontypes.FontStyle_NUMBER_XL).WithFontColor("#8D8D8D"),
			commontypes.GetPlainStringText(fmt.Sprintf("%s", moneyPkg.ToDisplayStringInIndianFormat(totalPfValue.GetPb(), 0, false))).WithFontStyle(commontypes.FontStyle_NUMBER_2XL).WithFontColor("#18191B"),
		),
		EligibleSchemes:   eligibleSchemeList,
		IneligibleSchemes: ineligibleSchemeList,
		ScreenBgColor:     widget.GetBlockBackgroundColour("#EFF2F6"),
	})
	if err != nil {
		return nil, 0, err
	}
	totalFundsPresent := len(eligibleSchemeList.GetSchemeDetails()) + len(ineligibleSchemeList.GetSchemeDetails())
	return dl, totalFundsPresent, nil
}

func (lp *LamfProvider) folioBreakupPage(_ context.Context, lh *palPbFeEnums.LoanHeader, fund *fundDetails) (*deeplink.Deeplink, error) {
	folioArr := make([]*palTypesPb.MfSchemeFolioBreakupScreen_FolioDetails, 0)
	for _, folio := range fund.folios {
		var detailsBelowDesc []*commontypes.Text
		if folio.GetPhoneNumber() != nil {
			detailsBelowDesc = append(detailsBelowDesc, commontypes.GetTextFromStringFontColourFontStyle(folio.GetPhoneNumber().ToSignedString(), "#929599", commontypes.FontStyle_HEADLINE_XS))
		}
		if folio.GetEmail() != "" {
			detailsBelowDesc = append(detailsBelowDesc, commontypes.GetTextFromStringFontColourFontStyle(folio.GetEmail(), "#929599", commontypes.FontStyle_HEADLINE_XS))
		}
		unit, nanos := moneyPkg.GetDisplayStringWithValueAndPrecision(moneyPkg.ParseFloat(folio.GetQuantity(), moneyPkg.RupeeCurrencyCode), 2, false,
			false, moneyPkg.IndianNumberSystem)
		folioArr = append(folioArr, &palTypesPb.MfSchemeFolioBreakupScreen_FolioDetails{
			FolioDesc: commontypes.GetPlainStringText(
				fmt.Sprintf("Folio No.- %s", folio.GetFolioNumber()),
			).WithFontStyle(commontypes.FontStyle_HEADLINE_S).WithFontColor("#333333"),
			Amount: types.GetFromBeMoney(folio.GetTotalAmount()),
			Units: commontypes.GetPlainStringText(
				fmt.Sprintf("%s units", unit+nanos),
			).WithFontStyle(commontypes.FontStyle_HEADLINE_XS).WithFontColor("#929599"),
			Details: detailsBelowDesc,
		})
	}
	return deeplinkv3.GetDeeplinkV3(deeplink.Screen_LOANS_MF_FOLIO_BREAKUP_SCREEN, &palTypesPb.MfSchemeFolioBreakupScreen{
		LoanHeader:    lh,
		Icon:          commontypes.GetImageFromUrl(fund.fundDetails.GetIcon()).WithHeight(72).WithWidth(72),
		Name:          commontypes.GetPlainStringText(fund.Name).WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#282828"),
		FolioDetails:  folioArr,
		ScreenBgColor: widget.GetBlockBackgroundColour("#E6E9ED"),
		Cta: &deeplink.Button{
			Text: commontypes.GetPlainStringText(fmt.Sprintf("Close")).WithFontStyle(commontypes.FontStyle_BUTTON_M).
				WithFontColor("#00B899").WithBgColor("#F7F9FA"),
			Padding: &deeplink.Button_Padding{
				LeftPadding:   24,
				RightPadding:  12,
				TopPadding:    24,
				BottomPadding: 12,
			},
			WrapContent: true,
			Cta: &deeplink.Cta{
				Type:         deeplink.Cta_CUSTOM,
				DisplayTheme: deeplink.Cta_SECONDARY,
				Status:       deeplink.Cta_CTA_STATUS_ENABLED,
				Text:         "Close",
			},
		},
	})
}

func (lp *LamfProvider) ineligibleFundInfoPage() (*deeplink.Deeplink, error) {
	return deeplinkv3.GetDeeplinkV3(deeplink.Screen_LOANS_SUBJECT_INFO_WITH_DETAILS_LIST_SCREEN, &palTypesPb.LoansSubjectInfoWithDetailsListScreen{
		Title: &widget.VisualElementTitleSubtitleElement{
			TitleText:    commontypes.GetPlainStringText("Ineligible funds").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#313234"),
			SubtitleText: commontypes.GetPlainStringText("Some funds may not be eligible for securing this loan. Here are some reasons why.").WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#6A6D70"),
		},
		DetailsList: &palTypesPb.LoansSubjectInfoWithDetailsListScreen_DetailsItemList{
			Items: []*widget.VisualElementTitleSubtitleElement{
				{
					VisualElement: commontypes.GetVisualElementImageFromUrl(palUi.GoldenEggBasketIcon).WithProperties(&commontypes.VisualElementProperties{
						Height: 48,
						Width:  48,
					}),
					TitleText:    commontypes.GetPlainStringText("Unavailable under ELSS").WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#313234"),
					SubtitleText: commontypes.GetPlainStringText("ELSS funds have a lock-in period of 3 years. That makes them ineligible for a loan collateral.").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#6A6D70"),
				},
				{
					VisualElement: commontypes.GetVisualElementImageFromUrl(palUi.PurpleLockerIcon).WithProperties(&commontypes.VisualElementProperties{
						Height: 48,
						Width:  48,
					}),
					TitleText:    commontypes.GetPlainStringText("Funds held in Demat form").WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#313234"),
					SubtitleText: commontypes.GetPlainStringText("Funds held in demat form (for e.g. Zerodha Coin investments) are not currently supported for pledging").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#6A6D70"),
				},
				{
					VisualElement: commontypes.GetVisualElementImageFromUrl(palUi.GreenLockIcon).WithProperties(&commontypes.VisualElementProperties{
						Height: 48,
						Width:  48,
					}),
					TitleText:    commontypes.GetPlainStringText("Already pledged funds").WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#313234"),
					SubtitleText: commontypes.GetPlainStringText("Funds that have already been put up as collateral against a loan or service with any bank or NBFC will be ineligible.").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#6A6D70"),
				},
				{
					VisualElement: commontypes.GetVisualElementImageFromUrl(palUi.RejectedLetterIcon).WithProperties(&commontypes.VisualElementProperties{
						Height: 48,
						Width:  48,
					}),
					TitleText:    commontypes.GetPlainStringText("Unapproved funds").WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor("#313234"),
					SubtitleText: commontypes.GetPlainStringText("Some funds might not be qualified per our partner, pre-approved list of eligible Mutual Funds.").WithFontStyle(commontypes.FontStyle_BODY_XS).WithFontColor("#6A6D70"),
				},
			},
		},
		Cta: &deeplink.Button{
			Text: commontypes.GetPlainStringText(fmt.Sprintf("Okay")).WithFontStyle(commontypes.FontStyle_BUTTON_M).
				WithFontColor("#FFFFFF").WithBgColor("#00B899"),
			Padding: &deeplink.Button_Padding{
				LeftPadding:   24,
				RightPadding:  12,
				TopPadding:    24,
				BottomPadding: 12,
			},
			WrapContent: true,
			Cta: &deeplink.Cta{
				Type:         deeplink.Cta_DONE,
				DisplayTheme: deeplink.Cta_PRIMARY,
				Status:       deeplink.Cta_CTA_STATUS_ENABLED,
				Text:         "Okay",
			},
		},
		ScreenBgColor: widget.GetBlockBackgroundColour("#E6E9ED"),
		Header:        nil,
	})
}

func (lp *LamfProvider) knowMoreDeeplink(lh *palPbFeEnums.LoanHeader) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_PRE_APPROVED_LOAN_KNOW_MORE_SCREEN,
		ScreenOptions: &deeplink.Deeplink_PreApprovedLoanKnowMoreScreenOptions{
			PreApprovedLoanKnowMoreScreenOptions: &deeplink.PreApprovedLoanKnowMoreScreenOptions{
				LoanHeader: lh,
				FaqTopic:   palPbFeEnums.FaqTopic_FAQ_TOPIC_MIN_MONTHLY_DUE,
			},
		},
	}
}

func loanValueCalculationInfoPopup() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
				IconUrl:       palUi.InfoPopupGreenIcon,
				TextTitle:     commontypes.GetPlainStringText("How is my loan amount calculated?").WithFontStyle(commontypes.FontStyle_SUBTITLE_2).WithFontColor("#333333"),
				TextSubTitle:  commontypes.GetPlainStringText("As per the Regulatory Authority guidelines, the maximum loan amount allowed against equity mutual funds is upto 50% of the Net Asset Value (NAV).\n\nFor debt and hybrid mutual funds, you can get a loan of upto 90% of NAV.\n\nHowever, the exact loan amount differs across schemes and can be anywhere between 45% - 90% of NAV.\n\nThe maximum loan amount you can take is 20 lakhs.").WithFontStyle(commontypes.FontStyle_BODY_3_PARA).WithFontColor("#929599"),
				DisplayScreen: deeplinkPb.InformationPopupOptions_DISPLAY_SCREEN_LAMF,
				BgColor:       "#FFFFFF",
			},
		},
	}
}

func minMonthlyDueInfoPopup() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
				IconUrl:       palUi.InfoPopupGreenIcon,
				TextTitle:     commontypes.GetPlainStringText("What is the minimum monthly due?").WithFontStyle(commontypes.FontStyle_SUBTITLE_2).WithFontColor("#333333"),
				TextSubTitle:  commontypes.GetPlainStringText("The minimum monthly due is the minimum amount you need to repay every month.\nIt's calculated as only the interest due for the month and has no principal repayment component. If you pay only the minimum due every month, the principal component becomes due in the last month of your loan.\n\nFor example: You take a loan of ₹1,00,000 for 12 months at an interest rate of 10.5% p.a.\nYour recommended EMI will be around ₹8815 ( ₹7940 (principal) + ₹875 (minimum monthly due))\nIf you choose to pay only the min. monthly due of ₹875 every month, you'll need to repay the entire principal of ₹1,00,000 in the 12th month.").WithFontStyle(commontypes.FontStyle_BODY_3_PARA).WithFontColor("#929599"),
				DisplayScreen: deeplinkPb.InformationPopupOptions_DISPLAY_SCREEN_LAMF,
			},
		},
	}
}

func (lp *LamfProvider) isActiveLoan(ctx context.Context, actorId string) (bool, error) {
	res, err := lp.GetPreApprovedLoansData(ctx, actorId)
	if err != nil {
		return false, errors.Wrap(err, "error wile checking active loan status of a user")
	}

	if res.GetActiveLoanAccount() != nil && res.GetActiveLoanAccount().GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
		return true, nil
	}
	return false, nil
}
