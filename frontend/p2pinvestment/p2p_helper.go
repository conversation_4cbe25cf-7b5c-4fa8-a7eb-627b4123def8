package p2pinvestment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	p2pPb "github.com/epifi/gamma/api/frontend/p2pinvestment"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	feTxnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	p2pPbBe "github.com/epifi/gamma/api/p2pinvestment"
	bePayPb "github.com/epifi/gamma/api/pay/attribute"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	typesPb "github.com/epifi/gamma/api/typesv2"
	p2pScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/p2pinvestment"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/p2pinvestment/common"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/p2pinvestment/deeplinks"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	p2pInvestmentPkg "github.com/epifi/gamma/pkg/p2pinvestment"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
)

const activityDateLayout = "Mon, 02 Jan"
const maxSchemeFE = 10
const daysUntilMaturityTxnsAreToBeActedOn = 35
const boosterDisplayName = "Booster"
const yellowInfoIcon = "https://epifi-icons.pointz.in/p2pinvestment/yellow-info-icon.png"
const yellowInfoIconLarge = "https://epifi-icons.pointz.in/p2pinvestment/yellow-info-icon-large.png"

func convertBeToFeTransactionAttribute(beTxnAttribute *bePayPb.TransactionAttribute) (*feTxnPb.TransactionAttribute, error) {
	feProtocol, ok := fePayPb.GetFePaymentProtocol(beTxnAttribute.GetPaymentProtocol())
	if !ok {
		return nil, fmt.Errorf("failed to convert BE to FE payment protocol")
	}
	return &feTxnPb.TransactionAttribute{
		PayerAccountId:                beTxnAttribute.GetPayerAccountId(),
		TransactionId:                 beTxnAttribute.GetRequestId(),
		MerchantRefId:                 beTxnAttribute.GetMerchantRefId(),
		PaymentProtocol:               feProtocol,
		ReferenceUrl:                  beTxnAttribute.GetReferenceUrl(),
		PayeeActorName:                beTxnAttribute.PayeeActorName,
		PayerMaskedAccountNumber:      beTxnAttribute.GetPayerMaskedAccountNumber(),
		Amount:                        typesPb.GetFromBeMoney(beTxnAttribute.GetAmount()),
		Remarks:                       beTxnAttribute.GetRemarks(),
		PayerPaymentInstrument:        beTxnAttribute.GetPayerPaymentInstrumentId(),
		PayeePaymentInstrument:        beTxnAttribute.GetPayeePaymentInstrumentId(),
		DisplayPayeePaymentInstrument: beTxnAttribute.GetDisplayPayeePaymentInstrument(),
		RequestId:                     beTxnAttribute.GetRequestId(),
	}, nil
}

// TODO(Vikas): move string to config
// nolint:funlen
func GetDashboardInfo(ctx context.Context, res *p2pPbBe.GetInvestmentDashboardResponse, investNowDl *deeplinkPb.Deeplink, showRecentActivity bool, conf *genconf.Config,
	isAmountUnderLockInOnly bool, withdrawableAmount *moneyPb.Money, riskProfileResp *profilePb.GetUserProfileResponse) (*p2pPb.DashboardInfo, error) {
	var returns *moneyPb.Money
	var err error
	investedAmount := typesPb.GetFromBeMoney(res.GetInvestmentData().GetTotalPrincipalAmount())
	returns, err = money.Subtract(res.GetInvestmentData().GetCurrentValue(), res.GetInvestmentData().GetTotalPrincipalAmount())
	if err != nil {
		return nil, errors.Wrap(err, "failed to compute returns")
	}
	investTileIconUrl := "https://epifi-icons.pointz.in/p2pinvestment/investment-enabled-final.png"
	investCtaStatus := deeplinkPb.Cta_CTA_STATUS_ENABLED
	investCtaDisplayTheme := deeplinkPb.Cta_PRIMARY
	if !res.GetIsInvestmentAllowed() {
		investTileIconUrl = "https://epifi-icons.pointz.in/p2pinvestment/investment-disabled-final.png"
		investCtaStatus = deeplinkPb.Cta_CTA_STATUS_DISABLED
		investCtaDisplayTheme = deeplinkPb.Cta_SECONDARY
	}

	// By default, setting the withdraw-deeplink to withdraw not allowed deeplink
	// Only, if withdrawal is allowed, we change it to the withdrawDeeplink
	withdrawCtaDeeplink := deeplinks.GetWithdrawNotAllowedDeeplink()
	withdrawCtaDisplayTheme := deeplinkPb.Cta_SECONDARY
	if res.GetIsWithdrawalAllowed() {
		withdrawCtaDisplayTheme = deeplinkPb.Cta_PRIMARY
		withdrawCtaDeeplink = deeplinks.GetWithdrawDeeplink()
	}
	if (money.Compare(withdrawableAmount, money.FromPaisa(100))) == -1 {
		withdrawCtaDeeplink = deeplinks.GetWithdrawNotAllowedInsufficientDeeplink(withdrawableAmount)
		withdrawCtaDisplayTheme = deeplinkPb.Cta_SECONDARY
	}

	// if none of the investments are approved by vendor, disable withdraw
	withdrawCtaStatus := deeplinkPb.Cta_CTA_STATUS_ENABLED
	if money.IsZero(res.GetInvestmentData().GetCurrentValue()) {
		withdrawCtaStatus = deeplinkPb.Cta_CTA_STATUS_DISABLED
		withdrawCtaDisplayTheme = deeplinkPb.Cta_SECONDARY
		withdrawCtaDeeplink = nil
	}

	isDownTime, timeStampString, err := isWithdrawalDowntime(conf)

	if err != nil {
		logger.ErrorNoCtx("error while getting withdrawal downtime", zap.Error(err))
	}

	if isDownTime {
		withdrawCtaDisplayTheme = deeplinkPb.Cta_SECONDARY
		withdrawCtaDeeplink = deeplinks.GetWithdrawalDowntimeDeeplink(timeStampString)
	}

	if riskProfileResp != nil && len(riskProfileResp.GetAccountsInfo()) > 0 {
		if riskProfileResp.GetAccountsInfo()[0].GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE ||
			riskProfileResp.GetAccountsInfo()[0].GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE {
			logger.Info(ctx, "withdrawal blocked for user due to credit freeze")
			withdrawCtaDisplayTheme = deeplinkPb.Cta_SECONDARY
			withdrawCtaDeeplink = deeplinks.GetWithdrawalNotAllowedCreditFreezeDeeplink()
		}
	}

	if isAmountUnderLockInOnly {
		withdrawCtaDisplayTheme = deeplinkPb.Cta_SECONDARY
		withdrawCtaDeeplink = deeplinks.GetWithdrawalNotAllowedAmountUnderLockInDeeplink()
	}

	var inProcessInfoItem *deeplinkPb.InfoItem
	if !money.ToDecimal(res.GetInvestmentData().GetRequestedInvestment()).IsZero() {
		inProcessInfoItem = &deeplinkPb.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/p2pinvestment/clock-final.png",
			Title: fmt.Sprintf("%v IN PROCESS", money.ToDisplayString(res.GetInvestmentData().GetRequestedInvestment())),
		}
	}

	return &p2pPb.DashboardInfo{
		Current:      typesPb.GetFromBeMoney(res.GetInvestmentData().GetCurrentValue()),
		Invested:     investedAmount,
		Returns:      typesPb.GetFromBeMoney(returns),
		DailyReturns: nil,
		InvestTile: &deeplinkPb.Tile{
			Icon: investTileIconUrl,
			Cta: &deeplinkPb.Cta{
				Text:         "Lend & earn",
				Deeplink:     investNowDl,
				DisplayTheme: investCtaDisplayTheme,
				Status:       investCtaStatus,
			},
		},
		WithdrawCta: &deeplinkPb.Cta{
			Text:         "Withdraw",
			Deeplink:     withdrawCtaDeeplink,
			DisplayTheme: withdrawCtaDisplayTheme,
			Status:       withdrawCtaStatus,
		},
		BottomInfoTiles: deeplinks.GetBottomTiles(false),
		RecentActivitiesInfo: &p2pPb.DashboardInfo_RecentActivitiesInfo{
			Title:                 "Latest activity",
			AllActivitiesLinkText: "All activity",
			Deeplink:              deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_P2P_INVESTMENT_ALL_ACTIVITY_SCREEN, &p2pScreenOptions.AllActivitiesScreenOptions{}),
		},
		InProcessInfoItem:  inProcessInfoItem,
		ShowRecentActivity: showRecentActivity,
		// Current Returns key
		CurrentKeyValue: &ui.VerticalKeyValuePair{
			Title:                        ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("CURRENT VALUE", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS)),
			Value:                        getCurrentKeyValueForDashboard(res, returns),
			VerticalPaddingBtwTitleValue: 4,
		},
		KeyInfos: &p2pPb.DashboardBannerKeyInfos{
			InfoKeyValue: []*ui.VerticalKeyValuePair{
				{
					Title: getAmountForDashboardKeyInfos(res, returns, res.GetInvestmentData().GetTotalPrincipalAmount(), "#FFFFFF"),
					Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("LENT",
						"#B9B9B9", commontypes.FontStyle_OVERLINE_XS_CAPS)),
					VerticalPaddingBtwTitleValue: 4,
				},
				{
					Title: getAmountForDashboardKeyInfos(res, returns, returns, "#C5E9B2"),
					Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("RETURNS",
						"#B9B9B9", commontypes.FontStyle_OVERLINE_XS_CAPS)),
					VerticalPaddingBtwTitleValue: 4,
				},
			},
			BgColor: "#383838",
		},
	}, nil
}

func getCurrentKeyValueForDashboard(res *p2pPbBe.GetInvestmentDashboardResponse, returns *moneyPb.Money) *ui.IconTextComponent {
	value, precision := money.GetDisplayStringWithValueAndPrecision(res.GetInvestmentData().GetCurrentValue(), 2,
		false, false, money.IndianNumberSystem)
	// We are not showing '-' as current value and instead showing '0'
	if value == "-" {
		value = "0"
	}
	if money.IsNegative(returns) {
		return ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("₹", "#FFFFFF", commontypes.FontStyle_RUPEE_M),
			commontypes.GetTextFromStringFontColourFontStyle(value, "#FFFFFF", commontypes.FontStyle_NUMBER_3XL),
			commontypes.GetTextFromStringFontColourFontStyle(precision, "#FFFFFF", commontypes.FontStyle_NUMBER_XL)).
			WithRightImageUrlHeightAndWidth(yellowInfoIcon, 20, 20).WithRightImagePadding(8).WithDeeplink(getReturnsNegativePopUp())
	}
	return ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("₹", "#FFFFFF", commontypes.FontStyle_RUPEE_XL),
		commontypes.GetTextFromStringFontColourFontStyle(value, "#FFFFFF", commontypes.FontStyle_NUMBER_3XL),
		commontypes.GetTextFromStringFontColourFontStyle(precision, "#FFFFFF", commontypes.FontStyle_NUMBER_XL))
}

func getReturnsNegativePopUp() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
				IconUrl: yellowInfoIconLarge,
				Ctas: []*deeplinkPb.Cta{
					{
						Type:         deeplinkPb.Cta_DONE,
						Text:         "Ok, got it",
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
					}},
				IsNonDismissible: false,
				TextTitle:        commontypes.GetTextFromStringFontColourFontStyle("Your returns value may be incorrect", "#282828", commontypes.FontStyle_HEADLINE_L),
				TextSubTitle:     commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("Last checked on %s", time.Now().Format("02 Jan 3:04 PM")), "#333333", commontypes.FontStyle_SUBTITLE_S),
				BodyTexts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle("Your exact returns will be updated once your Withdrawals or Maturity payouts have been completed successfully.", "#646464", commontypes.FontStyle_BODY_S),
				},
				BgColor: "#F4E7BF",
			},
		},
	}
}

func getAmountForDashboardKeyInfos(_ *p2pPbBe.GetInvestmentDashboardResponse, returns *moneyPb.Money,
	valueForDisplay *moneyPb.Money, fontColor string) *ui.IconTextComponent {
	if money.IsNegative(returns) {
		return ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("-", "#FFFFFF", commontypes.FontStyle_NUMBER_M))
	}
	value := money.ToDisplayStringInIndianFormat(valueForDisplay, 0, true)
	// We are showing '-' only if returns are negative
	if value == "-" {
		value = "0"
	}
	return ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(value, fontColor, commontypes.FontStyle_NUMBER_M))
}

func getFeDate(date *date.Date) *typesPb.Date {
	if date == nil {
		return nil
	}
	return &typesPb.Date{
		Year:  date.GetYear(),
		Month: date.GetMonth(),
		Day:   date.GetDay(),
	}
}

func (s *Service) isP2PInternalUser(ctx context.Context, actorId string) (bool, error) {
	userGroups, err := s.rpcHelper.GetUserGroupsForActor(ctx, actorId)
	if err != nil {
		return false, errors.Wrap(err, "failed to check user group for an actor")
	}
	isP2PInternalUser := false
	for _, grp := range userGroups {
		if grp == commontypes.UserGroup_P2P_INVESTMENT_INTERNAL {
			isP2PInternalUser = true
		}
	}
	return isP2PInternalUser, nil
}

func isWithdrawalDowntime(conf *genconf.Config) (bool, string, error) {

	// DownTime due to Night time maintainence
	timeStampString := "1:30 AM"
	if !conf.P2PInvestment().WithdrawalDowntime().IsEnable() {
		return false, "", nil
	}
	currTime := time.Now().In(datetime.IST)
	inDowntime, err := datetime.TimeLieBetweenStartAndEndInHHMM(currTime, conf.P2PInvestment().WithdrawalDowntime().StartTime(), conf.P2PInvestment().WithdrawalDowntime().EndTime())
	if !inDowntime || err != nil {
		if err != nil {
			return false, "", fmt.Errorf("error in parsing p2p withdrawal downtime %w", err)
		}
		// default false flag
		return false, "", nil
	}
	return true, timeStampString, nil
}

func getDashboardForJumpClosingState(di *p2pPb.DashboardInfo) {
	di.InvestmentsLeft = &deeplinkPb.InfoItem{
		Icon:  "https://epifi-icons.s3.ap-south-1.amazonaws.com/p2pinvestment/alert_icon.png",
		Title: "Update on Jump Investments Maturing in March-April",
		Desc:  "Jump investments maturing between 18th March-10th April will be processed in two steps. Check email for details.",
	}
	di.InvestTile = &deeplinkPb.Tile{
		Icon: "https://epifi-icons.pointz.in/p2pinvestment/investment-disabled-final.png",
		Cta: &deeplinkPb.Cta{
			Text:         "Invest in Jump",
			DisplayTheme: deeplinkPb.Cta_SECONDARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_DISABLED,
		},
	}
}

// nolint: unused
func getDashboardForForceUpgradeState(di *p2pPb.DashboardInfo) {
	di.InvestmentsLeft = &deeplinkPb.InfoItem{
		Icon:  "https://epifi-icons.pointz.in/p2pinvestment/9percent-final.png",
		Title: "Jump just got an upgrade",
		Desc:  "Update your app & gain access to 3 new plans.",
	}
	di.InvestTile = &deeplinkPb.Tile{
		Icon: "https://epifi-icons.pointz.in/p2pinvestment/investment-disabled-final.png",
		Cta: &deeplinkPb.Cta{
			Text:         "Invest in Jump",
			DisplayTheme: deeplinkPb.Cta_SECONDARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_DISABLED,
		},
	}
}

func isNudgePresent() bool {
	return true
}

func getNudgeTitleText(missOutReturn string) string {
	return fmt.Sprintf("You'll miss out on %s in returns!", missOutReturn)
}

func getNudgeDescText(missOutReturn string, amount string) string {
	return fmt.Sprintf("%s in Jump for 1 year will be %s", missOutReturn, amount)
}

func getAmountBreakUpForBe(amountBreakUp []*p2pPb.WithdrawV2Request_AmountBreakUp) []*p2pPbBe.WithdrawV2Request_AmountBreakUp {
	var amountBreakUpForBe []*p2pPbBe.WithdrawV2Request_AmountBreakUp
	for _, amountBreakUpUnit := range amountBreakUp {
		amountBreakUp := &p2pPbBe.WithdrawV2Request_AmountBreakUp{
			Amount:     amountBreakUpUnit.GetAmount().GetBeMoney(),
			SchemeName: p2pPbBe.SchemeName(p2pPbBe.SchemeName_value[amountBreakUpUnit.GetSchemeName()]),
		}
		amountBreakUpForBe = append(amountBreakUpForBe, amountBreakUp)
	}
	return amountBreakUpForBe
}

func getDisplayStringForAmount(amount *moneyPb.Money) string {
	return money.ToDisplayStringInIndianFormat(amount, 0, true)
}

func getFeSchemeNameAndColor(name common.DisplaySchemeName) (string, string, string) {
	var feSchemeName, feSchemeColor, feSchemeIcon string
	switch name { //nolint:exhaustive
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_9_LONG_TERM_PENALTY:
		feSchemeName = "Long-term"
		feSchemeColor = "#7FBECE"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/upto9light.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_7_FLEXI_LIQUID:
		feSchemeName = "Flexi"
		feSchemeColor = "#9287BD"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/Upto7light.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_8_SHORT_TERM_LOCK_IN:
		feSchemeName = "Short-term"
		feSchemeColor = "#879EDB"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/Upto8light.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_9_SHORT_TERM_LOCK_IN:
		feSchemeName = "Short-term"
		feSchemeColor = "#879EDB"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/short-term-9-light.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_10_BOOSTER_LOCK_IN:
		feSchemeName = boosterDisplayName
		feSchemeColor = "#AC7C44"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/Upto10light.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_8_FLEXI_LIQUID:
		feSchemeName = "Flexi"
		feSchemeColor = "#9287BD"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/Upto8flexi.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_8_25_FLEXI_LIQUID:
		feSchemeName = "Flexi"
		feSchemeColor = "#9287BD"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/Upto825flexi.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_8_75_FLEXI_LIQUID:
		feSchemeName = "Flexi"
		feSchemeColor = "#9287BD"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/Upto875flexi.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_12_FESTIVE_LOCK_IN:
		feSchemeName = "Festive"
		feSchemeColor = "#955990"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/Upto12Festive.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_10_5_FESTIVE_LOCK_IN_51_DAYS:
		feSchemeName = "Festive"
		feSchemeColor = "#955990"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/10_5_summary_break_up.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_10_SHORT_TERM_LOCK_IN, common.DisplaySchemeName_DISPLAY_SCHEME_NAME_10_SHORT_TERM_LOCK_IN_51_DAYS:
		feSchemeName = "Short-term"
		feSchemeColor = "#879EDB"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/Upto10Short.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_9_75_FESTIVE_LOCK_IN:
		feSchemeName = "Festive"
		feSchemeColor = "#955990"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/festive_9_75_summary_page.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_9_QUICK_LOCK_IN_3_MHP:
		feSchemeName = "Quick"
		feSchemeColor = "#955990"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/quick_9_summary_page.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_9_5_SHORT_TERM_LOCK_IN_6_MHP:
		feSchemeName = "Short-term"
		feSchemeColor = "#879EDB"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/short_term_9_5_summary_page.png"
	case common.DisplaySchemeName_DISPLAY_SCHEME_NAME_10_BOOSTER_LOCK_IN_12_MHP:
		feSchemeName = boosterDisplayName
		feSchemeColor = "#AC7C44"
		feSchemeIcon = "https://epifi-icons.pointz.in/p2pinvestment/Upto10light.png"
	}
	return feSchemeName, feSchemeColor, feSchemeIcon
}

func getFeSchemeWiseInvestmentDetails(details []*p2pPbBe.GetInvestmentSummaryDetailsResponse_InvestmentSchemeWiseDetail, isInternalUser bool) ([]*FeSchemeWiseInvestmentDetails, error) {
	schemeNameToInvestmentDetailsMap := make(map[common.DisplaySchemeName]*FeSchemeWiseInvestmentDetails)
	schemesEnabledInProd := p2pInvestmentPkg.GetDisplaySchemesEnabledInProd(isInternalUser)
	if len(details) == 0 {
		for _, schemeName := range schemesEnabledInProd {
			schemeNameToInvestmentDetailsMap[schemeName] = &FeSchemeWiseInvestmentDetails{
				SchemeName:     schemeName,
				CurrentValue:   money.ZeroINR().GetPb(),
				InvestedAmount: money.ZeroINR().GetPb(),
				Returns:        money.ZeroINR().GetPb(),
			}
		}
	}
	for _, invDetail := range details {
		feSchemeNameEnum, fesErr := getFeSchemeNameEnum(invDetail.GetSchemeName())
		if fesErr != nil {
			return nil, fesErr
		}
		isSchemeEnabled := false
		for _, schemeName := range schemesEnabledInProd {
			if feSchemeNameEnum == schemeName {
				isSchemeEnabled = true
				break
			}
		}
		if !isSchemeEnabled {
			continue
		}
		if val, ok := schemeNameToInvestmentDetailsMap[feSchemeNameEnum]; ok {
			var err error
			schemeNameToInvestmentDetailsMap[feSchemeNameEnum].InvestedAmount, err = money.Sum(val.InvestedAmount, invDetail.GetInvestedAmount())
			if err != nil {
				return nil, err
			}
			schemeNameToInvestmentDetailsMap[feSchemeNameEnum].CurrentValue, err = money.Sum(val.CurrentValue, invDetail.GetCurrentValue())
			if err != nil {
				return nil, err
			}
			schemeNameToInvestmentDetailsMap[feSchemeNameEnum].Returns, err = money.Sum(val.Returns, invDetail.GetReturns())
			if err != nil {
				return nil, err
			}
		} else {
			schemeNameToInvestmentDetailsMap[feSchemeNameEnum] = &FeSchemeWiseInvestmentDetails{
				SchemeName:     feSchemeNameEnum,
				CurrentValue:   invDetail.GetCurrentValue(),
				InvestedAmount: invDetail.GetInvestedAmount(),
				Returns:        invDetail.GetReturns(),
			}
		}
	}
	var res []*FeSchemeWiseInvestmentDetails
	for _, scheme := range schemesEnabledInProd {
		if invDetails, ok := schemeNameToInvestmentDetailsMap[scheme]; ok {
			if money.IsZero(invDetails.InvestedAmount) {
				continue
			}
			res = append(res, schemeNameToInvestmentDetailsMap[scheme])
		}
	}
	return res, nil
}

func isAmountUnderLock(lockInAmount *moneyPb.Money, totalAmountToWithdraw *moneyPb.Money) bool {
	if !money.IsZero(lockInAmount) && money.IsZero(totalAmountToWithdraw) {
		return true
	}
	return false
}

func getLockInAmount(hardLockInAmount *moneyPb.Money, longTermLockInAmount *moneyPb.Money) (*moneyPb.Money, error) {
	// lockInAmount amount is the sum of lockInAmount from vendor side and the hard lock in that we have enforced on investor on long term plan to avoid plan abuse by the investor
	lockInAmount, sumErr := money.Sum(longTermLockInAmount, hardLockInAmount)
	if sumErr != nil {
		return nil, errors.Wrap(sumErr, "unable to calculate lock in amount")
	}
	return lockInAmount, nil
}

func getWithdrawalInfo(penaltyFreeAmount *moneyPb.Money, withPenaltyAmount *moneyPb.Money, hardLockInAmount *moneyPb.Money, longTermLockInAmount *moneyPb.Money) (*moneyPb.Money, *moneyPb.Money, error) {
	// this is amount user can withdraw with and without penalty
	withdrawableAmount, sumErr := money.Sum(penaltyFreeAmount, withPenaltyAmount)
	if sumErr != nil {
		return nil, nil, errors.Wrap(sumErr, "failed to add penalty free withdrawable amount and with penalty withdrawable amount")
	}
	lockInAmount, err := getLockInAmount(hardLockInAmount, longTermLockInAmount)
	if err != nil {
		return nil, nil, errors.Wrap(err, "error")
	}
	// remove lock term lock-in amount as it is forced at our side
	totalWithdrawableAmount, subtractErr := money.Subtract(withdrawableAmount, longTermLockInAmount)
	if subtractErr != nil {
		return nil, nil, errors.Wrap(subtractErr, "unable to calculate totalWithdrawableAmount")
	}
	return totalWithdrawableAmount, lockInAmount, nil
}

// getFeSchemeNameEnum returns the scheme name enum corresponding to the scheme name displayed to the user
func getFeSchemeNameEnum(schemeName p2pPbBe.SchemeName) (common.DisplaySchemeName, error) {
	displayScheme, ok := p2pInvestmentPkg.BeSchemeToDisplayScheme[schemeName]
	if !ok {
		return common.DisplaySchemeName_DISPLAY_SCHEME_NAME_UNSPECIFIED, fmt.Errorf("unhandled scheme name: %v", schemeName.String())
	}
	return displayScheme, nil
}

type FeSchemeWiseInvestmentDetails struct {
	SchemeName     common.DisplaySchemeName
	CurrentValue   *moneyPb.Money
	InvestedAmount *moneyPb.Money
	Returns        *moneyPb.Money
}
