package deeplinks

import (
	"context"
	"fmt"

	"github.com/google/wire"
	"github.com/pkg/errors"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/be-common/pkg/epifierrors"
)

var (
	GeneratorWireSet = wire.NewSet(
		NewKnowMoreGenerator,
		NewUnlockAccessGenerator,
		NewWithdrawGenerator,
		NewDeeplinkGenerator,
		NewAvailablePlansInfoGenerator,
		NewEnterWithdrawAmountGenerator,
	)
)

type IDeeplinkGenerator interface {
	GenerateDeeplink(ctx context.Context, req *GenerateDeeplinkRequest) (*deeplinkPb.Deeplink, error)
}

type GenerateDeeplinkRequest struct {
	ActorId string
}

type DeeplinkGenerator struct {
	knowMoreGenerator            *KnowMoreGenerator
	unlockAccessGenerator        *UnlockAccessGenerator
	withdrawGenerator            *WithdrawGenerator
	availablePlansInfoGenerator  *AvailablePlansInfoGenerator
	enterWithdrawAmountGenerator *EnterWithdrawAmountGenerator
}

func NewDeeplinkGenerator(
	knowMoreGenerator *KnowMoreGenerator,
	unlockAccessGenerator *UnlockAccessGenerator,
	withdrawGenerator *WithdrawGenerator,
	availablePlansInfoGenerator *AvailablePlansInfoGenerator,
	enterWithdrawAmountGenerator *EnterWithdrawAmountGenerator,
) *DeeplinkGenerator {
	return &DeeplinkGenerator{
		knowMoreGenerator:            knowMoreGenerator,
		unlockAccessGenerator:        unlockAccessGenerator,
		withdrawGenerator:            withdrawGenerator,
		availablePlansInfoGenerator:  availablePlansInfoGenerator,
		enterWithdrawAmountGenerator: enterWithdrawAmountGenerator,
	}
}

func (d *DeeplinkGenerator) GenerateDeeplink(ctx context.Context, screen deeplinkPb.Screen, req *GenerateDeeplinkRequest) (*deeplinkPb.Deeplink, error) {
	var generator IDeeplinkGenerator
	switch screen {
	default:
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, fmt.Sprintf("Unsupported Screen: %v", screen.String()))
	case deeplinkPb.Screen_P2P_INVESTMENT_KNOW_MORE_SCREEN:
		generator = d.knowMoreGenerator
	case deeplinkPb.Screen_P2P_INVESTMENT_UNLOCK_ACCESS_SCREEN:
		generator = d.unlockAccessGenerator
	case deeplinkPb.Screen_P2P_WITHDRAW_INVESTMENT_SCREEN:
		generator = d.withdrawGenerator
	case deeplinkPb.Screen_P2P_INVESTMENT_AVAILABLE_PLANS_INFO:
		generator = d.availablePlansInfoGenerator
	case deeplinkPb.Screen_P2P_WITHDRAW_MONEY_ENTER_AMOUNT:
		generator = d.enterWithdrawAmountGenerator
	}
	return generator.GenerateDeeplink(ctx, req)
}
