package tiering

import (
	"github.com/google/wire"

	tieringAddFunds "github.com/epifi/gamma/frontend/tiering/add_funds"
	"github.com/epifi/gamma/frontend/tiering/benefits"
	"github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/deeplink"
	"github.com/epifi/gamma/frontend/tiering/deeplink/plan_options"
	tieringBottomInfo "github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/bottom_info"
	tieringCard "github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/card"
	tieringCta "github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/cta"
	tieringRelease "github.com/epifi/gamma/frontend/tiering/release"
)

var TieringAddFundsManagerWireSet = wire.NewSet(
	wire.NewSet(tieringAddFunds.NewTieringAddFundsManagerService, wire.Bind(new(tieringAddFunds.TieringAddFundsManager), new(*tieringAddFunds.TieringAddFundsManagerService))),
	TieringReleaseManagerWireSet,
)

var TieringReleaseManagerWireSet = wire.NewSet(
	wire.NewSet(tieringRelease.NewFeReleaseManagerService, wire.Bind(new(tieringRelease.FeManager), new(*tieringRelease.FeManagerService))),
)

var TieringDeeplinkManagerWireSet = wire.NewSet(
	wire.NewSet(deeplink.NewDeeplinkManagerService, wire.Bind(new(deeplink.Manager), new(*deeplink.Service))),
	wire.NewSet(benefits.NewBenefitsManagerService, wire.Bind(new(benefits.Manager), new(*benefits.Service))),
	wire.NewSet(plan_options.NewPlanOptionsManagerService, wire.Bind(new(plan_options.Manager), new(*plan_options.Service))),
	wire.NewSet(tieringBottomInfo.NewBottomInfoManagerService, wire.Bind(new(tieringBottomInfo.Manager), new(*tieringBottomInfo.Service))),
	wire.NewSet(tieringCta.NewCtaManagerService, wire.Bind(new(tieringCta.Manager), new(*tieringCta.Service))),
	wire.NewSet(tieringCard.NewCardManagerService, wire.Bind(new(tieringCard.Manager), new(*tieringCard.Service))),
)

var TieringDataCollectorWireSet = wire.NewSet(
	wire.NewSet(data_collector.NewDataCollectorService, wire.Bind(new(data_collector.DataCollector), new(*data_collector.DataCollectorService))),
)
