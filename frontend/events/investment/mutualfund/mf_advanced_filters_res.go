package mutualfund

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
	feEvents "github.com/epifi/gamma/frontend/events"
)

type FilterResponseGiven struct {
	EventId         string
	UserId          string
	ProspectId      string
	EventName       string
	EventType       string
	EventProperties *map[string]interface{}
	EventTraits     *map[string]interface{}
	SessionId       string
	Source          string
	AppVersion      uint32
	AppOs           string
	Category        string
	FilterCount     int
	FiltersSelected []string
	FundCount       uint32
	PresetFilterId  string
}

func (f *FilterResponseGiven) GetEventId() string {
	return f.EventId
}

func (f *FilterResponseGiven) GetUserId() string {
	return f.UserId
}

func (f *FilterResponseGiven) GetProspectId() string {
	return f.ProspectId
}

func (f *FilterResponseGiven) GetEventName() string {
	return f.EventName
}

func (f *FilterResponseGiven) GetEventType() string {
	return f.EventType
}

func (f *FilterResponseGiven) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(f, properties)
	return properties
}

func (f *FilterResponseGiven) GetEventTraits() map[string]interface{} {
	return nil
}

func NewFilterResponseGiven(entrypoint string, selectedFilters []string, collectionId string, fundCount uint32,
	actorId string, sessionId string, platform commontypes.Platform, appVersion uint32) *FilterResponseGiven {
	return &FilterResponseGiven{
		EventId:         uuid.New().String(),
		UserId:          actorId,
		ProspectId:      uuid.New().String(),
		EventName:       feEvents.EventMfAdvancedFilterResponse,
		EventType:       events.EventTrack,
		SessionId:       sessionId,
		Source:          entrypoint,
		AppVersion:      appVersion,
		AppOs:           platform.String(),
		Category:        feEvents.CategoryMutualFund,
		FilterCount:     len(selectedFilters),
		FiltersSelected: selectedFilters,
		FundCount:       fundCount,
		PresetFilterId:  collectionId,
	}
}
