package lens_test

import (
	"context"
	"testing"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	lens "github.com/epifi/gamma/frontend/analyser/investments/mutualfunds/equity/lens/market_cap"
)

func TestInsightGenerator_GenerateSummary(t *testing.T) {
	type fields struct {
		knownMarketCapAggregates   []*lens.MarketCapAggregate
		unknownMarketCapAggregates *lens.MarketCapAggregate
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "successfully generate summary with known market cap aggregates",
			fields: fields{
				knownMarketCapAggregates: []*lens.MarketCapAggregate{
					{
						DisplayName: "small cap",
						CurrentValue: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        90,
							Nanos:        0,
						},
						AllocationPercentage: 90.0,
					},
					{
						DisplayName: "mid cap",
						CurrentValue: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        10,
							Nanos:        0,
						},
						AllocationPercentage: 10.0,
					},
				},
				unknownMarketCapAggregates: nil,
			},
			args: args{
				ctx: context.Background(),
			},
			want:    "small cap companies made up 90% of your funds",
			wantErr: false,
		},
		{
			name: "successfully generate summary for unknown market cap aggregates",
			fields: fields{
				knownMarketCapAggregates: []*lens.MarketCapAggregate{},
				unknownMarketCapAggregates: &lens.MarketCapAggregate{
					DisplayName: "Unknown",
					CurrentValue: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        10,
						Nanos:        0,
					},
					AllocationPercentage: 100.0,
				},
			},
			args: args{
				ctx: context.Background(),
			},
			want:    "Unknown allocation made up 100% of your funds",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := lens.NewInsightGenerator(tt.fields.knownMarketCapAggregates, tt.fields.unknownMarketCapAggregates)
			got, err := i.GenerateSummary(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateSummary() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GenerateSummary() got = %v, want %v", got, tt.want)
			}
		})
	}
}
