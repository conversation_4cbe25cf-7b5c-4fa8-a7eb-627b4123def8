package credit_score

//go:generate mockgen -source=credit_report_fetcher.go -destination=./mocks/mock_credit_report_fetcher.go -package=mocks

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/google/wire"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	analyserFePb "github.com/epifi/gamma/api/frontend/analyser"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	analyserErrors "github.com/epifi/gamma/frontend/analyser/errors"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
)

var WireCreditReportFetcherSet = wire.NewSet(NewCreditReportFetcher, wire.Bind(new(ICreditReportFetcher), new(*CreditReportFetcher)))

type ICreditReportFetcher interface {
	// RefreshAndGet fetches latest 2 credit reports and optionally may refresh the report
	RefreshAndGet(ctx context.Context, actorId string) ([]*creditReportPb.CreditReportDownloadDetails, error)
}

type CreditReportFetcher struct {
	creditScoreAnalyserCfg *genconf.CreditScoreAnalyserConfig
	defaultTime            datetime.Time
	releaseEvaluator       release.IEvaluator
	creditReportClient     creditReportPb.CreditReportManagerClient
}

func NewCreditReportFetcher(creditScoreAnalyserCfg *genconf.CreditScoreAnalyserConfig, defaultTime datetime.Time,
	releaseEvaluator release.IEvaluator, creditReportClient creditReportPb.CreditReportManagerClient) *CreditReportFetcher {
	return &CreditReportFetcher{
		creditScoreAnalyserCfg: creditScoreAnalyserCfg,
		defaultTime:            defaultTime,
		releaseEvaluator:       releaseEvaluator,
		creditReportClient:     creditReportClient,
	}
}

// RefreshAndGet fetches latest 2 credit reports and optionally may refresh the report
func (crf *CreditReportFetcher) RefreshAndGet(ctx context.Context, actorId string) ([]*creditReportPb.CreditReportDownloadDetails, error) {
	creditReportRes, err := crf.fetchLatestReports(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch credit reports: %w", err)
	}

	var latestCreditReport *creditReportPb.CreditReportDownloadDetails
	if len(creditReportRes.GetCreditReports()) > 0 {
		latestCreditReport = creditReportRes.GetCreditReports()[0]
	} else {
		return nil, fmt.Errorf("no reports retuned by credit report service: %w", analyserErrors.NoDataFoundErr)
	}

	if crf.defaultTime.Since(latestCreditReport.GetCreatedAt().AsTime()) < 24*time.Hour*time.Duration(crf.creditScoreAnalyserCfg.AutoRefreshCoolOffDurationInDays()) {
		return creditReportRes.GetCreditReports(), nil
	}

	logger.Info(ctx, "attempting credit report auto refresh")

	reports, err := crf.refreshReport(ctx, actorId)
	if err != nil {
		// silent error as analyser can be created with older data
		logger.Error(ctx, "failed to refresh credit report", zap.Error(err))
		return creditReportRes.GetCreditReports(), nil
	}

	return reports, err
}

// refreshReport starts credit report for actor and wait for process completion till the configured duration
func (crf *CreditReportFetcher) refreshReport(ctx context.Context, actorId string) ([]*creditReportPb.CreditReportDownloadDetails, error) {
	consentStatus, err := crf.creditReportClient.GetCreditReportConsentStatus(ctx, &creditReportPb.GetCreditReportConsentStatusRequest{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_EXPERIAN,
	})
	if rpcErr := epifigrpc.RPCError(consentStatus, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get credit report consent status: %w", err)
	}

	if consentStatus.GetCreditReportConsentStatus() != creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_ACTIVE {
		return nil, fmt.Errorf("consent status is not active %v", consentStatus.GetCreditReportConsentStatus())
	}

	if err = crf.startRefresh(ctx, actorId); err != nil {
		return nil, fmt.Errorf("failed to start credit report refresh: %w", err)
	}

	isRefreshComplete, err := crf.isLatestRefreshComplete(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to check the completion of refresh process: %w", err)
	}

	if isRefreshComplete {
		latestCreditReports, err := crf.fetchLatestReports(ctx, actorId)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch credit reports post refresh: %w", err)
		}
		return latestCreditReports.GetCreditReports(), nil
	} else {
		return nil, fmt.Errorf("credit report refresh is not complete")
	}
}

// fetchLatestReports fetches latest two credit reports for the actor
func (crf *CreditReportFetcher) fetchLatestReports(ctx context.Context, actorId string) (*creditReportPb.GetCreditReportsResponse, error) {
	creditReportRes, err := crf.creditReportClient.GetCreditReports(ctx, &creditReportPb.GetCreditReportsRequest{
		ActorId: actorId,
		Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_EXPERIAN},
		Limit:   2,
	})
	if rpcErr := epifigrpc.RPCError(creditReportRes, err); rpcErr != nil {
		if creditReportRes.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("credit report not found: %w", analyserErrors.NoDataFoundErr)
		}
		return nil, fmt.Errorf("failed to fetch credit report: %w", rpcErr)
	}

	return creditReportRes, nil
}

// startRefresh checks for any ongoing download process otherwise starts a new download
// refresh will be only started if user has atleast one download initiated in past
func (crf *CreditReportFetcher) startRefresh(ctx context.Context, actorId string) error {
	latestDownloadProcessRes, err := crf.getLatestRefreshDetails(ctx, actorId)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return fmt.Errorf("failed to get latest credit report download details: %w", err)
	}

	if !errors.Is(err, epifierrors.ErrRecordNotFound) && !latestDownloadProcessRes.GetProcessStatus().IsTerminalState() {
		return nil
	}

	crRes, err := crf.creditReportClient.StartDownloadProcess(ctx, &creditReportPb.StartDownloadProcessRequest{
		ActorId:    actorId,
		RequestId:  uuid.New().String(),
		Provenance: creditReportPb.Provenance_PROVENANCE_ANALYSER_AUTO_REFRESH,
		Vendor:     commonvgpb.Vendor_EXPERIAN,
		RedirectDeeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_ANALYSER_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_AnalyserScreenOptions{
				AnalyserScreenOptions: &deeplinkPb.AnalyserScreenOptions{
					AnalyserName: analyserFePb.AnalyserName_ANALYSER_NAME_CREDIT_SCORE.String(),
				},
			},
		},
		AllowWithoutPan: true,
	})
	if rpcErr := epifigrpc.RPCError(crRes, err); rpcErr != nil {
		return fmt.Errorf("failed to start credit report download process: %w", rpcErr)
	}

	return nil
}

// getLatestRefreshDetails fetched latest credit report download process details
func (crf *CreditReportFetcher) getLatestRefreshDetails(ctx context.Context, actorId string) (*creditReportPb.GetLatestDownloadProcessDetailsResponse, error) {
	latestDownloadProcessRes, err := crf.creditReportClient.GetLatestDownloadProcessDetails(ctx, &creditReportPb.GetLatestDownloadProcessDetailsRequest{
		ActorId:    actorId,
		Provenance: creditReportPb.Provenance_PROVENANCE_ANALYSER_AUTO_REFRESH,
		Vendor:     commonvgpb.Vendor_EXPERIAN,
	})

	if rpcErr := epifigrpc.RPCError(latestDownloadProcessRes, err); rpcErr != nil {
		if latestDownloadProcessRes.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("no download process found for credit report refresh: %w", epifierrors.ErrRecordNotFound)
		}
		return nil, fmt.Errorf("failed to start download process: %w", rpcErr)
	}

	return latestDownloadProcessRes, nil
}

// isLatestRefreshComplete polls the refresh status till the timeout duration
// if case the process is complete it return true
// if case of any error the poller stops and returns error
// if timeout duration is breached the process return with completion status false
func (crf *CreditReportFetcher) isLatestRefreshComplete(ctx context.Context, actorId string) (bool, error) {
	// check the status before starting poller to avoid unnecessary 1 sec delay
	details, err := crf.getLatestRefreshDetails(ctx, actorId)
	if err != nil {
		return false, fmt.Errorf("failed to get latest report refresh status: %w", err)
	}
	if details.GetProcessStatus().IsTerminalState() {
		return true, nil
	}

	ticker := time.NewTicker(1 * time.Second)
	defer func() { ticker.Stop() }()

	startTime := crf.defaultTime.Now()

	for {
		<-ticker.C
		details, err := crf.getLatestRefreshDetails(ctx, actorId)
		if err != nil {
			return false, fmt.Errorf("poller failed to get latest report refresh status: %w", err)
		}
		if details.GetProcessStatus().IsTerminalState() {
			return true, nil
		}
		if crf.defaultTime.Since(startTime) > crf.creditScoreAnalyserCfg.AutoRefreshPollerTimeoutDuration() {
			return false, nil
		}
	}
}
