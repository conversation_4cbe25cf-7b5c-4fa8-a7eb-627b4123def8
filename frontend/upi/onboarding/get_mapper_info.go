package onboarding

import (
	"context"
	"fmt"
	"strconv"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	upiPkg "github.com/epifi/gamma/pkg/upi"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	feUpiOnbPb "github.com/epifi/gamma/api/frontend/upi/onboarding"
	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	upi2 "github.com/epifi/gamma/api/upi"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
)

const (
	Cancel                = "Cancel"
	Confirm               = "Confirm"
	currentlyLinkedTo     = "Your UPI Number is currently linked to "
	LinkYourUpiNumberHere = ". Link here to receive payments in your bank account linked with "
	LinkToFi              = "and link it to Fi"
	FontColour            = "#333333"
	LinkUpiNumberToFi     = "Link UPI number to Fi"
	NumberAvailable       = "Number available"
	NumberUnavailable     = "Number unavailable"
	// Colour/Error Red (FA3B11)
	UpiNumberUnavailableTextFontColour = "#FA3B11"
	// Colour/Dark mint (87BA6B)
	UpiNumberAvailableTextFontColour = "#87BA6B"
)

var (
	UpiNumberUnavailableText = commontypes.GetTextFromStringFontColourFontStyle(
		NumberUnavailable,
		UpiNumberUnavailableTextFontColour,
		commontypes.FontStyle_BODY_4,
	)

	UpiNumberAvailableText = commontypes.GetTextFromStringFontColourFontStyle(
		NumberAvailable,
		UpiNumberAvailableTextFontColour,
		commontypes.FontStyle_BODY_4,
	)
)

// GetMapperInfo checks the availability of a upiNumber
// If UpiNumberType is
//   - NUMERIC_ID
//     Response is sent using UpiNumberAvailabilityInfo
//   - PHONE_NUMBER
//     If it is not being used anywhere (neither on Fi nor on any other psp)
//     Response is sent using UpiNumberAvailabilityInfo
//     else
//     Response is sent using UpiNumberPortInfo
func (s *Service) GetMapperInfo(ctx context.Context, req *feUpiOnbPb.GetMapperInfoRequest) (*feUpiOnbPb.GetMapperInfoResponse, error) {
	var (
		res = &feUpiOnbPb.GetMapperInfoResponse{
			RespHeader: &headerPb.ResponseHeader{},
		}
		getMapperInfoBeRequest  *upiOnbPb.GetMapperInfoRequest
		getMapperInfoBeResponse *upiOnbPb.GetMapperInfoResponse
		err                     error
	)

	// check if upiNumber is valid
	if req.GetUpiNumber() == "" {
		logger.Error(ctx, "upi-number can't be empty", zap.Error(epifierrors.ErrInvalidArgument))
		res.RespHeader.Status = rpcPb.StatusInvalidArgument()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	// build getMapperInfo backend request of type CHECK since we need to check availability of upiNumber
	if getMapperInfoBeRequest, err = s.buildGetMapperInfoBeRequest(ctx, req, feUpiOnbEnumsPb.UpiNumberLinkingType_UPI_NUMBER_LINKING_TYPE_NEW); err != nil {
		logger.Error(ctx, "error in building getMapperInfo backend request",
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusFromError(err)
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	// populate the content client needs to show to the user based on the upiNumberType
	switch req.GetUpiNumberType() {
	case feUpiOnbEnumsPb.UpiNumberType_UPI_NUMBER_TYPE_NUMERIC_ID:
		// call backend rpc to check availability of upiNumber
		getMapperInfoBeResponse, err = s.upiOnboardingClient.GetMapperInfo(ctx, getMapperInfoBeRequest)
		if err = epifigrpc.RPCError(getMapperInfoBeResponse, err); err != nil || len(getMapperInfoBeResponse.GetUpiMapperInfos()) == 0 {
			// ignoring error here, as we need to let user know that this numeric id in unavailable
			logger.Error(ctx, "error in calling getMapperInfo backend RPC or empty mapper infor received",
				zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
			res.Identifier = &feUpiOnbPb.GetMapperInfoResponse_UpiNumberAvailabilityInfo{
				UpiNumberAvailabilityInfo: &feUpiOnbPb.UpiNumberAvailabilityInfo{
					Text:                 UpiNumberUnavailableText,
					IsUpiNumberAvailable: false,
				},
			}
			res.RespHeader.Status = rpcPb.StatusOk()
			return res, nil
		}
		if len(getMapperInfoBeResponse.GetUpiMapperInfos()) > 0 && getMapperInfoBeResponse.GetUpiMapperInfos()[0].GetUpiNumberState() != upiOnbEnumsPb.UpiNumberState_UPI_NUMBER_STATE_NEW {
			res.Identifier = &feUpiOnbPb.GetMapperInfoResponse_UpiNumberSamePspPortInfo{
				UpiNumberSamePspPortInfo: &feUpiOnbPb.UpiNumberSamePspPortInfo{
					PrevVpa: &feUpiOnbPb.VpaInfo{Vpa: getMapperInfoBeResponse.GetUpiMapperInfos()[0].GetVpa()},
					NextVpa: &feUpiOnbPb.VpaInfo{Vpa: req.GetVpa()},
				},
			}
			res.RespHeader.Status = rpcPb.StatusOk()
			return res, nil
		}
		res.Identifier = &feUpiOnbPb.GetMapperInfoResponse_UpiNumberAvailabilityInfo{
			UpiNumberAvailabilityInfo: &feUpiOnbPb.UpiNumberAvailabilityInfo{
				Text:                 UpiNumberAvailableText,
				IsUpiNumberAvailable: true,
			},
		}
	case feUpiOnbEnumsPb.UpiNumberType_UPI_NUMBER_TYPE_PHONE_NUMBER:
		// call backend rpc to check availability of upiNumber
		getMapperInfoBeResponse, err = s.upiOnboardingClient.GetMapperInfo(ctx, getMapperInfoBeRequest)
		switch {
		case err != nil:
			logger.Error(ctx, "error in calling getMapperInfo backend RPC",
				zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		// If PhoneNumber is not linked to any psp
		case getMapperInfoBeResponse.GetStatus().GetCode() == uint32(upiOnbPb.GetMapperInfoResponse_MAPPING_DOES_NOT_EXISTS):
			logger.Debug(ctx, "No mapping found for given upi number", zap.String(logger.UPI_NUMBER, req.GetUpiNumber()))
			res.Identifier = &feUpiOnbPb.GetMapperInfoResponse_UpiNumberAvailabilityInfo{
				UpiNumberAvailabilityInfo: &feUpiOnbPb.UpiNumberAvailabilityInfo{
					IsUpiNumberAvailable: true,
					Text:                 UpiNumberAvailableText,
				},
			}
			res.RespHeader.Status = rpcPb.StatusOk()
			return res, nil
		//	If PhoneNumber is already linked to a different VPA, attempt PORT request
		case getMapperInfoBeResponse.GetStatus().GetCode() == uint32(upiOnbPb.GetMapperInfoResponse_ID_MAPPED_TO_DIFFERENT_VPA):
			getMapperInfoPortReq, buildErr := s.buildGetMapperInfoBeRequest(ctx, req, feUpiOnbEnumsPb.UpiNumberLinkingType_UPI_NUMBER_LINKING_TYPE_PORT)
			if buildErr != nil {
				logger.Error(ctx, "error in building getMapperInfo backend request of type port", zap.Error(buildErr))
				res.RespHeader.Status = rpcPb.StatusFromError(err)
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
			portMapperInfoRes, portErr := s.upiOnboardingClient.GetMapperInfo(ctx, getMapperInfoPortReq)
			if te := epifigrpc.RPCError(portMapperInfoRes, portErr); te != nil && portMapperInfoRes.GetStatus().GetCode() != uint32(upiOnbPb.GetMapperInfoResponse_MAPPING_DOES_NOT_EXISTS) {
				logger.Error(ctx, "error attempting port request", zap.Error(te))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}

			oldVpa := portMapperInfoRes.GetUpiMapperInfos()[0].GetVpa()
			isSamePspHandle, pspErr := s.isSamePspHandle(oldVpa, req.GetVpa())
			if pspErr != nil {
				logger.Error(ctx, "error checking if same psp handle", zap.Error(pspErr))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
			if isSamePspHandle {
				// sent for info to client that it is same PSP
				res.Identifier = s.getSamePspPortInfo(oldVpa, req.GetVpa())
			} else {
				// client needs to show port info to the user
				res.Identifier = getUpiNumberPortInfo(ctx, req.GetVpa(), portMapperInfoRes)
			}
			res.RespHeader.Status = rpcPb.StatusOk()
			return res, nil
		case !getMapperInfoBeResponse.GetStatus().IsSuccess():
			logger.Error(ctx, "getMapperInfo backend RPC returned non success status code",
				zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.String(logger.STATUS_CODE, strconv.Itoa(int(getMapperInfoBeResponse.GetStatus().GetCode()))))
			res.RespHeader.Status = rpcPb.StatusFromError(err)
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}

		// send availability info to the client
		res.Identifier = &feUpiOnbPb.GetMapperInfoResponse_UpiNumberAvailabilityInfo{
			UpiNumberAvailabilityInfo: &feUpiOnbPb.UpiNumberAvailabilityInfo{
				Text:                 UpiNumberAvailableText,
				IsUpiNumberAvailable: true,
			},
		}
	}

	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

// buildGetMapperInfoBeRequest builds the request for GetMappperInfo Backend Rpc
func (s *Service) buildGetMapperInfoBeRequest(ctx context.Context, req *feUpiOnbPb.GetMapperInfoRequest, linkingType feUpiOnbEnumsPb.UpiNumberLinkingType) (*upiOnbPb.GetMapperInfoRequest, error) {
	// fetch phoneNumber for the actor
	phoneNumber, err := s.getPhoneNumberForActor(ctx, req.GetReq().GetAuth().GetActorId())
	if err != nil {
		return nil, err
	}

	getMapperInfoBeRequest := &upiOnbPb.GetMapperInfoRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		Device:  upi2.FromFeDevice(ctx, req.GetReq().GetAuth().GetDevice(), phoneNumber),
		Vpa:     req.GetVpa(),
	}
	switch linkingType {
	case feUpiOnbEnumsPb.UpiNumberLinkingType_UPI_NUMBER_LINKING_TYPE_NEW:
		getMapperInfoBeRequest.Identifier = &upiOnbPb.GetMapperInfoRequest_CheckRequest{
			CheckRequest: &upiOnbPb.CheckRequest{
				UpiNumber:     req.GetUpiNumber(),
				UpiNumberType: feToBeUpiNumberTypeMap[req.GetUpiNumberType()],
			},
		}
	case feUpiOnbEnumsPb.UpiNumberLinkingType_UPI_NUMBER_LINKING_TYPE_PORT:
		getMapperInfoBeRequest.Identifier = &upiOnbPb.GetMapperInfoRequest_PortRequest{
			PortRequest: &upiOnbPb.PortRequest{
				PhoneNumber: req.GetUpiNumber(),
			},
		}
	// use check request as default
	default:
		getMapperInfoBeRequest.Identifier = &upiOnbPb.GetMapperInfoRequest_CheckRequest{
			CheckRequest: &upiOnbPb.CheckRequest{
				UpiNumber:     req.GetUpiNumber(),
				UpiNumberType: feToBeUpiNumberTypeMap[req.GetUpiNumberType()],
			},
		}
	}
	return getMapperInfoBeRequest, nil
}

// getUpiNumberPortInfo generates the UpiNumberPortInfo for the client when upiNumberType - PhoneNumber
func getUpiNumberPortInfo(ctx context.Context, nextVpa string, getMapperInfoBeResponse *upiOnbPb.GetMapperInfoResponse) *feUpiOnbPb.GetMapperInfoResponse_UpiNumberPortInfo {
	upiNumberInfo := getMapperInfoBeResponse.GetUpiMapperInfos()[0]
	preVpaLogo, err := upiPkg.GetPspImageForVpa(ctx, upiNumberInfo.GetVpa())
	if err != nil {
		logger.Error(ctx, "error getting prev psp logo from vpa", zap.Error(err))
	}
	nextVpaLogo, err := upiPkg.GetPspImageForVpa(ctx, nextVpa)
	if err != nil {
		logger.Error(ctx, "error getting next psp logo from vpa", zap.Error(err))
	}
	return &feUpiOnbPb.GetMapperInfoResponse_UpiNumberPortInfo{
		UpiNumberPortInfo: &feUpiOnbPb.UpiNumberPortInfo{
			Title: commontypes.GetTextFromStringFontColourFontStyle(LinkUpiNumberToFi, FontColour, commontypes.FontStyle_SUBTITLE_2),
			PreVpa: &feUpiOnbPb.VpaInfo{
				Vpa:        upiNumberInfo.GetVpa(),
				PspLogoUrl: preVpaLogo,
			},
			NextVpa: &feUpiOnbPb.VpaInfo{
				Vpa:        nextVpa,
				PspLogoUrl: nextVpaLogo,
			},
			Description: &typesUiPb.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(currentlyLinkedTo, FontColour, commontypes.FontStyle_BODY_4),
					commontypes.GetTextFromStringFontColourFontStyle(upiNumberInfo.GetVpa(), FontColour, commontypes.FontStyle_CAPTION_1),
					commontypes.GetTextFromStringFontColourFontStyle(LinkYourUpiNumberHere, FontColour, commontypes.FontStyle_BODY_4),
					commontypes.GetTextFromStringFontColourFontStyle(nextVpa, FontColour, commontypes.FontStyle_BODY_4),
				},
			},
			Ctas: []*deeplinkPb.Cta{
				{
					Type:         deeplinkPb.Cta_DONE,
					Text:         Cancel,
					DisplayTheme: deeplinkPb.Cta_TERTIARY,
				},
				{
					Type:         deeplinkPb.Cta_CONTINUE,
					Text:         Confirm,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
				},
			},
		},
	}
}

func (s *Service) getSamePspPortInfo(prevVpa, nextVpa string) *feUpiOnbPb.GetMapperInfoResponse_UpiNumberSamePspPortInfo {
	return &feUpiOnbPb.GetMapperInfoResponse_UpiNumberSamePspPortInfo{
		UpiNumberSamePspPortInfo: &feUpiOnbPb.UpiNumberSamePspPortInfo{
			PrevVpa: &feUpiOnbPb.VpaInfo{
				Vpa: prevVpa,
			},
			NextVpa: &feUpiOnbPb.VpaInfo{
				Vpa: nextVpa,
			},
		},
	}
}

func (s *Service) isSamePspHandle(prevVpa, nextVpa string) (bool, error) {
	prevPspHandle, err := upiPkg.GetPspHandleFromVpa(prevVpa)
	if err != nil {
		return false, fmt.Errorf("error getting prev psp handle %w", err)
	}
	nextPspHandle, err := upiPkg.GetPspHandleFromVpa(nextVpa)
	if err != nil {
		return false, fmt.Errorf("error getting next psp handle %w", err)
	}

	// implies both VPAs belong within Fi PSP
	if prevPspHandle == nextPspHandle {
		return true, nil
	}

	return false, nil
}
