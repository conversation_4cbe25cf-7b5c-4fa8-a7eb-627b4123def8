groups:
- name: system_services_alert.rules
  rules:
  - alert: JenkinsServiceDown
    expr: node_systemd_unit_state{job="ec2-system",name="jenkins.service",state="active"} == 0
    for: 1m
    labels:
      severity: "p0"
      team: "devops"
    annotations:
      summary: "Endpoint {{ $labels.instance }} down"
      description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minutes."
  - alert: VictoriaMetricsServiceDown
    expr: node_systemd_unit_state{job="ec2-system",name="victoriametrics.service",state="active"} == 0
    for: 1m
    labels:
      severity: "p0"
      team: "devops"
    annotations:
      summary: "Endpoint {{ $labels.instance }} down"
      description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minutes."
  - alert: GrafanaServiceDown
    expr: node_systemd_unit_state{job="ec2-system",name="grafana-server.service",state="active"} == 0
    for: 1m
    labels:
      severity: "p0"
      team: "devops"
    annotations:
      summary: "Endpoint {{ $labels.instance }} down"
      description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minutes."
  - alert: DockerServiceDown
    expr: node_systemd_unit_state{job="ec2-system",name="docker.service",state="active"} == 0
    for: 1m
    labels:
      severity: "p0"
      team: "devops"
    annotations:
      summary: "Endpoint {{ $labels.instance }} down"
      description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minutes."
  - alert: FluentbitServiceDown
    expr: >-
      (
        node_systemd_unit_state{
          job="ec2-system",
          name="fluent-bit.service",
          state="active",
          environment="prod"
        } != 1
      ) or (
        node_systemd_unit_state{
          job="ec2-system",
          name="fluent-bit.service",
          state="failed",
          environment="prod"
        } != 0
      )
    for: 5m
    labels:
      severity: "P1"
      team: "ied"
    annotations:
      summary: "down: fluentbit ec2 agent for {{ $labels.service }}"
      description: "fluentbit agent for {{ $labels.service }} on {{ $labels.instance_id }} in {{ $labels.environment }} is down for last 5m"
  - alert: VPNServiceDown
    expr: node_systemd_unit_state{job="ec2-system",name="pritunl.service",state="active"} == 0
    for: 1m
    labels:
      severity: "p0"
      team: "devops"
    annotations:
      summary: "Endpoint {{ $labels.instance }} down"
      description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minutes."
