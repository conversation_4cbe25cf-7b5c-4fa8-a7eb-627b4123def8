groups:
  - name: frontend-wealthonboarding.rules
    rules:
      - alert: GetNextOnboardingStep
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|DeadlineExceeded|NotFound",grpc_method="GetNextOnboardingStep", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="GetNextOnboardingStep",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 5 and ON() sum(increase(grpc_server_handled_total{environment="prod", grpc_method="GetNextOnboardingStep", grpc_service="frontend.wealthonboarding.WealthOnboarding", grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|NotFound"}[1h])) > 10
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
      - alert: CollectDataFromCustomer
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|InvalidArgument|Code\\(100\\)|Code\\(101\\)|Code\\(102\\)|DeadlineExceeded",grpc_method="CollectDataFromCustomer", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="CollectDataFromCustomer",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 5 and ON() sum(increase(grpc_server_handled_total{environment="prod", grpc_method="CollectDataFromCustomer", grpc_service="frontend.wealthonboarding.WealthOnboarding", grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|InvalidArgument|Code\\(100\\)|Code\\(101\\)|Code\\(102\\)|DeadlineExceeded"}[3h])) > 5
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
      - alert: ConfirmComplianceData
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|DeadlineExceeded",grpc_method="ConfirmComplianceData", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="ConfirmComplianceData",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 5 and ON() sum(increase(grpc_server_handled_total{environment="prod", grpc_method="ConfirmComplianceData", grpc_service="frontend.wealthonboarding.WealthOnboarding", grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled"}[3h])) > 5
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
      - alert: DownloadDigilockerDocs
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|DeadlineExceeded",grpc_method="DownloadDigilockerDocs", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="DownloadDigilockerDocs",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 5 and ON() sum(increase(grpc_server_handled_total{environment="prod", grpc_method="DownloadDigilockerDocs", grpc_service="frontend.wealthonboarding.WealthOnboarding", grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled"}[6h])) > 5
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
      - alert: GenerateOTP
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|Code\\(100\\)|Code\\(101\\)Code\\(102\\)|Code\\(103\\)",grpc_method="GenerateOTP", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="GenerateOTP",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 5 and ON() sum(increase(grpc_server_handled_total{environment="prod", grpc_method="GenerateOTP", grpc_service="frontend.wealthonboarding.WealthOnboarding", grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|Code\\(100\\)|Code\\(101\\)Code\\(102\\)|Code\\(103\\)"}[1h])) > 5
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
      - alert: CollectDataFromCustomerWithStream
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|DeadlineExceeded",grpc_method="CollectDataFromCustomerWithStream", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="CollectDataFromCustomerWithStream",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 5 and ON() sum(increase(grpc_server_handled_total{environment="prod", grpc_method="CollectDataFromCustomerWithStream", grpc_service="frontend.wealthonboarding.WealthOnboarding", grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled"}[6h])) > 5
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
      - alert: GetNextOnboardingStepDeadlineExceeded
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code="DeadlineExceeded",grpc_method="GetNextOnboardingStep", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="GetNextOnboardingStep",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 80
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
      - alert: CollectDataFromCustomerDeadlineExceeded
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code="DeadlineExceeded",grpc_method="CollectDataFromCustomer", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="CollectDataFromCustomer",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 80
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
      - alert: ConfirmComplianceDataDeadlineExceeded
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code="DeadlineExceeded",grpc_method="ConfirmComplianceData", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="ConfirmComplianceData",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 80
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
      - alert: DownloadDigilockerDocsDeadlineExceeded
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code="DeadlineExceeded",grpc_method="DownloadDigilockerDocs", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="DownloadDigilockerDocs",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 80
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
      - alert: CollectDataFromCustomerWithStreamDeadlineExceeded
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code="DeadlineExceeded",grpc_method="CollectDataFromCustomerWithStream", grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="CollectDataFromCustomerWithStream",grpc_service="frontend.wealthonboarding.WealthOnboarding"}[30m])) by (environment,grpc_service,grpc_method,service) * 100  > 80
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "wealth onboarding frontend rpc {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "wealth onboarding frontend rpc {{ $labels.grpc_method }} has been down for more than 15m."
