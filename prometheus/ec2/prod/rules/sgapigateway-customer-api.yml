groups:
  - name: sgapigateway-customer-api.rules
    rules:
      - alert: Dedupe<PERSON>heck
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled",grpc_method="DedupeCheck",grpc_service="stockguardian.sgapigateway.customer.CustomerService"}[1h])) by (environment,grpc_service,grpc_method,service) / sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="DedupeCheck",grpc_service="stockguardian.sgapigateway.customer.CustomerService"}[1h])) by (environment,grpc_service,grpc_method,service) * 100  > 2 and ON()  sum(increase(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled",grpc_service="stockguardian.sgapigateway.customer.CustomerService",grpc_method="DedupeCheck"}[1h])) >= 2
        for: 15m
        labels:
          severity: p1
          team: onboarding
        annotations:
          summary: '{{ $labels.grpc_service }} api {{ $labels.grpc_method }} is down in {{ $labels.environment }}.'
          description: '{{ $labels.grpc_service }} api {{ $labels.grpc_method }} has been down for more than 15m.'
