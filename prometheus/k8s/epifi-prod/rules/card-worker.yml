groups:
  - name: card-worker.rules
    rules:
      - alert: DCWorkflowTaskBacklog
        expr: histogram_quantile(0.95, (sum by(workflow_type, le) (rate(temporal_workflow_task_schedule_to_start_latency_seconds_bucket{exported_namespace="prod_card",task_queue="prod_card_task_queue"}[1h])) OR ON() vector(0))) > 0.5
        for: 15m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "Workflow task backlog detected for {{ $labels.workflow_type }}"
          description: "schedule to start latency is beyond 500ms for {{ $labels.workflow_type }} "
      - alert: CardWorkerMemoryUtilization
        expr: (sum (container_memory_working_set_bytes{pod=~"prod-card-worker-.*", container!~"POD", container!~""}) by (pod) / sum (container_spec_memory_limit_bytes{pod=~"prod-card-worker-.*", container!~"POD", container!~""}) by (pod)) * 100 > 80
        for: 5m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "card worker pod memory utilization threshold breached"
          description: "card worker pod memory utilization crossed the threshold of 80% for {{ $labels.pod }}"
      - alert: CardWorkerCPUUtilization
        expr: ((sum (container_cpu_usage_seconds_total{pod=~"prod-card-worker-.*", container!~"POD", container!~""}) by (pod) / sum (kube_pod_container_resource_limits{pod=~"prod-card-worker-.*", container!~"POD", container!~""}) by (pod)) * 100) > 80
        for: 5m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "card worker pod CPU utilization threshold breached"
          description: "card worker pod CPU utilization crossed the threshold of 80% for {{ $labels.pod }}"
      - alert: CardWorkflowFailurePercentage
        expr: ((sum by(exported_namespace) (increase(workflow_failed{operation="CompletionStats",type="history", exported_namespace="prod_card"}[1h])) OR ON() vector(0)) / ((sum by(exported_namespace) (increase(workflow_failed{operation="CompletionStats",type="history", exported_namespace="prod_card"}[1h])) OR ON() vector(0)) + (sum by(exported_namespace) (increase(workflow_timedout{operation="CompletionStats",type="history", exported_namespace="prod_card"}[1h])) OR ON() vector(0)) + (sum by(exported_namespace) (increase(workflow_success{operation="CompletionStats",type="history", exported_namespace="prod_card"}[1h])) OR ON() vector(0)))) * 100 > 20 AND (sum by(exported_namespace) (increase(workflow_failed{operation="CompletionStats",type="history", exported_namespace="prod_card"}[1h])) > 5)
        for: 15m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "Spike in card workflow failure percentage"
          description: "Failure percentage for card workflow breached the threshold of 20% for namespace {{ $labels.exported_namespace }}"
      - alert: CardWorkflowsFailurePercentage
        expr: ((sum by(exported_namespace, workflow_type) (rate(temporal_workflow_failed_total{exported_namespace="prod_card"}[1h])) OR ON() vector(0)) / ((sum by(exported_namespace, workflow_type) (rate(temporal_workflow_failed_total{exported_namespace="prod_card"}[1h])) OR ON() vector(0)) + (sum by(exported_namespace, workflow_type) (rate(temporal_workflow_completed_total{exported_namespace="prod_card"}[1h])) OR ON() vector(0))))*100 > 15  AND (sum by(exported_namespace, workflow_type) (rate(temporal_workflow_failed_total{exported_namespace="prod_card"}[1h])) > 5)
        for: 15m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "Spike in card workflows failure percentage"
          description: "Failure percentage (failed/(failed+completed)) for card workflows breached the threshold of 15% for exported namespace {{ $labels.exported_namespace }} and workflow {{ $labels.workflow_type }}"
      - alert: AddressUpdateStageFailure
        expr: sum(increase(celestial_workflow_stage_status_total{container="card-worker", exported_namespace=~"prod-card",workflow_type=~"OrderPhysicalCardWithChargesV2",stage="SHIPPING_ADDRESS_UPDATE",stage_status!~"SUCCESSFUL|INITIATED|CREATED"}[10m])) by (container, exported_namespace, workflow_type) >= 1
        for: 10m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "Spike in SHIPPING_ADDRESS_UPDATE stage failures of workflow OrderPhysicalCardWithChargesV2"
      - alert: FulfillmentStageFailure
        expr: sum(increase(celestial_workflow_stage_status_total{container="card-worker", exported_namespace=~"prod-card",workflow_type=~"OrderPhysicalCardWithChargesV2",stage="FULFILLMENT",stage_status!~"SUCCESSFUL|INITIATED|CREATED"}[10m])) by (container, exported_namespace, workflow_type) >= 1
        for: 10m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "Spike in FULFILLMENT stage failures of workflow OrderPhysicalCardWithChargesV2"
      - alert: BlockCardStageFailure
        expr: sum(increase(celestial_workflow_stage_status_total{container="card-worker", exported_namespace=~"prod-card",workflow_type=~"RenewCard",stage="BLOCK_CARD_STAGE",stage_status!~"SUCCESSFUL|INITIATED|CREATED"}[10m])) by (container, exported_namespace, workflow_type) >= 1.1
        for: 5m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "Spike in BLOCK_CARD_STAGE stage failures of workflow RenewCard"
      - alert: CreateCardStageFailures
        expr: sum(increase(temporal_activity_failures_total{container="card-worker", activity_type=~"CreateNewCard|PollCardCreationStatus",workflow_type="RenewCard", error_type="PermanentError"}[10m]) OR ON() vector(0)) >= 1
        for: 0s
        labels:
          severity: "p0"
          team: pay
          service: "card-worker"
        annotations:
          summary: "Spike in CREATE_NEW_CARD_STAGE stage failures of workflow RenewCard"
      - alert: CreateCardStageFailuresBusinessFailures
        expr: sum(increase(temporal_activity_failures_total{container="card-worker", activity_type="PollCardCreationStatus",workflow_type="RenewCard", error_type="BusinessFailure"}[10m]) OR ON() vector(0)) >= 5
        for: 5m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "Spike in CREATE_NEW_CARD_STAGE stage business failures of workflow RenewCard"
      - alert: PhysicalCardDispatchStageFailure
        expr: sum(increase(celestial_workflow_stage_status_total{container="card-worker", exported_namespace=~"prod-card",workflow_type=~"RenewCard",stage="INITIATE_PHYSICAL_CARD_DISPATCH_STAGE",stage_status!~"SUCCESSFUL|INITIATED|CREATED|PENDING"}[10m]) OR ON() vector(0)) >= 2
        for: 5m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "Spike in INITIATE_PHYSICAL_CARD_DISPATCH_STAGE stage failures of workflow RenewCard"
      - alert: DeliveryTrackingStageFailure
        expr: sum(increase(celestial_workflow_stage_status_total{container="card-worker", exported_namespace=~"prod-card",workflow_type=~"TrackCardDelivery",stage="DELIVERY_FULFILLMENT",stage_status!~"SUCCESSFUL|INITIATED|CREATED"}[15m])) by (container, exported_namespace, workflow_type)/sum(increase(celestial_workflow_stage_status_total{container="card-worker", exported_namespace=~"prod-card",workflow_type=~"TrackCardDelivery",stage="DELIVERY_FULFILLMENT"}[15m])) by (container, exported_namespace, workflow_type) * 100 > 5 and ON() sum(increase(celestial_workflow_stage_status_total{container="card-worker", exported_namespace=~"prod-card",workflow_type=~"TrackCardDelivery",stage="DELIVERY_FULFILLMENT",stage_status!~"SUCCESSFUL|INITIATED|CREATED"}[15m])) > 15
        for: 10m
        labels:
          severity: "p1"
          team: pay
          service: "card-worker"
        annotations:
          summary: "Spike in DELIVERY_FULFILLMENT stage failures of workflow TrackCardDelivery"
      - alert: TrackShipmentActivityFailure
        expr: sum(increase(temporal_activity_failures_total{container="card-worker", activity_type="TrackShipment",workflow_type="TrackCardDelivery", error_type="PermanentError"}[15m]) OR ON() vector(0)) >= 5
        for: 10m
        labels:
          severity: "p0"
          team: central-growth
          service: "card-worker"
        annotations:
          summary: "Spike in TrackShipment activity failures of workflow TrackCardDelivery"
