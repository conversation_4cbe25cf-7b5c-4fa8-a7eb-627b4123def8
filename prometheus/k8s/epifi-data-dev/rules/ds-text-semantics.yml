groups:
- name: ds-text-semantics.rules
  rules:
    # Realtime Text Semantics service
    ## High CPU usage alert
    - alert: RealtimeDSTextSemanticsHighCPU
      expr: sum(rate(container_cpu_usage_seconds_total{pod=~"data-dev-text-semantics-.*", container!~"POD", container!~""}[5m]))/sum(kube_pod_container_info{pod=~"data-dev-text-semantics-.*", container!~"POD", container!~""})/sum(topk(1,kube_pod_container_resource_requests{unit="core",pod=~"data-dev-text-semantics-.*"})) > 0.8
      for: 5m
      labels:
        severity: "p1"
        service: 'ds-text-semantics'
        team: "datascience"
      annotations:
        summary: "Realtime ds-text-semantics service high CPU"
        description: "CPU usage on ds-text-semantics has been above 80% for the last 5 minutes"
    ## High Memory usage alert
    - alert: RealtimeDSTextSemanticsHighMemory
      expr: sum(container_memory_working_set_bytes{pod=~"data-dev-text-semantics-.*", container!~"POD", container!~""})/sum (kube_pod_container_resource_limits{pod=~"data-dev-text-semantics-.*", container!~"POD", container!~""}) > 0.8
      for: 5m
      labels:
        severity: "p1"
        service: 'ds-text-semantics'
        team: "datascience"
      annotations:
        summary: "Realtime ds-text-semantics service high memory"
        description: "Memory usage on ds-text-semantics has been above 80% for the last 5 minutes"
    ## High API response time for ontology map endpoint
    - alert: RealtimeDSTextSemanticsOntologyHighAPIResponseTime
      expr: histogram_quantile(0.95, sum by (le) (rate(text_semantics_requests_processing_time_seconds_bucket{path_template="/v1/ontology_map"}[1m]))) > 0.15
      for: 5m
      labels:
        severity: "p0"
        service: 'ds-text-semantics'
        team: "datascience"
      annotations:
        summary: "Realtime ds-text-semantics service high response time"
        description: "Response time on ds-text-semantics has been above 150ms for the last 5 minutes for ontology map endpoint"
    ## High Text Semantics API error counts for ontology map endpoint
    - alert: RealtimeDSTextSemanticsOntologyHigh5xx
      expr: sum(rate(text_semantics_responses_total{path_template="/v1/ontology_map", status_code=~"5.*"}[1m])) > 0.1
      for: 1m
      labels:
        severity: "p0"
        service: 'ds-text-semantics'
        team: "datascience"
      annotations:
        summary: "Realtime ds-text-semantics API high errors"
        description: "Realtime ds-text-semantics service API response is not 200 for the last 1 minute for ontology map endpoint"
    ## Text Semantics service down alert
    - alert: RealTimeDSTextSemanticsServiceDown
      expr: absent(text_semantics_requests_total{path_template='/_health'}) == 1
      for: 1m
      labels:
        severity: "p0"
        service: "ds-text-semantics"
        team: "datascience"
      annotations:
        summary: "Realtime ds-text-semantics service down"
        description: "Realtime ds-text-semantics is down for the last 1 minute"
