package generators

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/actor"
	mockActor "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/frontend/insights/story"
	"github.com/epifi/gamma/api/insights/story/model"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/group/mocks"
	"github.com/epifi/gamma/insights/config"
	"github.com/epifi/gamma/insights/config/genconf"
	"github.com/epifi/be-common/pkg/datetime"
	mockTime "github.com/epifi/be-common/pkg/datetime/mocks"
)

func TestMonthlyLottieGenerator_GenerateValues(t *testing.T) {
	type fields struct {
		isLottieExperimentEnabled bool
	}
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		setupMocks  func(actorClient *mockActor.MockActorClient, userGrpClient *mocks.MockGroupClient, timeClient *mockTime.MockTime)
		wantValues  map[string]string
		wantActions map[string]*story.StoryAppAction
		wantErr     bool
	}{
		{
			name: "successfully generated lottie var values",
			fields: fields{
				isLottieExperimentEnabled: false,
			},
			setupMocks: func(actorClient *mockActor.MockActorClient, userGrpClient *mocks.MockGroupClient, timeClient *mockTime.MockTime) {
				timeClient.EXPECT().Now().Return(time.Date(2023, 5, 1, 0, 0, 0, 0, datetime.IST))
			},
			args: args{
				ctx:     context.Background(),
				actorId: actorId1,
			},
			wantValues: map[string]string{
				MonthlyLottieStories_MonthlyAbout_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Apr-About.json",
				MonthlyLottieStories_MonthlyOutro_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Apr-Outro.json",
				MonthlyLottieStories_MonthlyIntro_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Apr-Intro.json",
			},
			wantActions: map[string]*story.StoryAppAction{},
			wantErr:     false,
		},
		{
			name: "successfully generated lottie var values - 2",
			fields: fields{
				isLottieExperimentEnabled: false,
			},
			setupMocks: func(actorClient *mockActor.MockActorClient, userGrpClient *mocks.MockGroupClient, timeClient *mockTime.MockTime) {
				timeClient.EXPECT().Now().Return(time.Date(2023, 10, 1, 0, 0, 0, 0, datetime.IST))
			},
			args: args{
				ctx:     context.Background(),
				actorId: actorId1,
			},
			wantValues: map[string]string{
				MonthlyLottieStories_MonthlyAbout_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-About.json",
				MonthlyLottieStories_MonthlyOutro_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-Outro.json",
				MonthlyLottieStories_MonthlyIntro_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-Intro.json",
			},
			wantActions: map[string]*story.StoryAppAction{},
			wantErr:     false,
		},
		{
			name: "successfully generated lottie var values in experiment for internal user",
			fields: fields{
				isLottieExperimentEnabled: true,
			},
			setupMocks: func(actorClient *mockActor.MockActorClient, userGrpClient *mocks.MockGroupClient, timeClient *mockTime.MockTime) {
				timeClient.EXPECT().Now().Return(time.Date(2023, 2, 1, 0, 0, 0, 0, datetime.IST))
				actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actor.GetEntityDetailsByActorIdRequest{
					ActorId: actorId1,
				}).Return(&actor.GetEntityDetailsByActorIdResponse{
					Status:  rpc.StatusOk(),
					EmailId: "<EMAIL>",
					Type:    types.ActorType_USER,
				}, nil)
				userGrpClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), &group.GetGroupsMappedToEmailRequest{
					Email: "<EMAIL>",
				}).Return(&group.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{
						commontypes.UserGroup_ALLROUND_FACILITIES,
						commontypes.UserGroup_INTERNAL,
					},
				}, nil)
			},
			args: args{
				ctx:     context.Background(),
				actorId: actorId1,
			},
			wantValues: map[string]string{
				MonthlyLottieStories_MonthlyAbout_Lottie: "test-url",
				MonthlyLottieStories_MonthlyOutro_Lottie: "test-url",
				MonthlyLottieStories_MonthlyIntro_Lottie: "test-url",
			},
			wantActions: map[string]*story.StoryAppAction{},
			wantErr:     false,
		},
		{
			name: "successfully generated lottie var values in experiment for non-internal user",
			fields: fields{
				isLottieExperimentEnabled: true,
			},
			setupMocks: func(actorClient *mockActor.MockActorClient, userGrpClient *mocks.MockGroupClient, timeClient *mockTime.MockTime) {
				timeClient.EXPECT().Now().Return(time.Date(2023, 10, 1, 0, 0, 0, 0, datetime.IST))
				actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actor.GetEntityDetailsByActorIdRequest{
					ActorId: actorId1,
				}).Return(&actor.GetEntityDetailsByActorIdResponse{
					Status:  rpc.StatusOk(),
					EmailId: "<EMAIL>",
					Type:    types.ActorType_USER,
				}, nil)
				userGrpClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), &group.GetGroupsMappedToEmailRequest{
					Email: "<EMAIL>",
				}).Return(&group.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{
						commontypes.UserGroup_ALLROUND_FACILITIES,
						commontypes.UserGroup_CX_INTERNAL,
					},
				}, nil)
			},
			args: args{
				ctx:     context.Background(),
				actorId: actorId1,
			},
			wantValues: map[string]string{
				MonthlyLottieStories_MonthlyAbout_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-About.json",
				MonthlyLottieStories_MonthlyOutro_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-Outro.json",
				MonthlyLottieStories_MonthlyIntro_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-Intro.json",
			},
			wantActions: map[string]*story.StoryAppAction{},
			wantErr:     false,
		},
		{
			name: "failed to get actor details, generated without experiment urls",
			fields: fields{
				isLottieExperimentEnabled: true,
			},
			setupMocks: func(actorClient *mockActor.MockActorClient, userGrpClient *mocks.MockGroupClient, timeClient *mockTime.MockTime) {
				timeClient.EXPECT().Now().Return(time.Date(2023, 10, 1, 0, 0, 0, 0, datetime.IST))
				actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actor.GetEntityDetailsByActorIdRequest{
					ActorId: actorId1,
				}).Return(&actor.GetEntityDetailsByActorIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			args: args{
				ctx:     context.Background(),
				actorId: actorId1,
			},
			wantValues: map[string]string{
				MonthlyLottieStories_MonthlyAbout_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-About.json",
				MonthlyLottieStories_MonthlyOutro_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-Outro.json",
				MonthlyLottieStories_MonthlyIntro_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-Intro.json",
			},
			wantActions: map[string]*story.StoryAppAction{},
			wantErr:     false,
		},
		{
			name: "failure in getting user group for actor, gen without experiment urls",
			fields: fields{
				isLottieExperimentEnabled: true,
			},
			setupMocks: func(actorClient *mockActor.MockActorClient, userGrpClient *mocks.MockGroupClient, timeClient *mockTime.MockTime) {
				timeClient.EXPECT().Now().Return(time.Date(2023, 10, 1, 0, 0, 0, 0, datetime.IST))
				actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actor.GetEntityDetailsByActorIdRequest{
					ActorId: actorId1,
				}).Return(&actor.GetEntityDetailsByActorIdResponse{
					Status:  rpc.StatusOk(),
					EmailId: "<EMAIL>",
					Type:    types.ActorType_USER,
				}, nil)
				userGrpClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), &group.GetGroupsMappedToEmailRequest{
					Email: "<EMAIL>",
				}).Return(&group.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			args: args{
				ctx:     context.Background(),
				actorId: actorId1,
			},
			wantValues: map[string]string{
				MonthlyLottieStories_MonthlyAbout_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-About.json",
				MonthlyLottieStories_MonthlyOutro_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-Outro.json",
				MonthlyLottieStories_MonthlyIntro_Lottie: "https://dza2kd7rioahk.cloudfront.net/assets/fi-minutes/Sep-Intro.json",
			},
			wantActions: map[string]*story.StoryAppAction{},
			wantErr:     false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			actorClient := mockActor.NewMockActorClient(ctrl)
			userGrpClient := mocks.NewMockGroupClient(ctrl)
			storyParams := getTestStoryParamsInDynConfig(tt.fields.isLottieExperimentEnabled)
			timeClient := mockTime.NewMockTime(ctrl)

			tt.setupMocks(actorClient, userGrpClient, timeClient)

			s := NewMonthlyLottieStoriesGenerator(model.StoryValueGenerator_STORY_VALUE_GENERATOR_UNSPECIFIED,
				actorClient, userGrpClient, storyParams, timeClient)
			gotValues, gotActions, err := s.GenerateValues(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateValues() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotValues, tt.wantValues) {
				t.Errorf("GenerateValues() gotValues = %v, want %v", gotValues, tt.wantValues)
			}
			if !reflect.DeepEqual(gotActions, tt.wantActions) {
				t.Errorf("GenerateValues() gotActions = %v, want %v", gotActions, tt.wantActions)
			}
		})
	}
}

func getTestStoryParamsInDynConfig(isEnabled bool) *genconf.StoryParams {
	gconf, _ := genconf.NewConfig()
	_ = gconf.Set(&config.Config{
		StoryParams: &config.StoryParams{
			IsLottieExperimentEnabledForInternal: isEnabled,
			LottieExperimentUrl:                  "test-url",
		},
	}, false, nil)
	return gconf.StoryParams()
}
