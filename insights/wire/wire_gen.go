// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	kms2 "github.com/aws/aws-sdk-go-v2/service/kms"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/epifi/be-common/pkg/aws/v2/kms"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	aws2 "github.com/epifi/be-common/pkg/secrets/aws"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/storage/v2/usecase"
	"github.com/epifi/gamma/analyser/creditscore/params_fetcher"
	types3 "github.com/epifi/gamma/analyser/wire/types"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/analyser/investment"
	"github.com/epifi/gamma/api/analyser/txnaggregates"
	"github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/pinot"
	insights2 "github.com/epifi/gamma/api/insights"
	"github.com/epifi/gamma/api/insights/accessinfo"
	emailparser2 "github.com/epifi/gamma/api/insights/emailparser"
	"github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/investment/aggregator"
	catalog3 "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/merchant"
	catalog2 "github.com/epifi/gamma/api/nps/catalog"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/rewards"
	pinot2 "github.com/epifi/gamma/api/rewards/pinot"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/securities/catalog"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	employment2 "github.com/epifi/gamma/api/vendorgateway/employment"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/insights"
	accessinfo2 "github.com/epifi/gamma/insights/accessinfo"
	consumer3 "github.com/epifi/gamma/insights/accessinfo/consumer"
	dao4 "github.com/epifi/gamma/insights/accessinfo/dao"
	"github.com/epifi/gamma/insights/config"
	"github.com/epifi/gamma/insights/config/genconf"
	"github.com/epifi/gamma/insights/consumer"
	"github.com/epifi/gamma/insights/dao"
	"github.com/epifi/gamma/insights/developer"
	"github.com/epifi/gamma/insights/developer/processor"
	"github.com/epifi/gamma/insights/emailparser"
	consumer2 "github.com/epifi/gamma/insights/emailparser/consumer"
	dao2 "github.com/epifi/gamma/insights/emailparser/dao"
	epf3 "github.com/epifi/gamma/insights/epf"
	consumer4 "github.com/epifi/gamma/insights/epf/consumer"
	dao3 "github.com/epifi/gamma/insights/epf/dao"
	"github.com/epifi/gamma/insights/helper"
	"github.com/epifi/gamma/insights/helper/evaluator"
	"github.com/epifi/gamma/insights/helper/realtime"
	"github.com/epifi/gamma/insights/kubair"
	"github.com/epifi/gamma/insights/kubair/component_builder"
	networth3 "github.com/epifi/gamma/insights/kubair/processor/networth"
	networth2 "github.com/epifi/gamma/insights/networth"
	"github.com/epifi/gamma/insights/networth/assetdaychange"
	"github.com/epifi/gamma/insights/networth/assets"
	"github.com/epifi/gamma/insights/networth/assets/helper/aa"
	"github.com/epifi/gamma/insights/networth/assets/helper/assethistory"
	epf2 "github.com/epifi/gamma/insights/networth/assets/helper/epf"
	investment2 "github.com/epifi/gamma/insights/networth/assets/helper/investment"
	"github.com/epifi/gamma/insights/networth/assets/helper/mutualfund"
	"github.com/epifi/gamma/insights/networth/dao/impl"
	"github.com/epifi/gamma/insights/networth/history"
	"github.com/epifi/gamma/insights/networth/history/entities/historyproviders"
	"github.com/epifi/gamma/insights/networth/history/entities/valueproviders"
	"github.com/epifi/gamma/insights/networth/history/entities/valueproviders/federal"
	"github.com/epifi/gamma/insights/networth/history/entities/valueproviders/tech"
	"github.com/epifi/gamma/insights/networth/history/entities/valueproviders/wealth"
	"github.com/epifi/gamma/insights/networth/investment_declaration"
	"github.com/epifi/gamma/insights/networth/investment_declaration/calculator"
	"github.com/epifi/gamma/insights/networth/liabilities"
	"github.com/epifi/gamma/insights/networth/liabilities/helper/creditreport"
	"github.com/epifi/gamma/insights/networth/llm"
	config2 "github.com/epifi/gamma/insights/networth/llm/config"
	realtime2 "github.com/epifi/gamma/insights/realtime"
	"github.com/epifi/gamma/insights/realtime/datagenerator"
	"github.com/epifi/gamma/insights/realtime/framework"
	"github.com/epifi/gamma/insights/realtime/framework/validator"
	"github.com/epifi/gamma/insights/realtime/generator"
	"github.com/epifi/gamma/insights/story"
	dao5 "github.com/epifi/gamma/insights/story/dao"
	"github.com/epifi/gamma/insights/story/storiesgenerator"
	"github.com/epifi/gamma/insights/story/story_value_generator/factory"
	"github.com/epifi/gamma/insights/user_declaration"
	impl2 "github.com/epifi/gamma/insights/user_declaration/dao/impl"
	"github.com/epifi/gamma/insights/utils"
	types2 "github.com/epifi/gamma/insights/wire/types"
	"github.com/epifi/gamma/pkg/dmf/datafetcher"
	txnaggregates2 "github.com/epifi/gamma/pkg/dmf/txnaggregates"
	"github.com/epifi/gamma/pkg/zinc/search"
	"gorm.io/gorm"
	"net/http"
	"time"
)

// Injectors from wire.go:

func InitializeService(gconf *genconf.Config, actorInsightDB types.ActorInsightsPGDB, actorClient actor.ActorClient, userGrpClient group.GroupClient, userClient user.UsersClient, txnAggClient txnaggregates.TxnAggregatesClient, client connected_account.ConnectedAccountClient, savingsClient savings.SavingsClient, merchantClient merchant.MerchantServiceClient, piClient paymentinstrument.PiClient, categorizerClient categorizer.TxnCategorizerClient, fireflyClient firefly.FireflyClient) *insights.Service {
	db := actorInsightsGormDbProvider(actorInsightDB)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	insightFrameworkDaoPgdb := dao.NewInsightFrameworkDaoPgdb(actorInsightDB)
	insightSegmentDaoPgdb := dao.NewInsightSegmentDaoPgdb(actorInsightDB)
	actorInsightDaoPgdb := dao.NewActorInsightDaoPgdb(actorInsightDB)
	contentTemplateDaoPgdb := dao.NewContentTemplateDaoPgdb(actorInsightDB)
	insightEngagementDaoPgdb := dao.NewInsightEngagementDaoPgdb(actorInsightDB)
	generationScriptRunDaoPgdb := dao.NewGenerationScriptRunDaoPgdb(actorInsightDB)
	insightFeedbackDaoPgdb := dao.NewInsightFeedbackDaoPgdb(actorInsightDB)
	goValuateEvaluator := evaluator.NewGoValuateEvaluator()
	frameworkValidator := validator.NewFrameworkValidator(goValuateEvaluator)
	currentTimeGeneratorImpl := utils.NewCurrentTimeGeneratorImpl()
	globalVarValuesImpl := helper.NewGlobalVarValuesImpl(currentTimeGeneratorImpl)
	relevanceScoreImpl := helper.NewRelevanceScoreImpl()
	actorInsightGenerationLogDaoPgdb := dao.NewActorInsightGenerationLogDaoPgdb(actorInsightDB)
	valueGeneratorFactoryImpl := framework.NewValueGeneratorFactoryImpl(frameworkValidator, currentTimeGeneratorImpl, globalVarValuesImpl, relevanceScoreImpl, actorInsightGenerationLogDaoPgdb)
	categoryHelperImpl := helper.NewCategoryHelperImpl(categorizerClient)
	actorAccountsImpl := datafetcher.NewActorAccountsImpl(client, savingsClient, fireflyClient)
	merchantsImpl := datafetcher.NewMerchantsImpl(merchantClient)
	txnAggregatesImpl := txnaggregates2.NewTxnAggregatesImpl(txnAggClient, actorAccountsImpl, merchantsImpl, piClient)
	categoryAggregateDataGenImpl := datagenerator.NewCategoryAggregateDataGen(categoryHelperImpl, txnAggregatesImpl, actorAccountsImpl)
	mostRelevantFrameworkValueSelector := realtime.NewMostRelevantFrameworkValueSelector(valueGeneratorFactoryImpl)
	insightsGeneratorFactoryImpl := generator.NewInsightsGeneratorFactoryImpl(valueGeneratorFactoryImpl, categoryHelperImpl, currentTimeGeneratorImpl, txnAggregatesImpl, categoryAggregateDataGenImpl, mostRelevantFrameworkValueSelector, actorAccountsImpl)
	generatorToFrameworkNamesMappingImpl := realtime2.NewGeneratorToFrameworkNamesMappingImpl()
	generateRandomNumberImpl := utils.NewGenerateRandomNumberImpl()
	generatorWithMaxAvailableSegments := realtime2.NewGeneratorWithMaxAvailableSegments(generatorToFrameworkNamesMappingImpl, insightFrameworkDaoPgdb, insightSegmentDaoPgdb, insightEngagementDaoPgdb, actorInsightGenerationLogDaoPgdb, generateRandomNumberImpl, currentTimeGeneratorImpl, gconf)
	insightsProcessorImpl := realtime2.NewInsightProcessorImpl(insightsGeneratorFactoryImpl, insightFrameworkDaoPgdb, insightSegmentDaoPgdb, insightEngagementDaoPgdb, generatorToFrameworkNamesMappingImpl, generatorWithMaxAvailableSegments)
	service := insights.NewService(gconf, gormTxnExecutor, insightFrameworkDaoPgdb, insightSegmentDaoPgdb, actorInsightDaoPgdb, contentTemplateDaoPgdb, insightEngagementDaoPgdb, generationScriptRunDaoPgdb, insightFeedbackDaoPgdb, actorClient, userGrpClient, userClient, insightsProcessorImpl, generateRandomNumberImpl)
	return service
}

func InitializeInsightsDevEntityService(db types.InsightsPGDB, actorInsightDB types.ActorInsightsPGDB) *developer.InsightsDevService {
	pgMerchantQueryDao := dao2.NewPGMerchantQueryDao(db)
	merchantQuery := processor.NewMerchantQuery(pgMerchantQueryDao)
	pgMessageDao := dao2.NewPGMessageDao(db)
	messageProcessingState := processor.NewMessageProcessingState(pgMessageDao)
	pgMailSyncLogDao := dao2.NewPGMailSyncLogDao(db)
	mailSyncLog := processor.NewMailSyncLog(pgMailSyncLogDao)
	pgMerchantDao := dao2.NewPGMerchantDao(db)
	processorMerchant := processor.NewMerchant(pgMerchantDao)
	insightFrameworkDaoPgdb := dao.NewInsightFrameworkDaoPgdb(actorInsightDB)
	insightFramework := processor.NewInsightFramework(insightFrameworkDaoPgdb)
	insightSegmentDaoPgdb := dao.NewInsightSegmentDaoPgdb(actorInsightDB)
	insightSegment := processor.NewInsightSegment(insightFrameworkDaoPgdb, insightSegmentDaoPgdb)
	insightContextMappingDaoPgdb := dao.NewInsightContextMappingDaoPgdb(actorInsightDB)
	insightContextMapping := processor.NewInsightContextMapping(insightFrameworkDaoPgdb, insightContextMappingDaoPgdb)
	generationScriptRunDaoPgdb := dao.NewGenerationScriptRunDaoPgdb(actorInsightDB)
	generationScriptRun := processor.NewGenerationScriptRun(insightFrameworkDaoPgdb, generationScriptRunDaoPgdb)
	contentTemplateDaoPgdb := dao.NewContentTemplateDaoPgdb(actorInsightDB)
	contentTemplate := processor.NewContentTemplate(insightFrameworkDaoPgdb, contentTemplateDaoPgdb)
	insightEngagementDaoPgdb := dao.NewInsightEngagementDaoPgdb(actorInsightDB)
	insightEngagement := processor.NewInsightEngagement(insightEngagementDaoPgdb)
	epfPassbookRequestDaoPGDB := dao3.NewEPFPassbookRequestDaoPGDB(db)
	epfPassbookRequest := processor.NewEpfPassbookRequest(epfPassbookRequestDaoPGDB)
	devFactory := developer.NewDevFactory(merchantQuery, messageProcessingState, mailSyncLog, processorMerchant, insightFramework, insightSegment, insightContextMapping, generationScriptRun, contentTemplate, insightEngagement, epfPassbookRequest)
	insightsDevService := developer.NewInsightsDevService(devFactory)
	return insightsDevService
}

func InitializeActorInsightConsumerService(actorInsightDB types.ActorInsightsPGDB, insightsClient insights2.InsightsClient, commsClient types2.InsightsCommsClientWithInterceptors, caClient connected_account.ConnectedAccountClient, dbConnProvider *usecase.DBResourceProvider[*gorm.DB], catalogClient catalog.SecuritiesCatalogClient) *consumer.Service {
	db := actorInsightsGormDbProvider(actorInsightDB)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	insightFrameworkDaoPgdb := dao.NewInsightFrameworkDaoPgdb(actorInsightDB)
	insightSegmentDaoPgdb := dao.NewInsightSegmentDaoPgdb(actorInsightDB)
	generationScriptRunDaoPgdb := dao.NewGenerationScriptRunDaoPgdb(actorInsightDB)
	actorInsightDaoPgdb := dao.NewActorInsightDaoPgdb(actorInsightDB)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	insightEngagementDaoPgdb := dao.NewInsightEngagementDaoPgdb(actorInsightDB)
	currentTimeGeneratorImpl := utils.NewCurrentTimeGeneratorImpl()
	aaBalanceFetcher := aa.NewAaBalanceFetcher(caClient)
	assetHistoryPGDB := impl.NewAssetHistoryPGDB(dbConnProvider)
	service := consumer.NewService(gormTxnExecutor, insightFrameworkDaoPgdb, insightSegmentDaoPgdb, generationScriptRunDaoPgdb, actorInsightDaoPgdb, insightsClient, commsCommsClient, insightEngagementDaoPgdb, currentTimeGeneratorImpl, aaBalanceFetcher, assetHistoryPGDB, catalogClient)
	return service
}

func InitializeEmailparserService(conf *config.Config, db types.InsightsPGDB, authClient auth.AuthClient, accessInfoClient accessinfo.AccessInfoClient, spendsPublisher types2.GmailUserSpendsPublisher) *emailparser.Service {
	pgUserSpendingDao := dao2.NewPGUserSpendingDao(db)
	pgMailSyncLogDao := dao2.NewPGMailSyncLogDao(db)
	pgMessageDao := dao2.NewPGMessageDao(db)
	pgGmailQueryExecResultsDao := dao2.NewPGGmailQueryExecResultsDao(db)
	gmailListApiClient := getGmailListApiClient(conf)
	oAuthCredential := oauthCredProvider(conf)
	oauth2Config := insights.GetOAuthCredentialConfig(oAuthCredential)
	gmailClient := getHttpClientGmail(conf)
	gmailBatchGetApiParams := gmailBatchGetParamsProvider(conf)
	gmailService := consumer2.NewGmailService(gmailListApiClient, oauth2Config, gmailClient, gmailBatchGetApiParams)
	pgMerchantDao := dao2.NewPGMerchantDao(db)
	pgMerchantQueryDao := dao2.NewPGMerchantQueryDao(db)
	service := emailparser.NewService(pgUserSpendingDao, pgMailSyncLogDao, pgMessageDao, authClient, accessInfoClient, pgGmailQueryExecResultsDao, gmailService, spendsPublisher, pgMerchantDao, pgMerchantQueryDao)
	return service
}

func InitializeEmailparserConsumerService(conf *config.Config, db types.InsightsPGDB, accessInfoClient accessinfo.AccessInfoClient, publisher types2.MailDataParserPublisher, spendsPublisher types2.GmailUserSpendsPublisher, actorClient actor.ActorClient, userClient user.UsersClient, awsConf aws.Config) *consumer2.Service {
	pgMessageDao := dao2.NewPGMessageDao(db)
	pgUserSpendingDao := dao2.NewPGUserSpendingDao(db)
	pgMailSyncLogDao := dao2.NewPGMailSyncLogDao(db)
	gmailListApiClient := getGmailListApiClient(conf)
	oAuthCredential := oauthCredProvider(conf)
	oauth2Config := insights.GetOAuthCredentialConfig(oAuthCredential)
	gmailClient := getHttpClientGmail(conf)
	gmailBatchGetApiParams := gmailBatchGetParamsProvider(conf)
	gmailService := consumer2.NewGmailService(gmailListApiClient, oauth2Config, gmailClient, gmailBatchGetApiParams)
	emailParserClient := getHttpClientForEmailParser(conf)
	emailParserParams := emailparserParamsProvider(conf)
	mailDataParserImpl := consumer2.NewMailDataParserImpl(emailParserClient, emailParserParams)
	client := NewKmsProvider(awsConf)
	mailDataCryptor := getMailDataCryptor(conf, client)
	mailFetchConcurrencyParams := mailFetchConcurrencyParamsProvider(conf)
	gormDB := insightsGormDbProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	mailDataFetcherImpl := consumer2.NewMailDataFetcherImpl(pgMessageDao, gmailService, publisher, mailDataCryptor, mailFetchConcurrencyParams, gormTxnExecutor)
	pgMerchantQueryDao := dao2.NewPGMerchantQueryDao(db)
	emailIdRegexString := emailIdRegexProvider(conf)
	mailFetchDateRange := mailFetchDateRangeProvider(conf)
	pgMerchantDao := dao2.NewPGMerchantDao(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	service := consumer2.NewService(pgMessageDao, pgUserSpendingDao, pgMailSyncLogDao, accessInfoClient, gmailService, mailDataParserImpl, mailDataFetcherImpl, mailDataCryptor, pgMerchantQueryDao, emailIdRegexString, mailFetchDateRange, spendsPublisher, actorClient, userClient, pgMerchantDao, crdbIdempotentTxnExecutor)
	return service
}

func InitializeMockEmailparserConsumerService(conf *config.Config, db types.InsightsPGDB, accessInfoClient accessinfo.AccessInfoClient, publisher types2.MailDataParserPublisher, spendsPublisher types2.GmailUserSpendsPublisher, actorClient actor.ActorClient, userClient user.UsersClient, awsConf aws.Config) *consumer2.Service {
	pgMessageDao := dao2.NewPGMessageDao(db)
	pgUserSpendingDao := dao2.NewPGUserSpendingDao(db)
	pgMailSyncLogDao := dao2.NewPGMailSyncLogDao(db)
	mockMailService := consumer2.NewMockMailService()
	mockEmailDataParser := consumer2.NewMockEmailDataParser()
	client := NewKmsProvider(awsConf)
	mailDataCryptor := getMailDataCryptor(conf, client)
	mockMailDataFetcher := consumer2.NewMockMailDataFetcher(pgMessageDao, mailDataCryptor, publisher)
	pgMerchantQueryDao := dao2.NewPGMerchantQueryDao(db)
	emailIdRegexString := emailIdRegexProvider(conf)
	mailFetchDateRange := mailFetchDateRangeProvider(conf)
	pgMerchantDao := dao2.NewPGMerchantDao(db)
	gormDB := insightsGormDbProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	service := consumer2.NewService(pgMessageDao, pgUserSpendingDao, pgMailSyncLogDao, accessInfoClient, mockMailService, mockEmailDataParser, mockMailDataFetcher, mailDataCryptor, pgMerchantQueryDao, emailIdRegexString, mailFetchDateRange, spendsPublisher, actorClient, userClient, pgMerchantDao, crdbIdempotentTxnExecutor)
	return service
}

func InitializeAccessInfoService(conf *config.Config, db types.InsightsPGDB, userMailAccessPublisher types2.UserEmailAccessPublisher, awsConf aws.Config, authCleint auth.AuthClient, periodicPublisher types2.PeriodicEmaiSyncPublisher, onbClient onboarding.OnboardingClient, epClient emailparser2.EmailParserClient) *accessinfo2.Service {
	pgAccessInfoDao := dao4.NewPGAccessInfoDao(db)
	singleMailSyncMessagePublisher := accessinfo2.NewSingleMessagePublisher(userMailAccessPublisher)
	oAuthCredential := oauthCredProvider(conf)
	oauth2Config := insights.GetOAuthCredentialConfig(oAuthCredential)
	oAuthClient := getOAuthClient(conf)
	oAuthRevokeTokenUrl := oauthRevokeTokenUrlProvider(conf)
	oAuthServiceImpl := accessinfo2.NewOAuthServiceImpl(oauth2Config, oAuthClient, oAuthRevokeTokenUrl)
	client := aws2.NewAwsSecretsManagerClient(awsConf)
	accessInfoCryptor := getAccessInfoCryptor(conf, client)
	kmsClient := NewKmsProvider(awsConf)
	kmsSymmetricCryptor := kms.NewKMSSymmetricCryptor(kmsClient)
	accessInfoKmsKeyId := accessInfoKmsKeyIdProvider(conf)
	pgWlOnbActorMappingDao := dao4.NewPGWlOnbActorMappingDao(db)
	addGmailAccountBannerParams := addEmailAccBannerConfigProvider(conf)
	service := accessinfo2.NewService(pgAccessInfoDao, singleMailSyncMessagePublisher, oAuthServiceImpl, accessInfoCryptor, kmsSymmetricCryptor, accessInfoKmsKeyId, authCleint, periodicPublisher, epClient, pgWlOnbActorMappingDao, addGmailAccountBannerParams, onbClient)
	return service
}

func InitializeMockAccessInfoService(conf *config.Config, db types.InsightsPGDB, userMailAccessPublisher types2.UserEmailAccessPublisher, awsConf aws.Config, authCleint auth.AuthClient, periodicPublisher types2.PeriodicEmaiSyncPublisher, onbClient onboarding.OnboardingClient, epClient emailparser2.EmailParserClient) *accessinfo2.Service {
	pgAccessInfoDao := dao4.NewPGAccessInfoDao(db)
	singleMailSyncMessagePublisher := accessinfo2.NewSingleMessagePublisher(userMailAccessPublisher)
	mockOAuthService := accessinfo2.NewMockOAuthService()
	client := aws2.NewAwsSecretsManagerClient(awsConf)
	accessInfoCryptor := getAccessInfoCryptor(conf, client)
	kmsClient := NewKmsProvider(awsConf)
	kmsSymmetricCryptor := kms.NewKMSSymmetricCryptor(kmsClient)
	accessInfoKmsKeyId := accessInfoKmsKeyIdProvider(conf)
	pgWlOnbActorMappingDao := dao4.NewPGWlOnbActorMappingDao(db)
	addGmailAccountBannerParams := addEmailAccBannerConfigProvider(conf)
	service := accessinfo2.NewService(pgAccessInfoDao, singleMailSyncMessagePublisher, mockOAuthService, accessInfoCryptor, kmsSymmetricCryptor, accessInfoKmsKeyId, authCleint, periodicPublisher, epClient, pgWlOnbActorMappingDao, addGmailAccountBannerParams, onbClient)
	return service
}

func InitializeAccessInfoConsumerService(db types.InsightsPGDB) *consumer3.Service {
	pgAccessInfoDao := dao4.NewPGAccessInfoDao(db)
	service := consumer3.NewService(pgAccessInfoDao)
	return service
}

func InitializeStoryService(conf *genconf.Config, actorInsightDB types.ActorInsightsPGDB, client rewards.RewardsGeneratorClient, usersClient user.UsersClient, txnAggClient txnaggregates.TxnAggregatesClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, connectedAccClient connected_account.ConnectedAccountClient, merchantClient merchant.MerchantServiceClient, piClient paymentinstrument.PiClient, creditReportManagerClient creditreportv2.CreditReportManagerClient, categorizerClient categorizer.TxnCategorizerClient, groupClient group.GroupClient, fireflyClient firefly.FireflyClient, accountBalanceClient balance.BalanceClient, ffTxnAggregatesClient pinot.TxnAggregatesClient, ffAccountingClient accounting.AccountingClient, rewardsAggregatesClient pinot2.RewardsAggregatesClient) *story.Service {
	storyDaoImpl := dao5.NewStoryDaoImpl()
	storyTemplateDaoImpl := dao5.NewStoryTemplateDaoImpl()
	storyGroupDaoImpl := dao5.NewStoryGroupDaoImpl()
	storyGroupMappingDaoImpl := dao5.NewStoryGroupMappingDaoImpl()
	storyGroupEngagementDaoPgdb := dao5.NewStoryGroupEngagementDaoPgdb(actorInsightDB)
	actorAccountsImpl := datafetcher.NewActorAccountsImpl(connectedAccClient, savingsClient, fireflyClient)
	merchantsImpl := datafetcher.NewMerchantsImpl(merchantClient)
	txnAggregatesImpl := txnaggregates2.NewTxnAggregatesImpl(txnAggClient, actorAccountsImpl, merchantsImpl, piClient)
	categoryHelperImpl := helper.NewCategoryHelperImpl(categorizerClient)
	generateRandomNumberImpl := utils.NewGenerateRandomNumberImpl()
	creditScoreParamsFetcherImpl := params_fetcher.NewCreditScoreParamsFetcherImpl(usersClient)
	defaultTime := datetime.NewDefaultTime()
	storyValueGeneratorFactoryImpl := factory.NewStoryValueGeneratorFactoryImpl(conf, txnAggregatesImpl, categoryHelperImpl, client, usersClient, txnAggClient, actorAccountsImpl, savingsClient, actorClient, merchantClient, generateRandomNumberImpl, creditReportManagerClient, creditScoreParamsFetcherImpl, groupClient, accountBalanceClient, defaultTime, fireflyClient, ffTxnAggregatesClient, ffAccountingClient, rewardsAggregatesClient)
	goValuateEvaluator := evaluator.NewGoValuateEvaluator()
	storiesGeneratorImpl := storiesgenerator.NewStoriesGeneratorImpl(conf, storyValueGeneratorFactoryImpl, goValuateEvaluator)
	service := story.NewService(conf, storyDaoImpl, storyTemplateDaoImpl, storyGroupDaoImpl, storyGroupMappingDaoImpl, storyGroupEngagementDaoPgdb, storiesGeneratorImpl)
	return service
}

func InitialiseNetWorthService(cfg *genconf.Config, insightsDb types.InsightsPGDB, savingsClient savings.SavingsClient, connectedAccountClient connected_account.ConnectedAccountClient, creditReportManagerClient creditreportv2.CreditReportManagerClient, userClient user.UsersClient, investAnalyticsClient investment.InvestmentAnalyticsClient, investmentAggregatorClient aggregator.InvestmentAggregatorClient, epfClient epf.EpfClient, paySavingsBalanceClient balance.BalanceClient, client onboarding.OnboardingClient, mfExternalClient external.MFExternalOrdersClient, netWorthClient networth.NetWorthClient, iftClient internationalfundtransfer.InternationalFundTransferClient, zincSearchClient search.SearchClient, preapprovedloanClient preapprovedloan.PreApprovedLoanClient, empClient employment.EmploymentClient, dbConnProvider *usecase.DBResourceProvider[*gorm.DB], catalogClient catalog.SecuritiesCatalogClient, npsCatalogClient catalog2.NpsCatalogClient, eventBroker events.Broker, mfCatalogClient catalog3.CatalogManagerClient, variableGeneratorClient variables.VariableGeneratorClient) *networth2.Service {
	defaultTime := datetime.NewDefaultTime()
	aaBalanceFetcher := aa.NewAaBalanceFetcher(connectedAccountClient)
	investmentAggregatorHelper := investment2.NewInvestmentAggregatorHelper(investmentAggregatorClient)
	epfAggregatorHelper := epf2.NewEpfAggregatorHelperClient(epfClient)
	mfAggregatorHelper := mutualfund.NewMfAggregatorHelperClient(defaultTime, investAnalyticsClient, cfg)
	db := insightsGormDbProvider(insightsDb)
	investmentDeclarationPGDB := impl.NewInvestmentDeclarationPGDB(db)
	networthParams := networthParamsProvider(cfg)
	investmentCalculatorFactoryImpl := calculator.NewInvestmentCalculatorFactoryImpl(defaultTime, networthParams, iftClient)
	investmentDeclarationProcessor := investment_declaration.NewInvestmentDeclarationProcessor(investmentDeclarationPGDB, investmentCalculatorFactoryImpl, iftClient)
	assertValueAggregatorFactory := assets.NewAssertValueAggregatorFactory(defaultTime, savingsClient, aaBalanceFetcher, investAnalyticsClient, investmentAggregatorHelper, epfClient, epfAggregatorHelper, mfAggregatorHelper, investmentDeclarationProcessor, paySavingsBalanceClient)
	creditScoreParamsFetcherImpl := params_fetcher.NewCreditScoreParamsFetcherImpl(userClient)
	creditReportHelper := creditreport.NewCreditReportHelper(creditReportManagerClient, creditScoreParamsFetcherImpl)
	liabilityValueAggregatorFactory := liabilities.NewLiabilityValueAggregatorFactory(creditReportHelper)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	netWorthRefreshSessionPGDB := impl.NewNetWorthRefreshSessionPGDB(db, domainIdGenerator)
	pmsProviderZinc := impl.NewPMSProviderZinc(zincSearchClient, cfg)
	aifZinc := impl.NewAIFZinc(zincSearchClient, cfg)
	aaAccountsBalanceProvider := wealth.NewAaAccountsBalanceProvider(aaBalanceFetcher)
	investmentSummaryProvider := wealth.NewInvestmentSummaryProvider(investmentAggregatorHelper)
	investmentDetailsProvider := tech.NewInvestmentDetailsProvider(investmentDeclarationProcessor, defaultTime)
	saAccountBalanceProvider := federal.NewSaAccountBalanceProvider(savingsClient, paySavingsBalanceClient)
	epfProvider := tech.NewEpfProvider(epfAggregatorHelper)
	mutualFundProvider := wealth.NewMutualFundProvider(mfAggregatorHelper)
	valueProviderService := valueproviders.NewValueProviderService(aaAccountsBalanceProvider, investmentSummaryProvider, investmentDetailsProvider, saAccountBalanceProvider, epfProvider, mutualFundProvider)
	assetHistoryPGDB := impl.NewAssetHistoryPGDB(dbConnProvider)
	assetHistoryFetcher := assethistory.NewAssetHistoryFetcher(assetHistoryPGDB, valueProviderService, cfg)
	historyProviderSvc := historyproviders.NewHistoryProviderSvc(assetHistoryFetcher)
	netWorthService := history.NewNetWorthService(cfg, valueProviderService, historyProviderSvc)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	mutualFundCalculator := assetdaychange.NewMutualFundCalculator(mfCatalogClient, mfExternalClient, variableGeneratorClient)
	indianStocksCalculator := assetdaychange.NewIndianStocksCalculator(cfg, catalogClient)
	npsCalculator := assetdaychange.NewNPSCalculator(npsCatalogClient)
	factoryService := assetdaychange.NewDayChangeCalculatorFactory(mutualFundCalculator, indianStocksCalculator, npsCalculator)
	geminiConf := GeminiConfProvider(cfg)
	httpContentRedactor := httpcontentredactor.GetInstance()
	geminiClient := llm.NewGeminiClient(geminiConf, httpContentRedactor)
	service := networth2.NewService(assertValueAggregatorFactory, liabilityValueAggregatorFactory, investmentDeclarationPGDB, netWorthRefreshSessionPGDB, investmentDeclarationProcessor, client, connectedAccountClient, mfExternalClient, netWorthClient, creditReportManagerClient, epfClient, cfg, defaultTime, preapprovedloanClient, pmsProviderZinc, aifZinc, empClient, netWorthService, gormTxnExecutor, factoryService, assetHistoryPGDB, geminiClient, eventBroker)
	return service
}

func InitializeEpfService(conf *genconf.Config, insightsDb types.InsightsPGDB, employmentClient employment2.EmploymentClient, userClient user.UsersClient, nameCheckClient namecheck.UNNameCheckClient, epfPassbookImportEventPublisher types2.EpfPassbookImportEventPublisher, eventBroker events.Broker, analyserRedisStore types3.AnalyserRedisStore, employerClient employment.EmploymentClient) *epf3.Service {
	client := types3.AnalyserRedisStoreRedisClientProvider(analyserRedisStore)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	uanAccountsCacheConfig := UANAccountsCacheConfigProvider(conf)
	uanAccountDaoPGDB := dao3.NewUANAccountDaoPGDB(insightsDb)
	uanAccountDBDao := dao3.ProvideUANAccountDBDao(uanAccountDaoPGDB, uanAccountsCacheConfig)
	uanAccountDaoCache := dao3.NewUANAccountDaoCache(redisCacheStorage, uanAccountsCacheConfig, uanAccountDBDao)
	uanAccountDao := dao3.ProvideUANAccountDaoCache(uanAccountDaoCache, uanAccountsCacheConfig)
	epfPassbookRequestDaoPGDB := dao3.NewEPFPassbookRequestDaoPGDB(insightsDb)
	db := insightsGormDbProvider(insightsDb)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	epfImportSessionDaoPGDB := dao3.NewEPFImportSessionDaoPGDB(insightsDb)
	employerPfHistoryDetailsDaoPGDB := dao3.NewEmployerPfHistoryDetailsDaoPGDB(insightsDb)
	epfHelper := epf3.NewEpfHelper(epfImportSessionDaoPGDB, gormTxnExecutor)
	defaultTime := datetime.NewDefaultTime()
	epfPassbookEmployeeDetailsDaoPGDB := dao3.NewEpfPassbookEmployeeDetailsDaoPGDB(insightsDb)
	epfPassbookEstDetailsDaoPGDB := dao3.NewEpfPassbookEstDetailsDaoPGDB(insightsDb)
	epfPassbookTransactionsDaoPGDB := dao3.NewEpfPassbookTransactionsDaoPGDB(insightsDb)
	epfPassbookOverallPfBalanceDaoPGDB := dao3.NewEpfPassbookOverallPfBalanceDaoPGDB(insightsDb)
	epfSmsDataDaoPGDB := dao3.NewEpfSmsDataDaoPGDB(insightsDb)
	service := epf3.NewService(conf, employmentClient, userClient, uanAccountDao, epfPassbookRequestDaoPGDB, gormTxnExecutor, nameCheckClient, epfImportSessionDaoPGDB, employerPfHistoryDetailsDaoPGDB, employerClient, epfHelper, epfPassbookImportEventPublisher, eventBroker, defaultTime, epfPassbookEmployeeDetailsDaoPGDB, epfPassbookEstDetailsDaoPGDB, epfPassbookTransactionsDaoPGDB, epfPassbookOverallPfBalanceDaoPGDB, epfSmsDataDaoPGDB)
	return service
}

func InitializeKubairService(dynConf *genconf.Config, networthClient networth.NetWorthClient, connectedAccountClient connected_account.ConnectedAccountClient) *kubair.Service {
	netWorthProcessor := networth3.NewNetWorthProcessor(networthClient)
	assetBottomComponentFactory := component_builder.NewAssetBottomComponentFactory(networthClient)
	aaBalanceFetcher := aa.NewAaBalanceFetcher(connectedAccountClient)
	npsProcessor := networth3.NewNpsProcessor(connectedAccountClient, aaBalanceFetcher)
	componentBuilderFactory := component_builder.NewComponentBuilderFactory(netWorthProcessor, assetBottomComponentFactory, npsProcessor)
	service := kubair.NewService(dynConf, netWorthProcessor, componentBuilderFactory)
	return service
}

func InitializeEpfPassbookConsumerService(conf *genconf.Config, insightsPgdb types.InsightsPGDB, analyserRedisStore types3.AnalyserRedisStore) *consumer4.EpfPassbookConsumerService {
	client := types3.AnalyserRedisStoreRedisClientProvider(analyserRedisStore)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	uanAccountsCacheConfig := UANAccountsCacheConfigProvider(conf)
	uanAccountDaoPGDB := dao3.NewUANAccountDaoPGDB(insightsPgdb)
	uanAccountDBDao := dao3.ProvideUANAccountDBDao(uanAccountDaoPGDB, uanAccountsCacheConfig)
	uanAccountDaoCache := dao3.NewUANAccountDaoCache(redisCacheStorage, uanAccountsCacheConfig, uanAccountDBDao)
	uanAccountDao := dao3.ProvideUANAccountDaoCache(uanAccountDaoCache, uanAccountsCacheConfig)
	db := insightsGormDbProvider(insightsPgdb)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	epfPassbookEmployeeDetailsDaoPGDB := dao3.NewEpfPassbookEmployeeDetailsDaoPGDB(insightsPgdb)
	epfPassbookOverallPfBalanceDaoPGDB := dao3.NewEpfPassbookOverallPfBalanceDaoPGDB(insightsPgdb)
	epfPassbookEstDetailsDaoPGDB := dao3.NewEpfPassbookEstDetailsDaoPGDB(insightsPgdb)
	epfPassbookTransactionsDaoPGDB := dao3.NewEpfPassbookTransactionsDaoPGDB(insightsPgdb)
	epfPassbookConsumerService := consumer4.NewEpfPassbookConsumerService(uanAccountDao, gormTxnExecutor, epfPassbookEmployeeDetailsDaoPGDB, epfPassbookOverallPfBalanceDaoPGDB, epfPassbookEstDetailsDaoPGDB, epfPassbookTransactionsDaoPGDB)
	return epfPassbookConsumerService
}

func InitializeUserDeclarationService(db types.InsightsPGDB) *user_declaration.Service {
	userDeclarationImpl := impl2.NewUserDeclarationImpl(db)
	service := user_declaration.NewService(userDeclarationImpl)
	return service
}

// wire.go:

func NewKmsProvider(awsConfig aws.Config) *kms2.Client {
	return kms.InitKMSClient(awsConfig)
}

func IDbResourceProviderProvider(dbConnProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) storagev2.IDbResourceProvider[storagev2.IdempotentTxnExecutor] {
	return dbConnProvider
}

func GeminiConfProvider(cfg *genconf.Config) *config2.GeminiConf {
	return cfg.GeminiConf()
}

func UANAccountsCacheConfigProvider(cfg *genconf.Config) *genconf.UANAccountsCacheConfig {
	return cfg.UANAccountsCacheConfig()
}

func networthParamsProvider(cfg *genconf.Config) *genconf.NetworthParams {
	return cfg.NetworthParams()
}

func addEmailAccBannerConfigProvider(conf *config.Config) *config.AddGmailAccountBannerParams {
	return conf.AddGmailAccountBannerParams
}

func oauthRevokeTokenUrlProvider(conf *config.Config) accessinfo2.OAuthRevokeTokenUrl {
	return accessinfo2.OAuthRevokeTokenUrl(conf.GoogleOAuthParams.RevokeTokenUrl)
}

func accessInfoKmsKeyIdProvider(conf *config.Config) accessinfo2.AccessInfoKmsKeyId {
	return accessinfo2.AccessInfoKmsKeyId(conf.Application.GmailDataEncrKeyKMSId)
}

func actorInsightsGormDbProvider(db types.ActorInsightsPGDB) *gorm.DB {
	return db
}

func insightsGormDbProvider(db types.InsightsPGDB) *gorm.DB {
	return db
}

func oauthCredentialProvider(conf *config.Config) insights.OAuthCredential {
	return insights.OAuthCredential(conf.Secrets.Ids[config.GoogleOAuthCredentials])
}

func gmailBatchGetParamsProvider(conf *config.Config) *config.GmailBatchGetApiParams {
	return conf.GmailBatchGetApiParams
}

func gmailListParamsProvider(conf *config.Config) *config.GmailListApiParams {
	return conf.GmailListApiParams
}

func emailparserParamsProvider(conf *config.Config) *config.EmailParserParams {
	return conf.EmailParserParams
}

func mailFetchConcurrencyParamsProvider(conf *config.Config) *config.MailFetchConcurrencyParams {
	return conf.MailFetchConcurrencyParams
}

func getGmailListApiClient(conf *config.Config) consumer2.GmailListApiClient {
	httpClient := http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        conf.GmailListApiParams.ClientMaxIdleConns,
			MaxIdleConnsPerHost: conf.GmailListApiParams.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(conf.GmailListApiParams.ClientIdleConnTimeout) * time.Second,
		},
		Timeout: time.Duration(conf.GmailListApiParams.TimeoutInMillis) * time.Millisecond,
	}
	return consumer2.GmailListApiClient(httpClient)
}

func getAccessInfoCryptor(conf *config.Config, secretManagetClient *secretsmanager.Client) accessinfo2.AccessInfoCryptor {
	env := conf.Application.Environment
	if env == cfg.DevelopmentEnv || env == cfg.TestEnv {
		return accessinfo2.NewMockAccessInfoCryptorImpl()
	} else {
		secretsManager := aws2.NewAwsSecretManager(secretManagetClient)
		return accessinfo2.NewAccessInfoCryptorImpl(conf.Application.GmailDataEncrKeyKMSId, secretsManager, insights.NewAESCryptor())
	}
}

func getMailDataCryptor(conf *config.Config, kmsClient *kms2.Client) consumer2.MailDataCryptor {
	env := conf.Application.Environment
	if env == cfg.DevelopmentEnv || env == cfg.TestEnv {
		return consumer2.NewMockMailDataCryptorImpl()
	} else {
		mailDataEncrKey := conf.Secrets.Ids[config.MailDataEncryptionKey]
		return consumer2.NewMailDataCryptorImpl(consumer2.MailDataEncryptionKey(mailDataEncrKey), consumer2.GmailDataEncrKeyKMSId(conf.Application.GmailDataEncrKeyKMSId), kms.NewKMSSymmetricCryptor(kmsClient), insights.NewAESCryptor())
	}
}

func getHttpClientGmail(conf *config.Config) *consumer2.GmailClient {
	httpClient := http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        conf.GmailBatchGetApiParams.ClientMaxIdleConns,
			MaxIdleConnsPerHost: conf.GmailBatchGetApiParams.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(conf.GmailBatchGetApiParams.ClientIdleConnTimeout) * time.Second,
		},
		Timeout: time.Duration(conf.GmailBatchGetApiParams.TimeoutInMillis) * time.Millisecond,
	}
	return &consumer2.GmailClient{Client: httpClient}
}

func getHttpClientForEmailParser(conf *config.Config) *consumer2.EmailParserClient {
	httpClient := http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        conf.EmailParserParams.ClientMaxIdleConns,
			MaxIdleConnsPerHost: conf.EmailParserParams.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(conf.EmailParserParams.ClientIdleConnTimeout) * time.Second,
		},
		Timeout: time.Duration(conf.EmailParserParams.TimeoutInMillis) * time.Millisecond,
	}
	return &consumer2.EmailParserClient{Client: httpClient}
}

func getOAuthClient(conf *config.Config) accessinfo2.OAuthClient {
	httpClient := http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        conf.GoogleOAuthParams.ClientMaxIdleConns,
			MaxIdleConnsPerHost: conf.GoogleOAuthParams.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(conf.GoogleOAuthParams.ClientIdleConnTimeout) * time.Second,
		},
		Timeout: time.Duration(conf.GoogleOAuthParams.TimeoutInMillis) * time.Millisecond,
	}
	return accessinfo2.OAuthClient(httpClient)
}

func mailFetchDateRangeProvider(conf *config.Config) consumer2.MailFetchDateRange {
	return consumer2.MailFetchDateRange(conf.Application.MailFetchDateRangeInMonths)
}

func emailIdRegexProvider(conf *config.Config) consumer2.EmailIdRegexString {
	return consumer2.EmailIdRegexString(conf.Application.EmailIdRegex)
}

func oauthCredProvider(conf *config.Config) insights.OAuthCredential {
	return insights.OAuthCredential(conf.Secrets.Ids[config.GoogleOAuthCredentials])
}
