package category

import (
	"flag"
	"log"
	"os"
	"testing"

	"github.com/epifi/gamma/insights/config/genconf"
	"github.com/epifi/gamma/insights/test"
)

// nolint:dogsled
// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, _, _, teardown = test.InitTestServer(false)
	var err error
	_, err = genconf.Load()
	if err != nil {
		log.Fatal("failed to load dynamic config", err)
	}

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
