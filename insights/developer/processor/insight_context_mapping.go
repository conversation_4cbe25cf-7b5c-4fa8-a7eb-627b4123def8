package processor

import (
	"context"
	"encoding/json"
	"sort"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/insights/developer"
	"github.com/epifi/gamma/insights/dao"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	screenParamName = "segment_id"
)

type InsightContextMapping struct {
	FrameworkDao      dao.InsightFrameworkDao
	contextMappingDao dao.InsightContextMappingDao
}

func NewInsightContextMapping(frameworkDao dao.InsightFrameworkDao, contextMappingDao dao.InsightContextMappingDao) *InsightContextMapping {
	return &InsightContextMapping{
		FrameworkDao:      frameworkDao,
		contextMappingDao: contextMappingDao,
	}
}

func getScreenNameList() []string {
	var ScreenOptions []string
	for key := range deeplinkPb.Screen_value {
		ScreenOptions = append(ScreenOptions, key)
	}
	sort.Strings(ScreenOptions)
	return ScreenOptions
}

func (s *InsightContextMapping) FetchParamList(ctx context.Context, entity developer.InsightsEntity) ([]*db_state.ParameterMeta, error) {
	screenNameList := getScreenNameList()
	paramList := []*db_state.ParameterMeta{
		{
			Name:            frameworkNameParamName,
			Label:           "Framework Name",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            frameworkIdParamName,
			Label:           "Framework Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            screenParamName,
			Label:           "Screen Name",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         screenNameList,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (s *InsightContextMapping) FetchData(ctx context.Context, entity developer.InsightsEntity, filters []*db_state.Filter) (string, error) {
	for _, filter := range filters {
		switch filter.ParameterName {

		case frameworkNameParamName:
			frameworkName := filter.GetStringValue()
			daoResp, err := s.FrameworkDao.GetByName(ctx, frameworkName)
			if err != nil {
				logger.Error(ctx, "error in fetching framework entry by frameworkName", zap.Error(err), zap.String(logger.FRAMEWORK_NAME, frameworkName))
				return err.Error(), nil
			}
			frameworkId := daoResp.GetId()
			return s.getByFrameworkId(ctx, frameworkId)

		case frameworkIdParamName:
			frameworkId := filter.GetStringValue()
			return s.getByFrameworkId(ctx, frameworkId)

		case screenParamName:
			screenName := filter.GetDropdownValue()
			var screen deeplinkPb.Screen
			if value, found := deeplinkPb.Screen_value[screenName]; found {
				screen = deeplinkPb.Screen(value)
			}
			daoResp, err := s.contextMappingDao.GetByScreen(ctx, screen)
			if err != nil {
				logger.Error(ctx, "error in fetching contextMapping entries by screenName", zap.Error(err), zap.String(logger.SCREEN, screenName))
				return err.Error(), nil
			}
			jsonResp, err := json.Marshal(daoResp)
			if err != nil {
				logger.Error(ctx, "cannot marshal to json", zap.Error(err))
				return err.Error(), nil
			}
			return string(jsonResp), err
		}
	}

	return InvalidArgumentsInInput, nil
}

// nolint
// getByFrameworkId - returns all context mapping converted to json string for a given frameworkId
func (s *InsightContextMapping) getByFrameworkId(ctx context.Context, frameworkId string) (string, error) {
	daoResp, err := s.contextMappingDao.GetByFrameworkId(ctx, frameworkId)
	if err != nil {
		logger.Error(ctx, "error in fetching contextMapping entries by frameworkId", zap.Error(err), zap.String(logger.FRAMEWORK_ID, frameworkId))
		return err.Error(), nil
	}
	jsonResp, err := json.Marshal(daoResp)
	if err != nil {
		logger.Error(ctx, "cannot marshal to json", zap.Error(err))
		return err.Error(), nil
	}
	return string(jsonResp), nil
}
