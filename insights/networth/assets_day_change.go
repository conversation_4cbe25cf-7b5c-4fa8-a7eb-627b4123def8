// nolint
package networth

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/syncmap"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	"github.com/epifi/gamma/insights/networth/assetdaychange"
)

func (s *Service) GetAssetsDayChange(ctx context.Context, request *networthPb.GetAssetsDayChangeRequest) (*networthPb.GetAssetsDayChangeResponse, error) {
	actorId := request.GetActorId()
	// inject ctx with actor Id for logging purpose
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	// Get all asset data requests first
	assetDataRequestMap, err := s.getAssetDataRequestMap(ctx, request)
	if err != nil {
		logger.Error(ctx, "error getting asset data request map", zap.Error(err))
		return &networthPb.GetAssetsDayChangeResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error getting asset data request map"),
		}, err
	}

	changeResponseSyncMap := &syncmap.Map[enumsPb.AssetType, *networthPb.AssetTypeDayChangeResponse]{}
	g := errgroup.New()

	for _, assetType := range request.GetAssetTypes() {
		currentAssetType := assetType // Capture range variable for goroutine

		g.Go(func() error {
			dayChangeCalculator, getCalculatorErr := s.dayChangeCalculatorFactory.GetCalculator(ctx, currentAssetType)
			if getCalculatorErr != nil {
				return fmt.Errorf("error getting day change calculator for asset type %s: %w", currentAssetType.String(), getCalculatorErr)
			}

			calculateRequest, ok := assetDataRequestMap[currentAssetType]
			if !ok {
				// This case should ideally not happen if getAssetDataRequestMap populates correctly for all requested assetTypes
				return fmt.Errorf("no asset data found for requested asset type %s", currentAssetType.String())
			}
			if currentAssetType != enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND && (len(calculateRequest.OwnershipToFinalDateDataMap) == 0 || len(calculateRequest.OwnershipToInitialDateDataMap) == 0) {
				logger.Info(ctx, "incomplete data for asset type", zap.String("assetType", currentAssetType.String()))
				return nil
			}

			// Use original ctx for the calculation, consistent with getAssetDataRequestMap changes
			changeResp, getChangeRespErr := dayChangeCalculator.CalculateAssetDayChangeValue(ctx, calculateRequest)
			if getChangeRespErr != nil {
				return fmt.Errorf("error calculating asset day change value for asset type %s: %w", currentAssetType.String(), getChangeRespErr)
			}
			s.debugLogCalculateRequestAndChangeResponse(ctx, actorId, calculateRequest, changeResp)
			changeResponseSyncMap.Store(currentAssetType, changeResp)
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		logger.Error(ctx, "error calculating asset day change value", zap.Error(err))
		return &networthPb.GetAssetsDayChangeResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error calculating asset day change value"),
		}, nil
	}

	// Convert sync map to standard map for the response
	finalChangeResponseMap := make(map[string]*networthPb.AssetTypeDayChangeResponse)
	changeResponseSyncMap.Range(func(key enumsPb.AssetType, value *networthPb.AssetTypeDayChangeResponse) bool {
		finalChangeResponseMap[key.String()] = value
		return true
	})

	return &networthPb.GetAssetsDayChangeResponse{
		Status:                          rpcPb.StatusOk(),
		AssetTypeToDayChangeResponseMap: finalChangeResponseMap,
	}, nil
}

func (s *Service) debugLogCalculateRequestAndChangeResponse(ctx context.Context, actorId string, request *assetdaychange.CalculateAssetDayChangeValueRequest, response *networthPb.AssetTypeDayChangeResponse) {
	if isPresent := s.config.NetworthParams().DebugActorIdsForDailyReport().Get(actorId); !isPresent {
		return
	}
	marshalledRequest, marshalRequestErr := json.Marshal(request)
	if marshalRequestErr != nil {
		logger.WarnWithCtx(ctx, "error marshalling marshalRequest", zap.Error(marshalRequestErr))
		return
	}
	marshalledResponse, marshalResponseErr := protojson.Marshal(response)
	if marshalResponseErr != nil {
		logger.WarnWithCtx(ctx, "error marshalling changeResp", zap.Error(marshalResponseErr))
		return
	}
	logger.Info(ctx, "debugging logs for assets day change",
		zap.String("calculateRequest", string(marshalledRequest)),
		zap.String("changeResp", string(marshalledResponse)),
	)
}

func (s *Service) getAssetDataRequestMap(ctx context.Context, request *networthPb.GetAssetsDayChangeRequest) (map[enumsPb.AssetType]*assetdaychange.CalculateAssetDayChangeValueRequest, error) {
	actorId := request.GetActorId()
	requestedAssetTypes := request.GetAssetTypes()
	initialDate := request.GetInitialDate()
	finalDate := request.GetFinalDate()

	ownershipsToProcess := []commontypes.Ownership{
		commontypes.Ownership_EPIFI_TECH,
		commontypes.Ownership_EPIFI_WEALTH,
		commontypes.Ownership_FEDERAL_BANK,
	}

	g := errgroup.New()
	assetValuesInitialDateDataMap := &syncmap.Map[enumsPb.AssetType, *syncmap.Map[commontypes.Ownership, *modelPb.AssetData]]{}
	assetValuesFinalDateDataMap := &syncmap.Map[enumsPb.AssetType, *syncmap.Map[commontypes.Ownership, *modelPb.AssetData]]{}
	// Initialize the resultMap for all requested asset types
	for _, assetType := range requestedAssetTypes {
		assetValuesInitialDateDataMap.Store(assetType, &syncmap.Map[commontypes.Ownership, *modelPb.AssetData]{})
		assetValuesFinalDateDataMap.Store(assetType, &syncmap.Map[commontypes.Ownership, *modelPb.AssetData]{})
	}

	for _, ownership := range ownershipsToProcess {
		currentOwnership := ownership // Capture range variable for goroutine

		g.Go(func() error {
			ctxWithOwnership := epificontext.WithOwnership(ctx, currentOwnership)

			// Fetch initial date histories for the current ownership
			initialDateHistoriesMap, fetchInitialErr := s.assetHistoryDao.GetMultipleHistoriesByDate(ctxWithOwnership, actorId, requestedAssetTypes, initialDate)
			if fetchInitialErr != nil {
				if errors.Is(fetchInitialErr, epifierrors.ErrRecordNotFound) {
					return nil
				}
				return fmt.Errorf("failed to fetch initial date asset histories for ownership %s: %w", currentOwnership.String(), fetchInitialErr)
			}

			for assetType, historyData := range initialDateHistoriesMap {
				// 1. fetch inner map first
				// 2. modify inner map
				// 3. replace inner map with updated map
				ownershipToInitialDataMap, ok := assetValuesInitialDateDataMap.Load(assetType)
				if !ok {
					ownershipToInitialDataMap = &syncmap.Map[commontypes.Ownership, *modelPb.AssetData]{}
				}
				ownershipToInitialDataMap.Store(currentOwnership, historyData.GetData())
				assetValuesInitialDateDataMap.Store(assetType, ownershipToInitialDataMap)
			}
			return nil
		})

		g.Go(func() error {
			ctxWithOwnership := epificontext.WithOwnership(ctx, currentOwnership)

			// Fetch final date histories for the current ownership
			finalDateHistoriesMap, fetchFinalErr := s.assetHistoryDao.GetMultipleHistoriesByDate(ctxWithOwnership, actorId, requestedAssetTypes, finalDate)
			if fetchFinalErr != nil {
				if errors.Is(fetchFinalErr, epifierrors.ErrRecordNotFound) {
					return nil
				}
				return fmt.Errorf("failed to fetch final date asset histories for ownership %s: %w", currentOwnership.String(), fetchFinalErr)
			}

			for assetType, historyData := range finalDateHistoriesMap {
				// 1. fetch inner map first
				// 2. modify inner map
				// 3. replace inner map with updated map
				ownershipToFinalDataMap, ok := assetValuesFinalDateDataMap.Load(assetType)
				if !ok {
					ownershipToFinalDataMap = &syncmap.Map[commontypes.Ownership, *modelPb.AssetData]{}
				}
				ownershipToFinalDataMap.Store(currentOwnership, historyData.GetData())
				assetValuesFinalDateDataMap.Store(assetType, ownershipToFinalDataMap)
			}
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}

	resultMap := make(map[enumsPb.AssetType]*assetdaychange.CalculateAssetDayChangeValueRequest)
	for _, assetType := range requestedAssetTypes {
		ownershipToAssetDataInitialMap := assetValuesInitialDateDataMap.Get(assetType)
		ownershipToAssetDataInitialMapConverted := make(map[commontypes.Ownership]*modelPb.AssetData)
		ownershipToAssetDataInitialMap.Range(func(ownership commontypes.Ownership, assetData *modelPb.AssetData) (continueRange bool) {
			ownershipToAssetDataInitialMapConverted[ownership] = assetData
			return true
		})
		ownershipToAssetDataFinalMap := assetValuesFinalDateDataMap.Get(assetType)
		ownershipToAssetDataFinalMapConverted := make(map[commontypes.Ownership]*modelPb.AssetData)
		ownershipToAssetDataFinalMap.Range(func(ownership commontypes.Ownership, assetData *modelPb.AssetData) (continueRange bool) {
			ownershipToAssetDataFinalMapConverted[ownership] = assetData
			return true
		})
		resultMap[assetType] = &assetdaychange.CalculateAssetDayChangeValueRequest{
			ActorId:                       actorId,
			InitialDate:                   initialDate,
			FinalDate:                     finalDate,
			OwnershipToInitialDateDataMap: ownershipToAssetDataInitialMapConverted,
			OwnershipToFinalDateDataMap:   ownershipToAssetDataFinalMapConverted,
		}
	}

	return resultMap, nil
}
