package mutual_funds

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	networthBePb "github.com/epifi/gamma/api/insights/networth"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/insights/config/genconf"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type MutualFunds struct {
	mfExternalClient mfExternalPb.MFExternalOrdersClient
	conf             *genconf.Config
	timeClient       datetime.Time
}

func NewMutualFundsRefreshDetailsPlugin(mfExternalClient mfExternalPb.MFExternalOrdersClient, genConf *genconf.Config, timeClient datetime.Time) *MutualFunds {
	return &MutualFunds{
		mfExternalClient: mfExternalClient,
		conf:             genConf,
		timeClient:       timeClient,
	}
}

// nolint: funlen
func (m *MutualFunds) GetInstrumentsRefreshDetails(ctx context.Context, actorId string) map[modelPb.NetWorthRefreshAsset]*networthBePb.InstrumentRefreshDetails {

	refreshDetailsMap := make(map[modelPb.NetWorthRefreshAsset]*networthBePb.InstrumentRefreshDetails)
	errorRefreshDetailsMap := make(map[modelPb.NetWorthRefreshAsset]*networthBePb.InstrumentRefreshDetails)
	errorRefreshDetailsMap[modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS] = &networthBePb.InstrumentRefreshDetails{
		HasError:  true,
		AssetName: modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
	}
	var (
		lastRefreshedDuration       time.Duration
		lastRefreshedTime           *timestamppb.Timestamp
		mfHoldingsImportRequestResp *mfExternalPb.GetHoldingsImportRequestsInDateRangeResponse
		err                         error
	)
	isRefreshRequired := false
	// if mutual fund refresh is not required then refresh status would be: REFRESH_STATUS_NOT_REQUIRED
	refreshStatus := networthBePb.RefreshStatus_REFRESH_STATUS_NOT_REQUIRED

	// fetch latest mf holding import request
	mfHoldingsImportRequestResp, err = m.mfExternalClient.GetHoldingsImportRequestsInDateRange(ctx, &mfExternalPb.GetHoldingsImportRequestsInDateRangeRequest{
		ActorId: actorId,
		Limit:   1,
	})

	if rpcErr := epifigrpc.RPCError(mfHoldingsImportRequestResp, err); rpcErr != nil && !mfHoldingsImportRequestResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failed to get mf holdings import requests", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return errorRefreshDetailsMap
	}

	if mfHoldingsImportRequestResp.GetStatus().IsRecordNotFound() || len(mfHoldingsImportRequestResp.GetHoldingsImportRequests()) < 1 {
		logger.Error(ctx, "no mf holdings import requests found", zap.String(logger.ACTOR_ID_V2, actorId))
		return errorRefreshDetailsMap
	}

	mfHoldingsImportReq := mfHoldingsImportRequestResp.GetHoldingsImportRequests()[0]

	if mfHoldingsImportReq == nil {
		logger.Error(ctx, "no mf holdings import requests found", zap.String(logger.ACTOR_ID_V2, actorId))
		return errorRefreshDetailsMap
	}

	currTime := m.timeClient.Now()

	lastRefreshedTime = mfHoldingsImportReq.GetUpdatedAt()
	lastRefreshedDuration = currTime.Sub(lastRefreshedTime.AsTime())

	// direct mapping of NetWorthRefreshAsset string to processing threshold refresh duration is present in config
	refreshThresholdDuration := m.conf.NetWorthRefreshParams().AssetsRefreshThreshold().Get(modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String())

	switch mfHoldingsImportReq.GetState() {

	// if last mf holding import is successful then state would be: EXTERNAL_ORDERS_REFRESH_SUCCESSFUL
	case mfExternalPb.State_EXTERNAL_ORDERS_REFRESH_SUCCESSFUL:

		isRefreshRequired, err = m.isRefreshRequired(lastRefreshedTime, refreshThresholdDuration)
		if err != nil {
			logger.Error(ctx, "error while computing is refresh required", zap.Error(err))
			return errorRefreshDetailsMap
		}

	// if last mf holdings import is in process then state would be: OTP_VERIFICATION_SUCCESSFUL
	case mfExternalPb.State_OTP_VERIFICATION_SUCCESSFUL:
		// direct mapping of NetWorthRefreshAsset string to processing threshold refresh duration is present in config
		processingRefreshThresholdDuration := m.conf.NetWorthRefreshParams().AssetsProcessingRefreshThreshold().Get(modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String())

		isRefreshRequired, err = m.isRefreshRequired(lastRefreshedTime, processingRefreshThresholdDuration)
		if err != nil {
			logger.Error(ctx, "error while computing is refresh required", zap.Error(err))
			return errorRefreshDetailsMap
		}

		// if mf holdings import request is in process then no need to refresh, as refresh is currently in process
		if !isRefreshRequired {
			refreshStatus = networthBePb.RefreshStatus_REFRESH_STATUS_IN_PROCESS
		}

	default:
		// we do not get any successful or in-process mf holdings request
		// we need to fetch last successful request for getting refresh details
		logger.Info(ctx, fmt.Sprintf("get non-success & non-processing state: %v for mf holdings import requests", mfHoldingsImportReq.GetState()))

		// fetch latest successful mf holding import request in state: EXTERNAL_ORDERS_REFRESH_SUCCESSFUL
		mfHoldingsImportRequestResp, err = m.mfExternalClient.GetHoldingsImportRequestsInDateRange(ctx, &mfExternalPb.GetHoldingsImportRequestsInDateRangeRequest{
			ActorId: actorId,
			Limit:   1,
			State:   mfExternalPb.State_EXTERNAL_ORDERS_REFRESH_SUCCESSFUL,
		})
		if rpcErr := epifigrpc.RPCError(mfHoldingsImportRequestResp, err); rpcErr != nil {
			logger.Error(ctx, "failed to get mf holdings import requests", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
			return errorRefreshDetailsMap
		}
		if len(mfHoldingsImportRequestResp.GetHoldingsImportRequests()) < 1 || mfHoldingsImportReq == nil {
			logger.Error(ctx, "no mf holdings import requests found", zap.String(logger.ACTOR_ID_V2, actorId))
			return errorRefreshDetailsMap
		}

		lastRefreshedTime = mfHoldingsImportRequestResp.GetHoldingsImportRequests()[0].GetUpdatedAt()
		lastRefreshedDuration = currTime.Sub(lastRefreshedTime.AsTime())

		isRefreshRequired, err = m.isRefreshRequired(lastRefreshedTime, refreshThresholdDuration)
		if err != nil {
			logger.Error(ctx, "error while computing is refresh required", zap.Error(err))
			return errorRefreshDetailsMap
		}
	}

	if isRefreshRequired {
		refreshStatus = networthBePb.RefreshStatus_REFRESH_STATUS_REQUIRED
	}

	refreshDetailsMap[modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS] = &networthBePb.InstrumentRefreshDetails{
		AssetName:             modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
		LastRefreshedTime:     lastRefreshedTime,
		LastRefreshedDuration: &durationpb.Duration{Seconds: int64(lastRefreshedDuration.Seconds())},
		IsRefreshRequired:     isRefreshRequired,
		HasError:              false,
		RefreshStatus:         refreshStatus,
	}
	return refreshDetailsMap
}

func (m *MutualFunds) isRefreshRequired(lastImportTime *timestamppb.Timestamp, thresholdDuration time.Duration) (bool, error) {

	currTime := m.timeClient.Now()

	if lastImportTime == nil {
		return false, fmt.Errorf("failed to get lastImportTime for mf holdings import request")
	}

	// failed to get value from config
	if thresholdDuration == 0 {
		return false, fmt.Errorf("failed to get refreshThresholdDuration from config for Mutual Funds")
	}

	lastImportDuration := currTime.Sub(lastImportTime.AsTime())

	if lastImportDuration.Minutes() > thresholdDuration.Minutes() {
		return true, nil
	}
	return false, nil
}

func (m *MutualFunds) GetNetWorthRefreshAssetType() modelPb.NetWorthRefreshAsset {
	return modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS
}
