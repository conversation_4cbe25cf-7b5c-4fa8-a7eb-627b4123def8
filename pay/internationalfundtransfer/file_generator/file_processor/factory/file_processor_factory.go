package factory

import (
	"fmt"

	celestialPb "github.com/epifi/be-common/api/celestial"

	genConf "github.com/epifi/gamma/pay/config/server/genconf"

	"github.com/epifi/be-common/pkg/aws/v2/s3"

	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/dao"
	fileProcessor "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/file_processor"
	lrsCheckFileProcessor "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/file_processor/lrs_check"
	swiftTransferFileProcessor "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/file_processor/swift_transfer"
)

type FileProcessorFactory struct {
	s3Client         s3.S3Client
	iftClient        iftPb.InternationalFundTransferClient
	config           *genConf.Config
	fileGeneratorDao dao.FileGenerationAttemptDao
	celestialClient  celestialPb.CelestialClient
}

func NewFileProcessorFactory(
	s3Client s3.S3Client,
	iftClient iftPb.InternationalFundTransferClient,
	config *genConf.Config,
	fileGeneratorDao dao.FileGenerationAttemptDao,
	celestialClient celestialPb.CelestialClient,
) *FileProcessorFactory {
	return &FileProcessorFactory{
		s3Client:         s3Client,
		iftClient:        iftClient,
		config:           config,
		fileGeneratorDao: fileGeneratorDao,
		celestialClient:  celestialClient,
	}
}

func (f *FileProcessorFactory) GetFileProcessor(fileType file_generator.FileType) (fileProcessor.FileProcessor, error) {
	switch fileType {
	case file_generator.FileType_FILE_TYPE_LRS_CHECK, file_generator.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK:
		return lrsCheckFileProcessor.NewLrsCheckFileProcessor(f.s3Client, f.iftClient, f.config, f.fileGeneratorDao, f.celestialClient), nil
	case file_generator.FileType_FILE_TYPE_SWIFT_TRANSFER:
		return swiftTransferFileProcessor.NewSwiftTransferFileProcessor(f.s3Client, f.iftClient), nil
	default:
		return nil, fmt.Errorf("invalid fileType: %v to process input file", fileType)
	}
}
