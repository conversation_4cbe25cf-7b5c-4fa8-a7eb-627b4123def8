package internationalfundtransfer

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	mockPayDao "github.com/epifi/gamma/pay/dao/mocks"
)

type fields struct {
	sofDetailsDao *mockPayDao.MockSofDetailDao
}

func initFields(ctrl *gomock.Controller) *fields {
	return &fields{
		sofDetailsDao: mockPayDao.NewMockSofDetailDao(ctrl),
	}
}

func TestService_BatchManualOverrideSofLimit(t *testing.T) {
	type args struct {
		ctx context.Context
		req *iftPb.BatchManualOverrideSofLimitRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       *iftPb.BatchManualOverrideSofLimitResponse
		wantErr    bool
	}{
		{
			name: "all sof ids manual override success",
			args: args{
				ctx: context.Background(),
				req: &iftPb.BatchManualOverrideSofLimitRequest{
					SofIds: []string{
						"sof-1", "sof-2", "sof-3",
					},
					ManualOverrideDetails: &iftPb.SOFLimitManualOverride{
						OverriddenBy: &iftPb.ManualOverridingUserInfo{
							Email: "<EMAIL>",
						},
						Reason:      iftPb.ManualOverrideReason_MANUAL_OVERRIDE_REASON_NO_LOAN_FOUND,
						Explanation: "test",
					},
				},
			},
			setupMocks: func(f *fields) {
				f.sofDetailsDao.EXPECT().BatchUpdateSofStrategyData(gomock.Any(), []string{"sof-1", "sof-2", "sof-3"}, iftPb.SOFLimitStrategy_SOF_LIMIT_STRATEGY_MANUAL_OVERRIDE, gomock.Any()).Return(
					[]*iftPb.SofDetails{
						{
							Id: "sof-1",
						},
						{
							Id: "sof-2",
						},
						{
							Id: "sof-3",
						},
					}, nil)
				f.sofDetailsDao.EXPECT().UpdateSofDetail(gomock.Any(), "sof-1", &iftPb.SofDetails{
					SofState: iftPb.SofState_SOF_STATE_STRATEGIES_PROCESSING_COMPLETED,
				}, []iftPb.SofDetailFieldMask{
					iftPb.SofDetailFieldMask_SOF_DETAIL_FIELD_MASK_SOF_STATE,
				}).Return(nil, nil)
				f.sofDetailsDao.EXPECT().UpdateSofDetail(gomock.Any(), "sof-2", &iftPb.SofDetails{
					SofState: iftPb.SofState_SOF_STATE_STRATEGIES_PROCESSING_COMPLETED,
				}, []iftPb.SofDetailFieldMask{
					iftPb.SofDetailFieldMask_SOF_DETAIL_FIELD_MASK_SOF_STATE,
				}).Return(nil, nil)
				f.sofDetailsDao.EXPECT().UpdateSofDetail(gomock.Any(), "sof-3", &iftPb.SofDetails{
					SofState: iftPb.SofState_SOF_STATE_STRATEGIES_PROCESSING_COMPLETED,
				}, []iftPb.SofDetailFieldMask{
					iftPb.SofDetailFieldMask_SOF_DETAIL_FIELD_MASK_SOF_STATE,
				}).Return(nil, nil)
			},
			want: &iftPb.BatchManualOverrideSofLimitResponse{
				Status: rpc.StatusOk(),
				SuccessSofIds: []string{
					"sof-1", "sof-2", "sof-3",
				},
				FailedSofIds: nil,
			},
			wantErr: false,
		},
		{
			name: "failed to update 1 + failure in updaing state of 1",
			args: args{
				ctx: context.Background(),
				req: &iftPb.BatchManualOverrideSofLimitRequest{
					SofIds: []string{
						"sof-1", "sof-2", "sof-3",
					},
					ManualOverrideDetails: &iftPb.SOFLimitManualOverride{
						OverriddenBy: &iftPb.ManualOverridingUserInfo{
							Email: "<EMAIL>",
						},
						Reason:      iftPb.ManualOverrideReason_MANUAL_OVERRIDE_REASON_NO_LOAN_FOUND,
						Explanation: "test",
					},
				},
			},
			setupMocks: func(f *fields) {
				f.sofDetailsDao.EXPECT().BatchUpdateSofStrategyData(gomock.Any(), []string{"sof-1", "sof-2", "sof-3"}, iftPb.SOFLimitStrategy_SOF_LIMIT_STRATEGY_MANUAL_OVERRIDE, gomock.Any()).Return(
					[]*iftPb.SofDetails{
						{
							Id: "sof-1",
						},
						{
							Id: "sof-2",
						},
					}, nil)
				f.sofDetailsDao.EXPECT().UpdateSofDetail(gomock.Any(), "sof-1", &iftPb.SofDetails{
					SofState: iftPb.SofState_SOF_STATE_STRATEGIES_PROCESSING_COMPLETED,
				}, []iftPb.SofDetailFieldMask{
					iftPb.SofDetailFieldMask_SOF_DETAIL_FIELD_MASK_SOF_STATE,
				}).Return(nil, nil)
				f.sofDetailsDao.EXPECT().UpdateSofDetail(gomock.Any(), "sof-2", &iftPb.SofDetails{
					SofState: iftPb.SofState_SOF_STATE_STRATEGIES_PROCESSING_COMPLETED,
				}, []iftPb.SofDetailFieldMask{
					iftPb.SofDetailFieldMask_SOF_DETAIL_FIELD_MASK_SOF_STATE,
				}).Return(nil, fmt.Errorf("err"))
			},
			want: &iftPb.BatchManualOverrideSofLimitResponse{
				Status: rpc.StatusOk(),
				SuccessSofIds: []string{
					"sof-1",
				},
				FailedSofIds: []string{"sof-2", "sof-3"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			tt.setupMocks(f)
			s := &Service{
				sofDetailsDao: f.sofDetailsDao,
			}
			got, err := s.BatchManualOverrideSofLimit(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchManualOverrideSofLimit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchManualOverrideSofLimit() got = %v, want %v", got, tt.want)
			}
		})
	}
}
