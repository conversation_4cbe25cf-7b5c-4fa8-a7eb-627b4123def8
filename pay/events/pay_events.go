package events

import (
	"github.com/epifi/be-common/pkg/events"

	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"
)

// event name constants
const (
	EventDebitTransaction = "DebitTransaction"
)

const (
	ServiceName = "pay"
)

type DebitTransaction struct {
	UserId        string
	ProspectId    string
	EventName     string
	SessionId     string
	AttemptId     string
	EventId       string
	TransactionId string
	Timestamp     time.Time
	Status        string
	ServiceName   string
	Protocol      string
	EntryPoint    string
	ActorIdOf     string
}

func NewDebitTransactions(userId, actorIdOf, transactionId, protocol, status string,
	timestamp time.Time) *DebitTransaction {
	return &DebitTransaction{
		UserId:        userId,
		ProspectId:    "",
		EventName:     EventDebitTransaction,
		SessionId:     "",
		AttemptId:     "",
		EventId:       uuid.NewString(),
		TransactionId: transactionId,
		Timestamp:     timestamp,
		Status:        status,
		ServiceName:   ServiceName,
		Protocol:      protocol,
		ActorIdOf:     actorIdOf,
	}
}

func (d *DebitTransaction) GetEventId() string {
	return d.EventId
}

func (d *DebitTransaction) GetUserId() string {
	return d.UserId
}

func (d *DebitTransaction) GetProspectId() string {
	return d.ProspectId
}

func (d *DebitTransaction) GetEventName() string {
	return d.EventName
}

func (d *DebitTransaction) GetEventType() string {
	return events.EventTrack
}

func (d *DebitTransaction) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(d, properties)
	return properties
}

func (d *DebitTransaction) GetEventTraits() map[string]interface{} {
	return nil
}
