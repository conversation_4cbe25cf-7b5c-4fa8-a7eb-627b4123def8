package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/gamma/api/pay/activity/internationalfundtransfer"
	internationalfundtransfer2 "github.com/epifi/gamma/pay/metrics/internationalfundtransfer"
	"github.com/epifi/be-common/pkg/epifierrors"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

func (p *Processor) PublishForexRateMetricToPrometheus(ctx context.Context,
	req *internationalfundtransfer.PublishForexRateMetricToPrometheusRequest) (
	*internationalfundtransfer.PublishForexRateMetricToPrometheusResponse, error) {
	lg := activity.GetLogger(ctx)
	lg.Info("publishing forex rate metric to prometheus", zap.Any("payload", req))
	if req.GetOperationType() != internationalfundtransfer.PublishForexRateMetricToPrometheusRequest_SET {
		return nil, errors.Wrap(epifierrors.ErrPermanent,
			fmt.Sprintf("operation type not supported %s", req.GetOperationType().String()))
	}
	floatAmount, _ := moneyPkg.ToDecimal(req.GetAmount()).Float64()
	internationalfundtransfer2.RecordForexRateUsedPercentageUpdate(req.GetForexRateId(), floatAmount)
	return &internationalfundtransfer.PublishForexRateMetricToPrometheusResponse{}, nil
}
