package internationalfundtransfer_test

import (
	"flag"
	"os"
	"sync"
	"testing"

	"github.com/epifi/gamma/pay/dao/internationalfundtransfer"
	"github.com/epifi/gamma/pay/test"
	"github.com/epifi/be-common/pkg/idgen"
	testV2 "github.com/epifi/be-common/pkg/test/v2"
)

var (
	initialiseSameDbOnce sync.Once
)

func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	dbName, conf, _, db, teardown := test.InitTestServer()

	db = db.Debug()

	idGen := idgen.NewDomainIdGenerator(idgen.NewClock())

	crdbDao := internationalfundtransfer.NewInternationalFundTransferChecksDao(db)
	lrsDao := internationalfundtransfer.NewLRSChecksDao(db)
	iftTs = NewIFTChecksDaoTestSuite(db, crdbDao, conf, dbName)
	lrsTs = NewLRSCheckDaoTestSuite(db, lrsDao, conf, dbName)
	userBlacklistCrdbDao := internationalfundtransfer.NewInternationalFundTransferUsersBlacklistDao(db)
	iftUserBlacklistTs = NewIFTUserBlacklistDaoTestSuite(db, userBlacklistCrdbDao, conf, dbName)

	forexRateTS = newForexRateTestSuite(db, conf, internationalfundtransfer.NewForexDaoCrdb(db, idGen), dbName)
	sofDetailTs = NewSofDetailDaoTestSuite(db, dbName, idGen)

	exitCode := m.Run()
	_ = testV2.DropTestDatabase(db, dbName)
	teardown()
	os.Exit(exitCode)
}
