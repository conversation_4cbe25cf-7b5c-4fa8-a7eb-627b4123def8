package actions

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/fittt"
	schedulerpb "github.com/epifi/gamma/api/fittt/scheduler"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type UpdateSportsChallengeStatsAction struct {
	fitttClient fittt.FitttClient
}

func NewUpdateSportsChallengeStatsAction(fitttClient fittt.FitttClient) *UpdateSportsChallengeStatsAction {
	return &UpdateSportsChallengeStatsAction{
		fitttClient: fitttClient,
	}
}

func (a *UpdateSportsChallengeStatsAction) Execute(ctx context.Context, job *schedulerpb.Job) (stopRecurrence bool, err error) {
	tournamentTagId := job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetTournamentTagId()
	logger.Info(ctx, "UpdateSportsChallengeStatsAction job started",
		zap.String(logger.TAG_ID, tournamentTagId),
		zap.String("SportsType", job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetSportsType().String()))

	err = a.updateSportsChallengeStats(ctx, job)
	if err != nil {
		logger.Error(ctx, "error in updating sports challenge stats", zap.String(logger.TAG_ID, tournamentTagId), zap.Error(err))
		return false, err
	}

	err = a.publishWeeklyRewardsEvent(ctx, job)
	if err != nil {
		logger.Error(ctx, "error in publishing weekly rewards stats", zap.String(logger.TAG_ID, tournamentTagId), zap.Error(err))
		return false, err
	}

	logger.Info(ctx, "Successfully updated sports challenge stats", zap.String(logger.TAG_ID, tournamentTagId))
	return false, nil
}

func (a *UpdateSportsChallengeStatsAction) publishWeeklyRewardsEvent(ctx context.Context, job *schedulerpb.Job) error {
	if job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetWeeklyRewardsPublishDay() == 0 {
		logger.Info(ctx, "not publishing weekly rewards for tournament", zap.String("tournamentId",
			job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetTournamentTagId()))
		return nil
	}

	if time.Weekday(job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetWeeklyRewardsPublishDay()) != time.Now().In(datetime.IST).Weekday() {
		logger.Info(ctx, "not publishing weekly rewards", zap.Any("currentDay", time.Now().In(datetime.IST).Weekday()),
			zap.Any("configuredDay", time.Weekday(job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetWeeklyRewardsPublishDay())))
		return nil
	}
	logger.Info(ctx, "going to publish weekly rewards", zap.Any("currentDay", time.Now().In(datetime.IST).Weekday()),
		zap.Any("configuredDay", time.Weekday(job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetWeeklyRewardsPublishDay())))

	resp, err := a.fitttClient.PublishSportsChallengeWeeklyRewards(ctx, &fittt.PublishSportsChallengeWeeklyRewardsRequest{
		TournamentId:                       job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetTournamentTagId(),
		WeekNo:                             GetPreviousWeekNoForFiSavingsLeague(job),
		NoOfUsersQualifiedForWeeklyRewards: job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetNoOfUsersQualifiedForWeeklyRewards(),
	})
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		logger.Error(ctx, "error in publishing sports challenge weekly rewards", zap.Error(err2),
			zap.Int32("week", GetPreviousWeekNoForFiSavingsLeague(job)))
		return err2
	}
	logger.Info(ctx, "successfully published weekly rewards", zap.Int32("week", GetPreviousWeekNoForFiSavingsLeague(job)))
	return nil
}

func GetPreviousWeekNoForFiSavingsLeague(job *schedulerpb.Job) int32 {
	// using second week start time to calculate weekNo
	// not using tournament start time, as first week of IPL2022 returned from vendor is of 8 days i.e. from Saturday to Saturday (both inclusive)
	// so using second week start timestamp to calculate further weeks

	// difference in seconds between start time and current time
	timeSinceStart := time.Now().In(datetime.IST).Unix() - job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().
		GetTournamentSecondWeekStartTimestamp().GetSeconds()
	return 1 /* first week */ + secondsToWeek(timeSinceStart)
}

// converts seconds to weeks
// does not consider ongoing week, since it has not been completed yet
func secondsToWeek(timeSinceStart int64) int32 {
	return int32(timeSinceStart / 604800) // 60(minute) * 60(hour) * 24(day) * 7(week)
}

func (a *UpdateSportsChallengeStatsAction) updateSportsChallengeStats(ctx context.Context, job *schedulerpb.Job) error {
	tournamentTagId := job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetTournamentTagId()

	// only daily computation of points are supported
	if job.GetSchedule().GetRecurrence().GetEveryDayV2() == nil && job.GetSchedule().GetRecurrence().GetOnce() == nil {
		return fmt.Errorf("only Once or DAILY recurrence is supported for UpdateSportsChallengeStats schedule")
	}

	var startTime, endTime *timestamppb.Timestamp
	if job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetStartTime() != nil &&
		job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetEndTime() != nil {
		startTime = job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetStartTime()
		endTime = job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetEndTime()
	} else {
		previousDay := time.Now().Add(-24 * time.Hour)
		startTime = timestamppb.New(datetime.StartOfDay(previousDay))
		endTime = timestamppb.New(datetime.EndOfDay(previousDay))
	}

	resp, err := a.fitttClient.UpdateSportsChallengeStats(ctx, &fittt.UpdateSportsChallengeStatsRequest{
		TournamentTagId: tournamentTagId,
		From:            startTime,
		To:              endTime,
		SportsType:      job.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetSportsType(),
	})
	if err2 := epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in updating sports challenge stats, error from FIT service", zap.String(logger.TAG_ID, tournamentTagId), zap.Error(err2))
		return err2
	}
	return nil
}
