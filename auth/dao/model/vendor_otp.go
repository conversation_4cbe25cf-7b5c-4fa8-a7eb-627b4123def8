package model

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"time"

	"github.com/epifi/gamma/api/auth"
)

type VendorOtp struct {
	Id string `gorm:"primary_key;type:uuid;default:gen_random_uuid()"`

	ActorId string `gorm:"primary_key"`

	Phone string

	Vendor commonvgpb.Vendor

	RequestId string

	RequestType auth.RequestType

	// time of creation of the vendor otp
	CreatedAt time.Time

	// last updated time of the vendor otp
	UpdatedAt time.Time
}
