package dao_test

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/actor/dao/model"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/auth/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	pkgTestv2 "github.com/epifi/be-common/pkg/test/v2"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	gormv2 "gorm.io/gorm"
)

type DevRegTestSuite struct {
	db         *gormv2.DB
	attemptDao dao.DeviceRegistrationAttemptsDao
}

var (
	ts      *DevRegTestSuite
	tables  = []string{"device_reg_attempts", "actors"}
	actorId = "15d053ca-c521-11eb-8529-0242ac130003"
	actor   = &model.Actor{
		Id:        actorId,
		CreatedAt: time.Now(),
	}
	attemptFix1 = &authPb.DeviceRegistrationAttempt{
		AttemptId:   uuid.New().String(),
		DeviceId:    uuid.New().String(),
		ActorId:     actorId,
		PhoneNumber: "123456789",
		RequestId:   "NEOREQ15d053ca",
		Status:      authPb.DeviceRegistrationStatus_UNREGISTERED,
	}
	attemptFix2 = &authPb.DeviceRegistrationAttempt{
		AttemptId:   uuid.New().String(),
		DeviceId:    uuid.New().String(),
		ActorId:     actorId,
		PhoneNumber: "123456789",
		RequestId:   "NEOREQ15d053ca",
		Status:      authPb.DeviceRegistrationStatus_UNREGISTERED,
	}
	attemptPb3 = &authPb.DeviceRegistrationAttempt{
		AttemptId:   uuid.New().String(),
		DeviceId:    uuid.New().String(),
		ActorId:     actorId,
		PhoneNumber: "123456789",
		RequestId:   "NEOREQ15d053ca",
		Status:      authPb.DeviceRegistrationStatus_UNREGISTERED,
	}
	attemptPb4 = &authPb.DeviceRegistrationAttempt{
		AttemptId:   uuid.New().String(),
		DeviceId:    uuid.New().String(),
		ActorId:     actorId,
		PhoneNumber: "123456789",
		RequestId:   "NEOREQ15d053ca",
		Status:      authPb.DeviceRegistrationStatus_UNREGISTERED,
	}
	attemptPb5 = &authPb.DeviceRegistrationAttempt{
		AttemptId:   uuid.New().String(),
		DeviceId:    uuid.New().String(),
		ActorId:     actorId,
		PhoneNumber: "123456789",
		RequestId:   "NEOREQ15d053ca",
		Status:      authPb.DeviceRegistrationStatus_UNREGISTERED,
	}
)

func TestDeviceRegistrationAttemptsCrdb_Create(t *testing.T) {
	pkgTestv2.PrepareScopedDatabase(t, afuTS.DBName, ts.db, tables)
	type args struct {
		attemptPb *authPb.DeviceRegistrationAttempt
	}
	type Test struct {
		args      args
		wantError bool
	}
	tests := []*Test{
		{
			args:      args{attemptPb: attemptFix1},
			wantError: false,
		},
	}
	for _, tt := range tests {
		res := ts.db.Create(actor)
		assert.Equal(t, nil, res.Error)
		attemptPb, err := ts.attemptDao.Create(context.Background(), tt.args.attemptPb)
		assert.Equal(t, tt.wantError, err != nil)
		if err != nil {
			assert.Equal(t, true, attemptPb.GetAttemptId() != "")
		}
	}
}

func TestDeviceRegistrationAttemptsCrdb_GetLastAttemptByActor(t *testing.T) {
	pkgTestv2.PrepareScopedDatabase(t, afuTS.DBName, ts.db, tables)
	type args struct {
		attemptPbs []*authPb.DeviceRegistrationAttempt
		actorId    string
	}
	type test struct {
		name          string
		args          args
		wantAttemptPb *authPb.DeviceRegistrationAttempt
		wantError     bool
	}
	tests := []test{
		{
			name: "get last attempt success",
			args: args{
				attemptPbs: []*authPb.DeviceRegistrationAttempt{attemptFix1, attemptFix2, attemptPb3, attemptPb4},
				actorId:    actorId,
			},
			wantAttemptPb: attemptPb4,
			wantError:     false,
		},
		{
			name: "get for unknown actor id",
			args: args{
				attemptPbs: []*authPb.DeviceRegistrationAttempt{attemptFix1, attemptFix2},
				actorId:    "random actor id",
			},
			wantAttemptPb: nil,
			wantError:     true,
		},
	}
	res := ts.db.Create(actor)
	assert.Equal(t, false, res.Error != nil)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for _, attempt := range tt.args.attemptPbs {
				_, _ = ts.attemptDao.Create(context.Background(), attempt)
			}
			lastAttempt, err := ts.attemptDao.GetLastAttemptByActor(context.Background(), tt.args.actorId)
			assert.Equal(t, tt.wantError, err != nil)
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&authPb.DeviceRegistrationAttempt{}, "created_at", "updated_at"),
			}
			if diff := cmp.Diff(lastAttempt, tt.wantAttemptPb, opts...); diff != "" {
				t.Errorf("results \ngot = %v \nwant = %v, \nDiff = %v", lastAttempt, tt.wantAttemptPb, diff)
			}
			if err == nil {
				assert.Equal(t, tt.wantAttemptPb.DeviceId, lastAttempt.GetDeviceId())
			}
		})
	}
}

func TestDeviceRegistrationAttemptsCrdb_UpdateDeviceRegistrationStatus(t *testing.T) {
	pkgTestv2.PrepareScopedDatabase(t, afuTS.DBName, ts.db, tables)
	type args struct {
		attemptPb    *authPb.DeviceRegistrationAttempt
		updateStatus authPb.DeviceRegistrationStatus
		attemptId    string
	}
	type test struct {
		name          string
		args          args
		wantAttemptPb *authPb.DeviceRegistrationAttempt
		wantError     bool
	}
	tests := []test{
		{
			name: "success update attempt",
			args: args{
				attemptPb:    attemptPb5,
				updateStatus: authPb.DeviceRegistrationStatus_REGISTERED,
			},
			wantAttemptPb: &authPb.DeviceRegistrationAttempt{
				DeviceId:    attemptPb5.GetDeviceId(),
				ActorId:     attemptPb5.GetActorId(),
				PhoneNumber: attemptPb5.GetPhoneNumber(),
				RequestId:   attemptPb5.GetRequestId(),
				Status:      authPb.DeviceRegistrationStatus_REGISTERED,
			},
			wantError: false,
		},
		{
			name: "record not found for attempt id",
			args: args{
				attemptPb:    attemptFix1,
				updateStatus: authPb.DeviceRegistrationStatus_REGISTERED,
			},
			wantError: false,
		},
	}
	ctx := context.Background()
	res := ts.db.Create(actor)
	assert.Equal(t, false, res.Error != nil)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			attemptPb, err := ts.attemptDao.Create(ctx, tt.args.attemptPb)
			assert.Equal(t, nil, err)
			err = ts.attemptDao.UpdateDeviceRegistrationStatus(ctx, attemptPb.GetAttemptId(), tt.args.updateStatus)
			assert.Equal(t, tt.wantError, err != nil)
			if err == nil && tt.wantAttemptPb != nil {
				tt.wantAttemptPb.AttemptId = attemptPb.GetAttemptId()
				updatedAttemptPb, err := ts.attemptDao.GetAttemptById(ctx, attemptPb.GetAttemptId())
				assert.Equal(t, nil, err)
				if !reflect.DeepEqual(tt.wantAttemptPb, updatedAttemptPb) {
					logger.Error(ctx, fmt.Sprintf("want: %v, got: %v", tt.wantAttemptPb, updatedAttemptPb))
				}
			}
		})
	}
}

func TestDeviceRegistrationAttemptsCrdb_GetAttemptsByActorId(t *testing.T) {
	pkgTestv2.PrepareScopedDatabase(t, afuTS.DBName, ts.db, tables)
	type input struct {
		addAttempts []*authPb.DeviceRegistrationAttempt
		actorId     string
	}
	type test struct {
		name         string
		input        input
		wantAttempts []*authPb.DeviceRegistrationAttempt
	}

	tests := []test{
		{
			name: "get attempts success",
			input: input{
				addAttempts: []*authPb.DeviceRegistrationAttempt{attemptFix1, attemptFix2},
				actorId:     actorId,
			},
			wantAttempts: []*authPb.DeviceRegistrationAttempt{
				attemptFix1, attemptFix2,
			},
		},
	}
	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			res := ts.db.Create(actor)
			if res.Error != nil {
				logger.Error(ctx, "failed to create actor")
			}
			for i, attempt := range tt.input.addAttempts {
				attempt1, err := ts.attemptDao.Create(ctx, attempt)
				tt.wantAttempts[i] = attempt1
				assert.Nil(t, err)
			}
			gotAttempts, err := ts.attemptDao.GetAttemptsByActorId(ctx, tt.input.actorId)
			assert.Nil(t, err)
			assert.Equal(t, len(gotAttempts), len(tt.wantAttempts))
			if len(tt.wantAttempts) == 2 {
				if !((gotAttempts[0].GetAttemptId() == tt.wantAttempts[0].GetAttemptId() && gotAttempts[1].GetAttemptId() == tt.wantAttempts[1].GetAttemptId()) ||
					(gotAttempts[0].GetAttemptId() == tt.wantAttempts[1].GetAttemptId() && gotAttempts[1].GetAttemptId() == tt.wantAttempts[0].GetAttemptId())) {
					t.Errorf("attempts did not match")
				}
			}
		})
	}
}

func TestDeviceRegistrationAttemptsCrdb_GetAttemptById(t *testing.T) {
	pkgTestv2.PrepareScopedDatabase(t, afuTS.DBName, ts.db, tables)

	type test struct {
		name      string
		attemptId string
		want      *authPb.DeviceRegistrationAttempt
		wantErr   error
	}

	attemptId := uuid.New().String()

	tests := []test{
		{
			name:      "empty attempt id",
			attemptId: "",
			wantErr:   fmt.Errorf("attempt id cannot be empty"),
		},
		{
			name:      "successful get",
			attemptId: attemptId,
			wantErr:   nil,
			want:      attemptFix1,
		},
		{
			name:      "record not found",
			attemptId: uuid.New().String(),
			wantErr:   gormv2.ErrRecordNotFound,
		},
	}
	ctx := context.Background()
	attemptFix1.ActorId = actor.Id
	_ = ts.db.Create(actor)
	attemptFix1.AttemptId = attemptId
	if _, err := ts.attemptDao.Create(ctx, attemptFix1); err != nil {
		fmt.Printf("error in inserting fixture: %v\n", err)
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			res, err := ts.attemptDao.GetAttemptById(ctx, tt.attemptId)
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr, err)
			} else {
				assert.Nil(t, err)
			}

			if tt.want != nil {
				if res.GetAttemptId() != tt.want.GetAttemptId() {
					t.Errorf("want=%v\n got=%v\n", tt.want, res)
				}
			}
		})
	}
}

func TestDeviceRegistrationAttemptsCrdb_UpdateSmsInfo(t *testing.T) {
	pkgTestv2.PrepareScopedDatabase(t, afuTS.DBName, ts.db, tables)
	_ = ts.db.Create(actor)
	if _, err := ts.attemptDao.Create(context.Background(), attemptFix1); err != nil {
		t.Errorf("error in inserting fixture: %v\n", err)
	}
	smsInfoFix := &authPb.DeviceRegistrationAttempt_SmsInfo{
		SmsAckAt:         timestamp.New(time.Now().Add(-4 * time.Minute)),
		SmsAckNotifiedAt: timestamp.New(time.Now().Add(-3 * time.Minute)),
	}
	updatedAtt := attemptFix1
	updatedAtt.SmsInfo = smsInfoFix
	tests := []struct {
		name      string
		attemptId string
		smsInfo   *authPb.DeviceRegistrationAttempt_SmsInfo
		wantErr   bool
		want      *authPb.DeviceRegistrationAttempt
		err       error
	}{
		{
			name:      "success update",
			attemptId: attemptFix1.GetAttemptId(),
			smsInfo:   smsInfoFix,
			want:      updatedAtt,
			wantErr:   false,
		},
		{
			name:      "record not found",
			attemptId: uuid.New().String(),
			smsInfo:   smsInfoFix,
			want:      updatedAtt,
			wantErr:   true,
			err:       epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dao.DeviceRegistrationAttemptsCrdb{
				Db: ts.db,
			}
			err := d.UpdateSmsInfo(context.Background(), tt.attemptId, tt.smsInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("unexpected error, \nwantErr %v \ngotErr %v", tt.err, err)
				return
			}
			if !errors.Is(tt.err, err) {
				t.Errorf("unequal error, \nwantErr %v \ngotErr %v", tt.err, err)
				return
			}

			got, _ := d.GetLastAttemptByActor(context.Background(), attemptFix1.GetActorId())
			if diff := cmp.Diff(got.GetSmsInfo(), tt.want.SmsInfo, protocmp.Transform()); diff != "" {
				t.Errorf("diff found:\n%s\n", diff)
			}
		})
	}
}
