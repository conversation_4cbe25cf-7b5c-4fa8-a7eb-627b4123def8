package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	csatComms "github.com/epifi/gamma/cx/ticket/csat/comms"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws"
	"github.com/epifi/be-common/pkg/queue"

	authPb "github.com/epifi/gamma/api/auth"
	commsPb "github.com/epifi/gamma/api/comms"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/issue_category/manager"
	dao2 "github.com/epifi/gamma/cx/watson/dao"
	"github.com/epifi/gamma/pkg/feature/release"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epificontext"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/cx/consumer"
	"github.com/epifi/gamma/cx/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"
	cxTypes "github.com/epifi/gamma/cx/wire/types"

	"github.com/golang/protobuf/jsonpb"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	ticketHelper "github.com/epifi/gamma/cx/ticket/helper"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	ticketConsumerPb "github.com/epifi/gamma/api/cx/ticket/consumer"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/ticket/dao"
)

type Service struct {
	conf                                *config.Config
	supportTicketDao                    dao.ISupportTicketDao
	txnExecutor                         storagev2.TxnExecutor
	helper                              helper.ICustomerIdentifier
	ticketHelper                        ticketHelper.ITicketHelper
	updateTicketPublisher               cxTypes.UpdateTicketPublisher
	crmIssueTrackerIntegrationPublisher cxTypes.CrmIssueTrackerIntegrationPublisher
	releaseEvaluator                    release.IEvaluator
	issueCategoryManager                manager.IssueCategoryManager
	cxGenConf                           *cxGenConf.Config
	watsonClient                        watsonPb.WatsonClient
	commsClient                         commsPb.CommsClient
	authClient                          authPb.AuthClient
	incidentTicketDetailDao             dao2.IIncidentTicketDetailDao
	incidentDao                         dao2.IIncidentDao
	ticketUpdateEventPublisher          cxTypes.TicketUpdateEventPublisher
	ticketClient                        ticketPb.TicketClient
	csatCommsSender                     csatComms.Sender
}

func NewTicketConsumerService(conf *config.Config, supportTicketDao dao.ISupportTicketDao,
	txnExecutor storagev2.TxnExecutor, helper helper.ICustomerIdentifier, ticketHelper ticketHelper.ITicketHelper,
	updateTicketPublisher cxTypes.UpdateTicketPublisher, crmIssueTrackerIntegrationPublisher cxTypes.CrmIssueTrackerIntegrationPublisher,
	releaseEvaluator release.IEvaluator, issueCategoryManager manager.IssueCategoryManager, cxGenConf *cxGenConf.Config,
	watsonClient watsonPb.WatsonClient, commsClient commsPb.CommsClient, authClient authPb.AuthClient,
	incidentTicketDetailDao dao2.IIncidentTicketDetailDao, incidentDao dao2.IIncidentDao, ticketUpdateEventPublisher cxTypes.TicketUpdateEventPublisher,
	ticketClient ticketPb.TicketClient, csatCommsSender csatComms.Sender) *Service {
	return &Service{
		conf:                                conf,
		supportTicketDao:                    supportTicketDao,
		txnExecutor:                         txnExecutor,
		helper:                              helper,
		ticketHelper:                        ticketHelper,
		updateTicketPublisher:               updateTicketPublisher,
		crmIssueTrackerIntegrationPublisher: crmIssueTrackerIntegrationPublisher,
		releaseEvaluator:                    releaseEvaluator,
		issueCategoryManager:                issueCategoryManager,
		cxGenConf:                           cxGenConf,
		watsonClient:                        watsonClient,
		commsClient:                         commsClient,
		authClient:                          authClient,
		incidentTicketDetailDao:             incidentTicketDetailDao,
		incidentDao:                         incidentDao,
		ticketUpdateEventPublisher:          ticketUpdateEventPublisher,
		ticketClient:                        ticketClient,
		csatCommsSender:                     csatCommsSender,
	}
}

const (
	DetailTypeOnTicketCreate       = "onTicketCreate"
	DetailTypeOnTicketUpdate       = "onTicketUpdate"
	DetailTypeOnConversationCreate = "onConversationCreate"
)

var (
	UpdateMaskForTicket = []ticketPb.SupportTicketFieldMask{
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_STATUS,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_SOURCE,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_PRODUCT_CATEGORY_DETAILS,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_PRODUCT_CATEGORY,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_REQUESTER,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_TICKET,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_USER_IDENTIFIER_TYPE,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_USER_IDENTIFIER_VALUE,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_TICKET_UPDATED_AT,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_RAW_TICKET,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_AGENT_GROUP,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_RESPONDER_ID,
	}
	UpdateMaskForReconciliationEvent = []ticketPb.SupportTicketFieldMask{
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_SOURCE,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_STATUS,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_PRODUCT_CATEGORY,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_PRODUCT_CATEGORY_DETAILS,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_USER_IDENTIFIER_TYPE,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_USER_IDENTIFIER_VALUE,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_TICKET,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_REQUESTER,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_TICKET_UPDATED_AT,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_AGENT_GROUP,
		ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_RESPONDER_ID,
	}
)

var _ ticketConsumerPb.TicketConsumerServer = &Service{}

func (s *Service) ProcessFreshdeskTicketEvent(ctx context.Context, req *ticketConsumerPb.ProcessFreshdeskTicketEventRequest) (*ticketConsumerPb.ProcessFreshdeskTicketEventResponse, error) {
	if req.GetDetailType() == DetailTypeOnConversationCreate {
		s.publishConversationEventForMonorailIntegration(ctx, req.GetDetail().GetConversation())
	}
	if req.GetDetailType() != DetailTypeOnTicketCreate && req.GetDetailType() != DetailTypeOnTicketUpdate {
		logger.WarnWithCtx(ctx, "event is not a ticket event hence ignoring the event", zap.String(logger.EVENT_TYPE, req.GetDetailType()))
		return &ticketConsumerPb.ProcessFreshdeskTicketEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status:         queuePb.MessageConsumptionStatus_SUCCESS,
				GrpcStatusCode: rpcPb.StatusOk(),
			},
		}, nil
	}
	if req.GetDetail().GetTicket() == nil {
		logger.WarnWithCtx(ctx, "ticket details are missing in event hence ignoring the event")
		return &ticketConsumerPb.ProcessFreshdeskTicketEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status:         queuePb.MessageConsumptionStatus_SUCCESS,
				GrpcStatusCode: rpcPb.StatusOk(),
			},
		}, nil
	}
	if s.cxGenConf.TicketConfig().IsTicketEventLoggingEnabled() {
		cxLogger.Info(ctx, "Freshdesk ticket event received", zap.Any(logger.TICKET_ID, req.GetDetail().GetTicket()),
			zap.String(logger.EVENT_TYPE, req.GetDetailType()))
	}
	cxTicket := ticketPb.FromFreshdeskVendorTicketAndRequesterMessage(req.GetDetail().GetTicket(),
		req.GetDetail().GetRequester(), s.conf.SupportTicketFreshdeskConfig, s.conf.Application.Environment)
	// initialising another variable for update mask
	// this is done so that we can append the update masks for fields
	// which are populated on best effort basis like actor id, expected resolution time etc
	// and we do not update them as nil if they are not available
	updateMask := UpdateMaskForTicket
	// populate actor id on best effort basis
	actor, err := s.helper.GetActorByCxTicket(ctx, cxTicket)
	if err != nil {
		logger.Error(ctx, "error while identifying actor in ticket consumer", zap.Error(err),
			zap.Any(logger.TICKET_ID, req.GetDetail().GetTicket().GetId()))
	} else {
		cxTicket.ActorId = actor.GetId()
		updateMask = append(updateMask, ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_ACTOR_ID)
	}
	// populate issue category on best effort basis
	issueCategoryId, getCategoryErr := s.getLatestIssueCategoryId(ctx, cxTicket)
	if getCategoryErr != nil {
		logger.Error(ctx, "error while fetching issue category id", zap.Error(getCategoryErr),
			zap.Int64(logger.TICKET_ID, cxTicket.GetId()),
			zap.String(logger.PRODUCT_CATEGORY, cxTicket.GetCustomFieldWithValue().GetProductCategory()))
	} else {
		cxTicket.IssueCategoryId = issueCategoryId
		updateMask = append(updateMask, ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_ISSUE_CATEGORY_ID)
	}

	ctx = epificontext.CtxWithActorId(ctx, actor.GetId())
	jsonMarshaler := jsonpb.Marshaler{EmitDefaults: true, EnumsAsInts: false}
	rawTicketJSON, err := jsonMarshaler.MarshalToString(req.GetDetail().GetTicket())

	// handle marshaling error at best effort basis
	// log the error and proceed with rest of the flow
	if err != nil {
		logger.Error(ctx, "error while marshaling raw ticket event to json", zap.Error(err),
			zap.Any(logger.TICKET_ID, req.GetDetail().GetTicket().GetId()))
	}

	TicketDetails := &ticketPb.TicketDetails{
		Id:                     cxTicket.GetId(),
		Source:                 cxTicket.GetSource(),
		Status:                 cxTicket.GetStatus(),
		ProductCategory:        cxTicket.GetCustomFields().GetProductCategory(),
		ProductCategoryDetails: cxTicket.GetCustomFields().GetProductCategoryDetailsAsString(),
		IdentifierType:         cxTicket.GetIdentifierType(),
		IdentifierValue:        cxTicket.GetIdentifierValue(),
		Ticket:                 cxTicket,
		Requester:              cxTicket.GetRequester(),
		TicketCreatedAt:        cxTicket.GetCreatedAt(),
		TicketUpdatedAt:        cxTicket.GetUpdatedAt(),
		Vendor:                 commonvgpb.Vendor_FRESHDESK,
		TicketMeta:             &ticketPb.TicketMeta{IsProcessedForOnboarding: commontypes.BooleanEnum_FALSE},
		RawTicket:              rawTicketJSON,
		AgentGroup:             cxTicket.GetGroup(),
		ActorId:                cxTicket.GetActorId(),
		IssueCategoryId:        cxTicket.GetIssueCategoryId(),
		ResponderId:            cxTicket.GetResponderId(),
	}
	// Populate expected resolution time field, in case of an error, it will fail silently with relevant logs
	expectedResolutionTime, expectedResolutionTimeErr := s.GetCalculatedExpectedResolutionTime(ctx, cxTicket)
	if expectedResolutionTimeErr != nil {
		if !errors.Is(expectedResolutionTimeErr, ticketHelper.FieldsToUpdateSLAUnchangedErr) {
			logger.Error(ctx, "error while populating expected resolution time", zap.Error(expectedResolutionTimeErr),
				zap.Int64(logger.TICKET_ID, cxTicket.GetId()))
		}
	} else {
		TicketDetails.ExpectedResolutionTime = expectedResolutionTime
		updateMask = append(updateMask, ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_EXPECTED_RESOLUTION_TIME)
	}
	// using updateMask variable here as it will only contain masks for values which are available
	// even if they are fetched on best effort basis
	isTicketChanged, existingTicket, txnErr := s.CreateOrUpdateTicketInTxnAndPublishChanges(ctx, TicketDetails, updateMask)
	if txnErr != nil {
		logger.Error(ctx, "error in ticket update/create transaction", zap.Int64(logger.TICKET_ID, TicketDetails.GetId()),
			zap.Error(txnErr))
		return &ticketConsumerPb.ProcessFreshdeskTicketEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status:         queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				GrpcStatusCode: rpcPb.StatusInternal(),
			},
		}, nil
	}
	// Publish to other queues on best effort basis for further processing
	s.publishTicketChangeEventForMonorailIntegration(ctx, isTicketChanged, existingTicket, TicketDetails)
	s.ingestTicketEventToWatson(ctx, existingTicket, TicketDetails)

	// based on ticket change event validity trigger csat for the ticket
	logger.Info(ctx, "starting csat evaluation for ticket", zap.Int64("ticket_id", TicketDetails.GetId()))
	err = s.validateEventAndSendCsat(ctx, isTicketChanged, existingTicket, TicketDetails)
	if err != nil {
		logger.Error(ctx, "error while sending csat comms", zap.Error(err), zap.Int64(logger.TICKET_ID, TicketDetails.GetId()))
	}
	s.publishUpdateTicketEvent(ctx, isTicketChanged, cxTicket)
	return &ticketConsumerPb.ProcessFreshdeskTicketEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status:         queuePb.MessageConsumptionStatus_SUCCESS,
			GrpcStatusCode: rpcPb.StatusOk(),
		},
	}, nil
}

func (s *Service) ProcessTicketReconciliationEvent(ctx context.Context, req *ticketConsumerPb.ProcessTicketReconciliationEventRequest) (*ticketConsumerPb.ProcessTicketReconciliationEventResponse, error) {
	if len(req.GetTickets()) == 0 {
		logger.Info(ctx, "event with empty ticket list received in ticket reconciliation consumer")
		return &ticketConsumerPb.ProcessTicketReconciliationEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status:         queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				GrpcStatusCode: rpcPb.StatusInvalidArgument(),
			},
		}, nil
	}
	// loop through the tickets in request and update the ticket data in db
	// not updating raw ticket here as we don't have that available for reconciliation events
	for _, ticket := range req.GetTickets() {
		// if ticket is empty skip the ticket details population
		if ticket == nil {
			logger.Info(ctx, "nil ticket found in reconciliation event")
			continue
		}
		// initialising another variable for update mask
		// this is done so that we can append the update masks for fields
		// which are populated on best effort basis like actor id, expected resolution time etc
		// and we do not update them as nil if they are not available
		updateMask := UpdateMaskForReconciliationEvent

		// populate issue category on best effort basis
		issueCategoryId, getCategoryErr := s.getLatestIssueCategoryId(ctx, ticket)
		if getCategoryErr != nil {
			logger.Error(ctx, "error while fetching issue category id", zap.Error(getCategoryErr),
				zap.Int64(logger.TICKET_ID, ticket.GetId()),
				zap.String(logger.PRODUCT_CATEGORY, ticket.GetCustomFieldWithValue().GetProductCategory()))
		} else {
			ticket.IssueCategoryId = issueCategoryId
			updateMask = append(updateMask, ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_ISSUE_CATEGORY_ID)
		}

		ticketDetails := &ticketPb.TicketDetails{
			Id:                     ticket.GetId(),
			Source:                 ticket.GetSource(),
			Status:                 ticket.GetStatus(),
			ProductCategory:        ticket.GetCustomFields().GetProductCategory(),
			ProductCategoryDetails: ticket.GetCustomFields().GetProductCategoryDetailsAsString(),
			IdentifierType:         ticket.GetIdentifierType(),
			IdentifierValue:        ticket.GetIdentifierValue(),
			Ticket:                 ticket,
			Requester:              ticket.GetRequester(),
			TicketCreatedAt:        ticket.GetCreatedAt(),
			TicketUpdatedAt:        ticket.GetUpdatedAt(),
			TicketMeta:             &ticketPb.TicketMeta{IsProcessedForOnboarding: commontypes.BooleanEnum_FALSE},
			AgentGroup:             ticket.GetGroup(),
			IssueCategoryId:        ticket.GetIssueCategoryId(),
			ResponderId:            ticket.GetResponderId(),
		}
		// enriching actor id on best effort basis
		// if there is an error, we are not retrying here to avoid starving other events
		actor, err := s.helper.GetActorByCxTicket(ctx, ticket)
		if err != nil {
			logger.Error(ctx, "error while identifying actor in ticket consumer", zap.Error(err), zap.Any(logger.TICKET_ID, ticket.GetId()))
		} else {
			ticketDetails.ActorId = actor.GetId()
			ticketDetails.Ticket.ActorId = actor.GetId()
			updateMask = append(updateMask, ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_ACTOR_ID)
		}
		// enrich the expected resolution time on best effort basis and fail silently with relevant logs
		// if there is an error, we are not retrying here to avoid starving other events
		// populate expected resolution time field, in case of an error, it will fail silently with relevant logs
		expectedResolutionTime, expectedResolutionTimeErr := s.GetCalculatedExpectedResolutionTime(ctx, ticket)
		if expectedResolutionTimeErr != nil {
			logger.Error(ctx, "error while populating expected resolution time", zap.Error(expectedResolutionTimeErr), zap.Int64(logger.TICKET_ID, ticket.GetId()))
		} else {
			ticketDetails.ExpectedResolutionTime = expectedResolutionTime
			updateMask = append(updateMask, ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_EXPECTED_RESOLUTION_TIME)
		}
		// update the ticket details in db
		// using updateMask variable here as it will only contain masks for values which are available
		// even if they are fetched on best effort basis
		_, _, txnErr := s.CreateOrUpdateTicketInTxnAndPublishChanges(ctx, ticketDetails, updateMask)
		if txnErr != nil {
			logger.Error(ctx, "error in ticket update/create transaction", zap.Int64(logger.TICKET_ID, ticketDetails.GetId()), zap.Error(txnErr))
			// return transient error if any ticket update in db fails, so we can retry the updates
			// since the updates are idempotent it's fine to retry already updated tickets as well
			return &ticketConsumerPb.ProcessTicketReconciliationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status:         queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
					GrpcStatusCode: rpcPb.StatusInternal(),
				},
			}, nil
		}
	}
	return &ticketConsumerPb.ProcessTicketReconciliationEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status:         queuePb.MessageConsumptionStatus_SUCCESS,
			GrpcStatusCode: rpcPb.StatusOk(),
		},
	}, nil
}

func (s *Service) getLatestIssueCategoryId(ctx context.Context, ticket *ticketPb.Ticket) (string, error) {
	issueCategoryId, err := s.issueCategoryManager.GetId(ctx,
		ticket.GetCustomFieldWithValue().GetProductCategory(), ticket.GetCustomFieldWithValue().GetProductCategoryDetails(),
		ticket.GetCustomFieldWithValue().GetSubCategory())
	if err != nil {
		return "", err
	}
	return issueCategoryId, nil
}

// Returns whether ticket is changed and the existing ticket in the DB
func (s *Service) CreateOrUpdateTicketInTxnAndPublishChanges(ctx context.Context, ticketDetails *ticketPb.TicketDetails,
	updateMask []ticketPb.SupportTicketFieldMask) (bool, *ticketPb.TicketDetails, error) {
	var (
		existingTicket  *ticketPb.TicketDetails
		isTicketChanged bool
		err             error
	)
	isTicketChanged = false
	if txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		existingTicket, err = s.supportTicketDao.GetByIdWithLock(txnCtx, ticketDetails.GetId())
		if err != nil {
			// record not found in db hence will create a new record in db
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				_, err = s.supportTicketDao.Create(txnCtx, ticketDetails)
				if err != nil {
					// there can be an edge case where more than one threads tries to create the record simultaneously and current one fails
					// this can only happen once for a ticket hence will just return transient error here and
					// ticket details will be updated in next try
					return errors.Wrap(err, "error while updating ticket details in db")
				}
				// create successful
				isTicketChanged = true
				return nil
			}
			return errors.Wrap(err, "error while fetching ticket details from db")
		}

		// don't update the ticket if the ticket provided in event is older than the ticket in db
		if existingTicket.GetTicketUpdatedAt().AsTime().After(ticketDetails.GetTicketUpdatedAt().AsTime()) {
			return nil
		}
		_, err = s.supportTicketDao.Update(txnCtx, ticketDetails, updateMask)
		if err != nil {
			return errors.Wrap(err, "error while updating ticket details in db")
		}
		isTicketChanged = true
		// update successful or not required
		return nil
	}); txnErr != nil {
		return false, nil, errors.Wrap(txnErr, "error in txn block")
	}

	return isTicketChanged, existingTicket, nil
}

func (s *Service) GetCalculatedExpectedResolutionTime(ctx context.Context, cxTicket *ticketPb.Ticket) (*timestamppb.Timestamp, error) {
	// SLA is populated based on best effort basis and will not be a blocking call in case of a error
	// For updating SLA on freshdesk, event is again published to common consumer, in case due to some unforeseen circumstance
	// updating SLA on freshdesk fails, we don't want to retry again and again and starve other events in the current queue
	if s.conf.TicketConfig.SLAConfig.IsSLACalculationEnabledInTicketConsumer {
		expectedResolutionTime, expectedResolutionTimeErr := s.ticketHelper.GetSLAForTicket(ctx, cxTicket)
		if expectedResolutionTimeErr != nil {
			// in case of an error, we log the error, but the expectedResolutionTime in this case is nil
			return nil, errors.Wrap(expectedResolutionTimeErr, "error while enriching expected resolution time")
		} else {
			// if flag is enabled to further update the expected resolution time on freshdesk on best effort basis
			// then we publish event in common consumer to update the "expected resolution date" custom field on freshdesk
			if s.conf.TicketConfig.SLAConfig.IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk {
				updateOnFreshdeskErr := s.updateExpectedResolutionTimeOnFreshdesk(ctx, cxTicket.GetId(), expectedResolutionTime)
				if updateOnFreshdeskErr != nil {
					cxLogger.Error(ctx, "error while publishing message to update expected resolution date on freshdesk", zap.Error(updateOnFreshdeskErr))
				}
			}
			// in case SLA is not available for a combination of product category, product category details and subcategory
			// the engineering decision taken here is that nil would be populated
			// so not checking for nil here and assigning whatever SLA is available
			return expectedResolutionTime, nil
		}
	}
	// Is population of expected resolution time is disabled in config, we return nil
	return nil, errors.New("populating expected resolution time is disabled in config")
}

func (s *Service) updateExpectedResolutionTimeOnFreshdesk(ctx context.Context, ticketId int64, calculatedExpectedResolutionDate *timestamppb.Timestamp) error {
	// Populating expected resolution date custom field on freshdesk is being done in an async manner
	// so that we can retry in case of some issue, this also helps us to limit huge inflow by consuming the events in an async manner
	// in case a large number of events are suddenly published
	_, err := s.updateTicketPublisher.Publish(ctx, &consumer.UpdateTicketEventRequest{
		UpdateTicketPayload: &consumer.UpdateTicketPayload{
			UpdateTicketRequestType: consumer.UpdateTicketRequestType_UPDATE_TICKET_REQUEST_TYPE_EXPECTED_RESOLUTION_DATE,
			DetailsPayload: &consumer.UpdateTicketPayload_ExpectedResolutionDateUpdatePayload{ExpectedResolutionDateUpdatePayload: &consumer.ExpectedResolutionDateUpdatePayload{
				TicketId:               ticketId,
				ExpectedResolutionDate: calculatedExpectedResolutionDate,
			}},
		},
	})
	if err != nil {
		return errors.Wrap(err, "failed publishing to update ticket event queue to update SLA")
	}
	return nil
}

func (s *Service) publishUpdateTicketEvent(ctx context.Context, isUpdated bool, ticket *ticketPb.Ticket) {
	switch {
	case !s.cxGenConf.TicketConfig().IsTicketUpdateEventPublishingEnabled():
		logger.Info(ctx, "publishing ticket update event is not enabled")
	case !isUpdated:
		return
	default:
		// setting product category in message attribute to allow teams easily filter tickets for their use-case
		productCategoryMsgAttribute, err := aws.NewDefaultStringMessageAttribute(queue.MessageAttributeProductCategory,
			ticket.GetCustomFields().GetProductCategory().String())
		if err != nil {
			logger.Error(ctx, "failed to create message attribute", zap.Error(err),
				zap.String(logger.PRODUCT_CATEGORY, ticket.GetCustomFields().GetProductCategory().String()))
			return
		}

		groupMsgAttribute, err := aws.NewDefaultStringMessageAttribute(queue.MessageAttributeGroup, ticket.GetGroup().String())
		if err != nil {
			logger.Error(ctx, "failed to create message attribute", zap.Error(err), zap.String(logger.AGENT_GROUP, ticket.GetGroup().String()))
			return
		}

		_, err = s.ticketUpdateEventPublisher.PublishWithAttributes(ctx, &ticketPb.TicketUpdateEvent{Ticket: ticket},
			[]queue.MessageAttribute{productCategoryMsgAttribute, groupMsgAttribute})
		if err != nil {
			logger.Error(ctx, "failed to publish ticket update event", zap.Error(err), zap.Int64(logger.TICKET_ID, ticket.GetId()),
				zap.String(logger.PRODUCT_CATEGORY, ticket.GetCustomFields().GetProductCategory().String()))
			return
		}
	}
}
