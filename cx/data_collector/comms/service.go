package comms

import (
	"context"

	cxLogger "github.com/epifi/gamma/cx/logger"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/cx"
	cxCommsPb "github.com/epifi/gamma/api/cx/data_collector/communications"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type Service struct {
	cxCommsPb.UnimplementedCustomerCommunicationServer
	commsClient commsPb.CommsClient
	authEngine  auth_engine.IAuthEngine
	commsConfig *config.Comms
}

func NewService(commsClient commsPb.CommsClient, authEngine auth_engine.IAuthEngine, commsConfig *config.Comms) *Service {
	return &Service{
		commsClient: commsClient,
		authEngine:  authEngine,
		commsConfig: commsConfig,
	}
}

var _ cxCommsPb.CustomerCommunicationServer = &Service{}

func (s *Service) GetCustomerMessages(ctx context.Context, req *cxCommsPb.GetCustomerMessagesRequest) (*cxCommsPb.GetCustomerMessagesResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(),
		req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxCommsPb.GetCustomerMessagesResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Info(ctx, "actor information not present in header")
		return &cxCommsPb.GetCustomerMessagesResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found"),
		}, nil
	}
	commsRequest := buildGetMessagesRequest(req, s.commsConfig)
	commsResp, err := s.commsClient.GetMessages(ctx, commsRequest)
	if te := epifigrpc.RPCError(commsResp, err); te != nil {
		cxLogger.Error(ctx, "unable to get messages for an actor", zap.Error(te))
		return &cxCommsPb.GetCustomerMessagesResponse{Status: rpcPb.StatusInternalWithDebugMsg("unable to get messages for an actor")}, nil
	}
	return &cxCommsPb.GetCustomerMessagesResponse{Status: rpcPb.StatusOk(), DetailedMessageList: commsResp.GetDetailedMessageList()}, nil
}

func buildGetMessagesRequest(req *cxCommsPb.GetCustomerMessagesRequest, commsConfig *config.Comms) *commsPb.GetMessagesRequest {
	commsReq := &commsPb.GetMessagesRequest{
		ActorId:   req.GetHeader().GetActor().GetId(),
		StartDate: req.GetStartDate(),
		EndDate:   req.GetEndDate(),
		Filter: &commsPb.GetMessagesRequest_Filter{
			MediumList: req.GetFilters().GetMediumList(),
		},
		PageContext: &commsPb.PageContextRequest{PageSize: uint32(commsConfig.PageSize)},
	}
	switch req.GetPageContext().GetToken().(type) {
	case *cx.PageContextRequest_BeforeToken:
		commsReq.PageContext.Token = &commsPb.PageContextRequest_BeforeToken{BeforeToken: req.GetPageContext().GetBeforeToken()}
	case *cx.PageContextRequest_AfterToken:
		commsReq.PageContext.Token = &commsPb.PageContextRequest_AfterToken{AfterToken: req.GetPageContext().GetAfterToken()}
	}
	return commsReq
}

func (s *Service) GetMessageDetails(ctx context.Context, req *cxCommsPb.GetMessageDetailsRequest) (*cxCommsPb.GetMessageDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(),
		req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxCommsPb.GetMessageDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	commsResp, err := s.commsClient.GetMessage(ctx, &commsPb.GetMessageRequest{VendorMessageId: req.GetVendorMessageId()})
	if te := epifigrpc.RPCError(commsResp, err); te != nil {
		logger.Error(ctx, "unable to get message for vendor message id", zap.Error(te))
		return &cxCommsPb.GetMessageDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("unable to get message for vendor message id"),
		}, nil
	}
	return &cxCommsPb.GetMessageDetailsResponse{
		Status:          rpcPb.StatusOk(),
		DetailedMessage: commsResp.GetDetailedMessage(),
		SmsVendorData:   commsResp.GetSmsVendorData(),
	}, nil
}
