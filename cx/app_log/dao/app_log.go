package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	applogPb "github.com/epifi/gamma/api/cx/app_log"
	"github.com/epifi/gamma/cx/app_log/dao/model"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	cxLogger "github.com/epifi/gamma/cx/logger"
	cxTypes "github.com/epifi/gamma/cx/wire/types"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type AppLogDao struct {
	rdb        cxTypes.CxCacheStore
	cxGenConf  *cxGenConf.Config
	cxS3Client s3.S3Client
}

func NewAppLogDao(redisClient cxTypes.CxCacheStore, cxGenConf *cxGenConf.Config, cxS3Client s3.S3Client) *AppLogDao {
	return &AppLogDao{
		rdb:        redisClient,
		cxGenConf:  cxGenConf,
		cxS3Client: cxS3Client,
	}
}

var _ IAppLogDao = &AppLogDao{}

func (a *AppLogDao) Add(ctx context.Context, logModel *model.AppLog, ttl time.Duration) (logKey string, err error) {
	defer metric_util.TrackDuration("cx/app_log/dao", "AppLogDao", "Add", time.Now())

	if logModel.IdentifierValue == "" || logModel.IdentifierType == applogPb.IdentifierType_IDENTIFIER_TYPE_UNSPECIFIED {
		return "", epifierrors.ErrInvalidArgument
	}
	logKey = MakeKeyForLogModel(logModel)
	// write the log to s3
	s3FilePath := a.getS3FilePathForLogKey(logKey)
	logger.Info(ctx, "uploading log file to s3 bucket", zap.Any("s3path", s3FilePath))
	err = a.cxS3Client.Write(ctx, s3FilePath, []byte(logModel.Logs), "bucket-owner-full-control")
	if err != nil {
		return "", errors.Wrap(err, "error writing log file to s3")
	}
	return logKey, nil
}

func (a *AppLogDao) getS3FilePathForLogKey(logKey string) string {
	// AppLogsFolderName has S3 lifecycle rule setup to delete the log file after 15 days
	return fmt.Sprintf("%s/%s.log", a.cxGenConf.CxS3Config().AppLogsFolderName, logKey)
}

func (a *AppLogDao) AddChunk(ctx context.Context, logModel *model.AppLog) (logKey string, err error) {
	defer metric_util.TrackDuration("cx/app_log/dao", "AppLogDao", "AddChunk", time.Now())
	logKey = MakeKeyForLogModel(logModel)
	value, err := json.Marshal(logModel)
	if err != nil {
		cxLogger.Info(ctx, "error while marshaling app log model", zap.Error(err))
		return "", fmt.Errorf("unable to marshal model %w", err)
	}
	prevExpire, err := time.Parse(time.RFC3339, logModel.Timestamp)
	if err != nil {
		return "", errors.Wrap(err, "Failed to parse time")
	}
	err = a.rdb.CreateOrAppendToList(ctx, time.Since(prevExpire), logKey, string(value))
	if err != nil {
		cxLogger.Error(ctx, "error while adding log chunk", zap.String(logger.IDENTIFIER_TYPE, logModel.IdentifierType.String()), zap.String(logger.IDENTIFIER_VALUE, logModel.IdentifierValue),
			zap.String("logKey", logKey), zap.Error(err),
		)
		return "", err
	}
	return logKey, nil
}

func (a *AppLogDao) Fetch(ctx context.Context, key string, chunkIndex int64) (*model.AppLog, error, int64) {
	defer metric_util.TrackDuration("cx/app_log/dao", "AppLogDao", "Fetch", time.Now())

	// fetch the file from s3
	appLog, err, listLen := a.fetchFromS3(ctx, key, chunkIndex)
	if err != nil {
		// For backward compatibility, check if the file is in redis
		// NOTE: this "fetch from redis" can be removed 15 days(ttl for logs) post these changes to shift form redis to s3 go live
		cxLogger.Info(ctx, "failed to fetch log data from s3, trying to fetch from redis...", zap.Error(err), zap.String("logKey", key))
		appLog, err, listLen = a.fetchFromRedis(ctx, key, chunkIndex)
		if err != nil {
			return nil, errors.Wrap(err, "error while fetching log data from redis"), 0
		}
	} else {
		cxLogger.Info(ctx, "successfully fetched log data from s3", zap.Error(err), zap.String("logKey", key))
	}
	return appLog, nil, listLen
}

func (a *AppLogDao) fetchFromS3(ctx context.Context, key string, chunkIndex int64) (*model.AppLog, error, int64) {
	defer metric_util.TrackDuration("cx/app_log/dao", "AppLogDao", "FetchFromS3", time.Now())
	s3FilePath := a.getS3FilePathForLogKey(key)
	res, err := a.cxS3Client.Read(ctx, s3FilePath)
	if err != nil {
		cxLogger.Error(ctx, "error while fetching log data from s3", zap.Error(err))
		return nil, errors.Wrap(err, "error while fetching log data from S3"), 0
	}
	chunkLength := a.cxGenConf.AppLog().LogChunkSize()
	// no of chunks the file will be divided into
	// NOTE: This chunking is only to support the RPC limitation.
	// This should be modified to S3 presigned URL or a streaming RPC or at least use temporary cache once we read the S3 file
	// monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=47323
	logs := string(res)
	listLen := int64(len(logs)/chunkLength + 1)
	// check if given index is valid
	if chunkIndex < 0 || !(chunkIndex < listLen) {
		cxLogger.Info(ctx, "invalid chunkIndex", zap.String("logKey", key), zap.Any("chunkIndex", chunkIndex), zap.Any("listLen", listLen))
		return nil, fmt.Errorf("chunk index is out of range"), listLen
	}
	// take slice of chunk size
	left := int(chunkIndex) * chunkLength
	right := integer.Min(int(chunkIndex+1)*chunkLength, len(logs))
	chunk := logs[left:right]
	appLog := &model.AppLog{
		Logs: chunk,
	}
	return appLog, nil, listLen
}

func (a *AppLogDao) fetchFromRedis(ctx context.Context, key string, chunkIndex int64) (*model.AppLog, error, int64) {
	defer metric_util.TrackDuration("cx/app_log/dao", "AppLogDao", "FetchFromRedis", time.Now())
	// get number of chunks stored for key
	listLen, err := a.rdb.ListLength(ctx, key)
	// value stored at key is not a list
	if err != nil {
		cxLogger.Error(ctx, "error while fetching logs chunk list", zap.Error(err))
		return nil, err, 0
	}
	// check if given index is valid
	if chunkIndex < 0 || !(chunkIndex < listLen) {
		cxLogger.Info(ctx, "invalid chunkIndex", zap.String("logKey", key), zap.Any("chunkIndex", chunkIndex), zap.Any("listLen", listLen))
		return nil, fmt.Errorf("chunk index is out of range"), listLen
	}
	// fetch the chunk stored at that index
	res, err := a.rdb.GetListElement(ctx, key, chunkIndex)
	// redis.Nil is nil data error given by redis in case of key not found
	if err == redis.Nil {
		cxLogger.Info(ctx, "value not found in redis for app log", zap.String("logKey", key))
		return nil, nil, 0
	} else if err != nil {
		cxLogger.Error(ctx, "error while fetching data from redis", zap.Error(err))
		return nil, fmt.Errorf("error while fetching data from redis %w", err), 0
	}
	// unmarshal the response to model
	appLog := &model.AppLog{}
	if err := json.Unmarshal([]byte(res), appLog); err != nil {
		cxLogger.Error(ctx, "error while un-marshaling redis data to model", zap.Error(err))
		return nil, fmt.Errorf("failed to unmarshal app log data %w", err), 0
	}
	return appLog, nil, listLen
}
