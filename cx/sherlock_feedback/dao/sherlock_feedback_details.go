package dao

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"sort"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	sbPb "github.com/epifi/gamma/api/cx/sherlock_feedback"
	"github.com/epifi/gamma/cx/sherlock_feedback/dao/model"

	gormV2 "gorm.io/gorm"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

// SherlockFeedbackDetailsDao holds the dependencies required to access DB models
type SherlockFeedbackDetailsDao struct {
	db *gormV2.DB
}

var _ ISherlockFeedbackDetailsDao = &SherlockFeedbackDetailsDao{}

// NewSherlockFeedbackDetailsDao creates a new SherlockFeedbackDetailsDao instance
func NewSherlockFeedbackDetailsDao(db types.SherlockPGDB) *SherlockFeedbackDetailsDao {
	return &SherlockFeedbackDetailsDao{
		db: db,
	}
}

func (s *SherlockFeedbackDetailsDao) createSherlockFeedback(ctx context.Context, tx *gormV2.DB, feedbackDetails *sbPb.SherlockFeedbackDetails) (*sbPb.SherlockFeedbackDetails, error) {
	if feedbackDetails.GetFeedbackCategory() == sbPb.FeedbackCategory_FEEDBACK_CATEGORY_UNSPECIFIED || feedbackDetails.GetFeedbackMessage() == "" || feedbackDetails.GetAgentEmail() == "" {
		return nil, errors.New("category, feedback msg and agent email are mandatory parameters")
	}
	sherlockFeedbackModel := model.NewSherlockFeedbackFromProtoMsg(feedbackDetails)
	db := gormctxv2.FromContextOrDefault(ctx, tx)
	if err := db.Create(sherlockFeedbackModel).Error; err != nil {
		return nil, errors.Wrap(err, "error while creating sherlock feedback record in db")
	}
	return sherlockFeedbackModel.ToProtoMessage(), nil
}

func (s *SherlockFeedbackDetailsDao) createFeedbackMetaDataMappings(ctx context.Context, tx *gormV2.DB, feedbackMetaData *sbPb.FeedbackMetaData, feedbackId string) (*sbPb.SherlockFeedbackMetaData, error) {
	if feedbackId == "" {
		return nil, errors.New("feedbackId is mandatory")
	}
	sherlockFeedbackMetaDataModel := model.NewSherlockFeedbackMetaDataMappingFromProtoMsg(feedbackMetaData, feedbackId)
	db := gormctxv2.FromContextOrDefault(ctx, tx)
	if err := db.Create(sherlockFeedbackMetaDataModel).Error; err != nil {
		return nil, errors.Wrap(err, "error while creating sherlock feedback meta data record in db")
	}
	return sherlockFeedbackMetaDataModel.ToProtoMessage(), nil
}

func isMetadataToBeStoredBasedOnFeedbackCategory(feedbackCategory sbPb.FeedbackCategory) bool {
	switch feedbackCategory {
	case sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
		sbPb.FeedbackCategory_FEEDBACK_CATEGORY_REPORT_PRODUCT_ISSUE,
		sbPb.FeedbackCategory_FEEDBACK_CATEGORY_REPORT_SHERLOCK_ISSUE:
		return true
	default:
		return false
	}
}

func (s *SherlockFeedbackDetailsDao) Create(ctx context.Context, feedbackMetaDataMapping *sbPb.SherlockFeedbackDetails) (*sbPb.SherlockFeedbackDetails, error) {
	defer metric_util.TrackDuration("cx/sherlock_feedback/dao", "SherlockFeedbackDetailsDao", "Create", time.Now())
	var sherlockFeedbackDetailsProtoMsg *sbPb.SherlockFeedbackDetails
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	err := db.Transaction(func(tx *gormV2.DB) error {
		feedbackData, err := s.createSherlockFeedback(ctx, tx, feedbackMetaDataMapping)
		if err != nil {
			return errors.Wrap(err, "failed to create feedback details")
		}
		// if we don't need to store metadata based on the feedback category we will return here
		// with the proto msg created based on feedback details and nil metadata
		if !isMetadataToBeStoredBasedOnFeedbackCategory(feedbackMetaDataMapping.GetFeedbackCategory()) {
			sherlockFeedbackDetailsProtoMsg = model.ConvertToSherlockFeedbackDetailsProtoMsg(feedbackData, nil)
			return nil
		}
		feedbackMetaDataMappingDetails, metaDataErr := s.createFeedbackMetaDataMappings(ctx, tx, feedbackMetaDataMapping.GetFeedbackMetaData(), feedbackData.Id)
		if metaDataErr != nil {
			return errors.Wrap(metaDataErr, "failed to create feedback metadata mapping")
		}
		sherlockFeedbackDetailsProtoMsg = model.ConvertToSherlockFeedbackDetailsProtoMsg(feedbackData, feedbackMetaDataMappingDetails)
		return nil
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to create feedback record in db")
	}
	return sherlockFeedbackDetailsProtoMsg, nil
}

func (s *SherlockFeedbackDetailsDao) GetAllFeedbacks(ctx context.Context, pageToken *pagination.PageToken,
	pageLimit int, filters *sbPb.SherlockFeedbackFilters) ([]*sbPb.SherlockFeedbackDetails, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("cx/sherlock_feedback/dao", "SherlockFeedbackDetailsDao", "GetAllFeedbacks", time.Now())
	if filters.GetToDate() == nil || filters.GetFromDate() == nil {
		return nil, nil, errors.New("from date and to date are mandatory")
	}
	var sherlockFeedbackModelList []*model.SherlockFeedbackDetails
	query := gormctxv2.FromContextOrDefault(ctx, s.db)
	// Apply all provided filters
	query = query.Model(&model.SherlockFeedback{})
	query = query.Joins("LEFT JOIN sherlock_feedback_meta_data_mappings AS t2 ON (t2.feedback_id = sherlock_feedbacks.id)")
	query = query.Select("sherlock_feedbacks.*, t2.frequency AS frequency, t2.is_urgent as is_urgent, t2.is_highlighted_before as is_highlighted_before")
	// passing params:  page token and filters
	// filterSherlockFeedbackTable involve indexes: sherlock_feedbacks_created_at_agent_email_index, sherlock_feedbacks_created_at_category_index
	// sherlock_feedbacks_created_at_identifier_index
	query = filterSherlockFeedbackTable(pageToken, filters).ApplyInGorm(query)
	// passing params: filters
	// filterSherlockFeedbackMetaDataMappingTable involve index: sherlock_feedback_meta_data_mappings_frequency_index, sherlock_feedback_meta_data_mappings_is_highlighted_before_index
	// sherlock_feedback_meta_data_mappings_is_urgent_index
	query = filterSherlockFeedbackMetaDataMappingTable(filters).ApplyInGorm(query)
	res := query.Limit(pageLimit + 1).Find(&sherlockFeedbackModelList)
	if res.Error != nil {
		// return record not found error if feedback list is empty
		return nil, nil, errors.Wrap(res.Error, "error while fetching sherlock feedback list from db")
	}

	// if the page token is of reverse type, then fix the order of entries as paginated query fetches entries in a reverse order for applying offset.
	if pageToken != nil && pageToken.IsReverse {
		sort.Slice(sherlockFeedbackModelList, func(i, j int) bool {
			return sherlockFeedbackModelList[i].CreatedAt.After(sherlockFeedbackModelList[j].CreatedAt)
		})
	}

	// return record not found error if feedback list is empty
	if len(sherlockFeedbackModelList) == 0 {
		return nil, nil, epifierrors.ErrRecordNotFound
	}
	rows, pageCtxResp, err := pagination.NewPageCtxResp(pageToken, pageLimit, model.SherlockFeedbackDetailsList(sherlockFeedbackModelList))
	if err != nil {
		return nil, nil, errors.Wrap(err, "failing in getting the page context response")
	}
	sherlockFeedbackModelList = rows.(model.SherlockFeedbackDetailsList)
	return model.ConvertToProtoList(sherlockFeedbackModelList), pageCtxResp, nil
}

func filterSherlockFeedbackTable(pageToken *pagination.PageToken, filters *sbPb.SherlockFeedbackFilters) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(query *gormV2.DB) *gormV2.DB {
		query = query.Where("sherlock_feedbacks.created_at >= ? and sherlock_feedbacks.created_at <= ?", filters.GetFromDate().AsTime(), filters.GetToDate().AsTime())
		if filters.GetFeedbackCategory() != sbPb.FeedbackCategory_FEEDBACK_CATEGORY_UNSPECIFIED {
			query = query.Where("sherlock_feedbacks.feedback_category = ?", filters.GetFeedbackCategory())
		}
		if filters.GetAgentEmail() != "" {
			query = query.Where("sherlock_feedbacks.agent_email = ?", filters.GetAgentEmail())
		}
		if filters.GetFeedbackIdentifierValue() != "" && filters.GetFeedbackIdentifierType() != sbPb.FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_UNSPECIFIED {
			query = query.Where("sherlock_feedbacks.feedback_identifier_value = ? and sherlock_feedbacks.feedback_identifier_type = ?", filters.GetFeedbackIdentifierValue(), filters.GetFeedbackIdentifierType())
		}
		// filter values by pagination
		if pageToken != nil {
			if pageToken.Timestamp != nil {
				if pageToken.IsReverse {
					query = query.Where("sherlock_feedbacks.created_at >= ?", pageToken.Timestamp.AsTime()).Order("sherlock_feedbacks.created_at ASC")
				} else {
					query = query.Where("sherlock_feedbacks.created_at <= ?", pageToken.Timestamp.AsTime()).Order("sherlock_feedbacks.created_at DESC")
				}
				query = query.Offset(int(pageToken.Offset))
			} else {
				query = query.Order("sherlock_feedbacks.created_at DESC")
			}
		} else {
			query = query.Order("sherlock_feedbacks.created_at DESC")
		}
		return query
	})
}

func filterSherlockFeedbackMetaDataMappingTable(filters *sbPb.SherlockFeedbackFilters) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(query *gormV2.DB) *gormV2.DB {
		if filters.GetFeedbackMetaData().GetFrequency() != sbPb.FeedbackFrequency_FEEDBACK_FREQUENCY_UNSPECIFIED {
			query = query.Where("frequency = ?", filters.GetFeedbackMetaData().GetFrequency())
		}
		if filters.GetFeedbackMetaData().GetIsUrgent() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
			query = query.Where("is_urgent = ?", filters.GetFeedbackMetaData().GetIsUrgent())
		}
		if filters.GetFeedbackMetaData().GetIsHighlightedBefore() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
			query = query.Where("is_highlighted_before = ?", filters.GetFeedbackMetaData().GetIsHighlightedBefore())
		}
		return query
	})
}
