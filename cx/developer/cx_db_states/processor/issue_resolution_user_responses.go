package processor

import (
	"context"

	"github.com/epifi/gamma/api/cx/developer"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	irfPb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	"github.com/epifi/gamma/cx/issue_resolution_feedback/dao"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
)

type DevIssueResolutionUserResponses struct {
	userResponseDao dao.IIssueResolutionUserResponseLogDao
}

func NewDevIssueResolutionUserResponses(userResponseDao dao.IIssueResolutionUserResponseLogDao) *DevIssueResolutionUserResponses {
	return &DevIssueResolutionUserResponses{
		userResponseDao: userResponseDao,
	}
}

const (
	IssueResolutionFeedbackId = "issue_resolution_feedback_id"
)

//nolint:funlen
func (d *DevIssueResolutionUserResponses) FetchParamList(ctx context.Context, entity developer.CXEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            IssueResolutionFeedbackId,
			Label:           "Client Request ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (d *DevIssueResolutionUserResponses) FetchData(ctx context.Context, entity developer.CXEntity, filters []*db_state.Filter) (string, error) {
	var (
		issueResFeedbackId string
		userResponseList   []*irfPb.IssueResolutionUserResponseLog
	)

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case IssueResolutionFeedbackId:
			issueResFeedbackId = filter.GetStringValue()
		default:
		}
	}

	// for the above feedback Id, fetch user responses from issue_resolution_user_response_logs
	userResponse, respErr := d.userResponseDao.GetLatestFeedbackByIssueResolutionFeedbackId(ctx, issueResFeedbackId)
	if respErr != nil {
		logger.Error(ctx, "error while fetching issue resolution user response logs from db", zap.Error(respErr))
		return errors.Wrap(respErr, "error while fetching issue resolution user response logs from db").Error(), nil
	}
	userResponseList = append(userResponseList, userResponse)

	// format response
	resp := &developer.DevIssueResolutionUserResponsesResponse{
		UserResponses: userResponseList,
	}
	mar := protojson.MarshalOptions{UseEnumNumbers: false, EmitUnpopulated: true, Multiline: true}
	jsonResp, err := mar.Marshal(resp)
	if err != nil {
		return "", err
	}
	return string(jsonResp), nil
}
