Flags:
  TrimDebugMessageFromStatus: true
  PublishExecutionUpdate: true
  PublishDepositNotification: false
  PublishAggrDepositNotification: false
  PublishDepositSMS: false
  PublishAggrDepositSMS: false
  EnableDepositRateLimiter: true

InsufficientBalanceNotificationParams:
    Title: "🔴 Alert: Low funds | FIT rules paused"
    Body: "Your account balance is too low. Add funds to your Fi account now and resume your rule"
    NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"

InactiveSmartDepositNotificationParams:
  Title: "Your FIT rule has paused ⏸"
  Body: "Please select a new Smart Deposit and resume your FIT rule. Your old Smart Deposit isn't active anymore."
  NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"

DepositPushNotificationParams:
  Title: "Look at how much you've saved ⬇️"
  Body: "You have saved %s in %s with your '%s' FIT rule"
  NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"

MaxPageSize: 50

SchedulerService:
  MaxJobs: 50
  BatchSize: 10
  TriggerJobsDeadlineInSeconds: 240 # 4 minutes. setting a value lesser than dkron job interval which runs every 5 minutes

SchedulerDkronIntervalInSeconds: 300 # 5 minutes

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/fittt/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1


RateLimiterConfig:
  ResourceMap:
    push_notifications:
      Rate: 500
      Period: 1m
    sms:
      Rate: 500
      Period: 1m
    add_funds_to_sd:
      Rate: 1
      Period: 1s
  Namespace: "fittt"

SMSAggregationCutOffTime:
  Hours: 17 # 05:00 PM

DepositRateLimitRetryJitterInterval: "5m"
