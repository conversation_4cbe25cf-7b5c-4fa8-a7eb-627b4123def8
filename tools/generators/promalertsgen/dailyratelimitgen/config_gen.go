package dailyratelimitgen

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"

	"github.com/epifi/be-common/pkg/data_structs/orderedromap"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/tools/generators/promalertsgen/config"
)

// ServerRateLimitAlertConfig is the struct representing the input config
type ServerRateLimitAlertConfig struct {
	// GroupName is the prometheus group name
	GroupName string `yaml:"GroupName"`

	// ServerName is the name of the server in which the RPC is present, eg. vendorgateway, fittt, etc.
	ServerName string `yaml:"ServerName"`

	// RPCAlerts is the list of alerts for various different RPCs.
	RPCAlerts []*RPCAlertParams `yaml:"RPCAlerts"`
}

type RPCAlertParams struct {
	// RPC is the rpc for which the alert is to be made.
	RPC string `yaml:"RPC"`

	// Rate is the number of requests that can be made to a given API on a daily basis, without exceeding the rate-limit.
	Rate int `yaml:"Rate"`

	// Set GRPCService to the fully-qualified name of the GRPC service to which this RPC belongs to,
	// when you have multiple different RPCs within the same server.
	GRPCService string `yaml:"GRPCService"`

	// Team is the particular team to which the alerts need to be sent.
	Team string `yaml:"Team"`

	// AlertRules lists the different time intervals at which the alert will be triggerred.
	AlertRules []*AlertRuleParams `yaml:"AlertRules"`
}

type AlertRuleParams struct {
	// Interval is the time duration starting from 00:00 IST, at which the alert expression will be evaluated.
	Interval time.Duration `yaml:"Interval"`

	// ThresholdToSeverityMap is a map to store the mapping between the different thresholds and the alert-levels.
	ThresholdToSeverityMap map[float64]string `yaml:"ThresholdToSeverityMap"`
}

// loadInputConfig reads and unmarshal the input config
func loadInputConfig(inputConfigPath string) *ServerRateLimitAlertConfig {
	file, err := os.ReadFile(filepath.Clean(inputConfigPath))
	if err != nil {
		log.Fatal("could not read input config file", err)
	}
	cfg := &ServerRateLimitAlertConfig{}
	err = yaml.Unmarshal(file, cfg)
	if err != nil {
		log.Fatal("could not load input config. please fix the issues", err)
	}
	return cfg
}

var (
	duration12h, _ = time.ParseDuration("12h")
	duration15h, _ = time.ParseDuration("15h")
	duration18h, _ = time.ParseDuration("18h")
	duration21h, _ = time.ParseDuration("21h")
	duration23h, _ = time.ParseDuration("23h")
)

var defaultAlertRuleParams = []*AlertRuleParams{
	{
		Interval: duration12h,
		ThresholdToSeverityMap: map[float64]string{
			60: "p2",
			70: "p1",
			80: "p0",
		},
	},
	{
		Interval: duration15h,
		ThresholdToSeverityMap: map[float64]string{
			70: "p2",
			80: "p1",
			90: "p0",
		},
	},
	{
		Interval: duration18h,
		ThresholdToSeverityMap: map[float64]string{
			80: "p2",
			85: "p1",
			90: "p0",
		},
	},
	{
		Interval: duration21h,
		ThresholdToSeverityMap: map[float64]string{
			92: "p1",
			95: "p0",
		},
	},
	{
		Interval: duration23h,
		ThresholdToSeverityMap: map[float64]string{
			95: "p1",
			98: "p0",
		},
	},
}

// GenerateRatelimitAlertConfig creates a prometheus alertmanager config with various alert rules set for different
// times of day with different severity for different usage levels.
func GenerateRatelimitAlertConfig(inputConfigPath string) *config.Config {
	inputCfg := loadInputConfig(inputConfigPath)
	return createOutputCfg(inputCfg)
}

// createOutputCfg maps the given input config to prometheus output config.
func createOutputCfg(cfg *ServerRateLimitAlertConfig) *config.Config {
	outputCfg := &config.Config{
		Groups: []*config.Group{
			{
				Name:  cfg.GroupName,
				Rules: make([]*config.Rule, 0),
			},
		},
	}

	for _, apiAlert := range cfg.RPCAlerts {
		outputCfg.Groups[0].Rules = append(outputCfg.Groups[0].Rules, getAlertRulesForRPC(cfg.ServerName, apiAlert)...)
	}

	return outputCfg
}

// getAlertRulesForRPC generates the prometheus alert rules for each of the given RPCs in the input config.
func getAlertRulesForRPC(serverName string, params *RPCAlertParams) []*config.Rule {
	rules := make([]*config.Rule, 0)

	rpc := params.RPC
	grpcService := params.GRPCService

	midnightTimestamp := time.Date(2023, 6, 10, 0, 0, 0, 0, datetime.IST)
	duration, _ := time.ParseDuration("30m")

	if len(params.AlertRules) == 0 {
		params.AlertRules = defaultAlertRuleParams
	}

	for _, p := range params.AlertRules {
		givenTime := midnightTimestamp.Add(p.Interval).Truncate(duration) // Make sure that the passed time is a multiple of 30m
		givenDurationStr := getPromQlTimeRange(midnightTimestamp, givenTime)

		alertLevelsMap := orderedromap.New[float64, string](p.ThresholdToSeverityMap)
		for alertLevelsMap.HasNext() {
			percentage, severityLevel, _ := alertLevelsMap.Next()
			rule := &config.Rule{
				Alert: fmt.Sprintf("%s-Breached-%.2f-Percent-At-%02d%02d", rpc, percentage, givenTime.Hour(), givenTime.Minute()),
				Expr:  getPromQlExpression(serverName, grpcService, rpc, givenDurationStr, givenTime, params.Rate, percentage),
				For:   "5m",
				Labels: &config.Label{
					Severity: severityLevel,
					Team:     params.Team,
				},
				Annotations: &config.Annotation{
					Summary:     fmt.Sprintf(`vendorgateway API {{ $labels.grpc_method }} has consumed %.2f%% of its daily quota`, percentage),
					Description: fmt.Sprintf(`vendorgateway API {{ $labels.grpc_method }} has consumed %.2f%% of its daily quota`, percentage),
				},
			}

			rules = append(rules, rule)
		}
	}
	return rules
}

func getPromQlTimeRange(midnightTimestamp, givenTime time.Time) string {
	givenDuration := givenTime.Sub(midnightTimestamp)

	var timeDurationStr string
	// If the given time is a proper hour
	if int(givenDuration.Minutes())%60 == 0 {
		timeDurationStr = fmt.Sprintf("%dh", givenTime.Hour())
	} else {
		timeDurationStr = fmt.Sprintf("%dh%dm", givenTime.Hour(), givenTime.Minute())
	}
	return timeDurationStr
}

func getPromQlExpression(serverName, grpcServiceName, rpcName, rangeDurationStr string, givenTime time.Time, rate int, percentage float64) string {
	// Create the expression using grpc_service label, if provided.
	if grpcServiceName != "" {
		return fmt.Sprintf(
			`(sum(increase(grpc_server_started_total{service="%s",grpc_service="%s",grpc_method="%s"}[%s])) by (environment) and ON() hour() == %v and ON() minute() >= %v < %v) * 100 / %v >= %v`,
			serverName,
			grpcServiceName,
			rpcName,
			rangeDurationStr,
			givenTime.UTC().Hour(),
			givenTime.UTC().Minute(),
			(givenTime.UTC().Minute()+10)%60,
			rate,
			percentage,
		)
	}
	return fmt.Sprintf(
		`(sum(increase(grpc_server_started_total{service="%s",grpc_method="%s"}[%s])) by (environment) and ON() hour() == %v and ON() minute() >= %v < %v) * 100 / %v >= %v`,
		serverName,
		rpcName,
		rangeDurationStr,
		givenTime.UTC().Hour(),
		givenTime.UTC().Minute(),
		(givenTime.UTC().Minute()+10)%60,
		rate,
		percentage,
	)
}
