package s3buckets

import (
	"fmt"
	"go/types"
	"regexp"
	"strings"

	"github.com/knadh/koanf"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	servergenConf "github.com/epifi/be-common/tools/servergen/config"
	"github.com/epifi/be-common/tools/servergen/iampolicygen/config"
	"github.com/epifi/be-common/tools/servergen/iampolicygen/config/filters"
	"github.com/epifi/be-common/tools/servergen/internal"
	servergenError "github.com/epifi/be-common/tools/servergen/internal/errors"
)

const s3PrefixFieldTag = "s3_prefix_field"
const s3PrefixFieldPattern = s3PrefixFieldTag + `:".*?"`

var s3PrefixFieldRe = regexp.MustCompile(s3PrefixFieldPattern)

// commonS3BucketLoader serves as the struct for implementing all functionality related to loading of S3
// configs. This is embedded in the different resource loaders requiring S3 where they utilize these functions.
type commonS3BucketLoader struct {
	*filters.S3BucketsFilter
	configLoader           *config.Loader
	iamPolicyAttributeName string
	iamPolicyTagName       string
	s3PrefixDelimiter      string
}

func (s *commonS3BucketLoader) getBucketNamesWithS3Prefixes(k *koanf.Koanf, configAst *types.Struct) ([]string, error) {
	// Currently we only support s3_prefix_field tag to be used only with string fields. So we process the
	// bucket names in string config fields and other config fields (eg, map, slices, etc.) separately.

	// First process the non-string bucket fields
	nonStringBucketPaths := s.configLoader.GetConfigPathsForMatchingFields(k, configAst, func(field *types.Var, fieldTag string) bool {
		return strings.Contains(fieldTag, s.iamPolicyTagName) && field.Type().String() != "string"
	}, nil)
	nonStringBucketConfigValues := s.configLoader.GetConfigValuesForFields(k, nonStringBucketPaths)

	// Then process the string bucket fields and fetch the bucket names along with their s3 prefix

	// Stores the paths of the Config fields which are used as s3_prefix_field
	s3PrefixFieldPaths := make([]string, 0)

	stringConfigPaths := s.configLoader.GetConfigPathsForMatchingFields(k, configAst, func(field *types.Var, fieldTag string) bool {
		return strings.Contains(fieldTag, s.iamPolicyTagName) && field.Type().String() == "string"
	}, func(field *types.Var, flattenedFieldPath string, fieldTag string) {
		// Perform additional steps to keep track of S3 IAM fields containing s3PrefixFieldTag
		if strings.Contains(fieldTag, s3PrefixFieldTag) {
			s3PrefixFieldText := s3PrefixFieldRe.FindString(fieldTag)
			// Trim s3_prefix_field:"<field_name>" part to get the field name
			s3PrefixFieldText = s3PrefixFieldText[len(s3PrefixFieldTag)+2 : len(s3PrefixFieldText)-1]
			s3PrefixFieldPaths = append(s3PrefixFieldPaths, s3PrefixFieldText)
		} else {
			// Add empty values to align each element of s3PrefixFieldPaths to stringConfigPaths
			s3PrefixFieldPaths = append(s3PrefixFieldPaths, "")
		}
	})

	prefixConfigPaths := lo.Map(stringConfigPaths, func(path string, index int) string {
		if s3PrefixFieldPaths[index] == "" {
			return ""
		}
		pathSegments := strings.Split(path, s.configLoader.Delimiter)
		pathSegments[len(pathSegments)-1] = s3PrefixFieldPaths[index]
		return strings.Join(pathSegments, s.configLoader.Delimiter)
	})

	bucketConfigValues := s.configLoader.GetConfigValuesForFields(k, stringConfigPaths)

	for i, val := range bucketConfigValues {
		if prefixConfigPaths[i] == "" {
			continue
		}
		prefixPath := k.Get(prefixConfigPaths[i])
		if prefixPath == nil {
			continue
		}
		prefixVal, ok := prefixPath.(string)
		if !ok {
			return nil, fmt.Errorf("WARN: S3 Prefix path %s is not a string type", prefixConfigPaths[i])
		}
		bucketConfigValues[i] = strings.Join([]string{val, prefixVal}, s.s3PrefixDelimiter)
	}

	// Now merge the bucket names fetched from string and non-string config fields
	bucketConfigValues = append(bucketConfigValues, nonStringBucketConfigValues...)
	return bucketConfigValues, nil
}

func (s *commonS3BucketLoader) getBucketNamesForServiceGroup(env string, serviceGroup *servergenConf.ServiceGroup) ([]string, error) {
	k, err := s.configLoader.GetConfigValuesForServiceGroup(env, serviceGroup)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("could not load config values for service group %s", serviceGroup.ServiceGroup))
	}

	configAst, err := s.configLoader.GetConfigTypeInfoAst(serviceGroup.GetConfigPkg())
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("could not parse the config struct for service group %s", serviceGroup.ServiceGroup))
	}

	return s.getBucketNamesWithS3Prefixes(k, configAst)
}

func (s *commonS3BucketLoader) getBucketNamesForServiceGroupsInServer(env, serverName string) ([]string, error) {
	serviceGroups, err := s.configLoader.GetServiceGroupsForServer(env, serverName)
	if err != nil {
		return nil, errors.Wrap(err, "could not load service groups present in server")
	}

	resources := make([]string, 0)

	for _, sg := range serviceGroups {
		namesFromSg, err := s.getBucketNamesForServiceGroup(env, sg)
		if err != nil {
			fmt.Println("WARN: could not fetch the bucket name from service group config. ", err)
			continue
		}
		resources = append(resources, namesFromSg...)
	}

	return resources, nil
}

func (s *commonS3BucketLoader) getBucketNamesForServer(env, serverName string) ([]string, error) {
	k, err := s.configLoader.LoadServerConfigValues(env, serverName)
	if err != nil {
		return nil, err
	}

	configAst, err := s.configLoader.GetConfigTypeInfoAst(config.CmdConfigPkgPath)
	if err != nil {
		return nil, errors.Wrap(err, "could not load the AST containing type info for server config struct")
	}

	return s.getBucketNamesWithS3Prefixes(k, configAst)
}

func (s *commonS3BucketLoader) getBucketNamesForServerAndServiceGroup(env, serverName string) ([]string, error) {
	serviceGroupBucketNames, err := s.getBucketNamesForServiceGroupsInServer(env, serverName)
	if err != nil {
		return nil, err
	}

	serverBucketNames, err := s.getBucketNamesForServer(env, serverName)
	if err != nil {
		return nil, err
	}

	finalResult := internal.MergeArrays(serverBucketNames, serviceGroupBucketNames)
	return finalResult, nil
}

// getS3BucketNamesFromS3Uri takes an array containing S3 bucket names and S3 URIs and maps each element as follows:
// the S3 bucket names are returned unchanged, Bucket name is returned for each of the S3 URI.
func (s *commonS3BucketLoader) getS3BucketNamesFromS3Uri(buckets []string) []string {
	const s3UriPrefix string = "s3://"

	for i, bucket := range buckets {
		if !strings.HasPrefix(bucket, s3UriPrefix) {
			continue
		}

		bucketName := strings.Split(strings.TrimPrefix(bucket, s3UriPrefix), s.s3PrefixDelimiter)
		buckets[i] = bucketName[0]
	}
	return buckets
}

func (s *commonS3BucketLoader) GetAllConfigValuesForEnvs(envs []string, serverName string) (map[string][]string, []error) {
	envToSecretsMap := make(map[string][]string, len(envs))

	errorsFound := make([]error, 0)
	for _, env := range envs {
		buckets, err := s.getBucketNamesForServerAndServiceGroup(env, serverName)
		if err != nil {
			errorsFound = append(errorsFound, errors.Wrap(err, fmt.Sprintf("unable to fetch resources for env %s", env)))
			buckets = nil
		}
		envToSecretsMap[env] = s.getS3BucketNamesFromS3Uri(buckets)
	}

	return envToSecretsMap, errorsFound
}
func (s *commonS3BucketLoader) GetResourceNamesForServiceGroup(env string, serverName string) (map[string][]string, error) {
	serviceGroups, err := s.configLoader.GetServiceGroupsForServer(env, serverName)
	if err != nil {
		if errors.Is(err, servergenError.ServerNotPresentInEnv) {
			return nil, servergenError.ServerNotPresentInEnv
		}
		return nil, errors.Wrap(err, "could not load service groups present in server")
	}

	sgToResourceMap := make(map[string][]string, 0)
	for _, sg := range serviceGroups {
		namesFromSg, err := s.getBucketNamesForServiceGroup(env, sg)
		if err != nil {
			fmt.Println("WARN: could not fetch the bucket name from service group config. ", err)
			continue
		}
		namesFromSg = s.getS3BucketNamesFromS3Uri(namesFromSg)
		sgToResourceMap[sg.ServiceGroup] = namesFromSg
	}

	return sgToResourceMap, nil
}
func (s *commonS3BucketLoader) GetResourceNamesForServer(env string, serverName string) (map[string][]string, error) {
	resourceMap := make(map[string][]string, 0)
	s3bucket, err := s.getBucketNamesForServer(env, serverName)
	if err != nil {
		return nil, err
	}
	s3bucket = s.getS3BucketNamesFromS3Uri(s3bucket)
	resourceMap[serverName] = s3bucket
	return resourceMap, nil
}
