//go:generate conf_gen github.com/epifi/be-common/tools/servergen/testdata/test-2/config Config
package config

import (
	"time"

	"github.com/epifi/be-common/pkg/cfg"
)

type Config struct {
	ShippingAddressUpdatePublisher     *cfg.SqsPublisher
	ShippingAddressUpdateSubscriber    *cfg.SqsSubscriber `dynamic:"true"`
	VKYC                               *VKYC              `dynamic:"true"`
	CreditReportPresenceSubscriber     *cfg.SqsSubscriber `dynamic:"true"`
	CreditReportVerificationSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	AuthDataPublisher                  *cfg.SqsPublisher
	AuthDataCustomDelayPublisher       *cfg.SqsCustomDelayQueuePublisher
}

type VKYC struct {
	// landing page skip option timer in seconds
	LandingPageSkipOptionTime   int32         `dynamic:"true"`
	EnableDemandManagement      bool          `dynamic:"true"`
	ShowAuditorAcceptedTileTime time.Duration `dynamic:"true"`
}
