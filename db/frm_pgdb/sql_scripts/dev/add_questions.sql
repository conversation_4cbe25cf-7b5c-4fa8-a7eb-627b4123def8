-- https://docs.google.com/spreadsheets/d/1QMKlA3dqh1ojWG-ZLj3dJWWkhT_KJAT80HbL8Fuoib0/edit#gid=**********

INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('ACCOUNT_PURPOSE', 'What was your purpose of opening a Federal Bank Savings Account through Fi?', null, 'Eg: Salary a/c, Expense management, Smart savings, etc.', null, 'QUESTION_TYPE_TEXT', '{"textOptions": {"maxCharsLimit": 30}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('ACCOUNT_USER', 'Who is using this account?', null, null, null, 'QUESTION_TYPE_MULTI_CHOICE', '{"multiChoice": {"choices": ["Self", "Friend", "Family Member", "Other"], "choiceConditionalQuestionsMap": {"Self": {}, "Other": {}, "Friend": {}, "Family Member": {}}}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('ANNUAL_INCOME_RANGE', 'Confirm your annual income range', null, 'Eg: ₹1L - ₹2L', null, 'QUESTION_TYPE_MULTI_CHOICE', '{"multiChoice": {"choices": ["0-₹1L", "₹1L-₹5L", "₹5L-₹10L", "₹10L-₹25L", "₹25L-₹50L", "₹50L-₹1Cr", ">1Cr"], "choiceConditionalQuestionsMap": {">1Cr": {}, "0-₹1L": {}, "₹1L-₹5L": {}, "₹5L-₹10L": {}, "₹10L-₹25L": {}, "₹25L-₹50L": {}, "₹50L-₹1Cr": {}}}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('COMMENT', 'Comments (if any)', null, null, null, 'QUESTION_TYPE_TEXT', '{"textOptions": {"maxCharsLimit": 200}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('EMPLOYER_NAME', 'What is your employer''s name?', null, 'You can find this information in your salary slip', null, 'QUESTION_TYPE_TEXT', '{"textOptions": {"maxCharsLimit": 50}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('EMPLOYMENT_TYPE', 'Confirm your employment type', null, 'Eg: Salaried, Business, Student, etc.', null, 'QUESTION_TYPE_MULTI_CHOICE', '{"multiChoice": {"choices": ["Salaried", "Business Owner", "Retired", "Student", "Home Owner", "Other"], "choiceConditionalQuestionsMap": {"Other": {}, "Retired": {}, "Student": {}, "Salaried": {"questions": [{"isMandatory": true, "questionCode": "EMPLOYER_NAME"}, {"isMandatory": true, "questionCode": "SALARY_SLIP"}]}, "Home Owner": {}, "Business Owner": {}}}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('HIGH_CREDIT_AMOUNT_OR_COUNT_PROOF_DOC', 'Share any document/proof to support the above event', null, null, null, 'QUESTION_TYPE_FILE', '{"fileOptions": {"allowedContentTypes": ["FILE_CONTENT_TYPE_PDF"]}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('HIGH_CREDIT_AMOUNT_PROOF_DOC_ASSET_LIQUIDATION', 'Share any document/proof to support the above event', null, 'Proof of sale of asset. eg: mutual funds withdrawal, sale of property', null, 'QUESTION_TYPE_FILE', '{"fileOptions": {"allowedContentTypes": ["FILE_CONTENT_TYPE_PDF"]}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('HIGH_CREDIT_AMOUNT_PROOF_DOC_BUSINESS_TRANSFER', 'Share any document/proof to support the above event', null, 'ITR4 or business registration document', null, 'QUESTION_TYPE_FILE', '{"fileOptions": {"allowedContentTypes": ["FILE_CONTENT_TYPE_PDF"]}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('HIGH_CREDIT_AMOUNT_PROOF_DOC_LOAN', 'Share any document/proof to support the above event', null, 'Proof of loan document or screenshot of the app from which you acquired the loan', null, 'QUESTION_TYPE_FILE', '{"fileOptions": {"allowedContentTypes": ["FILE_CONTENT_TYPE_PDF"]}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('HIGH_CREDIT_AMOUNT_PROOF_DOC_PERSONAL_EVENT', 'Share any document/proof to support the above event', null, 'Marriage wedding card,any other proof for personal event', null, 'QUESTION_TYPE_FILE', '{"fileOptions": {"allowedContentTypes": ["FILE_CONTENT_TYPE_PDF"]}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('HIGH_CREDIT_AMOUNT_REASON', 'You received a significantly high amount of money in recent months. State a reason why.', null, 'Share the purpose and source of your funds', null, 'QUESTION_TYPE_MULTI_CHOICE', '{"multiChoice": {"choices": ["Personal Event (marriage, etc)", "Asset Liquidation", "Money Transfers From Family", "Loan or Borrowings", "Business Transfer", "Salary", "Other"], "choiceConditionalQuestionsMap": {"Other": {"questions": [{"isMandatory": true, "questionCode": "HIGH_CREDIT_AMOUNT_REASON_TEXT"}, {"isMandatory": true, "questionCode": "HIGH_CREDIT_AMOUNT_OR_COUNT_PROOF_DOC"}]}, "Salary": {}, "Asset Liquidation": {"questions": [{"isMandatory": true, "questionCode": "HIGH_CREDIT_AMOUNT_PROOF_DOC_ASSET_LIQUIDATION"}]}, "Business Transfer": {"questions": [{"isMandatory": true, "questionCode": "HIGH_CREDIT_AMOUNT_PROOF_DOC_BUSINESS_TRANSFER"}]}, "Loan or Borrowings": {"questions": [{"isMandatory": true, "questionCode": "HIGH_CREDIT_AMOUNT_PROOF_DOC_LOAN"}]}, "Money Transfers From Family": {"questions": [{"isMandatory": true, "questionCode": "HIGH_CREDIT_AMOUNT_OR_COUNT_PROOF_DOC"}]}, "Personal Event (marriage, etc)": {"questions": [{"isMandatory": true, "questionCode": "HIGH_CREDIT_AMOUNT_PROOF_DOC_PERSONAL_EVENT"}]}}}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('HIGH_CREDIT_AMOUNT_REASON_TEXT', 'Tell us why you received such a high amount of money in the recent months', null, null, null, 'QUESTION_TYPE_TEXT', '{"textOptions": {"maxCharsLimit": 200}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('HIGH_CREDIT_COUNT_AND_AMOUNT_REASON_TEXT', 'You received a significantly high amount of money in recent months. Also, the number of incoming transactions was high. State a reason why.', null, 'Share the purpose and source of your funds', null, 'QUESTION_TYPE_TEXT', '{"textOptions": {"maxCharsLimit": 200}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('HIGH_CREDIT_COUNT_REASON_TEXT', 'You received a significantly high number of transactions in recent months. State a reason why.', null, 'Share the purpose and source of your funds', null, 'QUESTION_TYPE_TEXT', '{"textOptions": {"maxCharsLimit": 200}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('ITR_DOC', 'Upload your Income Tax Returns (ITR) document', null, 'This should be received from the IT department. Supported format: PDF', null, 'QUESTION_TYPE_FILE', '{"fileOptions": {"allowedContentTypes": ["FILE_CONTENT_TYPE_PDF"]}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('SALARY_SLIP', 'Upload your latest salary slip', null, 'This should have your name and the employer''s name. It should be from the past 3 months. Supported format: PDF', null, 'QUESTION_TYPE_FILE', '{"fileOptions": {"allowedContentTypes": ["FILE_CONTENT_TYPE_PDF"]}}', 1);
INSERT INTO questions (code, text, description, tip, placeholder, type, options, version) VALUES ('STOP_BUSINESS_ACTIVITY_ACK', 'Please acknowledge that you will not be using the Federal savings account linked to Fi for business transactions.', null, null, null, 'QUESTION_TYPE_MULTI_CHOICE', '{"multiChoice": {"choices": ["Agree", "No, Please close my federal savings account linked with FI"], "choiceConditionalQuestionsMap": {"Agree": {}, "No, Please close my federal savings account linked with FI": {}}}}', 1);
