CREATE TABLE IF NOT EXISTS digilocker_users
(
	id              UUID                     DEFAULT uuid_generate_v4() PRIMARY KEY,
	user_identifier VARCHAR NOT NULL,
	flow            VARCHAR NOT NULL,
	profile         JSONB   NOT NULL,
	created_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
	updated_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
	deleted_at      TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS digilocker_uid_flow_unq_idx ON digilocker_users (user_identifier, flow);

COMMENT ON TABLE digilocker_users IS 'Stores the user details fetched from digilocker vendor.';

COMMENT ON COLUMN digilocker_users.id IS 'Primary key, unique identifier for each user entry.';
COMMENT ON COLUMN digilocker_users.user_identifier IS 'User identifier for the flow from the source system.';
COMMENT ON COLUMN digilocker_users.flow IS 'Flow through which documents are fetched and stored.';
COMMENT ON COLUMN digilocker_users.profile IS 'JSONB object storing user profile (name, dob, gender, etc.) fetched from DigiLock<PERSON>.';
COMMENT ON COLUMN digilocker_users.created_at IS 'Timestamp of when the user entry was created.';
COMMENT ON COLUMN digilocker_users.updated_at IS 'Timestamp of when the user entry was last updated.';
COMMENT ON COLUMN digilocker_users.deleted_at IS 'Timestamp of when the user entry was soft deleted (NULL if not deleted).';


