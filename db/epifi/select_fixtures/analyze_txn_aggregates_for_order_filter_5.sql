explain
analyze (distsql )
SELECT sum(t.computed_amount) as Sum<PERSON>mount, count(*) as Count FROM transactions t  INNER JOIN orders o ON t.order_ref_id = o.id  WHERE  (t.pi_from in ('PI220221nXYgcMyRRHCuqetU/ob+hA==','PI220221EMYEtU6QSlCga/tuhsEMaQ==','PI2202218rbnOalNRXOhzn06xVH/rA==') OR t.pi_to in ('PI220221nXYgcMyRRHCuqetU/ob+hA==','PI220221EMYEtU6QSlCga/tuhsEMaQ==','PI2202218rbnOalNRXOhzn06xVH/rA=='))  AND (COALESCE(t.debited_at, t.credited_at, t.created_at) >= '2022-08-22 20:08:38.894' ::TIMESTAMPTZ AND COALESCE(t.debited_at, t.credited_at, t.created_at) <= '2022-12-01 12:58:38.894' ::TIMESTAMPTZ)  AND o.provenance IN ('USER_APP','ATM','ECOMM');
