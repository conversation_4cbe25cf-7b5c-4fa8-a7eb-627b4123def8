-- temporary fix to unblock user stuck on customer creation due to INVALID_DEVICE_TOKEN
/*
Keep track of actor Ids in this sheet, remove them once device registration is completed and revert changes are done:
ACNvw6J7xERQOaBI1D5rVBNQ230616==
ACG661/6cfTWesSlhKX3C3Cw230418==
ACCRzSIHZ3QHisxrP4R6gbxg230807==
AC230203rkV1caRTQYGpaDuiXrdRiA==
ACeIT4jNgbTcGuuj85s+fnrA230715==
ACrvOY5e9wRAKkkhbmp3pUhQ230509==
ACkpBw8cDNQ/6QXHif7KcI7Q230727==
*/
update device_registrations
set actor_id = actor_id || '.temp'
where actor_id IN (
        'ACNvw6J7xERQOaBI1D5rVBNQ230616==',
        'ACG661/6cfTWesSlhKX3C3Cw230418==',
        'ACCRzSIHZ3QHisxrP4R6gbxg230807==',
        'AC230203rkV1caRTQYGpaDuiXrdRiA==',
        'ACeIT4jNgbTcGuuj85s+fnrA230715==',
        'ACrvOY5e9wRAKkkhbmp3pUhQ230509==',
        'ACkpBw8cDNQ/6QXHif7KcI7Q230727=='
    );
