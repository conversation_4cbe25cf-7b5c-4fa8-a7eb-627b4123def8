CREATE TABLE public.do_once_tasks (
  task_name STRING NOT NULL,
  deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
  CONSTRAINT do_once_tasks_pkey PRIMARY KEY (task_name ASC, deleted_at_unix ASC),
  INDEX do_once_tasks_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.do_once_tasks_user (
       user_id UUID NOT NULL DEFAULT gen_random_uuid(),
       created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
       updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
       deleted_at TIMESTAMPTZ NULL,
       CONSTRAINT "primary" PRIMARY KEY (user_id ASC),
       UNIQUE INDEX do_once_tasks_users_user_id_key (user_id ASC)
);