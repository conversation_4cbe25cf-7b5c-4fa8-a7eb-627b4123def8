-- few payment went to success state even after failure at partner's end. This was due to a regression bug in
-- status code mapping. Moving the status for such cases to unknown and in_payment in txn and order respectively.
-- this update will help us in forcing enquiry for the txn in prod.

-- utr: 118104681782
UPDATE transactions SET status = 'UNKNOWN' WHERE id = 'TXN210630vRWY6tkJQSGtNYeejVqQUw==' AND status = 'SUCCESS';
UPDATE orders SET status = 'IN_PAYMENT' where id = 'OD210630OLwMu8ksRJ+k8HWTMgwdDg==' AND status  = 'PAID';

-- utr: 117608160337
UPDATE transactions SET status = 'UNKNOWN' WHERE id = 'TXN2106255+3r8ez2Rju4tJSZJmlu0Q==' AND status = 'SUCCESS';
UPDATE orders SET status = 'IN_PAYMENT' where id = 'OD210625fN9zDaY4RdOLmUfcFUH3qQ==' AND status  = 'PAID';

-- utr: 117704503035
UPDATE transactions SET status = 'UNKNOWN' WHERE id = 'TXN2106261yS9UkibT8m+l7lu+D2exQ==' AND status = 'SUCCESS';
UPDATE orders SET status = 'IN_PAYMENT' where id = 'OD210626QZy9LYtsTsKTUq+dkjMAjQ==' AND status  = 'PAID';
