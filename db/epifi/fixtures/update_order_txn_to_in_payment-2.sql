-- following transactions was marked as failed due to queuing delay at vendor's end.
-- where in status enquiry they returned data not found resulting txn and order moving to failure
-- state where as it was actually picked by vendor scheduler post enquiry
-- utr: 130522921941
UPDATE transactions SET status = 'UNKNOWN' WHERE id = 'TXN211101Vgu8mVJMR4ypdWudNZvNjw==' AND status = 'FAILED';
UPDATE orders SET status = 'IN_PAYMENT' where id = 'OD211101InSYew5tTReInSvQZBbthA==' AND status  = 'PAYMENT_FAILED';
