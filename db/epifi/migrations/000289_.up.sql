CREATE TABLE IF NOT EXISTS card_action_attempts (
    id UUID NOT NULL DEFAULT gen_random_uuid(),
    card_id UUID NOT NULL,
    actor_id STRING NOT NULL,
    action STRING NOT NULL,
    state STRING NOT NULL,
    vendor_response_code STRING,
    vendor_response_reason STRING,
    internal_response_code STRING,
    created_at          TIMESTAMPTZ        NOT NULL DEFAULT NOW(),
    updated_at          TIMESTAMPTZ        NOT NULL DEFAULT NOW(),
    deleted_at          TIMESTAMPTZ,
    CONSTRAINT "primary" PRIMARY KEY (id ASC),
    CONSTRAINT fk_card_action_attempts_card_id FOREIGN KEY (card_id) REFERENCES cards(id),
    CONSTRAINT fk_card_action_attempts_actor_id FOREIGN KEY (actor_id) REFERENCES actors(id),
    INDEX card_action_attempts_card_id_lookup_idx (card_id) ,
    INDEX card_action_attempts_actor_id_lookup_idx (actor_id)
);

comment on table card_action_attempts is 'table to store details of each card action attempts along with vendor response code and reason';
