CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.employer_pf_history (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id character varying NOT NULL,
    company_name character varying NOT NULL,
    epf_history jsonb,
    data_source character varying NOT NULL,
    last_refreshed_at timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.employer_pf_history IS 'entity where all the Employer PF History values, fetched from different sources, are stored(<PERSON><PERSON><PERSON> is the vendor now, we might extend this for PF employer history from other vendors or other sources as well)';
COMMENT ON COLUMN public.employer_pf_history.company_id IS 'Contains the company id of the employer as in employer service DB';
COMMENT ON COLUMN public.employer_pf_history.company_name IS 'Contains the company name as in employer service DB';
COMMENT ON COLUMN public.employer_pf_history.epf_history IS 'Contains employer provident fund history of the company across months';
COMMENT ON COLUMN public.employer_pf_history.data_source IS 'Contains the source from where the data is fetched. i.e. Karza, internal or other sources';
COMMENT ON COLUMN public.employer_pf_history.last_refreshed_at IS 'Contains the timestamp when the data was last updated';
CREATE TABLE public.epf_import_session (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    epf_session_details jsonb,
    exit_deeplink jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.epf_import_session IS 'stores details for epf import session initiated by actor';
COMMENT ON COLUMN public.epf_import_session.actor_id IS 'stores actor_id who initiated the request';
COMMENT ON COLUMN public.epf_import_session.epf_session_details IS 'stores epf session details which includes details like client req ids of epf passbook request';
COMMENT ON COLUMN public.epf_import_session.exit_deeplink IS 'to store success and failure exit deeplink for epf passbook request';
CREATE TABLE public.epf_passbook_employee_details (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    uan_id character varying NOT NULL,
    uan_number character varying NOT NULL,
    user_name character varying,
    father_name character varying,
    date_of_birth date,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.epf_passbook_est_details (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    uan_id character varying NOT NULL,
    uan_number character varying NOT NULL,
    epf_passbook_employee_details_id character varying NOT NULL,
    establishment_name character varying,
    member_id character varying,
    office_code character varying,
    date_of_joining_epf date,
    date_of_enquiry_epf date,
    date_of_enquiry_employee_pension_scheme date,
    provident_fund_balance jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON COLUMN public.epf_passbook_est_details.establishment_name IS 'Name of the Employer Establishment';
COMMENT ON COLUMN public.epf_passbook_est_details.office_code IS 'Epf Office Code applicable to the Employer Establishment';
COMMENT ON COLUMN public.epf_passbook_est_details.date_of_joining_epf IS 'Date of Joining EPF';
COMMENT ON COLUMN public.epf_passbook_est_details.date_of_enquiry_epf IS 'Date of Enquiry of EPF Data';
COMMENT ON COLUMN public.epf_passbook_est_details.date_of_enquiry_employee_pension_scheme IS 'Date of Enquiry of Employee Pension Scheme Data';
COMMENT ON COLUMN public.epf_passbook_est_details.provident_fund_balance IS 'Provident fund balance details of employer (Net balance, PF ammount status)';
CREATE TABLE public.epf_passbook_overall_pf_balance (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    uan_id character varying NOT NULL,
    uan_number character varying NOT NULL,
    epf_passbook_employee_details_id character varying NOT NULL,
    pension_balance jsonb,
    current_provident_fund_balance jsonb,
    employee_share_total jsonb,
    pdf jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON COLUMN public.epf_passbook_overall_pf_balance.employee_share_total IS 'contains amount debited and credited by the employee and total balance amount for employee share';
COMMENT ON COLUMN public.epf_passbook_overall_pf_balance.pdf IS 'contains details of employee passbook (Establishment ID of the employer, EPF Passbook in Pdf format)';
CREATE TABLE public.epf_passbook_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    uan_number character varying NOT NULL,
    client_request_id character varying NOT NULL,
    vendor_request_id character varying,
    request_status character varying DEFAULT 'REQUEST_STATUS_UNSPECIFIED'::character varying NOT NULL,
    failure_reason character varying DEFAULT 'FAILURE_REASON_UNSPECIFIED'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    exit_deeplink jsonb
);
COMMENT ON TABLE public.epf_passbook_requests IS 'represents a single epf passbook fetch request for a UAN';
COMMENT ON COLUMN public.epf_passbook_requests.actor_id IS 'stores actor_id who initiated the request';
COMMENT ON COLUMN public.epf_passbook_requests.uan_number IS 'stores UAN Number';
COMMENT ON COLUMN public.epf_passbook_requests.client_request_id IS 'stores unique id given by client during otp generation';
COMMENT ON COLUMN public.epf_passbook_requests.vendor_request_id IS 'stores unique request id given by vendor after otp generation';
COMMENT ON COLUMN public.epf_passbook_requests.request_status IS 'stores request status of the request';
COMMENT ON COLUMN public.epf_passbook_requests.failure_reason IS 'stores failure reason if request_status is REQUEST_STATUS_FAILED';
COMMENT ON COLUMN public.epf_passbook_requests.exit_deeplink IS 'to store exit deeplink for the passbook request';
CREATE TABLE public.epf_passbook_transactions (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    uan_id character varying NOT NULL,
    uan_number character varying NOT NULL,
    epf_passbook_employee_details_id character varying NOT NULL,
    epf_passbook_est_details_id character varying NOT NULL,
    date_of_transactions date,
    transaction_approval_date date,
    credit_employee_share jsonb,
    credit_employer_share jsonb,
    credit_pension_balance jsonb,
    debit_credit_flag character varying,
    particular character varying,
    month_year character varying,
    transactions_approval_date date,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON COLUMN public.epf_passbook_transactions.date_of_transactions IS 'Date of transactions.';
COMMENT ON COLUMN public.epf_passbook_transactions.transaction_approval_date IS 'Date of approval of the transactions.';
COMMENT ON COLUMN public.epf_passbook_transactions.credit_employee_share IS 'Employees Share of Contribution to EPF for the month.';
COMMENT ON COLUMN public.epf_passbook_transactions.credit_employer_share IS 'Employers share of contribution to EPF for the month.';
COMMENT ON COLUMN public.epf_passbook_transactions.credit_pension_balance IS 'Amount credited to Pension Account.';
COMMENT ON COLUMN public.epf_passbook_transactions.debit_credit_flag IS 'transactions Type Debit "D" or Credit "C".';
COMMENT ON COLUMN public.epf_passbook_transactions.particular IS 'Description of the transactions as per EPF Passbook.';
COMMENT ON COLUMN public.epf_passbook_transactions.month_year IS 'Month and Year for which the contribution is made "MYYYY" OR "MMYYYY".';
COMMENT ON COLUMN public.epf_passbook_transactions.transactions_approval_date IS 'Transactions approval date.';
CREATE TABLE public.epf_sms_data (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    masked_uan_number character varying NOT NULL,
    passbook_number character varying NOT NULL,
    credit_amount jsonb NOT NULL,
    passbook_balance jsonb NOT NULL,
    credit_month date NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at_unix bigint DEFAULT 0 NOT NULL
);
COMMENT ON TABLE public.epf_sms_data IS 'Entity where EPF balance information extracted from SMS is stored';
COMMENT ON COLUMN public.epf_sms_data.id IS 'Unique identifier for epf sms data table';
COMMENT ON COLUMN public.epf_sms_data.actor_id IS 'Actor ID of the user';
COMMENT ON COLUMN public.epf_sms_data.masked_uan_number IS 'Masked UAN number of the user';
COMMENT ON COLUMN public.epf_sms_data.passbook_number IS 'Passbook number associated with the EPF account fetched in SMS';
COMMENT ON COLUMN public.epf_sms_data.credit_amount IS 'Monthly contribution amount to EPF stored as Money proto';
COMMENT ON COLUMN public.epf_sms_data.passbook_balance IS 'Current balance in the EPF passbook stored as Money proto';
COMMENT ON COLUMN public.epf_sms_data.credit_month IS 'Month and year for which the EPF data is applicable (Format: MMM-YY, e.g., SEP-23) stored as Date proto';
COMMENT ON COLUMN public.epf_sms_data.deleted_at_unix IS 'Timestamp in unix format when the record was soft deleted, 0 if not deleted';
CREATE TABLE public.gmail_query_exec_results (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    query_name character varying NOT NULL,
    is_mail_present boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
CREATE TABLE public.investment_declarations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    invested_amount jsonb NOT NULL,
    invested_at timestamp with time zone NOT NULL,
    maturity_date timestamp with time zone,
    interest_rate double precision,
    declaration_details jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    instrument_type character varying DEFAULT 'INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED'::character varying NOT NULL,
    external_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    consent_id uuid,
    source character varying
);
COMMENT ON TABLE public.investment_declarations IS 'entity where all the investments declared by the user are stored';
COMMENT ON COLUMN public.investment_declarations.declaration_details IS 'contains instrument specific details of investment';
COMMENT ON COLUMN public.investment_declarations.external_id IS 'a unique id that can be shared with external systems';
COMMENT ON COLUMN public.investment_declarations.consent_id IS 'identifier to consent recorded when user submitted the investment declaration';
COMMENT ON COLUMN public.investment_declarations.source IS 'stores source of declaration, e.g. "Magic Import"';
CREATE TABLE public.mail_sync_logs (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    request_hash character varying NOT NULL,
    is_full_mail_sync boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    messages_fetched boolean DEFAULT false,
    deleted_at timestamp with time zone
);
CREATE TABLE public.merchant_queries (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    merchant character varying NOT NULL,
    query_type character varying NOT NULL,
    query text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
CREATE TABLE public.merchants (
    name character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
CREATE TABLE public.message_processing_states (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    request_id uuid NOT NULL,
    query_id uuid NOT NULL,
    message_id character varying NOT NULL,
    state character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    time_division integer
);
CREATE TABLE public.networth_refresh_session (
    id character varying NOT NULL,
    actor_id character varying NOT NULL,
    asset_refresh_details jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.networth_refresh_session IS 'table for storing all networth refresh sessions initiated by the user';
COMMENT ON COLUMN public.networth_refresh_session.actor_id IS 'actor_id to whom the assets belongs';
COMMENT ON COLUMN public.networth_refresh_session.asset_refresh_details IS 'ordered list of assets to be refreshed with their refresh info';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.uan_accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    uan_number character varying NOT NULL,
    phone_number jsonb,
    is_primary boolean DEFAULT false NOT NULL,
    raw_details jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.uan_accounts IS 'represents Universal Account Number (UAN) issued by Employees'' Provident Fund Organization (EPFO)';
COMMENT ON COLUMN public.uan_accounts.actor_id IS 'stores actor_id to which UAN belongs';
COMMENT ON COLUMN public.uan_accounts.uan_number IS 'stores UAN Number';
COMMENT ON COLUMN public.uan_accounts.phone_number IS 'stores phone number linked to the uan';
COMMENT ON COLUMN public.uan_accounts.is_primary IS 'stores if this is the primary uan for the user';
COMMENT ON COLUMN public.uan_accounts.raw_details IS 'stores raw parsed epf passbook';
CREATE TABLE public.user_declarations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    external_id character varying DEFAULT public.uuid_generate_v4() NOT NULL,
    user_declaration jsonb NOT NULL,
    declaration_type character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.user_declarations IS 'entity where all declared value by the user are stored';
COMMENT ON COLUMN public.user_declarations.user_declaration IS 'contains specific details declared by the user';
COMMENT ON COLUMN public.user_declarations.declaration_type IS 'type of declaration made by the user';
CREATE TABLE public.user_mail_access_infos (
    actor_id character varying NOT NULL,
    email_id character varying NOT NULL,
    access_info text NOT NULL,
    key_secret_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    synced_till date,
    last_synced_on date,
    deleted_at timestamp with time zone,
    user_type character varying
);
CREATE TABLE public.user_mail_details (
    message_processing_id uuid NOT NULL,
    mail_detail text NOT NULL,
    key_secret_id character varying,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    data_encryption_key text
);
CREATE TABLE public.user_spendings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying,
    message_id character varying NOT NULL,
    merchant character varying NOT NULL,
    mail_date timestamp with time zone NOT NULL,
    amount real,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    transaction_type character varying,
    transaction_id character varying,
    deleted_at timestamp with time zone,
    onboarded_actor_id character varying,
    email_id character varying
);
CREATE TABLE public.waitlist_onboarded_actor_mappings (
    waitlist_actor_id character varying NOT NULL,
    onboarded_actor_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    deleted_at timestamp with time zone
);
ALTER TABLE ONLY public.employer_pf_history
    ADD CONSTRAINT employer_pf_history_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.epf_import_session
    ADD CONSTRAINT epf_import_session_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.epf_passbook_employee_details
    ADD CONSTRAINT epf_passbook_employee_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.epf_passbook_employee_details
    ADD CONSTRAINT epf_passbook_employee_details_uan_id_key UNIQUE (uan_id);
ALTER TABLE ONLY public.epf_passbook_est_details
    ADD CONSTRAINT epf_passbook_est_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.epf_passbook_overall_pf_balance
    ADD CONSTRAINT epf_passbook_overall_pf_balance_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.epf_passbook_requests
    ADD CONSTRAINT epf_passbook_requests_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.epf_passbook_transactions
    ADD CONSTRAINT epf_passbook_transactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.epf_sms_data
    ADD CONSTRAINT epf_sms_data_pkey PRIMARY KEY (actor_id, id);
ALTER TABLE ONLY public.gmail_query_exec_results
    ADD CONSTRAINT gmail_query_exec_results_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.investment_declarations
    ADD CONSTRAINT investment_declarations_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.mail_sync_logs
    ADD CONSTRAINT mail_sync_logs_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.merchant_queries
    ADD CONSTRAINT merchant_queries_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.merchants
    ADD CONSTRAINT merchants_pkey PRIMARY KEY (name);
ALTER TABLE ONLY public.message_processing_states
    ADD CONSTRAINT message_processing_states_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.networth_refresh_session
    ADD CONSTRAINT networth_refresh_session_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.uan_accounts
    ADD CONSTRAINT uan_accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.user_declarations
    ADD CONSTRAINT user_declarations_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.user_mail_access_infos
    ADD CONSTRAINT user_mail_access_infos_pkey PRIMARY KEY (actor_id, email_id);
ALTER TABLE ONLY public.user_mail_details
    ADD CONSTRAINT user_mail_details_pkey PRIMARY KEY (message_processing_id);
ALTER TABLE ONLY public.user_spendings
    ADD CONSTRAINT user_spendings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.waitlist_onboarded_actor_mappings
    ADD CONSTRAINT waitlist_onboarded_actor_mappings_pkey PRIMARY KEY (waitlist_actor_id);
CREATE UNIQUE INDEX employer_pf_history_data_source ON public.employer_pf_history USING btree (company_id, data_source);
CREATE INDEX employer_pf_history_updated_at_idx ON public.employer_pf_history USING btree (updated_at DESC);
CREATE INDEX epf_import_session_updated_at_idx ON public.epf_import_session USING btree (updated_at);
CREATE UNIQUE INDEX epf_passbook_employee_details_uan_id_idx ON public.epf_passbook_employee_details USING btree (uan_id) WHERE (deleted_at IS NULL);
CREATE INDEX epf_passbook_employee_details_updated_at_idx ON public.epf_passbook_employee_details USING btree (updated_at);
CREATE INDEX epf_passbook_est_details_updated_at_idx ON public.epf_passbook_est_details USING btree (updated_at);
CREATE INDEX epf_passbook_overall_pf_balance_updated_at_idx ON public.epf_passbook_overall_pf_balance USING btree (updated_at);
CREATE UNIQUE INDEX epf_passbook_requests_actor_id_client_request_id_idx ON public.epf_passbook_requests USING btree (actor_id, client_request_id) WHERE (deleted_at IS NULL);
CREATE INDEX epf_passbook_requests_actor_id_created_at_idx ON public.epf_passbook_requests USING btree (actor_id, created_at DESC) WHERE (deleted_at IS NULL);
CREATE INDEX epf_passbook_requests_actor_id_uan_number_created_at_idx ON public.epf_passbook_requests USING btree (actor_id, uan_number, created_at) WHERE (deleted_at IS NULL);
CREATE INDEX epf_passbook_requests_updated_at_idx ON public.epf_passbook_requests USING btree (updated_at);
CREATE INDEX epf_passbook_transactions_updated_at_idx ON public.epf_passbook_transactions USING btree (updated_at);
CREATE UNIQUE INDEX epf_sms_data_id_idx ON public.epf_sms_data USING btree (id);
COMMENT ON INDEX public.epf_sms_data_id_idx IS 'Ensures uniqueness of EPF SMS data records based on id';
CREATE UNIQUE INDEX epf_sms_data_unique_idx ON public.epf_sms_data USING btree (actor_id, passbook_number, credit_month, deleted_at_unix);
COMMENT ON INDEX public.epf_sms_data_unique_idx IS 'Ensures uniqueness of EPF SMS data based on actor_id, passbook_number, credit_month and deleted_at_unix combination';
CREATE INDEX epf_sms_data_updated_at_idx ON public.epf_sms_data USING btree (updated_at DESC);
CREATE INDEX idx_access_info_last_synced_on ON public.user_mail_access_infos USING btree (last_synced_on);
CREATE INDEX idx_accessinfo_email_id ON public.user_mail_access_infos USING btree (email_id);
CREATE INDEX idx_actor_id_merchant_date ON public.user_spendings USING btree (actor_id, merchant, mail_date);
CREATE INDEX idx_actor_map_onb_actor_id ON public.waitlist_onboarded_actor_mappings USING btree (onboarded_actor_id);
CREATE UNIQUE INDEX idx_merchant_query_type_query ON public.merchant_queries USING btree (merchant, query_type, query);
CREATE UNIQUE INDEX idx_message_processing_states_request_id_query_id_message_id ON public.message_processing_states USING btree (request_id, query_id, message_id);
CREATE UNIQUE INDEX idx_query_results_actor_id_query_name ON public.gmail_query_exec_results USING btree (actor_id, query_name);
CREATE INDEX in_gqer_updated_at_index ON public.gmail_query_exec_results USING btree (updated_at);
CREATE INDEX in_mps_updated_at_index ON public.message_processing_states USING btree (updated_at);
CREATE INDEX in_mq_updated_at_index ON public.merchant_queries USING btree (updated_at);
CREATE INDEX in_msl_updated_at_index ON public.mail_sync_logs USING btree (updated_at);
CREATE INDEX in_umai_updated_at_index ON public.user_mail_access_infos USING btree (updated_at);
CREATE INDEX in_umd_updated_at_index ON public.user_mail_details USING btree (updated_at);
CREATE INDEX in_us_updated_at_index ON public.user_spendings USING btree (updated_at);
CREATE INDEX in_woam_updated_at_index ON public.waitlist_onboarded_actor_mappings USING btree (updated_at);
CREATE INDEX investment_declarations_actor_id_instrument_type_created_at_idx ON public.investment_declarations USING btree (actor_id, instrument_type, created_at) WHERE (deleted_at IS NULL);
CREATE UNIQUE INDEX investment_declarations_external_id_key ON public.investment_declarations USING btree (external_id) WHERE (deleted_at IS NULL);
CREATE INDEX investment_declarations_updated_at_idx ON public.investment_declarations USING btree (updated_at DESC);
CREATE INDEX mail_sync_logs_actor_id ON public.mail_sync_logs USING btree (actor_id);
CREATE UNIQUE INDEX mail_sync_logs_request_hash_key ON public.mail_sync_logs USING btree (request_hash);
CREATE UNIQUE INDEX uan_accounts_actor_id_uan_number_key ON public.uan_accounts USING btree (actor_id, uan_number) WHERE (deleted_at IS NULL);
CREATE INDEX uan_accounts_updated_at_idx ON public.uan_accounts USING btree (updated_at);
CREATE UNIQUE INDEX uniq_idx_actor_id_merchant_tr_id ON public.user_spendings USING btree (actor_id, merchant, transaction_id) WHERE (((actor_id)::text <> ''::text) AND ((transaction_id)::text <> ''::text));
CREATE UNIQUE INDEX uniq_idx_actor_id_msg_id ON public.user_spendings USING btree (actor_id, message_id) WHERE ((actor_id)::text <> ''::text);
CREATE UNIQUE INDEX uniq_idx_onb_actor_id_merchant_tr_id ON public.user_spendings USING btree (onboarded_actor_id, merchant, transaction_id) WHERE (((onboarded_actor_id)::text <> ''::text) AND ((transaction_id)::text <> ''::text));
CREATE UNIQUE INDEX uniq_idx_onb_actor_id_msg_id ON public.user_spendings USING btree (onboarded_actor_id, message_id) WHERE ((onboarded_actor_id)::text <> ''::text);
CREATE UNIQUE INDEX user_declarations_actor_id_declaration_type_uniq_idx ON public.user_declarations USING btree (actor_id, declaration_type) WHERE (deleted_at IS NULL);
CREATE UNIQUE INDEX user_declarations_external_id_uniq_idx ON public.user_declarations USING btree (external_id) WHERE (deleted_at IS NULL);
CREATE INDEX user_declarations_updated_at_idx ON public.user_declarations USING btree (updated_at DESC);
CREATE INDEX user_spendings_actor_id_email_id ON public.user_spendings USING btree (actor_id, email_id);
ALTER TABLE ONLY public.user_mail_details
    ADD CONSTRAINT fk_message_processing_id FOREIGN KEY (message_processing_id) REFERENCES public.message_processing_states(id) ON DELETE CASCADE;
