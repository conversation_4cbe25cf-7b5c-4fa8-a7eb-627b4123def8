CREATE TABLE IF NOT EXISTS cibil_reports (
   actor_id                     VA<PERSON>HAR  NULL,
   credit_report_id             VA<PERSON>HAR  NULL,
   credit_report_downloaded_at_ist TIMESTAMPTZ  NULL DEFAULT now(),
    status                      VARCHAR NULL,
    safety_check_failure          BOOL NULL,
    expiration_date              VA<PERSON><PERSON>R NULL,
    creation_date                VA<PERSON><PERSON>R NULL,
    asset_id                     VARCHAR NULL,
    asset_type                   VA<PERSON>HAR NULL,
    reference_key                VARCHAR NULL,
    current_version              VARCHAR NULL,
    safety_check_passed           BOOL NULL,
    frozen                        VARCHAR NULL,
    deceased_indicator            BOOL NULL,
    fraud_indicator               BOOL NULL,
    message_code_symbol           VA<PERSON><PERSON>R NULL,
    message_code_description      VARCHAR NULL,
    message_code_rank             VARCHAR NULL,
    message_text                  VARCHAR NULL,
    message_type_symbol           VA<PERSON><PERSON>R NULL,
    message_type_description      VARCHAR NULL,
    message_type_rank             VARCHAR NULL,
    message_type_abbreviation     VA<PERSON><PERSON>R NULL,
    source_inquiry_date           VARCHAR NULL,
    source_bureau_symbol          <PERSON><PERSON><PERSON><PERSON> NULL,
    source_bureau_description     VARCHAR NULL,
    source_bureau_rank            VA<PERSON>HAR NULL,
    source_bureau_abbreviation    VA<PERSON>HAR NULL
    );
