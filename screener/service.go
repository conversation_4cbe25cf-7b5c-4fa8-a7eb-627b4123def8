package screener

import (
	"context"
	"strings"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	screenerPb "github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/screener/checks"
	"github.com/epifi/gamma/screener/config/genconf"
	"github.com/epifi/gamma/screener/dao"
	"github.com/epifi/gamma/screener/metrics"

	"go.uber.org/zap"
)

type Service struct {
	conf             *genconf.Config
	saDao            dao.ScreenerAttemptsDao
	scaDao           dao.ScreenerCheckAttemptsDao
	releaseEvaluator release.IEvaluator
	// check factory
	uanPresenceCheck       checks.CheckFactory
	backgroundCheck        checks.CheckFactory
	workEmailCheck         checks.CheckFactory
	gmailVerificationCheck checks.CheckFactory
	epfoCheck              checks.CheckFactory
	connectedAccountsCheck checks.CheckFactory
	creditReportCheck      checks.CheckFactory
	incomeEstimateCheck    checks.CheckFactory
	itrIntimationCheck     checks.CheckFactory
	lendabilityCheck       checks.CheckFactory
	smsParserCheck         checks.CheckFactory
	installedAppsCheck     checks.CheckFactory
}

func NewService(conf *genconf.Config, saDao dao.ScreenerAttemptsDao, scaDao dao.ScreenerCheckAttemptsDao, uanPresenceCheck *checks.UANPresenceCheck,
	backgroundCheck *checks.BackgroundCheck, workEmailCheck *checks.WorkEmailCheck, gmailVerificationCheck *checks.GmailVerificationCheck,
	epfoCheck *checks.EpfoCheck, connectedAccountsCheck *checks.ConnectedAccountsCheck, creditReportCheck *checks.CreditReportCheck,
	incomeEstimateCheck *checks.IncomeEstimateCheck, itrIntimationCheck *checks.ITRIntimationCheck, lendabilityCheck *checks.LendabilityCheck,
	smsParserCheck *checks.SMSParserCheck, installedAppsCheck *checks.InstalledAppsCheck, releaseEvaluator release.IEvaluator) *Service {
	return &Service{
		conf:                   conf,
		saDao:                  saDao,
		scaDao:                 scaDao,
		uanPresenceCheck:       uanPresenceCheck,
		backgroundCheck:        backgroundCheck,
		workEmailCheck:         workEmailCheck,
		gmailVerificationCheck: gmailVerificationCheck,
		epfoCheck:              epfoCheck,
		connectedAccountsCheck: connectedAccountsCheck,
		creditReportCheck:      creditReportCheck,
		incomeEstimateCheck:    incomeEstimateCheck,
		itrIntimationCheck:     itrIntimationCheck,
		lendabilityCheck:       lendabilityCheck,
		smsParserCheck:         smsParserCheck,
		installedAppsCheck:     installedAppsCheck,
		releaseEvaluator:       releaseEvaluator,
	}
}

var _ screenerPb.ScreenerServer = (*Service)(nil)

// RunCheck rpc runs a particular screener check mentioned in the request.
// If the relevant user data is not present, then a deeplink is sent in the response
// that needs to be shown to the user to collect the details necessary to run the check
func (s *Service) RunCheck(ctx context.Context, req *screenerPb.RunCheckRequest) (*screenerPb.RunCheckResponse, error) {
	enumToCheckFactoryMap := s.getCheckToFactoryMap()
	// get active screener attempt.
	// check if expired (and delete), then create.
	// else create if not exists
	sa, err := s.getOrCreateScreenerAttempt(ctx, req.GetActorId())
	if err != nil {
		return &screenerPb.RunCheckResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in getOrCreateScreenerAttempt"),
		}, nil
	}

	// run factory check
	checkToRun := enumToCheckFactoryMap[req.GetCheckType()]
	checkResp, err := checkToRun.RunCheck(ctx, &checks.RunCheckRequest{
		ActorId:           req.GetActorId(),
		ScreenerAttemptId: sa.GetId(),
		BackAction:        req.GetBackAction(),
	})
	if grpcErr := epifigrpc.RPCError(checkResp, err); grpcErr != nil {
		logger.Error(ctx, "error while running check", zap.Error(grpcErr),
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.STAGE, req.GetCheckType().String()))
		if err != nil {
			return &screenerPb.RunCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
		return checkResp, nil
	}

	// update screener attempt in table (if passed/terminally failed)
	if err = s.updateScreenerAttempt(ctx, req.GetActorId(), checkResp); err != nil {
		return &screenerPb.RunCheckResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// return status along with (if needed) deeplink
	return checkResp, nil
}

// GetScreenerAttemptsByActorId fetches all the screener checks that were run
// for a user at the time when the rpc call is made
func (s *Service) GetScreenerAttemptsByActorId(ctx context.Context, req *screenerPb.GetScreenerAttemptsByActorIdRequest) (*screenerPb.GetScreenerAttemptsByActorIdResponse, error) {
	if req.GetActorId() == "" {
		return &screenerPb.GetScreenerAttemptsByActorIdResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("actor id is empty"),
		}, nil
	}
	var (
		sa     *screenerPb.ScreenerAttempt
		errGet error
	)

	if req.GetCachedData() {
		sa, errGet = s.saDao.GetLastScreenerAttemptForActor(ctx, req.GetActorId())
		if storage.IsRecordNotFoundError(errGet) {
			return &screenerPb.GetScreenerAttemptsByActorIdResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		if errGet != nil {
			logger.Error(ctx, "failed to get active attempts for actor", zap.Error(errGet))
			return &screenerPb.GetScreenerAttemptsByActorIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to get active attempts for actor"),
			}, nil
		}
	} else {
		sa, errGet = s.getOrCreateScreenerAttempt(ctx, req.GetActorId())
		if errGet != nil {
			return &screenerPb.GetScreenerAttemptsByActorIdResponse{
				Status: rpc.StatusInternalWithDebugMsg(errGet.Error()),
			}, nil
		}
	}

	// fill the check details for the checks run for screener attempt
	checksMap, err := s.getCheckDetailsForScreenerAttempt(ctx, req.GetActorId(), sa.GetId())
	if err != nil {
		return &screenerPb.GetScreenerAttemptsByActorIdResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while getting check details"),
		}, nil
	}
	metrics.RecordScreenerAttemptStatus(sa.GetResultInfo().GetResult())
	return &screenerPb.GetScreenerAttemptsByActorIdResponse{
		Status:          rpc.StatusOk(),
		ScreenerAttempt: sa,
		ChecksMap:       checksMap,
	}, nil
}

// FailScreenerCheck fails (mark check result as FAILED) all the IN_PROGRESS
// check attempts for a given check type for an actor
func (s *Service) FailScreenerCheck(ctx context.Context, req *screenerPb.FailScreenerCheckRequest) (*screenerPb.FailScreenerCheckResponse, error) {
	if strings.EqualFold(req.GetActorId(), "") || req.GetCheckType() == screenerPb.CheckType_CHECK_TYPE_UNSCPECIFIED {
		logger.Error(ctx, "required arguments not passed", zap.String(logger.CHECK_NAME, req.GetCheckType().String()))
		return &screenerPb.FailScreenerCheckResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("required arguments not passed"),
		}, nil
	}

	sa, errGet := s.saDao.GetActiveAttemptForActor(ctx, req.GetActorId())
	if errGet != nil {
		logger.Error(ctx, "failed to get active attempts for actor", zap.Error(errGet))
		return &screenerPb.FailScreenerCheckResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to get active attempts for actor"),
		}, nil
	}
	saId := sa.GetId()

	if err := s.scaDao.UpdateCheckDetails(ctx, &screenerPb.ScreenerCheckAttempt{
		ScreenerAttemptId: saId,
		CheckType:         req.GetCheckType(),
		CheckResult:       screenerPb.CheckResult_CHECK_RESULT_FAILED,
	}, []screenerPb.ScreenerCheckAttemptFieldMask{
		screenerPb.ScreenerCheckAttemptFieldMask_SCREENER_CHECK_ATTEMPT_FIELD_MASK_RESULT,
	}); err != nil {
		logger.Error(ctx, "error while updating check attempt", zap.Error(err), zap.String("screener_attempt_id", saId))
		return &screenerPb.FailScreenerCheckResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while updating check attempt"),
		}, nil
	}

	if err := s.scaDao.DeleteCheckAttempt(ctx, saId, req.GetCheckType()); err != nil {
		logger.Error(ctx, "error while deleting check entry in db", zap.Error(err), zap.String("screener_attempt_id", saId))
		return &screenerPb.FailScreenerCheckResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while deleting check entry in db"),
		}, nil
	}

	logger.Error(ctx, "failed screener check", zap.String(logger.CHECK_NAME, req.GetCheckType().String()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
	return &screenerPb.FailScreenerCheckResponse{
		Status: rpc.StatusOk(),
	}, nil
}
