minReplicas: 0
maxReplicas: 5
name: protos
repository: protos
labels:
- k8s
custom_container_image:
  enabled: true
  image: 632884248997.dkr.ecr.ap-south-1.amazonaws.com/actions-runner-dind:latest
dockerEnabled: true
dockerdWithinRunnerContainer: true
resources:
  enabled: true
  requests:
    cpu: 1.5
    memory: 3.5Gi
  limits:
    cpu: 4
    memory: 8Gi
nodeselector:
  enabled: true
  labels:
    size: xLarge
    type: runner
tolerations:
- key: runner
  value: 'true'
  operator: "Equal"
  effect: NoSchedule
ephemeral: true
