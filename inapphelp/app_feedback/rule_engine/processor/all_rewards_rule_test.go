package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"strings"
	"testing"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/inapphelp/app_feedback"
	rewardsPb "github.com/epifi/gamma/api/rewards"

	"github.com/epifi/gamma/api/rewards/mocks"

	"github.com/golang/mock/gomock"

	"github.com/epifi/gamma/inapphelp/config"
)

type AllRewardTestSuite struct {
	conf *config.Config
}

var (
	arts AllRewardTestSuite
)

func TestAllRewardRuleProcessor_IsUserEligible(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)

	mockRewardsClient := mocks.NewMockRewardsGeneratorClient(ctr)

	type args struct {
		mocks		[]interface{}
		ctx		context.Context
		actorId		string
		platform	commontypes.Platform
	}
	tests := []struct {
		name			string
		args			args
		want			commontypes.BooleanEnum
		wantErr			bool
		wantIneligibilityReason	app_feedback.AppRatingNudgeIneligibilityReason
	}{
		{
			name:	"error while fetching last earned reward in time threshold",
			args: args{
				ctx:	context.Background(),
				mocks: []interface{}{
					mockRewardsClient.EXPECT().GetRewardsByActorId(context.Background(), gomock.Any()).
						Return(&rewardsPb.RewardsResponse{Status: rpcPb.StatusInternal()}, nil),
				},
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"error while fetching last earned reward in time threshold",
			args: args{
				ctx:	context.Background(),
				mocks: []interface{}{
					mockRewardsClient.EXPECT().GetRewardsByActorId(context.Background(), gomock.Any()).
						Return(nil, errors.New("test")),
				},
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"no reward found in threshold period",
			args: args{
				ctx:	context.Background(),
				mocks: []interface{}{
					mockRewardsClient.EXPECT().GetRewardsByActorId(context.Background(), gomock.Any()).
						Return(&rewardsPb.RewardsResponse{
							Status:		rpcPb.StatusOk(),
							Rewards:	[]*rewardsPb.Reward{},
						}, nil),
				},
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
			},
			want:				commontypes.BooleanEnum_FALSE,
			wantErr:			false,
			wantIneligibilityReason:	app_feedback.AppRatingNudgeIneligibilityReason_APP_RATING_NUDGE_INELIGIBILITY_REASON_ALL_REWARDS_RULE_NO_REWARDS_EARNED_IN_SET_DURATION,
		},
		{
			name:	"user eligible",
			args: args{
				ctx:	context.Background(),
				mocks: []interface{}{
					mockRewardsClient.EXPECT().GetRewardsByActorId(context.Background(), gomock.Any()).
						Return(&rewardsPb.RewardsResponse{
							Status:		rpcPb.StatusOk(),
							Rewards:	[]*rewardsPb.Reward{&rewardsPb.Reward{Id: "abc", ChosenReward: &rewardsPb.RewardOption{RewardType: rewardsPb.RewardType_FI_COINS, Option: &rewardsPb.RewardOption_FiCoins{FiCoins: &rewardsPb.FiCoins{Units: uint32(1500)}}}}},
						}, nil),
				},
				actorId:	"test-actor-1",
				platform:	commontypes.Platform_ANDROID,
			},
			want:		commontypes.BooleanEnum_TRUE,
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := NewAllRewardRuleProcessor(mockRewardsClient)
			got, gotIneligibilityReason, err := a.IsUserEligible(tt.args.ctx, tt.args.actorId, tt.args.platform,
				arts.conf.FeedbackRuleEngineConfigMap[strings.ToLower(tt.args.platform.String())])
			if (err != nil) != tt.wantErr {
				t.Errorf("IsUserEligible() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsUserEligible() got = %v, want %v", got, tt.want)
			}
			if gotIneligibilityReason != tt.wantIneligibilityReason {
				t.Errorf("IsUserEligible() got = %v, want %v", gotIneligibilityReason, tt.wantIneligibilityReason)
			}
		})
	}
}
