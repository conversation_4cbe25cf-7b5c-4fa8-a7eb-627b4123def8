package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"

	accountsPb "github.com/epifi/gamma/api/accounts"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	upiActivityPb "github.com/epifi/gamma/api/upi/activity"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	upiNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/upi"
)

var (
	upiLitePi = &piPb.PaymentInstrument{
		Id:   "pi-23",
		Type: piPb.PaymentInstrumentType_UPI_LITE,
		Identifier: &piPb.PaymentInstrument_UpiLite{UpiLite: &piPb.UpiLite{
			PiRefId: "pi-ref-id",
			Lrn:     "lrn",
		}},
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  false,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
	}
)

func TestProcessor_UpiLitePiCreation(t *testing.T) {
	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockGetByClientRequestId struct {
		enable       bool
		clientReqId  string
		upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail
		err          error
	}

	type mockCreateUpiLitePi struct {
		enable       bool
		AccountRefId string
		lrn          string
		want         *piPb.PaymentInstrument
		err          error
	}

	type mockCreateAccountPi struct {
		enable bool
		req    *accountPiPb.CreateAccountPIRequest
		err    error
	}

	tests := []struct {
		name                     string
		req                      *upiActivityPb.UpiLitePiCreationRequest
		mockGetByClientRequestId mockGetByClientRequestId
		mockCreateAccountPi      mockCreateAccountPi
		mockCreateUpiLitePi      mockCreateUpiLitePi
		res                      *upiActivityPb.UpiLitePiCreationResponse
		wantErr                  bool
		assertErr                func(err error) bool
	}{
		{
			name: "successfully create upi lite PI",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			mockCreateUpiLitePi: mockCreateUpiLitePi{
				enable:       true,
				lrn:          "lrn",
				AccountRefId: "account-id",
				want:         upiLitePi,
			},
			mockCreateAccountPi: mockCreateAccountPi{
				enable: true,
				req: &accountPiPb.CreateAccountPIRequest{
					ActorId:     "actor-id",
					AccountId:   "upi-lite-account-id",
					AccountType: accountsPb.Type_UPI_LITE,
					PiId:        "pi-23",
				},
			},
		},
		{
			name: "transient failure while creating PI as CreateAccountPi call failed with transient error",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			mockCreateUpiLitePi: mockCreateUpiLitePi{
				enable:       true,
				lrn:          "lrn",
				AccountRefId: "account-id",
				want:         upiLitePi,
			},
			mockCreateAccountPi: mockCreateAccountPi{
				enable: true,
				req: &accountPiPb.CreateAccountPIRequest{
					ActorId:     "actor-id",
					AccountId:   "upi-lite-account-id",
					AccountType: accountsPb.Type_UPI_LITE,
					PiId:        "pi-23",
				},
				err: epifierrors.ErrTransient,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "transient failure while creating PI as CreateAccountPi call failed with permanent error",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			mockCreateUpiLitePi: mockCreateUpiLitePi{
				enable:       true,
				lrn:          "lrn",
				AccountRefId: "account-id",
				want:         upiLitePi,
			},
			mockCreateAccountPi: mockCreateAccountPi{
				enable: true,
				req: &accountPiPb.CreateAccountPIRequest{
					ActorId:     "actor-id",
					AccountId:   "upi-lite-account-id",
					AccountType: accountsPb.Type_UPI_LITE,
					PiId:        "pi-23",
				},
				err: epifierrors.ErrPermanent,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "transient failure while creating PI as CreateUpiLitePi call failed with transient error",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			mockCreateUpiLitePi: mockCreateUpiLitePi{
				enable:       true,
				lrn:          "lrn",
				AccountRefId: "account-id",
				want:         nil,
				err:          epifierrors.ErrTransient,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "permanent failure while creating PI as CreateUpiLitePi call failed with permanent error",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			mockCreateUpiLitePi: mockCreateUpiLitePi{
				enable:       true,
				lrn:          "lrn",
				AccountRefId: "account-id",
				want:         nil,
				err:          epifierrors.ErrPermanent,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "transient failure as GetByClientRequestId() call failed",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:       true,
				clientReqId:  "client-req-id",
				upiOnbDetail: nil,
				err:          epifierrors.ErrContextCanceled,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "permanent failure as GetByClientRequestId() call failed with InvalidArgument error",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:       true,
				clientReqId:  "client-req-id",
				upiOnbDetail: nil,
				err:          epifierrors.ErrInvalidArgument,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as GetByClientRequestId() call failed with RecordNotFound error",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:       true,
				clientReqId:  "client-req-id",
				upiOnbDetail: nil,
				err:          epifierrors.ErrRecordNotFound,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as upi onboarding detail has unexpected action",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as upiOnboardingDetail is already in terminal state",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as lrn is empty",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:              "",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as account-id is empty",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn:          "lrn",
				AccountRefId: "",
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as client-req-id is empty",
			req: &upiActivityPb.UpiLitePiCreationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "",
				},
				Lrn:              "lrn",
				AccountRefId:     "account-id",
				ActorId:          "actor-id",
				UpiLiteAccountId: "upi-lite-account-id",
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByClientRequestId.enable {
				md.upiOnboardingDetailDao.EXPECT().GetByClientRequestId(gomock.Any(), tt.mockGetByClientRequestId.clientReqId).
					Return(tt.mockGetByClientRequestId.upiOnbDetail, tt.mockGetByClientRequestId.err)
			}

			if tt.mockCreateUpiLitePi.enable {
				md.piHelper.EXPECT().CreateUpiLitePi(gomock.Any(), tt.mockCreateUpiLitePi.AccountRefId, tt.mockCreateUpiLitePi.lrn).
					Return(tt.mockCreateUpiLitePi.want, tt.mockCreateUpiLitePi.err)
			}

			if tt.mockCreateAccountPi.enable {
				md.piHelper.EXPECT().CreateAccountPi(gomock.Any(), tt.mockCreateAccountPi.req).
					Return(nil, tt.mockCreateAccountPi.err)
			}

			if _, err := env.ExecuteActivity(upiNs.UpiLitePiCreation, tt.req); (err != nil) != tt.wantErr {
				t.Errorf("UpiLitePiCreation() error = %v, wantErr %v", err, tt.wantErr)
			} else if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("UpiLitePiCreation() error = %v, assertion failed", err)
				return
			}
			assertTest()
		})
	}
}
