package mandate_test

import (
	"context"
	"errors"
	"reflect"
	"testing"

	gormv2 "gorm.io/gorm"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/idgen"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	"github.com/epifi/gamma/api/vendors"
	"github.com/epifi/gamma/upi/dao/mocks"
	mocks2 "github.com/epifi/gamma/upi/helper/mandate/mocks"
	"github.com/epifi/gamma/upi/mandate"
)

func TestService_CreateMandate(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockMandateDao := mocks.NewMockMandateDao(ctr)
	mockMandateRequestDao := mocks.NewMockMandateRequestDao(ctr)
	mandateCreationTimestamp := timestamppb.Now()
	expireTimestamp := timestamppb.New(mandateCreationTimestamp.AsTime().Add(vendors.UPIDefaultExpiry))
	numberIdGen := idgen.NewNumberIdGenerator(idgen.NewCryptoSeededSource())
	mockMandateHelper := mocks2.NewMockMandateProcessor(ctr)
	svc := mandate.NewService(mockMandateDao, mockMandateRequestDao, nil, mockMandateHelper, nil, nil, nil, numberIdGen, nil, nil)

	type mockCreateMandate struct {
		enable  bool
		mandate *upiMandatePb.MandateEntity
		want    *upiMandatePb.MandateEntity
		err     error
	}

	type mockCreateMandateRequest struct {
		enable         bool
		mandateRequest *upiMandatePb.MandateRequest
		want           *upiMandatePb.MandateRequest
		err            error
	}

	type mockGetByRecurringPaymentId struct {
		enable bool
		rpId   string
		want   *upiMandatePb.MandateEntity
		err    error
	}
	type mockIsMandateEnabledForActor struct {
		enable  bool
		actorId string
		res     bool
	}
	tests := []struct {
		name                         string
		req                          *upiMandatePb.CreateMandateRequest
		mockCreateMandate            mockCreateMandate
		mockCreateMandateRequest     mockCreateMandateRequest
		mockGetByRecurringPaymentId  mockGetByRecurringPaymentId
		mockIsMandateEnabledForActor mockIsMandateEnabledForActor
		res                          *upiMandatePb.CreateMandateResponse
		wantErr                      bool
	}{
		{
			name: "create mandate and request successfully",
			req: &upiMandatePb.CreateMandateRequest{
				RecurringPaymentId: "rp-id-1",
				ReqId:              "req-id-1",
				Revokeable:         false,
				ShareToPayee:       false,
				BlockFund:          false,
				InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				MandateRequestPayload: &upiMandatePb.Payload{
					MerchantRefId:          "ref-id-1",
					RefUrl:                 "ref-url",
					MandateOriginTimestamp: mandateCreationTimestamp,
					CustRefId:              "123456",
				},
				CurrentActorRole: upiMandatePb.ActorRole_ACTOR_ROLE_PAYER,
				CurrentActorId:   "actor-1",
			},
			mockIsMandateEnabledForActor: mockIsMandateEnabledForActor{
				enable:  true,
				actorId: "actor-1",
				res:     true,
			},
			mockGetByRecurringPaymentId: mockGetByRecurringPaymentId{
				enable: true,
				rpId:   "rp-id-1",
				err:    gormv2.ErrRecordNotFound,
			},
			mockCreateMandate: mockCreateMandate{
				enable: true,
				mandate: &upiMandatePb.MandateEntity{
					RecurringPaymentId: "rp-id-1",
					ReqId:              "req-id-1",
					Revokeable:         false,
					ShareToPayee:       false,
					BlockFund:          false,
					InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				},
				want: &upiMandatePb.MandateEntity{
					Id:                 "mandate-id-1",
					RecurringPaymentId: "rp-id-1",
					ReqId:              "req-id-1",
					Revokeable:         false,
					ShareToPayee:       false,
					BlockFund:          false,
					InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				},
				err: nil,
			},
			mockCreateMandateRequest: mockCreateMandateRequest{
				enable: true,
				mandateRequest: &upiMandatePb.MandateRequest{
					MandateId: "mandate-id-1",
					ReqId:     "req-id-1",
					ReqInfo: &upiMandatePb.Payload{
						MerchantRefId:          "ref-id-1",
						RefUrl:                 "ref-url",
						MandateOriginTimestamp: mandateCreationTimestamp,
						InitiationMode:         vendors.DefaultInitiationModeMandate,
						Purpose:                vendors.DefaultPurposeMandate,
						CustRefId:              "123456",
					},
					Status:   upiMandatePb.MandateRequestStatus_INITIATED,
					Stage:    upiMandatePb.MandateStage_REQ_AUTH_MANDATE_RECEIVED,
					Action:   upiMandatePb.MandateType_CREATE,
					ExpireAt: expireTimestamp,
				},
				want: &upiMandatePb.MandateRequest{
					Id:        "mandate-request-id-1",
					MandateId: "mandate-id-1",
					ReqId:     "req-id-1",
					ReqInfo: &upiMandatePb.Payload{
						MerchantRefId:          "ref-id-1",
						RefUrl:                 "ref-url",
						MandateOriginTimestamp: mandateCreationTimestamp,
					},
					Status: upiMandatePb.MandateRequestStatus_INITIATED,
					Stage:  upiMandatePb.MandateStage_REQ_AUTH_MANDATE_RECEIVED,
					Action: upiMandatePb.MandateType_CREATE,
				},
				err: nil,
			},
			res: &upiMandatePb.CreateMandateResponse{
				Status: rpc.StatusOk(),
				Mandate: &upiMandatePb.MandateEntity{
					Id:                 "mandate-id-1",
					RecurringPaymentId: "rp-id-1",
					ReqId:              "req-id-1",
					Revokeable:         false,
					ShareToPayee:       false,
					BlockFund:          false,
					InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				},
			},
		},
		{
			name: "error creating mandate",
			req: &upiMandatePb.CreateMandateRequest{
				RecurringPaymentId: "rp-id-1",
				ReqId:              "req-id-1",
				Revokeable:         false,
				ShareToPayee:       false,
				BlockFund:          false,
				InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				MandateRequestPayload: &upiMandatePb.Payload{
					MerchantRefId: "ref-id-1",
					RefUrl:        "ref-url",
				},
				CurrentActorId: "actor-1",
			},
			mockIsMandateEnabledForActor: mockIsMandateEnabledForActor{
				enable:  true,
				actorId: "actor-1",
				res:     true,
			},
			mockGetByRecurringPaymentId: mockGetByRecurringPaymentId{
				enable: true,
				rpId:   "rp-id-1",
				err:    gormv2.ErrRecordNotFound,
			},
			mockCreateMandate: mockCreateMandate{
				enable: true,
				mandate: &upiMandatePb.MandateEntity{
					RecurringPaymentId: "rp-id-1",
					ReqId:              "req-id-1",
					Revokeable:         false,
					ShareToPayee:       false,
					BlockFund:          false,
					InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				},
				err: errors.New("error creating mandate"),
			},
			res: &upiMandatePb.CreateMandateResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "error creating mandate request",
			req: &upiMandatePb.CreateMandateRequest{
				RecurringPaymentId: "rp-id-1",
				ReqId:              "req-id-1",
				Revokeable:         false,
				ShareToPayee:       false,
				BlockFund:          false,
				InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				MandateRequestPayload: &upiMandatePb.Payload{
					MerchantRefId:          "ref-id-1",
					RefUrl:                 "ref-url",
					MandateOriginTimestamp: mandateCreationTimestamp,
					CustRefId:              "123456",
				},
				CurrentActorRole: upiMandatePb.ActorRole_ACTOR_ROLE_PAYER,
				CurrentActorId:   "actor-1",
			},
			mockIsMandateEnabledForActor: mockIsMandateEnabledForActor{
				enable:  true,
				actorId: "actor-1",
				res:     true,
			},
			mockGetByRecurringPaymentId: mockGetByRecurringPaymentId{
				enable: true,
				rpId:   "rp-id-1",
				err:    gormv2.ErrRecordNotFound,
			},
			mockCreateMandate: mockCreateMandate{
				enable: true,
				mandate: &upiMandatePb.MandateEntity{
					RecurringPaymentId: "rp-id-1",
					ReqId:              "req-id-1",
					Revokeable:         false,
					ShareToPayee:       false,
					BlockFund:          false,
					InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				},
				want: &upiMandatePb.MandateEntity{
					Id:                 "mandate-id-1",
					RecurringPaymentId: "rp-id-1",
					ReqId:              "req-id-1",
					Revokeable:         false,
					ShareToPayee:       false,
					BlockFund:          false,
					InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				},
				err: nil,
			},
			mockCreateMandateRequest: mockCreateMandateRequest{
				enable: true,
				mandateRequest: &upiMandatePb.MandateRequest{
					MandateId: "mandate-id-1",
					ReqId:     "req-id-1",
					ReqInfo: &upiMandatePb.Payload{
						MerchantRefId:          "ref-id-1",
						RefUrl:                 "ref-url",
						MandateOriginTimestamp: mandateCreationTimestamp,
						InitiationMode:         vendors.DefaultInitiationModeMandate,
						Purpose:                vendors.DefaultPurposeMandate,
						CustRefId:              "123456",
					},
					Status:   upiMandatePb.MandateRequestStatus_INITIATED,
					Stage:    upiMandatePb.MandateStage_REQ_AUTH_MANDATE_RECEIVED,
					Action:   upiMandatePb.MandateType_CREATE,
					ExpireAt: expireTimestamp,
				},
				err: errors.New("error creating mandate request"),
			},
			res: &upiMandatePb.CreateMandateResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "mandate already exists",
			req: &upiMandatePb.CreateMandateRequest{
				RecurringPaymentId: "rp-id-1",
				ReqId:              "req-id-1",
				Revokeable:         false,
				ShareToPayee:       false,
				BlockFund:          false,
				InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				MandateRequestPayload: &upiMandatePb.Payload{
					MerchantRefId:          "ref-id-1",
					RefUrl:                 "ref-url",
					MandateOriginTimestamp: mandateCreationTimestamp,
				},
				CurrentActorId: "actor-1",
			},
			mockIsMandateEnabledForActor: mockIsMandateEnabledForActor{
				enable:  true,
				actorId: "actor-1",
				res:     true,
			},
			mockGetByRecurringPaymentId: mockGetByRecurringPaymentId{
				enable: true,
				rpId:   "rp-id-1",
				want: &upiMandatePb.MandateEntity{
					RecurringPaymentId: "rp-id-1",
				},
			},
			res: &upiMandatePb.CreateMandateResponse{
				Status: rpc.StatusAlreadyExists(),
				Mandate: &upiMandatePb.MandateEntity{
					RecurringPaymentId: "rp-id-1",
				},
			},
		},
		{
			name: "mandate flow disable for actor",
			req: &upiMandatePb.CreateMandateRequest{
				RecurringPaymentId: "rp-id-1",
				ReqId:              "req-id-1",
				Revokeable:         false,
				ShareToPayee:       false,
				BlockFund:          false,
				InitiatedBy:        upiMandatePb.MandateInitiatedBy_PAYEE,
				MandateRequestPayload: &upiMandatePb.Payload{
					MerchantRefId:          "ref-id-1",
					RefUrl:                 "ref-url",
					MandateOriginTimestamp: mandateCreationTimestamp,
				},
				CurrentActorId: "actor-1",
			},
			mockIsMandateEnabledForActor: mockIsMandateEnabledForActor{
				enable:  true,
				actorId: "actor-1",
				res:     false,
			},
			res: &upiMandatePb.CreateMandateResponse{
				Status: rpc.StatusPermissionDenied(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByRecurringPaymentId.enable {
				mockMandateDao.EXPECT().GetByRecurringPaymentId(context.Background(), tt.mockGetByRecurringPaymentId.rpId).
					Return(tt.mockGetByRecurringPaymentId.want, tt.mockGetByRecurringPaymentId.err)
			}
			if tt.mockIsMandateEnabledForActor.enable {
				mockMandateHelper.EXPECT().IsMandateEnabledForActor(context.Background(), tt.mockIsMandateEnabledForActor.actorId).
					Return(tt.mockIsMandateEnabledForActor.res)
			}
			if tt.mockCreateMandate.enable {
				mockMandateDao.EXPECT().Create(gomock.Any(), tt.mockCreateMandate.mandate).
					Return(tt.mockCreateMandate.want, tt.mockCreateMandate.err)
			}
			if tt.mockCreateMandateRequest.enable {
				mockMandateRequestDao.EXPECT().Create(gomock.Any(), tt.mockCreateMandateRequest.mandateRequest).
					Return(tt.mockCreateMandateRequest.want, tt.mockCreateMandateRequest.err)
			}

			got, err := svc.CreateMandate(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateMandate() gotErr: %v wantErr: %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.res) {
				t.Errorf("CreateMandate() got: %v want: %v", got, tt.req)
				return
			}
		})
	}

}
