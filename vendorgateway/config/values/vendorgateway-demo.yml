Application:
  Environment: "demo"
  Name: "vendorgateway"
  IsSecureRedis: true
  SyncWrapperTimeout: 30
  VGAuthSvcSyncWrapperTimeout: 15
  IsStatementAPIEnabled: true
  IsListKeysSimulated: true
  #dmp dispute
  CreateDisputeURL: "https://simulator.demo.pointz.in:8080/test/DMP/v1.0.0/createDispute"
  DisputeStatusCheckUrl: "https://simulator.demo.pointz.in:8080/DMP/v1.0.0/disputeStatusCheck"
  SendCorrespondenceUrl: "https://simulator.demo.pointz.in:8080/DMP/v1.0.0/disputeCorrespondence"
  UploadDocumentUrl: "https://simulator.demo.pointz.in:8080/DMP/v1.0.0/disputeDocument"
  ChannelQuestionnaireUrl: "https://simulator.demo.pointz.in:8080/DMP/v1.0.0/channelQuestionnaire"
  AccountTransactionsUrl: "https://simulator.demo.pointz.in:8080/DMP/v1.0.0/transactions"
  CreateCustomerURL: "https://simulator.demo.pointz.in:9091/createCustomerFederal"
  CreateLoanCustomerURL: "https://simulator.demo.pointz.in:9091/createLoanCustomerFederal"
  LoanCustomerCreationStatusURL: "https://simulator.demo.pointz.in:9091/loanCustomerCreationStatusFederal"
  CheckCustomerStatusURL: "https://simulator.demo.pointz.in:8080/checkCustomerStatusFederal"
  CreateAccountURL: "https://simulator.demo.pointz.in:9091/createAccountFederal"
  CheckAccountStatusURL: "https://simulator.demo.pointz.in:8080/checkAccountStatusFederal"
  DedupeCheckURL: "https://simulator.demo.pointz.in:8080/dedupeCheck"
  FetchCustomerDetailsUrl: "https://simulator.demo.pointz.in:9091/openbanking/accounts/federal/GetCustomerDetails"
  EnquireVKYCStatusUrl: "https://simulator.demo.pointz.in:9091/openbanking/enquirevkyc"
  EnquireBalanceURL: "https://simulator.demo.pointz.in:9091/openbanking/accounts/federal/getBalance"
  CkycSearchURL: "https://simulator.demo.pointz.in:9091/ckyc/search"
  GetKycDataURL: "https://simulator.demo.pointz.in:9091/ckyc/download"
  CreateVirtualIdURL: "https://simulator.demo.pointz.in:8080/createVirtualIdFederal"
  GetTokenURL: "https://simulator.demo.pointz.in:8080/listKeys"
  DeviceRegistrationURL: "https://simulator.demo.pointz.in:9091/registerDevice"
  SetPINURL: "https://simulator.demo.pointz.in:8080/SetCredFederal"
  ValidateAddressURL: "https://simulator.demo.pointz.in:8080/ValAddFederal"
  UPIBalanceEnquiryURL: "https://simulator.demo.pointz.in:8080/BalEnq"
  ReqComplaintURL: "https://simulator.demo.pointz.in:8080/reqComplaint"
  ReqCheckComplaintStatusUrl: "https://simulator.demo.pointz.in:8080/checkComplaintStatus"
  GenerateUpiOtpURL: "https://simulator.demo.pointz.in:8080/generateUpiOtp"
  RespAuthDetailsURL: "https://simulator.demo.pointz.in:8080/RespAuthDetails"
  ReqPayURL: "https://simulator.demo.pointz.in:8080/ReqPay"
  RegisterMobileURL: "https://simulator.demo.pointz.in:8080/registerMobile"
  ListUpiKeyUrl: "https://simulator.demo.pointz.in:8080/ListKeys"
  ListAccountURL: "https://simulator.demo.pointz.in:8080/ListAccount"
  ListAccountProviderURL: "https://simulator.demo.pointz.in:8080/ListAcctProvider"
  RespTxnConfirmationURL: "https://simulator.demo.pointz.in:8080/RespTxnConfirmationFederal"
  RespValidateAddressURL: "https://simulator.demo.pointz.in:8080/RespValAddFederal"
  ReqCheckTxnStatusURL: "https://simulator.demo.pointz.in:8080/ReqCheckTxnStatusFederal"
  ListVaeURL: "https://simulator.demo.pointz.in:8080/ListVaeFederal"
  ReqMandateURL: "https://simulator.demo.pointz.in:8080/ReqMandate"
  RespAuthMandateURL: "https://simulator.demo.pointz.in:8080/RespAuthMandate"
  RespMandateConfirmationURL: "https://simulator.demo.pointz.in:8080/RespMandateConfirmation"
  RespAuthValCustURL: "https://simulator.demo.pointz.in:8080/RespAuthValCustURL"
  ReqActivationUrl: "https://simulator.demo.pointz.in:8080/ActivateInternationalPayments"
  ListPspURL: "https://simulator.demo.pointz.in:8080/ListPsp"
  GetMapperInfoURL: "https://simulator.demo.pointz.in:8080/GetMapperInfo"
  RegMapperURL: "https://simulator.demo.pointz.in:8080/RegMapper"
  ReqValQRUrl: "https://simulator.demo.pointz.in:8080/ValidateInternationalQR"
  GetUpiLiteURL: "https://simulator.demo.pointz.in:8080/GetUpiLite"
  SyncUpiLiteInfoURL: "https://simulator.demo.pointz.in:8080/SyncUpiLiteInfo"

  PanProfileURL: "https://testapi.karza.in/v3/pan-profile"

  # Liveness Service
  Veri5CheckLivenessRequestURL: "https://simulator.demo.pointz.in:9091/video-id-kyc/api/1.0/liveness"
  Veri5MatchFaceRequestURL: "https://sandbox.veri5digital.com/video-id-kyc/api/1.0/faceCompare"
  KarzaCheckLivenessRequestURL: "https://simulator.demo.pointz.in:9091/v3/uat/video-liveness"
  KarzaLivenessCallbackURL: "https://vnotificationgw.demo.pointz.in/liveness/karza"
  KarzaMatchFaceRequestURL: "https://simulator.demo.pointz.in:9091/v3/facesimilarity"
  KarzaCheckPassiveLivenessRequestURL: "https://simulator.demo.pointz.in:9091/v3/image-liveness"
  KarzaCheckLivenessStatusURL: "https://simulator.demo.pointz.in:9091/v3/video-liveness-status"
  InhouseCheckLivenessRequestURL: "https://simulator.demo.pointz.in:9091/inhouse-liveness"
  InhouseMatchFaceRequestURL: "https://simulator.demo.pointz.in:9091/inhouse-facematch"
  InhouseMatchFaceRequestURLV2: "https://simulator.demo.pointz.in:9091/inhouse-facematch"
  UseFormMarshalForKarza: false
  UseFormMarshalForKarzaFM: false

  # EPAN urls
  GetEPANKarzaStatusURL: "https://simulator.demo.pointz.in:9091/karza/epan"
  InhouseGetAndValidateEPANURL: "https://simulator.demo.pointz.in:9091/inhouse/epan"

  # ITR
  InhouseVerifyAndGetITRIntimationDetailsURL: "https://simulator.demo.pointz.in:9091/inhouse/verify/itr-intimation"

  #Call back urls for account setup
  CreateCustomerCallBackUrl: "https://vnotificationgw.demo.pointz.in/openbanking/customer/create"
  CreateAccountCallBackUrl: "https://vnotificationgw.demo.pointz.in/openbanking/account/create"

  # Employment Service
  Employment:
    KarzaPFOTPURL: "https://testapi.karza.in/v2/epf-get-otp"
    KarzaPFPassbookURL: "https://testapi.karza.in/v3/epf-get-passbook"
    KarzaEmploymentVerificationURL: "https://testapi.karza.in/v2/employment-verification-advanced"
    KarzaSearchCompanyNameURL: "https://testapi.kscan.in/v3/employer-search-lite"
    KarzaUANLookupURL: "https://simulator.demo.pointz.in:9091/v2/uan-lookup"
    KarzaEPFAuthURL: "https://testapi.karza.in/v2/epf-auth"
    KarzaEmployeeNameSearchURL: "https://simulator.demo.pointz.in:9091/v2/employee-search"
    KarzaCompanyMasterLLPDataURL: "https://simulator.demo.pointz.in:9091/v2/mca"
    KarzaSearchGSTINBasisPAN: "https://gst.karza.in/uat/v1/search"
    KarzaGetForm16QuarterlyURL: "https://testapi.karza.in/v3/tdsq"
    KarzaGetEmployerDetailsByGstinURL: "https://simulator.demo.pointz.in:9091/v2/gst-verification"
    KarzaGetUANFromPan: "https://simulator.demo.pointz.in:9091/v2/uan-by-pan"
    SignzyLoginURL: "https://simulator.demo.pointz.in:9091/v2/login"
    SignzyDomainNameVerificationURL: "https://simulator.demo.pointz.in:9091/v2/domainverifications"
    KarzaFindUanByPan: "https://simulator.demo.pointz.in:9091/v3/pan-uan"

  # CRM Service
  FreshdeskURI:
    Agent: "/agents"
    Ticket: "/tickets"
    FilterTicket: "/search/tickets"
    Solutions: "/solutions"
    Contacts: "/contacts"
    TicketField: "/admin/ticket_fields"
  FreshDeskAccountConfig:
    EPIFI_TECH:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifitechnologies.freshdesk.com/api/v2"
    FEDERAL_BANK:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifitechnologies.freshdesk.com/api/v2"

  # Freshdesk service
  FreshdeskAgentURL: "https://ficaretesting.freshdesk.com/api/v2/agents"
  FreshdeskTicketURL: "https://ficaretesting.freshdesk.com/api/v2/tickets"
  FreshdeskFilterTicketURL: "https://ficaretesting.freshdesk.com/api/v2/search/tickets"
  FreshdeskSolutionsURL: "https://ficaretesting.freshdesk.com/api/v2/solutions"
  FreshdeskContactsURL: "https://ficaretesting.freshdesk.com/api/v2/contacts"
  FreshdeskTicketFieldURL: "https://ficaretesting.freshdesk.com/api/v2/admin/ticket_fields"
  CxFreshdeskTicketAttachmentsBucketName: "epifi-demo-cx-ticket-attachments"

  #Freshchat service
  FreshchatConversationURL: "https://epifi6.freshchat.com/v2/conversations"
  FreshchatUserURL: "https://epifi6.freshchat.com/v2/users"
  FreshchatAgentURL: "https://epifi6.freshchat.com/v2/agents"


  DrivingLicenseValidationUrl: "https://simulator.demo.pointz.in:9091/v3/dl"

  VoterIdValidationUrl: "https://simulator.demo.pointz.in:9091/v2/voter"

  BankAccountVerificationUrl: "https://simulator.demo.pointz.in:9091/verify-bank-account"

  CAMS:
    OrderFeedFileURL: "https://simulator.demo.pointz.in:8080/cams/ProcessOrderFeedFile"
    FATCAFileURL: "https://simulator.demo.pointz.in:8080/cams/ProcessFATCAFeedFile"
    ElogFileURL: "https://simulator.demo.pointz.in:8080/cams/ProcessElogFile"
    OrderFeedFileStatusURL: "https://simulator.demo.pointz.in:8080/cams/GetOrderFeedFileStatus"
    OrderFeedFileSyncURL: "https://simulator.demo.pointz.in:8080/cams/ProcessOrderFeedFileSync"
    S3Bucket: "epifi-demo-mutualfund"
    NFTFileURL: "https://simulator.demo.pointz.in:8080/cams/ProcessNFTFile"
    GetFolioDetailsURL: "https://simulator.demo.pointz.in:8080/cams/GetFolioDetails"

  Tiering:
    AddSchemeChangeURL: "https://simulator.demo.pointz.in:9091/tiering/schemeChangeAdd"
    EnquireSchemeChangeURL: "https://simulator.demo.pointz.in:9091/tiering/schemeChangeEnq"

  SmallCase:
    CreateTransactionURL: "https://simulator.demo.pointz.in:8080/smallcase/CreateTransaction"
    InitiateHoldingsImportURL: "https://simulator.demo.pointz.in:8080/smallcase/InitiateHoldingsImportURL"
    TriggerHoldingsImportFetchURL: "https://simulator.demo.pointz.in:8080/smallcase/TriggerHoldingsImportFetchURL"
    MFAnalyticsURL: "https://mf-api.smallcase.com/gateway/mf/analytics"
    SmallCaseGateway: "fimoney-stag"

  MFCentral:
    GenerateTokenURL: "https://simulator.demo.pointz.in:8080/mfcentral/GenerateToken"
    EncryptAndSignURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
    VerifyAndDecryptURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
    UpdateFolioEmailURL: "https://simulator.demo.pointz.in:8080/mfcentral/api/client/v1/updateEmail"
    UpdateFolioMobileURL: "https://simulator.demo.pointz.in:8080/mfcentral/api/client/v1/updateMobile"
    InvestorConsentUrl: "https://simulator.demo.pointz.in:8080/mfcentral/api/client/v1/investorconsent"
    SubmitCasSummaryUrl: "https://simulator.demo.pointz.in:8080/mfcentral/api/client/v1/submitcassummaryrequest"
    GetCasDocumentUrl: "https://simulator.demo.pointz.in:8080/mfcentral/api/client/v1/getcasdocument"
    GetTransactionStatusUrl: "https://simulator.demo.pointz.in:8080/mfcentral/api/client/v1/getTransactionStatus"


  ## aa smart parser
  InHouseAAParserURL: "https://smart-parser.data-dev.pointz.in/parse"
  InHouseAABulkParserURL: "https://smart-parser.data-dev.pointz.in/bulk_parse"

  #SMS Service
  Exotel:
    URL: "https://%s:%<EMAIL>/v1/Accounts/%s/SMS"
    AccountSid: "epifi2"
    SenderId: "***********"
  Twilio:
    URL: "https://api.twilio.com/2010-04-01/Accounts/%s/Messages"
    SenderId: "***********"
  AclEpifi:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "epifdalt"
    SenderId: "FiMony"
  AclFederal:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "fbefidem"
    SenderId: "FedFib"
  AclEpifiOtp:
    FallbackURL: "https://dmzotp.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    URL: "https://otp2.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    AppId: "epifiotp"
    SenderId: "FiMony"
  KaleyraFederal:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  KaleyraEpifi:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FiMony"
#  KaleyraEpifiNR:
#    URL: "https://api.in.kaleyra.io/alerts/api/v4"
#    SenderId: ""
  KaleyraSmsCallbackURL: "https://vnotificationgw.demo.pointz.in/sms/callback/kaleyra/UrlListner/requestListener"
  AclWhatsapp:
    URL: "http://simulator.demo.pointz.in:8080/pull-platform-receiver/wa/messages"
    OptInURL: "http://115.113.127.155:8085/api/v1/addoptinpost"
  KaleyraFederalCreditCard:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  #Loylty Service
  Loylty:
    AuthTokenURL: "https://authuat.loylty.com/mtoken"
    GiftCardBookingURL: "https://uategvb9.loylty.com/V2/GiftCard/Request"
    CharityBookingURL: "https://uatcbkb9.loylty.com/V2/InitiateV2"
    GiftCardProductListURL: "https://uategvb9.loylty.com/V2/GiftCard/Products"
    GiftCardProductDetailURL: "https://uategvb9.loylty.com/V2/GiftCard/Products/%s"
    CreateOrderURL: "https://uatordb9.loylty.com/V2/Order"
    ConfirmOrderURL: "https://uatordb9.loylty.com/V2/Order/%s/Confirm"
    GetOrderDetailsURL: "https://uatordb9.loylty.com/V2/Order/%s"

  Qwikcilver:
    GetAuthorizationCodeBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/authorization-code"
    GetAccessTokenBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/access-token"
    CreateOrderBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/create-order"
    GetActivatedCardDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/activated-card-details/%s"
    GetCategoryDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/category-details"
    GetOrderStatusBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/order-status/%s"
    GetProductDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/product-details/%s"
    GetProductListBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/product-list/%s"
    AccessTokenValidityDuration: "5s"
    MailOrderDetailsTo: "<EMAIL>"

  Thriwe:
    BaseUrl: "https://staging-india-api-gateway.thriwe.com"

  Riskcovry:
    BaseUrl: "https://api.uat-riskcovry.com/api/partner"

  Onsurity:
    BaseUrl: "https://partner.preprod.onsurity.com"

  Karvy:
    KarviAppId: "RIA"
    AgentCode: "INA200015185"
    BranchCode: "R999"
    BucketName: "epifi-demo-mutualfund-karvy"
    FATCAFileURL: "https://simulator.demo.pointz.in:8080/karvy/ProcessFATCAFeedFile"
    OrderFeedFileSyncURL: "https://simulator.demo.pointz.in:8080/karvy/ProcessOrderFeedFileSync"
    OrderFeedFileV2SyncURL: "https://simulator.demo.pointz.in:8080/karvy/ProcessOrderFeedFileSync"
    NFTFileUploadURL: "https://simulator.demo.pointz.in:8080/karvy/NFTFileUploadURL"
    GetFolioDetailsURL: "https://simulator.demo.pointz.in:8080/karvy/GetFolioDetailsURL"

  #json file path
  PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
  PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
  PayAckStatusCodeJson: "./mappingJson/ackStatusCode.json"

  DepositAckStatusCodeFilePath: "./mappingJson/depositAckStatusCode.json"
  DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"

  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  SIResponseStatusCodeFilePath: "./mappingJson/siResponseStatusCodes.json"

  Federal:
    PayIntraBankURL: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/federal/intraBank"
    PayNEFTURL: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/federal/neft"
    PayIMPSURL: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/federal/imps"
    PayRTGSURL: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/federal/rtgs"
    PayStatusURL: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/federal/enquiry"
    PayIntraBankDepositURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/OwnDepositAccFT"
    RemitterDetailsFetchUrl: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/federal/GetRemitterDetails"
    RemitterDetailsV1FetchUrl: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/federal/GetRemitterDetailsV1"
    # TODO(Sundeep): Fill the correct URL
    BeneficiaryNameLookupUrl: ""
    GetCsisStatusUrl: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/federal/CSISStatusCheck"

    PayIntraBankCallbackURL: "https://vnotificationgw.demo.pointz.in/openbanking/payment/federal"
    PayNEFTCallbackURL: "https://vnotificationgw.demo.pointz.in/openbanking/payment/federal"
    PayIMPSCallbackURL: "https://vnotificationgw.demo.pointz.in/openbanking/payment/federal"
    PayRTGSCallbackURL: "https://vnotificationgw.demo.pointz.in/openbanking/payment/federal"

    # B2C Payments
    PayB2CIntraBankURL: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/B2C/federal/intraBank"
    PayB2CIntraBankCallbackURL: "https://vnotificationgw.demo.pointz.in/openbanking/payment/b2c/federal"
    PayB2CStatusURL: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/B2C/federal/enquiry"
    PayB2CImpsURL: "https://simulator.demo.pointz.in:9091/openbanking/fundtransfer/B2C/federal/imps"
    PayB2CImpsCallbackURL: "https://vnotificationgw.demo.pointz.in/openbanking/payment/b2c/federal"

    # Shipping Preference Service
    ShippingAddressUpdateURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/ShipAddrModification"
    ShippingAddressUpdateCallbackURL: "https://vnotificationgw.demo.pointz.in/openbanking/shipping_preference/federal"

    # Card Service
    DebitCardCreateURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardCreation"
    DebitCardCreateCallbackURL: "https://vnotificationgw.demo.pointz.in/openbanking/card/federal"
    DebitCardActivateURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardActivation"
    DebitCardEnquiryUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardEnqService"
    DebitCardPinSetUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinChangeUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinResetUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinValidationUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardBlockUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardSuspendOnOffUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardLocationOnOffUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardECommerceOnOffUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardLimitEnquiry: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/LimitEnquiry"
    DebitCardUpdateLimit: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/LimitUpdate"
    DebitCardDeliveryTracking: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/DeliveryTracking"
    DebitCardCVVEnquiryUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/CVVEnquiry"
    DebitCardConsolidatedCardControlUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/FHMCardONOFF"
    DebitCardPhysicalDispatchUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking-card/cardDispatch"
    DebitCardPhysicalDispatchCallbackUrl: "https://vnotificationgw.demo.pointz.in/openbanking/cardPhysicalDispatch/federal"

    # PAN Service
    PANValidationURL: "https://simulator.demo.pointz.in:8080/panValidation"
    PANAadhaarValidationURL: "https://simulator.demo.pointz.in:8080/panAadhaarValidation"

    EkycNameDobValidationURL: "https://simulator.demo.pointz.in:8080/ekyc/namedob/validation"
    AadharMobileValidationURL: "https://simulator.demo.pointz.in:8080/aadharmobilevalidate"
    ShareDocWithVendorURL: ""

    # UN Name Check Service
    UNNameCheckURL: "https://simulator.demo.pointz.in:8080/UNNameCheck"

    # Device Re-registration / Profile Update API
    DeviceReRegURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/device/re-registration"
    DeviceReRegCallbackUrl: "https://vnotificationgw.demo.pointz.in/openbanking/auth/federal/user-device/re-register"

    # Device De-registration / Deactivate User
    DeviceDeRegURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/user-device/deactivation"

    # Generate OTP
    GenerateOTPURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/auth/generate-otp"

    DeviceReactivationURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/user-device/reactivate"
    # Deposit service
    CreateFDURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/CreateFD"
    CreateSDURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/CreateSD"
    CreateRDURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/CreateRD"
    AutoRenewFdURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/AutoRenewFd"
    CloseDepositAccountURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/ClosingDepositAcc"
    CheckDepositAccountStatusURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/DepositEnq"
    GetDepositAccountDetailURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/GetAccDetails"
    GetPreClosureDetailURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/digitalCredit/v1.0.0/DepositAcctPreclosEnq"
    DepositListAccountURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/GetAccList"
    InterestRateInfoURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/interestRateInfo"
    CalculateInterestDetailsURL: "https://simulator.demo.pointz.in:9091/fedbnk/uat/neobanking/CalculateInterestDetailsURL"
    CreateDepositCallbackUrl: "https://vnotificationgw.demo.pointz.in/openbanking/deposit/federal/create"
    PreCloseDepositCallbackUrl: "https://vnotificationgw.demo.pointz.in/openbanking/deposit/federal/preclose"
    AutoRenewFdCallbackUrl: "https://vnotificationgw.demo.pointz.in/openbanking/deposit/federal/AutoRenewFd"

    # Standing Instruction service
    CreateSIUrl: "https://simulator.demo.pointz.in:9091/standinginstruction/federal/sicreate"
    ExecuteSIUrl: "https://simulator.demo.pointz.in:9091/standinginstruction/federal/siexecute"
    SICallbackUrl: "https://vnotificationgw.demo.pointz.in/openbanking/payment/federal"
    ModifySIUrl: "https://simulator.demo.pointz.in:9091/standinginstruction/federal/simodify"
    RevokeSIUrl: "https://simulator.demo.pointz.in:9091/standinginstruction/federal/sirevoke"

    # csv file path
    CityCodesCsv: "./mappingCsv/cityCodes.csv"
    StateCodesCsv: "./mappingCsv/stateCodes.csv"
    CountryCodesCsv: "./mappingCsv/countryCodes.csv"

    #Account
    OpeningBalanceURL: "https://simulator.demo.pointz.in:9091/openbanking/accounts/federal/GetAccStatement"
    ClosingBalanceURL: "https://simulator.demo.pointz.in:9091/openbanking/accounts/federal/GetClosingBalance"
    AccountStatementURL: "https://simulator.demo.pointz.in:9091/openbanking/accounts/federal/GetAccStatement"
    EnquireBalanceV1URL: "https://simulator.demo.pointz.in:9091/openbanking/accounts/federal/GetBalanceV1"
    MiniStatementURL: "https://simulator.demo.pointz.in:9091/openbanking/accounts/federal/GetMiniStatement"
    AccountStatusURL: "https://simulator.demo.pointz.in:9091/openbanking/accounts/federal/AccountStatusEnquiry"

    # Partner SDK
    GetSessionParamsUrl: "https://simulator.demo.pointz.in:9091/fedbnk/uat/partnersdk/GetSessionParams"

    # Enquiry Service Url
    CustomerCreationEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/CustomerCreationEnquiryStatus"
    AccountCreationEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/AccountCreationEnquiryStatus"
    CardCreationEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/CardCreationEnquiryStatus"
    DeviceReRegistrationDetailsEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/DeviceReRegistrationDetailsEnquiryStatus"
    MailingAddressModifyDetailsEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/MailingAddressModifyDetailsEnquiryStatus"
    ShippingAddressUpdateEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/ShippingAddressUpdateEnquiryStatus"
    DeviceRegistrationDetailsEnquiryStatusURL: "https://simulator.demo.pointz.in:9091/DeviceRegistrationDetailsEnquiryStatus"
    PhysicalCardDispatchDetailsEnquiryStatus: "https://simulator.demo.pointz.in:9091/PhysicalCardDispatchDetailsEnquiryStatus"

    # Chequebook Request and Track URLs
    OrderChequebookUrl: "https://simulator.demo.pointz.in:9091/fedbnk/neobanking/v1.0.0/chequeBookOrder"
    TrackChequebookUrl: "https://simulator.demo.pointz.in:9091/fedbnk/neobanking/v1.0.0/chequeBookTrack"
    IssueDigitalCancelledChequeUrl: "https://simulator.demo.pointz.in:9091/fedbnk/account_utility/v1.0.0/digitalChequeLeafIssuance"

    # Profile Update and enquiry URL
    ProfileUpdateUrl: "https://simulator.demo.pointz.in:9091/fedbnk/account_utility/v1.0.0/updateProfileAtBank"
    ProfileUpdateEnquiryUrl: "https://simulator.demo.pointz.in:9091/fedbnk/account_utility/v1.0.0/profileUpdateStatus"

    # lien service url
    LienUrl: "https://simulator.demo.pointz.in:9091/openbanking/lien/federal"

    # e-nach service url
    ListEnachUrl: "https://simulator.demo.pointz.in:9091/NACHEnquiry_API/v1/enquiry"

    FetchEnachTransactionsUrl: ""

  LeadSquared:
    CreateOrUpdateLeadUrl: "https://simulator.demo.pointz.in:8080/salaryprogram/leadsquared/CreateOrUpdateLead%s%s"

  Karza:
    GenerateSessionTokenUrl: "https://testapi.karza.in/v3/get-jwt"
    AddNewCustomerUrl: "https://app.karza.in/test/videokyc/api/v2/customers"
    UpdateCustomerV3Url: "https://app.karza.in/test/videokyc/api/v3/customers"
    AddNewCustomerV3Url: "https://app.karza.in/test/videokyc/api/v3/customers"
    GenerateCustomerTokenUrl: "https://app.karza.in/test/videokyc/api/v2/generate-usertoken"
    GetSlotUrl: "https://app.karza.in/test/videokyc/api/v2/get-slot"
    BookSlotUrl: "https://app.karza.in/test/videokyc/api/v2/book-slot"
    GenerateWebLinkUrl: "https://app.karza.in/test/videokyc/api/v2/link"
    SlotAgentsUrl: "https://app.karza.in/test/videokyc/api/v2/slot-agents"
    TransactionStatusEnquiryUrl: "https://app.karza.in/test/videokyc/api/v2/transaction-events"
    ReScheduleSlotUrl: "https://app.karza.in/test/videokyc/api/v2/reschedule-customer"
    TriggerCallback: "https://app.karza.in/test/videokyc/api/v2/trigger-callback"
    AgentDashboardUrl: "https://simulator.demo.pointz.in:8080/test/videokyc/api/agent-dashboard"
    AgentDashboardAuthUrl: "https://simulator.demo.pointz.in:8080/test/videoky/api/agent-dashboard-auth"
    EmploymentVerificationAdvancedUrl: "https://testapi.karza.in/v2/employment-verification-advanced"
    KycOcrUrl: "https://simulator.demo.pointz.in:9091/karza/v1/extract_passport"
    PassportVerificationURL: "https://simulator.demo.pointz.in:9091/karza/v1/verify_passport"

  Roanuz:
    GenerateCricketAccessTokenUrl: "https://simulator.demo.pointz.in:8080/test/rounaz/cricket"
    CricketURL: "https://simulator.demo.pointz.in:8080/test/rounaz/cricket"
    GenerateFootballAccessTokenUrl: "https://simulator.demo.pointz.in:8080/test/rounaz/football/auth/"
    FootballUrl: "https://simulator.demo.pointz.in:8080/test/rounaz/football"
    FootballAppId: "com.epififootball.www"
    FootballDeviceId: "developer"

  IPStack:
    GetLocationDetailFromIpUrl: "https://simulator.demo.pointz.in:8080/test/ipstack"

  CvlKra:
    SoapHost: "https://krapancheck.cvlindia.com"
    PanEnquiryURL: "https://simulator.demo.pointz.in:8080/CVLPanInquiry.svc"
    InsertUpdateKycURL: "https://simulator.demo.pointz.in:8080/CVLPanInquiry.svc"
    # CvlKra sftp config
    Host: "sftp.deploy.pointz.in"
    Port: 22

  NsdlKra:
    PanInquiryURL: "https://simulator.demo.pointz.in:8080/TIN/PanInquiryBackEnd"
    GenerateSignatureURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/nsdl/v1/generateSignature"
    DisableSigning: true
    PerformPanInquiryV4URL: "https://simulator.demo.pointz.in:8080/TIN/PanInquiryBackEnd"

  Manch:
    TransactionsURL: "https://simulator.demo.pointz.in:8080/app/api/transactions"
    DocumentsURL: "https://simulator.demo.pointz.in:8080/app/api/documents"
    OrgId: "TST00180"
    ReturnUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

  Digio:
    TransactionsURL: "https://ext.digio.in:444/v2/client/document"
    ExpiryInDays: 10

  WealthKarza:
    OcrURL: "https://testapi.karza.in/v3/kycocr"
    MaskAadhaar: true
    HideAadhaar: true
    Confidence: true
    CheckBlur: true
    CheckBlackAndWhite: true
    CheckCutCard: true
    CheckBrightness: true

  Experian:
    CheckCreditReportPresenceURL: "https://simulator.staging.pointz.in:8080/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportURL: "https://simulator.staging.pointz.in:8080/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportForExistingUserURL: "https://simulator.staging.pointz.in:8080/ECV-P2/content/onDemandRefresh.action"
    FetchExtendSubscriptionURL: "https://simulator.staging.pointz.in:8080/ECV-P2/content/consumerConsentReRegistration.action"

  Cibil:
    PingUrl: "https://simulator.demo.pointz.in:9091/cibil/consumer/dtc/ping"
    FulfillOfferUrl: "https://simulator.demo.pointz.in:9091/cibil/consumer/dtc/fulfilloffer"
    GetAuthQuestionsUrl: "https://simulator.demo.pointz.in:9091/cibil/consumer/dtc/GetAuthenticationQuestions"
    VerifyAuthAnswersUrl: "https://simulator.demo.pointz.in:9091/cibil/consumer/dtc/VerifyAuthenticationAnswers"
    GetCustomerAssetsUrl: "https://simulator.demo.pointz.in:9091/cibil/consumer/dtc/GetCustomerAssets"
    GetProductTokenUrl: "https://simulator.demo.pointz.in:9091/cibil/consumer/dtc/GetProductWebToken"
    ProductUrlPrefix: "https://atlasls-in-live.sd.demo.truelink.com/CreditView"

  Shipway:
    BulkUploadShipmentDataUrl: "https://simulator.demo.pointz.in:8080/shipway/BulkPushOrderData"
    GetShipmentDetailsUrl: "https://simulator.demo.pointz.in:9091/shipway/GetOrderShipmentDetails"
    AddOrUpdateWebhookUrl: "https://simulator.demo.pointz.in:9091/shipway/AddOrUpdateWebhook"
    UploadShipmentDataUrl: "https://simulator.demo.pointz.in:9091/shipway/PushOrderData"

  # AA service vendor URLs
  AA:
    BaseURL: "" #Fetched from central registry
    PostConsentURL: "/Consent"
    ConsentStatusURL: "/Consent/handle"
    ConsentArtefactURL: "/Consent"
    RequestDataURL: "/FI/request"
    FetchDataURL: "/FI/fetch"
    GetAccountLinkStatusURL: "/Account/link/status"
    GenerateAccessTokenURL: "https://uattokens.sahamati.org.in/auth/realms/sahamati/protocol/openid-connect/token"
    FetchCrEntityDetailURL: "https://uatcr.sahamati.org.in/entityInfo/AA"
    ConsentUpdateURL: "/consent/update"
    AccountDeLinkURL: "/account/delink"
    UseSahamatiCrAndToken: true
    OneMoneyCrId: "onemoney-aa"
    FinvuCrId: "<EMAIL>"
    GetAccountLinkStatusBulkURL: "/Account/link/Status"
    GetHeartbeatStatusURL: "/Heartbeat"
    EpifiAaKid: "654024c8-29c8-11e8-8868-0289437bf331"
    AAClientApiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************.A-VX3lgu6T_r2FWIp2bsDAQK9vll6p4uQC_D5LwXmdo"
    SahamatiClientId: "EPIFIUAT"
    GenerateFinvuJwtTokenURL: "/web/token"
    GetBulkConsentRequestURL: "/Consent/status/bulk"
    AaSecretsVersionToUse: "V1"
    FinvuFipMetricsURL: "/fip/latest-metrics-all"
    Ignosis:
      Url: "https://simulator.demo.pointz.in:8080"

  # Bouncy castle library URLs
  BouncyCastle:
    GenerateKeyPairURL: "https://bouncycastle.deploy.pointz.in/ecc/v1/generateKey"
    GetSharedSecretURL: "https://bouncycastle.deploy.pointz.in/ecc/v1/getSharedKey"
    DecryptDataURL: "https://bouncycastle.deploy.pointz.in/ecc/v1/decrypt"
    CkycEncryptAndSignURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/ckyc/v1/encryptAndSign"
    CkycVerifyAndDecryptURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/ckyc/v1/verifyAndDecrypt"

  # Fennel Vendor URLs
  FennelFeatureStore:
    ExtractFeatureSetsURL: "https://staging.fe-r25urt0hd1.aws.fennel.ai/api/v1/extract_features"
    ExtractFeatureSetsURLV2: "https://epifi-staging.aws.fennel.ai/api/v1/query"
    ExtractFeatureSetsURLV3: "https://babel.data-dev.pointz.in/api/v1/query"
    LogDatasetsURL: "https://staging.fe-r25urt0hd1.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV2: "https://epifi-staging.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV3: "https://babel.data-dev.pointz.in/api/v1/log"
    PdScoreURL: "https://loan-default.data-dev.pointz.in/v2/loan_default"


  Ckyc:
    SearchURL: "https://simulator.demo.pointz.in:8080/Search/ckycverificationservice/verify"
    ApiVersion: "1.2"
    DownloadURL: "https://simulator.demo.pointz.in:8080/Search/ckycverificationservice/download"
    EnableCryptor: false

  InhouseNameCheckUrl: "https://simulator.demo.pointz.in:9091/namematch"
  InhouseEmployerNameMatchUrl: "https://entity-matcher.data-dev.pointz.in/api/v1/companymatch"

  InhouseOCR:
    MaskDocURL: "https://ocular.data-dev.pointz.in/v1/mask_doc"
    ExtractFieldsURL: "https://ocular.data-dev.pointz.in/v1/extract_fields"
    DetectDocumentURL: "https://ocular.data-dev.pointz.in/v1/detect_doc"

  InhousePopularFAQUrl: "http://popular-faqs.data-dev.pointz.in"

  Digilocker:
    GetAuthorizationCodeUrl: "https://simulator.demo.pointz.in:8080/public/oauth2/1/authorize"
    GetAccessTokenUrl: "https://simulator.demo.pointz.in:8080/public/oauth2/1/token"
    ClientId: "EBB0DE86"
    RedirectUri: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"
    GetListOfIssuedDocumentsUrl: "https://simulator.demo.pointz.in:8080/public/oauth2/2/files/issued"
    GetRefreshTokenUrl: "https://simulator.demo.pointz.in:8080/public/oauth2/1/token"
    GetFileFromUriUrl: "https://simulator.demo.pointz.in:8080/public/oauth2/1/file/"
    GetAadhaarInXmlUrl: "https://simulator.demo.pointz.in:8080/public/oauth2/3/xml/eaadhaar"
    TokenCacheTtl: 10m

  Liquiloans:
    Host: "https://staging-backend-v2.liquiloan.in/api/apiintegration/v3"
    SupplyIntegrationHost: "https://staging-backend-v2.liquiloan.in/api/apiintegration/v3"
    SftpHost: ""
    SftpPort: 22
    S3BucketP2PInvestmentLedger: "epifi-demo-p2p-investment-ledger"

  # TODO(@prasoon): update URL once available
  Lending:
    PreApprovedLoan:
      Federal:
        UrlLentra: "https://simulator.demo.pointz.in:9091/lending/federal/v1/enquiry"
        Url: "https://simulator.demo.pointz.in:9091"
        HttpUrl: "https://simulator.demo.pointz.in:9091"
        FetchDetailsUrl: "https://simulator.demo.pointz.in:9091/account_utility/v1.0.0/fetchLoanDetails"
        RespUrl: ""
        # TODO(@kantikumar): update sftp host once available
        SftpHost: ""
        SftpPort: 22
      # TODO(@Shivansh) update URL once available
      Liquiloans:
        Url: ""
        # This URL points to HTTP port of simulator as few Liquiloans API needs request body  in HTTP GET request, those are
        # mocked using Router and not by gRPC method.
        HttpUrl: ""
      Idfc:
        Url: ""
        Source: "FIMONEY"
      Abfl:
        Url: ""
        BreUrl: ""
        TxnDetailsUrl: ""
        PwaJourneyUrl: ""
      Moneyview:
        BaseUrl: "https://simulator.demo.pointz.in:9091/moneyview"
      Setu:
        BaseUrl: ""
      Digitap:
        UanAdvancedUrl: "https://svcdemo.digitap.work/cv/v3/uan_advanced/sync"

    CreditCard:
      M2P:
        RegisterCustomerHost: "https://kycuat.yappay.in/"
        M2PHost: "https://sit-secure.yappay.in/"
        CreditCardRepaymentHost: "https://uat-bnpl.m2pfintech.com/"
        M2PFallbackHost: "https://uat-bnpl.m2pfintech.com/"
        M2PLMSHost: "https://uat-bnpl.m2pfintech.com/"
        M2PPartnerSdkUrl: "https://uat-creditcard.m2pfintech.com/gateway/"
        M2PSetPinUrl: "https://uat-creditcard.m2pfintech.com/gateway/"
        EnableEncryption: false
      Federal:
        Url: "https://uatgateway.federalbank.co.in/fedbnk/uat/CreditCard/v1.0.0/"
        UpdateCreditCardLimitDetailsUnsecuredChannel: "M2P-FDEPIFICR"
        UpdateCreditCardLimitDetailsMassUnsecuredChannel: "M2P-FDEPIFIMASSCR"
    CreditLine:
      M2P:
        Url: "https://uat-federal-onboarding.m2pfintech.com/api/v1/customer"
    Collateral:
      LendingMFCentralConfig:
        HttpUrl:
          AuthToken: "https://simulator.demo.pointz.in:9091/mfcentral/oauth/token"
          EncryptAndSign: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
          DecryptAndVerify: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
          SubmitCasSummary: "https://simulator.demo.pointz.in:9091/mfcentral/submitcassummary"
          InvestorConsent: "https://simulator.demo.pointz.in:9091/mfcentral/investorconsent"
          GetCasDocument: "https://simulator.demo.pointz.in:9091/mfcentral/getcasdocument"
          ValidateLien: "https://simulator.demo.pointz.in:9091/mfcentral/validatelien"
          SubmitLien: "https://simulator.demo.pointz.in:9091/mfcentral/submitlien"
          InvokeRevokeLien: "https://simulator.demo.pointz.in:9091/mfcentral/validateLienInvokeRevoke"
          CheckStatus: "https://simulator.demo.pointz.in:9091/mfcentral/lienCheckStatus"
          GetTransactionStatus: "https://simulator.demo.pointz.in:9091/mfcentral/getTransactionStatus"
    SecuredLoans:
      Url: "https://simulator.demo.pointz.in:9091/fiftyfin"

  Alpaca:
    BrokerApiHost: "https://broker-api.sandbox.alpaca.markets"
    BrokerApiVersion: "v1"
    MarketApiHost: "https://data.sandbox.alpaca.markets"
    MarketApiVersion: "v2"
    StreamApiHost: "simulator.staging.pointz.in:8080"
    StreamApiPath: "test/v2/iex"
    StreamApiScheme: "wss"
    ShouldUseSimulatedEnvForWs: true
    OrderEventsApiPath: "/test/alpaca/v1/trade/events"
    AccountEventsApiPath: "/test/alpaca/v1/account/events"
    FundTransferEventsPath: "/test/alpaca/v1/events/transfers/status"
    JournalEventsPath: "/test/alpaca/v1/events/journals/status"
    BrokerEventsApiHost: "simulator.demo.pointz.in:8080"
    BrokerEventsApiScheme: "https"
    ShouldUseSimulatedEnvForEvents: true
    DataMarketBetaVersion: "v1beta1"

  MorningStar:
    TokenCacheTtl: 12h
    EquityAPIURL: "https://equityapi.morningstar.com"
    ApiUrl: "https://api.morningstar.com"

  FederalInternationalFundTransfer:
    URL: "https://simulator.demo.pointz.in:8080/test/federal"
    CheckLRSEligibilityPrecision: 9

  Esign:
    Leegality:
      Url: "https://sandbox.leegality.com/api/v3.0/"

  ProfileValidation:
    Federal:
      Url: "https://simulator.demo.pointz.in:9091"

  #GPlace Vendor URLs
  GPlace:
    FindPlaceReqURL: "https://maps.googleapis.com/maps/api/place/findplacefromtext/json"
    GetPlaceDetailsReqURL: "https://maps.googleapis.com/maps/api/place/details/json"

  GoogleReverseGeocodingUrl: "https://simulator.demo.pointz.in:9091/get-address-coordinate"
  InhouseLocationServiceUrl: "https://geo.data-dev.pointz.in"
  MaxmindIp2CityUrlPrefix: "https://simulator.demo.pointz.in:9091/get-address-ip/"

  BureauPhoneNumberDetailsUrl: "https://simulator.demo.pointz.in:9091/v1/phone-network"

  #DronaPay
  DronapayHostURL: "https://riskuat.dronapay.pointz.in/springapi"

  InhouseRiskServiceURL: "https://simulator.demo.pointz.in:9091/risk"
  InhouseRiskServiceURLV1: "https://simulator.demo.pointz.in:9091/risk"
  InhouseReonboardingRiskServiceURL: "https://onboarding-risk-detection.data-dev.pointz.in/v1/afu_risk_detection"

  "InhouseMerchantResolutionServiceUrl": "https://merchant.data-dev.pointz.in/resolution"

  Aml:
    Tss:
      ScreeningUrl: "https://simulator.demo.pointz.in:8080/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"

  IncomeEstimatorConf:
    InhouseIncomeEstimatorURL: "https://simulator.demo.pointz.in:9091/get-income-estimate"

  LocationModel:
    InHouseUrl: "http://onboarding-risk-detection.data-dev.pointz.in/v1/geo_score"
    RiskSeverityValToEnumMapping:
      "LOW": "RISK_SEVERITY_LOW"
      "MEDIUM": "RISK_SEVERITY_MEDIUM"
      "HIGH": "RISK_SEVERITY_HIGH"
      "CRITICAL": "RISK_SEVERITY_CRITICAL"

  EpanConfig:
    FederalConfig:
      SftpConn:
        Host: ""
        Port: 80

  EnachConfig:
    FederalConfig:
      SftpConn:
        Host: ""
        Port: 80
        User: ""
        Password: ""
        SshKey: ""
      FederalSFTPUploadPath: "/data/"
      FederalSFTPDownloadPath: "/data/"
      FileTypeToSFTPPathMap:
        FILE_TYPE_PRESENTATION_FILE: "/data/"
        FILE_TYPE_PRESENTATION_ACK_FILE: "/data/"
        FILE_TYPE_PRESENTATION_RESPONSE_FILE: "/data/"

Server:
  Ports:
    GrpcPort: 8081
    GrpcSecurePort: 9522
    HttpPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    #M2P
    M2PSecrets: "demo/vendorgateway/m2p"
    M2PSecuredCardSecrets: "demo/vendorgateway/m2p/secured"
    M2PMassUnsecuredCardSecrets: "demo/vendorgateway/m2p/mass-unsecured"
    EpifiM2pRsaPrivateKey: "demo/vendorgateway/epifi-m2p-private-key"
    EpifiM2pRsaPublicKey: "demo/vendorgateway/m2p-public-key"

    # In-house BRE
    InHouseBreBearer: "demo/vendorgateway/inhouse-bre-bearer"

    #Federal
    EpifiFederalPgpPrivateKey: "demo/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "demo/pgp/pgp-federal-dummy-key-for-sim"
    EpifiFederalPgpPassphrase: "demo/pgp/pgp-epifi-key-passphrase-fed-api"
    EpifiFederalUPIPrivateKey: "demo/vendorgateway/upi-xml-signature"
    EpifiFederalUPIFallbackPrivateKey: "demo/vendorgateway/upi-xml-signature"
    SenderCode: "demo/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "demo/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "demo/vg-vn-vgpci/federal-auth-service-access-code"
    ClientId: "demo/vg-vgpci/federal-auth-client-id"
    ClientSecretKey: "demo/vg-vgpci/federal-auth-client-secret-key"
    EpifiFederalCardDataPrivateKeyFallBack: "demo/vg-vngw-vgpci/rsa-federal-card-data"
    EpifiFederalCardDataPrivateKey: "demo/vg-vngw-vgpci/rsa-federal-card-data"

    #Closing Balance params
    ClosingBalanceCredentials: "demo/vendorgateway/federal-closing-balance-secrets"

    GetBalanceCredentialsV1: "demo/vendorgateway/federal-get-balance-v1-secrets"

    GetRemitterDetailsCredentials: "demo/vendorgateway/federal-get-remitter-details-secrets"
    GetRemitterDetailsV1Credentials: "demo/vendorgateway/federal-get-remitter-details-secrets-v1"
    GetCsisStatusCredentials: "demo/vendorgateway/federal-get-csis-status-secrets"

    #FCM
    FCMServiceAccountCredJson: "demo/vendorgateway/fcm-account-credentials"
    #Sendgrid
    SendGridAPIKey: "demo/vendorgateway/sendgrid-api-key"
    #TLS certs
    SimulatorCert: "demo/vg-vgpci/tls-client-cert-for-sim"
    EpiFiFederalClientSslCert: "demo/vg-vgpci/tls-client-cert-for-federal"
    EpiFiFederalClientSslKey: "demo/vg-vgpci/tls-client-priv-key-for-federal"
    #Freshdesk
    FreshdeskApiKey: "demo/vendorgateway/freshdesk-api-key"
    EpifiTechRiskFreshdeskApiKey: "demo/vendorgateway/epifitech-risk-freshdesk-api-key"
    #Ozonetel
    OzonetelApiKey: "demo/vendorgateway/ozonetel-api-key"
    #Freshchat
    FreshchatApiKey: "demo/vendorgateway/freshchat-api-key"
    # Pan Validation
    PanValidationAccessId: "demo/vendorgateway/federal-auth-pan-validation-access-id"
    PanValidationAccessCode: "demo/vendorgateway/federal-auth-pan-validation-access-code"

    #Loylty
    LoyltyClientId: "demo/vendorgateway/loylty-auth-client-id"
    LoyltyClientKey: "demo/vendorgateway/loylty-auth-client-key"
    LoyltyClientSecret: "demo/vendorgateway/loylty-auth-client-secret"
    LoyltyClientEncryptionKey: "demo/vendorgateway/loylty-auth-client-encryption-key"
    LoyltyEGVModuleId: "demo/vendorgateway/loylty-auth-egv-module-id"
    LoyltyCharityModuleId: "demo/vendorgateway/loylty-auth-charity-module-id"
    LoyltyApplicationId: "demo/vendorgateway/loylty-auth-application-id"
    LoyltyProgramId: "demo/vendorgateway/loylty-auth-program-id"

    #Qwikcilver
    QwikcilverSecrets: "demo/vendorgateway/qwikcilver-secrets"

    #Thriwe
    ThriweSecrets: "demo/vendorgateway/thriwe-secrets"

    #Riskcovry
    RiskcovrySecrets: "demo/vendorgateway/riskcovry-secrets"

    #Onsurity Secrets
    OnsuritySecrets: "demo/vendorgateway/onsurity-secrets"


    # UPI API
    UPISenderUserId: "demo/vendorgateway/federal-upi-sender-user-id"
    UPISenderPassword: "demo/vendorgateway/federal-upi-sender-password"
    UPISenderCode: "demo/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdfede: "demo/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordfede: "demo/vendorgateway/federal-upi-sender-password"
    UPISenderCodefede: "demo/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdIosAddFunds: "demo/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordIosAddFunds: "demo/vendorgateway/federal-upi-sender-password"
    UPISenderCodeIosAddFunds: "demo/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdfifederal: "demo/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordfifederal: "demo/vendorgateway/federal-upi-sender-password"
    UPISenderCodefifederal: "demo/vendorgateway/federal-upi-sender-code"

    # SMS API keys
    TwilioAccountSid: "demo/vendorgateway/twilio-account-sid"
    TwilioApiKey: "demo/vendorgateway/twilio-api-key"
    ExotelApiKey: "demo/vendorgateway/exotel-api-key"
    ExotelApiToken: "demo/vendorgateway/exotel-api-token"
    AclEpifiUserId: "demo/vendorgateway/acl-epifi-user-id"
    AclEpifiPassword: "demo/vendorgateway/acl-epifi-password"
    AclFederalUserId: "demo/vendorgateway/acl-federal-user-id"
    AclFederalPassword: "demo/vendorgateway/acl-federal-password"
    AclEpifiOtpUserId: "demo/vendorgateway/acl-epifi-otp-user-id"
    AclEpifiOtpPassword: "demo/vendorgateway/acl-epifi-otp-password"
    KaleyraFederalApiKey: "demo/vendorgateway/kaleyra-federal-api-key"
    KaleyraEpifiApiKey: "demo/vendorgateway/kaleyra-epifi-api-key"
    # KaleyraEpifiNRApiKey: "demo/vendorgateway/kaleyra-epifi-nr-api-key" - uncomment when demo key is created.
    AclWhatsappUserId: "demo/vendorgateway/acl-whatsapp-user-id"
    AclWhatsappPassword: "demo/vendorgateway/acl-whatsapp-password"
    WhatsappEnterpriseId: "demo/vendorgateway/whatsapp-enterprise-id"
    WhatsappEnterpriseToken: "demo/vendorgateway/whatsapp-enterprise-token"
    KaleyraFederalCreditCardApiKey: "demo/vendorgateway/kaleyra-federal-cc-api-key"
    # Video kyc KARZA api key
    KarzaVkycApiKey: "demo/vendorgateway/karza-vkyc-apikey"
    KarzaVkycPriorityApiKey: "demo/vendorgateway/karza-vkyc-priority-apikey"

    # Rounza cricket api's project and api key
    RounazCricketProjectKey: "demo/vendorgateway/roanuz-project-key"
    RounazCricketApiKey: "demo/vendorgateway/roanuz-api-key"

    # Rounaz football api's access and secret key
    RounazFootballAccessKey: "demo/vendorgateway/roanuz-football-access-key"
    RounazFootballSecretKey: "demo/vendorgateway/roanuz-football-secret-key"

    # B2C payments keys
    B2cUserId: "demo/vendorgateway/federal-auth-b2c-payment-user-id"
    B2cPassword: "demo/vendorgateway/federal-auth-b2c-payment-password"
    B2cSenderCodeKey: "demo/vendorgateway/federal-auth-b2c-payment-sender-code"

    # ipstack access key
    IpstackAccessKey: "demo/vendorgateway/ipstack-access-key"

    # Shipway username and password
    ShipwayUsername: "demo/vendorgateway/shipway-username"
    ShipwayPassword: "demo/vendorgateway/shipway-password"

    # client api key for aa
    AaVgSecretsV1: "demo/vendorgateway/aa-sahamati-secrets-v1"
    AaVgVnSecretsV1: "demo/vg-vn/aa-secrets-v1"

    # Manch secure key and template key
    ManchSecureKey: "demo/vendorgateway/manch-secure-key"
    ManchTemplateKey: "demo/vendorgateway/manch-template-key"

    # Experian Credit Report
    ExperianCreditReportPresenceClientName: "demo/vendorgateway/experian-credit-report-presence-client-name"
    ExperianCreditReportFetchClientName: "demo/vendorgateway/experian-credit-report-fetch-client-name"
    ExperianCreditReportForExistingUserClientName: "demo/vendorgateway/experian-credit-report-for-existing-user-client-name"
    ExperianExtendSubscriptionClientName: "demo/vendorgateway/experian-extend-subscription-client-name"
    ExperianVoucherCode: "demo/vendorgateway/experian-credit-report-voucher-code"

    SeonClientApiKey: "demo/vendorgateway/seon-api-key"

    CAMSKey: "demo/investment-vendorgateway/cams-key"

    KarvyKey: "demo/investment-vendorgateway/karvy-key"

    SmallCaseKey: "demo/vendorgateway/smallcase-key"

    MFCentralKey: "demo/vendorgateway/mfcentral-key"

    #ckyc
    CkycFiCode: "demo/vendorgateway/ckyc-fi-code"

    # cvl secrets
    CvlSftpUser: "demo/vendorgateway/cvl-sftp-user"
    CvlSftpPass: "demo/vendorgateway/cvl-sftp-pass"
    CvlSftpUploadUser: "demo/vendorgateway/cvl-sftp-upload-user"
    CvlSftpUploadPass: "demo/vendorgateway/cvl-sftp-upload-pass"
    CvlKraPassKey: "demo/vendorgateway/cvl-kra-pass-key"
    CvlKraPosCode: "demo/vendorgateway/cvl-kra-pos-code"
    CvlKraUserName: "demo/vendorgateway/cvl-kra-user-name"
    CvlKraPassword: "demo/vendorgateway/cvl-kra-password"
    CvlSftpSshKey: "demo/vendorgateway/cvl-sftp-ssh-key"
    CvlSecrets: "demo/vendorgateway/cvl-secrets"

    #digilocker
    DigilockerClientSecret: "demo/vendorgateway/digilocker-client-secret"

    # p2p Investment secrets
    p2pInvestmentLiquiloansSftpUser: "demo/vendorgateway/p2p-investment-liquiloans-sftp-user"
    p2pInvestmentLiquiloansSftpPassword: "demo/vendorgateway/p2p-investment-liquiloans-sftp-pass"

    # TODO(@prasoon): Add secret keys json
    # Lending keys
    PreApprovedLoanFederalSecrets: "demo/vendorgateway/lending-preapprovedloans-secrets"
    FederalSftpSshKey: "demo/vendorgateway/federal-sftp-ssh-key"
    PreApprovedLoanSecrets: "demo/vendorgateway/lending-preapprovedloans-secrets"

    # Leegality
    LeegalitySecret: "demo/vendorgateway/esign-leegality-secrets"

    #Liquiloans
    LiquiloansMid: "demo/vendorgateway/liquiloans-mid"
    LiquiloansKey: "demo/vendorgateway/liquiloans-key"
    LiquiloansSecrets: "demo/vendorgateway/liquiloans-secrets"

    #GPlace api key
    GPlaceApiKey: "demo/vendorgateway/gplace-api-key"

    # karza api keys
    KarzaKey: "demo/vendorgateway/karza-key"
    TartanKey: "demo/vendorgateway/tartan-key"

    VKYCAgentDashboardSecrets: "demo/vendorgateway/vkyc-agent-dash"

    # DronaPay
    DronaPayKey: "demo/vendorgateway/dronapay-key"

    GeolocationKey: "demo/vendorgateway/geolocation-key"

    # Secrets of payu
    PayuToken: "demo/vendorgateway/payu-token"
    PayuApiKey: "demo/vendorgateway/payu-key"

    MaxmindSecrets: "demo/vendorgateway/maxmind-secrets"
    BureauSecrets: "demo/vendorgateway/bureau-secrets"

    SignzySecrets: 'demo/vendorgateway/signzy-secrets'

    AlpacaSecrets: "demo/vendorgateway/alpaca-secrets"

    FederalInternationalFundTransferSecrets: "demo/vendorgateway/federal-internationalfundtransfer-secrets"

    FederalProfileValidationSecrets: 'demo/vendorgateway/hunter-secrets'

    MorningStarSecrets: "demo/vendorgateway/morningstar-secrets"
    MorningStarAccountSecrets: "demo/vendorgateway/morningstar-account-secrets"

    DepositInterestRateInfoSecrets: "demo/vendorgateway/deposit-interest-rate-info-secrets"

    TssApiToken: "demo/vendorgateway/tss-api-token"

    VistaraSecrets: "demo/vendorgateway/vistara-secrets"

    FederalDepositSecrets: "demo/vendorgateway/federal-deposit-secrets"

    # Fennel Secrets
    FennelFeatureStoreSecrets: "demo/vendorgateway/fennel-secrets"

    # LAMF secrets
    LendingMFCentralSecrets: '{"ClientId" : "fi", "ClientSecret" : "secret", "LenderCode" : "001", "Password" : "wordpass", "UserName" : "conan"}'

    # Lentra secrets
    LentraSecrets: "demo/vendorgateway/lentra-secrets"

    EpifiFederalEpanSftpSecrets: "demo/vendorgateway/epifi-federal-epan-sftp-secrets"

    EpifiFederalEnachSftpSecrets: "demo/vendorgateway/epifi-federal-enach-sftp-secrets"

    CredgenicsAuthToken: "demo/vendorgateway/credgenics"

    LendingFiftyFinLamfSecrets: "demo/vendorgateway/fiftyfin-lamf-secrets"

    KarzaPanProfileKey: "demo/vendorgateway/pan-profile-karza-key"

    CibilSecrets: "demo/vendorgateway/cibil"

DisputeSFTP:
  user: ""
  password: ""
  host: ""
  port: 80
  S3BucketName: "epifi-federal-disputes"

Flags:
  TrimDebugMessageFromStatus: true
  TokenValidation: true
  AllowSpecialCharactersInAddress: true
  EnableFennelClusterV3: true

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendorgateway/secure.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.demo-common-cache-redis.wqltco.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 15

FcmAnalyticsLabel: "demo-push-notification"

AwsSes:
  FiMoneyArn: "arn:aws:ses:ap-south-1:632884248997:identity/fi.money"
  FiCareArn: "arn:aws:ses:ap-south-1:632884248997:identity/fi.care"
  StockGuardianArn: "arn:aws:ses:ap-south-1:632884248997:identity/stockguardian.in"

# generic downtime handler for vg
# the vendor name is the enum value with config values like IsEnabled, StartTime, EndTime
DowntimeConfig:
  NSDL:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-03-15 15:04:05"
        EndTimestamp: "2022-03-16 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CVLKRA:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CKYC:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "This is due to a downtime at our vendor partner. This should be up by %s. We will notify you once its up."
  DIGILOCKER:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  MANCH:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-08-26 23:00:00"
        EndTimestamp: "2022-08-27 09:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "demo/pgp/v1/pgp-epifi-fed-api"
  ExternalEntity:
    - Secret: "demo/pgp/v1/federal-simulator"

