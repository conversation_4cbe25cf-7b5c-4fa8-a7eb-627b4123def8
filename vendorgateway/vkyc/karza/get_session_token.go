package karza

import (
	"net/http"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	pb "github.com/epifi/gamma/api/vendorgateway/vkyc"
	"github.com/epifi/gamma/api/vendors/karza"
)

type GenerateSessionTokenRequest struct {
	Method string
	Req    *pb.GenerateSessionTokenRequest
	Url    string
	ApiKey string
	// api key provided by karza to give users priority compared to the general
	PriorityApiKey            string
	KarzaReVkycPriorityApiKey string
	KarzaLoanVkycApiKey       string
}

func (g GenerateSessionTokenRequest) Marshal() ([]byte, error) {
	productIdList := []string{}
	for _, productId := range g.Req.GetProductIdList() {
		switch productId {
		case pb.ProductId_VIDEO_KYC:
			productIdList = append(productIdList, "video_kyc")
		case pb.ProductId_EPAN:
			productIdList = append(productIdList, "epan")
		}
	}
	apiRequest := &karza.GenerateSessionTokenRequest{
		ProductId: productIdList,
	}
	return protojson.Marshal(apiRequest)
}

func (g GenerateSessionTokenRequest) HTTPMethod() string {
	return g.Method
}

func (g GenerateSessionTokenRequest) URL() string {
	return g.Url
}

func (g GenerateSessionTokenRequest) GetResponse() vendorapi.Response {
	return &GenerateSessionTokenResponse{}
}

func (g GenerateSessionTokenRequest) Add(req *http.Request) *http.Request {
	switch g.Req.GetVkycPriorityType() {
	case pb.VKYCPriorityType_VKYC_PRIORITY_TYPE_RE_VKYC:
		req.Header.Add("x-karza-key", g.KarzaReVkycPriorityApiKey)
	case pb.VKYCPriorityType_VKYC_PRIORITY_TYPE_DEFAULT_PRIORITY:
		req.Header.Add("x-karza-key", g.PriorityApiKey)
	case pb.VKYCPriorityType_VKYC_PRIORITY_TYPE_LOAN:
		req.Header.Add("x-karza-key", g.KarzaLoanVkycApiKey)
	default:
		req.Header.Add("x-karza-key", g.ApiKey)
	}
	return req
}

type GenerateSessionTokenResponse struct {
}

func (g GenerateSessionTokenResponse) Unmarshal(b []byte) (proto.Message, error) {
	apiResp := &karza.GenerateSessionTokenResponse{}
	un := protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}
	if err := un.Unmarshal(b, apiResp); err != nil {
		logger.ErrorNoCtx("error in unmarshalling generate session token response for karza api", zap.Error(err))
		return nil, errors.Wrap(err, "error in unmarshalling generate session token response for karza api")
	}
	if apiResp.GetResult().GetData().GetKarzaToken() != "" && apiResp.GetStatusCode() == 101 {
		return &pb.GenerateSessionTokenResponse{
			Status:     rpcPb.StatusOk(),
			RequestId:  apiResp.GetRequestId(),
			KarzaToken: apiResp.GetResult().GetData().GetKarzaToken(),
		}, nil
	}
	logger.ErrorNoCtx("error fetching session token from karza API", zap.Any("apiResp", apiResp))
	return &pb.GenerateSessionTokenResponse{
		Status: rpcPb.StatusInternal(),
	}, nil
}
