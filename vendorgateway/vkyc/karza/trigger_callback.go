package karza

import (
	"context"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	pb "github.com/epifi/gamma/api/vendorgateway/vkyc"
	"github.com/epifi/gamma/api/vendors/karza"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
)

type TriggerCallbackRequest struct {
	Method string
	Req    *pb.TriggerCallbackRequest
	Url    string
}

func (g TriggerCallbackRequest) Marshal() ([]byte, error) {
	callbackType, err := getCallbackType(g.Req.GetCallbackType())
	if err != nil {
		return nil, errors.Wrap(err, "error in getting callback type")
	}
	apiRequest := &karza.TriggerCallbackRequest{
		Type:          callbackType,
		TransactionId: g.Req.GetTransactionId(),
	}
	return protojson.Marshal(apiRequest)
}

func getCallbackType(callbackType pb.CallbackType) (string, error) {
	switch callbackType {
	case pb.CallbackType_CALLBACK_TYPE_AGENT:
		return "agent", nil
	case pb.CallbackType_CALLBACK_TYPE_AUDITOR:
		return "auditor", nil
	default:
		return "", fmt.Errorf("unexpected callback type %v", callbackType)
	}
}

func (g TriggerCallbackRequest) HTTPMethod() string {
	return g.Method
}

func (g TriggerCallbackRequest) URL() string {
	return g.Url
}

func (g TriggerCallbackRequest) GetResponse() vendorapi.Response {
	return TriggerCallbackResponse{}
}

func (g TriggerCallbackRequest) Add(req *http.Request) *http.Request {
	req.Header.Add("karzatoken", g.Req.GetKarzaToken())
	return req
}

type TriggerCallbackResponse struct {
}

func (g TriggerCallbackResponse) Unmarshal(b []byte) (proto.Message, error) {
	apiResp := &karza.TriggerCallbackResponse{}
	un := protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}
	if err := un.Unmarshal(b, apiResp); err != nil {
		logger.ErrorNoCtx("error in unmarshalling vkyc trigger callback response", zap.Error(err))
		return nil, errors.Wrap(err, "error in unmarshalling vkyc trigger callback response")
	}
	if apiResp.GetRequestId() != "" && apiResp.GetStatusCode() == 101 {
		return &pb.TriggerCallbackResponse{
			Status:    rpcPb.StatusOk(),
			RequestId: apiResp.GetRequestId(),
		}, nil
	}
	logger.ErrorNoCtx("error triggering vkyc callback", zap.Any("apiResp", apiResp))
	return &pb.TriggerCallbackResponse{
		Status: rpcPb.StatusInternal(),
	}, nil
}

func (g TriggerCallbackResponse) HandleHttpError(ctx context.Context, httpStatus int, _ []byte) (proto.Message, error) {
	// TODO (RISHU SAHU): remove the log once it is stable
	logger.Error(ctx, "customer token got expired")
	return &pb.TriggerCallbackResponse{
		Status: vendorapi.GetRpcStatusFromHttpCode(ctx, httpStatus),
	}, nil
}
