package federal

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	vgLendingPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	vendorFederalPb "github.com/epifi/gamma/api/vendors/federal/lending"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/federal"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

type GetInstantLoanApplicationRequest struct {
	*federal.DefaultHeaderAdder

	Method string
	Req    *vgLendingPb.GetInstantLoanApplicationRequest
	Conf   *config.FederalPreapprovedLoan
}

func (c *GetInstantLoanApplicationRequest) HTTPMethod() string {
	return c.Method
}

func (c *GetInstantLoanApplicationRequest) URL() string {
	return c.Conf.Url + "/fedInstantLoan/getApplication"
}

func (c *GetInstantLoanApplicationRequest) GetResponse() vendorapi.Response {
	return &GetInstantLoanApplicationRes{}
}

type GetInstantLoanApplicationRes struct {
}

func (c *GetInstantLoanApplicationRequest) Marshal() ([]byte, error) {
	intProcessingFee, _ := money.ToPaise(c.Req.GetProcessingFee())
	processingFee := float64(intProcessingFee) / 100
	loanAmount, _ := money.ToDecimal(c.Req.GetLoanAmount()).Round(2).Float64()
	emiAmount, _ := money.ToDecimal(c.Req.GetEmiAmount()).Round(2).Float64()

	requestPayload := &vendorFederalPb.GetInstantLoanApplicationRequest{
		InstantLoanProcessRequest: &vendorFederalPb.GetInstantLoanApplicationRequest_InstantLoanProcessRequest{
			Header: &vendorFederalPb.Header{
				UserName: c.Conf.UserName,
				Password: c.Conf.Password,
			},
			Body: &vendorFederalPb.GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body{
				SenderCode:              c.Conf.SenderCode,
				Otp:                     c.Req.GetOtp(),
				OfferId:                 c.Req.GetOfferId(),
				PhoneNumber:             c.Req.GetPhoneNumber().ToStringNationalNumber(),
				MaskedAccountNumber:     c.Req.GetMaskedAccountNumber(),
				ProcessingFee:           processingFee,
				CustomerIpDeviceDetails: c.Req.GetCustomerDeviceIp(),
				ApplicationId:           c.Req.GetApplicationId(),
				LoanAmount:              loanAmount,
				EmiAmount:               emiAmount,
				InterestRate:            c.Req.GetInterestRate(),
				TenureMonths:            c.Req.GetTenureMonths(),
			},
		},
	}
	return protojson.Marshal(requestPayload)
}

func (c GetInstantLoanApplicationRequest) CanLogUnredactedEncryptedPayload() bool {
	return true
}

//nolint:dupl
func (c *GetInstantLoanApplicationRes) Unmarshal(b []byte) (proto.Message, error) {
	res := vendorFederalPb.GetInstantLoanApplicationResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to unmarshal byte array to federal proto message")
	}

	if _, exists := SuccessCodes[res.GetInstantLoanProcessResponse().GetResponseCode()]; !exists {
		return &vgLendingPb.GetInstantLoanApplicationResponse{
			Status: VendorErrorResponseCodesToRpcResponse(res.GetInstantLoanProcessResponse().GetResponseCode()),
		}, nil
	}

	return &vgLendingPb.GetInstantLoanApplicationResponse{
		ApplicationId:     res.GetInstantLoanProcessResponse().GetApplicationId(),
		Status:            rpc.StatusOk(),
		RawResponseCode:   res.GetInstantLoanProcessResponse().GetResponseCode(),
		RawResponseReason: res.GetInstantLoanProcessResponse().GetResponseReason(),
	}, nil
}

func (c GetInstantLoanApplicationRes) CanLogUnredactedEncryptedPayload() bool {
	return false
}

func (c *GetInstantLoanApplicationRes) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in FEDERAL GetInstantLoanApplication", zap.String("error", string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	return &vgLendingPb.GetInstantLoanApplicationResponse{
		Status: vendorapi.GetRpcStatusFromHttpCodeWithDebugMsg(
			ctx,
			httpStatus,
			fmt.Sprintf("Error response: %s", string(b)),
		),
	}, nil
}
