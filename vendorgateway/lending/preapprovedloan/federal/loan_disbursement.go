// nolint: dupl
package federal

import (
	"context"
	"fmt"
	"net/http"

	"go.uber.org/zap"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	vgLendingPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	vendorFederalPb "github.com/epifi/gamma/api/vendors/federal/lending"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/federal"
)

const operativeAccountNumber = "**************"

type GetLoanDisbursementRequest struct {
	Method string
	Req    *vgLendingPb.LoanDisbursementRequest
	Conf   *config.PreApprovedLoan
}

func (c *GetLoanDisbursementRequest) HTTPMethod() string {
	return c.Method
}

func (c *GetLoanDisbursementRequest) URL() string {
	return c.Conf.Federal.Url + "/digitalCredit/v1.0.0/digitalLendingControls"
}

func (c *GetLoanDisbursementRequest) GetResponse() vendorapi.Response {
	return &GetLoanDisbursementResponse{}
}

type GetLoanDisbursementResponse struct {
}

func (c *GetLoanDisbursementRequest) Marshal() ([]byte, error) {
	partnerName, partnerCode, senderCode := c.getSenderCredentials()
	var pennydropReqId string
	var pennydropTranDate string
	if c.Req.GetCredentialCategory() == vgLendingPb.CredentialCategory_CREDENTIAL_CATEGORY_NTB {
		pennydropReqId = c.Req.GetPennydropTranId()
		pennydropTranDate = datetime.DateToString(c.Req.GetPennydropTranDate(), "02-01-2006", datetime.IST)
	} else {
		pennydropReqId = senderCode
	}
	requestPayload := &vendorFederalPb.LoanDisbursementRequest{
		PartnerName:              partnerName,
		PartnerCode:              partnerCode,
		SenderCode:               senderCode,
		RequestId:                c.Req.GetRequestId(),
		CustomerId:               c.Req.GetCustomerId(),
		LoanAccountNumber:        c.Req.GetLoanAccountNumber(),
		LoanAmount:               c.Req.GetLoanAmount().GetUnits(),
		OperativeAccountNumber:   operativeAccountNumber,
		BeneficiaryIfsc:          c.Req.GetBeneficiaryAccountDetails().GetIfsc(),
		BeneficiaryAccountNumber: c.Req.GetBeneficiaryAccountDetails().GetAccountNumber(),
		BeneficiaryAccountName:   c.Req.GetBeneficiaryAccountDetails().GetAccountName(),
		PennydropReqId:           pennydropReqId,
		PaymentMode:              "IMPS",
		PennydropTranDate:        pennydropTranDate,
	}
	return protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(requestPayload)
}

func (c GetLoanDisbursementRequest) CanLogUnredactedEncryptedPayload() bool {
	return false
}

func (c *GetLoanDisbursementRequest) getSenderCredentials() (string, string, string) {
	if c.Req.GetCredentialCategory() == vgLendingPb.CredentialCategory_CREDENTIAL_CATEGORY_NTB {
		return c.Conf.FederalNTB.DisbPartnerName, c.Conf.FederalNTB.PartnerCodeFundTransfer, c.Conf.FederalNTB.DisbSenderCode
	}
	return c.Conf.Federal.UserAccessId, c.Conf.Federal.UserAccessCode, c.Conf.Federal.DisbursementApiSenderCode
}

func (c *GetLoanDisbursementRequest) Add(req *http.Request) *http.Request {
	if c.Req.GetCredentialCategory() == vgLendingPb.CredentialCategory_CREDENTIAL_CATEGORY_NTB {
		req.Header.Add(federal.ClientIdKey, c.Conf.FederalNTB.ClientId)
		req.Header.Add(federal.ClientSecretKey, c.Conf.FederalNTB.ClientSecret)
	} else {
		req.Header.Add(federal.ClientSecretKey, cfg.Secrets.Ids[config.ClientSecretKey])
		req.Header.Add(federal.ClientIdKey, cfg.Secrets.Ids[config.ClientId])
	}
	return req
}

// nolint: dupl
func (c *GetLoanDisbursementResponse) Unmarshal(b []byte) (proto.Message, error) {
	res := vendorFederalPb.LoanDisbursementReponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to unmarshal byte array to GetLoanDisbursementResponse proto message")
	}

	if _, exists := SuccessCodes[res.GetResponseCode()]; !exists {
		return &vgLendingPb.LoanDisbursementResponse{
			Status: vendorResponseCodeToRpcStatusForLoanDisbursement(res.GetResponseCode()),
		}, nil
	}

	return &vgLendingPb.LoanDisbursementResponse{
		Status:          rpc.StatusOk(),
		ReferenceNumber: res.GetReferenceNumber(),
	}, nil
}

func (c GetLoanDisbursementResponse) CanLogUnredactedEncryptedPayload() bool {
	return false
}

func (c *GetLoanDisbursementResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in FEDERAL GetLoanDisbursement", zap.String("error", string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	return &vgLendingPb.LoanDisbursementResponse{
		Status: vendorapi.GetRpcStatusFromHttpCodeWithDebugMsg(
			ctx,
			httpStatus,
			fmt.Sprintf("Error response: %s", string(b)),
		),
	}, nil
}

func vendorResponseCodeToRpcStatusForLoanDisbursement(s string) *rpc.Status {
	switch s {
	case "003":
		return rpc.StatusFailedPreconditionWithDebugMsg("REQUEST DOES NOT MEET THE PREQUISITE")
	case "444":
		return rpc.StatusPermanentFailureWithDebug("Incorrect Account Details")
	case "666":
		return rpc.StatusFailedPreconditionWithDebugMsg("Incorrect Username and Password")
	case "876":
		return rpc.StatusFailedPreconditionWithDebugMsg("Penny Drop API Failed")
	case "777":
		return rpc.StatusPermanentFailureWithDebug("Loan Account Details and Beneficiary Details not matching")
	default:
		return rpc.StatusInternalWithDebugMsg(fmt.Sprintf("code: %v, msg: unhandled response code received", s))
	}
}
