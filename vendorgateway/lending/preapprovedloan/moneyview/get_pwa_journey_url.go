package moneyview

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	moneyviewVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	vendorMoneyviewPb "github.com/epifi/gamma/api/vendors/moneyview"
)

type GetPWAJourneyUrlRequest struct {
	AccessToken string
	BaseUrl     string
	LeadId      string
}

type GetPWAJourneyUrlResponse struct {
}

func (r *GetPWAJourneyUrlRequest) HTTPMethod() string {
	return http.MethodGet
}

func (r *GetPWAJourneyUrlRequest) URL() string {
	return r.BaseUrl + fmt.Sprintf("/journey-url/%s", r.LeadId)
}

func (c *GetPWAJourneyUrlRequest) SetAuth(req *http.Request) *http.Request {
	req.Header.Add(tokenHeaderKey, c.AccessToken)
	return req
}

func (r *GetPWAJourneyUrlRequest) GetResponse() vendorapi.Response {
	return &GetPWAJourneyUrlResponse{}
}

func (r *GetPWAJourneyUrlRequest) Marshal() ([]byte, error) {
	return nil, nil
}

// nolint: dupl
func (c *GetPWAJourneyUrlResponse) Unmarshal(b []byte) (proto.Message, error) {
	vendorRes := &vendorMoneyviewPb.GetJourneyUrlResponse{}
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err := unmarshaller.Unmarshal(b, vendorRes); err != nil {
		return nil, fmt.Errorf("error unmarshalling get pwa journey url api response, err : %w", err)
	}

	switch {
	case strings.EqualFold(vendorRes.GetStatus(), apiStatusFailure):
		logger.ErrorNoCtx("get pwa journey url api failed", zap.String("vendorStatusCode", vendorRes.GetCode()), zap.String(logger.VENDOR_STATUS, vendorRes.GetStatus()), zap.String("vendorStatusMsg", vendorRes.GetMessage()))
		return &moneyviewVgPb.GetPWAJourneyUrlResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("get pwa journey url api failed, status : %s, code : %s, message : %s", vendorRes.GetStatus(), vendorRes.GetCode(), vendorRes.GetMessage())),
		}, nil

	case strings.EqualFold(vendorRes.GetStatus(), apiStatusExpired), strings.EqualFold(vendorRes.GetStatus(), apiStatusReject):
		logger.InfoNoCtx("received expired/reject api status in get pwa journey url api", zap.String("vendorStatusCode", vendorRes.GetCode()), zap.String(logger.VENDOR_STATUS, vendorRes.GetStatus()), zap.String("vendorStatusMsg", vendorRes.GetMessage()))
		return &moneyviewVgPb.GetPWAJourneyUrlResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("lead in rejected/expired state"),
		}, nil

	case strings.EqualFold(vendorRes.GetStatus(), apiStatusSuccess):
		// success response would be returned in the latter part of the flow.
	default:
		logger.InfoNoCtx("unhandled vendor api status in get pwa journey url api", zap.String("vendorStatusCode", vendorRes.GetCode()), zap.String(logger.VENDOR_STATUS, vendorRes.GetStatus()), zap.String("vendorStatusMsg", vendorRes.GetMessage()))
		return &moneyviewVgPb.GetPWAJourneyUrlResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("unhandled vendor api status in get pwa journey url api, status : %s, code : %s, message : %s", vendorRes.GetStatus(), vendorRes.GetCode(), vendorRes.GetMessage())),
		}, nil
	}

	return &moneyviewVgPb.GetPWAJourneyUrlResponse{
		Status: rpc.StatusOk(),
		SsoUrl: vendorRes.GetPwaUrl(),
	}, nil
}

//nolint:dupl
func (c *GetPWAJourneyUrlResponse) HandleHttpError(ctx context.Context, httpStatusCode int, responseBody []byte) (proto.Message, error) {
	logger.Error(ctx, "error in get journey url api", zap.Int("httpStatusCode", httpStatusCode), zap.String("rawResponse", string(responseBody)))

	return &moneyviewVgPb.GetPWAJourneyUrlResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("get journey url api failed")),
	}, nil
}
