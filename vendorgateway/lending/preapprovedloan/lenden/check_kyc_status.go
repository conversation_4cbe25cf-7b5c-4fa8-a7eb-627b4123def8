package lenden

import (
	"context"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	lendenPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	vendorLendenPb "github.com/epifi/gamma/api/vendors/lenden"
	"github.com/epifi/gamma/vendorgateway/config"
)

// CheckKycStatusRequest represents the request structure for checking the KYC status.
type CheckKycStatusRequest struct {
	Req  *lendenPb.CheckKycStatusRequest
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

// CheckKycStatusResponse represents the response structure for checking the KYC status.
type CheckKycStatusResponse struct {
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

func (c *CheckKycStatusRequest) URL() string {
	return c.Conf.BaseUrl
}

func (c *CheckKycStatusRequest) HTTPMethod() string {
	return http.MethodPost
}

func (c *CheckKycStatusRequest) GetResponse() vendorapi.Response {
	return &CheckKycStatusResponse{
		Conf:        c.Conf,
		Cryptor:     c.Cryptor,
		LogRedactor: c.LogRedactor,
	}
}

//nolint:dupl
func (c *CheckKycStatusRequest) Marshal() ([]byte, error) {
	requestPayload := &vendorLendenPb.CheckKycStatusRequest{
		Params: &vendorLendenPb.Params{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Fields: &vendorLendenPb.Fields{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Json: &vendorLendenPb.CheckKycStatusRequestPayload{
			ProductId:   c.Conf.ProductId,
			UserId:      c.Req.GetUserId(),
			ReferenceId: c.Req.GetTrackingId(),
		},
		Attributes: &vendorLendenPb.Attributes{
			Authorization: c.Conf.Authorization,
		},
		ApiCode: string(InternalApiCodeCheckKycStatus),
	}

	// Step 2: Marshal the VendorRequest into JSON
	vendorReqBytes, err := protojson.Marshal(requestPayload)
	if err != nil {
		return nil, fmt.Errorf("error marshaling vendor request: %w", err)
	}

	// Step 3: Encrypt the JSON request
	encryptedReqBytes, err := c.Cryptor.Encrypt(vendorReqBytes)
	if err != nil {
		return nil, fmt.Errorf("error encrypting vendor request: %w", err)
	}

	// Step 4: Return the encrypted bytes
	return encryptedReqBytes, nil
}

//nolint:dupl
func (c *CheckKycStatusResponse) Unmarshal(b []byte) (proto.Message, error) {
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	encryptedResponse := vendorLendenPb.LendenEncryptedResponse{}

	if err := unmarshaller.Unmarshal(b, &encryptedResponse); err != nil {
		return nil, fmt.Errorf("error unmarshalling response body: %w", err)
	}
	encryptedResponsePayload := encryptedResponse.GetResponse().GetPayload()
	if encryptedResponsePayload == "" {
		return nil, errors.New("empty response received from vendor")
	}

	decryptedBytes, err := c.Cryptor.Decrypt([]byte(encryptedResponsePayload))
	if err != nil {
		return nil, fmt.Errorf("error decrypting response: %w", err)
	}

	responseWithWrapper := vendorLendenPb.CheckKycStatusResponseWrapper{}
	if err := unmarshaller.Unmarshal(decryptedBytes, &responseWithWrapper); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}

	vendorResponse := responseWithWrapper.GetResponse().GetResponseData()
	kycStatus, err := getKYCStatusEnumFromString(vendorResponse.GetKycStatus())
	if err != nil {
		return nil, errors.Wrap(err, "error getting kyc status enum from string")
	}
	return &lendenPb.CheckKycStatusResponse{
		Status:    rpc.StatusOk(),
		KycStatus: kycStatus,
	}, nil
}

func (c *CheckKycStatusResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in lenden check kyc status API", zap.String(logger.PAYLOAD, string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	return &lendenPb.CheckKycStatusResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error response: %s", string(b))),
	}, nil
}
