// nolint
package federal

import (
	"encoding/json"

	vgPciConf "github.com/epifi/gamma/vendorgateway-pci/config"

	"github.com/epifi/gamma/vendorgateway/federal"

	"github.com/golang/protobuf/jsonpb"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	vgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	simPb "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
)

type CardActivationReq struct {
	*federal.DefaultHeaderAdder

	Method    string
	Req       *vgPb.ActivateCardRequest
	Url       string
	VgPciConf *vgPciConf.Config
}

func (ca CardActivationReq) Marshal() ([]byte, error) {
	var (
		commonPayload *map[string]string
	)
	if ca.VgPciConf != nil {
		commonPayload = GetCommonRequestParams(ca.VgPciConf)
	} else {
		commonPayload = federal.GetCommonRequestParams()
	}

	caReq := &simPb.CardActivationRequest{
		SenderCode:    (*commonPayload)[federal.SenderCodeKey],
		AccessId:      (*commonPayload)[federal.ServiceAccessIdKey],
		AccessCode:    (*commonPayload)[federal.ServiceAccessCodeKey],
		DeviceId:      ca.Req.Auth.DeviceId,
		DeviceToken:   ca.Req.Auth.DeviceToken,
		UserProfileId: ca.Req.Auth.UserProfileId,
		// TODO(anand): credblock must not mandatory for activation api. In discussion with federal, uncomment if
		// required or else remove.
		// CredBlock:     ca.Req.Auth.EncryptedPin,
		RequestId:   ca.Req.RequestId,
		RequestType: simPb.RequestType_CARD_ACTIVATE.String(),
		// TODO(anand): awaiting federal docs for clarity on these two fields. Adding sample values
		CardDetails: &simPb.CardDetails{
			CardStatus: "1",
			CardType:   "00",
		},
		AccountDetails: &simPb.AccountDetails{
			CustomerId:    ca.Req.AccountDetails.CustomerId,
			AccountNumber: ca.Req.AccountDetails.AccountNumber,
			// TODO(anand): does something like ********** work? or ************?
			MobileNumber: ca.Req.AccountDetails.PhoneNumber.ToStringNationalNumber(),
		},
	}
	return json.Marshal(caReq)
}

func (ca CardActivationReq) HTTPMethod() string {
	return ca.Method
}

func (ca CardActivationReq) URL() string {
	return ca.Url
}

func (ca CardActivationReq) GetResponse() vendorapi.Response {
	return CardActivationResp{}
}

var statusActivation = map[string]rpc.StatusFactory{
	"000": rpc.StatusOk,
}

type CardActivationResp struct {
}

func (ca CardActivationResp) Unmarshal(b []byte) (proto.Message, error) {
	fedResp := simPb.CardActivationResponse{}
	err := jsonpb.UnmarshalString(string(b), &fedResp)
	if err != nil {
		logger.ErrorNoCtx("Error in unmarshal CardActivationResponse from Federal")
		return nil, err
	}

	resp := &vgPb.ActivateCardResponse{}
	// if the status is amongst what is known, populate the response
	// the response reason is populated as the debug message of the rpc status
	if rpcStatusFactory, ok := statusActivation[fedResp.ResponseCode]; ok {
		rpcStatus := rpcStatusFactory()
		rpcStatus.SetDebugMessage(fedResp.ResponseReason)
		resp.Status = rpcStatus
		return resp, nil
	}

	// Log error and set response type as UNKNOWN.
	// When such a status is found, it indicates there is a change at the Vendor's end
	// and we need to update our status mapping.
	logger.InfoNoCtx("Got unknown response",
		zap.String("ResponseCode", fedResp.ResponseCode),
		zap.String("Reason", fedResp.ResponseReason),
	)
	resp.Status = rpc.StatusUnknown()
	return resp, nil
}

func (ca CardActivationReq) CanLogUnredactedEncryptedPayload() bool {
	return false
}
