package federal

import (
	"encoding/xml"
	"fmt"
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	vgFederal "github.com/epifi/gamma/vendorgateway/federal"

	employmentPb "github.com/epifi/gamma/api/employment"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/vendorgateway/config"
	federalCommon "github.com/epifi/gamma/vendorgateway/openbanking/federal"
	opfederal "github.com/epifi/gamma/vendorgateway/openbanking/federal"
)

type CifCreation struct {
	XMLName           xml.Name           `xml:"Cif_Creation"`
	SenderCredentials *SenderCredentials `xml:"SenderCredentials"`
	CifRequest        *CifRequest        `xml:"Cif_Request"`
}

type SenderCredentials struct {
	UserAccessId   string `xml:"UserAccessId"`
	UserAccessCode string `xml:"UserAccessCode"`
	SenderCode     string `xml:"SenderCode"`
}

type CifRequest struct {
	RequestId             string                 `xml:"RequestId"`
	SolId                 string                 `xml:"SolId"`
	BRERefNum             string                 `xml:"BRERefNum"`
	PersonalDetails       *PersonalDetails       `xml:"Personal_Details"`
	ContactDetails        *ContactDetails        `xml:"Contact_Details"`
	AdditionalDetails     *AdditionalDetails     `xml:"Additional_Details"`
	IdentificationDetails *IdentificationDetails `xml:"Identification_Details"`
	SignId                string                 `xml:"Sign_Id"`
}

type PersonalDetails struct {
	Title         string `xml:"Title"`
	FirstName     string `xml:"FirstName"`
	MiddleName    string `xml:"MiddleName"`
	LastName      string `xml:"LastName"`
	FatherName    string `xml:"FatherName"`
	MotherName    string `xml:"MotherName"`
	DateOfBirth   string `xml:"DateOfBirth"` // YYYY-MM-DD
	Gender        string `xml:"Gender"`
	MaritalStatus string `xml:"MaritalStatus"`
	UidNo         string `xml:"Uid_No"`
}

type ContactDetails struct {
	Mobile                string   `xml:"Mobile"`
	Email                 string   `xml:"Email"`
	Communication_Address *Address `xml:"Communication_Address"`
	CA_Sameas_PA          string   `xml:"CA_Sameas_PA"`
	Permanent_Address     *Address `xml:"Permanent_Address"`
}

type Address struct {
	House      string `xml:"House"`
	Place      string `xml:"Place"`
	City_Cd    string `xml:"City_Cd"`
	State_Cd   string `xml:"State_Cd"`
	Country_Cd string `xml:"Country_Cd"`
	PinCode    string `xml:"PinCode"`
	LandLine   string `xml:"LandLine"`
}

type AdditionalDetails struct {
	AnnualIncome      string `xml:"AnnualIncome"`
	PanNo             string `xml:"PanNo"`
	Religion          string `xml:"Religion"`
	Community         string `xml:"Community"`
	Qualification     string `xml:"Qualification"`
	Occupation        string `xml:"Occupation"`
	Form60            string `xml:"Form60"`
	TaxSlab           string `xml:"TaxSlab"`
	Employment        string `xml:"Employement"`
	EmployerName      string `xml:"EmployerName"`
	Designation       string `xml:"Designation"`
	WorkPlace         string `xml:"WorkPlace"`
	EmployerType      string `xml:"EmployerType"`
	SpouseName        string `xml:"SpouseNme"`
	SpouseOccupation  string `xml:"SpouseOcupation"`
	SpouseDesignation string `xml:"SpouseDesig"`
	NoOfChild         string `xml:"NoOfChild"`
	NoOfDependents    string `xml:"NoOfDependents"`
	Networth          string `xml:"Networth"`
	// TODO: uncomment when moving to fed v3 api
	// CustomerStatus        string `xml:"CustomerStatus"`
	// Category              string `xml:"Category"`
	// SubCategoryOccupation string `xml:"SubCategoryOccupation"`
}

type IdentificationDetails struct {
	ProofOfIdentity *ProofDetails `xml:"ProofOfIdentity"`
	ProofOfAddress  *ProofDetails `xml:"ProofOfAddress"`
}

type ProofDetails struct {
	Type         string `xml:"Type"`
	IdNumber     string `xml:"Id_Number"`
	IdIssueDate  string `xml:"Id_IssueDate"`
	IdExpiryDate string `xml:"Id_ExpiryDate"`
}

type CifResponse struct {
	XMLName        xml.Name `xml:"CifResponse"`
	RequestId      string   `xml:"RequestId"`
	ResponseCode   string   `xml:"ResponseCode"`
	ResponseReason string   `xml:"ResponseReason"`
}

const (
	DefaultEmployment   = "Others"
	DefaultAnnualSalary = "500000"
	DefaultReligion     = "OTH"
)

type CreateLoanCustomerReq struct {
	Method                         string
	Req                            *customer.CreateLoanCustomerRequest
	Url                            string
	UseOccupationTypeInCifCreation bool
	FieldSanitizer                 *federalCommon.FieldSanitiser
	Conf                           *config.FederalNTBLoanCredentials
}

// need to ask why we are implementing this
func FormatDate(date *date.Date) string {
	d := fmt.Sprintf("%04v-%02v-%02v", date.GetYear(), date.GetMonth(), date.GetDay())
	return d
}

var customerCreationErrorCodes = map[string]*rpc.Status{
	"000":     rpc.StatusOk(),               // Request Accepted
	"CCE_000": rpc.StatusOk(),               // Request Accepted
	"CCE_001": rpc.StatusAlreadyExists(),    // Duplicate RequestId Found
	"CCE_002": rpc.StatusInvalidArgument(),  // Request_ID is Required
	"CCE_003": rpc.StatusPermissionDenied(), // This Service is not enabled for this Sender
	"CCE_004": rpc.StatusPermissionDenied(), // Invalid username or password or sendercode
	"CCE_005": rpc.StatusInvalidArgument(),  // Contains Invalid Data
	"CCE_006": rpc.StatusInvalidArgument(),  // Should Contain (Y OR N)
	"CCE_007": rpc.StatusInvalidArgument(),  // taxslab Not a valid value
	"CCE_008": rpc.StatusInvalidArgument(),  // Invalid value in PAN or AADHAAR OR FORM60 Tags
	"CCE_011": rpc.StatusInternal(),         // Exception while inserting into CIF tables
	"CCE_012": rpc.StatusInternal(),         // Internal Service Down
	"CCE_013": rpc.StatusInvalidArgument(),  // Field is missing or Field value is missing
	"CCE_014": rpc.StatusInvalidArgument(),  // Issuedt or ExpDate or Id_Number Field is missing, Issuedt or ExpDate or Id_Number Field value is missing (PP)
	"CCE_015": rpc.StatusInvalidArgument(),  // Issuedt or ExpDate Field or Id_Number Field is missing, Issuedt or ExpDate Field or Id_Number field value is missing (PP)
	"CCE_016": rpc.StatusInvalidArgument(),  // ExpDate or Id_Number Field is missing, ExpDate or Id_Number Field value is missing (DL)
	"CCE_017": rpc.StatusInvalidArgument(),  // ExpDate or Id_Number Field is missing, ExpDate or Id_Number Field value is missing (DL)
	"CCE_018": rpc.StatusInvalidArgument(),  // ProofOfAddress Id_Number or Personal_Details UID NO Field is missing, ProofOfAddress Id_Number or Personal_Details UID NO Field value is missing
	"CCE_019": rpc.StatusInvalidArgument(),  // ProofOfIdentity Id_Number or Personal_Details UID NO Field is missing, ProofOfIdentity Id_Number or Personal_Details UID NO Field value is missing
	"CCE_020": rpc.StatusInvalidArgument(),  // Id_Number Field is missing, Id_Number field value is missing
	"CCE_021": rpc.StatusInvalidArgument(),  // Id_Number Field is missing, Id_Number field value is missing
}

func (c *CreateLoanCustomerReq) Marshal() ([]byte, error) {
	request := c.Req
	fieldSanitizer = c.FieldSanitizer
	// Id proof mapping
	proofEnumMapping := map[kycPb.IdProofType]string{
		kycPb.IdProofType_ID_PROOF_TYPE_UNSPECIFIED:           "GOVT", // Need to confirm if mpping is correct
		kycPb.IdProofType_PASSPORT:                            "PP",
		kycPb.IdProofType_VOTER_ID:                            "VTRID",
		kycPb.IdProofType_PAN:                                 "PAN",
		kycPb.IdProofType_DRIVING_LICENSE:                     "DL",
		kycPb.IdProofType_UID:                                 "AADHA", // Need to confirm if mpping is correct
		kycPb.IdProofType_NREGA_JOB_CARD:                      "NREGA",
		kycPb.IdProofType_NATIONAL_POPULATION_REGISTER_LETTER: "GOVT", // Need to confirm if mpping is correct
		kycPb.IdProofType_CKYC_RECORD:                         "GOVT",
	}

	employmentTypeMapping := map[employmentPb.EmploymentType]string{
		employmentPb.EmploymentType_SELF_EMPLOYED:               "Self Employed",
		employmentPb.EmploymentType_SALARIED:                    "Salaried",
		employmentPb.EmploymentType_RETIRED:                     "Retired",
		employmentPb.EmploymentType_OTHERS:                      "Other",
		employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED: DefaultEmployment,
		employmentPb.EmploymentType_BUSINESS_OWNER:              "Self Employed",
		employmentPb.EmploymentType_FREELANCER:                  "Self Employed",
		employmentPb.EmploymentType_WORKING_PROFESSIONAL:        "Self Employed",
		employmentPb.EmploymentType_STUDENT:                     "Unemployed",
		employmentPb.EmploymentType_HOMEMAKER:                   "Housewife",
		employmentPb.EmploymentType_SELF_EMPLOYED_PROFESSIONAL:  "Self Employed Professional",
		employmentPb.EmploymentType_POLITICIAN_OR_STATESMAN:     "Politician/Statesman",
	}

	communityMapping := map[typesv2.Community]string{
		typesv2.Community_BUDDHIST:  "BH",
		typesv2.Community_CHRISTIAN: "CH",
		typesv2.Community_HINDU:     "HI",
		typesv2.Community_MUSLIM:    "MU",
		typesv2.Community_JAIN:      "JN",
		// typesv2.Community_OTHERS_COMMUNITY:      "OTH", //need to change after generated code gets merged
		typesv2.Community_PARSI:                 "PR",
		typesv2.Community_SIKH:                  "SK",
		typesv2.Community_ZORASTRIAN:            "ZS",
		typesv2.Community_COMMUNITY_UNSPECIFIED: "NA",
	}

	// need to change after generated code gets merged
	qualificationMapping := map[typesv2.Qualification]string{
		typesv2.Qualification_CHARTED_ACCOUNTANTS:                          "CA",
		typesv2.Qualification_DIPLOMA_HOLDERS:                              "DIP",
		typesv2.Qualification_DOCTORAL:                                     "DR",
		typesv2.Qualification_ENGINEERING_GRADUATE:                         "ENGG",
		typesv2.Qualification_ENGINEERING_GRADUATE_FROM_PREMIER_INSTITUTES: "ENGP",
		typesv2.Qualification_GRADUATE:                                     "G",
		typesv2.Qualification_NON_MATRICULATE:                              "NONM",
		typesv2.Qualification_POSTGRADUATE:                                 "PG",
		typesv2.Qualification_QUALIFICATION_UNSPECIFIED:                    "NA",
		typesv2.Qualification_UNDERGRADUATE:                                "UG",
		typesv2.Qualification_NON_LITERATE:                                 "NONLT",
		typesv2.Qualification_PROFESSIONAL_DEGREE_OR_DIPLOMA:               "PRFDP",
	}
	// need to change after generated code gets merged
	designationMapping := map[typesv2.Designation]string{
		typesv2.Designation_MANAGER:              "Manager",
		typesv2.Designation_PRESIDENT:            "President",
		typesv2.Designation_CEO:                  "CEO",
		typesv2.Designation_CFO:                  "CFO",
		typesv2.Designation_CTO:                  "CTO",
		typesv2.Designation_OFFICER:              "Officer",
		typesv2.Designation_EXECUTIVE:            "Executive",
		typesv2.Designation_SUPERVISOR:           "Supervisor",
		typesv2.Designation_DIRECTOR:             "Director",
		typesv2.Designation_ACCOUNT_MANAGER:      "Account Mgr",
		typesv2.Designation_RELATIONSHIP_MANAGER: "Relationship Mgr",
		typesv2.Designation_DESIGNATION_OTHERS:   "Partner",
	}

	// TODO: uncomment when moving to fed v3 api
	// categoaryMapping := map[typesv2.Category]string{
	//	typesv2.Category_CATEGORY_UNSPECIFIED:          "NA",
	//	typesv2.Category_CATEGORY_NOT_APPLICABLE:       "NA",
	//	typesv2.Category_CATEGORY_OTHER_BACKWARD_CASTE: "OBC",
	//	typesv2.Category_CATEGORY_SCHEDULE_CASTE:       "SC",
	//	typesv2.Category_CATEGORY_SCHEDULE_TRIBE:       "ST",
	//	typesv2.Category_CATEGORY_GENERAL:              "GN",
	// }

	name, err := fieldSanitizer.SanitiseFullName(request.GetName())
	if err != nil {
		return nil, err
	}
	personalDetails := &PersonalDetails{
		Title:         opfederal.GetTitleForFederal(request.GetName().GetHonorific(), request.GetGender().String()),
		FirstName:     name.GetFirstName(),
		MiddleName:    name.GetMiddleName(),
		LastName:      name.GetLastName(),
		FatherName:    fieldSanitizer.SanitiseNameComponent(request.GetFatherName()),
		MotherName:    fieldSanitizer.SanitiseNameComponent(request.GetMotherName()),
		DateOfBirth:   FormatDate(request.GetDateOfBirth()),
		Gender:        opfederal.GetGenderCode(request.GetGender().String()),
		MaritalStatus: opfederal.GetMaritalStatusCode(request.GetMaritalStatus().String()),
		UidNo:         request.GetUidNo(),
	}

	senderCredentials := &SenderCredentials{
		UserAccessId:   c.Conf.UserAccessId,
		UserAccessCode: c.Conf.UserAccessCode,
		SenderCode:     c.Conf.SenderCode,
	}

	commAddressLines := request.GetCommunicationAddress().GetAddressLines()
	commAddressLines = append(commAddressLines, request.GetCommunicationAddress().GetSublocality())
	house, place, err := GetHouseAndPlace(commAddressLines)
	if err != nil {
		return nil, err
	}

	communicationAddress := &Address{
		House:      house,
		Place:      place,
		City_Cd:    request.GetCommunicationAddress().GetLocality(),
		State_Cd:   request.GetCommunicationAddress().GetAdministrativeArea(),
		Country_Cd: request.GetCommunicationAddress().GetRegionCode(),
		PinCode:    request.GetCommunicationAddress().GetPostalCode(),
		LandLine:   request.GetPhoneNumber().ToString(),
	}

	permAddressLines := request.GetPermanentAddress().GetAddressLines()
	permAddressLines = append(permAddressLines, request.GetPermanentAddress().GetSublocality())
	house, place, err = GetHouseAndPlace(permAddressLines)
	if err != nil {
		return nil, err
	}

	permanentAddress := &Address{
		House:      house,
		Place:      place,
		City_Cd:    request.GetPermanentAddress().GetLocality(),
		State_Cd:   request.GetPermanentAddress().GetAdministrativeArea(),
		Country_Cd: request.GetPermanentAddress().GetRegionCode(),
		PinCode:    request.GetPermanentAddress().GetPostalCode(),
		LandLine:   request.GetPhoneNumber().ToString(),
	}

	contactDetails := &ContactDetails{
		Mobile:                request.GetPhoneNumber().ToString(),
		Email:                 request.GetEmail(),
		Communication_Address: communicationAddress,
		CA_Sameas_PA:          opfederal.CheckIfAddressSame(request.GetPermanentAddress(), request.GetCommunicationAddress()),
		Permanent_Address:     permanentAddress,
	}

	// evaluate tax slab by senior citizen status
	taxSlab := "TDSI"
	if request.GetPanNumber() == "" {
		taxSlab = "TDSNP"
	}

	employmentType := employmentTypeMapping[c.Req.GetEmploymentType()]
	annualIncome := fmt.Sprintf("%v", int(c.Req.GetAnnualIncome()))
	if int(c.Req.GetAnnualIncome()) == 0 {
		annualIncome = DefaultAnnualSalary
	}
	form60 := "Y"
	if request.GetPanNumber() != "" {
		form60 = "N"
	}
	// TODO: uncomment when moving to fed v3 api
	// disabilityType := "NR"
	// if request.GetDisabilityType() == typesv2.DisabilityType_DISABILITY_TYPE_DIFFERENTLY_ABLED {
	//	disabilityType = "DA"
	// }

	occupation, ok := opfederal.OccTypeToOccupationString[c.Req.GetOccupationType()]
	if !ok {
		return nil, status.Error(codes.InvalidArgument, "invalid occupation type")
	}

	additionalDetails := &AdditionalDetails{
		AnnualIncome:  annualIncome,
		PanNo:         request.GetPanNumber(),
		Religion:      DefaultReligion,
		Community:     communityMapping[request.GetCommunity()],
		Qualification: qualificationMapping[request.GetQualification()],
		Occupation:    occupation,
		Form60:        form60,
		TaxSlab:       taxSlab,
		Employment:    employmentType,
		Designation:   designationMapping[request.GetDesignation()],
		// TODO: uncomment when moving to fed v3 api
		// CustomerStatus:        disabilityType,
		// Category:              categoaryMapping[request.GetCategory()],
		// SubCategoryOccupation: occupation,
	}

	proofOfIdentity := &ProofDetails{
		Type:         proofEnumMapping[request.GetIdentityProof().GetType()],
		IdNumber:     fieldSanitizer.SanitiseIdNumber(request.GetIdentityProof().GetIdNumber()),
		IdIssueDate:  FormatDate(request.GetIdentityProof().GetIdIssueDate()),
		IdExpiryDate: FormatDate(request.GetIdentityProof().GetIdExpiryDate()),
	}

	proofOfAddress := &ProofDetails{
		Type:         proofEnumMapping[request.GetAddressProof().GetType()],
		IdNumber:     fieldSanitizer.SanitiseIdNumber(request.GetAddressProof().GetIdNumber()),
		IdIssueDate:  FormatDate(request.GetAddressProof().GetIdIssueDate()),
		IdExpiryDate: FormatDate(request.GetAddressProof().GetIdExpiryDate()),
	}

	identificationDetails := &IdentificationDetails{
		ProofOfIdentity: proofOfIdentity,
		ProofOfAddress:  proofOfAddress,
	}

	solId := request.GetSolId()
	if solId == "" {
		return nil, status.Error(codes.InvalidArgument, "empty sol id")
	}

	cifReq := &CifRequest{
		RequestId:             request.GetRequestId(),
		SolId:                 solId,
		BRERefNum:             request.GetBreRefNumber(),
		PersonalDetails:       personalDetails,
		ContactDetails:        contactDetails,
		AdditionalDetails:     additionalDetails,
		IdentificationDetails: identificationDetails,
		SignId:                request.GetSignImage(),
	}

	r := &CifCreation{
		SenderCredentials: senderCredentials,
		CifRequest:        cifReq,
	}
	return xml.Marshal(r)

}
func (c *CreateLoanCustomerReq) URL() string {
	return c.Url
}

func (c *CreateLoanCustomerReq) Add(req *http.Request) *http.Request {
	req.Header.Add(vgFederal.ClientIdKey, c.Conf.ClientId)
	req.Header.Add(vgFederal.ClientSecretKey, c.Conf.ClientSecret)
	return req
}

func (c *CreateLoanCustomerReq) HTTPMethod() string {
	return c.Method
}
func (c *CreateLoanCustomerReq) ContentTypeString() string {
	return vendorapi.ContentTypeXML
}

type CreateLoanCustomerRes struct {
}

func (c *CreateLoanCustomerReq) GetResponse() vendorapi.Response {
	return &CreateLoanCustomerRes{}
}

func (c *CreateLoanCustomerRes) Unmarshal(b []byte) (proto.Message, error) {
	response := &CifResponse{}
	err := xml.Unmarshal(b, response)
	if err != nil {
		logger.ErrorNoCtx("c", zap.Error(err))
		return &customer.CreateLoanCustomerResponse{Status: rpc.StatusInternal(), VendorStatus: &commonvgpb.VendorStatus{Description: string(b)}}, nil
	}

	grpcResponseStatus, ok := customerCreationErrorCodes[response.ResponseCode]
	if !ok {
		logger.ErrorNoCtx("error while mapping Cif creation Resp code to grpc code", zap.String("responseCode", response.ResponseCode))
		grpcResponseStatus = rpc.StatusInternal()
	}

	return &customer.CreateLoanCustomerResponse{
		Status:    grpcResponseStatus,
		RequestId: response.RequestId,
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        response.ResponseCode,
			Description: response.ResponseReason,
		},
	}, nil
}
