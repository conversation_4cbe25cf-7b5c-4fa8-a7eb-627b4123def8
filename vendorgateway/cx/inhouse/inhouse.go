package inhouse

import (
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	cxInHousePb "github.com/epifi/gamma/api/vendorgateway/cx/inhouse"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/cx/inhouse/popular_faqs"
)

func NewInHouseRequest(req proto.Message) vendorapi.SyncRequest {
	conf, _ := config.Load()
	switch v := req.(type) {
	case *cxInHousePb.GetPopularFAQListRequest:
		return popular_faqs.GetPopularFAQsReq{
			Method: http.MethodGet,
			Req:    req.(*cxInHousePb.GetPopularFAQListRequest),
			Url:    conf.Application.InhousePopularFAQUrl,
		}
	default:
		logger.ErrorNoCtx("Unsupported request type", zap.Any("reqType", v))
		return nil
	}
}
