package saven

import (
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	ccVgPb "github.com/epifi/gamma/api/vendorgateway/creditcard"
	ccVendorsPb "github.com/epifi/gamma/api/vendors/saven/creditcard"
)

type UpdateDeliveryStatusRequest struct {
	Req     *ccVgPb.UpdateCreditCardDeliveryStateRequest
	Method  string
	BaseUrl string
}

func (c *UpdateDeliveryStatusRequest) HTTPMethod() string {
	return c.Method
}

func (c *UpdateDeliveryStatusRequest) URL() string {
	return c.BaseUrl + "/card/webhook/cardDeliveredStatus"
}

func (c *UpdateDeliveryStatusRequest) GetResponse() vendorapi.Response {
	return &UpdateDeliveryStatusResponse{}
}

func (c *UpdateDeliveryStatusRequest) Marshal() ([]byte, error) {
	if c.Req == nil {
		return nil, errors.New("request cannot be nil")
	}
	vgRequest := &ccVendorsPb.UpdateCreditCardDeliveryStatusRequest{
		DeliveryDetails: &ccVendorsPb.UpdateCreditCardDeliveryStatusRequest_DeliveryDetails{
			DeliveryStatus: c.Req.GetDeliveryState().String(),
			DeliveryVendor: c.Req.GetCarrier(),
			TrackingUrl:    c.Req.GetTrackingUrl(),
		},
		UserId: c.Req.GetUserId(),
	}
	return protojson.Marshal(vgRequest)
}

type UpdateDeliveryStatusResponse struct{}

func (c *UpdateDeliveryStatusResponse) Unmarshal(b []byte) (proto.Message, error) {
	return &ccVgPb.UpdateCreditCardDeliveryStateResponse{
		Status: rpc.StatusOk(),
	}, nil
}
