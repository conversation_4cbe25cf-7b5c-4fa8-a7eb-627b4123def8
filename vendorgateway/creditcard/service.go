// nolint: dupl
package creditcard

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	ccVgPb "github.com/epifi/gamma/api/vendorgateway/creditcard"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/config/genconf"
	"github.com/epifi/gamma/vendorgateway/creditcard/saven"
)

type Service struct {
	ccVgPb.UnimplementedCreditCardServer
	handler            *vendorapi.HTTPRequestHandler
	conf               *config.Config
	gconf              *genconf.Config
	savenRequestSigner *saven.JWTSigner
}

func NewService(handler *vendorapi.HTTPRequestHandler, conf *config.Config, gconf *genconf.Config, savenRequestSigner *saven.JWTSigner) *Service {
	return &Service{
		handler:            handler,
		conf:               conf,
		gconf:              gconf,
		savenRequestSigner: savenRequestSigner,
	}
}

func (s *Service) GenerateCreditCardSdkAuthToken(ctx context.Context, req *ccVgPb.GenerateCreditCardSdkAuthTokenRequest) (*ccVgPb.GenerateCreditCardSdkAuthTokenResponse, error) {
	vendorRequest, err := vendorapi.NewVendorRequest(req, s.getReqFactoryMap())
	if err != nil {
		logger.Error(ctx, "Error creating new vendor request for GenerateCreditCardSdkAuthToken : "+err.Error())
		return &ccVgPb.GenerateCreditCardSdkAuthTokenResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	res, err := s.handler.Handle(ctx, vendorRequest)
	if err != nil {
		logger.Error(ctx, "Error handling vendor request for GenerateCreditCardSdkAuthToken : "+err.Error())
		return &ccVgPb.GenerateCreditCardSdkAuthTokenResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	return res.(*ccVgPb.GenerateCreditCardSdkAuthTokenResponse), nil
}

func (s *Service) UpdateCreditCardDeliveryState(ctx context.Context, req *ccVgPb.UpdateCreditCardDeliveryStateRequest) (*ccVgPb.UpdateCreditCardDeliveryStateResponse, error) {
	vendorRequest, err := vendorapi.NewVendorRequest(req, s.getReqFactoryMap())
	if err != nil {
		logger.Error(ctx, "Error creating new vendor request for UpdateCreditCardDeliveryState : "+err.Error())
		return &ccVgPb.UpdateCreditCardDeliveryStateResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	res, err := s.handler.Handle(ctx, vendorRequest)
	if err != nil {
		logger.Error(ctx, "Error handling vendor request for UpdateCreditCardDeliveryState : "+err.Error())
		return &ccVgPb.UpdateCreditCardDeliveryStateResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	return res.(*ccVgPb.UpdateCreditCardDeliveryStateResponse), nil
}
