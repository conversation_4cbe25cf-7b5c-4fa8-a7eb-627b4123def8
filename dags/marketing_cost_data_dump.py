import sys
sys.path.append('/home/<USER>/airflow')
from airflow.utils.dates import days_ago
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from scripts.vendor_data.marketing_cost_data_dump import get_roster_data
from scripts.wormhole.execute_wormhole import base_etl
from scripts.wormhole.execute_wormhole import etl
from scripts.epiview.execute_epiview import execute_epiview

default_args = {
    'owner': 'airflow',
    'start_date': days_ago(1),
    'depends_on_past': False
}


dag_name = 'marketing_cost_data_dump'

dag = DAG(
    dag_name,
    default_args=default_args,
    schedule_interval='10 0 * * *',
    catchup=False,
    max_active_runs=1
)


marketing_cost_data_dump = PythonOperator(
    task_id='data_dump',
    python_callable=get_roster_data,
    op_kwargs={},
    provide_context=True,
    dag=dag
)

base_etl = PythonOperator(
    task_id='base_etl_marketing_cost',
    python_callable=base_etl,
    op_kwargs={'type': 'vendor', 'database': 'marketing_cost', 'table': 'marketing_cost', 'dag_name': dag_name, 'emr_master_dns':'EMR6_MASTER_DNS'},
    provide_context=True,
    dag=dag
)

sfdna_etl = PythonOperator(
    task_id='sf-dna_etl_marketing_cost',
    python_callable=etl,
    op_kwargs={'dag_name': dag_name, 'type': 'vendor', 'database': 'marketing_cost', 'table': 'marketing_cost',  'etl_conf': 'sf-dna_etl', 'emr_master_dns':'EMR6_MASTER_DNS'},
    provide_context=True,
    dag=dag
)

marketing_cost_data_dump >> base_etl >> sfdna_etl

