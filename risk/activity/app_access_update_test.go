package activity_test

// nolint: depguard
import (
	"testing"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epifitemporal"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/risk"
	activityPb "github.com/epifi/gamma/api/risk/activity"
	enumsPb "github.com/epifi/gamma/api/risk/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
)

func TestProcessor_AppAccessUpdate(t *testing.T) {
	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type args struct {
		req *activityPb.AppAccessUpdateRequest
	}
	type mockSavingsUpdate struct {
		isEnable bool
		req      *savingsPb.UpdateAccountRequest
		res      *savingsPb.UpdateAccountResponse
		err      error
	}
	type mockUserUpdateUser struct {
		isEnable bool
		req      *userPb.UpdateAccessRevokeDetailsRequest
		res      *userPb.UpdateAccessRevokeDetailsResponse
		err      error
	}
	type mockGetActorById struct {
		isEnable bool
		req      *actorPb.GetActorByIdRequest
		res      *actorPb.GetActorByIdResponse
		err      error
	}
	type mockSignOutUser struct {
		isEnable bool
		req      *authPb.SignOutRequest
		res      *authPb.SignOutResponse
		err      error
	}
	tests := []struct {
		name               string
		args               args
		mockSavingsUpdate  mockSavingsUpdate
		mockUserUpdateUser mockUserUpdateUser
		mockGetActorById   mockGetActorById
		mockSignOutUser    mockSignOutUser
		wantErr            bool
		want               *activityPb.AppAccessUpdateResponse
		assertErr          func(err error) bool
	}{
		{
			name: "Error when GetActorById returns internal",
			args: args{
				req: &activityPb.AppAccessUpdateRequest{
					ActorId: "actor-1",
					Action:  enumsPb.Action_ACTION_UNFREEZE,
					RequestReason: &risk.RequestReason{
						Reason:  enumsPb.RequestReason_REQUEST_REASON_LEA_UNFREEZE,
						Remarks: "remark-1",
					},
					InitiatedBy: "lea-workflow",
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: true,
				req: &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				},
				res: &actorPb.GetActorByIdResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			mockSavingsUpdate: mockSavingsUpdate{
				isEnable: false,
			},
			mockUserUpdateUser: mockUserUpdateUser{
				isEnable: false,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: false,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "Error when UpdateAccessRevokeDetails returns internal",
			args: args{
				req: &activityPb.AppAccessUpdateRequest{
					ActorId: "actor-1",
					Action:  enumsPb.Action_ACTION_UNFREEZE,
					RequestReason: &risk.RequestReason{
						Reason:  enumsPb.RequestReason_REQUEST_REASON_LEA_UNFREEZE,
						Remarks: "remark-1",
					},
					InitiatedBy: "lea-workflow",
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: true,
				req: &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				},
				res: &actorPb.GetActorByIdResponse{
					Actor: &typesv2.Actor{
						EntityId: "actor-entity-1",
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSavingsUpdate: mockSavingsUpdate{
				isEnable: true,
				req: &savingsPb.UpdateAccountRequest{
					Identifier: &savingsPb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: "actor-entity-1"},
					Constraints: &savingsPb.AccountConstraints{
						AccessLevel:  savingsPb.AccessLevel_ACCESS_LEVEL_FULL_ACCESS,
						Restrictions: nil,
						UpdateDetails: &savingsPb.ConstraintsUpdateDetails{
							Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_LEA_UNFREEZE,
							Remarks:   "remark-1",
							UpdatedBy: "lea-workflow",
						},
					},
					UpdateMask: []savingsPb.AccountFieldMask{savingsPb.AccountFieldMask_CONSTRAINTS},
				},
				res: &savingsPb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockUserUpdateUser: mockUserUpdateUser{
				isEnable: true,
				req: &userPb.UpdateAccessRevokeDetailsRequest{
					UserIdentifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier{
						Identifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier_UserId{
							UserId: "actor-entity-1",
						},
					},
					AccessRevokeDetails: &userPb.AccessRevokeDetails{
						AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED,
						RestoreReason:      userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_LEA_UNFREEZE,
						Remarks:            "remark-1",
						UpdatedBy:          "lea-workflow",
					},
					TriggerUserComms: true,
				},
				res: &userPb.UpdateAccessRevokeDetailsResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: false,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "SUCCESS: UNFREEZE",
			args: args{
				req: &activityPb.AppAccessUpdateRequest{
					ActorId: "actor-1",
					Action:  enumsPb.Action_ACTION_UNFREEZE,
					RequestReason: &risk.RequestReason{
						Reason:  enumsPb.RequestReason_REQUEST_REASON_LEA_UNFREEZE,
						Remarks: "remark-1",
					},
					InitiatedBy: "lea-workflow",
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: true,
				req: &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				},
				res: &actorPb.GetActorByIdResponse{
					Actor: &typesv2.Actor{
						EntityId: "actor-entity-1",
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSavingsUpdate: mockSavingsUpdate{
				isEnable: true,
				req: &savingsPb.UpdateAccountRequest{
					Identifier: &savingsPb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: "actor-entity-1"},
					Constraints: &savingsPb.AccountConstraints{
						AccessLevel:  savingsPb.AccessLevel_ACCESS_LEVEL_FULL_ACCESS,
						Restrictions: nil,
						UpdateDetails: &savingsPb.ConstraintsUpdateDetails{
							Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_LEA_UNFREEZE,
							Remarks:   "remark-1",
							UpdatedBy: "lea-workflow",
						},
					},
					UpdateMask: []savingsPb.AccountFieldMask{savingsPb.AccountFieldMask_CONSTRAINTS},
				},
				res: &savingsPb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockUserUpdateUser: mockUserUpdateUser{
				isEnable: true,
				req: &userPb.UpdateAccessRevokeDetailsRequest{
					UserIdentifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier{
						Identifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier_UserId{
							UserId: "actor-entity-1",
						},
					},
					AccessRevokeDetails: &userPb.AccessRevokeDetails{
						AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED,
						RestoreReason:      userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_LEA_UNFREEZE,
						UpdatedBy:          "lea-workflow",
						Remarks:            "remark-1",
					},
					TriggerUserComms: true,
				},
				res: &userPb.UpdateAccessRevokeDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: false,
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "SUCCESS: FREEZE",
			args: args{
				req: &activityPb.AppAccessUpdateRequest{
					ActorId: "actor-1",
					Action:  enumsPb.Action_ACTION_FULL_FREEZE,
					RequestReason: &risk.RequestReason{
						Reason:  enumsPb.RequestReason_REQUEST_REASON_LEA_COMPLAINT,
						Remarks: "remark-1",
					},
					InitiatedBy: "lea-workflow",
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: true,
				req: &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				},
				res: &actorPb.GetActorByIdResponse{
					Actor: &typesv2.Actor{
						EntityId: "actor-entity-1",
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSavingsUpdate: mockSavingsUpdate{
				isEnable: true,
				req: &savingsPb.UpdateAccountRequest{
					Identifier: &savingsPb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: "actor-entity-1"},
					Constraints: &savingsPb.AccountConstraints{
						AccessLevel:  savingsPb.AccessLevel_ACCESS_LEVEL_NO_ACCESS,
						Restrictions: nil,
						UpdateDetails: &savingsPb.ConstraintsUpdateDetails{
							Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_LEA_COMPLAINT,
							Remarks:   "remark-1",
							UpdatedBy: "lea-workflow",
						},
					},
					UpdateMask: []savingsPb.AccountFieldMask{savingsPb.AccountFieldMask_CONSTRAINTS},
				},
				res: &savingsPb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockUserUpdateUser: mockUserUpdateUser{
				isEnable: true,
				req: &userPb.UpdateAccessRevokeDetailsRequest{
					UserIdentifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier{
						Identifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier_UserId{
							UserId: "actor-entity-1",
						},
					},
					AccessRevokeDetails: &userPb.AccessRevokeDetails{
						AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED,
						RestoreReason:      userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_TOTAL_FREEZE,
						Remarks:            "remark-1",
						UpdatedBy:          "lea-workflow",
					},
					TriggerUserComms: true,
				},
				res: &userPb.UpdateAccessRevokeDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: true,
				req: &authPb.SignOutRequest{
					Actor: &typesv2.Actor{
						Id:       "actor-1",
						EntityId: "actor-entity-1",
					},
				},
				res: &authPb.SignOutResponse{Status: rpc.StatusOk()},
				err: nil,
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "SUCCESS: DEBIT FREEZE",
			args: args{
				req: &activityPb.AppAccessUpdateRequest{
					ActorId: "actor-1",
					Action:  enumsPb.Action_ACTION_DEBIT_FREEZE,
					RequestReason: &risk.RequestReason{
						Reason:  enumsPb.RequestReason_REQUEST_REASON_LEA_COMPLAINT,
						Remarks: "remark-1",
					},
					InitiatedBy: "lea-workflow",
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: true,
				req: &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				},
				res: &actorPb.GetActorByIdResponse{
					Actor: &typesv2.Actor{
						EntityId: "actor-entity-1",
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSavingsUpdate: mockSavingsUpdate{
				isEnable: true,
				req: &savingsPb.UpdateAccountRequest{
					Identifier: &savingsPb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: "actor-entity-1"},
					Constraints: &savingsPb.AccountConstraints{
						AccessLevel:  savingsPb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS,
						Restrictions: []savingsPb.Restriction{savingsPb.Restriction_RESTRICTION_DEBIT_FREEZE},
						UpdateDetails: &savingsPb.ConstraintsUpdateDetails{
							Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_LEA_COMPLAINT,
							Remarks:   "remark-1",
							UpdatedBy: "lea-workflow",
						},
					},
					UpdateMask: []savingsPb.AccountFieldMask{savingsPb.AccountFieldMask_CONSTRAINTS},
				},
				res: &savingsPb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockUserUpdateUser: mockUserUpdateUser{
				isEnable: true,
				req: &userPb.UpdateAccessRevokeDetailsRequest{
					UserIdentifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier{
						Identifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier_UserId{
							UserId: "actor-entity-1",
						},
					},
					AccessRevokeDetails: &userPb.AccessRevokeDetails{
						AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED,
						RestoreReason:      userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_DEBIT_FREEZE,
						Remarks:            "remark-1",
						UpdatedBy:          "lea-workflow",
					},
					TriggerUserComms: true,
				},
				res: &userPb.UpdateAccessRevokeDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: false,
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "SUCCESS: CREDIT FREEZE",
			args: args{
				req: &activityPb.AppAccessUpdateRequest{
					ActorId: "actor-1",
					Action:  enumsPb.Action_ACTION_CREDIT_FREEZE,
					RequestReason: &risk.RequestReason{
						Reason:  enumsPb.RequestReason_REQUEST_REASON_LEA_COMPLAINT,
						Remarks: "remark-1",
					},
					InitiatedBy: "lea-workflow",
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: true,
				req: &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				},
				res: &actorPb.GetActorByIdResponse{
					Actor: &typesv2.Actor{
						EntityId: "actor-entity-1",
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSavingsUpdate: mockSavingsUpdate{
				isEnable: true,
				req: &savingsPb.UpdateAccountRequest{
					Identifier: &savingsPb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: "actor-entity-1"},
					Constraints: &savingsPb.AccountConstraints{
						AccessLevel:  savingsPb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS,
						Restrictions: []savingsPb.Restriction{savingsPb.Restriction_RESTRICTION_CREDIT_FREEZE},
						UpdateDetails: &savingsPb.ConstraintsUpdateDetails{
							Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_LEA_COMPLAINT,
							Remarks:   "remark-1",
							UpdatedBy: "lea-workflow",
						},
					},
					UpdateMask: []savingsPb.AccountFieldMask{savingsPb.AccountFieldMask_CONSTRAINTS},
				},
				res: &savingsPb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockUserUpdateUser: mockUserUpdateUser{
				isEnable: true,
				req: &userPb.UpdateAccessRevokeDetailsRequest{
					UserIdentifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier{
						Identifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier_UserId{
							UserId: "actor-entity-1",
						},
					},
					AccessRevokeDetails: &userPb.AccessRevokeDetails{
						AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED,
						RestoreReason:      userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_CREDIT_FREEZE,
						Remarks:            "remark-1",
						UpdatedBy:          "lea-workflow",
					},
					TriggerUserComms: true,
				},
				res: &userPb.UpdateAccessRevokeDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: false,
			},
			wantErr:   false,
			assertErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetActorById.isEnable {
				md.actorClient.EXPECT().GetActorById(gomock.Any(), tt.mockGetActorById.req).Return(tt.mockGetActorById.res, tt.mockGetActorById.err)
			}
			if tt.mockSavingsUpdate.isEnable {
				md.savingsClient.EXPECT().UpdateAccount(gomock.Any(), newSavingsUpdateRequestArgMatcher(tt.mockSavingsUpdate.req)).Return(tt.mockSavingsUpdate.res, tt.mockSavingsUpdate.err)
			}
			if tt.mockUserUpdateUser.isEnable {
				md.userClient.EXPECT().UpdateAccessRevokeDetails(gomock.Any(), newUpdateUserRequestArgMatcher(tt.mockUserUpdateUser.req)).Return(tt.mockUserUpdateUser.res, tt.mockUserUpdateUser.err)
			}
			if tt.mockSignOutUser.isEnable {
				md.authClient.EXPECT().SignOut(gomock.Any(), tt.mockSignOutUser.req).Return(tt.mockSignOutUser.res, tt.mockSignOutUser.err)
			}
			var result *activityPb.AppAccessUpdateResponse
			got, err := env.ExecuteActivity(riskNs.AppAccessUpdate, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessRecurringPaymentCreation() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetBankActionStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("GetBankActionStatus() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("GetBankActionStatus() got = %v, want %v", result, tt.want)
				return
			}
			assertTest()
		})
	}
}
