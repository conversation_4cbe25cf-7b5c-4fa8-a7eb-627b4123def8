package activity_test

import (
	"fmt"
	"testing"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/risk"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
)

type SavingsUpdateRequestArgMatcher struct {
	want *savingsPb.UpdateAccountRequest
}

func newSavingsUpdateRequestArgMatcher(want *savingsPb.UpdateAccountRequest) *SavingsUpdateRequestArgMatcher {
	return &SavingsUpdateRequestArgMatcher{want: want}
}

func (c *SavingsUpdateRequestArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*savingsPb.UpdateAccountRequest)
	if !ok {
		return false
	}

	c.want.Constraints.UpdateDetails.UpdatedAt = got.Constraints.UpdateDetails.UpdatedAt
	return proto.Equal(c.want, got)
}

func (c *SavingsUpdateRequestArgMatcher) String() string {
	return fmt.Sprintf("want: %v", c.want)
}

type UpdateUserRequestArgMatcher struct {
	want *userPb.UpdateAccessRevokeDetailsRequest
}

func newUpdateUserRequestArgMatcher(want *userPb.UpdateAccessRevokeDetailsRequest) *UpdateUserRequestArgMatcher {
	return &UpdateUserRequestArgMatcher{want: want}
}

func (c *UpdateUserRequestArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*userPb.UpdateAccessRevokeDetailsRequest)
	if !ok {
		return false
	}

	c.want.AccessRevokeDetails.UpdatedAt = got.AccessRevokeDetails.UpdatedAt
	return proto.Equal(c.want, got)
}

func (c *UpdateUserRequestArgMatcher) String() string {
	return fmt.Sprintf("want: %v", c.want)
}

func TestProcessor_FiFreezeStatusUpdate(t *testing.T) {
	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type args struct {
		req *activityPb.Request
	}
	type getByClientReqIdReq struct {
		clientReqId string
		fieldMask   []risk.RiskBankActionsFieldMask
	}
	type mockGetByClientReqId struct {
		isEnable bool
		res      *risk.RiskBankActions
		req      getByClientReqIdReq
		err      error
	}
	type mockSavingsUpdate struct {
		isEnable bool
		req      *savingsPb.UpdateAccountRequest
		res      *savingsPb.UpdateAccountResponse
		err      error
	}
	type mockUserUpdateUser struct {
		isEnable bool
		req      *userPb.UpdateAccessRevokeDetailsRequest
		res      *userPb.UpdateAccessRevokeDetailsResponse
		err      error
	}
	type mockGetActorById struct {
		isEnable bool
		req      *actorPb.GetActorByIdRequest
		res      *actorPb.GetActorByIdResponse
		err      error
	}
	type mockSignOutUser struct {
		isEnable bool
		req      *authPb.SignOutRequest
		res      *authPb.SignOutResponse
		err      error
	}
	tests := []struct {
		name                 string
		args                 args
		mockGetByClientReqId mockGetByClientReqId
		mockSavingsUpdate    mockSavingsUpdate
		mockUserUpdateUser   mockUserUpdateUser
		mockGetActorById     mockGetActorById
		mockSignOutUser      mockSignOutUser
		wantErr              bool
		want                 *activityPb.Response
		assertErr            func(err error) bool
	}{
		{
			name: "FAIL: Account not in success",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			mockGetByClientReqId: mockGetByClientReqId{
				isEnable: true,
				res: &risk.RiskBankActions{
					ActorId:     "actor-1",
					State:       riskEnumsPb.State_STATE_CANCELED,
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
				},
				req: getByClientReqIdReq{
					clientReqId: "client-req-id-1",
					fieldMask: []risk.RiskBankActionsFieldMask{
						risk.RiskBankActionsFieldMask_ALL,
					},
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: false,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: false,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "FAIL: Account not of savings type",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			mockGetByClientReqId: mockGetByClientReqId{
				isEnable: true,
				res: &risk.RiskBankActions{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_CURRENT,
					State:       riskEnumsPb.State_STATE_SUCCESS,
				},
				req: getByClientReqIdReq{
					clientReqId: "client-req-id-1",
					fieldMask: []risk.RiskBankActionsFieldMask{
						risk.RiskBankActionsFieldMask_ALL,
					},
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: false,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: false,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "SUCCESS: UNFREEZE",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			mockGetByClientReqId: mockGetByClientReqId{
				isEnable: true,
				req: getByClientReqIdReq{
					clientReqId: "client-req-id-1",
					fieldMask: []risk.RiskBankActionsFieldMask{
						risk.RiskBankActionsFieldMask_ALL,
					},
				},
				res: &risk.RiskBankActions{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					State:       riskEnumsPb.State_STATE_SUCCESS,
					Action:      riskEnumsPb.Action_ACTION_UNFREEZE,
					RequestReason: &risk.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_DUE_DILIGENCE,
						Remarks: "remark-1",
					},
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: true,
				req: &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				},
				res: &actorPb.GetActorByIdResponse{
					Actor: &typesPb.Actor{
						EntityId: "actor-entity-1",
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSavingsUpdate: mockSavingsUpdate{
				isEnable: true,
				req: &savingsPb.UpdateAccountRequest{
					Identifier: &savingsPb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: "actor-entity-1"},
					Constraints: &savingsPb.AccountConstraints{
						AccessLevel:  savingsPb.AccessLevel_ACCESS_LEVEL_FULL_ACCESS,
						Restrictions: nil,
						UpdateDetails: &savingsPb.ConstraintsUpdateDetails{
							Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_DUE_DILIGENCE,
							Remarks:   "remark-1",
							UpdatedBy: "risk-ops via workflow",
						},
					},
					UpdateMask: []savingsPb.AccountFieldMask{savingsPb.AccountFieldMask_CONSTRAINTS},
				},
				res: &savingsPb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockUserUpdateUser: mockUserUpdateUser{
				isEnable: true,
				req: &userPb.UpdateAccessRevokeDetailsRequest{
					UserIdentifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier{
						Identifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier_UserId{
							UserId: "actor-entity-1",
						},
					},
					AccessRevokeDetails: &userPb.AccessRevokeDetails{
						AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED,
						RestoreReason:      userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_DUE_DILIGENCE,
						UpdatedBy:          "risk-ops via workflow",
						Remarks:            "remark-1",
					},
					TriggerUserComms: true,
				},
				res: &userPb.UpdateAccessRevokeDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: false,
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "SUCCESS: FREEZE",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			mockGetByClientReqId: mockGetByClientReqId{
				isEnable: true,
				req: getByClientReqIdReq{
					clientReqId: "client-req-id-1",
					fieldMask: []risk.RiskBankActionsFieldMask{
						risk.RiskBankActionsFieldMask_ALL,
					},
				},
				res: &risk.RiskBankActions{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					State:       riskEnumsPb.State_STATE_SUCCESS,
					Action:      riskEnumsPb.Action_ACTION_FULL_FREEZE,
					RequestReason: &risk.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT,
						Remarks: "remark-1",
					},
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: true,
				req: &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				},
				res: &actorPb.GetActorByIdResponse{
					Actor: &typesPb.Actor{
						EntityId: "actor-entity-1",
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSavingsUpdate: mockSavingsUpdate{
				isEnable: true,
				req: &savingsPb.UpdateAccountRequest{
					Identifier: &savingsPb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: "actor-entity-1"},
					Constraints: &savingsPb.AccountConstraints{
						AccessLevel:  savingsPb.AccessLevel_ACCESS_LEVEL_NO_ACCESS,
						Restrictions: nil,
						UpdateDetails: &savingsPb.ConstraintsUpdateDetails{
							Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_FRAUDULENT_ACCOUNT,
							Remarks:   "remark-1",
							UpdatedBy: "risk-ops via workflow",
						},
					},
					UpdateMask: []savingsPb.AccountFieldMask{savingsPb.AccountFieldMask_CONSTRAINTS},
				},
				res: &savingsPb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockUserUpdateUser: mockUserUpdateUser{
				isEnable: true,
				req: &userPb.UpdateAccessRevokeDetailsRequest{
					UserIdentifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier{
						Identifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier_UserId{
							UserId: "actor-entity-1",
						},
					},
					AccessRevokeDetails: &userPb.AccessRevokeDetails{
						AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED,
						RestoreReason:      userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_TOTAL_FREEZE,
						Remarks:            "remark-1",
						UpdatedBy:          "risk-ops via workflow",
					},
					TriggerUserComms: true,
				},
				res: &userPb.UpdateAccessRevokeDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: true,
				req: &authPb.SignOutRequest{
					Actor: &typesPb.Actor{
						Id:       "actor-1",
						EntityId: "actor-entity-1",
					},
				},
				res: &authPb.SignOutResponse{Status: rpc.StatusOk()},
				err: nil,
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "SUCCESS: Manual override",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			mockGetByClientReqId: mockGetByClientReqId{
				isEnable: true,
				req: getByClientReqIdReq{
					clientReqId: "client-req-id-1",
					fieldMask: []risk.RiskBankActionsFieldMask{
						risk.RiskBankActionsFieldMask_ALL,
					},
				},
				res: &risk.RiskBankActions{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					State:       riskEnumsPb.State_STATE_REJECT_MANUAL_OVERRIDE,
					Action:      riskEnumsPb.Action_ACTION_FULL_FREEZE,
					RequestReason: &risk.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT,
						Remarks: "remark-1",
					},
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: false,
				err:      nil,
			},
			mockSavingsUpdate: mockSavingsUpdate{
				isEnable: false,
				err:      nil,
			},
			mockUserUpdateUser: mockUserUpdateUser{
				isEnable: false,
				err:      nil,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: false,
				err:      nil,
			},
			wantErr:   false,
			assertErr: nil,
		},
		{
			name: "SUCCESS: UpdateAccountResponse returns internal",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
				},
			},
			mockGetByClientReqId: mockGetByClientReqId{
				isEnable: true,
				req: getByClientReqIdReq{
					clientReqId: "client-req-id-1",
					fieldMask: []risk.RiskBankActionsFieldMask{
						risk.RiskBankActionsFieldMask_ALL,
					},
				},
				res: &risk.RiskBankActions{
					ActorId:     "actor-1",
					AccountId:   "account-1",
					AccountType: accounts.Type_SAVINGS,
					State:       riskEnumsPb.State_STATE_SUCCESS,
					Action:      riskEnumsPb.Action_ACTION_UNFREEZE,
					RequestReason: &risk.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_CUSTOMER_OUTCALL,
						Remarks: "remark-1",
					},
				},
			},
			mockGetActorById: mockGetActorById{
				isEnable: true,
				req: &actorPb.GetActorByIdRequest{
					Id: "actor-1",
				},
				res: &actorPb.GetActorByIdResponse{
					Actor: &typesPb.Actor{
						EntityId: "actor-entity-1",
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSavingsUpdate: mockSavingsUpdate{
				isEnable: true,
				req: &savingsPb.UpdateAccountRequest{
					Identifier: &savingsPb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: "actor-entity-1"},
					Constraints: &savingsPb.AccountConstraints{
						AccessLevel:  savingsPb.AccessLevel_ACCESS_LEVEL_FULL_ACCESS,
						Restrictions: nil,
						UpdateDetails: &savingsPb.ConstraintsUpdateDetails{
							Reason:    savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_CUSTOMER_OUTCALL,
							Remarks:   "remark-1",
							UpdatedBy: "risk-ops via workflow",
						},
					},
					UpdateMask: []savingsPb.AccountFieldMask{savingsPb.AccountFieldMask_CONSTRAINTS},
				},
				res: &savingsPb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockUserUpdateUser: mockUserUpdateUser{
				isEnable: true,
				req: &userPb.UpdateAccessRevokeDetailsRequest{
					UserIdentifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier{
						Identifier: &userPb.UpdateAccessRevokeDetailsRequest_UserIdentifier_UserId{
							UserId: "actor-entity-1",
						},
					},
					AccessRevokeDetails: &userPb.AccessRevokeDetails{
						AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED,
						RestoreReason:      userPb.AccessRestoreReason_ACCESS_RESTORE_REASON_CUSTOMER_OUTCALL,
						Remarks:            "remark-1",
						UpdatedBy:          "risk-ops via workflow",
					},
					TriggerUserComms: true,
				},
				res: &userPb.UpdateAccessRevokeDetailsResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			mockSignOutUser: mockSignOutUser{
				isEnable: false,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByClientReqId.isEnable {
				md.riskBankActionDao.EXPECT().GetByClientReqId(gomock.Any(), tt.mockGetByClientReqId.req.clientReqId, tt.mockGetByClientReqId.req.fieldMask).Return(tt.mockGetByClientReqId.res, tt.mockGetByClientReqId.err)
			}
			if tt.mockGetActorById.isEnable {
				md.actorClient.EXPECT().GetActorById(gomock.Any(), tt.mockGetActorById.req).Return(tt.mockGetActorById.res, tt.mockGetActorById.err)
			}
			if tt.mockSavingsUpdate.isEnable {
				md.savingsClient.EXPECT().UpdateAccount(gomock.Any(), newSavingsUpdateRequestArgMatcher(tt.mockSavingsUpdate.req)).Return(tt.mockSavingsUpdate.res, tt.mockSavingsUpdate.err)
			}
			if tt.mockUserUpdateUser.isEnable {
				md.userClient.EXPECT().UpdateAccessRevokeDetails(gomock.Any(), newUpdateUserRequestArgMatcher(tt.mockUserUpdateUser.req)).Return(tt.mockUserUpdateUser.res, tt.mockUserUpdateUser.err)
			}
			if tt.mockSignOutUser.isEnable {
				md.authClient.EXPECT().SignOut(gomock.Any(), tt.mockSignOutUser.req).Return(tt.mockSignOutUser.res, tt.mockSignOutUser.err)
			}
			var result *activityPb.Response
			got, err := env.ExecuteActivity(riskNs.FiFreezeStatusUpdate, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessRecurringPaymentCreation() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetBankActionStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("GetBankActionStatus() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("GetBankActionStatus() got = %v, want %v", result, tt.want)
				return
			}
			assertTest()
		})
	}
}
