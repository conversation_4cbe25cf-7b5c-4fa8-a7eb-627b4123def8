package essential

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/risk/case_management/dao"
)

//go:generate mockgen -source=rule.go -destination=mocks/mock_rule_manager.go -package=mock_essential
type RuleManager interface {
	// GetRulesForCaseId - get all the rules against the case id
	// fails with epifierrors.InvalidArgument when case id is nil
	// fails with epifierrors.InvalidArgument when no alerts could be found against the case id
	// fails when not able to fetch the alerts against the case id
	// fails when not able to fetch the rules
	GetRulesForCaseId(ctx context.Context, caseId string) ([]*case_management.Rule, error)

	// GetPrecisionForRule - retrieves the precision of a rule.
	// if the ForceUseSeedPrecision flag is true, it returns the rule's seed precision;
	// otherwise, it returns the most recent calculated precision from the precision calculator.
	// if precision for given rule is not found in db, it returns the rule's seed precision.
	GetPrecisionForRule(ctx context.Context, rule *case_management.Rule) (float32, error)
}

type RuleManagerImpl struct {
	alertDao         dao.AlertDao
	ruleDao          dao.RuleDao
	rulePrecisionDao dao.RulePrecisionDao
}

var _ RuleManager = &RuleManagerImpl{}

func NewRuleManagerImpl(alertDao dao.AlertDao, ruleDao dao.RuleDao, rulePrecisionDao dao.RulePrecisionDao) *RuleManagerImpl {
	return &RuleManagerImpl{
		alertDao:         alertDao,
		ruleDao:          ruleDao,
		rulePrecisionDao: rulePrecisionDao,
	}
}

func (c *RuleManagerImpl) GetRulesForCaseId(ctx context.Context, caseId string) ([]*case_management.Rule, error) {
	if len(caseId) == 0 {
		return nil, epifierrors.ErrInvalidArgument
	}
	alerts, alertsErr := c.alertDao.GetByCaseId(ctx, caseId)
	if alertsErr != nil {
		if errors.Is(alertsErr, epifierrors.ErrRecordNotFound) {
			return nil, fmt.Errorf("coludn't get the alerts %w", epifierrors.ErrInvalidArgument)
		}
		return nil, fmt.Errorf("coludn't get the alerts %w", alertsErr)
	}
	var ruleIds []string
	for _, alert := range alerts {
		ruleIds = append(ruleIds, alert.GetRuleId())
	}
	rules, rulesErr := c.ruleDao.GetBulkById(ctx, ruleIds)
	if rulesErr != nil {
		return nil, fmt.Errorf("coludn't get the rules %w", rulesErr)
	}

	return rules, nil
}

func (r *RuleManagerImpl) GetPrecisionForRule(ctx context.Context, rule *case_management.Rule) (float32, error) {
	if rule.GetForceUseSeedPrecision() {
		return rule.GetSeedPrecision(), nil
	}
	rulePrecision, err := r.rulePrecisionDao.GetLatestByRuleId(ctx, rule.GetId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return rule.GetSeedPrecision(), nil
	case err != nil:
		return 0, fmt.Errorf("failed to fetch rule precision from db %w", err)
	}
	return rulePrecision.GetPrecision(), nil
}
