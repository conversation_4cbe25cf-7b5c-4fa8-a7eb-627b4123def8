// nolint:protogetter
package workflow_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"errors"
	"testing"
	"time"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"
	cmActivityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	caseReviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	enumsPb "github.com/epifi/gamma/api/risk/case_management/review"
	cmWorkflowPb "github.com/epifi/gamma/api/risk/case_management/workflow"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	celestialActivity "github.com/epifi/gamma/celestial/activity"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	cmActivity "github.com/epifi/gamma/risk/case_management/activity"
	cmWorkflow "github.com/epifi/gamma/risk/case_management/workflow"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.temporal.io/sdk/workflow"
	protoJson "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/anypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	testActorId1 = "actor-id-1"
)

var (
	totalF = &caseReviewPb.Action{
		CaseId:     "888",
		ReviewType: enumsPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
		ActionType: caseReviewPb.ActionType_ACTION_TYPE_FREEZE_ACCOUNT,
		Parameters: &caseReviewPb.ActionParameters{
			Parameter: &caseReviewPb.ActionParameters_AccountFreezeParameters{
				AccountFreezeParameters: &caseReviewPb.AccountFreezeParameters{
					FreezeLevel: caseReviewPb.FreezeLevel_FREEZE_LEVEL_TOTAL,
					RequestReason: &caseReviewPb.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
						Remarks: "Manually resolved",
					},
				},
			},
		},
		Source:       caseReviewPb.ActionSource_ACTION_SOURCE_AUTO_FREEZE_FLOW,
		AnalystEmail: "<EMAIL>",
		InitiatedAt:  &timestampPb.Timestamp{Seconds: **********, Nanos: *********},
	}
	a1Payload, _ = protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case: &caseReviewPb.Case{
			Id:         totalF.CaseId,
			ActorId:    testActorId1,
			ReviewType: totalF.ReviewType,
		},
		ActionType:   totalF.ActionType,
		Parameters:   totalF.Parameters,
		Source:       totalF.Source,
		AnalystEmail: totalF.AnalystEmail,
		InitiatedAt:  totalF.InitiatedAt,
	})
	performActionRequest = &cmWorkflowPb.FullFreezeRequest{
		CaseId: totalF.CaseId,
		Parameters: &caseReviewPb.AccountFreezeParameters{
			FreezeLevel: caseReviewPb.FreezeLevel_FREEZE_LEVEL_TOTAL,
			RequestReason: &caseReviewPb.RequestReason{
				Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
				Remarks: "Manually resolved",
			},
		},
		AnalystEmail: totalF.AnalystEmail,
		ActorId:      testActorId1,
	}
	performActionBytes, _ = protoJson.Marshal(performActionRequest)
	childPayload, _       = anypb.New(performActionRequest)

	creditF = &caseReviewPb.Action{
		CaseId:     "888",
		ReviewType: enumsPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
		ActionType: caseReviewPb.ActionType_ACTION_TYPE_FREEZE_ACCOUNT,
		Parameters: &caseReviewPb.ActionParameters{
			Parameter: &caseReviewPb.ActionParameters_AccountFreezeParameters{
				AccountFreezeParameters: &caseReviewPb.AccountFreezeParameters{
					FreezeLevel: caseReviewPb.FreezeLevel_FREEZE_LEVEL_CREDIT,
					RequestReason: &caseReviewPb.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
						Remarks: "Manually resolved",
					},
				},
			},
		},
		Source:       caseReviewPb.ActionSource_ACTION_SOURCE_AUTO_FREEZE_FLOW,
		AnalystEmail: "<EMAIL>",
		InitiatedAt:  &timestampPb.Timestamp{Seconds: **********, Nanos: *********},
	}
	creditFPayload, _ = protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case: &caseReviewPb.Case{
			Id:         creditF.CaseId,
			ActorId:    testActorId1,
			ReviewType: creditF.ReviewType,
		},
		ActionType:   creditF.ActionType,
		Parameters:   creditF.Parameters,
		Source:       creditF.Source,
		AnalystEmail: creditF.AnalystEmail,
		InitiatedAt:  creditF.InitiatedAt,
	})
	cfREq = &cmWorkflowPb.CreditFreezeRequest{
		CaseId: totalF.CaseId,
		Parameters: &caseReviewPb.AccountFreezeParameters{
			FreezeLevel: caseReviewPb.FreezeLevel_FREEZE_LEVEL_CREDIT,
			RequestReason: &caseReviewPb.RequestReason{
				Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
				Remarks: "Manually resolved",
			},
		},
		AnalystEmail: totalF.AnalystEmail,
		ActorId:      testActorId1,
		Case: &caseReviewPb.Case{
			Id:         passUser.CaseId,
			ActorId:    testActorId1,
			ReviewType: passUser.ReviewType,
		},
	}
	cfActionBytes, _        = protoJson.Marshal(cfREq)
	cfActionChildPayload, _ = anypb.New(cfREq)

	clientReqIdObj = &workflowPb.ClientReqId{
		Id:     "client-req-id",
		Client: workflowPb.Client_RISK,
	}

	passUser = &caseReviewPb.Action{
		CaseId:       "888",
		ReviewType:   enumsPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
		ActionType:   caseReviewPb.ActionType_ACTION_TYPE_PASS_ACCOUNT,
		Source:       caseReviewPb.ActionSource_ACTION_SOURCE_AUTO_FREEZE_FLOW,
		AnalystEmail: "<EMAIL>",
		InitiatedAt:  &timestampPb.Timestamp{Seconds: **********, Nanos: *********},
	}

	passUserPayload, _ = protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case: &caseReviewPb.Case{
			Id:         passUser.CaseId,
			ActorId:    testActorId1,
			ReviewType: passUser.ReviewType,
		},
		ActionType:   passUser.ActionType,
		Source:       passUser.Source,
		AnalystEmail: passUser.AnalystEmail,
		InitiatedAt:  passUser.InitiatedAt,
	})

	performPassUserRequest = &cmWorkflowPb.PassUserRequest{
		CaseId:       passUser.CaseId,
		AnalystEmail: passUser.AnalystEmail,
		ActorId:      testActorId1,
	}
	performPassUserBytes, _        = protoJson.Marshal(performPassUserRequest)
	performPassUserChildPayload, _ = anypb.New(performPassUserRequest)

	moveToReview, _ = protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case: &caseReviewPb.Case{
			Id:         passUser.CaseId,
			ActorId:    testActorId1,
			ReviewType: passUser.ReviewType,
		},
		ActionType:   caseReviewPb.ActionType_ACTION_TYPE_MOVE_TO_REVIEW,
		Source:       passUser.Source,
		AnalystEmail: passUser.AnalystEmail,
		InitiatedAt:  passUser.InitiatedAt,
	})

	moveToReviewRequest = &cmWorkflowPb.MoveToReviewRequest{
		CaseId:       passUser.CaseId,
		AnalystEmail: passUser.AnalystEmail,
		ActorId:      testActorId1,
	}
	moveToReviewBytes, _ = protoJson.Marshal(moveToReviewRequest)

	requestUserInfo, _ = protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case: &caseReviewPb.Case{
			Id:         passUser.CaseId,
			ActorId:    testActorId1,
			ReviewType: passUser.ReviewType,
		},
		ActionType:   caseReviewPb.ActionType_ACTION_TYPE_REQUEST_USER_INFO,
		Source:       passUser.Source,
		AnalystEmail: passUser.AnalystEmail,
		InitiatedAt:  passUser.InitiatedAt,
	})

	requestUserInfoRequest = &cmWorkflowPb.RequestUserInfoRequest{
		CaseId:       passUser.CaseId,
		AnalystEmail: passUser.AnalystEmail,
		ActorId:      testActorId1,
	}
	requestUserInfoBytes, _ = protoJson.Marshal(requestUserInfoRequest)

	passUserOnboardingPayload, _ = protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case: &caseReviewPb.Case{
			Id:         totalF.CaseId,
			ActorId:    testActorId1,
			ReviewType: totalF.ReviewType,
		},
		ActionType:   caseReviewPb.ActionType_ACTION_TYPE_PASS_USER_ONBOARDING,
		Source:       totalF.Source,
		AnalystEmail: totalF.AnalystEmail,
		InitiatedAt:  totalF.InitiatedAt,
	})

	processOnboardingReviewRequest = &cmWorkflowPb.ProcessOnboardingReviewVerdictRequest{
		CaseId:  passUser.CaseId,
		ActorId: testActorId1,
		Verdict: cmEnumsPb.Verdict_VERDICT_FAIL,
	}
	processOnboardingReviewBytes, _   = protoJson.Marshal(processOnboardingReviewRequest)
	passUserOnboardingChildPayload, _ = anypb.New(processOnboardingReviewRequest)

	processReviewActionForLivenessRetryPayload, _ = protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case: &caseReviewPb.Case{
			Id:         totalF.CaseId,
			ActorId:    testActorId1,
			ReviewType: totalF.ReviewType,
		},
		ActionType:   caseReviewPb.ActionType_ACTION_TYPE_ADD_LIVENESS_RETRIES,
		Source:       totalF.Source,
		AnalystEmail: totalF.AnalystEmail,
		InitiatedAt:  totalF.InitiatedAt,
	})

	addLivenessRequest = &cmWorkflowPb.AddLivenessRetriesRequest{
		ActorId: testActorId1,
	}
	addLivenessBytes, _ = protoJson.Marshal(addLivenessRequest)

	snoozeTill = timestampPb.New(time.Now().AddDate(0, 0, 1))
	snooze, _  = protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case: &caseReviewPb.Case{
			Id:         passUser.CaseId,
			ActorId:    testActorId1,
			ReviewType: passUser.ReviewType,
		},
		Parameters:   &caseReviewPb.ActionParameters{Parameter: &caseReviewPb.ActionParameters_SnoozeParameters{SnoozeParameters: &caseReviewPb.SnoozeParameters{SnoozeTill: snoozeTill}}},
		ActionType:   caseReviewPb.ActionType_ACTION_TYPE_SNOOZE,
		Source:       passUser.Source,
		AnalystEmail: passUser.AnalystEmail,
		InitiatedAt:  passUser.InitiatedAt,
	})

	snoozeRequest = &cmWorkflowPb.SnoozeRequest{
		CaseId:           passUser.CaseId,
		AnalystEmail:     passUser.AnalystEmail,
		ActorId:          testActorId1,
		SnoozeTill:       snoozeTill,
		SnoozeParameters: &caseReviewPb.SnoozeParameters{SnoozeTill: snoozeTill},
	}
	snoozeBytes, _ = protoJson.Marshal(snoozeRequest)

	processAFUReviewVerdict, _ = protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case: &caseReviewPb.Case{
			Id:         passUser.CaseId,
			ActorId:    testActorId1,
			ReviewType: passUser.ReviewType,
		},
		ActionType:   caseReviewPb.ActionType_ACTION_TYPE_FAIL_AFU,
		Source:       passUser.Source,
		AnalystEmail: passUser.AnalystEmail,
		InitiatedAt:  passUser.InitiatedAt,
	})

	ProcessAFUReviewVerdictRequest = &cmWorkflowPb.ProcessAFUReviewVerdictRequest{
		CaseId:       passUser.CaseId,
		AnalystEmail: passUser.AnalystEmail,
		ActorId:      testActorId1,
		Verdict:      cmEnumsPb.Verdict_VERDICT_FAIL,
	}
	processAFUReviewVerdictBytes, _ = protoJson.Marshal(ProcessAFUReviewVerdictRequest)

	dfUser = &caseReviewPb.Action{
		CaseId:     "888",
		ReviewType: enumsPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
		ActionType: caseReviewPb.ActionType_ACTION_TYPE_FREEZE_ACCOUNT,
		Source:     caseReviewPb.ActionSource_ACTION_SOURCE_AUTO_FREEZE_FLOW,
		Parameters: &caseReviewPb.ActionParameters{
			Parameter: &caseReviewPb.ActionParameters_AccountFreezeParameters{
				AccountFreezeParameters: &caseReviewPb.AccountFreezeParameters{
					FreezeLevel: caseReviewPb.FreezeLevel_FREEZE_LEVEL_DEBIT,
					RequestReason: &caseReviewPb.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
						Remarks: "Manually resolved",
					},
				},
			},
		},
		AnalystEmail: "<EMAIL>",
		InitiatedAt:  &timestampPb.Timestamp{Seconds: **********, Nanos: *********},
	}

	dfReqProcessReview = &cmWorkflowPb.DebitFreezeRequest{
		CaseId: dfUser.CaseId,
		Parameters: &caseReviewPb.AccountFreezeParameters{
			FreezeLevel: caseReviewPb.FreezeLevel_FREEZE_LEVEL_DEBIT,
			RequestReason: &caseReviewPb.RequestReason{
				Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
				Remarks: "Manually resolved",
			},
		},
		AnalystEmail: dfUser.AnalystEmail,
		ActorId:      testActorId1,
	}

	performReviewActionDfREq, _ = protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case: &caseReviewPb.Case{
			Id:         passUser.CaseId,
			ActorId:    testActorId1,
			ReviewType: passUser.ReviewType,
		},
		ActionType:   caseReviewPb.ActionType_ACTION_TYPE_FREEZE_ACCOUNT,
		Source:       passUser.Source,
		AnalystEmail: passUser.AnalystEmail,
		InitiatedAt:  passUser.InitiatedAt,
		Parameters: &caseReviewPb.ActionParameters{
			Parameter: &caseReviewPb.ActionParameters_AccountFreezeParameters{
				AccountFreezeParameters: &caseReviewPb.AccountFreezeParameters{
					FreezeLevel: caseReviewPb.FreezeLevel_FREEZE_LEVEL_DEBIT,
					RequestReason: &caseReviewPb.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
						Remarks: "Manually resolved",
					},
				},
			},
		},
	})
)

const (
	defaultWorkflowID = "default-test-workflow-id"
	clientReqId       = "default-test-client-req-id"
	ownership         = commontypes.Ownership_EPIFI_TECH
	newWFId           = "new-wf-id"
)

func TestPerformReviewAction(t *testing.T) {
	type args struct {
		req *workflowPb.Request
	}
	type mockGetWorkflowProcessingParamsV2 struct {
		enable bool
		req    *activityPb.GetWorkflowProcessingParamsV2Request
		res    *activityPb.GetWorkflowProcessingParamsV2Response
		err    error
	}
	type mockLogActionInDB struct {
		enable bool
		req    *cmActivityPb.LogActionInDBRequest
		res    *cmActivityPb.LogActionInDBResponse
		err    error
	}
	type mockUpdateWorkflowStage struct {
		enable bool
		req    *activityPb.UpdateWorkflowStageRequest
		res    *activityPb.UpdateWorkflowStageResponse
		err    error
	}
	type mockInitiateWorkflowStageV2 struct {
		enable bool
		req    *activityPb.InitiateWorkflowStageV2Request
		err    error
	}
	type mockPublishWorkflowUpdateEventV2 struct {
		enable bool
		req    *activityPb.PublishWorkflowUpdateEventV2Request
		err    error
	}
	type mockFullFreeze struct {
		enable bool
		req    *workflowPb.Request
		cwo    workflow.ChildWorkflowOptions
		res    *cmWorkflowPb.FullFreezeResponse
		err    error
	}
	type mockCreditFreeze struct {
		enable bool
		req    *workflowPb.Request
		cwo    workflow.ChildWorkflowOptions
		res    *cmWorkflowPb.CreditFreezeResponse
		err    error
	}
	type mockDebitFreeze struct {
		enable bool
		req    *cmWorkflowPb.DebitFreezeRequest
		cwo    workflow.ChildWorkflowOptions
		res    *cmWorkflowPb.DebitFreezeResponse
		err    error
	}
	type mockPassUser struct {
		enable bool
		req    *workflowPb.Request
		res    *workflowPb.Response
		err    error
	}
	type mockMoveToReview struct {
		enable bool
		req    *cmWorkflowPb.MoveToReviewRequest
		res    *cmWorkflowPb.MoveToReviewResponse
		err    error
	}
	type mockRequestUserInfo struct {
		enable bool
		req    *cmWorkflowPb.RequestUserInfoRequest
		res    *cmWorkflowPb.RequestUserInfoResponse
		err    error
	}
	type mockSnooze struct {
		enable bool
		req    *cmWorkflowPb.SnoozeRequest
		res    *cmWorkflowPb.SnoozeResponse
		err    error
	}
	type mockProcessAFUReviewAction struct {
		enable bool
		req    *cmWorkflowPb.ProcessAFUReviewVerdictRequest
		res    *cmWorkflowPb.ProcessAFUReviewVerdictResponse
		err    error
	}
	type mockRiskPerformActionUnFreeze struct {
		enable bool
		req    *workflowPb.Request
		res    *cmWorkflowPb.UnfreezeResponse
		err    error
	}
	type mockRiskPerformActionCreditFreeze struct {
		enable bool
		req    *workflowPb.Request
		res    *workflowPb.Response
		err    error
	}
	type processOnboardingReview struct {
		enable bool
		req    *cmWorkflowPb.ProcessOnboardingReviewVerdictRequest
		res    *cmWorkflowPb.ProcessOnboardingReviewVerdictResponse
		err    error
	}
	type mockRetryLiveness struct {
		enable bool
		req    *cmWorkflowPb.AddLivenessRetriesRequest
		res    *cmWorkflowPb.AddLivenessRetriesResponse
		err    error
	}
	type mockCreateWorkflowRequest struct {
		enable bool
		req    *activityPb.CreateWorkflowRequest
		res    *activityPb.CreateWorkflowResponse
		err    error
	}
	type mockUpdateCaseStatus struct {
		enable bool
		req    *cmActivityPb.UpdateCaseRequest
		res    *cmActivityPb.UpdateCaseResponse
		err    error
	}
	tests := []struct {
		name                              string
		req                               *workflowPb.Request
		args                              args
		mockGetWorkflowProcessingParamsV2 mockGetWorkflowProcessingParamsV2
		mockLogActionInDB                 mockLogActionInDB
		mockFullFreeze                    mockFullFreeze
		mockCreditFreeze                  mockCreditFreeze
		mockDebitFreeze                   mockDebitFreeze
		mockPassUser                      mockPassUser
		mockMoveToReview                  mockMoveToReview
		mockRequestUserInfo               mockRequestUserInfo
		mockSnooze                        mockSnooze
		mockProcessAFUReviewAction        mockProcessAFUReviewAction
		mockRiskUnFreeze                  mockRiskPerformActionUnFreeze
		mockProcessOnboardingReview       processOnboardingReview
		mockRetryLiveness                 mockRetryLiveness
		mockCreateWorkflowRequest         mockCreateWorkflowRequest
		mockUpdateWorkflowStage           []mockUpdateWorkflowStage
		mockInitiateWorkflowStageV2       []mockInitiateWorkflowStageV2
		mockPublishWorkflowUpdateEventV2  []mockPublishWorkflowUpdateEventV2
		mockUpdateCaseStatus              []mockUpdateCaseStatus
		wantErr                           bool
	}{
		{
			name: "success addition for total freeze",
			req:  &workflowPb.Request{},
			mockLogActionInDB: mockLogActionInDB{
				enable: true,
				req: &cmActivityPb.LogActionInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					CaseId:       totalF.CaseId,
					ReviewType:   totalF.ReviewType,
					ActionType:   totalF.ActionType,
					Parameters:   totalF.Parameters,
					Source:       totalF.Source,
					AnalystEmail: totalF.AnalystEmail,
					InitiatedAt:  totalF.InitiatedAt,
					ActorId:      testActorId1,
				},
				res: &cmActivityPb.LogActionInDBResponse{},
				err: nil,
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: a1Payload,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
			},
			mockPublishWorkflowUpdateEventV2: []mockPublishWorkflowUpdateEventV2{
				{enable: true,
					req: &activityPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					}},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &activityPb.CreateWorkflowRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     testActorId1,
						Version:     workflowPb.Version_V0,
						Type:        celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_RISK_FULL_FREEZE),
						Payload:     performActionBytes,
						ClientReqId: clientReqIdObj,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &activityPb.CreateWorkflowResponse{
					Params: &celestialPb.WorkflowCreationResponseParams{
						WorkflowRequestId: newWFId,
					},
				},
			},
			mockFullFreeze: mockFullFreeze{
				enable: true,
				req:    &workflowPb.Request{Payload: childPayload},
				cwo: workflow.ChildWorkflowOptions{
					WorkflowID: newWFId,
				},
				res: &cmWorkflowPb.FullFreezeResponse{
					ResponseHeader: &workflowPb.ResponseHeader{
						Status: workflowPb.StatusOk(),
					},
				},
			},
			mockUpdateCaseStatus: []mockUpdateCaseStatus{
				{
					enable: true,
					req: &cmActivityPb.UpdateCaseRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						Case: &caseReviewPb.Case{
							Id:      totalF.CaseId,
							Status:  caseReviewPb.Status_STATUS_RESOLVED,
							Verdict: cmEnumsPb.Verdict_VERDICT_FAIL,
						},
						FieldMasks: []caseReviewPb.CaseFieldMask{
							caseReviewPb.CaseFieldMask_CASE_FIELD_STATUS,
							caseReviewPb.CaseFieldMask_CASE_FIELD_MASK_VERDICT,
						},
					},
					res: &cmActivityPb.UpdateCaseResponse{
						ResponseHeader: nil,
					},
					err: nil,
				},
			},
		},
		{
			name: "full freeze returned error in trigger individual action",
			req:  &workflowPb.Request{},
			mockLogActionInDB: mockLogActionInDB{
				enable: true,
				req: &cmActivityPb.LogActionInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					CaseId:       totalF.CaseId,
					ReviewType:   totalF.ReviewType,
					ActionType:   totalF.ActionType,
					Parameters:   totalF.Parameters,
					Source:       totalF.Source,
					AnalystEmail: totalF.AnalystEmail,
					InitiatedAt:  totalF.InitiatedAt,
					ActorId:      testActorId1,
				},
				res: &cmActivityPb.LogActionInDBResponse{},
				err: nil,
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: a1Payload,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
			},
			mockPublishWorkflowUpdateEventV2: []mockPublishWorkflowUpdateEventV2{
				{enable: true,
					req: &activityPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					}},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &activityPb.CreateWorkflowRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     testActorId1,
						Version:     workflowPb.Version_V0,
						Type:        celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_RISK_FULL_FREEZE),
						Payload:     performActionBytes,
						ClientReqId: clientReqIdObj,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &activityPb.CreateWorkflowResponse{
					Params: &celestialPb.WorkflowCreationResponseParams{
						WorkflowRequestId: newWFId,
					},
				},
			},
			mockFullFreeze: mockFullFreeze{
				enable: true,
				req:    &workflowPb.Request{Payload: childPayload},
				cwo: workflow.ChildWorkflowOptions{
					WorkflowID: newWFId,
				},
				res: &cmWorkflowPb.FullFreezeResponse{
					ResponseHeader: &workflowPb.ResponseHeader{
						Status: workflowPb.StatusOk(),
					},
				},
				err: errors.New("activity failed"),
			},
			mockUpdateCaseStatus: []mockUpdateCaseStatus{
				{
					enable: true,
					req: &cmActivityPb.UpdateCaseRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						Case: &caseReviewPb.Case{
							Id:     totalF.CaseId,
							Status: caseReviewPb.Status_STATUS_MANUAL_INTERVENTION,
						},
						FieldMasks: []caseReviewPb.CaseFieldMask{
							caseReviewPb.CaseFieldMask_CASE_FIELD_STATUS,
						},
					},
					res: &cmActivityPb.UpdateCaseResponse{
						ResponseHeader: nil,
					},
					err: nil,
				},
			},
			wantErr: true,
		},
		{
			name: "success addition for credit freeze",
			req:  &workflowPb.Request{},
			mockLogActionInDB: mockLogActionInDB{
				enable: true,
				req: &cmActivityPb.LogActionInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					CaseId:       creditF.CaseId,
					ReviewType:   creditF.ReviewType,
					ActionType:   creditF.ActionType,
					Parameters:   creditF.Parameters,
					Source:       creditF.Source,
					AnalystEmail: creditF.AnalystEmail,
					InitiatedAt:  creditF.InitiatedAt,
					ActorId:      testActorId1,
				},
				res: &cmActivityPb.LogActionInDBResponse{},
				err: nil,
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: creditFPayload,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
			},
			mockPublishWorkflowUpdateEventV2: []mockPublishWorkflowUpdateEventV2{
				{enable: true,
					req: &activityPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					}},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &activityPb.CreateWorkflowRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     testActorId1,
						Version:     workflowPb.Version_V0,
						Type:        celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_RISK_CREDIT_FREEZE),
						Payload:     performActionBytes,
						ClientReqId: clientReqIdObj,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &activityPb.CreateWorkflowResponse{
					Params: &celestialPb.WorkflowCreationResponseParams{
						WorkflowRequestId: newWFId,
					},
				},
			},
			mockCreditFreeze: mockCreditFreeze{
				enable: true,
				req:    &workflowPb.Request{Payload: cfActionChildPayload},
				cwo: workflow.ChildWorkflowOptions{
					WorkflowID: newWFId,
				},
				res: &cmWorkflowPb.CreditFreezeResponse{
					ResponseHeader: &workflowPb.ResponseHeader{
						Status: workflowPb.StatusOk(),
					},
				},
			},
			mockUpdateCaseStatus: []mockUpdateCaseStatus{
				{
					enable: true,
					req: &cmActivityPb.UpdateCaseRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						Case: &caseReviewPb.Case{
							Id:      totalF.CaseId,
							Status:  caseReviewPb.Status_STATUS_RESOLVED,
							Verdict: cmEnumsPb.Verdict_VERDICT_FAIL,
						},
						FieldMasks: []caseReviewPb.CaseFieldMask{
							caseReviewPb.CaseFieldMask_CASE_FIELD_STATUS,
							caseReviewPb.CaseFieldMask_CASE_FIELD_MASK_VERDICT,
						},
					},
					res: &cmActivityPb.UpdateCaseResponse{
						ResponseHeader: nil,
					},
					err: nil,
				},
			},
		},
		{
			name: "success addition move to review",
			req:  &workflowPb.Request{},
			mockLogActionInDB: mockLogActionInDB{
				enable: true,
				req: &cmActivityPb.LogActionInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					CaseId:       passUser.CaseId,
					ReviewType:   passUser.ReviewType,
					ActionType:   caseReviewPb.ActionType_ACTION_TYPE_MOVE_TO_REVIEW,
					Source:       passUser.Source,
					AnalystEmail: passUser.AnalystEmail,
					InitiatedAt:  passUser.InitiatedAt,
					ActorId:      testActorId1,
				},
				res: &cmActivityPb.LogActionInDBResponse{},
				err: nil,
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: moveToReview,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
			},
			mockPublishWorkflowUpdateEventV2: []mockPublishWorkflowUpdateEventV2{
				{enable: true,
					req: &activityPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					}},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &activityPb.CreateWorkflowRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     testActorId1,
						Version:     workflowPb.Version_V0,
						Type:        celestialPkg.GetTypeEnumFromWorkflowType(riskNs.MoveToReview),
						Payload:     moveToReviewBytes,
						ClientReqId: clientReqIdObj,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &activityPb.CreateWorkflowResponse{
					Params: &celestialPb.WorkflowCreationResponseParams{
						WorkflowRequestId: newWFId,
					},
				},
			},
			mockMoveToReview: mockMoveToReview{
				enable: true,
				req: &cmWorkflowPb.MoveToReviewRequest{
					CaseId:       passUser.CaseId,
					AnalystEmail: passUser.GetAnalystEmail(),
					ActorId:      testActorId1,
				},
				res: &cmWorkflowPb.MoveToReviewResponse{
					ResponseHeader: &workflowPb.ResponseHeader{
						Status: workflowPb.StatusOk(),
					},
				},
			},
		},
		{
			name: "success snooze",
			req:  &workflowPb.Request{},
			mockLogActionInDB: mockLogActionInDB{
				enable: true,
				req: &cmActivityPb.LogActionInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					CaseId:       passUser.CaseId,
					ReviewType:   passUser.ReviewType,
					ActionType:   caseReviewPb.ActionType_ACTION_TYPE_SNOOZE,
					Source:       passUser.Source,
					AnalystEmail: passUser.AnalystEmail,
					InitiatedAt:  passUser.InitiatedAt,
					Parameters:   &caseReviewPb.ActionParameters{Parameter: &caseReviewPb.ActionParameters_SnoozeParameters{SnoozeParameters: &caseReviewPb.SnoozeParameters{SnoozeTill: snoozeTill}}},
					ActorId:      testActorId1,
				},
				res: &cmActivityPb.LogActionInDBResponse{},
				err: nil,
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: snooze,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
			},
			mockPublishWorkflowUpdateEventV2: []mockPublishWorkflowUpdateEventV2{
				{enable: true,
					req: &activityPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					}},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &activityPb.CreateWorkflowRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     testActorId1,
						Version:     workflowPb.Version_V0,
						Type:        celestialPkg.GetTypeEnumFromWorkflowType(riskNs.Snooze),
						Payload:     snoozeBytes,
						ClientReqId: clientReqIdObj,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &activityPb.CreateWorkflowResponse{
					Params: &celestialPb.WorkflowCreationResponseParams{
						WorkflowRequestId: newWFId,
					},
				},
			},
			mockSnooze: mockSnooze{
				enable: true,
				req: &cmWorkflowPb.SnoozeRequest{
					CaseId:           passUser.CaseId,
					AnalystEmail:     passUser.GetAnalystEmail(),
					ActorId:          testActorId1,
					SnoozeTill:       snoozeTill,
					SnoozeParameters: &caseReviewPb.SnoozeParameters{SnoozeTill: snoozeTill},
				},
				res: &cmWorkflowPb.SnoozeResponse{
					ResponseHeader: &workflowPb.ResponseHeader{
						Status: workflowPb.StatusOk(),
					},
				},
			},
		},
		{
			name: "success - fail AFU review action",
			req:  &workflowPb.Request{},
			mockLogActionInDB: mockLogActionInDB{
				enable: true,
				req: &cmActivityPb.LogActionInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					CaseId:       passUser.CaseId,
					ReviewType:   passUser.ReviewType,
					ActionType:   caseReviewPb.ActionType_ACTION_TYPE_FAIL_AFU,
					Source:       passUser.Source,
					AnalystEmail: passUser.AnalystEmail,
					InitiatedAt:  passUser.InitiatedAt,
					ActorId:      testActorId1,
				},
				res: &cmActivityPb.LogActionInDBResponse{},
				err: nil,
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: processAFUReviewVerdict,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
			},
			mockPublishWorkflowUpdateEventV2: []mockPublishWorkflowUpdateEventV2{
				{enable: true,
					req: &activityPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					}},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &activityPb.CreateWorkflowRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     testActorId1,
						Version:     workflowPb.Version_V0,
						Type:        celestialPkg.GetTypeEnumFromWorkflowType(riskNs.ProcessAfuReviewVerdict),
						Payload:     processOnboardingReviewBytes,
						ClientReqId: clientReqIdObj,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &activityPb.CreateWorkflowResponse{
					Params: &celestialPb.WorkflowCreationResponseParams{
						WorkflowRequestId: newWFId,
					},
				},
			},
			mockProcessAFUReviewAction: mockProcessAFUReviewAction{
				enable: true,
				req: &cmWorkflowPb.ProcessAFUReviewVerdictRequest{
					CaseId:       passUser.CaseId,
					AnalystEmail: passUser.GetAnalystEmail(),
					ActorId:      testActorId1,
					Verdict:      ProcessAFUReviewVerdictRequest.Verdict,
				},
				res: &cmWorkflowPb.ProcessAFUReviewVerdictResponse{
					ResponseHeader: &workflowPb.ResponseHeader{
						Status: workflowPb.StatusOk(),
					},
				},
			},
			mockUpdateCaseStatus: []mockUpdateCaseStatus{
				{
					enable: true,
					req: &cmActivityPb.UpdateCaseRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						Case: &caseReviewPb.Case{
							Id:      passUser.CaseId,
							Status:  caseReviewPb.Status_STATUS_RESOLVED,
							Verdict: cmEnumsPb.Verdict_VERDICT_FAIL,
						},
						FieldMasks: []caseReviewPb.CaseFieldMask{
							caseReviewPb.CaseFieldMask_CASE_FIELD_STATUS,
							caseReviewPb.CaseFieldMask_CASE_FIELD_MASK_VERDICT,
						},
					},
					res: &cmActivityPb.UpdateCaseResponse{
						ResponseHeader: nil,
					},
					err: nil,
				},
			},
		},
		{
			name: "success - debit freeze",
			req:  &workflowPb.Request{},
			mockLogActionInDB: mockLogActionInDB{
				enable: true,
				req: &cmActivityPb.LogActionInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					CaseId:     dfUser.CaseId,
					ReviewType: dfUser.ReviewType,
					ActionType: caseReviewPb.ActionType_ACTION_TYPE_FREEZE_ACCOUNT,
					Parameters: &caseReviewPb.ActionParameters{
						Parameter: &caseReviewPb.ActionParameters_AccountFreezeParameters{
							AccountFreezeParameters: &caseReviewPb.AccountFreezeParameters{
								FreezeLevel: caseReviewPb.FreezeLevel_FREEZE_LEVEL_DEBIT,
								RequestReason: &caseReviewPb.RequestReason{
									Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
									Remarks: "Manually resolved",
								},
							},
						},
					},
					Source:       dfUser.Source,
					AnalystEmail: dfUser.AnalystEmail,
					InitiatedAt:  dfUser.InitiatedAt,
					ActorId:      testActorId1,
				},
				res: &cmActivityPb.LogActionInDBResponse{},
				err: nil,
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: performReviewActionDfREq,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
			},
			mockDebitFreeze: mockDebitFreeze{
				enable: true,
				req:    dfReqProcessReview,
				cwo: workflow.ChildWorkflowOptions{
					WorkflowID: newWFId,
				},
				res: &cmWorkflowPb.DebitFreezeResponse{
					ResponseHeader: &workflowPb.ResponseHeader{
						Status: workflowPb.StatusOk(),
					},
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
			},
			mockPublishWorkflowUpdateEventV2: []mockPublishWorkflowUpdateEventV2{
				{enable: true,
					req: &activityPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					}},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &activityPb.CreateWorkflowRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     testActorId1,
						Version:     workflowPb.Version_V0,
						Type:        celestialPkg.GetTypeEnumFromWorkflowType(riskNs.RiskDebitFreeze),
						ClientReqId: clientReqIdObj,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &activityPb.CreateWorkflowResponse{
					Params: &celestialPb.WorkflowCreationResponseParams{
						WorkflowRequestId: newWFId,
					},
				},
			},
			mockUpdateCaseStatus: []mockUpdateCaseStatus{
				{
					enable: true,
					req: &cmActivityPb.UpdateCaseRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						Case: &caseReviewPb.Case{
							Id:      dfUser.CaseId,
							Status:  caseReviewPb.Status_STATUS_RESOLVED,
							Verdict: cmEnumsPb.Verdict_VERDICT_FAIL,
						},
						FieldMasks: []caseReviewPb.CaseFieldMask{
							caseReviewPb.CaseFieldMask_CASE_FIELD_STATUS,
							caseReviewPb.CaseFieldMask_CASE_FIELD_MASK_VERDICT,
						},
					},
					res: &cmActivityPb.UpdateCaseResponse{
						ResponseHeader: nil,
					},
					err: nil,
				},
			},
		},
		{
			name: "success: pass user",
			req:  &workflowPb.Request{},
			mockLogActionInDB: mockLogActionInDB{
				enable: true,
				req: &cmActivityPb.LogActionInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					CaseId:       passUser.CaseId,
					ReviewType:   passUser.ReviewType,
					ActionType:   passUser.ActionType,
					Source:       passUser.Source,
					AnalystEmail: passUser.AnalystEmail,
					InitiatedAt:  passUser.InitiatedAt,
					ActorId:      testActorId1,
				},
				res: &cmActivityPb.LogActionInDBResponse{},
				err: nil,
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: passUserPayload,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
			},
			mockPublishWorkflowUpdateEventV2: []mockPublishWorkflowUpdateEventV2{
				{
					enable: true,
					req: &activityPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					}},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &activityPb.CreateWorkflowRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     testActorId1,
						Version:     workflowPb.Version_V0,
						Payload:     performPassUserBytes,
						Type:        celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_RISK_PASS_USER),
						ClientReqId: clientReqIdObj,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &activityPb.CreateWorkflowResponse{
					Params: &celestialPb.WorkflowCreationResponseParams{
						WorkflowRequestId: newWFId,
					},
				},
			},
			mockPassUser: mockPassUser{
				enable: true,
				req: &workflowPb.Request{
					Payload: performPassUserChildPayload,
				},
				res: &workflowPb.Response{},
			},
			mockUpdateCaseStatus: []mockUpdateCaseStatus{
				{
					enable: true,
					req: &cmActivityPb.UpdateCaseRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						Case: &caseReviewPb.Case{
							Id:      passUser.CaseId,
							Status:  caseReviewPb.Status_STATUS_RESOLVED,
							Verdict: cmEnumsPb.Verdict_VERDICT_PASS,
						},
						FieldMasks: []caseReviewPb.CaseFieldMask{
							caseReviewPb.CaseFieldMask_CASE_FIELD_STATUS,
							caseReviewPb.CaseFieldMask_CASE_FIELD_MASK_VERDICT,
						},
					},
					res: &cmActivityPb.UpdateCaseResponse{
						ResponseHeader: nil,
					},
					err: nil,
				},
			},
		},
		{
			name: "success: pass user onboarding",
			req:  &workflowPb.Request{},
			mockLogActionInDB: mockLogActionInDB{
				enable: true,
				req: &cmActivityPb.LogActionInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					CaseId:       passUser.CaseId,
					ReviewType:   passUser.ReviewType,
					ActionType:   caseReviewPb.ActionType_ACTION_TYPE_PASS_USER_ONBOARDING,
					Source:       passUser.Source,
					AnalystEmail: passUser.AnalystEmail,
					InitiatedAt:  passUser.InitiatedAt,
					ActorId:      testActorId1,
				},
				res: &cmActivityPb.LogActionInDBResponse{},
				err: nil,
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: passUserOnboardingPayload,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
			},
			mockPublishWorkflowUpdateEventV2: []mockPublishWorkflowUpdateEventV2{
				{
					enable: true,
					req: &activityPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					}},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &activityPb.CreateWorkflowRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     testActorId1,
						Version:     workflowPb.Version_V0,
						Payload:     nil,
						Type:        celestialPkg.GetTypeEnumFromWorkflowType(riskNs.ProcessOnboardingReviewVerdict),
						ClientReqId: clientReqIdObj,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &activityPb.CreateWorkflowResponse{
					Params: &celestialPb.WorkflowCreationResponseParams{
						WorkflowRequestId: newWFId,
					},
				},
			},
			mockProcessOnboardingReview: processOnboardingReview{
				enable: true,
				req: &cmWorkflowPb.ProcessOnboardingReviewVerdictRequest{
					CaseId:  passUser.CaseId,
					ActorId: testActorId1,
					Verdict: cmEnumsPb.Verdict_VERDICT_PASS,
				},
				res: &cmWorkflowPb.ProcessOnboardingReviewVerdictResponse{},
			},
			mockUpdateCaseStatus: []mockUpdateCaseStatus{
				{
					enable: true,
					req: &cmActivityPb.UpdateCaseRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						Case: &caseReviewPb.Case{
							Id:      passUser.CaseId,
							Status:  caseReviewPb.Status_STATUS_RESOLVED,
							Verdict: cmEnumsPb.Verdict_VERDICT_PASS,
						},
						FieldMasks: []caseReviewPb.CaseFieldMask{
							caseReviewPb.CaseFieldMask_CASE_FIELD_STATUS,
							caseReviewPb.CaseFieldMask_CASE_FIELD_MASK_VERDICT,
						},
					},
					res: &cmActivityPb.UpdateCaseResponse{
						ResponseHeader: nil,
					},
					err: nil,
				},
			},
		},
		{
			name: "success: pass user liveness",
			req:  &workflowPb.Request{},
			mockLogActionInDB: mockLogActionInDB{
				enable: true,
				req: &cmActivityPb.LogActionInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					CaseId:       passUser.CaseId,
					ReviewType:   passUser.ReviewType,
					ActionType:   caseReviewPb.ActionType_ACTION_TYPE_ADD_LIVENESS_RETRIES,
					Source:       passUser.Source,
					AnalystEmail: passUser.AnalystEmail,
					InitiatedAt:  passUser.InitiatedAt,
					ActorId:      testActorId1,
				},
				res: &cmActivityPb.LogActionInDBResponse{},
				err: nil,
			},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: processReviewActionForLivenessRetryPayload,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_INITIATED,
					},
					err: nil,
				},
			},
			mockUpdateWorkflowStage: []mockUpdateWorkflowStage{
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_STORE_IN_DB,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
				{
					enable: true,
					req: &activityPb.UpdateWorkflowStageRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   ownership,
						},
						WfReqId: defaultWorkflowID,
						Stage:   workflowPb.Stage_RISK_PROCESS_REVIEW_ACTION_PERFORM_ACTION,
						Status:  stagePb.Status_SUCCESSFUL,
					},
					res: &activityPb.UpdateWorkflowStageResponse{},
					err: nil,
				},
			},
			mockPublishWorkflowUpdateEventV2: []mockPublishWorkflowUpdateEventV2{
				{
					enable: true,
					req: &activityPb.PublishWorkflowUpdateEventV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					}},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &activityPb.CreateWorkflowRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     testActorId1,
						Version:     workflowPb.Version_V0,
						Payload:     nil,
						Type:        celestialPkg.GetTypeEnumFromWorkflowType(riskNs.AddLivenessRetry),
						ClientReqId: clientReqIdObj,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &activityPb.CreateWorkflowResponse{
					Params: &celestialPb.WorkflowCreationResponseParams{
						WorkflowRequestId: newWFId,
					},
				},
			},
			mockRetryLiveness: mockRetryLiveness{
				enable: true,
				req: &cmWorkflowPb.AddLivenessRetriesRequest{
					ActorId: testActorId1,
				},
				res: &cmWorkflowPb.AddLivenessRetriesResponse{},
			},
			mockUpdateCaseStatus: []mockUpdateCaseStatus{
				{
					enable: true,
					req: &cmActivityPb.UpdateCaseRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: clientReqId,
							Ownership:   commontypes.Ownership_EPIFI_TECH,
						},
						Case: &caseReviewPb.Case{
							Id:      passUser.CaseId,
							Status:  caseReviewPb.Status_STATUS_RESOLVED,
							Verdict: cmEnumsPb.Verdict_VERDICT_FAIL,
						},
						FieldMasks: []caseReviewPb.CaseFieldMask{
							caseReviewPb.CaseFieldMask_CASE_FIELD_STATUS,
							caseReviewPb.CaseFieldMask_CASE_FIELD_MASK_VERDICT,
						},
					},
					res: &cmActivityPb.UpdateCaseResponse{
						ResponseHeader: nil,
					},
					err: nil,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(cmWorkflow.RiskFullFreeze)
			env.RegisterWorkflow(cmWorkflow.RiskCreditFreeze)
			env.RegisterWorkflow(cmWorkflow.RiskDebitFreeze)
			env.RegisterWorkflow(cmWorkflow.RiskUnfreeze)
			env.RegisterWorkflow(cmWorkflow.RiskPassUser)
			env.RegisterWorkflow(cmWorkflow.MoveToReview)
			env.RegisterWorkflow(cmWorkflow.RequestUserInfo)
			env.RegisterWorkflow(cmWorkflow.ProcessOnboardingReviewVerdict)
			env.RegisterWorkflow(cmWorkflow.AddLivenessRetry)
			env.RegisterWorkflow(cmWorkflow.Snooze)
			env.RegisterWorkflow(cmWorkflow.ProcessAfuReviewVerdict)
			env.RegisterActivity(&celestialActivity.Processor{})
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&cmActivity.Processor{})

			if tt.mockGetWorkflowProcessingParamsV2.enable {
				env.OnActivity(
					string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, tt.mockGetWorkflowProcessingParamsV2.req,
				).Return(tt.mockGetWorkflowProcessingParamsV2.res, tt.mockGetWorkflowProcessingParamsV2.err)
			}

			for _, mockInitiateWfStage := range tt.mockInitiateWorkflowStageV2 {
				if mockInitiateWfStage.enable {
					env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, mockInitiateWfStage.req).
						Return(mockInitiateWfStage.err)
				}
			}

			if tt.mockLogActionInDB.enable {
				env.OnActivity(string(riskNs.LogActionInDB), mock.Anything, tt.mockLogActionInDB.req).
					Return(tt.mockLogActionInDB.res, tt.mockLogActionInDB.err)
			}

			for _, mockUpdateCase := range tt.mockUpdateCaseStatus {
				if mockUpdateCase.enable {
					env.OnActivity(string(riskNs.UpdateCase), mock.Anything, mockUpdateCase.req).
						Return(mockUpdateCase.res, mockUpdateCase.err)
				}
			}

			for _, mockUpdateWfStageStatus := range tt.mockUpdateWorkflowStage {
				if mockUpdateWfStageStatus.enable {
					env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, mockUpdateWfStageStatus.req).
						Return(mockUpdateWfStageStatus.err)
				}
			}

			for _, mockPublishWfUpdateEvent := range tt.mockPublishWorkflowUpdateEventV2 {
				if mockPublishWfUpdateEvent.enable {
					env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, mockPublishWfUpdateEvent.req).
						Return(mockPublishWfUpdateEvent.err)
				}
			}

			// todo (navneet) : OnActivity arg-matcher alternatives and pass request for CreateWorkflowRequest
			if tt.mockCreateWorkflowRequest.enable {
				env.OnActivity(string(epifitemporal.CreateWorkflowRequest), mock.Anything, mock.Anything).
					Return(tt.mockCreateWorkflowRequest.res, tt.mockCreateWorkflowRequest.err)
			}

			if tt.mockFullFreeze.enable {
				env.OnWorkflow(cmWorkflow.RiskFullFreeze, mock.Anything, tt.mockFullFreeze.req).Return(tt.mockFullFreeze.res, tt.mockFullFreeze.err)
			}

			if tt.mockRiskUnFreeze.enable {
				env.OnWorkflow(cmWorkflow.RiskUnfreeze, mock.Anything, tt.mockRiskUnFreeze.req).Return(tt.mockRiskUnFreeze.res, tt.mockRiskUnFreeze.err)
			}

			if tt.mockCreditFreeze.enable {
				env.OnWorkflow(cmWorkflow.RiskCreditFreeze, mock.Anything, tt.mockCreditFreeze.req).Return(tt.mockCreditFreeze.res, tt.mockCreditFreeze.err)
			}
			if tt.mockDebitFreeze.enable {
				env.OnWorkflow(cmWorkflow.RiskDebitFreeze, mock.Anything, tt.mockDebitFreeze.req).Return(tt.mockDebitFreeze.res, tt.mockDebitFreeze.err)
			}
			if tt.mockPassUser.enable {
				env.OnWorkflow(cmWorkflow.RiskPassUser, mock.Anything, tt.mockPassUser.req).Return(tt.mockPassUser.res, tt.mockPassUser.err)
			}
			if tt.mockMoveToReview.enable {
				env.OnWorkflow(cmWorkflow.MoveToReview, mock.Anything, tt.mockMoveToReview.req).Return(tt.mockMoveToReview.res, tt.mockMoveToReview.err)
			}
			if tt.mockRequestUserInfo.enable {
				env.OnWorkflow(cmWorkflow.RequestUserInfo, mock.Anything, tt.mockRequestUserInfo.req).Return(tt.mockRequestUserInfo.res, tt.mockRequestUserInfo.err)
			}
			if tt.mockProcessOnboardingReview.enable {
				env.OnWorkflow(cmWorkflow.ProcessOnboardingReviewVerdict, mock.Anything, tt.mockProcessOnboardingReview.req).Return(tt.mockProcessOnboardingReview.res, tt.mockProcessOnboardingReview.err)
			}
			if tt.mockRetryLiveness.enable {
				env.OnWorkflow(cmWorkflow.AddLivenessRetry, mock.Anything, tt.mockRetryLiveness.req).Return(tt.mockRetryLiveness.res, tt.mockRetryLiveness.err)
			}
			if tt.mockSnooze.enable {
				env.OnWorkflow(cmWorkflow.Snooze, mock.Anything, tt.mockSnooze.req).Return(tt.mockSnooze.res, tt.mockSnooze.err)
			}
			if tt.mockProcessAFUReviewAction.enable {
				env.OnWorkflow(cmWorkflow.ProcessAfuReviewVerdict, mock.Anything, tt.mockProcessAFUReviewAction.req).Return(tt.mockProcessAFUReviewAction.res, tt.mockProcessAFUReviewAction.err)
			}

			env.ExecuteWorkflow(cmWorkflow.RiskProcessReviewAction, tt.args.req)

			assert.True(t, env.IsWorkflowCompleted())

			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("RiskProcessReviewAction() error = %s, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			env.AssertExpectations(t)
		})
	}
}
