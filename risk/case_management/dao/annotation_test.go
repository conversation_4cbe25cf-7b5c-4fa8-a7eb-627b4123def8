package dao

import (
	"context"
	"errors"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"google.golang.org/protobuf/testing/protocmp"
	"gorm.io/gorm"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/api/risk/case_management/review"
	"github.com/epifi/be-common/pkg/epifierrors"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/risk/config"
)

type AnnotationDaoTestSuite struct {
	db            *gorm.DB
	annotationDao AnnotationDao
	conf          *config.Config
}

var (
	annotationTestTables = []string{"annotations"}
	annodts              AnnotationDaoTestSuite

	// create dao vars
	annotationIdCreate           = uuid.NewString()
	annotationEntityIdCreate     = "someEntityId"
	annotationEntityIdCreate2    = "someEntityId2"
	allowedAnnotationIdCreate    = "someAllowedAnnotationId"
	addedByEmailCreate           = "addedByEmail"
	annotationsCaseId            = "caseId"
	annotationWithNullEntityType = &review.Annotation{
		Id: annotationIdCreate,
	}
	annotationWithNullEntityId = &review.Annotation{
		Id:         annotationIdCreate,
		EntityType: review.ReviewEntityType_REVIEW_ENTITY_TYPE_RULE,
	}
	annotationWithNullAllowedAnnotationId = &review.Annotation{
		Id:         annotationIdCreate,
		EntityType: review.ReviewEntityType_REVIEW_ENTITY_TYPE_RULE,
		EntityId:   annotationEntityIdCreate,
	}
	annotationWithNullAddedByEmail = &review.Annotation{
		Id:                  annotationIdCreate,
		EntityType:          review.ReviewEntityType_REVIEW_ENTITY_TYPE_RULE,
		EntityId:            annotationEntityIdCreate,
		AllowedAnnotationId: annotationEntityIdCreate,
	}
	annotationWithSuccess = &review.Annotation{
		Id:                  uuid.NewString(),
		EntityType:          review.ReviewEntityType_REVIEW_ENTITY_TYPE_RULE,
		EntityId:            annotationEntityIdCreate,
		AllowedAnnotationId: annotationEntityIdCreate,
		AddedByEmail:        addedByEmailCreate,
		CaseId:              annotationsCaseId,
	}
	annotationWithCaseIdSuccess1 = &review.Annotation{
		Id:                  uuid.NewString(),
		EntityType:          review.ReviewEntityType_REVIEW_ENTITY_TYPE_RULE,
		EntityId:            annotationEntityIdCreate,
		AllowedAnnotationId: annotationEntityIdCreate,
		AddedByEmail:        addedByEmailCreate,
		CaseId:              "caseId1",
	}
	annotationWithCaseIdSuccess2 = &review.Annotation{
		Id:                  uuid.NewString(),
		EntityType:          review.ReviewEntityType_REVIEW_ENTITY_TYPE_LIVENESS,
		EntityId:            annotationEntityIdCreate,
		AllowedAnnotationId: annotationEntityIdCreate,
		AddedByEmail:        addedByEmailCreate,
		CaseId:              "caseId1",
	}

	// Get by entity dao
	GetByEntityId                  = uuid.NewString()
	GetByEntityId1                 = uuid.NewString()
	GetByEntityId2                 = uuid.NewString()
	GetByEntityAllowedAnnotationId = uuid.NewString()
	GetByEntityIdAnnotationSuccess = &review.Annotation{
		Id:                  GetByEntityId,
		EntityType:          review.ReviewEntityType_REVIEW_ENTITY_TYPE_RULE,
		EntityId:            GetByEntityId,
		AllowedAnnotationId: annotationEntityIdCreate,
		AddedByEmail:        addedByEmailCreate,
	}
	GetByEntityIdAnnotationSuccess2 = &review.Annotation{
		Id:                  GetByEntityId2,
		EntityType:          review.ReviewEntityType_REVIEW_ENTITY_TYPE_RULE,
		EntityId:            GetByEntityId,
		AllowedAnnotationId: annotationEntityIdCreate2,
		AddedByEmail:        addedByEmailCreate,
	}
	GetByEntityIdAnnotationSuccess1 = &review.Annotation{
		Id:                  GetByEntityId1,
		EntityType:          review.ReviewEntityType_REVIEW_ENTITY_TYPE_CASE,
		EntityId:            GetByEntityId1,
		AllowedAnnotationId: GetByEntityAllowedAnnotationId,
		AddedByEmail:        addedByEmailCreate,
	}
)

func TestAnnotationDaoCRDB_Create(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, annodts.conf.FRMDb.GetName(), annodts.db, annotationTestTables)
	ctx := context.Background()
	tests := []struct {
		name       string
		annotation *review.Annotation
		want       *review.Annotation
		wantErr    bool
	}{
		{
			name:       "fails when entity type is unspecified",
			annotation: annotationWithNullEntityType,
			want:       nil,
			wantErr:    true,
		},
		{
			name:       "fails when entity id is nil",
			annotation: annotationWithNullEntityId,
			want:       nil,
			wantErr:    true,
		},
		{
			name:       "fails when allowed annotation id is nil",
			annotation: annotationWithNullAllowedAnnotationId,
			want:       nil,
			wantErr:    true,
		},
		{
			name:       "fails when added by email is nil",
			annotation: annotationWithNullAddedByEmail,
			want:       nil,
			wantErr:    true,
		},
		{
			name:       "success",
			annotation: annotationWithSuccess,
			want:       annotationWithSuccess,
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AnnotationDaoCRDB{
				db: annodts.db,
			}
			got, err := a.Create(ctx, tt.annotation)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&review.Annotation{}, "id", "created_at", "updated_at", "deleted_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("CREATE() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}

func TestAnnotationDaoCRDB_BulkCreate(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, annodts.conf.FRMDb.GetName(), annodts.db, annotationTestTables)
	ctx := context.Background()
	tests := []struct {
		name        string
		annotations []*review.Annotation
		want        []*review.Annotation
		wantErr     bool
	}{
		{
			name:        "fails when entity type is unspecified",
			annotations: []*review.Annotation{annotationWithNullEntityType},
			want:        nil,
			wantErr:     true,
		},
		{
			name:        "fails when entity id is nil",
			annotations: []*review.Annotation{annotationWithNullEntityId},
			want:        nil,
			wantErr:     true,
		},
		{
			name:        "fails when allowed annotation id is nil",
			annotations: []*review.Annotation{annotationWithNullAllowedAnnotationId},
			want:        nil,
			wantErr:     true,
		},
		{
			name:        "fails when added by email is nil",
			annotations: []*review.Annotation{annotationWithNullAddedByEmail},
			want:        nil,
			wantErr:     true,
		},
		{
			name:        "success",
			annotations: []*review.Annotation{annotationWithSuccess, annotationWithCaseIdSuccess1},
			want:        []*review.Annotation{annotationWithSuccess, annotationWithCaseIdSuccess1},
			wantErr:     false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AnnotationDaoCRDB{
				db: annodts.db,
			}
			got, err := a.BulkCreate(ctx, tt.annotations)
			if (err != nil) != tt.wantErr {
				t.Errorf("BulkCreate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&review.Annotation{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("BulkCreate() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}

func TestAnnotationDaoCRDB_GetByQuery(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, annodts.conf.FRMDb.GetName(), annodts.db, annotationTestTables)
	ctx := context.Background()
	annodts.annotationDao.Create(ctx, GetByEntityIdAnnotationSuccess)
	annodts.annotationDao.Create(ctx, GetByEntityIdAnnotationSuccess1)
	annodts.annotationDao.Create(ctx, GetByEntityIdAnnotationSuccess2)
	tests := []struct {
		name    string
		query   *caseManagementPb.AnnotationQuery
		want    []*review.Annotation
		wantErr bool
	}{
		{
			name:    "fails when invalid entity type is present",
			query:   &caseManagementPb.AnnotationQuery{},
			want:    nil,
			wantErr: true,
		},
		{
			name: "fails when invalid entity id is present",
			query: &caseManagementPb.AnnotationQuery{
				EntityType: review.ReviewEntityType_REVIEW_ENTITY_TYPE_RULE,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "fails when no record found for the entity",
			query: &caseManagementPb.AnnotationQuery{
				EntityType: review.ReviewEntityType_REVIEW_ENTITY_TYPE_RULE,
				EntityId:   uuid.NewString(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success with added filter of allowed annotation id",
			query: &caseManagementPb.AnnotationQuery{
				EntityType:          review.ReviewEntityType_REVIEW_ENTITY_TYPE_CASE,
				EntityId:            GetByEntityId1,
				AllowedAnnotationId: GetByEntityAllowedAnnotationId,
			},
			want:    []*review.Annotation{GetByEntityIdAnnotationSuccess1},
			wantErr: false,
		},
		{
			name: "success",
			query: &caseManagementPb.AnnotationQuery{
				EntityType: review.ReviewEntityType_REVIEW_ENTITY_TYPE_RULE,
				EntityId:   GetByEntityId,
			},
			want:    []*review.Annotation{GetByEntityIdAnnotationSuccess2, GetByEntityIdAnnotationSuccess},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AnnotationDaoCRDB{
				db: annodts.db,
			}
			got, err := a.GetByQuery(ctx, tt.query)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByQuery() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&review.Annotation{}, "id", "created_at", "updated_at", "deleted_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetByQuery() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}

func TestAnnotationDaoCRDB_GetByCaseId(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, annodts.conf.FRMDb.GetName(), annodts.db, annotationTestTables)
	ctx := context.Background()
	_, _ = annodts.annotationDao.Create(ctx, annotationWithSuccess)
	_, _ = annodts.annotationDao.Create(ctx, annotationWithCaseIdSuccess1)
	_, _ = annodts.annotationDao.Create(ctx, annotationWithCaseIdSuccess2)
	tests := []struct {
		name    string
		caseIds []string
		limit   int
		want    []*review.Annotation
		wantErr bool
		errType error
	}{
		{
			name:    "fails for empty case id list",
			want:    nil,
			wantErr: true,
			errType: epifierrors.ErrInvalidArgument,
		},
		{
			name:    "fails with record not found",
			caseIds: []string{uuid.NewString()},
			want:    nil,
			wantErr: true,
			errType: epifierrors.ErrRecordNotFound,
		},
		{
			name:    "success",
			caseIds: []string{annotationsCaseId, "caseId1"},
			want: []*review.Annotation{annotationWithCaseIdSuccess2,
				annotationWithCaseIdSuccess1, annotationWithSuccess},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := AnnotationDaoCRDB{
				db: annodts.db,
			}
			got, err := a.GetByCaseIds(ctx, tt.caseIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByCaseIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.errType != nil && !errors.Is(err, tt.errType) {
				t.Errorf("GetByCaseIds() error type mismatch got: %v\nwant: %v", err, tt.errType)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&review.Annotation{}, "id", "created_at", "updated_at", "deleted_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetByCaseIds() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}
