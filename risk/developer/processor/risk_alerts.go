package processor

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/api/risk/developer"
	"github.com/epifi/gamma/risk/case_management/dao"
)

const (
	actorIdAlertParamName    = "actor_id"
	riskAlertsCountParamName = "alerts_count"
	maxFetchLimit            = 100
	minFetchLimit            = 0
	defaultAlertsCount       = 10
)

type RiskAlertProcessor struct {
	riskAlertDao dao.AlertDao
}

func NewRiskAlertProcessor(riskAlertDao dao.AlertDao) *RiskAlertProcessor {
	return &RiskAlertProcessor{
		riskAlertDao: riskAlertDao,
	}
}

func (r *RiskAlertProcessor) FetchParamList(ctx context.Context, entity developer.Entity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            actorIdAlertParamName,
			Label:           "Actor Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            riskAlertsCountParamName,
			Label:           "Alerts Count (max 100)",
			Type:            db_state.ParameterDataType_INTEGER,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}

	return paramList, nil
}

func (r *RiskAlertProcessor) FetchData(ctx context.Context, entity developer.Entity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be empty")
	}

	var (
		riskAlerts  []*case_management.Alert
		actorId     string
		alertsCount int
	)

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case actorIdAlertParamName:
			actorId = filter.GetStringValue()
		case riskAlertsCountParamName:
			alertsCount = int(filter.GetIntegerValue())
			switch {
			case alertsCount <= minFetchLimit:
				alertsCount = defaultAlertsCount
			case alertsCount > maxFetchLimit:
				return "", fmt.Errorf("alerts count should be in between %v to %v", minFetchLimit, maxFetchLimit)
			}
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}

	if actorId == "" {
		return "", fmt.Errorf("ActorId cannot be empty")
	}
	riskAlerts, err := r.riskAlertDao.GetByActorId(ctx, actorId, []case_management.AlertFieldMask{case_management.AlertFieldMask_ALERT_FIELD_MASK_ALL}, alertsCount)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return "", fmt.Errorf("alerts not found for actor")
		}
		return "", fmt.Errorf("error while fetching alerts for the given actor id: %s : %w", actorId, err)
	}

	var alerts []string

	for _, ex := range riskAlerts {
		ele, errR := protojson.Marshal(ex)
		if errR != nil {
			return "", fmt.Errorf("cannot marshal alerts request %w", errR)
		}
		alerts = append(alerts, string(ele))
	}
	return "[" + strings.Join(alerts, ", ") + "]", nil
}
