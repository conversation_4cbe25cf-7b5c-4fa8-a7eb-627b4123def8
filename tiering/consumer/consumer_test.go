// nolint: dogsled
package consumer

import (
	"context"
	"flag"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	queuePb "github.com/epifi/be-common/api/queue"
	mockEvent "github.com/epifi/be-common/pkg/events/mocks"

	kycPb "github.com/epifi/gamma/api/kyc"
	savingsCoPb "github.com/epifi/gamma/api/pay/savings_account/consumer"
	salaryProgramPb "github.com/epifi/gamma/api/salaryprogram"
	salaryprogramEventsPb "github.com/epifi/gamma/api/salaryprogram/events"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringCoPb "github.com/epifi/gamma/api/tiering/consumer"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	userpb "github.com/epifi/gamma/api/user"
	mockRelease "github.com/epifi/gamma/pkg/feature/release/mocks"
	"github.com/epifi/gamma/tiering/config"
	"github.com/epifi/gamma/tiering/config/genconf"
	mockDao "github.com/epifi/gamma/tiering/dao/mocks"
	tieringErrors "github.com/epifi/gamma/tiering/errors"
	"github.com/epifi/gamma/tiering/orchestrator"
	"github.com/epifi/gamma/tiering/release"
	"github.com/epifi/gamma/tiering/test"
	"github.com/epifi/gamma/tiering/test/mocks"
	mockTierOptions "github.com/epifi/gamma/tiering/test/mocks/tier_options"
	"github.com/epifi/gamma/tiering/tier_options"
)

var (
	conf    *config.Config
	dynconf *genconf.Config

	FiveTierOptions = func() []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{},
			},
		}
	}

	TenTierOptions = func() []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{},
			},
		}
	}

	OneHundredTierOptions = func(minBalance *moneyPb.Money) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Balance{
								Balance: &criteriaPb.Balance{
									MinBalance: minBalance,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
		}
	}

	OneThousandTierOptions = func(minBalance *moneyPb.Money) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Balance{
								Balance: &criteriaPb.Balance{
									MinBalance: minBalance,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
		}
	}

	TwoThousandTierOptions = func() []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType: salaryProgramPb.SalaryActivationType_FULL_SALARY_ACTIVATION,
								},
							},
						},
					},
				},
			},
		}
	}

	// TODO(sainath): Change this once we get new activation type for AA SALARY
	OneThousandTwoFiftyTierOptions = func() []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType: salaryProgramPb.SalaryActivationType_SALARY_LITE_ACTIVATION,
								},
							},
						},
					},
				},
			},
		}
	}
	OneThousandFiveHundredTierOptions = func() []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType: salaryProgramPb.SalaryActivationType_SALARY_LITE_ACTIVATION,
								},
							},
						},
					},
				},
			},
		}
	}
	criteria1 = &tieringPb.TierCriteria{
		Id:           "57f83510-407f-4a9a-ada3-998a032a54ee",
		CriteriaName: tieringEnumPb.CriteriaName_CRITERIA_NAME_C_TWO,
		Details: &tieringPb.Criterion{
			MovementDetailsList: []*criteriaPb.MovementDetails{
				{
					TierName: tieringEnumPb.Tier_TIER_FIVE,
					Options:  FiveTierOptions(),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_TEN,
					Options:  TenTierOptions(),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					Options:  OneHundredTierOptions(&moneyPb.Money{CurrencyCode: "INR", Units: 10000}),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					Options:  OneThousandTierOptions(&moneyPb.Money{CurrencyCode: "INR", Units: 50000}),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_FIFTY,
					Options:  OneThousandTwoFiftyTierOptions(),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_THOUSAND_FIVE_HUNDRED,
					Options:  OneThousandFiveHundredTierOptions(),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_TWO_THOUSAND,
					Options:  TwoThousandTierOptions(),
				},
			},
		},
		Status: tieringEnumPb.TierCriteriaStatus_TIER_CRITERIA_STATUS_ACTIVE,
	}

	tierOptionsMap = map[tieringEnumPb.Tier][]*criteriaPb.Option{
		tieringEnumPb.Tier_TIER_FIVE: FiveTierOptions(),
		tieringEnumPb.Tier_TIER_TEN:  TenTierOptions(),
		tieringEnumPb.Tier_TIER_ONE_HUNDRED: OneHundredTierOptions(&moneyPb.Money{
			CurrencyCode: "INR",
			Units:        10000,
		}),
		tieringEnumPb.Tier_TIER_ONE_THOUSAND: OneThousandTierOptions(&moneyPb.Money{
			CurrencyCode: "INR",
			Units:        50000,
		}),
		tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_FIFTY:    OneThousandTwoFiftyTierOptions(),
		tieringEnumPb.Tier_TIER_ONE_THOUSAND_FIVE_HUNDRED: OneThousandFiveHundredTierOptions(),
		tieringEnumPb.Tier_TIER_TWO_THOUSAND:              TwoThousandTierOptions(),
	}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, conf, dynconf, _, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockStruct struct {
	orchestrator       *mocks.MockOrchestrator
	releaseManager     *mockRelease.MockIEvaluator
	eventBroker        *mockEvent.MockBroker
	actorTierInfoDao   *mockDao.MockActorTierInfoDao
	timelineManager    *mocks.MockTierTimeline
	tierOptionsManager *mockTierOptions.MockManager
	dataProcessor      *mocks.MockTieringDataProcessor
}

func initMocks(ctl *gomock.Controller) *mockStruct {
	return &mockStruct{
		orchestrator:       mocks.NewMockOrchestrator(ctl),
		releaseManager:     mockRelease.NewMockIEvaluator(ctl),
		eventBroker:        mockEvent.NewMockBroker(ctl),
		actorTierInfoDao:   mockDao.NewMockActorTierInfoDao(ctl),
		timelineManager:    mocks.NewMockTierTimeline(ctl),
		tierOptionsManager: mockTierOptions.NewMockManager(ctl),
		dataProcessor:      mocks.NewMockTieringDataProcessor(ctl),
	}
}

func TestTieringConsumer_ProcessTierReEvaluationEvent(t1 *testing.T) {
	ctrl := gomock.NewController(t1)
	defer ctrl.Finish()
	mockOrchestrator := mocks.NewMockOrchestrator(ctrl)

	type fields struct {
		UnimplementedTieringConsumerServer tieringCoPb.UnimplementedTieringConsumerServer
		conf                               *genconf.Config
		orchestrator                       orchestrator.Orchestrator
		releaseManager                     release.Manager
	}
	type args struct {
		ctx   context.Context
		req   *tieringCoPb.ProcessTierReEvaluationEventRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *tieringCoPb.ProcessTierReEvaluationEventResponse
		wantErr bool
	}{
		{
			name: "#1 actor id not present",
			fields: fields{
				conf: dynconf,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringCoPb.ProcessTierReEvaluationEventRequest{
					ActorEvaluationDetails: &tieringCoPb.ActorEvaluationDetails{},
				},
			},
			want: &tieringCoPb.ProcessTierReEvaluationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
			},
			wantErr: false,
		},
		{
			name: "#2 error orchestrating tier movement",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringCoPb.ProcessTierReEvaluationEventRequest{
					ActorEvaluationDetails: &tieringCoPb.ActorEvaluationDetails{
						ActorId:  "actor-id-1",
						FromTier: tieringEnumPb.Tier_TIER_TEN,
						ToTier:   tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					},
				},
				mocks: []interface{}{
					mockOrchestrator.EXPECT().OrchestrateTierMovement(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringEnumPb.Tier_TIER_UNSPECIFIED, errors.New("error orchestrating movement")),
				},
			},
			want: &tieringCoPb.ProcessTierReEvaluationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus},
			},
			wantErr: false,
		},
		{
			name: "#3 non terminal error - same tier movement",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringCoPb.ProcessTierReEvaluationEventRequest{
					ActorEvaluationDetails: &tieringCoPb.ActorEvaluationDetails{
						ActorId:  "actor-id-1",
						FromTier: tieringEnumPb.Tier_TIER_TEN,
						ToTier:   tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					},
				},
				mocks: []interface{}{
					mockOrchestrator.EXPECT().OrchestrateTierMovement(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringErrors.ErrSameTierMovementNotAllowed),
				},
			},
			want: &tieringCoPb.ProcessTierReEvaluationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			},
			wantErr: false,
		},
		{
			name: "#4 successfully orchestrated movement",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringCoPb.ProcessTierReEvaluationEventRequest{
					ActorEvaluationDetails: &tieringCoPb.ActorEvaluationDetails{
						ActorId:  "actor-id-1",
						FromTier: tieringEnumPb.Tier_TIER_TEN,
						ToTier:   tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					},
				},
				mocks: []interface{}{
					mockOrchestrator.EXPECT().OrchestrateTierMovement(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						tieringEnumPb.Tier_TIER_TEN, tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil),
				},
			},
			want: &tieringCoPb.ProcessTierReEvaluationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			},
			wantErr: false,
		},
		{
			name: "skip current packet",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringCoPb.ProcessTierReEvaluationEventRequest{
					ActorEvaluationDetails: &tieringCoPb.ActorEvaluationDetails{
						ActorId:  "actor-id-1",
						FromTier: tieringEnumPb.Tier_TIER_UNSPECIFIED,
						ToTier:   tieringEnumPb.Tier_TIER_TEN,
					},
				},
				mocks: []interface{}{},
			},
			want: &tieringCoPb.ProcessTierReEvaluationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			},
			wantErr: false,
		},
		{
			name: "dont skip current packet",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringCoPb.ProcessTierReEvaluationEventRequest{
					ActorEvaluationDetails: &tieringCoPb.ActorEvaluationDetails{
						ActorId:  "actor-id-1",
						FromTier: tieringEnumPb.Tier_TIER_ONE_THOUSAND,
						ToTier:   tieringEnumPb.Tier_TIER_TEN,
					},
				},
				mocks: []interface{}{
					mockOrchestrator.EXPECT().OrchestrateTierMovement(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						tieringEnumPb.Tier_TIER_TEN, tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil),
				},
			},
			want: &tieringCoPb.ProcessTierReEvaluationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &TieringConsumer{
				UnimplementedTieringConsumerServer: tt.fields.UnimplementedTieringConsumerServer,
				conf:                               tt.fields.conf,
				orchestrator:                       tt.fields.orchestrator,
			}
			got, err := t.ProcessTierReEvaluationEvent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t1.Errorf("ProcessTierReEvaluationEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ProcessTierReEvaluationEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTieringConsumer_ProcessKycUpdateEvent(t1 *testing.T) {
	ctrl := gomock.NewController(t1)
	defer ctrl.Finish()
	mockOrchestrator := mocks.NewMockOrchestrator(ctrl)

	type fields struct {
		UnimplementedTieringConsumerServer tieringCoPb.UnimplementedTieringConsumerServer
		conf                               *genconf.Config
		orchestrator                       orchestrator.Orchestrator
	}
	type args struct {
		ctx   context.Context
		req   *userpb.KycEvent
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *tieringCoPb.ProcessKycUpdateEventResponse
		wantErr bool
	}{
		{
			name: "#1 actor id not present",
			fields: fields{
				conf: dynconf,
			},
			args: args{
				ctx: context.Background(),
				req: &userpb.KycEvent{},
			},
			want: &tieringCoPb.ProcessKycUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
			},
			wantErr: false,
		},
		{
			name: "#4 error in orchestrator",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &userpb.KycEvent{
					ActorId: "actor-id-1",
				},
				mocks: []interface{}{
					mockOrchestrator.EXPECT().OrchestrateTierMovement(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringEnumPb.Tier_TIER_UNSPECIFIED, errors.New("error in orchestrator"),
					),
				},
			},
			want: &tieringCoPb.ProcessKycUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus},
			},
			wantErr: false,
		},
		{
			name: "#5 error same tier movement not allowed",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &userpb.KycEvent{
					ActorId: "actor-id-1",
				},
				mocks: []interface{}{
					mockOrchestrator.EXPECT().OrchestrateTierMovement(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringErrors.ErrSameTierMovementNotAllowed,
					),
				},
			},
			want: &tieringCoPb.ProcessKycUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			},
			wantErr: false,
		},
		{
			name: "#6 successfully orchestrated tier movement",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &userpb.KycEvent{
					ActorId: "actor-id-1",
				},
				mocks: []interface{}{
					mockOrchestrator.EXPECT().OrchestrateTierMovement(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						tieringEnumPb.Tier_TIER_TEN, tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil,
					),
				},
			},
			want: &tieringCoPb.ProcessKycUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &TieringConsumer{
				UnimplementedTieringConsumerServer: tt.fields.UnimplementedTieringConsumerServer,
				conf:                               tt.fields.conf,
				orchestrator:                       tt.fields.orchestrator,
			}
			got, err := t.ProcessKycUpdateEvent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t1.Errorf("ProcessKycUpdateEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ProcessKycUpdateEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTieringConsumer_ProcessSalaryUpdateEvent(t1 *testing.T) {
	ctrl := gomock.NewController(t1)
	defer ctrl.Finish()
	mockOrchestrator := mocks.NewMockOrchestrator(ctrl)

	type fields struct {
		UnimplementedTieringConsumerServer tieringCoPb.UnimplementedTieringConsumerServer
		conf                               *genconf.Config
		orchestrator                       orchestrator.Orchestrator
		releaseManager                     release.Manager
	}
	type args struct {
		ctx   context.Context
		req   *salaryprogramEventsPb.SalaryProgramStatusUpdateEvent
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *tieringCoPb.ProcessSalaryUpdateEventResponse
		wantErr bool
	}{
		{
			name: "#1 actor id not present",
			fields: fields{
				conf: dynconf,
			},
			args: args{
				ctx: context.Background(),
				req: &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent{},
			},
			want: &tieringCoPb.ProcessSalaryUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: permanentFailureStatus},
			},
			wantErr: false,
		},
		{
			name: "#2 salary program status unprocessable",
			fields: fields{
				conf: dynconf,
			},
			args: args{
				ctx: context.Background(),
				req: &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent{
					ActorId:             "actor-id-1",
					SalaryProgramStatus: salaryprogramEventsPb.SalaryProgramStatus_SALARY_PROGRAM_STATUS_REGISTRATION_COMPLETED,
				},
			},
			want: &tieringCoPb.ProcessSalaryUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			},
			wantErr: false,
		},
		{
			name: "#5 error in orchestrator",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent{
					ActorId:             "actor-id-1",
					SalaryProgramStatus: salaryprogramEventsPb.SalaryProgramStatus_SALARY_PROGRAM_STATUS_ACTIVE,
				},
				mocks: []interface{}{
					mockOrchestrator.EXPECT().OrchestrateTierMovement(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringEnumPb.Tier_TIER_UNSPECIFIED, errors.New("error in orchestrator"),
					),
				},
			},
			want: &tieringCoPb.ProcessSalaryUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: transientFailureStatus},
			},
			wantErr: false,
		},
		{
			name: "#6 error same tier movement not allowed",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent{
					ActorId:             "actor-id-1",
					SalaryProgramStatus: salaryprogramEventsPb.SalaryProgramStatus_SALARY_PROGRAM_STATUS_ACTIVE,
				},
				mocks: []interface{}{
					mockOrchestrator.EXPECT().OrchestrateTierMovement(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringErrors.ErrSameTierMovementNotAllowed,
					),
				},
			},
			want: &tieringCoPb.ProcessSalaryUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			},
			wantErr: false,
		},
		{
			name: "#7 successfully orchestrated tier movement",
			fields: fields{
				conf:         dynconf,
				orchestrator: mockOrchestrator,
			},
			args: args{
				ctx: context.Background(),
				req: &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent{
					ActorId:             "actor-id-1",
					SalaryProgramStatus: salaryprogramEventsPb.SalaryProgramStatus_SALARY_PROGRAM_STATUS_ACTIVE,
				},
				mocks: []interface{}{
					mockOrchestrator.EXPECT().OrchestrateTierMovement(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
						tieringEnumPb.Tier_TIER_TEN, tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil,
					),
				},
			},
			want: &tieringCoPb.ProcessSalaryUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: successStatus},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &TieringConsumer{
				UnimplementedTieringConsumerServer: tt.fields.UnimplementedTieringConsumerServer,
				conf:                               tt.fields.conf,
				orchestrator:                       tt.fields.orchestrator,
				releaseManager:                     tt.fields.releaseManager,
			}
			got, err := t.ProcessSalaryUpdateEvent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t1.Errorf("ProcessSalaryUpdateEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ProcessSalaryUpdateEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTieringConsumer_ProcessBalanceUpdateEvent(t1 *testing.T) {
	curTimestamp := timestampPb.Now()
	type args struct {
		ctx context.Context
		req *savingsCoPb.BalanceUpdate
	}
	tests := []struct {
		name    string
		mocks   func(f *mockStruct)
		toSleep bool
		args    args
		want    *tieringCoPb.ProcessBalanceUpdateEventResponse
		wantErr bool
	}{
		{
			name: "successfully processed",
			mocks: func(f *mockStruct) {
				f.dataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil)
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.tierOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).Return(tierOptionsMap, nil)
				f.eventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any())
			},
			args: args{
				ctx: context.Background(),
				req: &savingsCoPb.BalanceUpdate{
					ActorId:   "actor-1",
					AccountId: "account-1",
					PreviousBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        5100,
					},
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        5200,
					},
					DiffBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
					LedgerBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        5200,
					},
					BalanceAt:          curTimestamp,
					PublishedTimestamp: curTimestamp,
				},
			},
			want: &tieringCoPb.ProcessBalanceUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				}},
			wantErr: false,
		},
		{
			name: "error in triggering event",
			mocks: func(f *mockStruct) {
				f.dataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_UNSPECIFIED, errors.New("some random error"))
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.tierOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).Return(tierOptionsMap, nil)
			},
			toSleep: true,
			args: args{
				ctx: context.Background(),
				req: &savingsCoPb.BalanceUpdate{
					ActorId:   "actor-1",
					AccountId: "account-1",
					PreviousBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        5100,
					},
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        5200,
					},
					DiffBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
					LedgerBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        5200,
					},
					BalanceAt:          curTimestamp,
					PublishedTimestamp: curTimestamp,
				},
			},
			want: &tieringCoPb.ProcessBalanceUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				}},
			wantErr: false,
		},
		{
			name: "empty actorId passed",
			mocks: func(f *mockStruct) {
			},
			args: args{
				ctx: context.Background(),
				req: &savingsCoPb.BalanceUpdate{},
			},
			want: &tieringCoPb.ProcessBalanceUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				}},
			wantErr: false,
		},
		{
			name: "not triggering event",
			mocks: func(f *mockStruct) {
			},
			args: args{
				ctx: context.Background(),
				req: &savingsCoPb.BalanceUpdate{
					ActorId:   "actor-1",
					AccountId: "account-1",
					PreviousBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        200,
					},
					DiffBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
					LedgerBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        200,
					},
					BalanceAt:          curTimestamp,
					PublishedTimestamp: curTimestamp,
				},
			},
			want: &tieringCoPb.ProcessBalanceUpdateEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t1.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()
			f := initMocks(ctrl)
			t := &TieringConsumer{
				actorTierInfoDao:   f.actorTierInfoDao,
				timelineManager:    f.timelineManager,
				tierOptionsManager: f.tierOptionsManager,
				eventBroker:        f.eventBroker,
				dataProcessor:      f.dataProcessor,
			}
			tt.mocks(f)
			got, err := t.ProcessBalanceUpdateEvent(tt.args.ctx, tt.args.req)
			if tt.toSleep {
				time.Sleep(time.Second)
			}
			if (err != nil) != tt.wantErr {
				t1.Errorf("ProcessBalanceUpdateEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ProcessBalanceUpdateEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTieringConsumer_determineNextClosestTier(t1 *testing.T) {
	type fields struct {
	}
	type args struct {
		currentTier    tieringExtPb.Tier
		balanceAmount  *moneyPb.Money
		tierOptionsMap tier_options.TierOptionsMap
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    tieringExtPb.Tier
		wantErr bool
	}{
		{
			name:   "current tier: Plus, enough balance for: Infinite",
			fields: fields{},
			args: args{
				currentTier: tieringExtPb.Tier_TIER_FI_PLUS,
				balanceAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        50001,
				},
				tierOptionsMap: tierOptionsMap,
			},
			want:    tieringExtPb.Tier_TIER_FI_INFINITE,
			wantErr: false,
		},
		{
			name:   "current tier: Plus, not enough balance for: Infinite",
			fields: fields{},
			args: args{
				currentTier: tieringExtPb.Tier_TIER_FI_PLUS,
				balanceAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        10001,
				},
				tierOptionsMap: tierOptionsMap,
			},
			want:    tieringExtPb.Tier_TIER_UNSPECIFIED,
			wantErr: false,
		},
		{
			name:   "current tier: Plus, not enough balance for: Plus",
			fields: fields{},
			args: args{
				currentTier: tieringExtPb.Tier_TIER_FI_PLUS,
				balanceAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        9001,
				},
				tierOptionsMap: tierOptionsMap,
			},
			want:    tieringExtPb.Tier_TIER_UNSPECIFIED,
			wantErr: false,
		},
		{
			name:   "current tier: Standard, enough balance for: Plus",
			fields: fields{},
			args: args{
				currentTier: tieringExtPb.Tier_TIER_FI_BASIC,
				balanceAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        10001,
				},
				tierOptionsMap: tierOptionsMap,
			},
			want:    tieringExtPb.Tier_TIER_FI_PLUS,
			wantErr: false,
		},
		// Should be plus since we only check for next highest tier
		{
			name:   "current tier: Standard, enough balance for: Infinite",
			fields: fields{},
			args: args{
				currentTier: tieringExtPb.Tier_TIER_FI_BASIC,
				balanceAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        50001,
				},
				tierOptionsMap: tierOptionsMap,
			},
			want:    tieringExtPb.Tier_TIER_FI_PLUS,
			wantErr: false,
		},
		{
			name:   "current tier: Salary, enough balance for: Infinite",
			fields: fields{},
			args: args{
				currentTier: tieringExtPb.Tier_TIER_FI_SALARY,
				balanceAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        50001,
				},
				tierOptionsMap: tierOptionsMap,
			},
			want:    tieringExtPb.Tier_TIER_UNSPECIFIED,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &TieringConsumer{}
			got, err := t.determineNextClosestTier(tt.args.currentTier, tt.args.balanceAmount, tt.args.tierOptionsMap)
			if (err != nil) != tt.wantErr {
				t1.Errorf("determineNextClosestTier() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t1.Errorf("determineNextClosestTier() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTieringConsumer_gatherDataForTriggeringEvent(t1 *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		mocks   func(f *mockStruct)
		toSleep bool
		args    args
		want    *BalanceEventCollectedData
		wantErr bool
	}{
		{
			name: "current tier: Plus, user is in cool off",
			mocks: func(f *mockStruct) {
				f.dataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil)
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.tierOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).Return(tierOptionsMap, nil)
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			want: &BalanceEventCollectedData{
				CurrentTier:     tieringExtPb.Tier_TIER_FI_PLUS,
				IsUserInCoolOff: true,
				TierOptionsMap:  tierOptionsMap,
			},
			wantErr: false,
		},
		{
			name: "error in getting current tier, user is in cool off",
			mocks: func(f *mockStruct) {
				f.dataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_UNSPECIFIED, errors.New("some random error"))
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.tierOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).Return(tierOptionsMap, nil)
			},
			toSleep: true,
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "current tier: Plus, error in chekcing if user is in cool off",
			mocks: func(f *mockStruct) {
				f.dataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil)
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(true, errors.New("some random error"))
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.tierOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).Return(tierOptionsMap, nil)
			},
			toSleep: true,
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error in fetching tier options",
			mocks: func(f *mockStruct) {
				f.dataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil)
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.tierOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
			},
			toSleep: true,
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t1.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()
			f := initMocks(ctrl)
			t := &TieringConsumer{
				actorTierInfoDao:   f.actorTierInfoDao,
				timelineManager:    f.timelineManager,
				tierOptionsManager: f.tierOptionsManager,
				dataProcessor:      f.dataProcessor,
			}
			tt.mocks(f)
			got, err := t.gatherDataForTriggeringEvent(tt.args.ctx, tt.args.actorId)
			if tt.toSleep {
				time.Sleep(time.Second)
			}
			if (err != nil) != tt.wantErr {
				t1.Errorf("gatherDataForTriggeringEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("gatherDataForTriggeringEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTieringConsumer_isUserInCoolOff(t1 *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		mocks   func(f *mockStruct)
		args    args
		toSleep bool
		want    bool
		wantErr bool
	}{
		{
			name: "user is in cool off and will be in cool off",
			mocks: func(f *mockStruct) {
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "user is not in cool off and will be in cool off",
			mocks: func(f *mockStruct) {
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(false, nil)
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "user is in cool off and will not be in cool off",
			mocks: func(f *mockStruct) {
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(false, nil)
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "error checking if user in cool off",
			mocks: func(f *mockStruct) {
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(false, errors.New("some random error"))
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(false, nil)
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			toSleep: true,
			want:    false,
			wantErr: true,
		},
		{
			name: "error checking if user will be in cool off",
			mocks: func(f *mockStruct) {
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(false, nil)
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(false, errors.New("some random error"))
			},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
			},
			toSleep: true,
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t1.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()
			f := initMocks(ctrl)
			t := &TieringConsumer{
				timelineManager: f.timelineManager,
			}
			tt.mocks(f)
			got, err := t.isUserInCoolOff(tt.args.ctx, tt.args.actorId)
			if tt.toSleep {
				time.Sleep(time.Second)
			}
			if (err != nil) != tt.wantErr {
				t1.Errorf("isUserInCoolOff() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t1.Errorf("isUserInCoolOff() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTieringConsumer_toTriggerEvent(t1 *testing.T) {
	type fields struct {
	}
	type args struct {
		req *savingsCoPb.BalanceUpdate
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name:   "True",
			fields: fields{},
			args: args{
				req: &savingsCoPb.BalanceUpdate{
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        5001,
					},
					DiffBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1,
					},
				},
			},
			want:    true,
			wantErr: false,
		},
		{
			name:   "False, no diff",
			fields: fields{},
			args: args{
				req: &savingsCoPb.BalanceUpdate{
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        balanceUpdateEventThreshold.GetUnits() + 1,
					},
					DiffBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
					},
				},
			},
			want:    false,
			wantErr: false,
		},
		{
			name:   "False, not enough balance available",
			fields: fields{},
			args: args{
				req: &savingsCoPb.BalanceUpdate{
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        balanceUpdateEventThreshold.GetUnits() - 1,
					},
					DiffBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1,
					},
				},
			},
			want:    false,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &TieringConsumer{}
			got, err := t.toTriggerEvent(tt.args.req)
			if (err != nil) != tt.wantErr {
				t1.Errorf("toTriggerEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t1.Errorf("toTriggerEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTieringConsumer_triggerEvent(t1 *testing.T) {
	type args struct {
		ctx           context.Context
		actorId       string
		balanceAmount *moneyPb.Money
	}
	tests := []struct {
		name    string
		mocks   func(f *mockStruct)
		args    args
		wantErr bool
	}{
		{
			name: "successfully trigger event",
			mocks: func(f *mockStruct) {
				f.dataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil)
				f.timelineManager.EXPECT().IsUserInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.timelineManager.EXPECT().WillUserBeInCooloff(gomock.Any(), gomock.Any()).Return(true, nil)
				f.tierOptionsManager.EXPECT().GetTierOptionsMap(gomock.Any(), gomock.Any()).Return(tierOptionsMap, nil)
				f.eventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any())
			},
			args: args{
				ctx:           context.Background(),
				actorId:       "actor-1",
				balanceAmount: balanceUpdateEventThreshold,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t1.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()
			f := initMocks(ctrl)
			t := &TieringConsumer{
				eventBroker:        f.eventBroker,
				actorTierInfoDao:   f.actorTierInfoDao,
				timelineManager:    f.timelineManager,
				tierOptionsManager: f.tierOptionsManager,
				dataProcessor:      f.dataProcessor,
			}
			tt.mocks(f)
			if err := t.triggerEvent(tt.args.ctx, tt.args.actorId, tt.args.balanceAmount); (err != nil) != tt.wantErr {
				t1.Errorf("triggerEvent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestTieringConsumer_ProcessBalanceUpdateMarketingEvent(t *testing.T) {
	type args struct {
		ctx context.Context
		req *savingsCoPb.BalanceUpdate
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(f *mockStruct)
		want    *tieringCoPb.ProcessBalanceUpdateMarketingEventResponse
		wantErr bool
	}{
		{
			name: "successfully processed",
			mocks: func(f *mockStruct) {
				f.eventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any())
			},
			args: args{
				ctx: context.Background(),
				req: &savingsCoPb.BalanceUpdate{
					ActorId:   "actor-1",
					AccountId: "account-1",
					PreviousBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        300,
					},
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        200,
					},
					DiffBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        -100,
					},
				},
			},
			want: &tieringCoPb.ProcessBalanceUpdateMarketingEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				}},
			wantErr: false,
		},
		{
			name:  "actor id is empty",
			mocks: func(f *mockStruct) {},
			args: args{
				ctx: context.Background(),
				req: &savingsCoPb.BalanceUpdate{
					ActorId: "",
				},
			},
			want: &tieringCoPb.ProcessBalanceUpdateMarketingEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				}},
			wantErr: false,
		},
		{
			name:  "balance update packet is not dropped balance being max threshold",
			mocks: func(f *mockStruct) {},
			args: args{
				ctx: context.Background(),
				req: &savingsCoPb.BalanceUpdate{
					ActorId:   "actor-1",
					AccountId: "account-1",
					PreviousBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        300,
					},
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2000,
					},
					DiffBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1700,
					},
				},
			},
			want: &tieringCoPb.ProcessBalanceUpdateMarketingEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				}},
			wantErr: false,
		},
		{
			name: "balance update packet is not eligible due to credit txn",
			mocks: func(f *mockStruct) {
			},
			args: args{
				ctx: context.Background(),
				req: &savingsCoPb.BalanceUpdate{
					ActorId:   "actor-1",
					AccountId: "account-1",
					PreviousBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        300,
					},
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        200,
					},
					DiffBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
				},
			},
			want: &tieringCoPb.ProcessBalanceUpdateMarketingEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()
			f := initMocks(ctrl)
			tc := &TieringConsumer{
				eventBroker: f.eventBroker,
			}
			tt.mocks(f)
			got, err := tc.ProcessBalanceUpdateMarketingEvent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t1.Errorf("ProcessBalanceUpdateEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ProcessBalanceUpdateEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}
