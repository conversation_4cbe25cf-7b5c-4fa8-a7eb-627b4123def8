package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"

	gormv2 "gorm.io/gorm"

	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	actorConsumerPb "github.com/epifi/gamma/api/actor/consumer"
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) PurgeActorPiResolution(ctx context.Context, req *actorConsumerPb.PurgeActorPiResolutionRequest) (*actorConsumerPb.PurgeActorPiResolutionResponse, error) {
	var (
		res    = &actorConsumerPb.PurgeActorPiResolutionResponse{}
		header = &queuePb.ConsumerResponseHeader{}
	)
	res.ResponseHeader = header

	logger.Info(ctx, "received event for deletion of actor pi resolution",
		zap.Any("actor-ids", req.GetActorIds()))

	actorIdsToDelete, err := s.getActorIdsToDelete(ctx, req.GetActorIds())
	if err != nil {
		logger.Error(ctx, "error fetching actors to delete", zap.Error(err))
		header.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	}

	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		err = s.actorPiDao.DeleteActorPiForActorIds(txnCtx, actorIdsToDelete)
		if err != nil {
			return fmt.Errorf("error deleting actor pi relation: %v :%w", err, epifierrors.ErrTransient)
		}

		_, err = s.purgeActorPublisher.Publish(txnCtx, &actorConsumerPb.PurgeWealthActorsRequest{
			ActorIds: actorIdsToDelete,
			PiIds:    req.GetPiIds(),
		})
		if err != nil {
			return fmt.Errorf("error publishing packet to purge wealth actors: %v %w", err, epifierrors.ErrTransient)
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "error while commit txn", zap.Error(txnErr))
		header.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	}

	logger.Info(ctx, "successfully processed deletion event for actor pi resolution",
		zap.Any("actor-ids", req.GetActorIds()))

	header.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}

func getNonMerchantWealthActorIds(actors []*types.Actor) []string {
	var wealthActorIds []string
	for _, actor := range actors {
		if actor.GetOwnership() == commontypes.Ownership_EPIFI_WEALTH &&
			actor.GetType() != types.Actor_MERCHANT &&
			actor.GetType() != types.Actor_EXTERNAL_MERCHANT {
			wealthActorIds = append(wealthActorIds, actor.GetId())
		}
	}
	return wealthActorIds
}

// getActorIdsToDelete returns the actor Ids which satisfy the following conditions
// 1. Is Wealth non merchant Actor
// 2. Is not involved in any other aa transaction
func (s *Service) getActorIdsToDelete(ctx context.Context, actorIds []string) ([]string, error) {
	var (
		actorIdsToDelete []string
	)
	actors, err := s.actorDao.GetByIds(ctx, actorIds)
	if err != nil {
		if !errors.Is(err, gormv2.ErrRecordNotFound) && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return actorIdsToDelete, fmt.Errorf("error fetching actors by ids: %v %w", err, epifierrors.ErrTransient)
		}
	}
	if len(actors) == 0 {
		return actorIdsToDelete, nil
	}
	wealthActorIds := getNonMerchantWealthActorIds(actors)
	txnCountRes, err := s.aaOrderClient.GetTransactionCountForActors(ctx, &aaOrderPb.GetTransactionCountForActorsRequest{
		ActorIds: wealthActorIds,
	})
	if rpcErr := epifigrpc.RPCError(txnCountRes, err); rpcErr != nil {
		return actorIdsToDelete, fmt.Errorf("error fetching txn count for actor ids: %v %w", rpcErr, epifierrors.ErrTransient)
	}
	for key, value := range txnCountRes.GetActorIdToTxnCountMap() {
		if value == 0 {
			actorIdsToDelete = append(actorIdsToDelete, key)
		}
	}

	return actorIdsToDelete, nil
}
