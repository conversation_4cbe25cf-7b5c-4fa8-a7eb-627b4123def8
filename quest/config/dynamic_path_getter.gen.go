// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxpagesize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Erro<PERSON>("invalid path %q for primitive field \"MaxPageSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxPageSize, nil
	case "mincontrolgrouprollout":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinControlGroupRollOut\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinControlGroupRollOut, nil
	case "useattemptid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.E<PERSON><PERSON>("invalid path %q for primitive field \"UseAttemptID\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseAttemptID, nil
	case "questmanager":
		return obj.QuestManager.Get(dynamicFieldPath[1:])
	case "questfrontend":
		return obj.QuestFrontend.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *QuestManager) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "totalusersfi":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TotalUsersFi\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TotalUsersFi, nil
	case "displaymetrics":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisplayMetrics\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisplayMetrics, nil
	case "usepresto":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UsePresto\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UsePresto, nil
	case "validateevents":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ValidateEvents\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ValidateEvents, nil
	case "enableselfapproval":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableSelfApproval\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableSelfApproval, nil
	case "redislockduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RedisLockDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RedisLockDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for QuestManager", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *QuestFrontend) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "experimentstatusmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.ExperimentStatusMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"ExperimentStatusMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.ExperimentStatusMap[dynamicFieldPath[1]], nil

		}
		return obj.ExperimentStatusMap, nil
	case "experimentversionstatusmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.ExperimentVersionStatusMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"ExperimentVersionStatusMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.ExperimentVersionStatusMap[dynamicFieldPath[1]], nil

		}
		return obj.ExperimentVersionStatusMap, nil
	case "segmentsnapshotmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.SegmentSnapshotMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"SegmentSnapshotMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.SegmentSnapshotMap[dynamicFieldPath[1]], nil

		}
		return obj.SegmentSnapshotMap, nil
	case "newexperimentpage":
		return obj.NewExperimentPage.Get(dynamicFieldPath[1:])
	case "editexperimentpage":
		return obj.EditExperimentPage.Get(dynamicFieldPath[1:])
	case "experimentform":
		return obj.ExperimentForm.Get(dynamicFieldPath[1:])
	case "experimentformv2":
		return obj.ExperimentFormV2.Get(dynamicFieldPath[1:])
	case "editexperimentformv2":
		return obj.EditExperimentFormV2.Get(dynamicFieldPath[1:])
	case "homepage":
		return obj.HomePage.Get(dynamicFieldPath[1:])
	case "listexperimentspage":
		return obj.ListExperimentsPage.Get(dynamicFieldPath[1:])
	case "getexperiment":
		return obj.GetExperiment.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for QuestFrontend", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ExperimentFormPage) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "pageinfo":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PageInfo\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PageInfo, nil
	case "pagetitletext":
		return obj.PageTitleText.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ExperimentFormPage", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Text) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "textvariant":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TextVariant\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TextVariant, nil
	case "text":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Text, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Text", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ExperimentForm) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "formlabelvariant":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FormLabelVariant\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FormLabelVariant, nil
	case "usermanuallink":
		return obj.UserManualLink.Get(dynamicFieldPath[1:])
	case "experimentnamelabel":
		return obj.ExperimentNameLabel.Get(dynamicFieldPath[1:])
	case "experimentnametextinput":
		return obj.ExperimentNameTextInput.Get(dynamicFieldPath[1:])
	case "experimentarealabel":
		return obj.ExperimentAreaLabel.Get(dynamicFieldPath[1:])
	case "experimentareadropdowninput":
		return obj.ExperimentAreaDropdownInput.Get(dynamicFieldPath[1:])
	case "experimentdurationlabel":
		return obj.ExperimentDurationLabel.Get(dynamicFieldPath[1:])
	case "experimentdurationtextinput":
		return obj.ExperimentDurationTextInput.Get(dynamicFieldPath[1:])
	case "experimentdurationcheckboxinput":
		return obj.ExperimentDurationCheckboxInput.Get(dynamicFieldPath[1:])
	case "experimentdescriptiontextlabel":
		return obj.ExperimentDescriptionTextLabel.Get(dynamicFieldPath[1:])
	case "experimentdescriptiontextinput":
		return obj.ExperimentDescriptionTextInput.Get(dynamicFieldPath[1:])
	case "experimenttagslabel":
		return obj.ExperimentTagsLabel.Get(dynamicFieldPath[1:])
	case "experimenttagstagsinput":
		return obj.ExperimentTagsTagsInput.Get(dynamicFieldPath[1:])
	case "experimentlayerlabel":
		return obj.ExperimentLayerLabel.Get(dynamicFieldPath[1:])
	case "experimentlayerdropdowninput":
		return obj.ExperimentLayerDropdownInput.Get(dynamicFieldPath[1:])
	case "metricssegmenttitletext":
		return obj.MetricsSegmentTitleText.Get(dynamicFieldPath[1:])
	case "selectmetricslabel":
		return obj.SelectMetricsLabel.Get(dynamicFieldPath[1:])
	case "selectmetricsdropdowninput":
		return obj.SelectMetricsDropDownInput.Get(dynamicFieldPath[1:])
	case "usersegmentconditionexpressionlabel":
		return obj.UserSegmentConditionExpressionLabel.Get(dynamicFieldPath[1:])
	case "usersegmentconditionexpressiontextinput":
		return obj.UserSegmentConditionExpressionTextInput.Get(dynamicFieldPath[1:])
	case "usersegmentconditionexpressionbutton":
		return obj.UserSegmentConditionExpressionButton.Get(dynamicFieldPath[1:])
	case "segmentdynamismlabel":
		return obj.SegmentDynamismLabel.Get(dynamicFieldPath[1:])
	case "segmentdynamismdropdowninput":
		return obj.SegmentDynamismDropdownInput.Get(dynamicFieldPath[1:])
	case "androidappversionconditionlabel":
		return obj.AndroidAppVersionConditionLabel.Get(dynamicFieldPath[1:])
	case "androidappversionconditionminmaxinput":
		return obj.AndroidAppVersionConditionMinMaxInput.Get(dynamicFieldPath[1:])
	case "androidusergroupconditionlabel":
		return obj.AndroidUserGroupConditionLabel.Get(dynamicFieldPath[1:])
	case "androidusergroupconditiondropdowninput":
		return obj.AndroidUserGroupConditionDropdownInput.Get(dynamicFieldPath[1:])
	case "iosappversionconditionlabel":
		return obj.IOSAppVersionConditionLabel.Get(dynamicFieldPath[1:])
	case "iosappversionconditionminmaxinput":
		return obj.IOSAppVersionConditionMinMaxInput.Get(dynamicFieldPath[1:])
	case "iosusergroupconditionlabel":
		return obj.IOSUserGroupConditionLabel.Get(dynamicFieldPath[1:])
	case "iosusergroupconditiondropdowninput":
		return obj.IOSUserGroupConditionDropdownInput.Get(dynamicFieldPath[1:])
	case "usergroupconditionlabel":
		return obj.UserGroupConditionLabel.Get(dynamicFieldPath[1:])
	case "usergroupconditiondropdowninput":
		return obj.UserGroupConditionDropdownInput.Get(dynamicFieldPath[1:])
	case "userlayersegmentpercentlabel":
		return obj.UserLayerSegmentPercentLabel.Get(dynamicFieldPath[1:])
	case "userlayersegmentpercentprogressbar":
		return obj.UserLayerSegmentPercentProgressBar.Get(dynamicFieldPath[1:])
	case "controlvariantschema":
		return obj.ControlVariantSchema.Get(dynamicFieldPath[1:])
	case "variantschema":
		return obj.VariantSchema.Get(dynamicFieldPath[1:])
	case "variantvariabletable":
		return obj.VariantVariableTable.Get(dynamicFieldPath[1:])
	case "approveremaillabel":
		return obj.ApproverEmailLabel.Get(dynamicFieldPath[1:])
	case "approveremailtextinput":
		return obj.ApproverEmailTextInput.Get(dynamicFieldPath[1:])
	case "submitbutton":
		return obj.SubmitButton.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ExperimentForm", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Link) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "text":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Text, nil
	case "pagepath":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PagePath\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PagePath, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Link", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Label) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "text":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Text, nil
	case "hint":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Hint\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Hint, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Label", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TextInput) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isrequired":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRequired\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRequired, nil
	case "iseditable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEditable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEditable, nil
	case "placeholder":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Placeholder\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Placeholder, nil
	case "hinttext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HintText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HintText, nil
	case "value":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Value\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Value, nil
	case "id":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Id\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Id, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TextInput", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DropdownInput) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isrequired":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRequired\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRequired, nil
	case "iseditable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEditable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEditable, nil
	case "hint":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Hint\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Hint, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DropdownInput", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CheckboxInput) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "value":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Value\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Value, nil
	case "iseditable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEditable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEditable, nil
	case "text":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Text, nil
	case "hint":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Hint\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Hint, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CheckboxInput", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TagsInput) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isrequired":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRequired\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRequired, nil
	case "iseditable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEditable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEditable, nil
	case "placeholder":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Placeholder\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Placeholder, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TagsInput", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AutoCompleteDropDownInput) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isrequired":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRequired\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRequired, nil
	case "iseditable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEditable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEditable, nil
	case "isclearable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsClearable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsClearable, nil
	case "placeholder":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Placeholder\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Placeholder, nil
	case "hint":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Hint\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Hint, nil
	case "id":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Id\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Id, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AutoCompleteDropDownInput", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Button) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "buttontype":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ButtonType\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ButtonType, nil
	case "lefticontype":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LeftIconType\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LeftIconType, nil
	case "lefticonmaterialuiiconname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LeftIconMaterialUiIconName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LeftIconMaterialUiIconName, nil
	case "righticontype":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RightIconType\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RightIconType, nil
	case "righticonmaterialuiiconname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RightIconMaterialUiIconName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RightIconMaterialUiIconName, nil
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "lefticonurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LeftIconUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LeftIconUrl, nil
	case "righticonurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RightIconUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RightIconUrl, nil
	case "text":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Text, nil
	case "functionname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FunctionName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FunctionName, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Button", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *MinMaxInput) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isrequired":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRequired\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRequired, nil
	case "iseditable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEditable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEditable, nil
	case "minlabel":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinLabel\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinLabel, nil
	case "maxlabel":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxLabel\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxLabel, nil
	case "minplaceholder":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinPlaceholder\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinPlaceholder, nil
	case "maxplaceholder":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxPlaceholder\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxPlaceholder, nil
	case "minid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinId, nil
	case "maxid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for MinMaxInput", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ProgressBarInput) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isrequired":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRequired\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRequired, nil
	case "iseditable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEditable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEditable, nil
	case "label":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Label\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Label, nil
	case "labelhint":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LabelHint\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LabelHint, nil
	case "filledlabel":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FilledLabel\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FilledLabel, nil
	case "filledlabelhint":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FilledLabelHint\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FilledLabelHint, nil
	case "unfilledlabel":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UnfilledLabel\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UnfilledLabel, nil
	case "unfilledlabelhint":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UnfilledLabelHint\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UnfilledLabelHint, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ProgressBarInput", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VariantSchema) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxvariants":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxVariants\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxVariants, nil
	case "variantlabel":
		return obj.VariantLabel.Get(dynamicFieldPath[1:])
	case "variantnamelabel":
		return obj.VariantNameLabel.Get(dynamicFieldPath[1:])
	case "variantnametextinput":
		return obj.VariantNameTextInput.Get(dynamicFieldPath[1:])
	case "rolloutpercentlabel":
		return obj.RolloutPercentLabel.Get(dynamicFieldPath[1:])
	case "rolloutpercenttextinput":
		return obj.RolloutPercentTextInput.Get(dynamicFieldPath[1:])
	case "descriptionlabel":
		return obj.DescriptionLabel.Get(dynamicFieldPath[1:])
	case "descriptiontextinput":
		return obj.DescriptionTextInput.Get(dynamicFieldPath[1:])
	case "overridepolicylabel":
		return obj.OverridePolicyLabel.Get(dynamicFieldPath[1:])
	case "overridepolicyusergroupdropdowninput":
		return obj.OverridePolicyUserGroupDropdownInput.Get(dynamicFieldPath[1:])
	case "overridepolicyphonenumberstextinput":
		return obj.OverridePolicyPhoneNumbersTextInput.Get(dynamicFieldPath[1:])
	case "removevariantbutton":
		return obj.RemoveVariantButton.Get(dynamicFieldPath[1:])
	case "newvariantbutton":
		return obj.NewVariantButton.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VariantSchema", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VariantVariableTable) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "tabletype":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TableType\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TableType, nil
	case "maxvariables":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxVariables\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxVariables, nil
	case "headers":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Headers\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Headers, nil
	case "newvariablebutton":
		return obj.NewVariableButton.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VariantVariableTable", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ExperimentFormV2) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "basicinfo":
		return obj.BasicInfo.Get(dynamicFieldPath[1:])
	case "hypothesis":
		return obj.Hypothesis.Get(dynamicFieldPath[1:])
	case "goals":
		return obj.Goals.Get(dynamicFieldPath[1:])
	case "users":
		return obj.Users.Get(dynamicFieldPath[1:])
	case "androidappversion":
		return obj.AndroidAppVersion.Get(dynamicFieldPath[1:])
	case "iosappversion":
		return obj.IOSAppVersion.Get(dynamicFieldPath[1:])
	case "userbase":
		return obj.UserBase.Get(dynamicFieldPath[1:])
	case "variantvariableschema":
		return obj.VariantVariableSchema.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ExperimentFormV2", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *BasicInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "label":
		return obj.Label.Get(dynamicFieldPath[1:])
	case "experimentname":
		return obj.ExperimentName.Get(dynamicFieldPath[1:])
	case "experimentarea":
		return obj.ExperimentArea.Get(dynamicFieldPath[1:])
	case "experimentlayer":
		return obj.ExperimentLayer.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for BasicInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Hypothesis) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "label":
		return obj.Label.Get(dynamicFieldPath[1:])
	case "ifstatement":
		return obj.IfStatement.Get(dynamicFieldPath[1:])
	case "thenstatement":
		return obj.ThenStatement.Get(dynamicFieldPath[1:])
	case "becausestatement":
		return obj.BecauseStatement.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Hypothesis", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Goals) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "label":
		return obj.Label.Get(dynamicFieldPath[1:])
	case "events":
		return obj.Events.Get(dynamicFieldPath[1:])
	case "duration":
		return obj.Duration.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Goals", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Users) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "selectosoptions":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SelectOSOptions\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SelectOSOptions, nil
	case "label":
		return obj.Label.Get(dynamicFieldPath[1:])
	case "segment":
		return obj.Segment.Get(dynamicFieldPath[1:])
	case "segmenttype":
		return obj.SegmentType.Get(dynamicFieldPath[1:])
	case "usergroup":
		return obj.UserGroup.Get(dynamicFieldPath[1:])
	case "selectos":
		return obj.SelectOS.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Users", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AppVersion) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "label":
		return obj.Label.Get(dynamicFieldPath[1:])
	case "appversion":
		return obj.AppVersion.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AppVersion", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UserBase) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "label":
		return obj.Label.Get(dynamicFieldPath[1:])
	case "usersinexp":
		return obj.UsersInExp.Get(dynamicFieldPath[1:])
	case "userstaken":
		return obj.UsersTaken.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UserBase", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VariantVariableSchema) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "id":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Id\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Id, nil
	case "label":
		return obj.Label.Get(dynamicFieldPath[1:])
	case "variantname":
		return obj.VariantName.Get(dynamicFieldPath[1:])
	case "variantdescription":
		return obj.VariantDescription.Get(dynamicFieldPath[1:])
	case "rolloutpercent":
		return obj.RolloutPercent.Get(dynamicFieldPath[1:])
	case "overridepolicylabel":
		return obj.OverridePolicyLabel.Get(dynamicFieldPath[1:])
	case "overridepolicyusergroup":
		return obj.OverridePolicyUserGroup.Get(dynamicFieldPath[1:])
	case "overridepolicyphonenumber":
		return obj.OverridePolicyPhoneNumber.Get(dynamicFieldPath[1:])
	case "addvariable":
		return obj.AddVariable.Get(dynamicFieldPath[1:])
	case "variablevalue":
		return obj.VariableValue.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VariantVariableSchema", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *HomePage) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "defaultrowelementstextvariant":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultRowElementsTextVariant\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultRowElementsTextVariant, nil
	case "tableheader":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TableHeader\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TableHeader, nil
	case "hellotext":
		return obj.HelloText.Get(dynamicFieldPath[1:])
	case "usermanual":
		return obj.UserManual.Get(dynamicFieldPath[1:])
	case "myexptitle":
		return obj.MyExpTitle.Get(dynamicFieldPath[1:])
	case "allexptitle":
		return obj.AllExpTitle.Get(dynamicFieldPath[1:])
	case "allmyexpbtn":
		return obj.AllMyExpBtn.Get(dynamicFieldPath[1:])
	case "allexpbtn":
		return obj.AllExpBtn.Get(dynamicFieldPath[1:])
	case "newexpbtn":
		return obj.NewExpBtn.Get(dynamicFieldPath[1:])
	case "editrequesttext":
		return obj.EditRequestText.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for HomePage", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UserManual) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "textvariant":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TextVariant\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TextVariant, nil
	case "text":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Text, nil
	case "pagepath":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PagePath\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PagePath, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UserManual", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ExpTitle) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "textvariant":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TextVariant\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TextVariant, nil
	case "prefix":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Prefix\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Prefix, nil
	case "suffix":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Suffix\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Suffix, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ExpTitle", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EditRequestText) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "pendingreviewtext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PendingReviewText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PendingReviewText, nil
	case "nopendingreviewtext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NoPendingReviewText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NoPendingReviewText, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EditRequestText", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ListExperimentsPage) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "defaultrowelementstextvariant":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultRowElementsTextVariant\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultRowElementsTextVariant, nil
	case "tableheader":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TableHeader\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TableHeader, nil
	case "myexptitle":
		return obj.MyExpTitle.Get(dynamicFieldPath[1:])
	case "allexptitle":
		return obj.AllExpTitle.Get(dynamicFieldPath[1:])
	case "searchfilterinput":
		return obj.SearchFilterInput.Get(dynamicFieldPath[1:])
	case "usermanual":
		return obj.UserManual.Get(dynamicFieldPath[1:])
	case "newexpbtn":
		return obj.NewExpBtn.Get(dynamicFieldPath[1:])
	case "editrequesttext":
		return obj.EditRequestText.Get(dynamicFieldPath[1:])
	case "submitbtn":
		return obj.SubmitBtn.Get(dynamicFieldPath[1:])
	case "clearbtn":
		return obj.ClearBtn.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ListExperimentsPage", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *GetExperiment) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "redirectionpath":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RedirectionPath\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RedirectionPath, nil
	case "redirectionexperimentnamequeryparam":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RedirectionExperimentNameQueryParam\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RedirectionExperimentNameQueryParam, nil
	case "redirectionexperimentversionidqueryparam":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RedirectionExperimentVersionIdQueryParam\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RedirectionExperimentVersionIdQueryParam, nil
	case "experimentdetails":
		return obj.ExperimentDetails.Get(dynamicFieldPath[1:])
	case "varianttable":
		return obj.VariantTable.Get(dynamicFieldPath[1:])
	case "variablevarianttable":
		return obj.VariableVariantTable.Get(dynamicFieldPath[1:])
	case "versioningtable":
		return obj.VersioningTable.Get(dynamicFieldPath[1:])
	case "editbtn":
		return obj.EditBtn.Get(dynamicFieldPath[1:])
	case "eventmetricstables":
		return obj.EventMetricsTables.Get(dynamicFieldPath[1:])
	case "stopbtn":
		return obj.StopBtn.Get(dynamicFieldPath[1:])
	case "unpausebtn":
		return obj.UnpauseBtn.Get(dynamicFieldPath[1:])
	case "pausebtn":
		return obj.PauseBtn.Get(dynamicFieldPath[1:])
	case "runbtn":
		return obj.RunBtn.Get(dynamicFieldPath[1:])
	case "approvebtn":
		return obj.ApproveBtn.Get(dynamicFieldPath[1:])
	case "approveandrunbtn":
		return obj.ApproveAndRunBtn.Get(dynamicFieldPath[1:])
	case "rejectbtn":
		return obj.RejectBtn.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for GetExperiment", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ExperimentDetails) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "defaulttextvariant":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultTextVariant\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultTextVariant, nil
	case "id":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Id\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Id, nil
	case "name":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Name\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Name, nil
	case "description":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Description\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Description, nil
	case "area":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Area\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Area, nil
	case "layer":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Layer\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Layer, nil
	case "usergroups":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UserGroups\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UserGroups, nil
	case "segment":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Segment\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Segment, nil
	case "segmentsnapshot":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SegmentSnapshot\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SegmentSnapshot, nil
	case "percentrequired":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PercentRequired\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PercentRequired, nil
	case "status":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Status\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Status, nil
	case "tags":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Tags\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Tags, nil
	case "start":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Start\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Start, nil
	case "end":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"End\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.End, nil
	case "duration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Duration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Duration, nil
	case "approvedby":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ApprovedBy\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ApprovedBy, nil
	case "androidappversionlabel":
		return obj.AndroidAppVersionLabel.Get(dynamicFieldPath[1:])
	case "iosappversionlabel":
		return obj.IOSAppVersionLabel.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ExperimentDetails", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AppVersionLabel) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "text":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Text, nil
	case "minlabel":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinLabel\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinLabel, nil
	case "maxlabel":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxLabel\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxLabel, nil
	case "usergroupslabel":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UserGroupsLabel\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UserGroupsLabel, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AppVersionLabel", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Table) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "defaulttextvariant":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultTextVariant\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultTextVariant, nil
	case "headers":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Headers\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Headers, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Table", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Tables) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "headers":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Headers\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Headers, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Tables", strings.Join(dynamicFieldPath, "."))
	}
}
