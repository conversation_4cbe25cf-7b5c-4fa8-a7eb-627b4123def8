// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync/atomic"

	questtypes "github.com/epifi/be-common/api/quest/types"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/quest/sdk/config"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Disable experiment evaluation and return the default value for any variable evaluation
	_Disable uint32
	// ActorIDToUserLayerBucketMapForTest is used for evaluating segment percentage condition for an actor if their actor id is added in below map.
	// This is used only in non prod envs for testing
	_ActorIDToUserLayerBucketMapForTest *syncmap.Map[string, int16]
	// ActorIDToSegmentBucketMapForTest is used for evaluating variant percentage condition for an actor if their actor id is added in below map.
	// This is used only in non prod envs for testing
	_ActorIDToVariantBucketMapForTest *syncmap.Map[string, int16]
}

// Disable experiment evaluation and return the default value for any variable evaluation
func (obj *Config) Disable() bool {
	if atomic.LoadUint32(&obj._Disable) == 0 {
		return false
	} else {
		return true
	}
}

// ActorIDToUserLayerBucketMapForTest is used for evaluating segment percentage condition for an actor if their actor id is added in below map.
// This is used only in non prod envs for testing
func (obj *Config) ActorIDToUserLayerBucketMapForTest() *syncmap.Map[string, int16] {
	return obj._ActorIDToUserLayerBucketMapForTest
}

// ActorIDToSegmentBucketMapForTest is used for evaluating variant percentage condition for an actor if their actor id is added in below map.
// This is used only in non prod envs for testing
func (obj *Config) ActorIDToVariantBucketMapForTest() *syncmap.Map[string, int16] {
	return obj._ActorIDToVariantBucketMapForTest
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disable"] = _obj.SetDisable

	_obj._ActorIDToUserLayerBucketMapForTest = &syncmap.Map[string, int16]{}
	_setters["actoridtouserlayerbucketmapfortest"] = _obj.SetActorIDToUserLayerBucketMapForTest

	_obj._ActorIDToVariantBucketMapForTest = &syncmap.Map[string, int16]{}
	_setters["actoridtovariantbucketmapfortest"] = _obj.SetActorIDToVariantBucketMapForTest
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disable"] = _obj.SetDisable

	_obj._ActorIDToUserLayerBucketMapForTest = &syncmap.Map[string, int16]{}
	_setters["actoridtouserlayerbucketmapfortest"] = _obj.SetActorIDToUserLayerBucketMapForTest

	_obj._ActorIDToVariantBucketMapForTest = &syncmap.Map[string, int16]{}
	_setters["actoridtovariantbucketmapfortest"] = _obj.SetActorIDToVariantBucketMapForTest

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "disable":
		return obj.SetDisable(v.Disable, true, nil)
	case "actoridtouserlayerbucketmapfortest":
		return obj.SetActorIDToUserLayerBucketMapForTest(v.ActorIDToUserLayerBucketMapForTest, true, path)
	case "actoridtovariantbucketmapfortest":
		return obj.SetActorIDToVariantBucketMapForTest(v.ActorIDToVariantBucketMapForTest, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetDisable(v.Disable, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetActorIDToUserLayerBucketMapForTest(v.ActorIDToUserLayerBucketMapForTest, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetActorIDToVariantBucketMapForTest(v.ActorIDToVariantBucketMapForTest, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	return nil
}

func (obj *Config) SetDisable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.Disable", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Disable, 1)
	} else {
		atomic.StoreUint32(&obj._Disable, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Disable")
	}
	return nil
}
func (obj *Config) SetActorIDToUserLayerBucketMapForTest(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int16)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.ActorIDToUserLayerBucketMapForTest", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._ActorIDToUserLayerBucketMapForTest, v, path)
}
func (obj *Config) SetActorIDToVariantBucketMapForTest(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int16)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.ActorIDToVariantBucketMapForTest", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._ActorIDToVariantBucketMapForTest, v, path)
}
