package catalog

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"

	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vgCatalogMocks "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise/mocks"
	"github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	daoMocks "github.com/epifi/gamma/securities/catalog/dao/mocks"
	ingesterMocks "github.com/epifi/gamma/securities/catalog/ingester/mocks"
	"github.com/epifi/gamma/securities/test"
)

func TestService_AddSecurityWithISINs(t *testing.T) {
	t.Parallel()

	type args struct {
		req *catalogPb.AddSecurityWithISINsRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(
			mockSecurityListingsDao *daoMocks.MockSecurityListingsDao,
			mockVgCatalogClient *vgCatalogMocks.MockCatalogClient,
			mockCatalogDataIngester *ingesterMocks.MockCatalogDataIngester,
		)
		want    *catalogPb.AddSecurityWithISINsResponse
		wantErr bool
	}{
		{
			name: "successful update of existing security listing",
			args: args{
				req: &catalogPb.AddSecurityWithISINsRequest{
					Isins: []string{"INE848E01016"},
				},
			},
			setupMocks: func(
				mockSecurityListingsDao *daoMocks.MockSecurityListingsDao,
				mockVgCatalogClient *vgCatalogMocks.MockCatalogClient,
				mockCatalogDataIngester *ingesterMocks.MockCatalogDataIngester,
			) {
				// Mock getAssetDetails call
				mockVgCatalogClient.EXPECT().
					GetAssetIdentifierDetails(gomock.Any(), &vgCatalogPb.GetAssetIdentifierDetailsRequest{
						Header:          &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
						IdentifierValue: "INE848E01016",
						IdentifierType:  vgCatalogPb.IdentifierType_IDENTIFIER_TYPE_ISIN,
					}).
					Return(&vgCatalogPb.GetAssetIdentifierDetailsResponse{
						Status: rpc.StatusOk(),
						AssetDetails: []*bridgewise.AssetDetails{
							{
								TradingItemId: 12345,
								CompanyId:     67890,
							},
						},
					}, nil)

				// Mock GetByVendorListingId call - existing listing found
				mockSecurityListingsDao.EXPECT().
					GetByVendorListingId(
						gomock.Any(),
						vendorgateway.Vendor_BRIDGEWISE,
						"12345",
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
						},
					).
					Return(&catalogPb.SecurityListing{
						ExternalId: "listing-123",
						Isin:       "",
					}, nil)

				// Mock UpdateByVendorListingId call
				mockSecurityListingsDao.EXPECT().
					UpdateByVendorListingId(
						gomock.Any(),
						&catalogPb.SecurityListing{
							Isin:            "INE848E01016",
							Vendor:          vendorgateway.Vendor_BRIDGEWISE,
							VendorListingId: "12345",
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
						},
					).
					Return(nil)

				// Mock catalogDataIngester call - no new ISINs to ingest
				mockCatalogDataIngester.EXPECT().
					IngestByISINs(gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]*catalogPb.IsinSecurityListingPair{}, []string{}, nil)
			},
			want: &catalogPb.AddSecurityWithISINsResponse{
				Status: rpc.StatusOk(),
				IsinSecurityListingPairs: []*catalogPb.IsinSecurityListingPair{
					{
						Isin:              "INE848E01016",
						SecurityListingId: "listing-123",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successful ingestion of new security listing",
			args: args{
				req: &catalogPb.AddSecurityWithISINsRequest{
					Isins: []string{"INE999E01999"},
				},
			},
			setupMocks: func(
				mockSecurityListingsDao *daoMocks.MockSecurityListingsDao,
				mockVgCatalogClient *vgCatalogMocks.MockCatalogClient,
				mockCatalogDataIngester *ingesterMocks.MockCatalogDataIngester,
			) {
				// Mock getAssetDetails call
				mockVgCatalogClient.EXPECT().
					GetAssetIdentifierDetails(gomock.Any(), &vgCatalogPb.GetAssetIdentifierDetailsRequest{
						Header:          &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
						IdentifierValue: "INE999E01999",
						IdentifierType:  vgCatalogPb.IdentifierType_IDENTIFIER_TYPE_ISIN,
					}).
					Return(&vgCatalogPb.GetAssetIdentifierDetailsResponse{
						Status: rpc.StatusOk(),
						AssetDetails: []*bridgewise.AssetDetails{
							{
								TradingItemId: 54321,
								CompanyId:     98765,
							},
						},
					}, nil)

				// Mock GetByVendorListingId call - listing not found
				mockSecurityListingsDao.EXPECT().
					GetByVendorListingId(
						gomock.Any(),
						vendorgateway.Vendor_BRIDGEWISE,
						"54321",
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
						},
					).
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock catalogDataIngester call - new ingestion needed
				mockCatalogDataIngester.EXPECT().
					IngestByISINs(gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]*catalogPb.IsinSecurityListingPair{
						{
							Isin:              "INE999E01999",
							SecurityListingId: "new-listing-456",
						},
					}, []string{}, nil)
			},
			want: &catalogPb.AddSecurityWithISINsResponse{
				Status: rpc.StatusOk(),
				IsinSecurityListingPairs: []*catalogPb.IsinSecurityListingPair{
					{
						Isin:              "INE999E01999",
						SecurityListingId: "new-listing-456",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "vendor gateway returns no asset details",
			args: args{
				req: &catalogPb.AddSecurityWithISINsRequest{
					Isins: []string{"INE000E00000"},
				},
			},
			setupMocks: func(
				mockSecurityListingsDao *daoMocks.MockSecurityListingsDao,
				mockVgCatalogClient *vgCatalogMocks.MockCatalogClient,
				mockCatalogDataIngester *ingesterMocks.MockCatalogDataIngester,
			) {
				// Mock getAssetDetails call - empty asset details
				mockVgCatalogClient.EXPECT().
					GetAssetIdentifierDetails(gomock.Any(), &vgCatalogPb.GetAssetIdentifierDetailsRequest{
						Header:          &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
						IdentifierValue: "INE000E00000",
						IdentifierType:  vgCatalogPb.IdentifierType_IDENTIFIER_TYPE_ISIN,
					}).
					Return(&vgCatalogPb.GetAssetIdentifierDetailsResponse{
						Status:       rpc.StatusOk(),
						AssetDetails: []*bridgewise.AssetDetails{},
					}, nil)

				// Mock catalogDataIngester call
				mockCatalogDataIngester.EXPECT().
					IngestByISINs(gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]*catalogPb.IsinSecurityListingPair{}, []string{"INE000E00000"}, nil)
			},
			want: &catalogPb.AddSecurityWithISINsResponse{
				Status:                   rpc.StatusOk(),
				IsinSecurityListingPairs: []*catalogPb.IsinSecurityListingPair{},
			},
			wantErr: false,
		},
		{
			name: "catalog data ingester returns error",
			args: args{
				req: &catalogPb.AddSecurityWithISINsRequest{
					Isins: []string{"INE999E01999"},
				},
			},
			setupMocks: func(
				mockSecurityListingsDao *daoMocks.MockSecurityListingsDao,
				mockVgCatalogClient *vgCatalogMocks.MockCatalogClient,
				mockCatalogDataIngester *ingesterMocks.MockCatalogDataIngester,
			) {
				// Mock getAssetDetails call
				mockVgCatalogClient.EXPECT().
					GetAssetIdentifierDetails(gomock.Any(), &vgCatalogPb.GetAssetIdentifierDetailsRequest{
						Header:          &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
						IdentifierValue: "INE999E01999",
						IdentifierType:  vgCatalogPb.IdentifierType_IDENTIFIER_TYPE_ISIN,
					}).
					Return(&vgCatalogPb.GetAssetIdentifierDetailsResponse{
						Status: rpc.StatusOk(),
						AssetDetails: []*bridgewise.AssetDetails{
							{
								TradingItemId: 54321,
								CompanyId:     98765,
							},
						},
					}, nil)

				// Mock GetByVendorListingId call - listing not found
				mockSecurityListingsDao.EXPECT().
					GetByVendorListingId(
						gomock.Any(),
						vendorgateway.Vendor_BRIDGEWISE,
						"54321",
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
						},
					).
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock catalogDataIngester call - returns error
				mockCatalogDataIngester.EXPECT().
					IngestByISINs(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil, nil, errors.New("ingestion failed"))
			},
			want: &catalogPb.AddSecurityWithISINsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "partial failure - vendor gateway error for one ISIN",
			args: args{
				req: &catalogPb.AddSecurityWithISINsRequest{
					Isins: []string{"INE848E01016", "INVALID_ISIN"},
				},
			},
			setupMocks: func(
				mockSecurityListingsDao *daoMocks.MockSecurityListingsDao,
				mockVgCatalogClient *vgCatalogMocks.MockCatalogClient,
				mockCatalogDataIngester *ingesterMocks.MockCatalogDataIngester,
			) {
				// First ISIN - successful
				mockVgCatalogClient.EXPECT().
					GetAssetIdentifierDetails(gomock.Any(), &vgCatalogPb.GetAssetIdentifierDetailsRequest{
						Header:          &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
						IdentifierValue: "INE848E01016",
						IdentifierType:  vgCatalogPb.IdentifierType_IDENTIFIER_TYPE_ISIN,
					}).
					Return(&vgCatalogPb.GetAssetIdentifierDetailsResponse{
						Status: rpc.StatusOk(),
						AssetDetails: []*bridgewise.AssetDetails{
							{
								TradingItemId: 12345,
								CompanyId:     67890,
							},
						},
					}, nil)

				mockSecurityListingsDao.EXPECT().
					GetByVendorListingId(
						gomock.Any(),
						vendorgateway.Vendor_BRIDGEWISE,
						"12345",
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
						},
					).
					Return(&catalogPb.SecurityListing{
						ExternalId: "listing-123",
						Isin:       "",
					}, nil)

				mockSecurityListingsDao.EXPECT().
					UpdateByVendorListingId(
						gomock.Any(),
						&catalogPb.SecurityListing{
							Isin:            "INE848E01016",
							Vendor:          vendorgateway.Vendor_BRIDGEWISE,
							VendorListingId: "12345",
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
						},
					).
					Return(nil)

				// Second ISIN - failure
				mockVgCatalogClient.EXPECT().
					GetAssetIdentifierDetails(gomock.Any(), &vgCatalogPb.GetAssetIdentifierDetailsRequest{
						Header:          &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
						IdentifierValue: "INVALID_ISIN",
						IdentifierType:  vgCatalogPb.IdentifierType_IDENTIFIER_TYPE_ISIN,
					}).
					Return(nil, errors.New("invalid ISIN"))

				// Mock catalogDataIngester call with failed ISIN
				mockCatalogDataIngester.EXPECT().
					IngestByISINs(gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]*catalogPb.IsinSecurityListingPair{}, []string{"INVALID_ISIN"}, nil)
			},
			want: &catalogPb.AddSecurityWithISINsResponse{
				Status: rpc.StatusOk(),
				IsinSecurityListingPairs: []*catalogPb.IsinSecurityListingPair{
					{
						Isin:              "INE848E01016",
						SecurityListingId: "listing-123",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "security listing dao error on get",
			args: args{
				req: &catalogPb.AddSecurityWithISINsRequest{
					Isins: []string{"INE848E01016"},
				},
			},
			setupMocks: func(
				mockSecurityListingsDao *daoMocks.MockSecurityListingsDao,
				mockVgCatalogClient *vgCatalogMocks.MockCatalogClient,
				mockCatalogDataIngester *ingesterMocks.MockCatalogDataIngester,
			) {
				// Mock getAssetDetails call
				mockVgCatalogClient.EXPECT().
					GetAssetIdentifierDetails(gomock.Any(), &vgCatalogPb.GetAssetIdentifierDetailsRequest{
						Header:          &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
						IdentifierValue: "INE848E01016",
						IdentifierType:  vgCatalogPb.IdentifierType_IDENTIFIER_TYPE_ISIN,
					}).
					Return(&vgCatalogPb.GetAssetIdentifierDetailsResponse{
						Status: rpc.StatusOk(),
						AssetDetails: []*bridgewise.AssetDetails{
							{
								TradingItemId: 12345,
								CompanyId:     67890,
							},
						},
					}, nil)

				// Mock GetByVendorListingId call - database error
				mockSecurityListingsDao.EXPECT().
					GetByVendorListingId(
						gomock.Any(),
						vendorgateway.Vendor_BRIDGEWISE,
						"12345",
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
						},
					).
					Return(nil, errors.New("database connection error"))

				// Mock catalogDataIngester call - no ISINs processed
				mockCatalogDataIngester.EXPECT().
					IngestByISINs(gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]*catalogPb.IsinSecurityListingPair{}, []string{}, nil)
			},
			want: &catalogPb.AddSecurityWithISINsResponse{
				Status:                   rpc.StatusOk(),
				IsinSecurityListingPairs: []*catalogPb.IsinSecurityListingPair{},
			},
			wantErr: false,
		},
		{
			name: "multiple asset details in response",
			args: args{
				req: &catalogPb.AddSecurityWithISINsRequest{
					Isins: []string{"INE999E01999"},
				},
			},
			setupMocks: func(
				mockSecurityListingsDao *daoMocks.MockSecurityListingsDao,
				mockVgCatalogClient *vgCatalogMocks.MockCatalogClient,
				mockCatalogDataIngester *ingesterMocks.MockCatalogDataIngester,
			) {
				// Mock getAssetDetails call - multiple trading items
				mockVgCatalogClient.EXPECT().
					GetAssetIdentifierDetails(gomock.Any(), &vgCatalogPb.GetAssetIdentifierDetailsRequest{
						Header:          &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
						IdentifierValue: "INE999E01999",
						IdentifierType:  vgCatalogPb.IdentifierType_IDENTIFIER_TYPE_ISIN,
					}).
					Return(&vgCatalogPb.GetAssetIdentifierDetailsResponse{
						Status: rpc.StatusOk(),
						AssetDetails: []*bridgewise.AssetDetails{
							{
								TradingItemId: 54321,
								CompanyId:     98765,
							},
							{
								TradingItemId: 54322,
								CompanyId:     98765,
							},
						},
					}, nil)

				// Mock GetByVendorListingId call for first trading item
				mockSecurityListingsDao.EXPECT().
					GetByVendorListingId(
						gomock.Any(),
						vendorgateway.Vendor_BRIDGEWISE,
						"54321",
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
						},
					).
					Return(&catalogPb.SecurityListing{
						ExternalId: "listing-001",
						Isin:       "",
					}, nil)

				// Mock UpdateByVendorListingId call for first trading item
				mockSecurityListingsDao.EXPECT().
					UpdateByVendorListingId(
						gomock.Any(),
						&catalogPb.SecurityListing{
							Isin:            "INE999E01999",
							Vendor:          vendorgateway.Vendor_BRIDGEWISE,
							VendorListingId: "54321",
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
						},
					).
					Return(nil)

				// Mock GetByVendorListingId call for second trading item
				mockSecurityListingsDao.EXPECT().
					GetByVendorListingId(
						gomock.Any(),
						vendorgateway.Vendor_BRIDGEWISE,
						"54322",
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
						},
					).
					Return(&catalogPb.SecurityListing{
						ExternalId: "listing-002",
						Isin:       "",
					}, nil)

				// Mock UpdateByVendorListingId call for second trading item
				mockSecurityListingsDao.EXPECT().
					UpdateByVendorListingId(
						gomock.Any(),
						&catalogPb.SecurityListing{
							Isin:            "INE999E01999",
							Vendor:          vendorgateway.Vendor_BRIDGEWISE,
							VendorListingId: "54322",
						},
						[]catalogPb.SecurityListingFieldMask{
							catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
						},
					).
					Return(nil)

				// Mock catalogDataIngester call - no new ISINs to ingest
				mockCatalogDataIngester.EXPECT().
					IngestByISINs(gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]*catalogPb.IsinSecurityListingPair{}, []string{}, nil)
			},
			want: &catalogPb.AddSecurityWithISINsResponse{
				Status: rpc.StatusOk(),
				IsinSecurityListingPairs: []*catalogPb.IsinSecurityListingPair{
					{
						Isin:              "INE999E01999",
						SecurityListingId: "listing-001",
					},
					{
						Isin:              "INE999E01999",
						SecurityListingId: "listing-002",
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctrl := gomock.NewController(t)
			test.InitTestServerWithoutDBConn()
			defer ctrl.Finish()

			// Initialize mocks
			mockSecurityListingsDao := daoMocks.NewMockSecurityListingsDao(ctrl)
			mockVgCatalogClient := vgCatalogMocks.NewMockCatalogClient(ctrl)
			mockCatalogDataIngester := ingesterMocks.NewMockCatalogDataIngester(ctrl)

			// Setup mocks
			tt.setupMocks(mockSecurityListingsDao, mockVgCatalogClient, mockCatalogDataIngester)

			// Create service instance
			service := &Service{
				securityListingsDao: mockSecurityListingsDao,
				vgCatalogClient:     mockVgCatalogClient,
				catalogDataIngester: mockCatalogDataIngester,
			}

			// Execute the method
			got, err := service.AddSecurityWithISINs(context.Background(), tt.args.req)

			// Assert results
			if (err != nil) != tt.wantErr {
				t.Errorf("Service.AddSecurityWithISINs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("Service.AddSecurityWithISINs() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
