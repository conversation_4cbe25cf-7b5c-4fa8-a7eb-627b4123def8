package ingester

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/monitoring"
	consumerPb "github.com/epifi/gamma/api/securities/catalog/consumer"
	wireTypes "github.com/epifi/gamma/securities/wire/types"

	securitiesPb "github.com/epifi/gamma/api/securities/catalog"
	ussCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	"github.com/epifi/gamma/securities/catalog/dao"
	genConf "github.com/epifi/gamma/securities/config/genconf"
)

var (
	// list of exchanges to fetch securities from
	bridgewiseExchanges             = []string{"NSEI", "BSE", "NYSE", "NasdaqGS", "NYSEAM", "OTCPK"}
	bridgewiseIndianExchanges       = []string{"NSEI", "BSE"}
	bridgewiseUsExchanges           = []string{"NYSE", "NasdaqGS", "NYSEAM", "OTCPK"}
	bridgewiseToInternalExchangeMap = map[string]securitiesPb.Exchange{
		"NSEI":     securitiesPb.Exchange_EXCHANGE_INDIA_NSE,
		"BSE":      securitiesPb.Exchange_EXCHANGE_INDIA_BSE,
		"NYSE":     securitiesPb.Exchange_EXCHANGE_USA_NYSE,
		"NASDAQGS": securitiesPb.Exchange_EXCHANGE_USA_NASDAQ,
		"NYSEAM":   securitiesPb.Exchange_EXCHANGE_USA_ASE,
		"OTCPK":    securitiesPb.Exchange_EXCHANGE_USA_OTC,
	}

	ErrMaxPagesExceeded = errors.New("Max pages exceeded")
)

//go:generate mockgen -source=catalog_data_ingester.go -destination=./mocks/mock_ingester.go -package=mocks

type CatalogDataIngester interface {
	IngestByPage(ctx context.Context, pageNum int32) error
	IngestByISINs(ctx context.Context, isinMappings []*IsinVendorIdMapping, failedISINs []string) ([]*securitiesPb.IsinSecurityListingPair, []string, error)
}

// Compile-time assertion to ensure CatalogDataIngesterImpl implements CatalogDataIngester
var _ CatalogDataIngester = (*CatalogDataIngesterImpl)(nil)

var CatalogIngesterSet = wire.NewSet(NewCatalogIngester, wire.Bind(new(CatalogDataIngester), new(*CatalogDataIngesterImpl)))

type CatalogDataIngesterImpl struct {
	securityDao                        dao.SecuritiesDao
	securityListingsDao                dao.SecurityListingsDao
	vgCatalogClient                    vgCatalogPb.CatalogClient
	ussCatalogClient                   ussCatalogPb.CatalogManagerClient
	conf                               *genConf.Config
	stockCatalogRefreshPublisher       wireTypes.StockCatalogRefreshPublisher
	securitiesHistoricalPricePublisher wireTypes.SecuritiesHistoricalPricePublisher
}

func NewCatalogIngester(
	conf *genConf.Config,
	securityDao dao.SecuritiesDao,
	securityListingsDao dao.SecurityListingsDao,
	vgCatalogClient vgCatalogPb.CatalogClient,
	ussCatalogClient ussCatalogPb.CatalogManagerClient,
	stockCatalogRefreshPublisher wireTypes.StockCatalogRefreshPublisher,
	securitiesHistoricalPricePublisher wireTypes.SecuritiesHistoricalPricePublisher,
) *CatalogDataIngesterImpl {
	return &CatalogDataIngesterImpl{
		conf:                               conf,
		securityDao:                        securityDao,
		securityListingsDao:                securityListingsDao,
		vgCatalogClient:                    vgCatalogClient,
		ussCatalogClient:                   ussCatalogClient,
		stockCatalogRefreshPublisher:       stockCatalogRefreshPublisher,
		securitiesHistoricalPricePublisher: securitiesHistoricalPricePublisher,
	}
}

type IsinVendorIdMapping struct {
	Isin             string
	VendorSecurityId string
	VendorListingId  string
}

func (s *CatalogDataIngesterImpl) IngestByPage(ctx context.Context, pageNum int32) error {
	if int(pageNum) > s.conf.AddNewSecuritiesConfig().MaximumPageNum() {
		return ErrMaxPagesExceeded
	}

	ingestionErrorCount := 0
	logger.Info(ctx, "processing add new securities", zap.Any("pageNum", pageNum), zap.Strings("exchanges", s.getExchanges()))

	//nolint: gosec
	resp, err := s.vgCatalogClient.GetCompanies(ctx, &vgCatalogPb.GetCompaniesRequest{
		Header:    &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
		Exchanges: s.getExchanges(),
		Page:      pageNum,
		PageSize:  int32(s.conf.AddNewSecuritiesConfig().PageSize()),
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		if resp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "Page number is out of bounds", zap.Any("page number", pageNum))
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error while calling GetCompanies vg", zap.Error(rpcErr))
		return rpcErr
	}

	for _, companyData := range resp.GetCompanies().GetData() {
		err = s.ingestCompanyAndListings(ctx, companyData, nil)
		if err != nil {
			logger.Error(ctx, "error while ingesting company and listings", zap.Error(err), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
			ingestionErrorCount++
		}
	}

	if ingestionErrorCount > 0 {
		monitoring.KibanaInfoServiceMonitor(ctx, cfg.SECURITIES_SERVICE, "count of security ingestion failures", zap.Int("ingestionErrorCount", ingestionErrorCount))
	}

	return nil
}

func (s *CatalogDataIngesterImpl) IngestByISINs(ctx context.Context, isinMappings []*IsinVendorIdMapping, failedISINs []string) ([]*securitiesPb.IsinSecurityListingPair, []string, error) {
	isinMappings = lo.Uniq(isinMappings)
	var isinSecurityListingPairs []*securitiesPb.IsinSecurityListingPair
	for _, isinMapping := range isinMappings {
		resp, err := s.vgCatalogClient.GetCompany(ctx, &vgCatalogPb.GetCompanyRequest{
			Header:    &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
			CompanyId: isinMapping.VendorSecurityId,
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil || len(resp.GetCompanies()) == 0 {
			logger.Error(ctx, "Error fetching company with vendorSecurityId", zap.Error(rpcErr),
				zap.String("vendorSecurityId", isinMapping.VendorSecurityId), zap.String("isin", isinMapping.Isin))
			failedISINs = append(failedISINs, isinMapping.Isin)
			continue
		}

		err = s.ingestCompanyAndListings(ctx, resp.GetCompanies()[0], isinMapping)
		if err != nil {
			logger.Error(ctx, "Error ingesting company and listings", zap.Error(err))
			failedISINs = append(failedISINs, isinMapping.Isin)
			continue
		}

		securityListing, daoErr := s.securityListingsDao.GetByVendorListingId(ctx, vendorgateway.Vendor_BRIDGEWISE, isinMapping.VendorListingId, []securitiesPb.SecurityListingFieldMask{
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID,
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SECURITY_ID,
		})
		if daoErr != nil {
			logger.Error(ctx, "Error fetching security listing with vendorListingId", zap.Error(daoErr))
			failedISINs = append(failedISINs, isinMapping.Isin)
			continue
		}

		security, daoErr := s.securityDao.GetById(ctx, securityListing.GetSecurityId(), []securitiesPb.SecurityFieldMask{
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID,
		})
		if daoErr != nil {
			logger.Error(ctx, "Error fetching security with security id", zap.Error(daoErr))
			failedISINs = append(failedISINs, isinMapping.Isin)
			continue
		}

		s.publishListingAndHistoricalPrices(ctx, securityListing.GetSecurityId(), securityListing.GetExternalId(), security.GetVendorSecurityId(), securityListing.GetVendorListingId())

		isinSecurityListingPairs = append(isinSecurityListingPairs, &securitiesPb.IsinSecurityListingPair{
			Isin:              securityListing.GetIsin(),
			SecurityListingId: securityListing.GetExternalId(),
		})
	}
	return isinSecurityListingPairs, failedISINs, nil
}

// publishListingAndHistoricalPrices published packets to refresh and fill historical prices for the security listing
func (s *CatalogDataIngesterImpl) publishListingAndHistoricalPrices(ctx context.Context, securityId, externalId, vendorSecurityId, vendorListingId string) {
	_, err := s.stockCatalogRefreshPublisher.Publish(ctx, &consumerPb.RefreshSecurityDetailsRequest{
		SecurityId:    securityId,
		CompanyId:     vendorSecurityId,
		TradingItemId: vendorListingId,
	})
	if err != nil {
		logger.Error(ctx, "Error publishing listing and historical prices", zap.Error(err))
	}

	curTime := time.Now().In(datetime.IST)
	curDate := time.Date(curTime.Year(), curTime.Month(), curTime.Day(), 0, 0, 0, 0, datetime.IST)
	startDate := timestamppb.New(curDate.Add(-365 * 24 * time.Hour)) // 1 year
	// This will add a 1-second buffer to the end date
	endDate := timestamppb.New(curDate.Add(1))

	_, err = s.stockCatalogRefreshPublisher.Publish(ctx, &consumerPb.ProcessSecurityListingHistoricalPricesRequest{
		SecurityListingId: externalId,
		CompanyId:         vendorSecurityId,
		TradingItemId:     vendorListingId,
		StartDate:         startDate,
		EndDate:           endDate,
	})
	if err != nil {
		logger.Error(ctx, "Error publishing listing and historical prices", zap.Error(err))
	}
}

func (s *CatalogDataIngesterImpl) ingestCompanyAndListings(ctx context.Context, companyData *vendorPb.CompanyDetails, isinMapping *IsinVendorIdMapping) error {
	security, err := s.upsertCompany(ctx, companyData)
	if err != nil {
		logger.Error(ctx, "error while get or create company data", zap.Error(err), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
		return err
	}

	securityListingsVg, vgErr := s.getSecurityListingsFromVg(ctx, security.GetVendorSecurityId())
	if vgErr != nil {
		logger.Error(ctx, "error while fetching security listings from vg", zap.Error(vgErr), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
		return vgErr
	}

	var securityListings []*securitiesPb.SecurityListing

	for _, securityListingVg := range securityListingsVg.GetAssetDetails() {
		if _, ok := bridgewiseToInternalExchangeMap[strings.ToUpper(securityListingVg.GetExchangeSymbol())]; !ok {
			// if exchange is not handled internally skip parsing, currently only Indian and US stocks are ingested
			continue
		}
		externalId, generateErr := s.generateSecurityListingExternalId(ctx, securityListingVg.GetTickerSymbol(), securityListingVg.GetExchangeSymbol())
		if generateErr != nil {
			logger.Error(ctx, "error while calling createOrGetSecurityListingExternalId", zap.Error(generateErr),
				zap.Any("symbol", securityListingVg.GetTickerSymbol()), zap.Any("exchange", securityListingVg.GetExchangeSymbol()),
				zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()), zap.Any("tradingItemId", securityListingVg.GetTradingItemId()))
			return generateErr
		}
		securityListing := convertTradingItemToSecurityListing(securityListingVg, security.GetId(), externalId, isinMapping)
		securityListings = append(securityListings, securityListing)
	}

	upsertErr := s.securityListingsDao.BatchUpsert(ctx, securityListings, nil)
	if upsertErr != nil {
		var vendorListingIds []int64
		for _, securityListing := range securityListingsVg.GetAssetDetails() {
			vendorListingIds = append(vendorListingIds, securityListing.GetTradingItemId())
		}
		logger.Error(ctx, "error while upserting security listings", zap.Error(upsertErr),
			zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()), zap.Any("vendorListingIds", vendorListingIds))
		return upsertErr
	}
	return nil
}

func convertAssetDetailsToSecurityPb(ctx context.Context, securityData *vendorPb.CompanyDetails) *securitiesPb.Security {
	// This parsing logic will be specific to bridgewise
	// Ex: If Bridgewise provides us Trading Companies and Distributors, replace all converts it to Trading Companies & Distributors
	gicsSectorType, ok := MapGICSSectorTypeToEnum[strings.ReplaceAll(securityData.GetGicsSectorName(), "and", "&")]
	if !ok {
		logger.Info(ctx, "GICS Sector Type unavailable", zap.String("GICS Sector Type", securityData.GetGicsSectorName()))
		gicsSectorType = securitiesPb.GICSSectorType_GICS_SECTOR_TYPE_UNSPECIFIED
	}
	gicsIndustryGroupType, ok := MapGICSIndustryGroupTypeToEnum[strings.ReplaceAll(securityData.GetGicsIndustryGroupName(), "and", "&")]
	if !ok {
		logger.Info(ctx, "GICS Industry Group Type unavailable", zap.String("GICS Industry Group Type", securityData.GetGicsIndustryGroupName()))
		gicsIndustryGroupType = securitiesPb.GICSIndustryGroupType_GICS_INDUSTRY_GROUP_TYPE_UNSPECIFIED
	}
	gicsIndustryType, ok := MapGICSIndustryTypeToEnum[strings.ReplaceAll(securityData.GetGicsIndustryName(), "and", "&")]
	if !ok {
		logger.Info(ctx, "GICS Industry Type unavailable", zap.String("GICS Industry Type", securityData.GetGicsIndustryName()))
		gicsIndustryType = securitiesPb.GICSIndustryType_GICS_INDUSTRY_TYPE_UNSPECIFIED
	}
	return &securitiesPb.Security{
		SecurityType:     securitiesPb.SecurityType_SECURITY_TYPE_STOCK,
		SecurityName:     securityData.GetCompanyName(),
		Vendor:           vendorgateway.Vendor_BRIDGEWISE,
		VendorSecurityId: strconv.Itoa(int(securityData.GetCompanyId())),
		SecurityDetails: &securitiesPb.SecurityDetails{
			SecurityType: &securitiesPb.SecurityDetails_StockDetails{
				StockDetails: &securitiesPb.StockDetails{
					StockName:                securityData.GetCompanyName(),
					StockShortName:           securityData.GetCompanyNameShort(),
					WebsiteUrl:               securityData.GetWebsite(),
					RegionName:               securityData.GetRegionName(),
					IncorporationCountryName: securityData.GetIncorporationCountryName(),
					GicsSectorType:           gicsSectorType,
					GicsIndustryType:         gicsIndustryType,
					GicsIndustryGroupType:    gicsIndustryGroupType,
				},
			},
		},
	}
}

func convertTradingItemToSecurityListing(tradingItem *vendorPb.AssetDetails, securityId, externalId string, isinMapping *IsinVendorIdMapping) *securitiesPb.SecurityListing {
	if isinMapping != nil && isinMapping.Isin != "" {
		return &securitiesPb.SecurityListing{
			ExternalId:       externalId,
			SecurityId:       securityId,
			Exchange:         bridgewiseToInternalExchangeMap[strings.ToUpper(tradingItem.GetExchangeSymbol())],
			Symbol:           tradingItem.GetTickerSymbol(),
			IsPrimaryListing: tradingItem.GetPrimaryFlag(),
			Status:           securitiesPb.ListingStatus_LISTING_STATUS_ACTIVE,
			Vendor:           vendorgateway.Vendor_BRIDGEWISE,
			VendorListingId:  strconv.Itoa(int(tradingItem.GetTradingItemId())),
			FinancialInfo:    &securitiesPb.FinancialInfo{},
			Isin:             isinMapping.Isin,
		}
	}
	return &securitiesPb.SecurityListing{
		ExternalId:       externalId,
		SecurityId:       securityId,
		Exchange:         bridgewiseToInternalExchangeMap[strings.ToUpper(tradingItem.GetExchangeSymbol())],
		Symbol:           tradingItem.GetTickerSymbol(),
		IsPrimaryListing: tradingItem.GetPrimaryFlag(),
		Status:           securitiesPb.ListingStatus_LISTING_STATUS_ACTIVE,
		Vendor:           vendorgateway.Vendor_BRIDGEWISE,
		VendorListingId:  strconv.Itoa(int(tradingItem.GetTradingItemId())),
		FinancialInfo:    &securitiesPb.FinancialInfo{},
	}
}

// Currently supporting US and Indian security listings only
// If US listing -> if id is present in uss catalog service, use that id as the external id, else generate a new external id with USS prefix
// If IN listing -> create external id with INS prefix
func (s *CatalogDataIngesterImpl) generateSecurityListingExternalId(ctx context.Context, symbol string, exchange string) (string, error) {
	var externalId string
	switch bridgewiseToInternalExchangeMap[strings.ToUpper(exchange)] {
	case securitiesPb.Exchange_EXCHANGE_INDIA_NSE, securitiesPb.Exchange_EXCHANGE_INDIA_BSE:
		logger.Info(ctx, "generating external id for indian securities", zap.String("symbol", symbol), zap.String("exchange", exchange))
		externalId = "INS" + idgen.RandAlphaNumericString(5)
	case securitiesPb.Exchange_EXCHANGE_USA_NYSE, securitiesPb.Exchange_EXCHANGE_USA_NASDAQ, securitiesPb.Exchange_EXCHANGE_USA_ASE, securitiesPb.Exchange_EXCHANGE_USA_OTC:
		resp, err := s.ussCatalogClient.GetStocks(ctx, &ussCatalogPb.GetStocksRequest{
			Identifiers: &ussCatalogPb.GetStocksRequest_Symbols{
				Symbols: &ussCatalogPb.RepeatedStrings{
					Ids: []string{symbol},
				},
			},
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			if !resp.GetStatus().IsRecordNotFound() {
				return "", fmt.Errorf("error getting us stock id, skipping id generation: %s", symbol)
			}
			logger.Info(ctx, "symbol not found in us catalog service, generating new external id", zap.Error(err), zap.String("symbol", symbol), zap.String("exchange", exchange))
			externalId = "USS" + idgen.RandAlphaNumericString(5)
		} else {
			if _, ok := resp.GetStocks()[symbol]; !ok {
				logger.Info(ctx, "symbol not found in us catalog service, generating new external id", zap.Error(err), zap.String("symbol", symbol), zap.String("exchange", exchange))
				externalId = "USS" + idgen.RandAlphaNumericString(5)
				return externalId, nil
			}
			logger.Info(ctx, "symbol found in us catalog service", zap.String("symbol", symbol), zap.String("id", resp.GetStocks()[symbol].GetId()))
			externalId = resp.GetStocks()[symbol].GetId()
		}
	default:
		return "", fmt.Errorf("unknown exchange type %s", exchange)
	}
	return externalId, nil
}

func (s *CatalogDataIngesterImpl) upsertCompany(ctx context.Context, companyData *vendorPb.CompanyDetails) (*securitiesPb.Security, error) {
	security, getErr := s.securityDao.GetByVendorSecurityId(ctx, vendorgateway.Vendor_BRIDGEWISE, strconv.Itoa(int(companyData.GetCompanyId())),
		[]securitiesPb.SecurityFieldMask{
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID,
		})
	if getErr != nil && errors.Is(getErr, epifierrors.ErrRecordNotFound) {
		securityObj := convertAssetDetailsToSecurityPb(ctx, companyData)
		logger.Info(ctx, "creating new security", zap.Any("securityObj", securityObj))

		var createErr error
		security, createErr = s.securityDao.Create(ctx, securityObj)
		if createErr != nil {
			logger.Error(ctx, "error while calling Create", zap.Error(createErr), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
			return nil, createErr
		}
	} else if getErr != nil {
		logger.Error(ctx, "error while calling GetByVendorSecurityId", zap.Error(getErr), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
		return nil, getErr
	}
	return security, nil
}

func (s *CatalogDataIngesterImpl) getSecurityListingsFromVg(ctx context.Context, companyId string) (*vgCatalogPb.GetCompanyTradingItemsResponse, error) {
	// Get security listings for a security
	tradingItems, tradingItemsErr := s.vgCatalogClient.GetCompanyTradingItems(ctx, &vgCatalogPb.GetCompanyTradingItemsRequest{
		Header:    &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
		CompanyId: companyId,
	})
	if rpcErr := epifigrpc.RPCError(tradingItems, tradingItemsErr); rpcErr != nil {
		logger.Error(ctx, "error while calling GetCompanyTradingItems vg", zap.Error(rpcErr), zap.String(logger.REQUEST_ID, companyId))
		return nil, rpcErr
	}
	return tradingItems, nil
}

func (s *CatalogDataIngesterImpl) getExchanges() []string {
	switch s.conf.AddNewSecuritiesConfig().ExchangeType() {
	case "INDIA":
		return bridgewiseIndianExchanges
	case "US":
		return bridgewiseUsExchanges
	default:
		return bridgewiseExchanges
	}
}
