package dao

//go:generate mockgen -source=dao.go -destination=./mocks/mock_dao.go package=mocks
//go:generate dao_metrics_gen .

import (
	"context"

	"github.com/google/wire"

	datePb "google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/vendorgateway"

	catalogPb "github.com/epifi/gamma/api/securities/catalog"
)

var (
	SecuritiesDaoWireSet      = wire.NewSet(NewSecuritiesDaoPGDB, wire.Bind(new(SecuritiesDao), new(*SecuritiesDaoPGDB)))
	SecurityListingDaoWireSet = wire.NewSet(NewSecurityListingsDaoPGDB, wire.Bind(new(SecurityListingsDao), new(*SecurityListingsDaoPGDB)))
	HistoricalPriceDaoWireSet = wire.NewSet(NewHistoricalPricesDaoPGDB, wire.Bind(new(HistoricalPricesDao), new(*HistoricalPricesDaoPGDB)))
)

type SecuritiesDao interface {
	Create(ctx context.Context, security *catalogPb.Security) (*catalogPb.Security, error)
	Update(ctx context.Context, security *catalogPb.Security, updateMask []catalogPb.SecurityFieldMask) (*catalogPb.Security, error)
	GetById(ctx context.Context, id string, fieldMask []catalogPb.SecurityFieldMask) (*catalogPb.Security, error)
	GetByVendorSecurityId(ctx context.Context, vendor vendorgateway.Vendor, vendorSecurityId string, fieldMask []catalogPb.SecurityFieldMask) (*catalogPb.Security, error)
	// BulkGet fetches multiple securities for given ids
	// fetches and populate only the fields given in the field mask
	// It is caller responsibility to check if response is present for given id
	// returns epifierrors.ErrRecordNotFound if no row is present
	BulkGet(ctx context.Context, ids []string, fieldMask []catalogPb.SecurityFieldMask) (map[string]*catalogPb.Security, error)
}

type SecurityListingsDao interface {
	// BatchUpsert upserts a batch of SecurityListing protos
	BatchUpsert(ctx context.Context, listings []*catalogPb.SecurityListing, fieldMasks []catalogPb.SecurityListingFieldMask) error
	// GetByExternalId fetches a SecurityListing by its external_id
	GetByExternalId(ctx context.Context, externalId string, fieldMask []catalogPb.SecurityListingFieldMask) (*catalogPb.SecurityListing, error)
	// GetBySymbolExchange fetches a SecurityListing by symbol and exchange
	GetBySymbolExchange(ctx context.Context, symbol string, exchange catalogPb.Exchange, fieldMask []catalogPb.SecurityListingFieldMask) (*catalogPb.SecurityListing, error)
	// GetByISINAndExchange GetByISIN fetches a SecurityListings by its ISIN and Exchange pairs
	GetByISINAndExchange(ctx context.Context, pairs []*catalogPb.ISINExchangePair, fieldMask []catalogPb.SecurityListingFieldMask) ([]*catalogPb.SecurityListing, error)
	// GetByExternalIds fetches SecurityListings by their external_ids in bulk
	GetByExternalIds(ctx context.Context, externalIds []string, fieldMask []catalogPb.SecurityListingFieldMask) ([]*catalogPb.SecurityListing, error)
	// GetByExchangeSymbols fetches SecurityListings by their exchange symbols in bulk
	GetByExchangeSymbols(ctx context.Context, exchangeSymbols []*catalogPb.ExchangeSymbol, fieldMask []catalogPb.SecurityListingFieldMask) ([]*catalogPb.SecurityListing, error)
	// Update updates SecurityListings by their vendor and vendor listing id
	// Caution: Sending an empty array will update the ids as well
	UpdateByVendorListingId(ctx context.Context, listing *catalogPb.SecurityListing, updateMask []catalogPb.SecurityListingFieldMask) error
	// GetByVendorListingId fetches SecurityListing by their vendor listing id
	GetByVendorListingId(ctx context.Context, vendor vendorgateway.Vendor, vendorListingId string, fieldMask []catalogPb.SecurityListingFieldMask) (*catalogPb.SecurityListing, error)
}

type HistoricalPricesDao interface {
	// BatchUpsert upserts a batch of HistoricalPrice protos
	BatchUpsert(ctx context.Context, prices []*catalogPb.HistoricalPrice) error
	// GetBySecuritiesAndDates fetches HistoricalPrice records for the given securityIds and priceDates.
	// Returns a slice of matching HistoricalPrice protos; fields returned are controlled by fieldMask.
	GetBySecuritiesAndDates(ctx context.Context, securityIds []string, priceDates []*datePb.Date, fieldMask []catalogPb.HistoricalPriceFieldMask) ([]*catalogPb.HistoricalPrice, error)
}
