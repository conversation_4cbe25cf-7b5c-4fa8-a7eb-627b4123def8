package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"

	catalogPb "github.com/epifi/gamma/api/securities/catalog"

	"github.com/epifi/gamma/securities/catalog/dao/model"

	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/epifi/be-common/api/vendorgateway"
)

type SecurityListingsDaoPGDB struct {
	db *gorm.DB
}

var (
	securityListingFieldMaskToColumnMap = map[catalogPb.SecurityListingFieldMask]string{
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID:        "internal_id",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID:        "external_id",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SECURITY_ID:        "security_id",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE:           "exchange",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL:             "symbol",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_IS_PRIMARY_LISTING: "is_primary_listing",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_STATUS:             "status",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_FINANCIAL_INFO:     "financial_info",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN:               "isin",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR:             "vendor",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID:  "vendor_listing_id",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_CREATED_AT:         "created_at",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_UPDATED_AT:         "updated_at",
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_DELETED_AT:         "deleted_at_unix",
	}
)

func NewSecurityListingsDaoPGDB(db *gorm.DB) *SecurityListingsDaoPGDB {
	return &SecurityListingsDaoPGDB{db: db}
}

func (s *SecurityListingsDaoPGDB) BatchUpsert(ctx context.Context, listings []*catalogPb.SecurityListing, fieldMasks []catalogPb.SecurityListingFieldMask) error {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecurityListingsDaoPGDB", "BatchUpsert", time.Now())
	if len(listings) == 0 || len(listings) > 1000 {
		return fmt.Errorf("invalid request length %v", len(listings))
	}

	models := make([]*model.SecurityListing, 0, len(listings))
	for _, proto := range listings {
		if err := s.validateRequest(proto); err != nil {
			return fmt.Errorf("failed to validate security listing: %w", err)
		}
		if proto.GetInternalId() == "" {
			proto.InternalId = "SL" + idgen.RandAlphaNumericString(5)
		}
		models = append(models, model.NewSecurityListing(proto))
	}

	cols, err := s.convertFieldMaskToColumnsUpsert(fieldMasks)
	if err != nil {
		return fmt.Errorf("failed to get col names from field mask: %w", err)
	}

	conflictColumns := []clause.Column{
		{Name: securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR]},
		{Name: securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID]},
		{Name: securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_DELETED_AT]},
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	resp := db.Clauses(
		clause.OnConflict{
			Columns:   conflictColumns,
			DoUpdates: clause.AssignmentColumns(cols),
		},
	).Create(models)

	if resp.Error != nil {
		return fmt.Errorf("unable to bulk upsert DB entries for security listings: %w", resp.Error)
	}

	return nil
}

func (s *SecurityListingsDaoPGDB) validateRequest(proto *catalogPb.SecurityListing) error {
	if proto.GetExternalId() == "" {
		return fmt.Errorf("external_id cannot be empty")
	}
	if proto.GetSecurityId() == "" {
		return fmt.Errorf("security_id cannot be empty")
	}
	if proto.GetExchange() == catalogPb.Exchange_EXCHANGE_UNSPECIFIED {
		return fmt.Errorf("exchange cannot be EXCHANGE_UNSPECIFIED")
	}
	if proto.GetSymbol() == "" {
		return fmt.Errorf("symbol cannot be empty")
	}
	if proto.GetStatus() == catalogPb.ListingStatus_LISTING_STATUS_UNSPECIFIED {
		return fmt.Errorf("status cannot be LISTING_STATUS_UNSPECIFIED")
	}
	if proto.GetVendor() == vendorgateway.Vendor_VENDOR_UNSPECIFIED {
		return fmt.Errorf("vendor cannot be VENDOR_UNSPECIFIED")
	}
	if proto.GetVendorListingId() == "" {
		return fmt.Errorf("vendor_listing_id cannot be empty")
	}
	return nil
}

// convertFieldMaskToColumns converts a field mask to DB column names using the map
func (s *SecurityListingsDaoPGDB) convertFieldMaskToColumns(fieldMask []catalogPb.SecurityListingFieldMask) ([]string, error) {
	if len(fieldMask) == 0 {
		cols := make([]string, 0, len(securityListingFieldMaskToColumnMap))
		for _, col := range securityListingFieldMaskToColumnMap {
			cols = append(cols, col)
		}
		return lo.Uniq(cols), nil
	}
	cols := make([]string, 0, len(fieldMask))
	for _, mask := range fieldMask {
		col, ok := securityListingFieldMaskToColumnMap[mask]
		if !ok {
			return nil, fmt.Errorf("unknown field mask: %v", mask)
		}
		cols = append(cols, col)
	}
	return lo.Uniq(cols), nil
}

// convertFieldMaskToColumnsUpsert converts a field mask to DB column names for BatchUpsert
func (s *SecurityListingsDaoPGDB) convertFieldMaskToColumnsUpsert(fieldMask []catalogPb.SecurityListingFieldMask) ([]string, error) {
	if len(fieldMask) == 0 {
		return []string{
			securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SECURITY_ID],
			securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE],
			securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL],
			securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_IS_PRIMARY_LISTING],
			securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_STATUS],
			securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_FINANCIAL_INFO],
			securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR],
			securityListingFieldMaskToColumnMap[catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID],
		}, nil
	}
	cols := make([]string, 0, len(fieldMask))
	for _, mask := range fieldMask {
		col, ok := securityListingFieldMaskToColumnMap[mask]
		if !ok {
			return nil, fmt.Errorf("unknown field mask: %v", mask)
		}
		cols = append(cols, col)
	}
	return lo.Uniq(cols), nil
}

// GetByExternalId fetches a SecurityListing by its external_id
func (s *SecurityListingsDaoPGDB) GetByExternalId(ctx context.Context, externalId string, fieldMask []catalogPb.SecurityListingFieldMask) (*catalogPb.SecurityListing, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecurityListingsDaoPGDB", "GetByExternalId", time.Now())
	var m model.SecurityListing

	if externalId == "" {
		return nil, fmt.Errorf("external_id cannot be empty")
	}

	cols, err := s.convertFieldMaskToColumns(fieldMask)
	if err != nil {
		return nil, fmt.Errorf("failed to get col names from field mask: %w", err)
	}

	query := s.db.WithContext(ctx).Where("external_id = ?", externalId)
	if len(cols) > 0 {
		query = query.Select(cols)
	}
	dbErr := query.Take(&m).Error
	if errors.Is(dbErr, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("record not found for external_id %s: %w", externalId, epifierrors.ErrRecordNotFound)
	}
	if dbErr != nil {
		return nil, fmt.Errorf("failed to get SecurityListing: %w", dbErr)
	}
	return m.ToProto(), nil
}

func (s *SecurityListingsDaoPGDB) GetBySymbolExchange(ctx context.Context, symbol string, exchange catalogPb.Exchange, fieldMask []catalogPb.SecurityListingFieldMask) (*catalogPb.SecurityListing, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecurityListingsDaoPGDB", "GetBySymbolExchange", time.Now())
	var m model.SecurityListing

	if symbol == "" || exchange == catalogPb.Exchange_EXCHANGE_UNSPECIFIED {
		return nil, fmt.Errorf("symbol cannot empty and exchange cannot be unspecified")
	}
	cols, err := s.convertFieldMaskToColumns(fieldMask)
	if err != nil {
		return nil, fmt.Errorf("failed to get col names from field mask: %w", err)
	}

	query := s.db.WithContext(ctx).Where("symbol = ? AND exchange = ?", symbol, exchange)
	if len(cols) > 0 {
		query = query.Select(cols)
	}

	dbErr := query.Take(&m).Error
	if errors.Is(dbErr, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("record not found for symbol %s and exchange %s: %w", symbol, exchange.String(), epifierrors.ErrRecordNotFound)
	}
	if dbErr != nil {
		return nil, fmt.Errorf("failed to get SecurityListing by symbol %s and exchange %s: %w", symbol, exchange.String(), dbErr)
	}
	return m.ToProto(), nil
}

func (s *SecurityListingsDaoPGDB) GetByISINAndExchange(ctx context.Context, pairs []*catalogPb.ISINExchangePair, fieldMask []catalogPb.SecurityListingFieldMask) ([]*catalogPb.SecurityListing, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecurityListingsDaoPGDB", "GetByISINAndExchange", time.Now())

	if len(pairs) == 0 {
		return nil, fmt.Errorf("pairs cannot be empty")
	}

	// Filter valid pairs
	var validPairs []*catalogPb.ISINExchangePair
	for _, pair := range pairs {
		if pair.GetIsin() == "" {
			logger.Info(ctx, "Skipping empty isin pair", zap.String("isin", pair.GetIsin()), zap.String("exchange", pair.GetExchange().String()))
			continue
		}
		if pair.GetExchange() == catalogPb.Exchange_EXCHANGE_UNSPECIFIED {
			logger.Info(ctx, "Skipping empty isin pair", zap.String("isin", pair.GetIsin()), zap.String("exchange", pair.GetExchange().String()))
			continue
		}
		validPairs = append(validPairs, pair)
	}

	if len(validPairs) == 0 {
		return nil, fmt.Errorf("no valid isin and exchange pairs found")
	}

	cols, err := s.convertFieldMaskToColumns(fieldMask)
	if err != nil {
		return nil, fmt.Errorf("failed to get col names from field mask: %w", err)
	}

	var models []model.SecurityListing
	query := s.db.WithContext(ctx)

	// Build the IN clause for multiple ISIN-exchange pairs
	var conditions [][]interface{}
	for _, p := range pairs {
		conditions = append(conditions, []interface{}{p.GetIsin(), p.GetExchange()})
	}

	// Use the IN clause with multiple columns (isin, exchange)
	query = query.Where("(isin, exchange) IN ?", conditions)
	if len(cols) > 0 {
		query = query.Select(cols)
	}
	err = query.Find(&models).Error
	if err != nil {
		return nil, err
	}

	// Convert models to protos
	results := make([]*catalogPb.SecurityListing, 0, len(models))
	for _, m := range models {
		results = append(results, m.ToProto())
	}

	return results, nil
}

// GetByExternalIds fetches SecurityListings by their external_ids in bulk
func (s *SecurityListingsDaoPGDB) GetByExternalIds(ctx context.Context, externalIds []string, fieldMask []catalogPb.SecurityListingFieldMask) ([]*catalogPb.SecurityListing, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecurityListingsDaoPGDB", "GetByExternalIds", time.Now())

	if len(externalIds) == 0 {
		return nil, fmt.Errorf("externalIds cannot be empty")
	}

	// Filter out empty external IDs
	var validExternalIds []string
	for _, id := range externalIds {
		if id != "" {
			validExternalIds = append(validExternalIds, id)
		}
	}

	if len(validExternalIds) == 0 {
		return nil, fmt.Errorf("no valid external IDs found")
	}

	// since query is done with external_id include it too
	fieldMask = append(fieldMask, catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID)
	cols, err := s.convertFieldMaskToColumns(fieldMask)
	if err != nil {
		return nil, fmt.Errorf("failed to get col names from field mask: %w", err)
	}

	var models []model.SecurityListing
	query := s.db.WithContext(ctx).Where("external_id IN ?", validExternalIds)
	if len(cols) > 0 {
		query = query.Select(cols)
	}
	err = query.Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get SecurityListings by external IDs: %w", err)
	}

	// Convert models to protos
	results := make([]*catalogPb.SecurityListing, 0, len(models))
	for _, m := range models {
		results = append(results, m.ToProto())
	}

	return results, nil
}

// GetByExchangeSymbols fetches SecurityListings by their exchange symbols in bulk
func (s *SecurityListingsDaoPGDB) GetByExchangeSymbols(ctx context.Context, exchangeSymbols []*catalogPb.ExchangeSymbol, fieldMask []catalogPb.SecurityListingFieldMask) ([]*catalogPb.SecurityListing, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecurityListingsDaoPGDB", "GetByExchangeSymbols", time.Now())

	if len(exchangeSymbols) == 0 {
		return nil, fmt.Errorf("exchangeSymbols cannot be empty")
	}

	// Filter valid exchange symbols
	var validSymbols []*catalogPb.ExchangeSymbol
	for _, symbol := range exchangeSymbols {
		if symbol.GetSymbol() != "" && symbol.GetExchange() != catalogPb.Exchange_EXCHANGE_UNSPECIFIED {
			validSymbols = append(validSymbols, symbol)
		} else {
			logger.Info(ctx, "Skipping invalid exchange symbol",
				zap.String("symbol", symbol.GetSymbol()),
				zap.String("exchange", symbol.GetExchange().String()))
		}
	}

	if len(validSymbols) == 0 {
		return nil, fmt.Errorf("no valid exchange symbols found")
	}

	// since query is done with symbol and exchange, include them too
	fieldMask = append(fieldMask, catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE)
	cols, err := s.convertFieldMaskToColumns(fieldMask)
	if err != nil {
		return nil, fmt.Errorf("failed to get col names from field mask: %w", err)
	}

	var models []model.SecurityListing
	query := s.db.WithContext(ctx)

	// Build the IN clause for multiple symbol-exchange pairs
	var conditions [][]interface{}
	for _, symbol := range validSymbols {
		conditions = append(conditions, []interface{}{symbol.GetSymbol(), symbol.GetExchange()})
	}

	// Use the IN clause with multiple columns (symbol, exchange)
	query = query.Where("(symbol, exchange) IN ?", conditions)
	if len(cols) > 0 {
		query = query.Select(cols)
	}
	err = query.Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get SecurityListings by exchange symbols: %w", err)
	}

	// Convert models to protos
	results := make([]*catalogPb.SecurityListing, 0, len(models))
	for _, m := range models {
		results = append(results, m.ToProto())
	}

	return results, nil
}

func (s *SecurityListingsDaoPGDB) UpdateByVendorListingId(ctx context.Context, listing *catalogPb.SecurityListing, updateMask []catalogPb.SecurityListingFieldMask) error {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecurityListingsDaoPGDB", "UpdateByVendorListingId", time.Now())

	if listing.GetVendor() == vendorgateway.Vendor_VENDOR_UNSPECIFIED {
		return fmt.Errorf("vendor cannot be unspecified")
	}
	if listing.GetVendorListingId() == "" {
		return fmt.Errorf("vendor listing id cannot be empty for security listing update operation")
	}

	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	cols, err := s.convertFieldMaskToColumns(updateMask)
	if err != nil || len(cols) == 0 {
		return fmt.Errorf("failed to get column names from field mask: %w", err)
	}
	securityListingModel := model.NewSecurityListing(listing)

	result := db.Model(&model.SecurityListing{}).
		Where("vendor = ? and vendor_listing_id = ?", listing.GetVendor(), listing.GetVendorListingId()).
		Select(cols).
		Updates(securityListingModel)

	if err = result.Error; err != nil {
		return fmt.Errorf("unable to update security listing model for vendor: %s and vendor listing id: %s, %w", listing.GetVendor(), listing.GetVendorListingId(), err)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("no security listing found for vendor: %s and vendor listing id: %s, %w", listing.GetVendor(), listing.GetVendorListingId(), epifierrors.ErrNoRowsAffected)
	}

	return nil
}

func (s *SecurityListingsDaoPGDB) GetByVendorListingId(ctx context.Context, vendor vendorgateway.Vendor, vendorListingId string, fieldMask []catalogPb.SecurityListingFieldMask) (*catalogPb.SecurityListing, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecurityListingsDaoPGDB", "GetByVendorListingId", time.Now())

	if vendorListingId == "" {
		return nil, fmt.Errorf("vendorListingId cannot be empty for get operation")
	}

	if vendor == vendorgateway.Vendor_VENDOR_UNSPECIFIED {
		return nil, fmt.Errorf("vendor cannot be unspecified for get operation")
	}

	cols, err := s.convertFieldMaskToColumns(fieldMask)
	if err != nil {
		return nil, fmt.Errorf("failed to get col names from field mask: %w", err)
	}

	securityListingModel := &model.SecurityListing{}

	query := s.db.WithContext(ctx).Where("vendor = ? and vendor_listing_id = ?", vendor, vendorListingId)
	if len(cols) > 0 {
		query = query.Select(cols)
	}
	dbErr := query.Take(&securityListingModel).Error
	if errors.Is(dbErr, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("record not found for vendor: %s and vendor_listing_id: %s, %w", vendor.String(), vendorListingId, epifierrors.ErrRecordNotFound)
	}
	if dbErr != nil {
		return nil, fmt.Errorf("failed to get SecurityListing: %w", dbErr)
	}

	return securityListingModel.ToProto(), nil
}
