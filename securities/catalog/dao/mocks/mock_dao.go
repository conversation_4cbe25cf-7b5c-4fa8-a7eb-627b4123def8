// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mock_dao is a generated GoMock package.
package mock_dao

import (
	context "context"
	reflect "reflect"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	catalog "github.com/epifi/gamma/api/securities/catalog"
	gomock "github.com/golang/mock/gomock"
	date "google.golang.org/genproto/googleapis/type/date"
)

// MockSecuritiesDao is a mock of SecuritiesDao interface.
type MockSecuritiesDao struct {
	ctrl     *gomock.Controller
	recorder *MockSecuritiesDaoMockRecorder
}

// MockSecuritiesDaoMockRecorder is the mock recorder for MockSecuritiesDao.
type MockSecuritiesDaoMockRecorder struct {
	mock *MockSecuritiesDao
}

// NewMockSecuritiesDao creates a new mock instance.
func NewMockSecuritiesDao(ctrl *gomock.Controller) *MockSecuritiesDao {
	mock := &MockSecuritiesDao{ctrl: ctrl}
	mock.recorder = &MockSecuritiesDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSecuritiesDao) EXPECT() *MockSecuritiesDaoMockRecorder {
	return m.recorder
}

// BulkGet mocks base method.
func (m *MockSecuritiesDao) BulkGet(ctx context.Context, ids []string, fieldMask []catalog.SecurityFieldMask) (map[string]*catalog.Security, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkGet", ctx, ids, fieldMask)
	ret0, _ := ret[0].(map[string]*catalog.Security)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkGet indicates an expected call of BulkGet.
func (mr *MockSecuritiesDaoMockRecorder) BulkGet(ctx, ids, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkGet", reflect.TypeOf((*MockSecuritiesDao)(nil).BulkGet), ctx, ids, fieldMask)
}

// Create mocks base method.
func (m *MockSecuritiesDao) Create(ctx context.Context, security *catalog.Security) (*catalog.Security, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, security)
	ret0, _ := ret[0].(*catalog.Security)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockSecuritiesDaoMockRecorder) Create(ctx, security interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockSecuritiesDao)(nil).Create), ctx, security)
}

// GetById mocks base method.
func (m *MockSecuritiesDao) GetById(ctx context.Context, id string, fieldMask []catalog.SecurityFieldMask) (*catalog.Security, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id, fieldMask)
	ret0, _ := ret[0].(*catalog.Security)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockSecuritiesDaoMockRecorder) GetById(ctx, id, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockSecuritiesDao)(nil).GetById), ctx, id, fieldMask)
}

// GetByVendorSecurityId mocks base method.
func (m *MockSecuritiesDao) GetByVendorSecurityId(ctx context.Context, vendor vendorgateway.Vendor, vendorSecurityId string, fieldMask []catalog.SecurityFieldMask) (*catalog.Security, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorSecurityId", ctx, vendor, vendorSecurityId, fieldMask)
	ret0, _ := ret[0].(*catalog.Security)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorSecurityId indicates an expected call of GetByVendorSecurityId.
func (mr *MockSecuritiesDaoMockRecorder) GetByVendorSecurityId(ctx, vendor, vendorSecurityId, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorSecurityId", reflect.TypeOf((*MockSecuritiesDao)(nil).GetByVendorSecurityId), ctx, vendor, vendorSecurityId, fieldMask)
}

// Update mocks base method.
func (m *MockSecuritiesDao) Update(ctx context.Context, security *catalog.Security, updateMask []catalog.SecurityFieldMask) (*catalog.Security, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, security, updateMask)
	ret0, _ := ret[0].(*catalog.Security)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockSecuritiesDaoMockRecorder) Update(ctx, security, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockSecuritiesDao)(nil).Update), ctx, security, updateMask)
}

// MockSecurityListingsDao is a mock of SecurityListingsDao interface.
type MockSecurityListingsDao struct {
	ctrl     *gomock.Controller
	recorder *MockSecurityListingsDaoMockRecorder
}

// MockSecurityListingsDaoMockRecorder is the mock recorder for MockSecurityListingsDao.
type MockSecurityListingsDaoMockRecorder struct {
	mock *MockSecurityListingsDao
}

// NewMockSecurityListingsDao creates a new mock instance.
func NewMockSecurityListingsDao(ctrl *gomock.Controller) *MockSecurityListingsDao {
	mock := &MockSecurityListingsDao{ctrl: ctrl}
	mock.recorder = &MockSecurityListingsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSecurityListingsDao) EXPECT() *MockSecurityListingsDaoMockRecorder {
	return m.recorder
}

// BatchUpsert mocks base method.
func (m *MockSecurityListingsDao) BatchUpsert(ctx context.Context, listings []*catalog.SecurityListing, fieldMasks []catalog.SecurityListingFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpsert", ctx, listings, fieldMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpsert indicates an expected call of BatchUpsert.
func (mr *MockSecurityListingsDaoMockRecorder) BatchUpsert(ctx, listings, fieldMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpsert", reflect.TypeOf((*MockSecurityListingsDao)(nil).BatchUpsert), ctx, listings, fieldMasks)
}

// GetByExchangeSymbols mocks base method.
func (m *MockSecurityListingsDao) GetByExchangeSymbols(ctx context.Context, exchangeSymbols []*catalog.ExchangeSymbol, fieldMask []catalog.SecurityListingFieldMask) ([]*catalog.SecurityListing, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByExchangeSymbols", ctx, exchangeSymbols, fieldMask)
	ret0, _ := ret[0].([]*catalog.SecurityListing)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByExchangeSymbols indicates an expected call of GetByExchangeSymbols.
func (mr *MockSecurityListingsDaoMockRecorder) GetByExchangeSymbols(ctx, exchangeSymbols, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByExchangeSymbols", reflect.TypeOf((*MockSecurityListingsDao)(nil).GetByExchangeSymbols), ctx, exchangeSymbols, fieldMask)
}

// GetByExternalId mocks base method.
func (m *MockSecurityListingsDao) GetByExternalId(ctx context.Context, externalId string, fieldMask []catalog.SecurityListingFieldMask) (*catalog.SecurityListing, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByExternalId", ctx, externalId, fieldMask)
	ret0, _ := ret[0].(*catalog.SecurityListing)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByExternalId indicates an expected call of GetByExternalId.
func (mr *MockSecurityListingsDaoMockRecorder) GetByExternalId(ctx, externalId, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByExternalId", reflect.TypeOf((*MockSecurityListingsDao)(nil).GetByExternalId), ctx, externalId, fieldMask)
}

// GetByExternalIds mocks base method.
func (m *MockSecurityListingsDao) GetByExternalIds(ctx context.Context, externalIds []string, fieldMask []catalog.SecurityListingFieldMask) ([]*catalog.SecurityListing, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByExternalIds", ctx, externalIds, fieldMask)
	ret0, _ := ret[0].([]*catalog.SecurityListing)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByExternalIds indicates an expected call of GetByExternalIds.
func (mr *MockSecurityListingsDaoMockRecorder) GetByExternalIds(ctx, externalIds, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByExternalIds", reflect.TypeOf((*MockSecurityListingsDao)(nil).GetByExternalIds), ctx, externalIds, fieldMask)
}

// GetByISINAndExchange mocks base method.
func (m *MockSecurityListingsDao) GetByISINAndExchange(ctx context.Context, pairs []*catalog.ISINExchangePair, fieldMask []catalog.SecurityListingFieldMask) ([]*catalog.SecurityListing, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByISINAndExchange", ctx, pairs, fieldMask)
	ret0, _ := ret[0].([]*catalog.SecurityListing)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByISINAndExchange indicates an expected call of GetByISINAndExchange.
func (mr *MockSecurityListingsDaoMockRecorder) GetByISINAndExchange(ctx, pairs, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByISINAndExchange", reflect.TypeOf((*MockSecurityListingsDao)(nil).GetByISINAndExchange), ctx, pairs, fieldMask)
}

// GetBySymbolExchange mocks base method.
func (m *MockSecurityListingsDao) GetBySymbolExchange(ctx context.Context, symbol string, exchange catalog.Exchange, fieldMask []catalog.SecurityListingFieldMask) (*catalog.SecurityListing, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBySymbolExchange", ctx, symbol, exchange, fieldMask)
	ret0, _ := ret[0].(*catalog.SecurityListing)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBySymbolExchange indicates an expected call of GetBySymbolExchange.
func (mr *MockSecurityListingsDaoMockRecorder) GetBySymbolExchange(ctx, symbol, exchange, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBySymbolExchange", reflect.TypeOf((*MockSecurityListingsDao)(nil).GetBySymbolExchange), ctx, symbol, exchange, fieldMask)
}

// GetByVendorListingId mocks base method.
func (m *MockSecurityListingsDao) GetByVendorListingId(ctx context.Context, vendor vendorgateway.Vendor, vendorSecurityId string, fieldMask []catalog.SecurityListingFieldMask) (*catalog.SecurityListing, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorListingId", ctx, vendor, vendorSecurityId, fieldMask)
	ret0, _ := ret[0].(*catalog.SecurityListing)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorListingId indicates an expected call of GetByVendorListingId.
func (mr *MockSecurityListingsDaoMockRecorder) GetByVendorListingId(ctx, vendor, vendorSecurityId, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorListingId", reflect.TypeOf((*MockSecurityListingsDao)(nil).GetByVendorListingId), ctx, vendor, vendorSecurityId, fieldMask)
}

// UpdateByVendorListingId mocks base method.
func (m *MockSecurityListingsDao) UpdateByVendorListingId(ctx context.Context, listing *catalog.SecurityListing, updateMask []catalog.SecurityListingFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByVendorListingId", ctx, listing, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateByVendorListingId indicates an expected call of UpdateByVendorListingId.
func (mr *MockSecurityListingsDaoMockRecorder) UpdateByVendorListingId(ctx, listing, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByVendorListingId", reflect.TypeOf((*MockSecurityListingsDao)(nil).UpdateByVendorListingId), ctx, listing, updateMask)
}

// MockHistoricalPricesDao is a mock of HistoricalPricesDao interface.
type MockHistoricalPricesDao struct {
	ctrl     *gomock.Controller
	recorder *MockHistoricalPricesDaoMockRecorder
}

// MockHistoricalPricesDaoMockRecorder is the mock recorder for MockHistoricalPricesDao.
type MockHistoricalPricesDaoMockRecorder struct {
	mock *MockHistoricalPricesDao
}

// NewMockHistoricalPricesDao creates a new mock instance.
func NewMockHistoricalPricesDao(ctrl *gomock.Controller) *MockHistoricalPricesDao {
	mock := &MockHistoricalPricesDao{ctrl: ctrl}
	mock.recorder = &MockHistoricalPricesDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHistoricalPricesDao) EXPECT() *MockHistoricalPricesDaoMockRecorder {
	return m.recorder
}

// BatchUpsert mocks base method.
func (m *MockHistoricalPricesDao) BatchUpsert(ctx context.Context, prices []*catalog.HistoricalPrice) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpsert", ctx, prices)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpsert indicates an expected call of BatchUpsert.
func (mr *MockHistoricalPricesDaoMockRecorder) BatchUpsert(ctx, prices interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpsert", reflect.TypeOf((*MockHistoricalPricesDao)(nil).BatchUpsert), ctx, prices)
}

// GetBySecuritiesAndDates mocks base method.
func (m *MockHistoricalPricesDao) GetBySecuritiesAndDates(ctx context.Context, securityIds []string, priceDates []*date.Date, fieldMask []catalog.HistoricalPriceFieldMask) ([]*catalog.HistoricalPrice, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBySecuritiesAndDates", ctx, securityIds, priceDates, fieldMask)
	ret0, _ := ret[0].([]*catalog.HistoricalPrice)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBySecuritiesAndDates indicates an expected call of GetBySecuritiesAndDates.
func (mr *MockHistoricalPricesDaoMockRecorder) GetBySecuritiesAndDates(ctx, securityIds, priceDates, fieldMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBySecuritiesAndDates", reflect.TypeOf((*MockHistoricalPricesDao)(nil).GetBySecuritiesAndDates), ctx, securityIds, priceDates, fieldMask)
}
