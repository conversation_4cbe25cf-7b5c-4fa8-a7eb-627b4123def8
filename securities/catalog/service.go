package catalog

import (
	"context"

	"google.golang.org/genproto/googleapis/type/money"

	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	ussCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/securities/catalog/ingester"

	"github.com/pkg/errors"

	"github.com/epifi/gamma/securities/catalog/historicalpricesfetcher"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	rpcPb "github.com/epifi/be-common/api/rpc"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"

	"github.com/epifi/be-common/pkg/epifierrors"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	"github.com/epifi/gamma/securities/catalog/dao"
	genConf "github.com/epifi/gamma/securities/config/genconf"
	wireTypes "github.com/epifi/gamma/securities/wire/types"
)

type Service struct {
	conf                         *genConf.Config
	vgCatalogClient              vgCatalogPb.CatalogClient
	stockCatalogRefreshPublisher wireTypes.StockCatalogRefreshPublisher
	idempotentTxnExecutor        storagev2.IdempotentTxnExecutor
	securityListingsDao          dao.SecurityListingsDao
	securitiesDao                dao.SecuritiesDao
	historicalPricesFetcher      historicalpricesfetcher.HistoricalPricesFetcher
	ussCatalogClient             ussCatalogPb.CatalogManagerClient
	catalogDataIngester          ingester.CatalogDataIngester
}

func NewService(
	conf *genConf.Config,
	vgCatalogClient vgCatalogPb.CatalogClient,
	stockCatalogRefreshPublisher wireTypes.StockCatalogRefreshPublisher,
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor,
	securityListingsDao dao.SecurityListingsDao,
	securitiesDao dao.SecuritiesDao,
	historicalPricesFetcher historicalpricesfetcher.HistoricalPricesCache,
	ussCatalogClient ussCatalogPb.CatalogManagerClient,
	catalogDataIngester ingester.CatalogDataIngester,
) *Service {
	return &Service{
		conf:                         conf,
		vgCatalogClient:              vgCatalogClient,
		stockCatalogRefreshPublisher: stockCatalogRefreshPublisher,
		idempotentTxnExecutor:        idempotentTxnExecutor,
		securityListingsDao:          securityListingsDao,
		securitiesDao:                securitiesDao,
		historicalPricesFetcher:      historicalPricesFetcher,
		ussCatalogClient:             ussCatalogClient,
		catalogDataIngester:          catalogDataIngester,
	}
}

// GetPriceByDateAndSecListingIDs retrieves historical price data for securities on a specified date.
// This includes closing prices, vendor information, and relevant timestamps.
func (s *Service) GetPriceByDateAndSecListingIDs(ctx context.Context, req *catalogPb.GetPriceByDateAndSecListingIDsRequest) (*catalogPb.GetPriceByDateAndSecListingIDsResponse, error) {
	if len(req.GetSecurityListingIds()) == 0 {
		logger.Error(ctx, "no security listing ids found")
		return &catalogPb.GetPriceByDateAndSecListingIDsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Security listing IDs are empty"),
		}, nil
	}
	if err := datetimePkg.ValidateDate(req.PriceDate); err != nil {
		logger.Error(ctx, "no price date found")
		return &catalogPb.GetPriceByDateAndSecListingIDsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("price date is nil"),
		}, nil
	}

	// Get historical prices using the fetcher
	priceResults, err := s.historicalPricesFetcher.GetPrices(ctx, req.SecurityListingIds, req.PriceDate)
	if err != nil {
		logger.Error(ctx, "Get prices failed with err: %v", zap.Error(err))
		return &catalogPb.GetPriceByDateAndSecListingIDsResponse{
			Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
		}, nil
	}

	parsedPrices := make(map[string]*money.Money)
	for _, result := range priceResults {
		parsedPrices[result.GetSecurityListingId()] = result.GetClosePrice()
	}

	return &catalogPb.GetPriceByDateAndSecListingIDsResponse{
		Status: rpcPb.StatusOk(),
		Prices: parsedPrices,
	}, nil
}

// GetSecListingIdsByISINs retrieves security listing IDs for given ISINs and exchanges.
// This helps in mapping ISINs to their specific exchange listing IDs.
func (s *Service) GetSecListingIdsByISINs(ctx context.Context, req *catalogPb.GetSecListingIdsByISINsRequest) (*catalogPb.GetSecListingIdsByISINsResponse, error) {
	if len(req.GetIsinExchangePairs()) == 0 {
		logger.Error(ctx, "GetSecListingIdByISIN request has no isin pairs in the request")
		return &catalogPb.GetSecListingIdsByISINsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ISIN exchange pairs are empty"),
		}, nil
	}

	// Only request the security listing ID field
	fieldMask := []catalogPb.SecurityListingFieldMask{
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
	}

	expandedPairs := populateExchangeForMissingISINs(req.GetIsinExchangePairs())

	// Get all listings in bulk
	listings, err := s.securityListingsDao.GetByISINAndExchange(ctx, expandedPairs, fieldMask)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "Record Not Found for isin and exchange pairs")
			return &catalogPb.GetSecListingIdsByISINsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "Error in fetching for isin and exchange pairs", zap.Error(err))
		return &catalogPb.GetSecListingIdsByISINsResponse{
			Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
		}, nil
	}

	// If no listings were found, return record not found
	if len(listings) == 0 {
		logger.Error(ctx, "Record Not Found for isin and exchange pairs")
		return &catalogPb.GetSecListingIdsByISINsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	// Check if any isin is missing in catalog and add call AddSecurityWithISINs
	expandedListings, expandedListingsErr := s.findAndIngestMissingISINs(ctx, req.GetIsinExchangePairs(), listings)
	if expandedListingsErr != nil {
		logger.Error(ctx, "Error in ingesting missing ISINs", zap.Error(expandedListingsErr))
		return &catalogPb.GetSecListingIdsByISINsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if len(expandedListings) > 0 {
		listings = append(listings, expandedListings...)
	}

	// Convert listings directly to map
	var secListIdsWithIsins []*catalogPb.SecListingIdWithISIN
	// Keep track of which ISINs we've already processed to handle BSE priority
	// this is to handle the case where an ISIN is present in both BSE and NSE
	processedIsins := make(map[string]bool)

	// First pass: process BSE listings
	for _, listing := range listings {
		if listing.GetExchange() == catalogPb.Exchange_EXCHANGE_INDIA_BSE {
			secListIdsWithIsins = append(secListIdsWithIsins, &catalogPb.SecListingIdWithISIN{
				IsinExchangePair: &catalogPb.ISINExchangePair{
					Isin:     listing.GetIsin(),
					Exchange: listing.GetExchange(),
				},
				SecurityListingExternalId: listing.GetExternalId(),
			})
			processedIsins[listing.GetIsin()] = true
		}
	}

	// Second pass: process NSE listings only for ISINs not found in BSE
	for _, listing := range listings {
		if listing.GetExchange() == catalogPb.Exchange_EXCHANGE_INDIA_NSE && !processedIsins[listing.GetIsin()] {
			secListIdsWithIsins = append(secListIdsWithIsins, &catalogPb.SecListingIdWithISIN{
				IsinExchangePair: &catalogPb.ISINExchangePair{
					Isin:     listing.GetIsin(),
					Exchange: listing.GetExchange(),
				},
				SecurityListingExternalId: listing.GetExternalId(),
			})
		}
	}

	return &catalogPb.GetSecListingIdsByISINsResponse{
		Status:                 rpcPb.StatusOk(),
		SecListingIdsWithIsins: secListIdsWithIsins,
	}, nil
}

func (s *Service) findAndIngestMissingISINs(ctx context.Context, requestISINs []*catalogPb.ISINExchangePair, responseISINs []*catalogPb.SecurityListing) ([]*catalogPb.SecurityListing, error) {
	missingISINs := missingIsinsInListings(requestISINs, responseISINs)
	if len(missingISINs) == 0 {
		return nil, nil
	}

	resp, err := s.AddSecurityWithISINs(ctx, &catalogPb.AddSecurityWithISINsRequest{
		Isins: missingISINs,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "Error in adding missing ISINs", zap.Error(rpcErr), zap.Strings("ISINs", missingISINs))
		return nil, rpcErr
	}

	var isinExchangePairs []*catalogPb.ISINExchangePair
	for _, pair := range resp.GetIsinSecurityListingPairs() {
		isinExchangePairs = append(isinExchangePairs, &catalogPb.ISINExchangePair{
			Isin:     pair.GetIsin(),
			Exchange: catalogPb.Exchange_EXCHANGE_UNSPECIFIED,
		})
	}
	expandedPairs := populateExchangeForMissingISINs(isinExchangePairs)

	listings, err := s.securityListingsDao.GetByISINAndExchange(ctx, expandedPairs, []catalogPb.SecurityListingFieldMask{
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
	})
	if err != nil {
		logger.Error(ctx, "Error in fetching for isin and exchange pairs", zap.Error(err))
		return nil, err
	}

	return listings, nil
}

func missingIsinsInListings(requestISINs []*catalogPb.ISINExchangePair, responseISINs []*catalogPb.SecurityListing) []string {
	responseIsinMap := make(map[string]struct{})
	for _, pair := range responseISINs {
		responseIsinMap[pair.GetIsin()] = struct{}{}
	}

	// Collect ISINs that are in requestISINs but not in responseIsinMap
	var missingISINs []string
	for _, pair := range requestISINs {
		if _, found := responseIsinMap[pair.GetIsin()]; !found {
			missingISINs = append(missingISINs, pair.GetIsin())
		}
	}
	return missingISINs
}

func populateExchangeForMissingISINs(isinExchangePairs []*catalogPb.ISINExchangePair) []*catalogPb.ISINExchangePair {
	// Preprocess request pairs to handle unspecified exchanges
	var expandedPairs []*catalogPb.ISINExchangePair
	for _, pair := range isinExchangePairs {
		if pair.GetExchange() == catalogPb.Exchange_EXCHANGE_UNSPECIFIED {
			// Add both BSE and NSE pairs for unspecified exchange
			expandedPairs = append(expandedPairs,
				&catalogPb.ISINExchangePair{
					Isin:     pair.GetIsin(),
					Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
				},
				&catalogPb.ISINExchangePair{
					Isin:     pair.GetIsin(),
					Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
				},
			)
		} else {
			expandedPairs = append(expandedPairs, pair)
		}
	}
	return expandedPairs
}

func (s *Service) GetSecurity(ctx context.Context, req *catalogPb.GetSecurityRequest) (*catalogPb.GetSecurityResponse, error) {
	sec, getSecurityErr := s.securitiesDao.GetById(ctx, req.GetId(), req.GetFields())
	if getSecurityErr != nil {
		if errors.Is(getSecurityErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "no security found for given id", zap.Error(getSecurityErr), zap.String(logger.ID, req.GetId()))
			return &catalogPb.GetSecurityResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error in fetching security", zap.Error(getSecurityErr), zap.String(logger.ID, req.GetId()))
		return &catalogPb.GetSecurityResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in fetching security"),
		}, nil
	}
	return &catalogPb.GetSecurityResponse{
		Status:   rpcPb.StatusOk(),
		Security: sec,
	}, nil
}

func (s *Service) GetSecurityListing(ctx context.Context, req *catalogPb.GetSecurityListingRequest) (*catalogPb.GetSecurityListingResponse, error) {
	// Validate that an identifier is provided
	if req.GetIdentifier() == nil {
		logger.Error(ctx, "GetSecurityListing request has no identifier")
		return &catalogPb.GetSecurityListingResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("identifier is required"),
		}, nil
	}

	var securityListing *catalogPb.SecurityListing
	var err error

	// Fetch security listing based on identifier type
	switch identifier := req.GetIdentifier().(type) {
	case *catalogPb.GetSecurityListingRequest_ExternalId:
		externalId := identifier.ExternalId
		if externalId == "" {
			logger.Error(ctx, "GetSecurityListing request has empty external_id")
			return &catalogPb.GetSecurityListingResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("external_id cannot be empty"),
			}, nil
		}
		securityListing, err = s.securityListingsDao.GetByExternalId(ctx, externalId, req.GetSecurityListingFields())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "Security listing not found for external_id", zap.String("external_id", externalId))
				return &catalogPb.GetSecurityListingResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}
			logger.Error(ctx, "Error fetching security listing by external_id", zap.Error(err), zap.String("external_id", externalId))
			return &catalogPb.GetSecurityListingResponse{
				Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
			}, nil
		}

	case *catalogPb.GetSecurityListingRequest_ExchangeSymbol:
		exchangeSymbol := identifier.ExchangeSymbol
		if exchangeSymbol == nil || exchangeSymbol.GetSymbol() == "" || exchangeSymbol.GetExchange() == catalogPb.Exchange_EXCHANGE_UNSPECIFIED {
			logger.Error(ctx, "GetSecurityListing request has invalid exchange_symbol")
			return &catalogPb.GetSecurityListingResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("exchange_symbol must have valid symbol and exchange"),
			}, nil
		}
		securityListing, err = s.securityListingsDao.GetBySymbolExchange(ctx, exchangeSymbol.GetSymbol(), exchangeSymbol.GetExchange(), req.GetSecurityListingFields())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "Security listing not found for symbol and exchange",
					zap.String("symbol", exchangeSymbol.GetSymbol()),
					zap.String("exchange", exchangeSymbol.GetExchange().String()))
				return &catalogPb.GetSecurityListingResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}
			logger.Error(ctx, "Error fetching security listing by symbol and exchange", zap.Error(err),
				zap.String("symbol", exchangeSymbol.GetSymbol()),
				zap.String("exchange", exchangeSymbol.GetExchange().String()))
			return &catalogPb.GetSecurityListingResponse{
				Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
			}, nil
		}

	default:
		logger.Error(ctx, "GetSecurityListing request has unknown identifier type")
		return &catalogPb.GetSecurityListingResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("unknown identifier type"),
		}, nil
	}

	// Fetch security data if security fields are requested
	if len(req.GetSecurityFields()) > 0 && securityListing.GetSecurityId() != "" {
		security, secErr := s.securitiesDao.GetById(ctx, securityListing.GetSecurityId(), req.GetSecurityFields())
		if secErr != nil {
			if errors.Is(secErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "Security not found for security_id", zap.String("security_id", securityListing.GetSecurityId()))
				return &catalogPb.GetSecurityListingResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}
			logger.Error(ctx, "Error fetching security", zap.Error(secErr), zap.String("security_id", securityListing.GetSecurityId()))
			return &catalogPb.GetSecurityListingResponse{
				Status: rpcPb.StatusFromErrorWithDefaultInternal(secErr),
			}, nil
		}
		return &catalogPb.GetSecurityListingResponse{
			Status:          rpcPb.StatusOk(),
			SecurityListing: securityListing,
			Security:        security,
		}, nil
	}

	return &catalogPb.GetSecurityListingResponse{
		Status:          rpcPb.StatusOk(),
		SecurityListing: securityListing,
	}, nil
}

func (s *Service) GetSecurities(ctx context.Context, req *catalogPb.GetSecuritiesRequest) (*catalogPb.GetSecuritiesResponse, error) {
	if len(req.GetIds()) == 0 {
		logger.Error(ctx, "no security ids found in request")
		return &catalogPb.GetSecuritiesResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("security ids are empty"),
		}, nil
	}

	securitiesMap, err := s.securitiesDao.BulkGet(ctx, req.GetIds(), req.GetFields())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "no securities found for given ids", zap.Error(err), zap.Strings("ids", req.GetIds()))
			return &catalogPb.GetSecuritiesResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error in fetching securities", zap.Error(err), zap.Strings("ids", req.GetIds()))
		return &catalogPb.GetSecuritiesResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in fetching securities"),
		}, nil
	}

	return &catalogPb.GetSecuritiesResponse{
		Status:        rpcPb.StatusOk(),
		SecuritiesMap: securitiesMap,
	}, nil
}

func (s *Service) GetSecurityListings(ctx context.Context, req *catalogPb.GetSecurityListingsRequest) (*catalogPb.GetSecurityListingsResponse, error) {
	// Validate that identifiers are provided
	if req.GetIdentifiers() == nil {
		logger.Error(ctx, "GetSecurityListings request has no identifiers")
		return &catalogPb.GetSecurityListingsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("identifiers are required"),
		}, nil
	}

	var securityListings []*catalogPb.SecurityListing
	var err error

	// Fetch security listings based on identifier type
	switch identifiers := req.GetIdentifiers().(type) {
	case *catalogPb.GetSecurityListingsRequest_ExternalIds_:
		externalIds := identifiers.ExternalIds.GetExternalIds()
		if len(externalIds) == 0 {
			logger.Info(ctx, "GetSecurityListings request has empty external_ids")
			return &catalogPb.GetSecurityListingsResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("external_ids cannot be empty"),
			}, nil
		}
		securityListings, err = s.securityListingsDao.GetByExternalIds(ctx, externalIds, req.GetSecurityListingFields())
		if err != nil {
			logger.Error(ctx, "Error fetching security listings by external_ids", zap.Error(err), zap.Strings("external_ids", externalIds))
			return &catalogPb.GetSecurityListingsResponse{
				Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
			}, nil
		}

	case *catalogPb.GetSecurityListingsRequest_ExchangeSymbols_:
		exchangeSymbols := identifiers.ExchangeSymbols.GetExchangeSymbols()
		if len(exchangeSymbols) == 0 {
			logger.Error(ctx, "GetSecurityListings request has empty exchange_symbols")
			return &catalogPb.GetSecurityListingsResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("exchange_symbols cannot be empty"),
			}, nil
		}
		// Validate exchange symbols
		for _, symbol := range exchangeSymbols {
			if symbol == nil || symbol.GetSymbol() == "" || symbol.GetExchange() == catalogPb.Exchange_EXCHANGE_UNSPECIFIED {
				logger.Error(ctx, "GetSecurityListings request has invalid exchange_symbol")
				return &catalogPb.GetSecurityListingsResponse{
					Status: rpcPb.StatusInvalidArgumentWithDebugMsg("all exchange_symbols must have valid symbol and exchange"),
				}, nil
			}
		}
		securityListings, err = s.securityListingsDao.GetByExchangeSymbols(ctx, exchangeSymbols, req.GetSecurityListingFields())
		if err != nil {
			logger.Error(ctx, "Error fetching security listings by exchange_symbols", zap.Error(err))
			return &catalogPb.GetSecurityListingsResponse{
				Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
			}, nil
		}

	default:
		logger.Error(ctx, "GetSecurityListings request has unknown identifiers type")
		return &catalogPb.GetSecurityListingsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("unknown identifiers type"),
		}, nil
	}

	// If no listings were found, return record not found
	if len(securityListings) == 0 {
		logger.Error(ctx, "No security listings found for given identifiers")
		return &catalogPb.GetSecurityListingsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	// Prepare response
	var securityAndSecurityListings []*catalogPb.SecurityAndSecurityListing

	// Fetch security data if security fields are requested
	if len(req.GetSecurityFields()) > 0 {
		// Collect unique security IDs
		securityIds := make([]string, 0)
		securityIdSet := make(map[string]bool)
		for _, listing := range securityListings {
			if listing.GetSecurityId() != "" && !securityIdSet[listing.GetSecurityId()] {
				securityIds = append(securityIds, listing.GetSecurityId())
				securityIdSet[listing.GetSecurityId()] = true
			}
		}

		// Fetch securities in bulk if we have security IDs
		var securitiesMap map[string]*catalogPb.Security
		if len(securityIds) > 0 {
			securitiesMap, err = s.securitiesDao.BulkGet(ctx, securityIds, req.GetSecurityFields())
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					logger.Error(ctx, "Some securities not found for security_ids", zap.Strings("security_ids", securityIds))
					return &catalogPb.GetSecurityListingsResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil
				}
				logger.Error(ctx, "Error fetching securities", zap.Error(err), zap.Strings("security_ids", securityIds))
				return &catalogPb.GetSecurityListingsResponse{
					Status: rpcPb.StatusFromErrorWithDefaultInternal(err),
				}, nil
			}
		}

		// Build response with both security and security listing data
		for _, listing := range securityListings {
			securityAndListing := &catalogPb.SecurityAndSecurityListing{
				SecurityListing: listing,
			}
			if listing.GetSecurityId() != "" && securitiesMap != nil {
				if security, exists := securitiesMap[listing.GetSecurityId()]; exists {
					securityAndListing.Security = security
				}
			}
			securityAndSecurityListings = append(securityAndSecurityListings, securityAndListing)
		}
	} else {
		// Build response with only security listing data
		for _, listing := range securityListings {
			securityAndListing := &catalogPb.SecurityAndSecurityListing{
				SecurityListing: listing,
			}
			securityAndSecurityListings = append(securityAndSecurityListings, securityAndListing)
		}
	}

	return &catalogPb.GetSecurityListingsResponse{
		Status:                      rpcPb.StatusOk(),
		SecurityAndSecurityListings: securityAndSecurityListings,
	}, nil
}
