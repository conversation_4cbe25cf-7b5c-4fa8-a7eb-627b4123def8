// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	catalog2 "github.com/epifi/gamma/securities/catalog"
	"github.com/epifi/gamma/securities/catalog/consumer"
	"github.com/epifi/gamma/securities/catalog/dao"
	"github.com/epifi/gamma/securities/catalog/historicalpricesfetcher"
	"github.com/epifi/gamma/securities/catalog/ingester"
	"github.com/epifi/gamma/securities/config/genconf"
	types2 "github.com/epifi/gamma/securities/wire/types"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeCatalogService(db types.StocksPGDB, genConf *genconf.Config, vgCatalogClient bridgewise.CatalogClient, stockCatalogRefreshPublisher types2.StockCatalogRefreshPublisher, securitiesHistoricalPricePublisher types2.SecuritiesHistoricalPricePublisher, catalogManagerClient catalog.CatalogManagerClient, redisClient types2.SecuritiesRedisStore) *catalog2.Service {
	gormDB := stocksGormDbProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	securityListingsDaoPGDB := dao.NewSecurityListingsDaoPGDB(gormDB)
	securitiesDaoPGDB := dao.NewSecuritiesDaoPGDB(gormDB)
	client := SecuritiesRedisStoreProvider(redisClient)
	historicalPricesDaoPGDB := dao.NewHistoricalPricesDaoPGDB(gormDB)
	pgdbHistoricalPricesDAO := historicalpricesfetcher.NewPGDBHistoricalPricesDAO(historicalPricesDaoPGDB)
	redisHistoricalPricesCache := historicalpricesfetcher.NewRedisHistoricalPricesCache(genConf, client, pgdbHistoricalPricesDAO)
	catalogDataIngesterImpl := ingester.NewCatalogIngester(genConf, securitiesDaoPGDB, securityListingsDaoPGDB, vgCatalogClient, catalogManagerClient, stockCatalogRefreshPublisher, securitiesHistoricalPricePublisher)
	service := catalog2.NewService(genConf, vgCatalogClient, stockCatalogRefreshPublisher, crdbIdempotentTxnExecutor, securityListingsDaoPGDB, securitiesDaoPGDB, redisHistoricalPricesCache, catalogManagerClient, catalogDataIngesterImpl)
	return service
}

func InitializeCatalogConsumerService(db types.StocksPGDB, genConf *genconf.Config, vgCatalogClient bridgewise.CatalogClient, ussCatalogClient catalog.CatalogManagerClient, publisher types2.AddNewSecuritiesPublisher, redisClient types.InvestmentRedisStore, stockCatalogRefreshPublisher types2.StockCatalogRefreshPublisher, securitiesHistoricalPricePublisher types2.SecuritiesHistoricalPricePublisher) *consumer.Service {
	gormDB := stocksGormDbProvider(db)
	securitiesDaoPGDB := dao.NewSecuritiesDaoPGDB(gormDB)
	securityListingsDaoPGDB := dao.NewSecurityListingsDaoPGDB(gormDB)
	historicalPricesDaoPGDB := dao.NewHistoricalPricesDaoPGDB(gormDB)
	client := types.InvestmentRedisStoreRedisClientProvider(redisClient)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client, clock, uuidGenerator)
	catalogDataIngesterImpl := ingester.NewCatalogIngester(genConf, securitiesDaoPGDB, securityListingsDaoPGDB, vgCatalogClient, ussCatalogClient, stockCatalogRefreshPublisher, securitiesHistoricalPricePublisher)
	service := consumer.NewService(genConf, securitiesDaoPGDB, securityListingsDaoPGDB, historicalPricesDaoPGDB, vgCatalogClient, ussCatalogClient, publisher, redisV9LockManager, catalogDataIngesterImpl)
	return service
}

// wire.go:

func stocksGormDbProvider(db types.StocksPGDB) *gorm.DB {
	return db
}

func SecuritiesRedisStoreProvider(cl types2.SecuritiesRedisStore) *redis.Client {
	return cl
}
