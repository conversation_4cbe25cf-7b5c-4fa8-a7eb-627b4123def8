package dao_test

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/kyc/agent/dao"
	"github.com/epifi/gamma/kyc/test"
	"github.com/epifi/gamma/pkg/changefeed"
	storage "github.com/epifi/be-common/pkg/storage/v2"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	_, gconf, _, kycDb, teardown := test.InitTestServer()
	changeFeed := changefeed.NewChangefeed(kycDb)
	txnExecutor := storage.NewGormTxnExecutor(kycDb)
	kycAgentDao := dao.NewKycAgentDaoPGDB(kycDb, txnExecutor, changeFeed)
	kycAgentDaoTs = NewKycAgentDaoTestSuite(kycDb, kycAgentDao, gconf)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
