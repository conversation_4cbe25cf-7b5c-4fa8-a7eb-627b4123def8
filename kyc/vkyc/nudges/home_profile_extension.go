//nolint:dupl
package nudges

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"

	actorPb "github.com/epifi/gamma/api/actor"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/kyc/config/genconf"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/vkyc"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/be-common/pkg/logger"
)

type HomeProfileExtension struct {
	actorClient        actorPb.ActorClient
	userClient         userPb.UsersClient
	userGroupClient    userGroupPb.GroupClient
	abEvaluatorGeneric *release.ABEvaluator[string]
	genConf            genconf.VKYC
}

const (
	NudgeNameHomeProfileExtensionUnspecified NudgeName = iota
	NudgeNameHomeProfileExtensionLastDay
	NudgeNameHomeProfileExtensionLast3Days
	NudgeNameHomeProfileExtensionGeneric
	NudgeNameHomeProfileExtensionLast30DaysBalBelow1000
	NudgeNameHomeProfileExtensionLast30DaysBalAbove1000
	NudgeNameHomeProfileExtensionInReview
	NudgeNameHomeProfileExtensionRejected
	NudgeNameHomeProfileExtension25PctDepositLimitBreached
	NudgeNameHomeProfileExtension50PctDepositLimitBreached
	NudgeNameHomeProfileExtension60PctDepositLimitBreached
	NudgeNameHomeProfileExtension70PctDepositLimitBreached
	NudgeNameHomeProfileExtension80PctDepositLimitBreached
	NudgeNameHomeProfileExtension90PctDepositLimitBreached
	NudgeNameHomeProfileExtension100PctDepositLimitBreached
	NudgeNameHomeProfileExtensionCallFailed
	NudgeNameHomeProfileExtensionLSOUser
)

var (
	homeProfileExtensionOrder = []NudgeName{
		NudgeNameHomeProfileExtensionLSOUser,
		NudgeNameHomeProfileExtensionLastDay,
		NudgeNameHomeProfileExtensionLast3Days,
		NudgeNameHomeProfileExtensionRejected,
		NudgeNameHomeProfileExtensionInReview,
		NudgeNameHomeProfileExtensionCallFailed,
		NudgeNameHomeProfileExtensionLast30DaysBalBelow1000,
		NudgeNameHomeProfileExtensionLast30DaysBalAbove1000,
		NudgeNameHomeProfileExtension100PctDepositLimitBreached,
		NudgeNameHomeProfileExtension90PctDepositLimitBreached,
		NudgeNameHomeProfileExtension80PctDepositLimitBreached,
		NudgeNameHomeProfileExtension70PctDepositLimitBreached,
		NudgeNameHomeProfileExtension60PctDepositLimitBreached,
		NudgeNameHomeProfileExtension50PctDepositLimitBreached,
		NudgeNameHomeProfileExtension25PctDepositLimitBreached,
		NudgeNameHomeProfileExtensionGeneric,
	}
	homeProfileExtensionToNudgeId = map[NudgeName]string{
		NudgeNameHomeProfileExtension25PctDepositLimitBreached:  "694710ca-8c95-4ec3-95af-aa28a5d21d3f",
		NudgeNameHomeProfileExtension50PctDepositLimitBreached:  "d9fcee3b-aa29-4bfc-b313-9f5132a96f70",
		NudgeNameHomeProfileExtension60PctDepositLimitBreached:  "8401c6ed-9feb-4320-a729-25fb8d41d898",
		NudgeNameHomeProfileExtension70PctDepositLimitBreached:  "c8c4ac2b-388c-49d3-b338-f07adb48fb74",
		NudgeNameHomeProfileExtension80PctDepositLimitBreached:  "f3e0cd56-00e5-43ea-8e84-98ad8e71228c",
		NudgeNameHomeProfileExtension90PctDepositLimitBreached:  "54dd363f-6e80-481a-88b3-d466d35d514d",
		NudgeNameHomeProfileExtension100PctDepositLimitBreached: "5a5fc0d4-2c1e-43de-bdf9-d638166835f4",
		NudgeNameHomeProfileExtensionLSOUser:                    "f6799169-c070-485f-852a-51179ef57b42",
	}
)

var _ NudgeProcType = &HomeProfileExtension{}

func NewHomeProfileExtension(actorClient actorPb.ActorClient, userClient userPb.UsersClient, userGroupClient userGroupPb.GroupClient,
	genConf *genconf.Config) *HomeProfileExtension {
	return &HomeProfileExtension{
		abEvaluatorGeneric: GetABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, genConf.VKYC().ABFeatureReleaseConfig(), func(str string) string { return str }),
	}
}

func (p *HomeProfileExtension) NudgeProc(ctx context.Context, req *NudgeProcRequest) (*NudgeProcResponse, error) {
	var homeProfileExtensionToValidateMap = map[NudgeName]func(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error){
		NudgeNameHomeProfileExtensionLSOUser:                    validateHomeProfileExtensionLSOUser,
		NudgeNameHomeProfileExtensionLastDay:                    validateHomeProfileExtensionLastDay,
		NudgeNameHomeProfileExtensionLast3Days:                  validateHomeProfileExtensionLast3Days,
		NudgeNameHomeProfileExtensionInReview:                   validateHomeProfileExtensionInReview,
		NudgeNameHomeProfileExtensionRejected:                   validateHomeProfileExtensionRejected,
		NudgeNameHomeProfileExtensionGeneric:                    validateHomeProfileExtensionGeneric,
		NudgeNameHomeProfileExtension25PctDepositLimitBreached:  validateHomeProfileExtension25SavingLimitBreach,
		NudgeNameHomeProfileExtension50PctDepositLimitBreached:  validateHomeProfileExtension50SavingLimitBreach,
		NudgeNameHomeProfileExtension60PctDepositLimitBreached:  validateHomeProfileExtension60SavingLimitBreach,
		NudgeNameHomeProfileExtension70PctDepositLimitBreached:  validateHomeProfileExtension70SavingLimitBreach,
		NudgeNameHomeProfileExtension80PctDepositLimitBreached:  validateHomeProfileExtension80SavingLimitBreach,
		NudgeNameHomeProfileExtension90PctDepositLimitBreached:  validateHomeProfileExtension90SavingLimitBreach,
		NudgeNameHomeProfileExtension100PctDepositLimitBreached: validateHomeProfileExtension100SavingLimitBreach,
		NudgeNameHomeProfileExtensionCallFailed:                 validateHomeProfileExtensionCallFailed,
		NudgeNameHomeProfileExtensionLast30DaysBalAbove1000:     validateHomeProfileExtensionLast30DaysBalAbove1000,
		NudgeNameHomeProfileExtensionLast30DaysBalBelow1000:     validateHomeProfileExtensionLast30DaysBalBelow1000,
	}
	for _, nudgeName := range homeProfileExtensionOrder {
		nudgeFunc, exist := homeProfileExtensionToValidateMap[nudgeName]
		if !exist {
			logger.Error(ctx, "error in getting processor for home profile extension nudge")
			return nil, homeProfileExtensionError
		}
		toShow, nudgeContent, err := nudgeFunc(ctx, p, req.GetReq())
		if err != nil {
			logger.Error(ctx, "error while getting content for home profile extension")
			return nil, homeProfileExtensionError
		}
		if toShow {
			return &NudgeProcResponse{
				Resp: nudgeContent,
			}, nil
		}
	}
	return nil, nil
}

// show the home profile extension to min kyc user
//
//nolint:funlen,dupl
func validateHomeProfileExtensionGeneric(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	if datetime.IsDatePastXDays(detail.CifSucceededAt, 15) {
		return true, &NudgeContent{
			Deeplink:  detail.LandingDeeplink,
			UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
			Title:     homeProfileExtensionDay1,
			BgColor:   "",
		}, nil
	}
	return false, nil, nil
}

// show the home profile extension to min kyc user for last 3 days
//
//nolint:funlen,dupl
func validateHomeProfileExtensionLastDay(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}

	if time.Now().After(detail.FundTransferEligibleOnLastDay.AsTime()) {
		return true, &NudgeContent{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_PAY_LANDING_SCREEN,
			},
			UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
			Title:     homeProfileExtensionLastDay,
			BgColor:   "",
		}, nil
	}
	return false, nil, nil
}

// show the home profile extension to lso users
//
//nolint:funlen,dupl
func validateHomeProfileExtensionLSOUser(ctx context.Context, _ *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if !lo.Contains(vkyc.LSOVKYCActors, detail.ActorId) || isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	nudgeName := NudgeNameHomeProfileExtensionLSOUser
	nudgeId, exist1 := homeProfileExtensionToNudgeId[nudgeName]
	if !exist1 {
		logger.Error(ctx, "mapping not defined for profile extension LSO user")
		return false, nil, fmt.Errorf("mapping not defined for profile extension LSO user")
	}
	return true, &NudgeContent{
		Deeplink:  detail.InstructionDeeplink,
		UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
		Title:     homeProfileExtensionLSOTitle,
		BgColor:   "",
		NudgeId:   nudgeId,
	}, nil
}

// show the home profile extension to min kyc user for last 3 days
//
//nolint:funlen,dupl
func validateHomeProfileExtensionLast3Days(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}

	if time.Now().After(detail.FundTransferEligibleOnLastThirdDay.AsTime()) {
		return true, &NudgeContent{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_PAY_LANDING_SCREEN,
			},
			UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
			Title:     fmt.Sprintf(homeProfileExtensionAccountClosing, detail.AccFreezeDaysRemaining),
			BgColor:   "",
		}, nil
	}
	return false, nil, nil
}

// show the home profile extension to min kyc user for last 30 days when balance >= 1000
//
//nolint:funlen,dupl
func validateHomeProfileExtensionLast30DaysBalAbove1000(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	if time.Now().After(detail.AccountCloseInLast30Days.AsTime()) {
		return true, &NudgeContent{
			Deeplink:  detail.LandingDeeplink,
			UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
			Title:     fmt.Sprintf(homeProfileExtensionAccountClosing, detail.AccFreezeDaysRemaining),
			BgColor:   "",
		}, nil
	}
	return false, nil, nil
}

// show the home profile extension to min kyc user for last 30 days when balance < 1000
//
//nolint:funlen,dupl
func validateHomeProfileExtensionLast30DaysBalBelow1000(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	if time.Now().After(detail.AccountCloseInLast30Days.AsTime()) {
		return true, &NudgeContent{
			Deeplink:  detail.LandingDeeplink,
			UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
			Title:     fmt.Sprintf(homeProfileExtensionAccountClosing, detail.AccFreezeDaysRemaining),
			BgColor:   "",
		}, nil
	}
	return false, nil, nil
}

// home profile extension to be shown when savings limit breach to min kyc user. (eg. user crossed 25% of his savings limit)
//
//nolint:funlen,dupl
func validateHomeProfileExtension25SavingLimitBreach(ctx context.Context, h *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	nudgeName := NudgeNameHomeProfileExtension25PctDepositLimitBreached
	nudgeId, exist1 := homeProfileExtensionToNudgeId[nudgeName]
	if !exist1 {
		logger.Error(ctx, "mapping not defined for home profile extension 25% savings limit breach")
		return false, nil, fmt.Errorf("mapping not defined for home profile extension 25 percent savings limit breach")
	}
	if mapping := getCommsMappingByElementId(detail.Mappings, nudgeId); mapping != nil {
		if timeConstraintEligibility(mapping.GetCreatedAt(), 7) {
			return true, &NudgeContent{
				Deeplink:  detail.LandingDeeplink,
				UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
				Title:     homeProfileExtensionLimitBreached,
				BgColor:   "",
			}, nil
		}
	}
	return false, nil, nil
}

// home profile extension to be shown when savings limit breach to min kyc user. (eg. user crossed 50% of his savings limit)
//
//nolint:funlen,dupl
func validateHomeProfileExtension50SavingLimitBreach(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	nudgeName := NudgeNameHomeProfileExtension50PctDepositLimitBreached
	nudgeId, exist1 := homeProfileExtensionToNudgeId[nudgeName]
	if !exist1 {
		logger.Error(ctx, "mapping not defined for home profile extension 50% savings limit breach")
		return false, nil, fmt.Errorf("mapping not defined for home profile extension 50 percent savings limit breach")
	}
	if mapping := getCommsMappingByElementId(detail.Mappings, nudgeId); mapping != nil {
		if timeConstraintEligibility(mapping.GetCreatedAt(), 7) {
			return true, &NudgeContent{
				Deeplink:  detail.LandingDeeplink,
				UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
				Title:     homeProfileExtensionLimitBreached,
				BgColor:   "",
			}, nil
		}
	}
	return false, nil, nil
}

// home profile extension to be shown when savings limit breach to min kyc user. (eg. user crossed 60% of his savings limit)
//
//nolint:funlen,dupl
func validateHomeProfileExtension60SavingLimitBreach(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	nudgeName := NudgeNameHomeProfileExtension60PctDepositLimitBreached
	nudgeId, exist1 := homeProfileExtensionToNudgeId[nudgeName]
	if !exist1 {
		logger.Error(ctx, "mapping not defined for home profile extension 60% savings limit breach")
		return false, nil, fmt.Errorf("mapping not defined for home profile extension 60 percent savings limit breach")
	}
	if mapping := getCommsMappingByElementId(detail.Mappings, nudgeId); mapping != nil {
		if timeConstraintEligibility(mapping.GetCreatedAt(), 7) {
			return true, &NudgeContent{
				Deeplink:  detail.LandingDeeplink,
				UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
				Title:     homeProfileExtensionLimitBreached,
				BgColor:   "",
			}, nil
		}
	}
	return false, nil, nil
}

// home profile extension to be shown when savings limit breach to min kyc user. (eg. user crossed 70% of his savings limit)
//
//nolint:funlen,dupl
func validateHomeProfileExtension70SavingLimitBreach(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	nudgeName := NudgeNameHomeProfileExtension70PctDepositLimitBreached
	nudgeId, exist1 := homeProfileExtensionToNudgeId[nudgeName]
	if !exist1 {
		logger.Error(ctx, "mapping not defined for home profile extension 70% savings limit breach")
		return false, nil, fmt.Errorf("mapping not defined for home profile extension 70 percent savings limit breach")
	}
	if mapping := getCommsMappingByElementId(detail.Mappings, nudgeId); mapping != nil {
		if timeConstraintEligibility(mapping.GetCreatedAt(), 7) {
			return true, &NudgeContent{
				Deeplink:  detail.LandingDeeplink,
				UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
				Title:     homeProfileExtensionLimitBreached,
				BgColor:   "",
			}, nil
		}
	}
	return false, nil, nil
}

// home profile extension to be shown when savings limit breach to min kyc user. (eg. user crossed 80% of his savings limit)
//
//nolint:funlen,dupl
func validateHomeProfileExtension80SavingLimitBreach(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	nudgeName := NudgeNameHomeProfileExtension80PctDepositLimitBreached
	nudgeId, exist1 := homeProfileExtensionToNudgeId[nudgeName]
	if !exist1 {
		logger.Error(ctx, "mapping not defined for home profile extension 80% savings limit breach")
		return false, nil, fmt.Errorf("mapping not defined for home profile extension 80 percent savings limit breach")
	}
	if mapping := getCommsMappingByElementId(detail.Mappings, nudgeId); mapping != nil {
		if timeConstraintEligibility(mapping.GetCreatedAt(), 7) {
			return true, &NudgeContent{
				Deeplink:  detail.LandingDeeplink,
				UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
				Title:     homeProfileExtensionLimitBreached,
				BgColor:   "",
			}, nil
		}
	}
	return false, nil, nil
}

// home profile extension to be shown when savings limit breach to min kyc user. (eg. user crossed 90% of his savings limit)
//
//nolint:funlen,dupl
func validateHomeProfileExtension90SavingLimitBreach(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	nudgeName := NudgeNameHomeProfileExtension90PctDepositLimitBreached
	nudgeId, exist1 := homeProfileExtensionToNudgeId[nudgeName]
	if !exist1 {
		logger.Error(ctx, "mapping not defined for home profile extension 90% savings limit breach")
		return false, nil, fmt.Errorf("mapping not defined for home profile extension 90 percent savings limit breach")
	}
	if mapping := getCommsMappingByElementId(detail.Mappings, nudgeId); mapping != nil {
		if timeConstraintEligibility(mapping.GetCreatedAt(), 7) {
			return true, &NudgeContent{
				Deeplink:  detail.LandingDeeplink,
				UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
				Title:     homeProfileExtensionLimitBreached,
				BgColor:   "",
			}, nil
		}
	}
	return false, nil, nil
}

// home profile extension to be shown when savings limit breach to min kyc user. (eg.. user crossed 100% of his savings limit)
//
//nolint:funlen,dupl
func validateHomeProfileExtension100SavingLimitBreach(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}
	nudgeName := NudgeNameHomeProfileExtension100PctDepositLimitBreached
	nudgeId, exist1 := homeProfileExtensionToNudgeId[nudgeName]
	if !exist1 {
		logger.Error(ctx, "mapping not defined for home profile extension 100% savings limit breach")
		return false, nil, fmt.Errorf("mapping not defined for home profile extension 100 percent savings limit breach")
	}
	if mapping := getCommsMappingByElementId(detail.Mappings, nudgeId); mapping != nil {
		if timeConstraintEligibility(mapping.GetCreatedAt(), 7) {
			return true, &NudgeContent{
				Deeplink:  detail.LandingDeeplink,
				UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
				Title:     homeProfileExtensionLimitBreached,
				BgColor:   "",
			}, nil
		}
	}
	return false, nil, nil
}

// home profile extension to be shown when vkyc documents are rejected
//
//nolint:funlen,dupl
func validateHomeProfileExtensionRejected(ctx context.Context, p *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC {
		return false, nil, nil
	}
	if detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary().GetStatus() ==
		vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED {
		return true, &NudgeContent{
			UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
			Title:     homeProfileExtensionVkycRejected,
			BgColor:   "",
		}, nil
	}
	return false, nil, nil
}

// home profile extension to be shown when vkyc documents are in review by agent
//
//nolint:funlen,dupl
func validateHomeProfileExtensionInReview(ctx context.Context, h *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC {
		return false, nil, nil
	}

	if detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary().GetStatus() ==
		vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW {
		dl, _ := vkyc.BuildVKYCStatusDeeplink(&vkyc.StatusScreenOptions{
			EntryPoint: vkycPb.EntryPoint_ENTRY_POINT_VKYC_HOME,
		})
		return true, &NudgeContent{
			UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
			Title:     homeProfileExtensionVkycReview,
			Deeplink:  dl,
		}, nil
	}
	return false, nil, nil
}

// home profile extension to be shown when vkyc call fails
//
//nolint:funlen,dupl
func validateHomeProfileExtensionCallFailed(ctx context.Context, h *HomeProfileExtension, detail *VkycDetail) (bool, *NudgeContent, error) {
	if detail.KycLevel != kyc.KYCLevel_MIN_KYC ||
		isVkycInTerminalState(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycSummary(), detail.VKYCApproved) {
		return false, nil, nil
	}

	if detail.VkycInfoResp.GetVkycStatusResponse().GetVkycCallInfo().GetStatus() ==
		vkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED {
		if vkyc.GetLatestVKYCCallInfo(detail.VkycInfoResp).GetSubStatus() == vkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_AGENT_REJECTED {
			dl, err := vkyc.HandleVKYCAgentRejectedDL(ctx, vkycPb.EntryPoint_ENTRY_POINT_VKYC_HOME)
			if err != nil {
				return false, nil, err
			}
			return true, &NudgeContent{
				UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
				Deeplink:  dl,
				Title:     homeProfileExtensionFirstCallFails,
			}, nil
		}
		if timeConstraintEligibility(detail.VkycInfoResp.GetVkycStatusResponse().GetVkycCallInfo().GetCreatedAt(), 3) {
			return true, &NudgeContent{
				UIElement: vkycPb.UIElement_UI_ELEMENT_HOME_PROFILE_EXTENSION,
				Deeplink:  detail.LandingDeeplink,
				Title:     homeProfileExtensionFirstCallFails,
			}, nil
		}
	}
	return false, nil, nil
}
