// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/casper/exchanger/exchanger_offer.pb.go

package exchanger

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the ExchangerOfferRedemptionCurrency in string format in DB
func (p ExchangerOfferRedemptionCurrency) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ExchangerOfferRedemptionCurrency while reading from DB
func (p *ExchangerOfferRedemptionCurrency) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ExchangerOfferRedemptionCurrency_value[val]
	if !ok {
		return fmt.Errorf("unexpected ExchangerOfferRedemptionCurrency value: %s", val)
	}
	*p = ExchangerOfferRedemptionCurrency(valInt)
	return nil
}

// Marshaler interface implementation for ExchangerOfferRedemptionCurrency
func (x ExchangerOfferRedemptionCurrency) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ExchangerOfferRedemptionCurrency
func (x *ExchangerOfferRedemptionCurrency) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ExchangerOfferRedemptionCurrency(ExchangerOfferRedemptionCurrency_value[val])
	return nil
}

// Valuer interface implementation for storing the ExchangerOfferStatus in string format in DB
func (p ExchangerOfferStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ExchangerOfferStatus while reading from DB
func (p *ExchangerOfferStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ExchangerOfferStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected ExchangerOfferStatus value: %s", val)
	}
	*p = ExchangerOfferStatus(valInt)
	return nil
}

// Marshaler interface implementation for ExchangerOfferStatus
func (x ExchangerOfferStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ExchangerOfferStatus
func (x *ExchangerOfferStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ExchangerOfferStatus(ExchangerOfferStatus_value[val])
	return nil
}

// Scanner interface implementation for parsing ExchangerOfferDisplayDetails while reading from DB
func (a *ExchangerOfferDisplayDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the ExchangerOfferDisplayDetails in string format in DB
func (a *ExchangerOfferDisplayDetails) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for ExchangerOfferDisplayDetails
func (a *ExchangerOfferDisplayDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for ExchangerOfferDisplayDetails
func (a *ExchangerOfferDisplayDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing ExchangerOfferAggregatesConfig while reading from DB
func (a *ExchangerOfferAggregatesConfig) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the ExchangerOfferAggregatesConfig in string format in DB
func (a *ExchangerOfferAggregatesConfig) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for ExchangerOfferAggregatesConfig
func (a *ExchangerOfferAggregatesConfig) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for ExchangerOfferAggregatesConfig
func (a *ExchangerOfferAggregatesConfig) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing ExchangerOfferOptionsConfig while reading from DB
func (a *ExchangerOfferOptionsConfig) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the ExchangerOfferOptionsConfig in string format in DB
func (a *ExchangerOfferOptionsConfig) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for ExchangerOfferOptionsConfig
func (a *ExchangerOfferOptionsConfig) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for ExchangerOfferOptionsConfig
func (a *ExchangerOfferOptionsConfig) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing ExchangerOfferAdditionalDetails while reading from DB
func (a *ExchangerOfferAdditionalDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the ExchangerOfferAdditionalDetails in string format in DB
func (a *ExchangerOfferAdditionalDetails) Value() (driver.Value, error) {
	return protojson.Marshal(a)
}

// Marshaler interface implementation for ExchangerOfferAdditionalDetails
func (a *ExchangerOfferAdditionalDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for ExchangerOfferAdditionalDetails
func (a *ExchangerOfferAdditionalDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
