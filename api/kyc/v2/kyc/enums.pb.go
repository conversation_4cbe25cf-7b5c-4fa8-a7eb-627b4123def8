//go:generate gen_sql -types=Vendor,Flow,Status,SubStatus,KYCType,KYCProcessorType,FailureReason

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/kyc/v2/enums.proto

package kyc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// enum used for mapping the processor types to KYC Processors
// General idea is to create an enum based on Vendor + Type combo
// This can further be extended to any other variable if needed in the future. For eg, Vendor + Type + Flow combo
type KYCProcessorType int32

const (
	KYCProcessorType_KYC_PROCESSOR_TYPE_UNSPECIFIED  KYCProcessorType = 0
	KYCProcessorType_KYC_PROCESSOR_TYPE_FEDERAL_EKYC KYCProcessorType = 1
	KYCProcessorType_KYC_PROCESSOR_TYPE_IDFC_VKYC    KYCProcessorType = 2
)

// Enum value maps for KYCProcessorType.
var (
	KYCProcessorType_name = map[int32]string{
		0: "KYC_PROCESSOR_TYPE_UNSPECIFIED",
		1: "KYC_PROCESSOR_TYPE_FEDERAL_EKYC",
		2: "KYC_PROCESSOR_TYPE_IDFC_VKYC",
	}
	KYCProcessorType_value = map[string]int32{
		"KYC_PROCESSOR_TYPE_UNSPECIFIED":  0,
		"KYC_PROCESSOR_TYPE_FEDERAL_EKYC": 1,
		"KYC_PROCESSOR_TYPE_IDFC_VKYC":    2,
	}
)

func (x KYCProcessorType) Enum() *KYCProcessorType {
	p := new(KYCProcessorType)
	*p = x
	return p
}

func (x KYCProcessorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KYCProcessorType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_kyc_v2_enums_proto_enumTypes[0].Descriptor()
}

func (KYCProcessorType) Type() protoreflect.EnumType {
	return &file_api_kyc_v2_enums_proto_enumTypes[0]
}

func (x KYCProcessorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KYCProcessorType.Descriptor instead.
func (KYCProcessorType) EnumDescriptor() ([]byte, []int) {
	return file_api_kyc_v2_enums_proto_rawDescGZIP(), []int{0}
}

// vendor represents RE for whom we created kyc attempt
type Vendor int32

const (
	Vendor_VENDOR_UNSPECIFIED Vendor = 0
	Vendor_VENDOR_FEDERAL     Vendor = 1
	Vendor_VENDOR_LIQUILOANS  Vendor = 2
	Vendor_VENDOR_KARZA       Vendor = 3
	Vendor_VENDOR_IDFC        Vendor = 4
)

// Enum value maps for Vendor.
var (
	Vendor_name = map[int32]string{
		0: "VENDOR_UNSPECIFIED",
		1: "VENDOR_FEDERAL",
		2: "VENDOR_LIQUILOANS",
		3: "VENDOR_KARZA",
		4: "VENDOR_IDFC",
	}
	Vendor_value = map[string]int32{
		"VENDOR_UNSPECIFIED": 0,
		"VENDOR_FEDERAL":     1,
		"VENDOR_LIQUILOANS":  2,
		"VENDOR_KARZA":       3,
		"VENDOR_IDFC":        4,
	}
)

func (x Vendor) Enum() *Vendor {
	p := new(Vendor)
	*p = x
	return p
}

func (x Vendor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Vendor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_kyc_v2_enums_proto_enumTypes[1].Descriptor()
}

func (Vendor) Type() protoreflect.EnumType {
	return &file_api_kyc_v2_enums_proto_enumTypes[1]
}

func (x Vendor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Vendor.Descriptor instead.
func (Vendor) EnumDescriptor() ([]byte, []int) {
	return file_api_kyc_v2_enums_proto_rawDescGZIP(), []int{1}
}

// Flow represents the client or the source which requests for kyc
type Flow int32

const (
	Flow_FLOW_UNSPECIFIED                Flow = 0
	Flow_FLOW_SAVINGS_ACCOUNT_ONBOARDING Flow = 1
	Flow_FLOW_CREDIT_CARD_ONBOARDING     Flow = 2
	Flow_FLOW_LENDING_ONBOARDING         Flow = 3
)

// Enum value maps for Flow.
var (
	Flow_name = map[int32]string{
		0: "FLOW_UNSPECIFIED",
		1: "FLOW_SAVINGS_ACCOUNT_ONBOARDING",
		2: "FLOW_CREDIT_CARD_ONBOARDING",
		3: "FLOW_LENDING_ONBOARDING",
	}
	Flow_value = map[string]int32{
		"FLOW_UNSPECIFIED":                0,
		"FLOW_SAVINGS_ACCOUNT_ONBOARDING": 1,
		"FLOW_CREDIT_CARD_ONBOARDING":     2,
		"FLOW_LENDING_ONBOARDING":         3,
	}
)

func (x Flow) Enum() *Flow {
	p := new(Flow)
	*p = x
	return p
}

func (x Flow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Flow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_kyc_v2_enums_proto_enumTypes[2].Descriptor()
}

func (Flow) Type() protoreflect.EnumType {
	return &file_api_kyc_v2_enums_proto_enumTypes[2]
}

func (x Flow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Flow.Descriptor instead.
func (Flow) EnumDescriptor() ([]byte, []int) {
	return file_api_kyc_v2_enums_proto_rawDescGZIP(), []int{2}
}

// Status represents state of kyc attempt
type Status int32

const (
	Status_STATUS_UNSPECIFIED Status = 0
	// represent attempt created and in progress but not completed
	Status_STATUS_IN_PROGRESS Status = 1
	// completed represents attempt reached in success terminal state
	Status_STATUS_COMPLETED Status = 2
	// failed represents attempt reached in failure terminal state
	Status_STATUS_FAILED Status = 3
	// expire represents attempt was in success state but due to compliance we purge data
	Status_STATUS_EXPIRED Status = 4
	// transient error represent attempt failed due to some code error, without reaching terminal state
	Status_STATUS_TRANSIENT_ERROR Status = 5
	// created represent attempt which got created but kyc is not initiated
	Status_STATUS_CREATED Status = 6
	// in review represents kyc which is being reviewed
	Status_STATUS_IN_REVIEW Status = 7
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_IN_PROGRESS",
		2: "STATUS_COMPLETED",
		3: "STATUS_FAILED",
		4: "STATUS_EXPIRED",
		5: "STATUS_TRANSIENT_ERROR",
		6: "STATUS_CREATED",
		7: "STATUS_IN_REVIEW",
	}
	Status_value = map[string]int32{
		"STATUS_UNSPECIFIED":     0,
		"STATUS_IN_PROGRESS":     1,
		"STATUS_COMPLETED":       2,
		"STATUS_FAILED":          3,
		"STATUS_EXPIRED":         4,
		"STATUS_TRANSIENT_ERROR": 5,
		"STATUS_CREATED":         6,
		"STATUS_IN_REVIEW":       7,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_kyc_v2_enums_proto_enumTypes[3].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_api_kyc_v2_enums_proto_enumTypes[3]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_api_kyc_v2_enums_proto_rawDescGZIP(), []int{3}
}

// SubStatus gives granular detail of status of each kyc type
type SubStatus int32

const (
	SubStatus_SUB_STATUS_UNSPECIFIED SubStatus = 0
)

// Enum value maps for SubStatus.
var (
	SubStatus_name = map[int32]string{
		0: "SUB_STATUS_UNSPECIFIED",
	}
	SubStatus_value = map[string]int32{
		"SUB_STATUS_UNSPECIFIED": 0,
	}
)

func (x SubStatus) Enum() *SubStatus {
	p := new(SubStatus)
	*p = x
	return p
}

func (x SubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_kyc_v2_enums_proto_enumTypes[4].Descriptor()
}

func (SubStatus) Type() protoreflect.EnumType {
	return &file_api_kyc_v2_enums_proto_enumTypes[4]
}

func (x SubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubStatus.Descriptor instead.
func (SubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_kyc_v2_enums_proto_rawDescGZIP(), []int{4}
}

// FailureType is used to store the particular reason why a kyc attempt failed
type FailureType int32

const (
	FailureType_FAILURE_TYPE_UNSPECIFIED           FailureType = 0
	FailureType_FAILURE_TYPE_VENDOR_REQUEST_FAILED FailureType = 1
	FailureType_FAILURE_TYPE_INVALID_PARAMS        FailureType = 2
	FailureType_FAILURE_TYPE_VENDOR_KYC_REJECTED   FailureType = 3
	FailureType_FAILURE_TYPE_VENDOR_KYC_EXPIRED    FailureType = 4
)

// Enum value maps for FailureType.
var (
	FailureType_name = map[int32]string{
		0: "FAILURE_TYPE_UNSPECIFIED",
		1: "FAILURE_TYPE_VENDOR_REQUEST_FAILED",
		2: "FAILURE_TYPE_INVALID_PARAMS",
		3: "FAILURE_TYPE_VENDOR_KYC_REJECTED",
		4: "FAILURE_TYPE_VENDOR_KYC_EXPIRED",
	}
	FailureType_value = map[string]int32{
		"FAILURE_TYPE_UNSPECIFIED":           0,
		"FAILURE_TYPE_VENDOR_REQUEST_FAILED": 1,
		"FAILURE_TYPE_INVALID_PARAMS":        2,
		"FAILURE_TYPE_VENDOR_KYC_REJECTED":   3,
		"FAILURE_TYPE_VENDOR_KYC_EXPIRED":    4,
	}
)

func (x FailureType) Enum() *FailureType {
	p := new(FailureType)
	*p = x
	return p
}

func (x FailureType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FailureType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_kyc_v2_enums_proto_enumTypes[5].Descriptor()
}

func (FailureType) Type() protoreflect.EnumType {
	return &file_api_kyc_v2_enums_proto_enumTypes[5]
}

func (x FailureType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FailureType.Descriptor instead.
func (FailureType) EnumDescriptor() ([]byte, []int) {
	return file_api_kyc_v2_enums_proto_rawDescGZIP(), []int{5}
}

// Deprecated in favour of FailureType
type FailureReason int32

const (
	FailureReason_FAILURE_REASON_UNSPECIFIED FailureReason = 0
)

// Enum value maps for FailureReason.
var (
	FailureReason_name = map[int32]string{
		0: "FAILURE_REASON_UNSPECIFIED",
	}
	FailureReason_value = map[string]int32{
		"FAILURE_REASON_UNSPECIFIED": 0,
	}
)

func (x FailureReason) Enum() *FailureReason {
	p := new(FailureReason)
	*p = x
	return p
}

func (x FailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_kyc_v2_enums_proto_enumTypes[6].Descriptor()
}

func (FailureReason) Type() protoreflect.EnumType {
	return &file_api_kyc_v2_enums_proto_enumTypes[6]
}

func (x FailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FailureReason.Descriptor instead.
func (FailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_kyc_v2_enums_proto_rawDescGZIP(), []int{6}
}

// TYPE represents kyc type
type KYCType int32

const (
	KYCType_KYC_TYPE_UNSPECIFIED KYCType = 0
	KYCType_KYC_TYPE_CKYC        KYCType = 1
	KYCType_KYC_TYPE_EKYC        KYCType = 2
	KYCType_KYC_TYPE_OKYC        KYCType = 3
	KYCType_KYC_TYPE_VKYC        KYCType = 4
)

// Enum value maps for KYCType.
var (
	KYCType_name = map[int32]string{
		0: "KYC_TYPE_UNSPECIFIED",
		1: "KYC_TYPE_CKYC",
		2: "KYC_TYPE_EKYC",
		3: "KYC_TYPE_OKYC",
		4: "KYC_TYPE_VKYC",
	}
	KYCType_value = map[string]int32{
		"KYC_TYPE_UNSPECIFIED": 0,
		"KYC_TYPE_CKYC":        1,
		"KYC_TYPE_EKYC":        2,
		"KYC_TYPE_OKYC":        3,
		"KYC_TYPE_VKYC":        4,
	}
)

func (x KYCType) Enum() *KYCType {
	p := new(KYCType)
	*p = x
	return p
}

func (x KYCType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KYCType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_kyc_v2_enums_proto_enumTypes[7].Descriptor()
}

func (KYCType) Type() protoreflect.EnumType {
	return &file_api_kyc_v2_enums_proto_enumTypes[7]
}

func (x KYCType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KYCType.Descriptor instead.
func (KYCType) EnumDescriptor() ([]byte, []int) {
	return file_api_kyc_v2_enums_proto_rawDescGZIP(), []int{7}
}

var File_api_kyc_v2_enums_proto protoreflect.FileDescriptor

var file_api_kyc_v2_enums_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x76, 0x32, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6b, 0x79, 0x63, 0x2e, 0x76, 0x32,
	0x2e, 0x6b, 0x79, 0x63, 0x2a, 0x7d, 0x0a, 0x10, 0x4b, 0x59, 0x43, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x4b, 0x59, 0x43, 0x5f,
	0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f,
	0x4b, 0x59, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x45, 0x4b, 0x59, 0x43, 0x10,
	0x01, 0x12, 0x20, 0x0a, 0x1c, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53,
	0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x44, 0x46, 0x43, 0x5f, 0x56, 0x4b, 0x59,
	0x43, 0x10, 0x02, 0x2a, 0x6e, 0x0a, 0x06, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x16, 0x0a,
	0x12, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f,
	0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x02,
	0x12, 0x10, 0x0a, 0x0c, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4b, 0x41, 0x52, 0x5a, 0x41,
	0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x46,
	0x43, 0x10, 0x04, 0x2a, 0x7f, 0x0a, 0x04, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x10, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47,
	0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x4c, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x03, 0x2a, 0xbb, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12,
	0x14, 0x0a, 0x10, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x45, 0x4e, 0x54,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57,
	0x10, 0x07, 0x2a, 0x27, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x0a, 0x16, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x2a, 0xbf, 0x01, 0x0a, 0x0b,
	0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53,
	0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x52, 0x45,
	0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f,
	0x4b, 0x59, 0x43, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x04, 0x2a, 0x2f, 0x0a,
	0x0d, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e,
	0x0a, 0x1a, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x2a, 0x6f,
	0x0a, 0x07, 0x4b, 0x59, 0x43, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x4b, 0x59, 0x43,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x4b, 0x59, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x4b, 0x59, 0x43, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4b, 0x59, 0x43, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x45, 0x4b, 0x59, 0x43, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x4b, 0x59, 0x43,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x4b, 0x59, 0x43, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d,
	0x4b, 0x59, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x10, 0x04, 0x42,
	0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x79,
	0x63, 0x2e, 0x76, 0x32, 0x2e, 0x6b, 0x79, 0x63, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x76, 0x32, 0x2f, 0x6b, 0x79, 0x63, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_kyc_v2_enums_proto_rawDescOnce sync.Once
	file_api_kyc_v2_enums_proto_rawDescData = file_api_kyc_v2_enums_proto_rawDesc
)

func file_api_kyc_v2_enums_proto_rawDescGZIP() []byte {
	file_api_kyc_v2_enums_proto_rawDescOnce.Do(func() {
		file_api_kyc_v2_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_kyc_v2_enums_proto_rawDescData)
	})
	return file_api_kyc_v2_enums_proto_rawDescData
}

var file_api_kyc_v2_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_api_kyc_v2_enums_proto_goTypes = []interface{}{
	(KYCProcessorType)(0), // 0: kyc.v2.kyc.KYCProcessorType
	(Vendor)(0),           // 1: kyc.v2.kyc.Vendor
	(Flow)(0),             // 2: kyc.v2.kyc.Flow
	(Status)(0),           // 3: kyc.v2.kyc.Status
	(SubStatus)(0),        // 4: kyc.v2.kyc.SubStatus
	(FailureType)(0),      // 5: kyc.v2.kyc.FailureType
	(FailureReason)(0),    // 6: kyc.v2.kyc.FailureReason
	(KYCType)(0),          // 7: kyc.v2.kyc.KYCType
}
var file_api_kyc_v2_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_kyc_v2_enums_proto_init() }
func file_api_kyc_v2_enums_proto_init() {
	if File_api_kyc_v2_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_kyc_v2_enums_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_kyc_v2_enums_proto_goTypes,
		DependencyIndexes: file_api_kyc_v2_enums_proto_depIdxs,
		EnumInfos:         file_api_kyc_v2_enums_proto_enumTypes,
	}.Build()
	File_api_kyc_v2_enums_proto = out.File
	file_api_kyc_v2_enums_proto_rawDesc = nil
	file_api_kyc_v2_enums_proto_goTypes = nil
	file_api_kyc_v2_enums_proto_depIdxs = nil
}
