// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package savings;

import "api/accounts/account_type.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/account/enums.proto";
import "api/vendorgateway/vendor.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/savings";
option java_package = "com.github.epifi.gamma.api.savings";


// Defines the GRPC service to create payment instruments for an account
// This consumer can be used to create payment instruments in Async at the time of account provisioning
//
// This GRPC service is registered with consumer subscriber and RPC method will be invoked by the consumer
// on receiving an event
//
// Service can have RPC methods to
// * CreateVPA i.e., UPI PI
// * CreateAccountPI
// * CreateCardPI
service CreatePIConsumer {
  // CreateVPA triggers VPA creation
  // RPC will register the VPA at both epiFi and partner bank
  // This method is invoked by consumer subscriber
  rpc CreateVPA (CreateVPARequest) returns (CreateVPAResponse) {}
  // ProcessSavingsAccountPICreation triggers PI creation for savings account
  rpc ProcessSavingsAccountPICreation (ProcessSavingsAccountPICreationRequest) returns (ProcessSavingsAccountPICreationResponse) {}
}


message CreateVPARequest {
  reserved 4;
  // A set of all the common attributes to be contained in a consumer consumer response
  queue.ConsumerRequestHeader request_header = 1;

  // VPA will be provisioned for `user_id` that owns the account
  // In case of multiple account holders, primary holder information will be used
  string user_id = 2;

  // Account ID against which the VPA is to be provisioned
  string account_id = 3 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // Partner bank to which the account belongs to
  vendorgateway.Vendor partner_bank = 5 [(validate.rules).enum = {not_in: [0]}];

  // Type of the account eg. savings
  accounts.Type account_type = 6 [(validate.rules).enum = {not_in: [0]}];
}

message CreateVPAResponse {
  // A set of all the common attributes to be contained in a consumer consumer response
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessSavingsAccountPICreationRequest {
  // A set of all the common attributes to be contained in a consumer consumer response
  queue.ConsumerRequestHeader request_header = 1;

  string actor_id = 2;

  string entity_id = 3;

  // savings account no
  string account_no = 4;

  // savings account id
  string account_id = 5;

  // savings account ifsc code
  string ifsc_code = 6;

  // Account Product Offering associated with the AccountType.
  // 1. This can be UNSPECIFIED if it's an older account which did not have a product offering explicitly associated with it.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
  api.typesv2.account.AccountProductOffering account_product_offering = 7;
}

message ProcessSavingsAccountPICreationResponse {
  // A set of all the common attributes to be contained in a consumer consumer response
  queue.ConsumerResponseHeader response_header = 1;
}
