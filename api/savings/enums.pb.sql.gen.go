// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/savings/enums.pb.go

package savings

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the SAClosureRequestStatus in string format in DB
func (p SAClosureRequestStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing SAClosureRequestStatus while reading from DB
func (p *SAClosureRequestStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := SAClosureRequestStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected SAClosureRequestStatus value: %s", val)
	}
	*p = SAClosureRequestStatus(valInt)
	return nil
}

// Marshaler interface implementation for SAClosureRequestStatus
func (x SAClosureRequestStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for SAClosureRequestStatus
func (x *SAClosureRequestStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = SAClosureRequestStatus(SAClosureRequestStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the SAClosureRequestStatusReason in string format in DB
func (p SAClosureRequestStatusReason) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing SAClosureRequestStatusReason while reading from DB
func (p *SAClosureRequestStatusReason) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := SAClosureRequestStatusReason_value[val]
	if !ok {
		return fmt.Errorf("unexpected SAClosureRequestStatusReason value: %s", val)
	}
	*p = SAClosureRequestStatusReason(valInt)
	return nil
}

// Marshaler interface implementation for SAClosureRequestStatusReason
func (x SAClosureRequestStatusReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for SAClosureRequestStatusReason
func (x *SAClosureRequestStatusReason) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = SAClosureRequestStatusReason(SAClosureRequestStatusReason_value[val])
	return nil
}

// Valuer interface implementation for storing the SAClosureRequestEntryPoint in string format in DB
func (p SAClosureRequestEntryPoint) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing SAClosureRequestEntryPoint while reading from DB
func (p *SAClosureRequestEntryPoint) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := SAClosureRequestEntryPoint_value[val]
	if !ok {
		return fmt.Errorf("unexpected SAClosureRequestEntryPoint value: %s", val)
	}
	*p = SAClosureRequestEntryPoint(valInt)
	return nil
}

// Marshaler interface implementation for SAClosureRequestEntryPoint
func (x SAClosureRequestEntryPoint) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for SAClosureRequestEntryPoint
func (x *SAClosureRequestEntryPoint) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = SAClosureRequestEntryPoint(SAClosureRequestEntryPoint_value[val])
	return nil
}
