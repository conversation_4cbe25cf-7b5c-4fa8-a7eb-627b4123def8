// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/user/contact/service.proto

package contact

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Contact_RecordHashedContacts_FullMethodName = "/contact.Contact/RecordHashedContacts"
	Contact_SyncContactDetails_FullMethodName   = "/contact.Contact/SyncContactDetails"
	Contact_GetRiskyContacts_FullMethodName     = "/contact.Contact/GetRiskyContacts"
	Contact_GetContactsByBatch_FullMethodName   = "/contact.Contact/GetContactsByBatch"
)

// ContactClient is the client API for Contact service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ContactClient interface {
	// RecordHashedContacts stores the hashed contacts of a user in contacts table
	RecordHashedContacts(ctx context.Context, in *RecordHashedContactsRequest, opts ...grpc.CallOption) (*RecordHashedContactsResponse, error)
	// Rpc to sync the user's contacts.
	// Will fetch the hashed contacts for the current actor and return the contacts those are on fi
	SyncContactDetails(ctx context.Context, in *SyncContactDetailsRequest, opts ...grpc.CallOption) (*SyncContactDetailsResponse, error)
	// Deprecated: Do not use.
	// RPC to give the risky users contact information of the user
	// It will give all the contacts who are flagged in the system for the actor
	// deprecating this as it may impact the database due to join query
	GetRiskyContacts(ctx context.Context, in *GetRiskyContactsRequest, opts ...grpc.CallOption) (*GetRiskyContactsResponse, error)
	// GetContactsByBatch is a RPC to get the contacts by either phone
	// or actor_id.
	GetContactsByBatch(ctx context.Context, in *GetContactsByBatchRequest, opts ...grpc.CallOption) (*GetContactsByBatchResponse, error)
}

type contactClient struct {
	cc grpc.ClientConnInterface
}

func NewContactClient(cc grpc.ClientConnInterface) ContactClient {
	return &contactClient{cc}
}

func (c *contactClient) RecordHashedContacts(ctx context.Context, in *RecordHashedContactsRequest, opts ...grpc.CallOption) (*RecordHashedContactsResponse, error) {
	out := new(RecordHashedContactsResponse)
	err := c.cc.Invoke(ctx, Contact_RecordHashedContacts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactClient) SyncContactDetails(ctx context.Context, in *SyncContactDetailsRequest, opts ...grpc.CallOption) (*SyncContactDetailsResponse, error) {
	out := new(SyncContactDetailsResponse)
	err := c.cc.Invoke(ctx, Contact_SyncContactDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *contactClient) GetRiskyContacts(ctx context.Context, in *GetRiskyContactsRequest, opts ...grpc.CallOption) (*GetRiskyContactsResponse, error) {
	out := new(GetRiskyContactsResponse)
	err := c.cc.Invoke(ctx, Contact_GetRiskyContacts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactClient) GetContactsByBatch(ctx context.Context, in *GetContactsByBatchRequest, opts ...grpc.CallOption) (*GetContactsByBatchResponse, error) {
	out := new(GetContactsByBatchResponse)
	err := c.cc.Invoke(ctx, Contact_GetContactsByBatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ContactServer is the server API for Contact service.
// All implementations should embed UnimplementedContactServer
// for forward compatibility
type ContactServer interface {
	// RecordHashedContacts stores the hashed contacts of a user in contacts table
	RecordHashedContacts(context.Context, *RecordHashedContactsRequest) (*RecordHashedContactsResponse, error)
	// Rpc to sync the user's contacts.
	// Will fetch the hashed contacts for the current actor and return the contacts those are on fi
	SyncContactDetails(context.Context, *SyncContactDetailsRequest) (*SyncContactDetailsResponse, error)
	// Deprecated: Do not use.
	// RPC to give the risky users contact information of the user
	// It will give all the contacts who are flagged in the system for the actor
	// deprecating this as it may impact the database due to join query
	GetRiskyContacts(context.Context, *GetRiskyContactsRequest) (*GetRiskyContactsResponse, error)
	// GetContactsByBatch is a RPC to get the contacts by either phone
	// or actor_id.
	GetContactsByBatch(context.Context, *GetContactsByBatchRequest) (*GetContactsByBatchResponse, error)
}

// UnimplementedContactServer should be embedded to have forward compatible implementations.
type UnimplementedContactServer struct {
}

func (UnimplementedContactServer) RecordHashedContacts(context.Context, *RecordHashedContactsRequest) (*RecordHashedContactsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordHashedContacts not implemented")
}
func (UnimplementedContactServer) SyncContactDetails(context.Context, *SyncContactDetailsRequest) (*SyncContactDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncContactDetails not implemented")
}
func (UnimplementedContactServer) GetRiskyContacts(context.Context, *GetRiskyContactsRequest) (*GetRiskyContactsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRiskyContacts not implemented")
}
func (UnimplementedContactServer) GetContactsByBatch(context.Context, *GetContactsByBatchRequest) (*GetContactsByBatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContactsByBatch not implemented")
}

// UnsafeContactServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ContactServer will
// result in compilation errors.
type UnsafeContactServer interface {
	mustEmbedUnimplementedContactServer()
}

func RegisterContactServer(s grpc.ServiceRegistrar, srv ContactServer) {
	s.RegisterService(&Contact_ServiceDesc, srv)
}

func _Contact_RecordHashedContacts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordHashedContactsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServer).RecordHashedContacts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Contact_RecordHashedContacts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServer).RecordHashedContacts(ctx, req.(*RecordHashedContactsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Contact_SyncContactDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncContactDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServer).SyncContactDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Contact_SyncContactDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServer).SyncContactDetails(ctx, req.(*SyncContactDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Contact_GetRiskyContacts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRiskyContactsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServer).GetRiskyContacts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Contact_GetRiskyContacts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServer).GetRiskyContacts(ctx, req.(*GetRiskyContactsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Contact_GetContactsByBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContactsByBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServer).GetContactsByBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Contact_GetContactsByBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServer).GetContactsByBatch(ctx, req.(*GetContactsByBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Contact_ServiceDesc is the grpc.ServiceDesc for Contact service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Contact_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "contact.Contact",
	HandlerType: (*ContactServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RecordHashedContacts",
			Handler:    _Contact_RecordHashedContacts_Handler,
		},
		{
			MethodName: "SyncContactDetails",
			Handler:    _Contact_SyncContactDetails_Handler,
		},
		{
			MethodName: "GetRiskyContacts",
			Handler:    _Contact_GetRiskyContacts_Handler,
		},
		{
			MethodName: "GetContactsByBatch",
			Handler:    _Contact_GetContactsByBatch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/user/contact/service.proto",
}
