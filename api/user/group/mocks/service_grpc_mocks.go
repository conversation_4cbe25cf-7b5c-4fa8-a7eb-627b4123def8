// Code generated by MockGen. DO NOT EDIT.
// Source: api/user/group/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	group "github.com/epifi/gamma/api/user/group"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockGroupClient is a mock of GroupClient interface.
type MockGroupClient struct {
	ctrl     *gomock.Controller
	recorder *MockGroupClientMockRecorder
}

// MockGroupClientMockRecorder is the mock recorder for MockGroupClient.
type MockGroupClientMockRecorder struct {
	mock *MockGroupClient
}

// NewMockGroupClient creates a new mock instance.
func NewMockGroupClient(ctrl *gomock.Controller) *MockGroupClient {
	mock := &MockGroupClient{ctrl: ctrl}
	mock.recorder = &MockGroupClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGroupClient) EXPECT() *MockGroupClientMockRecorder {
	return m.recorder
}

// AddEmailGroupMapping mocks base method.
func (m *MockGroupClient) AddEmailGroupMapping(ctx context.Context, in *group.AddEmailGroupMappingRequest, opts ...grpc.CallOption) (*group.AddEmailGroupMappingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddEmailGroupMapping", varargs...)
	ret0, _ := ret[0].(*group.AddEmailGroupMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddEmailGroupMapping indicates an expected call of AddEmailGroupMapping.
func (mr *MockGroupClientMockRecorder) AddEmailGroupMapping(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEmailGroupMapping", reflect.TypeOf((*MockGroupClient)(nil).AddEmailGroupMapping), varargs...)
}

// AddMappings mocks base method.
func (m *MockGroupClient) AddMappings(ctx context.Context, in *group.AddMappingsRequest, opts ...grpc.CallOption) (*group.AddMappingsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddMappings", varargs...)
	ret0, _ := ret[0].(*group.AddMappingsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMappings indicates an expected call of AddMappings.
func (mr *MockGroupClientMockRecorder) AddMappings(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMappings", reflect.TypeOf((*MockGroupClient)(nil).AddMappings), varargs...)
}

// CheckMapping mocks base method.
func (m *MockGroupClient) CheckMapping(ctx context.Context, in *group.CheckMappingRequest, opts ...grpc.CallOption) (*group.CheckMappingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckMapping", varargs...)
	ret0, _ := ret[0].(*group.CheckMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMapping indicates an expected call of CheckMapping.
func (mr *MockGroupClientMockRecorder) CheckMapping(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMapping", reflect.TypeOf((*MockGroupClient)(nil).CheckMapping), varargs...)
}

// DeleteEmailGroupMapping mocks base method.
func (m *MockGroupClient) DeleteEmailGroupMapping(ctx context.Context, in *group.DeleteEmailGroupMappingRequest, opts ...grpc.CallOption) (*group.DeleteEmailGroupMappingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteEmailGroupMapping", varargs...)
	ret0, _ := ret[0].(*group.DeleteEmailGroupMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEmailGroupMapping indicates an expected call of DeleteEmailGroupMapping.
func (mr *MockGroupClientMockRecorder) DeleteEmailGroupMapping(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEmailGroupMapping", reflect.TypeOf((*MockGroupClient)(nil).DeleteEmailGroupMapping), varargs...)
}

// DeleteMapping mocks base method.
func (m *MockGroupClient) DeleteMapping(ctx context.Context, in *group.DeleteMappingRequest, opts ...grpc.CallOption) (*group.DeleteMappingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteMapping", varargs...)
	ret0, _ := ret[0].(*group.DeleteMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteMapping indicates an expected call of DeleteMapping.
func (mr *MockGroupClientMockRecorder) DeleteMapping(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMapping", reflect.TypeOf((*MockGroupClient)(nil).DeleteMapping), varargs...)
}

// GetGroupsMappedToEmail mocks base method.
func (m *MockGroupClient) GetGroupsMappedToEmail(ctx context.Context, in *group.GetGroupsMappedToEmailRequest, opts ...grpc.CallOption) (*group.GetGroupsMappedToEmailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupsMappedToEmail", varargs...)
	ret0, _ := ret[0].(*group.GetGroupsMappedToEmailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupsMappedToEmail indicates an expected call of GetGroupsMappedToEmail.
func (mr *MockGroupClientMockRecorder) GetGroupsMappedToEmail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupsMappedToEmail", reflect.TypeOf((*MockGroupClient)(nil).GetGroupsMappedToEmail), varargs...)
}

// GetGroupsMappedToIdentifier mocks base method.
func (m *MockGroupClient) GetGroupsMappedToIdentifier(ctx context.Context, in *group.GetGroupsMappedToIdentifierRequest, opts ...grpc.CallOption) (*group.GetGroupsMappedToIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupsMappedToIdentifier", varargs...)
	ret0, _ := ret[0].(*group.GetGroupsMappedToIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupsMappedToIdentifier indicates an expected call of GetGroupsMappedToIdentifier.
func (mr *MockGroupClientMockRecorder) GetGroupsMappedToIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupsMappedToIdentifier", reflect.TypeOf((*MockGroupClient)(nil).GetGroupsMappedToIdentifier), varargs...)
}

// GetUsersMappedToGroups mocks base method.
func (m *MockGroupClient) GetUsersMappedToGroups(ctx context.Context, in *group.GetUsersMappedToGroupsRequest, opts ...grpc.CallOption) (*group.GetUsersMappedToGroupsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUsersMappedToGroups", varargs...)
	ret0, _ := ret[0].(*group.GetUsersMappedToGroupsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsersMappedToGroups indicates an expected call of GetUsersMappedToGroups.
func (mr *MockGroupClientMockRecorder) GetUsersMappedToGroups(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersMappedToGroups", reflect.TypeOf((*MockGroupClient)(nil).GetUsersMappedToGroups), varargs...)
}

// MockGroupServer is a mock of GroupServer interface.
type MockGroupServer struct {
	ctrl     *gomock.Controller
	recorder *MockGroupServerMockRecorder
}

// MockGroupServerMockRecorder is the mock recorder for MockGroupServer.
type MockGroupServerMockRecorder struct {
	mock *MockGroupServer
}

// NewMockGroupServer creates a new mock instance.
func NewMockGroupServer(ctrl *gomock.Controller) *MockGroupServer {
	mock := &MockGroupServer{ctrl: ctrl}
	mock.recorder = &MockGroupServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGroupServer) EXPECT() *MockGroupServerMockRecorder {
	return m.recorder
}

// AddEmailGroupMapping mocks base method.
func (m *MockGroupServer) AddEmailGroupMapping(arg0 context.Context, arg1 *group.AddEmailGroupMappingRequest) (*group.AddEmailGroupMappingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddEmailGroupMapping", arg0, arg1)
	ret0, _ := ret[0].(*group.AddEmailGroupMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddEmailGroupMapping indicates an expected call of AddEmailGroupMapping.
func (mr *MockGroupServerMockRecorder) AddEmailGroupMapping(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEmailGroupMapping", reflect.TypeOf((*MockGroupServer)(nil).AddEmailGroupMapping), arg0, arg1)
}

// AddMappings mocks base method.
func (m *MockGroupServer) AddMappings(arg0 context.Context, arg1 *group.AddMappingsRequest) (*group.AddMappingsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMappings", arg0, arg1)
	ret0, _ := ret[0].(*group.AddMappingsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMappings indicates an expected call of AddMappings.
func (mr *MockGroupServerMockRecorder) AddMappings(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMappings", reflect.TypeOf((*MockGroupServer)(nil).AddMappings), arg0, arg1)
}

// CheckMapping mocks base method.
func (m *MockGroupServer) CheckMapping(arg0 context.Context, arg1 *group.CheckMappingRequest) (*group.CheckMappingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckMapping", arg0, arg1)
	ret0, _ := ret[0].(*group.CheckMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMapping indicates an expected call of CheckMapping.
func (mr *MockGroupServerMockRecorder) CheckMapping(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMapping", reflect.TypeOf((*MockGroupServer)(nil).CheckMapping), arg0, arg1)
}

// DeleteEmailGroupMapping mocks base method.
func (m *MockGroupServer) DeleteEmailGroupMapping(arg0 context.Context, arg1 *group.DeleteEmailGroupMappingRequest) (*group.DeleteEmailGroupMappingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEmailGroupMapping", arg0, arg1)
	ret0, _ := ret[0].(*group.DeleteEmailGroupMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEmailGroupMapping indicates an expected call of DeleteEmailGroupMapping.
func (mr *MockGroupServerMockRecorder) DeleteEmailGroupMapping(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEmailGroupMapping", reflect.TypeOf((*MockGroupServer)(nil).DeleteEmailGroupMapping), arg0, arg1)
}

// DeleteMapping mocks base method.
func (m *MockGroupServer) DeleteMapping(arg0 context.Context, arg1 *group.DeleteMappingRequest) (*group.DeleteMappingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMapping", arg0, arg1)
	ret0, _ := ret[0].(*group.DeleteMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteMapping indicates an expected call of DeleteMapping.
func (mr *MockGroupServerMockRecorder) DeleteMapping(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMapping", reflect.TypeOf((*MockGroupServer)(nil).DeleteMapping), arg0, arg1)
}

// GetGroupsMappedToEmail mocks base method.
func (m *MockGroupServer) GetGroupsMappedToEmail(arg0 context.Context, arg1 *group.GetGroupsMappedToEmailRequest) (*group.GetGroupsMappedToEmailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupsMappedToEmail", arg0, arg1)
	ret0, _ := ret[0].(*group.GetGroupsMappedToEmailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupsMappedToEmail indicates an expected call of GetGroupsMappedToEmail.
func (mr *MockGroupServerMockRecorder) GetGroupsMappedToEmail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupsMappedToEmail", reflect.TypeOf((*MockGroupServer)(nil).GetGroupsMappedToEmail), arg0, arg1)
}

// GetGroupsMappedToIdentifier mocks base method.
func (m *MockGroupServer) GetGroupsMappedToIdentifier(arg0 context.Context, arg1 *group.GetGroupsMappedToIdentifierRequest) (*group.GetGroupsMappedToIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupsMappedToIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*group.GetGroupsMappedToIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupsMappedToIdentifier indicates an expected call of GetGroupsMappedToIdentifier.
func (mr *MockGroupServerMockRecorder) GetGroupsMappedToIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupsMappedToIdentifier", reflect.TypeOf((*MockGroupServer)(nil).GetGroupsMappedToIdentifier), arg0, arg1)
}

// GetUsersMappedToGroups mocks base method.
func (m *MockGroupServer) GetUsersMappedToGroups(arg0 context.Context, arg1 *group.GetUsersMappedToGroupsRequest) (*group.GetUsersMappedToGroupsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersMappedToGroups", arg0, arg1)
	ret0, _ := ret[0].(*group.GetUsersMappedToGroupsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsersMappedToGroups indicates an expected call of GetUsersMappedToGroups.
func (mr *MockGroupServerMockRecorder) GetUsersMappedToGroups(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersMappedToGroups", reflect.TypeOf((*MockGroupServer)(nil).GetUsersMappedToGroups), arg0, arg1)
}

// MockUnsafeGroupServer is a mock of UnsafeGroupServer interface.
type MockUnsafeGroupServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeGroupServerMockRecorder
}

// MockUnsafeGroupServerMockRecorder is the mock recorder for MockUnsafeGroupServer.
type MockUnsafeGroupServerMockRecorder struct {
	mock *MockUnsafeGroupServer
}

// NewMockUnsafeGroupServer creates a new mock instance.
func NewMockUnsafeGroupServer(ctrl *gomock.Controller) *MockUnsafeGroupServer {
	mock := &MockUnsafeGroupServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeGroupServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeGroupServer) EXPECT() *MockUnsafeGroupServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedGroupServer mocks base method.
func (m *MockUnsafeGroupServer) mustEmbedUnimplementedGroupServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedGroupServer")
}

// mustEmbedUnimplementedGroupServer indicates an expected call of mustEmbedUnimplementedGroupServer.
func (mr *MockUnsafeGroupServerMockRecorder) mustEmbedUnimplementedGroupServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedGroupServer", reflect.TypeOf((*MockUnsafeGroupServer)(nil).mustEmbedUnimplementedGroupServer))
}
