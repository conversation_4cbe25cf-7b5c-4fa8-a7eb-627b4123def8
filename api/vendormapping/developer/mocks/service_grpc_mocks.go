// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendormapping/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDevVendorMappingClient is a mock of DevVendorMappingClient interface.
type MockDevVendorMappingClient struct {
	ctrl     *gomock.Controller
	recorder *MockDevVendorMappingClientMockRecorder
}

// MockDevVendorMappingClientMockRecorder is the mock recorder for MockDevVendorMappingClient.
type MockDevVendorMappingClientMockRecorder struct {
	mock *MockDevVendorMappingClient
}

// NewMockDevVendorMappingClient creates a new mock instance.
func NewMockDevVendorMappingClient(ctrl *gomock.Controller) *MockDevVendorMappingClient {
	mock := &MockDevVendorMappingClient{ctrl: ctrl}
	mock.recorder = &MockDevVendorMappingClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevVendorMappingClient) EXPECT() *MockDevVendorMappingClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevVendorMappingClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevVendorMappingClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevVendorMappingClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockDevVendorMappingClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevVendorMappingClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevVendorMappingClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockDevVendorMappingClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevVendorMappingClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevVendorMappingClient)(nil).GetParameterList), varargs...)
}

// MockDevVendorMappingServer is a mock of DevVendorMappingServer interface.
type MockDevVendorMappingServer struct {
	ctrl     *gomock.Controller
	recorder *MockDevVendorMappingServerMockRecorder
}

// MockDevVendorMappingServerMockRecorder is the mock recorder for MockDevVendorMappingServer.
type MockDevVendorMappingServerMockRecorder struct {
	mock *MockDevVendorMappingServer
}

// NewMockDevVendorMappingServer creates a new mock instance.
func NewMockDevVendorMappingServer(ctrl *gomock.Controller) *MockDevVendorMappingServer {
	mock := &MockDevVendorMappingServer{ctrl: ctrl}
	mock.recorder = &MockDevVendorMappingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevVendorMappingServer) EXPECT() *MockDevVendorMappingServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevVendorMappingServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevVendorMappingServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevVendorMappingServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockDevVendorMappingServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevVendorMappingServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevVendorMappingServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockDevVendorMappingServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevVendorMappingServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevVendorMappingServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeDevVendorMappingServer is a mock of UnsafeDevVendorMappingServer interface.
type MockUnsafeDevVendorMappingServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDevVendorMappingServerMockRecorder
}

// MockUnsafeDevVendorMappingServerMockRecorder is the mock recorder for MockUnsafeDevVendorMappingServer.
type MockUnsafeDevVendorMappingServerMockRecorder struct {
	mock *MockUnsafeDevVendorMappingServer
}

// NewMockUnsafeDevVendorMappingServer creates a new mock instance.
func NewMockUnsafeDevVendorMappingServer(ctrl *gomock.Controller) *MockUnsafeDevVendorMappingServer {
	mock := &MockUnsafeDevVendorMappingServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDevVendorMappingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDevVendorMappingServer) EXPECT() *MockUnsafeDevVendorMappingServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDevVendorMappingServer mocks base method.
func (m *MockUnsafeDevVendorMappingServer) mustEmbedUnimplementedDevVendorMappingServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDevVendorMappingServer")
}

// mustEmbedUnimplementedDevVendorMappingServer indicates an expected call of mustEmbedUnimplementedDevVendorMappingServer.
func (mr *MockUnsafeDevVendorMappingServerMockRecorder) mustEmbedUnimplementedDevVendorMappingServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDevVendorMappingServer", reflect.TypeOf((*MockUnsafeDevVendorMappingServer)(nil).mustEmbedUnimplementedDevVendorMappingServer))
}
