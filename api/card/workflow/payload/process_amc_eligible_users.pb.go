// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/workflow/payload/process_amc_eligible_users.proto

package payload

import (
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessAmcEligibleUsers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// s3 path where the file generated through report generation (analytics db) is
	// persisted. We will use this file to apply filters on user row list.
	S3Path string `protobuf:"bytes,1,opt,name=s3_path,json=s3Path,proto3" json:"s3_path,omitempty"`
	// date on which file generation has begun
	FileGenDate *date.Date `protobuf:"bytes,2,opt,name=file_gen_date,json=fileGenDate,proto3" json:"file_gen_date,omitempty"`
}

func (x *ProcessAmcEligibleUsers) Reset() {
	*x = ProcessAmcEligibleUsers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_workflow_payload_process_amc_eligible_users_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAmcEligibleUsers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAmcEligibleUsers) ProtoMessage() {}

func (x *ProcessAmcEligibleUsers) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_workflow_payload_process_amc_eligible_users_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAmcEligibleUsers.ProtoReflect.Descriptor instead.
func (*ProcessAmcEligibleUsers) Descriptor() ([]byte, []int) {
	return file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessAmcEligibleUsers) GetS3Path() string {
	if x != nil {
		return x.S3Path
	}
	return ""
}

func (x *ProcessAmcEligibleUsers) GetFileGenDate() *date.Date {
	if x != nil {
		return x.FileGenDate
	}
	return nil
}

type ProcessAmcEligibleUsersBatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// card ids which need to be processed in the given batch.
	CardIds             []string   `protobuf:"bytes,1,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	EligibleUsersS3Path string     `protobuf:"bytes,2,opt,name=eligible_users_s3_path,json=eligibleUsersS3Path,proto3" json:"eligible_users_s3_path,omitempty"`
	FailedUsersS3Path   string     `protobuf:"bytes,3,opt,name=failed_users_s3_path,json=failedUsersS3Path,proto3" json:"failed_users_s3_path,omitempty"`
	BatchNumber         int32      `protobuf:"varint,4,opt,name=batch_number,json=batchNumber,proto3" json:"batch_number,omitempty"`
	FileGenDate         *date.Date `protobuf:"bytes,5,opt,name=file_gen_date,json=fileGenDate,proto3" json:"file_gen_date,omitempty"`
}

func (x *ProcessAmcEligibleUsersBatch) Reset() {
	*x = ProcessAmcEligibleUsersBatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_workflow_payload_process_amc_eligible_users_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAmcEligibleUsersBatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAmcEligibleUsersBatch) ProtoMessage() {}

func (x *ProcessAmcEligibleUsersBatch) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_workflow_payload_process_amc_eligible_users_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAmcEligibleUsersBatch.ProtoReflect.Descriptor instead.
func (*ProcessAmcEligibleUsersBatch) Descriptor() ([]byte, []int) {
	return file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessAmcEligibleUsersBatch) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *ProcessAmcEligibleUsersBatch) GetEligibleUsersS3Path() string {
	if x != nil {
		return x.EligibleUsersS3Path
	}
	return ""
}

func (x *ProcessAmcEligibleUsersBatch) GetFailedUsersS3Path() string {
	if x != nil {
		return x.FailedUsersS3Path
	}
	return ""
}

func (x *ProcessAmcEligibleUsersBatch) GetBatchNumber() int32 {
	if x != nil {
		return x.BatchNumber
	}
	return 0
}

func (x *ProcessAmcEligibleUsersBatch) GetFileGenDate() *date.Date {
	if x != nil {
		return x.FileGenDate
	}
	return nil
}

var File_api_card_workflow_payload_process_amc_eligible_users_proto protoreflect.FileDescriptor

var file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x61, 0x6d, 0x63, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x69, 0x0a,
	0x17, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x6d, 0x63, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x33, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x33, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x35, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x67, 0x65, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x47, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x22, 0xf9, 0x01, 0x0a, 0x1c, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x41, 0x6d, 0x63, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2f, 0x0a, 0x14, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x53, 0x33, 0x50, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x35, 0x0a,
	0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x67, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x47, 0x65, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x42, 0x64, 0x0a, 0x30, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDescOnce sync.Once
	file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDescData = file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDesc
)

func file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDescGZIP() []byte {
	file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDescOnce.Do(func() {
		file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDescData)
	})
	return file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDescData
}

var file_api_card_workflow_payload_process_amc_eligible_users_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_card_workflow_payload_process_amc_eligible_users_proto_goTypes = []interface{}{
	(*ProcessAmcEligibleUsers)(nil),      // 0: payload.ProcessAmcEligibleUsers
	(*ProcessAmcEligibleUsersBatch)(nil), // 1: payload.ProcessAmcEligibleUsersBatch
	(*date.Date)(nil),                    // 2: google.type.Date
}
var file_api_card_workflow_payload_process_amc_eligible_users_proto_depIdxs = []int32{
	2, // 0: payload.ProcessAmcEligibleUsers.file_gen_date:type_name -> google.type.Date
	2, // 1: payload.ProcessAmcEligibleUsersBatch.file_gen_date:type_name -> google.type.Date
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_card_workflow_payload_process_amc_eligible_users_proto_init() }
func file_api_card_workflow_payload_process_amc_eligible_users_proto_init() {
	if File_api_card_workflow_payload_process_amc_eligible_users_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_workflow_payload_process_amc_eligible_users_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAmcEligibleUsers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_workflow_payload_process_amc_eligible_users_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAmcEligibleUsersBatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_workflow_payload_process_amc_eligible_users_proto_goTypes,
		DependencyIndexes: file_api_card_workflow_payload_process_amc_eligible_users_proto_depIdxs,
		MessageInfos:      file_api_card_workflow_payload_process_amc_eligible_users_proto_msgTypes,
	}.Build()
	File_api_card_workflow_payload_process_amc_eligible_users_proto = out.File
	file_api_card_workflow_payload_process_amc_eligible_users_proto_rawDesc = nil
	file_api_card_workflow_payload_process_amc_eligible_users_proto_goTypes = nil
	file_api_card_workflow_payload_process_amc_eligible_users_proto_depIdxs = nil
}
