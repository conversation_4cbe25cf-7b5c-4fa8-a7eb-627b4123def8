// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/app_instance_ids.proto

package types

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AppInstanceIdName int32

const (
	AppInstanceIdName_ID_NAME_UNSPECIFIED AppInstanceIdName = 0
	// unique identifier created internally by us on every app installation
	AppInstanceIdName_PROSPECT_ID AppInstanceIdName = 1
	// AppsF<PERSON>er's 'appsflyer_id'
	AppInstanceIdName_CLIENT_APPSFLYER_ID AppInstanceIdName = 2
	// Google Firebase's 'app_instance_id'
	AppInstanceIdName_FIREBASE_APP_INSTANCE_ID AppInstanceIdName = 3
)

// Enum value maps for AppInstanceIdName.
var (
	AppInstanceIdName_name = map[int32]string{
		0: "ID_NAME_UNSPECIFIED",
		1: "PROSPECT_ID",
		2: "CLIENT_APPSFLYER_ID",
		3: "FIREBASE_APP_INSTANCE_ID",
	}
	AppInstanceIdName_value = map[string]int32{
		"ID_NAME_UNSPECIFIED":      0,
		"PROSPECT_ID":              1,
		"CLIENT_APPSFLYER_ID":      2,
		"FIREBASE_APP_INSTANCE_ID": 3,
	}
)

func (x AppInstanceIdName) Enum() *AppInstanceIdName {
	p := new(AppInstanceIdName)
	*p = x
	return p
}

func (x AppInstanceIdName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppInstanceIdName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_types_app_instance_ids_proto_enumTypes[0].Descriptor()
}

func (AppInstanceIdName) Type() protoreflect.EnumType {
	return &file_api_types_app_instance_ids_proto_enumTypes[0]
}

func (x AppInstanceIdName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppInstanceIdName.Descriptor instead.
func (AppInstanceIdName) EnumDescriptor() ([]byte, []int) {
	return file_api_types_app_instance_ids_proto_rawDescGZIP(), []int{0}
}

type AppInstanceId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the ID, eg. Firebase app_instance_id, AppsFlyerId, Prospect ID, etc.
	Name AppInstanceIdName `protobuf:"varint,1,opt,name=name,proto3,enum=types.AppInstanceIdName" json:"name,omitempty"`
	// value of the ID
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *AppInstanceId) Reset() {
	*x = AppInstanceId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_app_instance_ids_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppInstanceId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppInstanceId) ProtoMessage() {}

func (x *AppInstanceId) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_app_instance_ids_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppInstanceId.ProtoReflect.Descriptor instead.
func (*AppInstanceId) Descriptor() ([]byte, []int) {
	return file_api_types_app_instance_ids_proto_rawDescGZIP(), []int{0}
}

func (x *AppInstanceId) GetName() AppInstanceIdName {
	if x != nil {
		return x.Name
	}
	return AppInstanceIdName_ID_NAME_UNSPECIFIED
}

func (x *AppInstanceId) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_api_types_app_instance_ids_proto protoreflect.FileDescriptor

var file_api_types_app_instance_ids_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x53, 0x0a, 0x0d, 0x41, 0x70, 0x70,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x74,
	0x0a, 0x11, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x44, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b,
	0x50, 0x52, 0x4f, 0x53, 0x50, 0x45, 0x43, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x17, 0x0a,
	0x13, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x53, 0x46, 0x4c, 0x59, 0x45,
	0x52, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x49, 0x52, 0x45, 0x42, 0x41,
	0x53, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x49, 0x44, 0x10, 0x03, 0x42, 0x44, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x5a, 0x20, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_types_app_instance_ids_proto_rawDescOnce sync.Once
	file_api_types_app_instance_ids_proto_rawDescData = file_api_types_app_instance_ids_proto_rawDesc
)

func file_api_types_app_instance_ids_proto_rawDescGZIP() []byte {
	file_api_types_app_instance_ids_proto_rawDescOnce.Do(func() {
		file_api_types_app_instance_ids_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_app_instance_ids_proto_rawDescData)
	})
	return file_api_types_app_instance_ids_proto_rawDescData
}

var file_api_types_app_instance_ids_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_types_app_instance_ids_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_types_app_instance_ids_proto_goTypes = []interface{}{
	(AppInstanceIdName)(0), // 0: types.AppInstanceIdName
	(*AppInstanceId)(nil),  // 1: types.AppInstanceId
}
var file_api_types_app_instance_ids_proto_depIdxs = []int32{
	0, // 0: types.AppInstanceId.name:type_name -> types.AppInstanceIdName
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_types_app_instance_ids_proto_init() }
func file_api_types_app_instance_ids_proto_init() {
	if File_api_types_app_instance_ids_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_types_app_instance_ids_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppInstanceId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_app_instance_ids_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_app_instance_ids_proto_goTypes,
		DependencyIndexes: file_api_types_app_instance_ids_proto_depIdxs,
		EnumInfos:         file_api_types_app_instance_ids_proto_enumTypes,
		MessageInfos:      file_api_types_app_instance_ids_proto_msgTypes,
	}.Build()
	File_api_types_app_instance_ids_proto = out.File
	file_api_types_app_instance_ids_proto_rawDesc = nil
	file_api_types_app_instance_ids_proto_goTypes = nil
	file_api_types_app_instance_ids_proto_depIdxs = nil
}
