syntax = "proto3";

package types.ui.sdui.components;

import "api/types/ui/widget/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/types/ui/sdui/components";
option java_package = "com.github.epifi.gamma.api.types.ui.sdui.components";

// A component to specify spacing between 2 other components/sections. Can be used in a horizontal or vertical contexts
// (this can be decided by the call-site)
message Spacer {
  // Optional: background color of the spacing. By default, this may not be sent, which means the Space will have no
  // background rendered
  types.ui.widget.BackgroundColour bg_colour = 1;
  // The size of the Spacer (This is considered to be height in a horizontal context and width in a vertical context)
  // Clients will translate the enum value into Device-specific dimensions (e.g. DP in Android or Points in iOS)
  Spacing spacing_value = 2;
  // This is to spacer size based on the weight. if value is set, it will override [spacing_value].
  Weight weight = 3;
  message Weight {
    float value = 1;
  }
}

// Spacing enum values defining common space values as per the Kiwi design system:
// https://www.figma.com/file/spRMTiw7iBKQI4JHsC4QNy/%F0%9F%A5%9D-Kiwi-2.0-(WIP)?type=design&node-id=5103-28742&mode=dev
// The enum values will be translated to DP size values based on the definitions in Above Figma file
enum Spacing {
    SPACING_UNSPECIFIED = 0;
    //  4 points (or dp) in client side
    SPACING_XXS = 1;
    //  8 points (or dp) in client side
    SPACING_XS = 2;
    // 12 points (or dp) in client side
    SPACING_S = 3;
    // 16 points (or dp) in client side
    SPACING_M = 4;
    // 24 points (or dp) in client side
    SPACING_L = 5;
    // 32 points (or dp) in client side
    SPACING_XL = 6;
    // 48 points (or dp) in client side
    SPACING_XXL = 7;
    // 64 points (or dp) in client side
    SPACING_XXXL = 8;
}
