//go:generate gen_sql -types=NudgeEventDataType,NudgeStatus,NudgeCategory,NudgeSubCategory

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/nudges/enums.proto

package nudges

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// standard nudge type defined by the Design team
// would be used for different UI treatments if needed
type NudgeDisplayType int32

const (
	NudgeDisplayType_NUDGE_DISPLAY_TYPE_UNSPECIFIED NudgeDisplayType = 0
	// nudge design displayed on home page
	NudgeDisplayType_HOME NudgeDisplayType = 1
	// nudge design displayed on investment landing for zero state nudges
	NudgeDisplayType_INVESTMENT_NOT_INVESTED NudgeDisplayType = 2
	// nudge design displayed on investment landing for non zero state nudges
	NudgeDisplayType_INVESTMENT_ALREADY_INVESTED NudgeDisplayType = 3
)

// Enum value maps for NudgeDisplayType.
var (
	NudgeDisplayType_name = map[int32]string{
		0: "NUDGE_DISPLAY_TYPE_UNSPECIFIED",
		1: "HOME",
		2: "INVESTMENT_NOT_INVESTED",
		3: "INVESTMENT_ALREADY_INVESTED",
	}
	NudgeDisplayType_value = map[string]int32{
		"NUDGE_DISPLAY_TYPE_UNSPECIFIED": 0,
		"HOME":                           1,
		"INVESTMENT_NOT_INVESTED":        2,
		"INVESTMENT_ALREADY_INVESTED":    3,
	}
)

func (x NudgeDisplayType) Enum() *NudgeDisplayType {
	p := new(NudgeDisplayType)
	*p = x
	return p
}

func (x NudgeDisplayType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NudgeDisplayType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_types_nudges_enums_proto_enumTypes[0].Descriptor()
}

func (NudgeDisplayType) Type() protoreflect.EnumType {
	return &file_api_types_nudges_enums_proto_enumTypes[0]
}

func (x NudgeDisplayType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NudgeDisplayType.Descriptor instead.
func (NudgeDisplayType) EnumDescriptor() ([]byte, []int) {
	return file_api_types_nudges_enums_proto_rawDescGZIP(), []int{0}
}

var File_api_types_nudges_enums_proto protoreflect.FileDescriptor

var file_api_types_nudges_enums_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x6e, 0x75, 0x64, 0x67,
	0x65, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x73, 0x2a, 0x7e, 0x0a, 0x10,
	0x4e, 0x75, 0x64, 0x67, 0x65, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x22, 0x0a, 0x1e, 0x4e, 0x55, 0x44, 0x47, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41,
	0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x4f, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x1b,
	0x0a, 0x17, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x49,
	0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44,
	0x59, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x45, 0x44, 0x10, 0x03, 0x42, 0x52, 0x0a, 0x27,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x73, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_types_nudges_enums_proto_rawDescOnce sync.Once
	file_api_types_nudges_enums_proto_rawDescData = file_api_types_nudges_enums_proto_rawDesc
)

func file_api_types_nudges_enums_proto_rawDescGZIP() []byte {
	file_api_types_nudges_enums_proto_rawDescOnce.Do(func() {
		file_api_types_nudges_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_nudges_enums_proto_rawDescData)
	})
	return file_api_types_nudges_enums_proto_rawDescData
}

var file_api_types_nudges_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_types_nudges_enums_proto_goTypes = []interface{}{
	(NudgeDisplayType)(0), // 0: types.nudges.NudgeDisplayType
}
var file_api_types_nudges_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_types_nudges_enums_proto_init() }
func file_api_types_nudges_enums_proto_init() {
	if File_api_types_nudges_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_nudges_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_nudges_enums_proto_goTypes,
		DependencyIndexes: file_api_types_nudges_enums_proto_depIdxs,
		EnumInfos:         file_api_types_nudges_enums_proto_enumTypes,
	}.Build()
	File_api_types_nudges_enums_proto = out.File
	file_api_types_nudges_enums_proto_rawDesc = nil
	file_api_types_nudges_enums_proto_goTypes = nil
	file_api_types_nudges_enums_proto_depIdxs = nil
}
