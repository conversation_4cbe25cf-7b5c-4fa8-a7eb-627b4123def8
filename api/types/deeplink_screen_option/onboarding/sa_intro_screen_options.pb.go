// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/deeplink_screen_option/onboarding/sa_intro_screen_options.proto

package onboarding

import (
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/types"
	deeplink_screen_option "github.com/epifi/gamma/api/types/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/types/ui"
	widget "github.com/epifi/gamma/api/types/ui/widget"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// screen options for https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?type=design&node-id=35097-54550&mode=design&t=lKKnFCiEbyXWWLuf-0
// and https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=402-41028&t=X6kkDvE8rbrbzXyP-0
type SaIntroScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// list of consent that the user is giving permissions for in the page. Will be passed in the RecordConsent RPC
	// that is called when Primary CTA is clicked
	// deprecated in favour of StaticConsent
	//
	// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/onboarding/sa_intro_screen_options.proto.
	ConsentTypes []deeplink.ConsentTypeUrl_ConsentType `protobuf:"varint,2,rep,packed,name=consent_types,json=consentTypes,proto3,enum=frontend.deeplink.ConsentTypeUrl_ConsentType" json:"consent_types,omitempty"`
	// header bar used in most onboarding screen
	HeaderBar *deeplink.HeaderBar `protobuf:"bytes,13,opt,name=header_bar,json=headerBar,proto3" json:"header_bar,omitempty"`
	// contains deeplink to render when back button is pressed
	BackButton *deeplink.BackAction `protobuf:"bytes,3,opt,name=back_button,json=backButton,proto3" json:"back_button,omitempty"`
	// left image showed on top of screen
	//
	// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/onboarding/sa_intro_screen_options.proto.
	LeftImage *types.VisualElement `protobuf:"bytes,4,opt,name=left_image,json=leftImage,proto3" json:"left_image,omitempty"`
	// right image showed on top of screen
	//
	// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/onboarding/sa_intro_screen_options.proto.
	RightImage *types.VisualElement `protobuf:"bytes,5,opt,name=right_image,json=rightImage,proto3" json:"right_image,omitempty"`
	// image showed on top of screen
	TopImage *types.VisualElement `protobuf:"bytes,12,opt,name=top_image,json=topImage,proto3" json:"top_image,omitempty"`
	// title component is image + text showed just below the two images
	TitleComponent      *ui.IconTextComponent                 `protobuf:"bytes,6,opt,name=title_component,json=titleComponent,proto3" json:"title_component,omitempty"`
	InformationGrid     *SaIntroScreenOptions_SectionTypeGrid `protobuf:"bytes,7,opt,name=information_grid,json=informationGrid,proto3" json:"information_grid,omitempty"`
	InformationCarousel *SectionTypeCarousel                  `protobuf:"bytes,8,opt,name=information_carousel,json=informationCarousel,proto3" json:"information_carousel,omitempty"`
	// learn more clickable component
	LearnMoreSnippet *ClickableSnippet `protobuf:"bytes,14,opt,name=learn_more_snippet,json=learnMoreSnippet,proto3" json:"learn_more_snippet,omitempty"`
	// terms and condition text that includes tappable web link to tnc of bank
	// deprecated in favour of StaticConsent
	//
	// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/onboarding/sa_intro_screen_options.proto.
	BankTnc *types.Text `protobuf:"bytes,9,opt,name=bank_tnc,json=bankTnc,proto3" json:"bank_tnc,omitempty"`
	// primary_cta upon swipe will call RecordConsent RPC
	PrimaryCta *deeplink.Cta `protobuf:"bytes,10,opt,name=primary_cta,json=primaryCta,proto3" json:"primary_cta,omitempty"`
	// icon + text that will be showed blow the primary CTA
	BottomText *ui.IconTextComponent `protobuf:"bytes,11,opt,name=bottom_text,json=bottomText,proto3" json:"bottom_text,omitempty"`
	// used to collect consent on screen, eg. If user has given the consent then we will trigger NRO account
	// Client sent the consent ids in RecordConsent RPC
	CheckboxConsents []*CheckboxConsent `protobuf:"bytes,15,rep,name=checkbox_consents,json=checkboxConsents,proto3" json:"checkbox_consents,omitempty"`
	// represent mandatory consent
	StaticConsents []*StaticConsent `protobuf:"bytes,16,rep,name=static_consents,json=staticConsents,proto3" json:"static_consents,omitempty"`
}

func (x *SaIntroScreenOptions) Reset() {
	*x = SaIntroScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaIntroScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaIntroScreenOptions) ProtoMessage() {}

func (x *SaIntroScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaIntroScreenOptions.ProtoReflect.Descriptor instead.
func (*SaIntroScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *SaIntroScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/onboarding/sa_intro_screen_options.proto.
func (x *SaIntroScreenOptions) GetConsentTypes() []deeplink.ConsentTypeUrl_ConsentType {
	if x != nil {
		return x.ConsentTypes
	}
	return nil
}

func (x *SaIntroScreenOptions) GetHeaderBar() *deeplink.HeaderBar {
	if x != nil {
		return x.HeaderBar
	}
	return nil
}

func (x *SaIntroScreenOptions) GetBackButton() *deeplink.BackAction {
	if x != nil {
		return x.BackButton
	}
	return nil
}

// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/onboarding/sa_intro_screen_options.proto.
func (x *SaIntroScreenOptions) GetLeftImage() *types.VisualElement {
	if x != nil {
		return x.LeftImage
	}
	return nil
}

// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/onboarding/sa_intro_screen_options.proto.
func (x *SaIntroScreenOptions) GetRightImage() *types.VisualElement {
	if x != nil {
		return x.RightImage
	}
	return nil
}

func (x *SaIntroScreenOptions) GetTopImage() *types.VisualElement {
	if x != nil {
		return x.TopImage
	}
	return nil
}

func (x *SaIntroScreenOptions) GetTitleComponent() *ui.IconTextComponent {
	if x != nil {
		return x.TitleComponent
	}
	return nil
}

func (x *SaIntroScreenOptions) GetInformationGrid() *SaIntroScreenOptions_SectionTypeGrid {
	if x != nil {
		return x.InformationGrid
	}
	return nil
}

func (x *SaIntroScreenOptions) GetInformationCarousel() *SectionTypeCarousel {
	if x != nil {
		return x.InformationCarousel
	}
	return nil
}

func (x *SaIntroScreenOptions) GetLearnMoreSnippet() *ClickableSnippet {
	if x != nil {
		return x.LearnMoreSnippet
	}
	return nil
}

// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/onboarding/sa_intro_screen_options.proto.
func (x *SaIntroScreenOptions) GetBankTnc() *types.Text {
	if x != nil {
		return x.BankTnc
	}
	return nil
}

func (x *SaIntroScreenOptions) GetPrimaryCta() *deeplink.Cta {
	if x != nil {
		return x.PrimaryCta
	}
	return nil
}

func (x *SaIntroScreenOptions) GetBottomText() *ui.IconTextComponent {
	if x != nil {
		return x.BottomText
	}
	return nil
}

func (x *SaIntroScreenOptions) GetCheckboxConsents() []*CheckboxConsent {
	if x != nil {
		return x.CheckboxConsents
	}
	return nil
}

func (x *SaIntroScreenOptions) GetStaticConsents() []*StaticConsent {
	if x != nil {
		return x.StaticConsents
	}
	return nil
}

// ClickableSnippet is a component that can be used to display a text, which has a clickable part at the end of it.
// The clickable part can navigate based on the deeplink mentioned.
// ex. "Want to open a NRO account? Learn More". Here "Learn More" is clickable and will open another deeplink when interacted with.
type ClickableSnippet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this is rendered to the left of clickable text
	NonClickableText *types.Text `protobuf:"bytes,1,opt,name=non_clickable_text,json=nonClickableText,proto3" json:"non_clickable_text,omitempty"`
	// the text that needs to be clickable. Deeplink mentioned inside will decide what the click will do.
	ClickableText *ui.IconTextComponent `protobuf:"bytes,2,opt,name=clickable_text,json=clickableText,proto3" json:"clickable_text,omitempty"`
}

func (x *ClickableSnippet) Reset() {
	*x = ClickableSnippet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClickableSnippet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClickableSnippet) ProtoMessage() {}

func (x *ClickableSnippet) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClickableSnippet.ProtoReflect.Descriptor instead.
func (*ClickableSnippet) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *ClickableSnippet) GetNonClickableText() *types.Text {
	if x != nil {
		return x.NonClickableText
	}
	return nil
}

func (x *ClickableSnippet) GetClickableText() *ui.IconTextComponent {
	if x != nil {
		return x.ClickableText
	}
	return nil
}

type CheckboxConsent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text *ui.IconTextComponent `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// list of consent ids that need to be sent back to backend in UpdateFormDetails RPCs
	ConsentIds []string `protobuf:"bytes,2,rep,name=consent_ids,json=consentIds,proto3" json:"consent_ids,omitempty"`
	// represent if the checkbox check or not by-default
	IsChecked bool `protobuf:"varint,3,opt,name=is_checked,json=isChecked,proto3" json:"is_checked,omitempty"`
}

func (x *CheckboxConsent) Reset() {
	*x = CheckboxConsent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckboxConsent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckboxConsent) ProtoMessage() {}

func (x *CheckboxConsent) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckboxConsent.ProtoReflect.Descriptor instead.
func (*CheckboxConsent) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *CheckboxConsent) GetText() *ui.IconTextComponent {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *CheckboxConsent) GetConsentIds() []string {
	if x != nil {
		return x.ConsentIds
	}
	return nil
}

func (x *CheckboxConsent) GetIsChecked() bool {
	if x != nil {
		return x.IsChecked
	}
	return false
}

type StaticConsent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text *ui.IconTextComponent `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// list of consent ids that need to be sent back to backend in UpdateFormDetails RPCs
	ConsentIds []string `protobuf:"bytes,2,rep,name=consent_ids,json=consentIds,proto3" json:"consent_ids,omitempty"`
}

func (x *StaticConsent) Reset() {
	*x = StaticConsent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaticConsent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaticConsent) ProtoMessage() {}

func (x *StaticConsent) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaticConsent.ProtoReflect.Descriptor instead.
func (*StaticConsent) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *StaticConsent) GetText() *ui.IconTextComponent {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *StaticConsent) GetConsentIds() []string {
	if x != nil {
		return x.ConsentIds
	}
	return nil
}

type SaIntroScreenOptions_SectionTypeGrid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title for grid
	SectionTitle *types.Text `protobuf:"bytes,1,opt,name=section_title,json=sectionTitle,proto3" json:"section_title,omitempty"`
	// list of components for grid
	InformationBlocks []*ui.VerticalIconTextComponent `protobuf:"bytes,2,rep,name=information_blocks,json=informationBlocks,proto3" json:"information_blocks,omitempty"`
	// bg colour for grid
	BgColor *widget.BackgroundColour `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// number of rows of information blocks in grid
	NumOfRows int32 `protobuf:"varint,4,opt,name=num_of_rows,json=numOfRows,proto3" json:"num_of_rows,omitempty"`
	// number of columns of blocks in grid
	NumOfCols int32 `protobuf:"varint,5,opt,name=num_of_cols,json=numOfCols,proto3" json:"num_of_cols,omitempty"`
	// list of icons
	PartnerLogos []*types.VisualElement `protobuf:"bytes,6,rep,name=partner_logos,json=partnerLogos,proto3" json:"partner_logos,omitempty"`
}

func (x *SaIntroScreenOptions_SectionTypeGrid) Reset() {
	*x = SaIntroScreenOptions_SectionTypeGrid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaIntroScreenOptions_SectionTypeGrid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaIntroScreenOptions_SectionTypeGrid) ProtoMessage() {}

func (x *SaIntroScreenOptions_SectionTypeGrid) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaIntroScreenOptions_SectionTypeGrid.ProtoReflect.Descriptor instead.
func (*SaIntroScreenOptions_SectionTypeGrid) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescGZIP(), []int{0, 0}
}

func (x *SaIntroScreenOptions_SectionTypeGrid) GetSectionTitle() *types.Text {
	if x != nil {
		return x.SectionTitle
	}
	return nil
}

func (x *SaIntroScreenOptions_SectionTypeGrid) GetInformationBlocks() []*ui.VerticalIconTextComponent {
	if x != nil {
		return x.InformationBlocks
	}
	return nil
}

func (x *SaIntroScreenOptions_SectionTypeGrid) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *SaIntroScreenOptions_SectionTypeGrid) GetNumOfRows() int32 {
	if x != nil {
		return x.NumOfRows
	}
	return 0
}

func (x *SaIntroScreenOptions_SectionTypeGrid) GetNumOfCols() int32 {
	if x != nil {
		return x.NumOfCols
	}
	return 0
}

func (x *SaIntroScreenOptions_SectionTypeGrid) GetPartnerLogos() []*types.VisualElement {
	if x != nil {
		return x.PartnerLogos
	}
	return nil
}

var File_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto protoreflect.FileDescriptor

var file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDesc = []byte{
	0x0a, 0x49, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x61,
	0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x27, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x48, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x66, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x74,
	0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x75, 0x69, 0x2f,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68,
	0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb4, 0x0c, 0x0a, 0x14, 0x53,
	0x61, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x56, 0x0a,
	0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x55, 0x72, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f,
	0x62, 0x61, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x52, 0x09, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42,
	0x61, 0x72, 0x12, 0x3e, 0x0a, 0x0b, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x42, 0x61, 0x63, 0x6b,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x42, 0x75, 0x74, 0x74,
	0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x0a, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x09, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x74, 0x6f, 0x70, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x08, 0x74, 0x6f, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x44, 0x0a, 0x0f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x0e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12,
	0x78, 0x0a, 0x10, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67,
	0x72, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x61, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x69, 0x64, 0x52, 0x0f, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x69, 0x64, 0x12, 0x6f, 0x0a, 0x14, 0x69, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65,
	0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x43, 0x61, 0x72,
	0x6f, 0x75, 0x73, 0x65, 0x6c, 0x52, 0x13, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x12, 0x67, 0x0a, 0x12, 0x6c, 0x65,
	0x61, 0x72, 0x6e, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x5f, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65,
	0x74, 0x52, 0x10, 0x6c, 0x65, 0x61, 0x72, 0x6e, 0x4d, 0x6f, 0x72, 0x65, 0x53, 0x6e, 0x69, 0x70,
	0x70, 0x65, 0x74, 0x12, 0x2a, 0x0a, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x74, 0x6e, 0x63, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x62, 0x61, 0x6e, 0x6b, 0x54, 0x6e, 0x63, 0x12,
	0x37, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0a, 0x70, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x74, 0x61, 0x12, 0x3c, 0x0a, 0x0b, 0x62, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x62, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x54, 0x65, 0x78, 0x74, 0x12, 0x65, 0x0a, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x62,
	0x6f, 0x78, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x38, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x62, 0x6f, 0x78, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x62, 0x6f, 0x78, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x5f, 0x0a,
	0x0f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x0e,
	0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0xd0,
	0x02, 0x0a, 0x0f, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72,
	0x69, 0x64, 0x12, 0x30, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x52, 0x0a, 0x12, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74,
	0x69, 0x63, 0x61, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x3c, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62,
	0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x6f, 0x66,
	0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x75, 0x6d,
	0x4f, 0x66, 0x52, 0x6f, 0x77, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x6f, 0x66,
	0x5f, 0x63, 0x6f, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x75, 0x6d,
	0x4f, 0x66, 0x43, 0x6f, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x6f,
	0x73, 0x22, 0x91, 0x01, 0x0a, 0x10, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x12, 0x39, 0x0a, 0x12, 0x6e, 0x6f, 0x6e, 0x5f, 0x63, 0x6c,
	0x69, 0x63, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x10, 0x6e, 0x6f, 0x6e, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x65, 0x78,
	0x74, 0x12, 0x42, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x61, 0x62, 0x6c,
	0x65, 0x54, 0x65, 0x78, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x62,
	0x6f, 0x78, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x22, 0x61, 0x0a, 0x0d, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x42, 0x8a, 0x01,
	0x0a, 0x42, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x50, 0x01, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescOnce sync.Once
	file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescData = file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDesc
)

func file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescGZIP() []byte {
	file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescOnce.Do(func() {
		file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescData)
	})
	return file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDescData
}

var file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_goTypes = []interface{}{
	(*SaIntroScreenOptions)(nil),                      // 0: types.deeplink_screen_option.onboarding.SaIntroScreenOptions
	(*ClickableSnippet)(nil),                          // 1: types.deeplink_screen_option.onboarding.ClickableSnippet
	(*CheckboxConsent)(nil),                           // 2: types.deeplink_screen_option.onboarding.CheckboxConsent
	(*StaticConsent)(nil),                             // 3: types.deeplink_screen_option.onboarding.StaticConsent
	(*SaIntroScreenOptions_SectionTypeGrid)(nil),      // 4: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.SectionTypeGrid
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 5: types.deeplink_screen_option.ScreenOptionHeader
	(deeplink.ConsentTypeUrl_ConsentType)(0),          // 6: frontend.deeplink.ConsentTypeUrl.ConsentType
	(*deeplink.HeaderBar)(nil),                        // 7: frontend.deeplink.HeaderBar
	(*deeplink.BackAction)(nil),                       // 8: frontend.deeplink.BackAction
	(*types.VisualElement)(nil),                       // 9: types.VisualElement
	(*ui.IconTextComponent)(nil),                      // 10: types.ui.IconTextComponent
	(*SectionTypeCarousel)(nil),                       // 11: types.deeplink_screen_option.onboarding.SectionTypeCarousel
	(*types.Text)(nil),                                // 12: types.Text
	(*deeplink.Cta)(nil),                              // 13: frontend.deeplink.Cta
	(*ui.VerticalIconTextComponent)(nil),              // 14: types.ui.VerticalIconTextComponent
	(*widget.BackgroundColour)(nil),                   // 15: types.ui.widget.BackgroundColour
}
var file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_depIdxs = []int32{
	5,  // 0: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.header:type_name -> types.deeplink_screen_option.ScreenOptionHeader
	6,  // 1: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.consent_types:type_name -> frontend.deeplink.ConsentTypeUrl.ConsentType
	7,  // 2: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.header_bar:type_name -> frontend.deeplink.HeaderBar
	8,  // 3: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.back_button:type_name -> frontend.deeplink.BackAction
	9,  // 4: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.left_image:type_name -> types.VisualElement
	9,  // 5: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.right_image:type_name -> types.VisualElement
	9,  // 6: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.top_image:type_name -> types.VisualElement
	10, // 7: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.title_component:type_name -> types.ui.IconTextComponent
	4,  // 8: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.information_grid:type_name -> types.deeplink_screen_option.onboarding.SaIntroScreenOptions.SectionTypeGrid
	11, // 9: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.information_carousel:type_name -> types.deeplink_screen_option.onboarding.SectionTypeCarousel
	1,  // 10: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.learn_more_snippet:type_name -> types.deeplink_screen_option.onboarding.ClickableSnippet
	12, // 11: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.bank_tnc:type_name -> types.Text
	13, // 12: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.primary_cta:type_name -> frontend.deeplink.Cta
	10, // 13: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.bottom_text:type_name -> types.ui.IconTextComponent
	2,  // 14: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.checkbox_consents:type_name -> types.deeplink_screen_option.onboarding.CheckboxConsent
	3,  // 15: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.static_consents:type_name -> types.deeplink_screen_option.onboarding.StaticConsent
	12, // 16: types.deeplink_screen_option.onboarding.ClickableSnippet.non_clickable_text:type_name -> types.Text
	10, // 17: types.deeplink_screen_option.onboarding.ClickableSnippet.clickable_text:type_name -> types.ui.IconTextComponent
	10, // 18: types.deeplink_screen_option.onboarding.CheckboxConsent.text:type_name -> types.ui.IconTextComponent
	10, // 19: types.deeplink_screen_option.onboarding.StaticConsent.text:type_name -> types.ui.IconTextComponent
	12, // 20: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.SectionTypeGrid.section_title:type_name -> types.Text
	14, // 21: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.SectionTypeGrid.information_blocks:type_name -> types.ui.VerticalIconTextComponent
	15, // 22: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.SectionTypeGrid.bg_color:type_name -> types.ui.widget.BackgroundColour
	9,  // 23: types.deeplink_screen_option.onboarding.SaIntroScreenOptions.SectionTypeGrid.partner_logos:type_name -> types.VisualElement
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_init() }
func file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_init() {
	if File_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto != nil {
		return
	}
	file_api_types_deeplink_screen_option_onboarding_fi_lite_screen_options_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaIntroScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClickableSnippet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckboxConsent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaticConsent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaIntroScreenOptions_SectionTypeGrid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_goTypes,
		DependencyIndexes: file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_depIdxs,
		MessageInfos:      file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_msgTypes,
	}.Build()
	File_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto = out.File
	file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_rawDesc = nil
	file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_goTypes = nil
	file_api_types_deeplink_screen_option_onboarding_sa_intro_screen_options_proto_depIdxs = nil
}
