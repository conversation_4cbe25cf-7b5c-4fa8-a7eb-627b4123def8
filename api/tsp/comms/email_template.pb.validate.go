// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/tsp/comms/email_template.proto

package comms

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EmailOption with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EmailOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmailOption with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EmailOptionMultiError, or
// nil if none found.
func (m *EmailOption) ValidateAll() error {
	return m.validate(true)
}

func (m *EmailOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Option.(type) {
	case *EmailOption_SampleEmailOption:
		if v == nil {
			err := EmailOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSampleEmailOption()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EmailOptionValidationError{
						field:  "SampleEmailOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EmailOptionValidationError{
						field:  "SampleEmailOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSampleEmailOption()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EmailOptionValidationError{
					field:  "SampleEmailOption",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *EmailOption_LoanConfirmationEmailOption:
		if v == nil {
			err := EmailOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLoanConfirmationEmailOption()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EmailOptionValidationError{
						field:  "LoanConfirmationEmailOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EmailOptionValidationError{
						field:  "LoanConfirmationEmailOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLoanConfirmationEmailOption()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EmailOptionValidationError{
					field:  "LoanConfirmationEmailOption",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return EmailOptionMultiError(errors)
	}

	return nil
}

// EmailOptionMultiError is an error wrapping multiple validation errors
// returned by EmailOption.ValidateAll() if the designated constraints aren't met.
type EmailOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmailOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmailOptionMultiError) AllErrors() []error { return m }

// EmailOptionValidationError is the validation error returned by
// EmailOption.Validate if the designated constraints aren't met.
type EmailOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmailOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmailOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmailOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmailOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmailOptionValidationError) ErrorName() string { return "EmailOptionValidationError" }

// Error satisfies the builtin error interface
func (e EmailOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmailOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmailOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmailOptionValidationError{}

// Validate checks the field values on SampleEmailOption with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SampleEmailOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SampleEmailOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SampleEmailOptionMultiError, or nil if none found.
func (m *SampleEmailOption) ValidateAll() error {
	return m.validate(true)
}

func (m *SampleEmailOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetEmailType() != 1 {
		err := SampleEmailOptionValidationError{
			field:  "EmailType",
			reason: "value must equal EMAIL_TYPE_SAMPLE",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Option.(type) {
	case *SampleEmailOption_SampleEmailOptionV1:
		if v == nil {
			err := SampleEmailOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSampleEmailOptionV1()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SampleEmailOptionValidationError{
						field:  "SampleEmailOptionV1",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SampleEmailOptionValidationError{
						field:  "SampleEmailOptionV1",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSampleEmailOptionV1()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SampleEmailOptionValidationError{
					field:  "SampleEmailOptionV1",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SampleEmailOptionMultiError(errors)
	}

	return nil
}

// SampleEmailOptionMultiError is an error wrapping multiple validation errors
// returned by SampleEmailOption.ValidateAll() if the designated constraints
// aren't met.
type SampleEmailOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SampleEmailOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SampleEmailOptionMultiError) AllErrors() []error { return m }

// SampleEmailOptionValidationError is the validation error returned by
// SampleEmailOption.Validate if the designated constraints aren't met.
type SampleEmailOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SampleEmailOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SampleEmailOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SampleEmailOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SampleEmailOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SampleEmailOptionValidationError) ErrorName() string {
	return "SampleEmailOptionValidationError"
}

// Error satisfies the builtin error interface
func (e SampleEmailOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSampleEmailOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SampleEmailOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SampleEmailOptionValidationError{}

// Validate checks the field values on SampleEmailOptionV1 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SampleEmailOptionV1) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SampleEmailOptionV1 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SampleEmailOptionV1MultiError, or nil if none found.
func (m *SampleEmailOptionV1) ValidateAll() error {
	return m.validate(true)
}

func (m *SampleEmailOptionV1) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTemplateVersion() != 1 {
		err := SampleEmailOptionV1ValidationError{
			field:  "TemplateVersion",
			reason: "value must equal VERSION_V1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SampleEmailOptionV1MultiError(errors)
	}

	return nil
}

// SampleEmailOptionV1MultiError is an error wrapping multiple validation
// errors returned by SampleEmailOptionV1.ValidateAll() if the designated
// constraints aren't met.
type SampleEmailOptionV1MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SampleEmailOptionV1MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SampleEmailOptionV1MultiError) AllErrors() []error { return m }

// SampleEmailOptionV1ValidationError is the validation error returned by
// SampleEmailOptionV1.Validate if the designated constraints aren't met.
type SampleEmailOptionV1ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SampleEmailOptionV1ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SampleEmailOptionV1ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SampleEmailOptionV1ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SampleEmailOptionV1ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SampleEmailOptionV1ValidationError) ErrorName() string {
	return "SampleEmailOptionV1ValidationError"
}

// Error satisfies the builtin error interface
func (e SampleEmailOptionV1ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSampleEmailOptionV1.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SampleEmailOptionV1ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SampleEmailOptionV1ValidationError{}

// Validate checks the field values on LoanConfirmationEmailOption with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanConfirmationEmailOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanConfirmationEmailOption with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanConfirmationEmailOptionMultiError, or nil if none found.
func (m *LoanConfirmationEmailOption) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanConfirmationEmailOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetEmailType() != 2 {
		err := LoanConfirmationEmailOptionValidationError{
			field:  "EmailType",
			reason: "value must equal LOAN_CONFIRMATION_EMAIL",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Option.(type) {
	case *LoanConfirmationEmailOption_LoansPaymentFileEmailV1:
		if v == nil {
			err := LoanConfirmationEmailOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLoansPaymentFileEmailV1()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanConfirmationEmailOptionValidationError{
						field:  "LoansPaymentFileEmailV1",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanConfirmationEmailOptionValidationError{
						field:  "LoansPaymentFileEmailV1",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLoansPaymentFileEmailV1()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanConfirmationEmailOptionValidationError{
					field:  "LoansPaymentFileEmailV1",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return LoanConfirmationEmailOptionMultiError(errors)
	}

	return nil
}

// LoanConfirmationEmailOptionMultiError is an error wrapping multiple
// validation errors returned by LoanConfirmationEmailOption.ValidateAll() if
// the designated constraints aren't met.
type LoanConfirmationEmailOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanConfirmationEmailOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanConfirmationEmailOptionMultiError) AllErrors() []error { return m }

// LoanConfirmationEmailOptionValidationError is the validation error returned
// by LoanConfirmationEmailOption.Validate if the designated constraints
// aren't met.
type LoanConfirmationEmailOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanConfirmationEmailOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanConfirmationEmailOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanConfirmationEmailOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanConfirmationEmailOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanConfirmationEmailOptionValidationError) ErrorName() string {
	return "LoanConfirmationEmailOptionValidationError"
}

// Error satisfies the builtin error interface
func (e LoanConfirmationEmailOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanConfirmationEmailOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanConfirmationEmailOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanConfirmationEmailOptionValidationError{}

// Validate checks the field values on LoanConfirmationEmailV1 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanConfirmationEmailV1) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanConfirmationEmailV1 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanConfirmationEmailV1MultiError, or nil if none found.
func (m *LoanConfirmationEmailV1) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanConfirmationEmailV1) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTemplateVersion() != 1 {
		err := LoanConfirmationEmailV1ValidationError{
			field:  "TemplateVersion",
			reason: "value must equal VERSION_V1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Name

	// no validation rules for LastFourDigitsOfAccountNumber

	// no validation rules for ContactNumber

	// no validation rules for LspLogo

	// no validation rules for LspName

	if len(errors) > 0 {
		return LoanConfirmationEmailV1MultiError(errors)
	}

	return nil
}

// LoanConfirmationEmailV1MultiError is an error wrapping multiple validation
// errors returned by LoanConfirmationEmailV1.ValidateAll() if the designated
// constraints aren't met.
type LoanConfirmationEmailV1MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanConfirmationEmailV1MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanConfirmationEmailV1MultiError) AllErrors() []error { return m }

// LoanConfirmationEmailV1ValidationError is the validation error returned by
// LoanConfirmationEmailV1.Validate if the designated constraints aren't met.
type LoanConfirmationEmailV1ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanConfirmationEmailV1ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanConfirmationEmailV1ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanConfirmationEmailV1ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanConfirmationEmailV1ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanConfirmationEmailV1ValidationError) ErrorName() string {
	return "LoanConfirmationEmailV1ValidationError"
}

// Error satisfies the builtin error interface
func (e LoanConfirmationEmailV1ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanConfirmationEmailV1.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanConfirmationEmailV1ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanConfirmationEmailV1ValidationError{}
