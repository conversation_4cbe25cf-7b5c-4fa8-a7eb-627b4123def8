// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/tsp/comms/email_template.proto

package comms

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// All email templates will be defined here which the clients can use to specify what they want to use
// Each template will have corresponding options to set the values
type EmailType int32

const (
	EmailType_EMAIL_TYPE_UNSPECIFIED EmailType = 0
	EmailType_EMAIL_TYPE_SAMPLE      EmailType = 1
	// email to be sent after nbfc loan is disbursed
	EmailType_LOAN_CONFIRMATION_EMAIL EmailType = 2
)

// Enum value maps for EmailType.
var (
	EmailType_name = map[int32]string{
		0: "EMAIL_TYPE_UNSPECIFIED",
		1: "EMAIL_TYPE_SAMPLE",
		2: "LOAN_CONFIRMATION_EMAIL",
	}
	EmailType_value = map[string]int32{
		"EMAIL_TYPE_UNSPECIFIED":  0,
		"EMAIL_TYPE_SAMPLE":       1,
		"LOAN_CONFIRMATION_EMAIL": 2,
	}
)

func (x EmailType) Enum() *EmailType {
	p := new(EmailType)
	*p = x
	return p
}

func (x EmailType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tsp_comms_email_template_proto_enumTypes[0].Descriptor()
}

func (EmailType) Type() protoreflect.EnumType {
	return &file_api_tsp_comms_email_template_proto_enumTypes[0]
}

func (x EmailType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailType.Descriptor instead.
func (EmailType) EnumDescriptor() ([]byte, []int) {
	return file_api_tsp_comms_email_template_proto_rawDescGZIP(), []int{0}
}

type EmailOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template options which will replace the variables in the template
	//
	// Types that are assignable to Option:
	//
	//	*EmailOption_SampleEmailOption
	//	*EmailOption_LoanConfirmationEmailOption
	Option isEmailOption_Option `protobuf_oneof:"option"`
}

func (x *EmailOption) Reset() {
	*x = EmailOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tsp_comms_email_template_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailOption) ProtoMessage() {}

func (x *EmailOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_tsp_comms_email_template_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailOption.ProtoReflect.Descriptor instead.
func (*EmailOption) Descriptor() ([]byte, []int) {
	return file_api_tsp_comms_email_template_proto_rawDescGZIP(), []int{0}
}

func (m *EmailOption) GetOption() isEmailOption_Option {
	if m != nil {
		return m.Option
	}
	return nil
}

func (x *EmailOption) GetSampleEmailOption() *SampleEmailOption {
	if x, ok := x.GetOption().(*EmailOption_SampleEmailOption); ok {
		return x.SampleEmailOption
	}
	return nil
}

func (x *EmailOption) GetLoanConfirmationEmailOption() *LoanConfirmationEmailOption {
	if x, ok := x.GetOption().(*EmailOption_LoanConfirmationEmailOption); ok {
		return x.LoanConfirmationEmailOption
	}
	return nil
}

type isEmailOption_Option interface {
	isEmailOption_Option()
}

type EmailOption_SampleEmailOption struct {
	// todo: to be remove, added just for reference
	SampleEmailOption *SampleEmailOption `protobuf:"bytes,1,opt,name=sample_email_option,json=sampleEmailOption,proto3,oneof"`
}

type EmailOption_LoanConfirmationEmailOption struct {
	LoanConfirmationEmailOption *LoanConfirmationEmailOption `protobuf:"bytes,2,opt,name=loan_confirmation_email_option,json=loanConfirmationEmailOption,proto3,oneof"`
}

func (*EmailOption_SampleEmailOption) isEmailOption_Option() {}

func (*EmailOption_LoanConfirmationEmailOption) isEmailOption_Option() {}

// each template will have certain options which are the variables that will be filled in the template
type SampleEmailOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type of template
	EmailType EmailType `protobuf:"varint,1,opt,name=email_type,json=emailType,proto3,enum=tsp.comms.EmailType" json:"email_type,omitempty"`
	// Types that are assignable to Option:
	//
	//	*SampleEmailOption_SampleEmailOptionV1
	Option isSampleEmailOption_Option `protobuf_oneof:"option"`
}

func (x *SampleEmailOption) Reset() {
	*x = SampleEmailOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tsp_comms_email_template_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SampleEmailOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleEmailOption) ProtoMessage() {}

func (x *SampleEmailOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_tsp_comms_email_template_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleEmailOption.ProtoReflect.Descriptor instead.
func (*SampleEmailOption) Descriptor() ([]byte, []int) {
	return file_api_tsp_comms_email_template_proto_rawDescGZIP(), []int{1}
}

func (x *SampleEmailOption) GetEmailType() EmailType {
	if x != nil {
		return x.EmailType
	}
	return EmailType_EMAIL_TYPE_UNSPECIFIED
}

func (m *SampleEmailOption) GetOption() isSampleEmailOption_Option {
	if m != nil {
		return m.Option
	}
	return nil
}

func (x *SampleEmailOption) GetSampleEmailOptionV1() *SampleEmailOptionV1 {
	if x, ok := x.GetOption().(*SampleEmailOption_SampleEmailOptionV1); ok {
		return x.SampleEmailOptionV1
	}
	return nil
}

type isSampleEmailOption_Option interface {
	isSampleEmailOption_Option()
}

type SampleEmailOption_SampleEmailOptionV1 struct {
	SampleEmailOptionV1 *SampleEmailOptionV1 `protobuf:"bytes,2,opt,name=sample_email_option_v1,json=sampleEmailOptionV1,proto3,oneof"`
}

func (*SampleEmailOption_SampleEmailOptionV1) isSampleEmailOption_Option() {}

type SampleEmailOptionV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateVersion TemplateVersion `protobuf:"varint,1,opt,name=template_version,json=templateVersion,proto3,enum=tsp.comms.TemplateVersion" json:"template_version,omitempty"`
}

func (x *SampleEmailOptionV1) Reset() {
	*x = SampleEmailOptionV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tsp_comms_email_template_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SampleEmailOptionV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleEmailOptionV1) ProtoMessage() {}

func (x *SampleEmailOptionV1) ProtoReflect() protoreflect.Message {
	mi := &file_api_tsp_comms_email_template_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleEmailOptionV1.ProtoReflect.Descriptor instead.
func (*SampleEmailOptionV1) Descriptor() ([]byte, []int) {
	return file_api_tsp_comms_email_template_proto_rawDescGZIP(), []int{2}
}

func (x *SampleEmailOptionV1) GetTemplateVersion() TemplateVersion {
	if x != nil {
		return x.TemplateVersion
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

type LoanConfirmationEmailOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmailType EmailType `protobuf:"varint,1,opt,name=email_type,json=emailType,proto3,enum=tsp.comms.EmailType" json:"email_type,omitempty"`
	// Types that are assignable to Option:
	//
	//	*LoanConfirmationEmailOption_LoansPaymentFileEmailV1
	Option isLoanConfirmationEmailOption_Option `protobuf_oneof:"option"`
}

func (x *LoanConfirmationEmailOption) Reset() {
	*x = LoanConfirmationEmailOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tsp_comms_email_template_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanConfirmationEmailOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanConfirmationEmailOption) ProtoMessage() {}

func (x *LoanConfirmationEmailOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_tsp_comms_email_template_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanConfirmationEmailOption.ProtoReflect.Descriptor instead.
func (*LoanConfirmationEmailOption) Descriptor() ([]byte, []int) {
	return file_api_tsp_comms_email_template_proto_rawDescGZIP(), []int{3}
}

func (x *LoanConfirmationEmailOption) GetEmailType() EmailType {
	if x != nil {
		return x.EmailType
	}
	return EmailType_EMAIL_TYPE_UNSPECIFIED
}

func (m *LoanConfirmationEmailOption) GetOption() isLoanConfirmationEmailOption_Option {
	if m != nil {
		return m.Option
	}
	return nil
}

func (x *LoanConfirmationEmailOption) GetLoansPaymentFileEmailV1() *LoanConfirmationEmailV1 {
	if x, ok := x.GetOption().(*LoanConfirmationEmailOption_LoansPaymentFileEmailV1); ok {
		return x.LoansPaymentFileEmailV1
	}
	return nil
}

type isLoanConfirmationEmailOption_Option interface {
	isLoanConfirmationEmailOption_Option()
}

type LoanConfirmationEmailOption_LoansPaymentFileEmailV1 struct {
	LoansPaymentFileEmailV1 *LoanConfirmationEmailV1 `protobuf:"bytes,2,opt,name=loans_payment_file_email_v1,json=loansPaymentFileEmailV1,proto3,oneof"`
}

func (*LoanConfirmationEmailOption_LoansPaymentFileEmailV1) isLoanConfirmationEmailOption_Option() {}

type LoanConfirmationEmailV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateVersion               TemplateVersion `protobuf:"varint,1,opt,name=template_version,json=templateVersion,proto3,enum=tsp.comms.TemplateVersion" json:"template_version,omitempty"`
	Name                          string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LastFourDigitsOfAccountNumber string          `protobuf:"bytes,3,opt,name=last_four_digits_of_account_number,json=lastFourDigitsOfAccountNumber,proto3" json:"last_four_digits_of_account_number,omitempty"`
	ContactNumber                 string          `protobuf:"bytes,4,opt,name=contact_number,json=contactNumber,proto3" json:"contact_number,omitempty"`
	LspLogo                       string          `protobuf:"bytes,5,opt,name=lsp_logo,json=lspLogo,proto3" json:"lsp_logo,omitempty"`
	LspName                       string          `protobuf:"bytes,6,opt,name=lsp_name,json=lspName,proto3" json:"lsp_name,omitempty"`
}

func (x *LoanConfirmationEmailV1) Reset() {
	*x = LoanConfirmationEmailV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tsp_comms_email_template_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanConfirmationEmailV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanConfirmationEmailV1) ProtoMessage() {}

func (x *LoanConfirmationEmailV1) ProtoReflect() protoreflect.Message {
	mi := &file_api_tsp_comms_email_template_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanConfirmationEmailV1.ProtoReflect.Descriptor instead.
func (*LoanConfirmationEmailV1) Descriptor() ([]byte, []int) {
	return file_api_tsp_comms_email_template_proto_rawDescGZIP(), []int{4}
}

func (x *LoanConfirmationEmailV1) GetTemplateVersion() TemplateVersion {
	if x != nil {
		return x.TemplateVersion
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (x *LoanConfirmationEmailV1) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LoanConfirmationEmailV1) GetLastFourDigitsOfAccountNumber() string {
	if x != nil {
		return x.LastFourDigitsOfAccountNumber
	}
	return ""
}

func (x *LoanConfirmationEmailV1) GetContactNumber() string {
	if x != nil {
		return x.ContactNumber
	}
	return ""
}

func (x *LoanConfirmationEmailV1) GetLspLogo() string {
	if x != nil {
		return x.LspLogo
	}
	return ""
}

func (x *LoanConfirmationEmailV1) GetLspName() string {
	if x != nil {
		return x.LspName
	}
	return ""
}

var File_api_tsp_comms_email_template_proto protoreflect.FileDescriptor

var file_api_tsp_comms_email_template_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x73, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x74, 0x73, 0x70, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x1a,
	0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x73, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xd6, 0x01, 0x0a, 0x0b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x13, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x74, 0x73, 0x70, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x53, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00,
	0x52, 0x11, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x6d, 0x0a, 0x1e, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x73,
	0x70, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x1b, 0x6c, 0x6f, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x08, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb3, 0x01, 0x0a,
	0x11, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0a, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x73, 0x70, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x73, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x55, 0x0a, 0x16, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x73, 0x70, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x53, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56,
	0x31, 0x48, 0x00, 0x52, 0x13, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x42, 0x08, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x66, 0x0a, 0x13, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x12, 0x4f, 0x0a, 0x10, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x73, 0x70, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xca, 0x01, 0x0a, 0x1b, 0x4c,
	0x6f, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0a, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x74, 0x73, 0x70, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x08, 0x02, 0x52, 0x09,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x62, 0x0a, 0x1b, 0x6c, 0x6f, 0x61,
	0x6e, 0x73, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x74, 0x73, 0x70, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x56, 0x31, 0x48, 0x00, 0x52, 0x17, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x31, 0x42, 0x08, 0x0a,
	0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa6, 0x02, 0x0a, 0x17, 0x4c, 0x6f, 0x61, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x56, 0x31, 0x12, 0x4f, 0x0a, 0x10, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x74, 0x73, 0x70, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x49, 0x0a, 0x22, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x66, 0x6f, 0x75, 0x72, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x6c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x44, 0x69,
	0x67, 0x69, 0x74, 0x73, 0x4f, 0x66, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x73,
	0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x73,
	0x70, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x2a, 0x5b, 0x0a, 0x09, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x16, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x41, 0x4d, 0x50, 0x4c, 0x45, 0x10, 0x01,
	0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x42, 0x4c, 0x0a,
	0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x73, 0x70, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5a, 0x24, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x73, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_tsp_comms_email_template_proto_rawDescOnce sync.Once
	file_api_tsp_comms_email_template_proto_rawDescData = file_api_tsp_comms_email_template_proto_rawDesc
)

func file_api_tsp_comms_email_template_proto_rawDescGZIP() []byte {
	file_api_tsp_comms_email_template_proto_rawDescOnce.Do(func() {
		file_api_tsp_comms_email_template_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_tsp_comms_email_template_proto_rawDescData)
	})
	return file_api_tsp_comms_email_template_proto_rawDescData
}

var file_api_tsp_comms_email_template_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_tsp_comms_email_template_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_tsp_comms_email_template_proto_goTypes = []interface{}{
	(EmailType)(0),                      // 0: tsp.comms.EmailType
	(*EmailOption)(nil),                 // 1: tsp.comms.EmailOption
	(*SampleEmailOption)(nil),           // 2: tsp.comms.SampleEmailOption
	(*SampleEmailOptionV1)(nil),         // 3: tsp.comms.SampleEmailOptionV1
	(*LoanConfirmationEmailOption)(nil), // 4: tsp.comms.LoanConfirmationEmailOption
	(*LoanConfirmationEmailV1)(nil),     // 5: tsp.comms.LoanConfirmationEmailV1
	(TemplateVersion)(0),                // 6: tsp.comms.TemplateVersion
}
var file_api_tsp_comms_email_template_proto_depIdxs = []int32{
	2, // 0: tsp.comms.EmailOption.sample_email_option:type_name -> tsp.comms.SampleEmailOption
	4, // 1: tsp.comms.EmailOption.loan_confirmation_email_option:type_name -> tsp.comms.LoanConfirmationEmailOption
	0, // 2: tsp.comms.SampleEmailOption.email_type:type_name -> tsp.comms.EmailType
	3, // 3: tsp.comms.SampleEmailOption.sample_email_option_v1:type_name -> tsp.comms.SampleEmailOptionV1
	6, // 4: tsp.comms.SampleEmailOptionV1.template_version:type_name -> tsp.comms.TemplateVersion
	0, // 5: tsp.comms.LoanConfirmationEmailOption.email_type:type_name -> tsp.comms.EmailType
	5, // 6: tsp.comms.LoanConfirmationEmailOption.loans_payment_file_email_v1:type_name -> tsp.comms.LoanConfirmationEmailV1
	6, // 7: tsp.comms.LoanConfirmationEmailV1.template_version:type_name -> tsp.comms.TemplateVersion
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_tsp_comms_email_template_proto_init() }
func file_api_tsp_comms_email_template_proto_init() {
	if File_api_tsp_comms_email_template_proto != nil {
		return
	}
	file_api_tsp_comms_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_tsp_comms_email_template_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tsp_comms_email_template_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SampleEmailOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tsp_comms_email_template_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SampleEmailOptionV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tsp_comms_email_template_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanConfirmationEmailOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tsp_comms_email_template_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanConfirmationEmailV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_tsp_comms_email_template_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*EmailOption_SampleEmailOption)(nil),
		(*EmailOption_LoanConfirmationEmailOption)(nil),
	}
	file_api_tsp_comms_email_template_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*SampleEmailOption_SampleEmailOptionV1)(nil),
	}
	file_api_tsp_comms_email_template_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*LoanConfirmationEmailOption_LoansPaymentFileEmailV1)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_tsp_comms_email_template_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_tsp_comms_email_template_proto_goTypes,
		DependencyIndexes: file_api_tsp_comms_email_template_proto_depIdxs,
		EnumInfos:         file_api_tsp_comms_email_template_proto_enumTypes,
		MessageInfos:      file_api_tsp_comms_email_template_proto_msgTypes,
	}.Build()
	File_api_tsp_comms_email_template_proto = out.File
	file_api_tsp_comms_email_template_proto_rawDesc = nil
	file_api_tsp_comms_email_template_proto_goTypes = nil
	file_api_tsp_comms_email_template_proto_depIdxs = nil
}
