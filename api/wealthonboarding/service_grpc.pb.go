// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/wealthonboarding/service.proto

package wealthonboarding

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	WealthOnboarding_GetOnboardingStatus_FullMethodName               = "/wealthonboarding.WealthOnboarding/GetOnboardingStatus"
	WealthOnboarding_GetNextOnboardingStep_FullMethodName             = "/wealthonboarding.WealthOnboarding/GetNextOnboardingStep"
	WealthOnboarding_CollectDataFromCustomer_FullMethodName           = "/wealthonboarding.WealthOnboarding/CollectDataFromCustomer"
	WealthOnboarding_CollectDataFromCustomerWithStream_FullMethodName = "/wealthonboarding.WealthOnboarding/CollectDataFromCustomerWithStream"
	WealthOnboarding_GetInvestmentOnbStatus_FullMethodName            = "/wealthonboarding.WealthOnboarding/GetInvestmentOnbStatus"
	WealthOnboarding_UpdateOnbStatus_FullMethodName                   = "/wealthonboarding.WealthOnboarding/UpdateOnbStatus"
	WealthOnboarding_GetInvestmentData_FullMethodName                 = "/wealthonboarding.WealthOnboarding/GetInvestmentData"
	WealthOnboarding_GetOnboardingTroubleshootDetails_FullMethodName  = "/wealthonboarding.WealthOnboarding/GetOnboardingTroubleshootDetails"
	WealthOnboarding_DownloadDigilockerDocs_FullMethodName            = "/wealthonboarding.WealthOnboarding/DownloadDigilockerDocs"
	WealthOnboarding_GetInvestmentDataV2_FullMethodName               = "/wealthonboarding.WealthOnboarding/GetInvestmentDataV2"
	WealthOnboarding_InitiateDocumentExtraction_FullMethodName        = "/wealthonboarding.WealthOnboarding/InitiateDocumentExtraction"
	WealthOnboarding_GetDocument_FullMethodName                       = "/wealthonboarding.WealthOnboarding/GetDocument"
	WealthOnboarding_GetOrCreateUser_FullMethodName                   = "/wealthonboarding.WealthOnboarding/GetOrCreateUser"
)

// WealthOnboardingClient is the client API for WealthOnboarding service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WealthOnboardingClient interface {
	// will be used to get the wealth onboarding status of the customer
	GetOnboardingStatus(ctx context.Context, in *GetOnboardingStatusRequest, opts ...grpc.CallOption) (*GetOnboardingStatusResponse, error)
	// will be used to get the next step for onboarding
	GetNextOnboardingStep(ctx context.Context, in *GetNextOnboardingStatusRequest, opts ...grpc.CallOption) (*GetNextOnboardingStatusResponse, error)
	// primary RPC to  collect data from customer
	CollectDataFromCustomer(ctx context.Context, in *CollectDataFromCustomerRequest, opts ...grpc.CallOption) (*CollectDataFromCustomerResponse, error)
	// fallback RPC for collecting large data from customer that don't fit into standard GRPC msg limit
	CollectDataFromCustomerWithStream(ctx context.Context, opts ...grpc.CallOption) (WealthOnboarding_CollectDataFromCustomerWithStreamClient, error)
	// GetInvestmentOnbStatus is to be used by the core investment service for fetching the current investment status of the user
	// It starts the investment onboarding of a user, if not already done. It supports bulk mode as well
	GetInvestmentOnbStatus(ctx context.Context, in *GetInvestmentOnbStatusRequest, opts ...grpc.CallOption) (*GetInvestmentOnbStatusResponse, error)
	// To update the onboarding status of a user
	UpdateOnbStatus(ctx context.Context, in *UpdateOnbStatusRequest, opts ...grpc.CallOption) (*UpdateOnbStatusResponse, error)
	// GetInvestmentData returns the pre investment details required for an actor
	// currently only details for fatca, order feed, elog and credit mis file is supported
	GetInvestmentData(ctx context.Context, in *GetInvestmentDataRequest, opts ...grpc.CallOption) (*GetInvestmentDataResponse, error)
	// RPC to get onboarding details and oboarding step details
	GetOnboardingTroubleshootDetails(ctx context.Context, in *GetOnboardingTroubleshootDetailsRequest, opts ...grpc.CallOption) (*GetOnboardingTroubleshootDetailsResponse, error)
	// RPC to get access tokens from DigiLocker using the authorization code returned by DigiLocker when
	// a user logs into DigiLocker and grants us permission.
	// These tokens can then be later used in other DigiLocker API calls to get user's documents, etc.
	// NOTE: In an earlier implementation of this RPC the DigiLocker documents were actually downloaded here itself, but
	// since downloading of documents can throw transient errors, this implementation has been moved to inside the wealth onboarding orchestrator for automatic retries.
	// Ideally this RPC should now be perhaps named as GetDigilockerAccess, but that would also mean a similar naming for the FE RPC and a client change.
	DownloadDigilockerDocs(ctx context.Context, in *DownloadDigilockerDocsRequest, opts ...grpc.CallOption) (*DownloadDigilockerDocsResponse, error)
	// GetInvestmentDataV2 returns the pre investment details required for an actor
	// currently only details for fatca, order feed, elog and credit mis file is supported
	// It also provides the current status of the data present on the wealth onboarding end
	GetInvestmentDataV2(ctx context.Context, in *GetInvestmentDataV2Request, opts ...grpc.CallOption) (*GetInvestmentDataV2Response, error)
	// Wealth On-boarding has user's kyc documents like pan card, Aadhar card etc, other domain services may need to access these documents
	// InitiateDocumentExtraction begins document extraction process,
	// document will be extracted from kra docket if not already present in db
	// actual document will be part of GetDocument rpc response.
	// currently only pan card extraction is supported
	InitiateDocumentExtraction(ctx context.Context, in *InitiateDocumentExtractionRequest, opts ...grpc.CallOption) (*InitiateDocumentExtractionResponse, error)
	// GetDocument returns an s3 url for the requested document
	GetDocument(ctx context.Context, in *GetDocumentRequest, opts ...grpc.CallOption) (*GetDocumentResponse, error)
	// GetOrCreateUser rpc returns the user by actor id if found, else it creates the user.
	GetOrCreateUser(ctx context.Context, in *GetOrCreateUserRequest, opts ...grpc.CallOption) (*GetOrCreateUserResponse, error)
}

type wealthOnboardingClient struct {
	cc grpc.ClientConnInterface
}

func NewWealthOnboardingClient(cc grpc.ClientConnInterface) WealthOnboardingClient {
	return &wealthOnboardingClient{cc}
}

func (c *wealthOnboardingClient) GetOnboardingStatus(ctx context.Context, in *GetOnboardingStatusRequest, opts ...grpc.CallOption) (*GetOnboardingStatusResponse, error) {
	out := new(GetOnboardingStatusResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_GetOnboardingStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) GetNextOnboardingStep(ctx context.Context, in *GetNextOnboardingStatusRequest, opts ...grpc.CallOption) (*GetNextOnboardingStatusResponse, error) {
	out := new(GetNextOnboardingStatusResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_GetNextOnboardingStep_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) CollectDataFromCustomer(ctx context.Context, in *CollectDataFromCustomerRequest, opts ...grpc.CallOption) (*CollectDataFromCustomerResponse, error) {
	out := new(CollectDataFromCustomerResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_CollectDataFromCustomer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) CollectDataFromCustomerWithStream(ctx context.Context, opts ...grpc.CallOption) (WealthOnboarding_CollectDataFromCustomerWithStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &WealthOnboarding_ServiceDesc.Streams[0], WealthOnboarding_CollectDataFromCustomerWithStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &wealthOnboardingCollectDataFromCustomerWithStreamClient{stream}
	return x, nil
}

type WealthOnboarding_CollectDataFromCustomerWithStreamClient interface {
	Send(*CollectDataFromCustomerWithStreamRequest) error
	CloseAndRecv() (*CollectDataFromCustomerWithStreamResponse, error)
	grpc.ClientStream
}

type wealthOnboardingCollectDataFromCustomerWithStreamClient struct {
	grpc.ClientStream
}

func (x *wealthOnboardingCollectDataFromCustomerWithStreamClient) Send(m *CollectDataFromCustomerWithStreamRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *wealthOnboardingCollectDataFromCustomerWithStreamClient) CloseAndRecv() (*CollectDataFromCustomerWithStreamResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(CollectDataFromCustomerWithStreamResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *wealthOnboardingClient) GetInvestmentOnbStatus(ctx context.Context, in *GetInvestmentOnbStatusRequest, opts ...grpc.CallOption) (*GetInvestmentOnbStatusResponse, error) {
	out := new(GetInvestmentOnbStatusResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_GetInvestmentOnbStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) UpdateOnbStatus(ctx context.Context, in *UpdateOnbStatusRequest, opts ...grpc.CallOption) (*UpdateOnbStatusResponse, error) {
	out := new(UpdateOnbStatusResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_UpdateOnbStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) GetInvestmentData(ctx context.Context, in *GetInvestmentDataRequest, opts ...grpc.CallOption) (*GetInvestmentDataResponse, error) {
	out := new(GetInvestmentDataResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_GetInvestmentData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) GetOnboardingTroubleshootDetails(ctx context.Context, in *GetOnboardingTroubleshootDetailsRequest, opts ...grpc.CallOption) (*GetOnboardingTroubleshootDetailsResponse, error) {
	out := new(GetOnboardingTroubleshootDetailsResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_GetOnboardingTroubleshootDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) DownloadDigilockerDocs(ctx context.Context, in *DownloadDigilockerDocsRequest, opts ...grpc.CallOption) (*DownloadDigilockerDocsResponse, error) {
	out := new(DownloadDigilockerDocsResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_DownloadDigilockerDocs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) GetInvestmentDataV2(ctx context.Context, in *GetInvestmentDataV2Request, opts ...grpc.CallOption) (*GetInvestmentDataV2Response, error) {
	out := new(GetInvestmentDataV2Response)
	err := c.cc.Invoke(ctx, WealthOnboarding_GetInvestmentDataV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) InitiateDocumentExtraction(ctx context.Context, in *InitiateDocumentExtractionRequest, opts ...grpc.CallOption) (*InitiateDocumentExtractionResponse, error) {
	out := new(InitiateDocumentExtractionResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_InitiateDocumentExtraction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) GetDocument(ctx context.Context, in *GetDocumentRequest, opts ...grpc.CallOption) (*GetDocumentResponse, error) {
	out := new(GetDocumentResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_GetDocument_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wealthOnboardingClient) GetOrCreateUser(ctx context.Context, in *GetOrCreateUserRequest, opts ...grpc.CallOption) (*GetOrCreateUserResponse, error) {
	out := new(GetOrCreateUserResponse)
	err := c.cc.Invoke(ctx, WealthOnboarding_GetOrCreateUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WealthOnboardingServer is the server API for WealthOnboarding service.
// All implementations should embed UnimplementedWealthOnboardingServer
// for forward compatibility
type WealthOnboardingServer interface {
	// will be used to get the wealth onboarding status of the customer
	GetOnboardingStatus(context.Context, *GetOnboardingStatusRequest) (*GetOnboardingStatusResponse, error)
	// will be used to get the next step for onboarding
	GetNextOnboardingStep(context.Context, *GetNextOnboardingStatusRequest) (*GetNextOnboardingStatusResponse, error)
	// primary RPC to  collect data from customer
	CollectDataFromCustomer(context.Context, *CollectDataFromCustomerRequest) (*CollectDataFromCustomerResponse, error)
	// fallback RPC for collecting large data from customer that don't fit into standard GRPC msg limit
	CollectDataFromCustomerWithStream(WealthOnboarding_CollectDataFromCustomerWithStreamServer) error
	// GetInvestmentOnbStatus is to be used by the core investment service for fetching the current investment status of the user
	// It starts the investment onboarding of a user, if not already done. It supports bulk mode as well
	GetInvestmentOnbStatus(context.Context, *GetInvestmentOnbStatusRequest) (*GetInvestmentOnbStatusResponse, error)
	// To update the onboarding status of a user
	UpdateOnbStatus(context.Context, *UpdateOnbStatusRequest) (*UpdateOnbStatusResponse, error)
	// GetInvestmentData returns the pre investment details required for an actor
	// currently only details for fatca, order feed, elog and credit mis file is supported
	GetInvestmentData(context.Context, *GetInvestmentDataRequest) (*GetInvestmentDataResponse, error)
	// RPC to get onboarding details and oboarding step details
	GetOnboardingTroubleshootDetails(context.Context, *GetOnboardingTroubleshootDetailsRequest) (*GetOnboardingTroubleshootDetailsResponse, error)
	// RPC to get access tokens from DigiLocker using the authorization code returned by DigiLocker when
	// a user logs into DigiLocker and grants us permission.
	// These tokens can then be later used in other DigiLocker API calls to get user's documents, etc.
	// NOTE: In an earlier implementation of this RPC the DigiLocker documents were actually downloaded here itself, but
	// since downloading of documents can throw transient errors, this implementation has been moved to inside the wealth onboarding orchestrator for automatic retries.
	// Ideally this RPC should now be perhaps named as GetDigilockerAccess, but that would also mean a similar naming for the FE RPC and a client change.
	DownloadDigilockerDocs(context.Context, *DownloadDigilockerDocsRequest) (*DownloadDigilockerDocsResponse, error)
	// GetInvestmentDataV2 returns the pre investment details required for an actor
	// currently only details for fatca, order feed, elog and credit mis file is supported
	// It also provides the current status of the data present on the wealth onboarding end
	GetInvestmentDataV2(context.Context, *GetInvestmentDataV2Request) (*GetInvestmentDataV2Response, error)
	// Wealth On-boarding has user's kyc documents like pan card, Aadhar card etc, other domain services may need to access these documents
	// InitiateDocumentExtraction begins document extraction process,
	// document will be extracted from kra docket if not already present in db
	// actual document will be part of GetDocument rpc response.
	// currently only pan card extraction is supported
	InitiateDocumentExtraction(context.Context, *InitiateDocumentExtractionRequest) (*InitiateDocumentExtractionResponse, error)
	// GetDocument returns an s3 url for the requested document
	GetDocument(context.Context, *GetDocumentRequest) (*GetDocumentResponse, error)
	// GetOrCreateUser rpc returns the user by actor id if found, else it creates the user.
	GetOrCreateUser(context.Context, *GetOrCreateUserRequest) (*GetOrCreateUserResponse, error)
}

// UnimplementedWealthOnboardingServer should be embedded to have forward compatible implementations.
type UnimplementedWealthOnboardingServer struct {
}

func (UnimplementedWealthOnboardingServer) GetOnboardingStatus(context.Context, *GetOnboardingStatusRequest) (*GetOnboardingStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnboardingStatus not implemented")
}
func (UnimplementedWealthOnboardingServer) GetNextOnboardingStep(context.Context, *GetNextOnboardingStatusRequest) (*GetNextOnboardingStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextOnboardingStep not implemented")
}
func (UnimplementedWealthOnboardingServer) CollectDataFromCustomer(context.Context, *CollectDataFromCustomerRequest) (*CollectDataFromCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CollectDataFromCustomer not implemented")
}
func (UnimplementedWealthOnboardingServer) CollectDataFromCustomerWithStream(WealthOnboarding_CollectDataFromCustomerWithStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectDataFromCustomerWithStream not implemented")
}
func (UnimplementedWealthOnboardingServer) GetInvestmentOnbStatus(context.Context, *GetInvestmentOnbStatusRequest) (*GetInvestmentOnbStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvestmentOnbStatus not implemented")
}
func (UnimplementedWealthOnboardingServer) UpdateOnbStatus(context.Context, *UpdateOnbStatusRequest) (*UpdateOnbStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOnbStatus not implemented")
}
func (UnimplementedWealthOnboardingServer) GetInvestmentData(context.Context, *GetInvestmentDataRequest) (*GetInvestmentDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvestmentData not implemented")
}
func (UnimplementedWealthOnboardingServer) GetOnboardingTroubleshootDetails(context.Context, *GetOnboardingTroubleshootDetailsRequest) (*GetOnboardingTroubleshootDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnboardingTroubleshootDetails not implemented")
}
func (UnimplementedWealthOnboardingServer) DownloadDigilockerDocs(context.Context, *DownloadDigilockerDocsRequest) (*DownloadDigilockerDocsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadDigilockerDocs not implemented")
}
func (UnimplementedWealthOnboardingServer) GetInvestmentDataV2(context.Context, *GetInvestmentDataV2Request) (*GetInvestmentDataV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvestmentDataV2 not implemented")
}
func (UnimplementedWealthOnboardingServer) InitiateDocumentExtraction(context.Context, *InitiateDocumentExtractionRequest) (*InitiateDocumentExtractionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateDocumentExtraction not implemented")
}
func (UnimplementedWealthOnboardingServer) GetDocument(context.Context, *GetDocumentRequest) (*GetDocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDocument not implemented")
}
func (UnimplementedWealthOnboardingServer) GetOrCreateUser(context.Context, *GetOrCreateUserRequest) (*GetOrCreateUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrCreateUser not implemented")
}

// UnsafeWealthOnboardingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WealthOnboardingServer will
// result in compilation errors.
type UnsafeWealthOnboardingServer interface {
	mustEmbedUnimplementedWealthOnboardingServer()
}

func RegisterWealthOnboardingServer(s grpc.ServiceRegistrar, srv WealthOnboardingServer) {
	s.RegisterService(&WealthOnboarding_ServiceDesc, srv)
}

func _WealthOnboarding_GetOnboardingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnboardingStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).GetOnboardingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_GetOnboardingStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).GetOnboardingStatus(ctx, req.(*GetOnboardingStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_GetNextOnboardingStep_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextOnboardingStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).GetNextOnboardingStep(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_GetNextOnboardingStep_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).GetNextOnboardingStep(ctx, req.(*GetNextOnboardingStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_CollectDataFromCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CollectDataFromCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).CollectDataFromCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_CollectDataFromCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).CollectDataFromCustomer(ctx, req.(*CollectDataFromCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_CollectDataFromCustomerWithStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(WealthOnboardingServer).CollectDataFromCustomerWithStream(&wealthOnboardingCollectDataFromCustomerWithStreamServer{stream})
}

type WealthOnboarding_CollectDataFromCustomerWithStreamServer interface {
	SendAndClose(*CollectDataFromCustomerWithStreamResponse) error
	Recv() (*CollectDataFromCustomerWithStreamRequest, error)
	grpc.ServerStream
}

type wealthOnboardingCollectDataFromCustomerWithStreamServer struct {
	grpc.ServerStream
}

func (x *wealthOnboardingCollectDataFromCustomerWithStreamServer) SendAndClose(m *CollectDataFromCustomerWithStreamResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *wealthOnboardingCollectDataFromCustomerWithStreamServer) Recv() (*CollectDataFromCustomerWithStreamRequest, error) {
	m := new(CollectDataFromCustomerWithStreamRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _WealthOnboarding_GetInvestmentOnbStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvestmentOnbStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).GetInvestmentOnbStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_GetInvestmentOnbStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).GetInvestmentOnbStatus(ctx, req.(*GetInvestmentOnbStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_UpdateOnbStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOnbStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).UpdateOnbStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_UpdateOnbStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).UpdateOnbStatus(ctx, req.(*UpdateOnbStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_GetInvestmentData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvestmentDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).GetInvestmentData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_GetInvestmentData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).GetInvestmentData(ctx, req.(*GetInvestmentDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_GetOnboardingTroubleshootDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnboardingTroubleshootDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).GetOnboardingTroubleshootDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_GetOnboardingTroubleshootDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).GetOnboardingTroubleshootDetails(ctx, req.(*GetOnboardingTroubleshootDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_DownloadDigilockerDocs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadDigilockerDocsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).DownloadDigilockerDocs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_DownloadDigilockerDocs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).DownloadDigilockerDocs(ctx, req.(*DownloadDigilockerDocsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_GetInvestmentDataV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvestmentDataV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).GetInvestmentDataV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_GetInvestmentDataV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).GetInvestmentDataV2(ctx, req.(*GetInvestmentDataV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_InitiateDocumentExtraction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateDocumentExtractionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).InitiateDocumentExtraction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_InitiateDocumentExtraction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).InitiateDocumentExtraction(ctx, req.(*InitiateDocumentExtractionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_GetDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).GetDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_GetDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).GetDocument(ctx, req.(*GetDocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WealthOnboarding_GetOrCreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrCreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WealthOnboardingServer).GetOrCreateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WealthOnboarding_GetOrCreateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WealthOnboardingServer).GetOrCreateUser(ctx, req.(*GetOrCreateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WealthOnboarding_ServiceDesc is the grpc.ServiceDesc for WealthOnboarding service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WealthOnboarding_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "wealthonboarding.WealthOnboarding",
	HandlerType: (*WealthOnboardingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOnboardingStatus",
			Handler:    _WealthOnboarding_GetOnboardingStatus_Handler,
		},
		{
			MethodName: "GetNextOnboardingStep",
			Handler:    _WealthOnboarding_GetNextOnboardingStep_Handler,
		},
		{
			MethodName: "CollectDataFromCustomer",
			Handler:    _WealthOnboarding_CollectDataFromCustomer_Handler,
		},
		{
			MethodName: "GetInvestmentOnbStatus",
			Handler:    _WealthOnboarding_GetInvestmentOnbStatus_Handler,
		},
		{
			MethodName: "UpdateOnbStatus",
			Handler:    _WealthOnboarding_UpdateOnbStatus_Handler,
		},
		{
			MethodName: "GetInvestmentData",
			Handler:    _WealthOnboarding_GetInvestmentData_Handler,
		},
		{
			MethodName: "GetOnboardingTroubleshootDetails",
			Handler:    _WealthOnboarding_GetOnboardingTroubleshootDetails_Handler,
		},
		{
			MethodName: "DownloadDigilockerDocs",
			Handler:    _WealthOnboarding_DownloadDigilockerDocs_Handler,
		},
		{
			MethodName: "GetInvestmentDataV2",
			Handler:    _WealthOnboarding_GetInvestmentDataV2_Handler,
		},
		{
			MethodName: "InitiateDocumentExtraction",
			Handler:    _WealthOnboarding_InitiateDocumentExtraction_Handler,
		},
		{
			MethodName: "GetDocument",
			Handler:    _WealthOnboarding_GetDocument_Handler,
		},
		{
			MethodName: "GetOrCreateUser",
			Handler:    _WealthOnboarding_GetOrCreateUser_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "CollectDataFromCustomerWithStream",
			Handler:       _WealthOnboarding_CollectDataFromCustomerWithStream_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "api/wealthonboarding/service.proto",
}
