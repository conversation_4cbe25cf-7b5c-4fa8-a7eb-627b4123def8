// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package risk;

import "api/dynamic_elements/dynamic_elements.proto";
import "api/risk/bankactions/risk_bank_actions.proto";
import "api/risk/bureau_id.proto";
import "api/risk/enums/enums.proto";
import "api/risk/internal/data_source.proto";
import "api/risk/internal/dispute.proto";
import "api/risk/internal/lea_complaint.proto";
import "api/risk/screener/enums.proto";
import "api/risk/screener/screener.proto";
import "api/risk/tagging/txn_tagging.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/risk";
option java_package = "com.github.epifi.gamma.api.risk";

service Risk {

  // DEPRECATED in favor of ScreenActor
  rpc GetRiskData (GetRiskDataRequest) returns (GetRiskDataResponse) {
    option deprecated = true;
  };
  rpc UpsertRiskData (UpsertRiskDataRequest) returns (UpsertRiskDataResponse);

  // BulkAddSavingsAccountBankAction rpc is used to add requests to the bank action(freeze, unfreeze)
  // flow for savings account. It creates celestial workflows for the successful entries
  // and returns a list of actors for which it failes
  rpc BulkAddSavingsAccountBankAction (BulkAddSavingsAccountBankActionRequest) returns (BulkAddSavingsAccountBankActionResponse);

  // BulkOverrideBankActionState rpc is used to manually override states for risk bank actions
  // It accepts an array of requests and returns array of failures for failed cases.
  // Manual overrides can only be done for MANUAL_INTERVENTION OR SENT_TO_BANK state
  // If current state: SENT_TO_BANK -> can request for both SUCCESS_MANUAL_OVERRIDE and REJECT_MANUAL_OVERRIDE
  // If current state: MANUAL_INTERVENTION -> can request for only REJECT_MANUAL_OVERRIDE as workflow is
  // already closed and no further processing will happen.
  rpc BulkOverrideBankActionState (BulkOverrideBankActionStateRequest) returns (BulkOverrideBankActionStateResponse);

  // rpc to create lea complaint entry in db
  rpc CreateLEAComplaint (CreateLEAComplaintRequest) returns (CreateLEAComplaintResponse);

  // rpc to fetch lea complaint entry in db by actor_id OR account_id
  // DEPRECATED in favor of GetUnifiedLeaComplaint
  rpc FetchLEAComplaints (FetchLEAComplaintsRequest) returns (FetchLEAComplaintsResponse);

  // RPC to screen given user for risk, It will perform different risk checks against given actor based on criteria given in request
  // will return final screening status and suggested action against user based on the individual risk checks results
  // DEPRECATED in favor of ScreenActor
  rpc ScreenUser (ScreenUserRequest) returns (ScreenUserResponse) {
    option deprecated = true;
  };

  // RPC to screen given actor for risk, It will perform different risk checks against given actor based on criteria given in request
  // RPC will return screener status in response as following:
  // IN_PROGRESS -> Screener checks are in progress and not finished yet, GetScreenerAttemptStatus should be polled to get final status
  // IN_MANUAL_REVIEW -> Manual review is required for the screener attempt and sent for review, GetScreenerAttemptStatus should be polled to get final status
  // DONE -> Screener evaluation is finished(either direct verdict or manual review scenario), Verdict in response can be referred for result
  // PROCESSING_FAILED -> Evalution of certain mandatory attempt has failed and should be retried by client
  // Verdict of screener attempt will be populated in response if screener status is DONE.
  rpc ScreenActor (ScreenActorRequest) returns (ScreenActorResponse);

  // Rpc to poll status of a screener attempt, can be called with screener attempt id or criteria + client request id to poll for the status
  // if criteria + client request id is passed latest valid attempt will be returned by this rpc
  // if no entry is found with given identifier, RPC will return record not found status
  // Status and Verdict in response can be interpreted similar to ScreenActor rpc
  //
  // NOTE: Record not found status code can be returned even if an attempt was done for the given actor in cases where screener attempt is considereded invalid due to different scenarios like
  // - Attempt will be expired after a particular threshold duration(can be configured based on criteria)
  // - Processing failed for given attempt
  // - Manual review verdict not received for more than a threshold duration etc
  // IF polling rpc returns record not found err clients should call ScreenActor rpc again to retry screening for the actor
  rpc GetScreenerAttemptStatus (GetScreenerAttemptStatusRequest) returns (GetScreenerAttemptStatusResponse);

  // RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  // ActorId is a mandatory parameter in the Request
  // Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no elements found for the given user on this screen
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the list of targeted comms elements is fetched successfully
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse);

  // DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse);
  // Rpc to be used for getting screener checks results for an actor
  // will return latest screener attempt details for the given actor by default
  rpc GetScreenerCheckResults (GetScreenerCheckResultsRequest) returns (GetScreenerCheckResultsResponse);

  // ProcessLEAComplaintNarration creates lea complaint narration for input actor and account id.
  // It also creates linked MO's (Modus Operandi) as annotations in case management for LEA review case against actor.
  rpc ProcessLEAComplaintNarration (ProcessLEAComplaintNarrationRequest) returns (ProcessLEAComplaintNarrationResponse);

  // Rpc to be used for getting screener check details for a particular screener check
  // details returned can vary across different checks, EX: for vpa name match check might return all vpa names vs for location check failure might return pindcode etc.
  // this is needed to analyse why a particular check has failed and can be useful during manual review
  rpc GetScreenerCheckDetails (GetScreenerCheckDetailsRequest) returns (GetScreenerCheckDetailsResponse);

  // AddLEAComplaintSource adds lea complaint source details to db for complaint.
  // It contains contact details of law enforcement agency from where complaint was received.
  rpc AddLEAComplaintSource (AddLEAComplaintSourceRequest) returns (AddLEAComplaintSourceResponse);

  // UpdateLEAComplaint updates lea complaint for input field masks.
  rpc UpdateLEAComplaint (UpdateLEAComplaintRequest) returns (UpdateLEAComplaintResponse);

  // UpdateScreenerAttempt updates the screener attempt fields based on the field mask given in the request
  rpc UpdateScreenerAttempt (UpdateScreenerAttemptRequest) returns (UpdateScreenerAttemptResponse);

  // UpsertDispute creates new dispute in db if not found else updates updatable fields.
  // NOTE: currently only creation is supported.
  rpc UpsertDispute (UpsertDisputeRequest) returns (UpsertDisputeResponse);

  // PassRiskScreenerAttempt -> pass the screener attempt for actor.
  rpc PassRiskScreenerAttempt (PassRiskScreenerAttemptRequest) returns (PassRiskScreenerAttemptResponse);

  // BulkInsertBureauIdRiskDetails allows inserting multiple bureau records at once.
  rpc BulkInsertBureauIdRiskDetails (BulkInsertBureauIdRiskDetailsRequest) returns (BulkInsertBureauIdRiskDetailsResponse);

  // GetBureauIdRiskDetails fetches bureau information based on actor ID and request ID.
  rpc GetBureauIdRiskDetails (GetBureauIdRiskDetailsRequest) returns (GetBureauIdRiskDetailsResponse);

  // GetTransactionTags rpc fetches transaction tags mapped with txn_id passed in the request.
  rpc GetTransactionTags (GetTransactionTagsRequest) returns (GetTransactionTagsResponse);
}

message GetTransactionTagsRequest {
  string txn_id = 1;
}

message GetTransactionTagsResponse {
  rpc.Status status = 1;
  repeated tagging.TransactionTag tags = 2;
}

message GetBureauIdRiskDetailsRequest {
  string actor_id = 1;
  string request_id = 2;
}

message GetBureauIdRiskDetailsResponse {
  enum Status {
    OK = 0;
    // invalid argument passed
    INVALID_ARGUMENT = 3;
    // risk bureau info isn't found with id.
    NOT_FOUND = 5;
    // ISE response due to some internal error in the rpc
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  repeated BureauIdRiskDetail bureau_id_risk_details = 2;
}

message BulkInsertBureauIdRiskDetailsRequest {
  repeated BureauIdRiskDetail bureau_id_risk_details = 1;
}

message BulkInsertBureauIdRiskDetailsResponse {
  enum Status {
    OK = 0;
    // invalid argument passed
    INVALID_ARGUMENT = 3;
    // ISE response due to some internal error in the rpc
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message PassRiskScreenerAttemptRequest {
  // actor_id and screener_criteria used to fetch the active screener attempt for actor
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  enums.ScreenerCriteria screener_criteria = 2 [(validate.rules).enum = {not_in: [0]}];
  // email address of the analyst
  string analyst_email = 3 [(validate.rules).string.min_len = 1];
  // reason to pass screener attempt for actor
  string manual_pass_reason = 4 [(validate.rules).string.min_len = 1];
}

message PassRiskScreenerAttemptResponse {
  enum Status {
    OK = 0;
    // invalid argument passed
    INVALID_ARGUMENT = 3;
    // ISE response due to some internal error in the rpc
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message UpsertDisputeRequest {
  Dispute dispute = 1;
}

message UpsertDisputeResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // ISE resposne due to some internal error in the rpc
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message AddLEAComplaintSourceRequest {
  LEAComplaintSource source = 1;
}

message AddLEAComplaintSourceResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;
}

message UpdateLEAComplaintRequest {
  // Only complaint id and field to be updated is required.
  LEAComplaint lea_complaint = 1;
  // list of fields to be updated.
  repeated LEAComplaintFieldMask field_masks = 2;
}

message UpdateLEAComplaintResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // lea complaint isn't found with id.
    NOT_FOUND = 5;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;
}

message ProcessLEAComplaintNarrationRequest {
  LEAComplaintNarrationDetails lea_complaint_narration_details = 1 [(validate.rules).message.required = true];

  string analyst_email = 4 [(validate.rules).string.min_len = 1];
}

message ProcessLEAComplaintNarrationResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;
}

message CreateLEAComplaintRequest {
  LEAComplaint lea_complaint = 1;
}

message CreateLEAComplaintResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;
}

message GetRiskDataRequest {
  string actor_id = 1;
  repeated RiskParam risk_params = 2;

  // remove these fields after screener V2 (user intel) service is in prod
  // deprecated in favour of metadata
  api.typesv2.common.BooleanEnum is_device_premium = 3 [deprecated = true];
  api.typesv2.common.BooleanEnum is_credit_report_found = 4 [deprecated = true];
  api.typesv2.common.BooleanEnum has_credit_report_download_consent = 5 [deprecated = true];
  float gmail_pan_name_match_score = 6 [deprecated = true];

  // if the field is set to true, data is returned from the DB only.
  // if the data is not available; no data is returned.
  api.typesv2.common.BooleanEnum want_cached_data = 7;

  // extra data required for processing a risk param.
  // please check corresponding documentation for the risk params to know about required metadata
  map<string, string> metadata = 8;
}

message GetRiskDataResponse {
  rpc.Status status = 1;
  // Deprecated in favour of risk_param_response_map
  repeated RiskData risk_data = 2 [deprecated = true];
  // risk_param_response_map is used to store the response for a particular risk param
  // The key for this map is the risk param enum string.
  map<string, RiskParamResponse> risk_param_response_map = 3;
}

message RiskParamResponse {
  enum Status {
    SUCCESS = 0;
    INTERNAL = 13;
    LOCATION_TOKEN_NOT_FOUND = 101;
  }
  Status status = 1;
  // risk_data should be checked only in case of success response
  RiskData risk_data = 2;
}

message UpsertRiskDataRequest {
  string actor_id = 2;
  RiskParam risk_param = 3;
  Payload payload = 4;
  Result result = 5;
}

message UpsertRiskDataResponse {
  rpc.Status status = 1;
}

message AddSavingsAccountBankActionRequest {
  oneof identifier {
    string actor_id = 1;
  }
  enums.Action action = 2;
  RequestReason request_reason = 3;
  repeated enums.CommsTemplate userCommsTemplate = 4;
  api.typesv2.common.BooleanEnum is_recon = 5;
  google.protobuf.Timestamp bank_action_date = 6;
  // case against which action is being taken
  string case_id = 7;
  // email of analyst
  string analyst_email = 8;
}

message AddSavingsAccountBankActionFailure {
  int32 request_index = 1;
  string actor_id = 2;
  string reason = 3;
  string error_string = 4;
}

message BulkAddSavingsAccountBankActionRequest {
  repeated AddSavingsAccountBankActionRequest requests = 1;
  enums.Provenance provenance = 2;
}

message BulkAddSavingsAccountBankActionResponse {
  rpc.Status status = 1;
  repeated AddSavingsAccountBankActionFailure failures = 2;
}

message OverrideBankActionStateRequest {
  // actor_id as a unique identifier will suffice as an actor can have only one open state
  // ie. SENT_TO_BANK or MANUAL_INTERVENTION, we need to act on ay one of these states
  // any other cases will be errored out
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // only STATE_SUCCESS_MANUAL_OVERRIDE and STATE_REJECT_MANUAL_OVERRIDE allowed here as per design
  enums.State required_state = 2 [(validate.rules).enum = {in: [7, 8]}];
  // reason for above action, will be appended on original reason
  string override_reason = 3 [(validate.rules).string.min_len = 1];
}

message BulkOverrideBankActionStateRequest {
  repeated OverrideBankActionStateRequest requests = 1;
  string requester_email = 2;
}

message BulkOverrideBankActionStateResponse {
  enum Status {
    OK = 0;
    // internal error while processing the request
    // none of the requests have processed
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  message OverrideBankActionStateFailure {
    string actor_id = 1;
    enum Status {
      OK = 0;
      // request parameters invalid
      INVALID_ARGUMENT = 3;
      // Already exists
      ALREADY_EXISTS = 6;
      // internal error while processing the request
      INTERNAL = 13;
    }
    // status for current request
    rpc.Status status = 2;
    // description for error
    string error_string = 3;
  }
  repeated OverrideBankActionStateFailure failures = 2;
}

message ScreenUserRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];

  // Criteria for which risk screening is being performed
  // Set of checks performed and threshold for failure could be different based on the purpose screening is happening for
  // EX: checks(and threshold) required in fi-lite onboarding vs savings account onboarding will differ
  enums.ScreenerCriteria screener_criteria = 2 [(validate.rules).enum = {not_in: [0]}];
}

message ScreenUserResponse {
  enum Status {
    OK = 0;
    // request validation failed
    INVALID_ARGUMENT = 3;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  enums.ScreenerAction screener_action = 2;
}

message FetchLEAComplaintsRequest {
  oneof identifier {
    string actor_id = 1;
    string account_id = 2;
  }
  // max number of responses to be served
  // if left empty, everything would be served
  uint32 limit = 3;
}

message FetchLEAComplaintsResponse {
  enum Status {
    OK = 0;
    // request validation failed
    INVALID_ARGUMENT = 3;
    // no record found for given identifier
    RECORD_NOT_FOUND = 5;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  repeated LEAComplaint lea_complaints = 2 [deprecated = true];

  repeated ExtendedLEAComplaint extended_lea_complaints = 3;
}

message GetScreenerCheckResultsRequest {
  oneof identifier {
    // if actor id is passed will return latest screener attempt data for the given actor
    string actor_id = 1;
  }

  message Filters {
    // filter by result of the checks
    // if not passed will return checks with bot passed and failed result
    Result result = 1;
  }

  Filters filters = 2;
}

message GetScreenerCheckResultsResponse {
  enum Status {
    OK = 0;
    // request validation failed
    INVALID_ARGUMENT = 3;

    // internal error while processing the request
    INTERNAL = 13;

    // No checks result found
    RECORD_NOT_FOUND = 5;
  }

  rpc.Status status = 1;

  message CheckResult {
    RiskData data = 1;
    // indicates whether additional check related details are available and can be fetched using get additional check details rpc
    bool are_additional_details_available = 2;
  }
  repeated CheckResult risk_check_results = 2;
}

message GetScreenerCheckDetailsRequest {
  // Id of the risk data row which stores a check result
  string result_id = 1;
}

message GetScreenerCheckDetailsResponse {
  enum Status {
    OK = 0;
    // request validation failed
    INVALID_ARGUMENT = 3;
    // No checks result found
    RECORD_NOT_FOUND = 5;
    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // Stored check result for the id passed in request
  risk.RiskData check_result = 2;
  // Additional details related to the checks for analysis
  // this can contain check specific information like certain model response fields
  // or raw details used for evaluating the given check etc.
  risk.screener.AdditionalCheckDetails additional_check_details = 3;
}

message ScreenActorRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];

  // Criteria for which risk screening is being performed
  // Set of checks performed and threshold for failure could be different based on the purpose screening is happening for
  // EX: checks(and threshold) required in fi-lite onboarding vs savings account onboarding will differ
  enums.ScreenerCriteria screener_criteria = 2 [(validate.rules).enum = {not_in: [0]}];
  // Required field, Screener service will dedupe the attempt based on criteria + client_request_id field
  string client_request_id = 3 [(validate.rules).string.min_len = 1];
}

message ScreenActorResponse {
  enum Status {
    OK = 0;
    // request validation failed
    INVALID_ARGUMENT = 3;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // Id for the screener attempt, can be used for polling status of screener attempt
  string attempt_id = 2;

  risk.screener.ScreenerStatus screener_status = 3;

  risk.screener.Verdict verdict = 4;
  // PotentialRiskFlags indicates plausible risky flags observed during the screener checks
  repeated risk.screener.PotentialRiskFlag potential_risk_flags = 5;
}

message GetScreenerAttemptStatusRequest {
  risk.screener.AttemptIdentifier attempt_identifier = 1;
}

message GetScreenerAttemptStatusResponse {
  enum Status {
    OK = 0;
    // request validation failed
    INVALID_ARGUMENT = 3;
    // internal error while processing the request
    INTERNAL = 13;
    // no record found for given identifier in request
    RECORD_NOT_FOUND = 5;
  }
  rpc.Status status = 1;

  risk.screener.ScreenerStatus screener_status = 2;

  risk.screener.Verdict verdict = 3;
  // PotentialRiskFlags indicates plausible risky flags observed during the screener checks
  repeated risk.screener.PotentialRiskFlag potential_risk_flags = 4;
}

message UpdateScreenerAttemptRequest {
  // Screener attempt details
  // Id field is mandatory
  risk.screener.ScreenerAttempt screener_attempt = 1;
  // Currently only status and verdict can be updated.
  repeated risk.screener.ScreenerAttemptFieldMask field_masks = 2;
}

message UpdateScreenerAttemptResponse {
  enum Status {
    OK = 0;
    // request validation failed
    INVALID_ARGUMENT = 3;
    // no record found for given identifier in request
    RECORD_NOT_FOUND = 5;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}
