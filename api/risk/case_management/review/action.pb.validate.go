// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/review/action.proto

package review

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/risk/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.CommsTemplate(0)
)

// Validate checks the field values on Action with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Action) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Action with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ActionMultiError, or nil if none found.
func (m *Action) ValidateAll() error {
	return m.validate(true)
}

func (m *Action) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := ActionValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _Action_ReviewType_NotInLookup[m.GetReviewType()]; ok {
		err := ActionValidationError{
			field:  "ReviewType",
			reason: "value must not be in list [REVIEW_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _Action_ActionType_NotInLookup[m.GetActionType()]; ok {
		err := ActionValidationError{
			field:  "ActionType",
			reason: "value must not be in list [ACTION_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionValidationError{
				field:  "Parameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _Action_Source_NotInLookup[m.GetSource()]; ok {
		err := ActionValidationError{
			field:  "Source",
			reason: "value must not be in list [ACTION_SOURCE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AnalystEmail

	if all {
		switch v := interface{}(m.GetInitiatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionValidationError{
					field:  "InitiatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionValidationError{
					field:  "InitiatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInitiatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionValidationError{
				field:  "InitiatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for Status

	// no validation rules for ProcessingType

	if len(errors) > 0 {
		return ActionMultiError(errors)
	}

	return nil
}

// ActionMultiError is an error wrapping multiple validation errors returned by
// Action.ValidateAll() if the designated constraints aren't met.
type ActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionMultiError) AllErrors() []error { return m }

// ActionValidationError is the validation error returned by Action.Validate if
// the designated constraints aren't met.
type ActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionValidationError) ErrorName() string { return "ActionValidationError" }

// Error satisfies the builtin error interface
func (e ActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionValidationError{}

var _Action_ReviewType_NotInLookup = map[ReviewType]struct{}{
	0: {},
}

var _Action_ActionType_NotInLookup = map[ActionType]struct{}{
	0: {},
}

var _Action_Source_NotInLookup = map[ActionSource]struct{}{
	0: {},
}

// Validate checks the field values on ActionParameters with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ActionParameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActionParametersMultiError, or nil if none found.
func (m *ActionParameters) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionParameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Parameter.(type) {
	case *ActionParameters_AccountFreezeParameters:
		if v == nil {
			err := ActionParametersValidationError{
				field:  "Parameter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAccountFreezeParameters()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionParametersValidationError{
						field:  "AccountFreezeParameters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionParametersValidationError{
						field:  "AccountFreezeParameters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAccountFreezeParameters()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionParametersValidationError{
					field:  "AccountFreezeParameters",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionParameters_SnoozeParameters:
		if v == nil {
			err := ActionParametersValidationError{
				field:  "Parameter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSnoozeParameters()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionParametersValidationError{
						field:  "SnoozeParameters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionParametersValidationError{
						field:  "SnoozeParameters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSnoozeParameters()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionParametersValidationError{
					field:  "SnoozeParameters",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionParameters_RequestUserInfoParameters:
		if v == nil {
			err := ActionParametersValidationError{
				field:  "Parameter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRequestUserInfoParameters()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionParametersValidationError{
						field:  "RequestUserInfoParameters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionParametersValidationError{
						field:  "RequestUserInfoParameters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRequestUserInfoParameters()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionParametersValidationError{
					field:  "RequestUserInfoParameters",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionParameters_RejectEscalationParameters:
		if v == nil {
			err := ActionParametersValidationError{
				field:  "Parameter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRejectEscalationParameters()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionParametersValidationError{
						field:  "RejectEscalationParameters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionParametersValidationError{
						field:  "RejectEscalationParameters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRejectEscalationParameters()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionParametersValidationError{
					field:  "RejectEscalationParameters",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionParameters_AccountLienParameters:
		if v == nil {
			err := ActionParametersValidationError{
				field:  "Parameter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAccountLienParameters()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionParametersValidationError{
						field:  "AccountLienParameters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionParametersValidationError{
						field:  "AccountLienParameters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAccountLienParameters()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionParametersValidationError{
					field:  "AccountLienParameters",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ActionParametersMultiError(errors)
	}

	return nil
}

// ActionParametersMultiError is an error wrapping multiple validation errors
// returned by ActionParameters.ValidateAll() if the designated constraints
// aren't met.
type ActionParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionParametersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionParametersMultiError) AllErrors() []error { return m }

// ActionParametersValidationError is the validation error returned by
// ActionParameters.Validate if the designated constraints aren't met.
type ActionParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionParametersValidationError) ErrorName() string { return "ActionParametersValidationError" }

// Error satisfies the builtin error interface
func (e ActionParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionParameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionParametersValidationError{}

// Validate checks the field values on AccountFreezeParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountFreezeParameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountFreezeParameters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountFreezeParametersMultiError, or nil if none found.
func (m *AccountFreezeParameters) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountFreezeParameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FreezeLevel

	if all {
		switch v := interface{}(m.GetRequestReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountFreezeParametersValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountFreezeParametersValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountFreezeParametersValidationError{
				field:  "RequestReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountFreezeParametersMultiError(errors)
	}

	return nil
}

// AccountFreezeParametersMultiError is an error wrapping multiple validation
// errors returned by AccountFreezeParameters.ValidateAll() if the designated
// constraints aren't met.
type AccountFreezeParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountFreezeParametersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountFreezeParametersMultiError) AllErrors() []error { return m }

// AccountFreezeParametersValidationError is the validation error returned by
// AccountFreezeParameters.Validate if the designated constraints aren't met.
type AccountFreezeParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountFreezeParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountFreezeParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountFreezeParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountFreezeParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountFreezeParametersValidationError) ErrorName() string {
	return "AccountFreezeParametersValidationError"
}

// Error satisfies the builtin error interface
func (e AccountFreezeParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountFreezeParameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountFreezeParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountFreezeParametersValidationError{}

// Validate checks the field values on AccountLienParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountLienParameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountLienParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountLienParametersMultiError, or nil if none found.
func (m *AccountLienParameters) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountLienParameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountLienParametersValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountLienParametersValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountLienParametersValidationError{
				field:  "RequestReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLienAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountLienParametersValidationError{
					field:  "LienAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountLienParametersValidationError{
					field:  "LienAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLienAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountLienParametersValidationError{
				field:  "LienAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LienDurationInHours

	// no validation rules for AccountNumber

	if len(errors) > 0 {
		return AccountLienParametersMultiError(errors)
	}

	return nil
}

// AccountLienParametersMultiError is an error wrapping multiple validation
// errors returned by AccountLienParameters.ValidateAll() if the designated
// constraints aren't met.
type AccountLienParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountLienParametersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountLienParametersMultiError) AllErrors() []error { return m }

// AccountLienParametersValidationError is the validation error returned by
// AccountLienParameters.Validate if the designated constraints aren't met.
type AccountLienParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountLienParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountLienParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountLienParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountLienParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountLienParametersValidationError) ErrorName() string {
	return "AccountLienParametersValidationError"
}

// Error satisfies the builtin error interface
func (e AccountLienParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountLienParameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountLienParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountLienParametersValidationError{}

// Validate checks the field values on SnoozeParameters with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SnoozeParameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SnoozeParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SnoozeParametersMultiError, or nil if none found.
func (m *SnoozeParameters) ValidateAll() error {
	return m.validate(true)
}

func (m *SnoozeParameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSnoozeTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SnoozeParametersValidationError{
					field:  "SnoozeTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SnoozeParametersValidationError{
					field:  "SnoozeTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSnoozeTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SnoozeParametersValidationError{
				field:  "SnoozeTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Options.(type) {
	case *SnoozeParameters_OutcallOptions_:
		if v == nil {
			err := SnoozeParametersValidationError{
				field:  "Options",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOutcallOptions()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SnoozeParametersValidationError{
						field:  "OutcallOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SnoozeParametersValidationError{
						field:  "OutcallOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOutcallOptions()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SnoozeParametersValidationError{
					field:  "OutcallOptions",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SnoozeParametersMultiError(errors)
	}

	return nil
}

// SnoozeParametersMultiError is an error wrapping multiple validation errors
// returned by SnoozeParameters.ValidateAll() if the designated constraints
// aren't met.
type SnoozeParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SnoozeParametersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SnoozeParametersMultiError) AllErrors() []error { return m }

// SnoozeParametersValidationError is the validation error returned by
// SnoozeParameters.Validate if the designated constraints aren't met.
type SnoozeParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SnoozeParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SnoozeParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SnoozeParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SnoozeParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SnoozeParametersValidationError) ErrorName() string { return "SnoozeParametersValidationError" }

// Error satisfies the builtin error interface
func (e SnoozeParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSnoozeParameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SnoozeParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SnoozeParametersValidationError{}

// Validate checks the field values on RequestUserInfoParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RequestUserInfoParameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestUserInfoParameters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RequestUserInfoParametersMultiError, or nil if none found.
func (m *RequestUserInfoParameters) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestUserInfoParameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mode

	switch v := m.Options.(type) {
	case *RequestUserInfoParameters_FormModeOptions:
		if v == nil {
			err := RequestUserInfoParametersValidationError{
				field:  "Options",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFormModeOptions()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RequestUserInfoParametersValidationError{
						field:  "FormModeOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RequestUserInfoParametersValidationError{
						field:  "FormModeOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFormModeOptions()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RequestUserInfoParametersValidationError{
					field:  "FormModeOptions",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RequestUserInfoParametersMultiError(errors)
	}

	return nil
}

// RequestUserInfoParametersMultiError is an error wrapping multiple validation
// errors returned by RequestUserInfoParameters.ValidateAll() if the
// designated constraints aren't met.
type RequestUserInfoParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestUserInfoParametersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestUserInfoParametersMultiError) AllErrors() []error { return m }

// RequestUserInfoParametersValidationError is the validation error returned by
// RequestUserInfoParameters.Validate if the designated constraints aren't met.
type RequestUserInfoParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestUserInfoParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestUserInfoParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestUserInfoParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestUserInfoParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestUserInfoParametersValidationError) ErrorName() string {
	return "RequestUserInfoParametersValidationError"
}

// Error satisfies the builtin error interface
func (e RequestUserInfoParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestUserInfoParameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestUserInfoParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestUserInfoParametersValidationError{}

// Validate checks the field values on FormOutcallModeOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FormOutcallModeOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FormOutcallModeOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FormOutcallModeOptionsMultiError, or nil if none found.
func (m *FormOutcallModeOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *FormOutcallModeOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPostOutcallDefaultAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormOutcallModeOptionsValidationError{
					field:  "PostOutcallDefaultAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormOutcallModeOptionsValidationError{
					field:  "PostOutcallDefaultAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostOutcallDefaultAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormOutcallModeOptionsValidationError{
				field:  "PostOutcallDefaultAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIdentifiers()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormOutcallModeOptionsValidationError{
					field:  "Identifiers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormOutcallModeOptionsValidationError{
					field:  "Identifiers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifiers()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormOutcallModeOptionsValidationError{
				field:  "Identifiers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpiresAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormOutcallModeOptionsValidationError{
					field:  "ExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormOutcallModeOptionsValidationError{
					field:  "ExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiresAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormOutcallModeOptionsValidationError{
				field:  "ExpiresAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FormOutcallModeOptionsMultiError(errors)
	}

	return nil
}

// FormOutcallModeOptionsMultiError is an error wrapping multiple validation
// errors returned by FormOutcallModeOptions.ValidateAll() if the designated
// constraints aren't met.
type FormOutcallModeOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FormOutcallModeOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FormOutcallModeOptionsMultiError) AllErrors() []error { return m }

// FormOutcallModeOptionsValidationError is the validation error returned by
// FormOutcallModeOptions.Validate if the designated constraints aren't met.
type FormOutcallModeOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FormOutcallModeOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FormOutcallModeOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FormOutcallModeOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FormOutcallModeOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FormOutcallModeOptionsValidationError) ErrorName() string {
	return "FormOutcallModeOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e FormOutcallModeOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFormOutcallModeOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FormOutcallModeOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FormOutcallModeOptionsValidationError{}

// Validate checks the field values on PostOutcallDefaultAction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostOutcallDefaultAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostOutcallDefaultAction with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostOutcallDefaultActionMultiError, or nil if none found.
func (m *PostOutcallDefaultAction) ValidateAll() error {
	return m.validate(true)
}

func (m *PostOutcallDefaultAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _PostOutcallDefaultAction_ActionType_InLookup[m.GetActionType()]; !ok {
		err := PostOutcallDefaultActionValidationError{
			field:  "ActionType",
			reason: "value must be in list [ACTION_TYPE_FREEZE_ACCOUNT ACTION_TYPE_PASS_ACCOUNT ACTION_TYPE_MOVE_TO_REVIEW]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetActionParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostOutcallDefaultActionValidationError{
					field:  "ActionParameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostOutcallDefaultActionValidationError{
					field:  "ActionParameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostOutcallDefaultActionValidationError{
				field:  "ActionParameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostOutcallDefaultActionMultiError(errors)
	}

	return nil
}

// PostOutcallDefaultActionMultiError is an error wrapping multiple validation
// errors returned by PostOutcallDefaultAction.ValidateAll() if the designated
// constraints aren't met.
type PostOutcallDefaultActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostOutcallDefaultActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostOutcallDefaultActionMultiError) AllErrors() []error { return m }

// PostOutcallDefaultActionValidationError is the validation error returned by
// PostOutcallDefaultAction.Validate if the designated constraints aren't met.
type PostOutcallDefaultActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostOutcallDefaultActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostOutcallDefaultActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostOutcallDefaultActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostOutcallDefaultActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostOutcallDefaultActionValidationError) ErrorName() string {
	return "PostOutcallDefaultActionValidationError"
}

// Error satisfies the builtin error interface
func (e PostOutcallDefaultActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostOutcallDefaultAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostOutcallDefaultActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostOutcallDefaultActionValidationError{}

var _PostOutcallDefaultAction_ActionType_InLookup = map[ActionType]struct{}{
	1: {},
	2: {},
	5: {},
}

// Validate checks the field values on RequestReason with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RequestReason) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestReason with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RequestReasonMultiError, or
// nil if none found.
func (m *RequestReason) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestReason) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Reason

	// no validation rules for Remarks

	if len(errors) > 0 {
		return RequestReasonMultiError(errors)
	}

	return nil
}

// RequestReasonMultiError is an error wrapping multiple validation errors
// returned by RequestReason.ValidateAll() if the designated constraints
// aren't met.
type RequestReasonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestReasonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestReasonMultiError) AllErrors() []error { return m }

// RequestReasonValidationError is the validation error returned by
// RequestReason.Validate if the designated constraints aren't met.
type RequestReasonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestReasonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestReasonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestReasonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestReasonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestReasonValidationError) ErrorName() string { return "RequestReasonValidationError" }

// Error satisfies the builtin error interface
func (e RequestReasonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestReason.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestReasonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestReasonValidationError{}

// Validate checks the field values on ActionProcessingParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActionProcessingParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionProcessingParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActionProcessingParamsMultiError, or nil if none found.
func (m *ActionProcessingParams) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionProcessingParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAction() == nil {
		err := ActionProcessingParamsValidationError{
			field:  "Action",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionProcessingParamsValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionProcessingParamsValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionProcessingParamsValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _ActionProcessingParams_ProcessingType_NotInLookup[m.GetProcessingType()]; ok {
		err := ActionProcessingParamsValidationError{
			field:  "ProcessingType",
			reason: "value must not be in list [ACTION_PROCESSING_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ActionProcessingParamsMultiError(errors)
	}

	return nil
}

// ActionProcessingParamsMultiError is an error wrapping multiple validation
// errors returned by ActionProcessingParams.ValidateAll() if the designated
// constraints aren't met.
type ActionProcessingParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionProcessingParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionProcessingParamsMultiError) AllErrors() []error { return m }

// ActionProcessingParamsValidationError is the validation error returned by
// ActionProcessingParams.Validate if the designated constraints aren't met.
type ActionProcessingParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionProcessingParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionProcessingParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionProcessingParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionProcessingParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionProcessingParamsValidationError) ErrorName() string {
	return "ActionProcessingParamsValidationError"
}

// Error satisfies the builtin error interface
func (e ActionProcessingParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionProcessingParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionProcessingParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionProcessingParamsValidationError{}

var _ActionProcessingParams_ProcessingType_NotInLookup = map[ActionProcessingType]struct{}{
	0: {},
}

// Validate checks the field values on RejectEscalationParameters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RejectEscalationParameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RejectEscalationParameters with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RejectEscalationParametersMultiError, or nil if none found.
func (m *RejectEscalationParameters) ValidateAll() error {
	return m.validate(true)
}

func (m *RejectEscalationParameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Reason

	if len(errors) > 0 {
		return RejectEscalationParametersMultiError(errors)
	}

	return nil
}

// RejectEscalationParametersMultiError is an error wrapping multiple
// validation errors returned by RejectEscalationParameters.ValidateAll() if
// the designated constraints aren't met.
type RejectEscalationParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RejectEscalationParametersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RejectEscalationParametersMultiError) AllErrors() []error { return m }

// RejectEscalationParametersValidationError is the validation error returned
// by RejectEscalationParameters.Validate if the designated constraints aren't met.
type RejectEscalationParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RejectEscalationParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RejectEscalationParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RejectEscalationParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RejectEscalationParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RejectEscalationParametersValidationError) ErrorName() string {
	return "RejectEscalationParametersValidationError"
}

// Error satisfies the builtin error interface
func (e RejectEscalationParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRejectEscalationParameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RejectEscalationParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RejectEscalationParametersValidationError{}

// Validate checks the field values on SnoozeParameters_OutcallOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SnoozeParameters_OutcallOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SnoozeParameters_OutcallOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SnoozeParameters_OutcallOptionsMultiError, or nil if none found.
func (m *SnoozeParameters_OutcallOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *SnoozeParameters_OutcallOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SendOutcallReminderComms

	if len(errors) > 0 {
		return SnoozeParameters_OutcallOptionsMultiError(errors)
	}

	return nil
}

// SnoozeParameters_OutcallOptionsMultiError is an error wrapping multiple
// validation errors returned by SnoozeParameters_OutcallOptions.ValidateAll()
// if the designated constraints aren't met.
type SnoozeParameters_OutcallOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SnoozeParameters_OutcallOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SnoozeParameters_OutcallOptionsMultiError) AllErrors() []error { return m }

// SnoozeParameters_OutcallOptionsValidationError is the validation error
// returned by SnoozeParameters_OutcallOptions.Validate if the designated
// constraints aren't met.
type SnoozeParameters_OutcallOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SnoozeParameters_OutcallOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SnoozeParameters_OutcallOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SnoozeParameters_OutcallOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SnoozeParameters_OutcallOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SnoozeParameters_OutcallOptionsValidationError) ErrorName() string {
	return "SnoozeParameters_OutcallOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e SnoozeParameters_OutcallOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSnoozeParameters_OutcallOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SnoozeParameters_OutcallOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SnoozeParameters_OutcallOptionsValidationError{}
