// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/pan/epan/consumer/epan_notification_event.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EPANCommsEvent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EPANCommsEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EPANCommsEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EPANCommsEventMultiError,
// or nil if none found.
func (m *EPANCommsEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *EPANCommsEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EPANCommsEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EPANCommsEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EPANCommsEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEventTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EPANCommsEventValidationError{
					field:  "EventTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EPANCommsEventValidationError{
					field:  "EventTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EPANCommsEventValidationError{
				field:  "EventTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsumeTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EPANCommsEventValidationError{
					field:  "ConsumeTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EPANCommsEventValidationError{
					field:  "ConsumeTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsumeTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EPANCommsEventValidationError{
				field:  "ConsumeTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CommType

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return EPANCommsEventMultiError(errors)
	}

	return nil
}

// EPANCommsEventMultiError is an error wrapping multiple validation errors
// returned by EPANCommsEvent.ValidateAll() if the designated constraints
// aren't met.
type EPANCommsEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EPANCommsEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EPANCommsEventMultiError) AllErrors() []error { return m }

// EPANCommsEventValidationError is the validation error returned by
// EPANCommsEvent.Validate if the designated constraints aren't met.
type EPANCommsEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EPANCommsEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EPANCommsEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EPANCommsEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EPANCommsEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EPANCommsEventValidationError) ErrorName() string { return "EPANCommsEventValidationError" }

// Error satisfies the builtin error interface
func (e EPANCommsEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEPANCommsEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EPANCommsEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EPANCommsEventValidationError{}
