//go:generate gen_sql -types=ProductType,RedemptionStatus
syntax = "proto3";

package cms;

option go_package = "github.com/epifi/gamma/api/cms";
option java_package = "com.github.epifi.gamma.api.cms";

enum ProductType {
  PRODUCT_TYPE_UNSPECIFIED = 0;
  PRODUCT_TYPE_EGV = 1;
  PRODUCT_TYPE_DISCOUNT_CODE = 2;
}

enum RedemptionStatus {
  REDEMPTION_STATUS_UNSPECIFIED = 0;
  REDEMPTION_STATUS_PROCESSING = 1;
  REDEMPTION_STATUS_FAILED = 2;
  REDEMPTION_STATUS_COMPLETE = 3;
}

enum RedemptionFieldMask {
  REDEMPTION_FIELD_MASK_UNSPECIFIED = 0;
  REDEMPTION_FIELD_MASK_STATUS = 1;
  REDEMPTION_FIELD_MASK_COUPON_ID = 2;
}

enum CouponFieldMask {
  COUPON_FIELD_MASK_UNSPECIFIED = 0;
  COUPON_FIELD_MASK_INVENTORY_LEFT = 1;
}