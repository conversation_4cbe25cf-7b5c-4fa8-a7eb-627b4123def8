// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/developer/ticket_summary/service.proto

package ticket_summary

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetTicketDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTicketDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTicketDetailsRequestMultiError, or nil if none found.
func (m *GetTicketDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTicketDetailsRequestMultiError(errors)
	}

	return nil
}

// GetTicketDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetTicketDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTicketDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketDetailsRequestMultiError) AllErrors() []error { return m }

// GetTicketDetailsRequestValidationError is the validation error returned by
// GetTicketDetailsRequest.Validate if the designated constraints aren't met.
type GetTicketDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketDetailsRequestValidationError) ErrorName() string {
	return "GetTicketDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketDetailsRequestValidationError{}

// Validate checks the field values on GetTicketDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTicketDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTicketDetailsResponseMultiError, or nil if none found.
func (m *GetTicketDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketDetailsResponseValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketDetailsResponseValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketDetailsResponseValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetTicketDetailsResponseMultiError(errors)
	}

	return nil
}

// GetTicketDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetTicketDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTicketDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketDetailsResponseMultiError) AllErrors() []error { return m }

// GetTicketDetailsResponseValidationError is the validation error returned by
// GetTicketDetailsResponse.Validate if the designated constraints aren't met.
type GetTicketDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketDetailsResponseValidationError) ErrorName() string {
	return "GetTicketDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketDetailsResponseValidationError{}

// Validate checks the field values on GetTicketConversationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTicketConversationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketConversationsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTicketConversationsRequestMultiError, or nil if none found.
func (m *GetTicketConversationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketConversationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketConversationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketConversationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketConversationsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTicketConversationsRequestMultiError(errors)
	}

	return nil
}

// GetTicketConversationsRequestMultiError is an error wrapping multiple
// validation errors returned by GetTicketConversationsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetTicketConversationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketConversationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketConversationsRequestMultiError) AllErrors() []error { return m }

// GetTicketConversationsRequestValidationError is the validation error
// returned by GetTicketConversationsRequest.Validate if the designated
// constraints aren't met.
type GetTicketConversationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketConversationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketConversationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketConversationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketConversationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketConversationsRequestValidationError) ErrorName() string {
	return "GetTicketConversationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketConversationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketConversationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketConversationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketConversationsRequestValidationError{}

// Validate checks the field values on GetTicketConversationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTicketConversationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketConversationsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTicketConversationsResponseMultiError, or nil if none found.
func (m *GetTicketConversationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketConversationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketConversationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketConversationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketConversationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTicketConversationsResponseMultiError(errors)
	}

	return nil
}

// GetTicketConversationsResponseMultiError is an error wrapping multiple
// validation errors returned by GetTicketConversationsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetTicketConversationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketConversationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketConversationsResponseMultiError) AllErrors() []error { return m }

// GetTicketConversationsResponseValidationError is the validation error
// returned by GetTicketConversationsResponse.Validate if the designated
// constraints aren't met.
type GetTicketConversationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketConversationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketConversationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketConversationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketConversationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketConversationsResponseValidationError) ErrorName() string {
	return "GetTicketConversationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketConversationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketConversationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketConversationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketConversationsResponseValidationError{}
