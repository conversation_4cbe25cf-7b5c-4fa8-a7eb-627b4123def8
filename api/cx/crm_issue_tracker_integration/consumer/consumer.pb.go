//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/crm_issue_tracker_integration/consumer/consumer.proto

package consumer

import (
	queue "github.com/epifi/be-common/api/queue"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessCrmIssueTrackerIntegrationEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	EventType     EventType                    `protobuf:"varint,2,opt,name=event_type,json=eventType,proto3,enum=cx.crm_issue_tracker_integration.consumer.EventType" json:"event_type,omitempty"`
	// Types that are assignable to EventPayload:
	//
	//	*ProcessCrmIssueTrackerIntegrationEventRequest_FreshdeskTicketChangePayload
	//	*ProcessCrmIssueTrackerIntegrationEventRequest_FreshdeskConversationPayload
	//	*ProcessCrmIssueTrackerIntegrationEventRequest_MonorailIssueUpdatesCommentsPayload
	EventPayload isProcessCrmIssueTrackerIntegrationEventRequest_EventPayload `protobuf_oneof:"event_payload"`
}

func (x *ProcessCrmIssueTrackerIntegrationEventRequest) Reset() {
	*x = ProcessCrmIssueTrackerIntegrationEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCrmIssueTrackerIntegrationEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCrmIssueTrackerIntegrationEventRequest) ProtoMessage() {}

func (x *ProcessCrmIssueTrackerIntegrationEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCrmIssueTrackerIntegrationEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessCrmIssueTrackerIntegrationEventRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessCrmIssueTrackerIntegrationEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCrmIssueTrackerIntegrationEventRequest) GetEventType() EventType {
	if x != nil {
		return x.EventType
	}
	return EventType_EVENT_TYPE_UNSPECIFIED
}

func (m *ProcessCrmIssueTrackerIntegrationEventRequest) GetEventPayload() isProcessCrmIssueTrackerIntegrationEventRequest_EventPayload {
	if m != nil {
		return m.EventPayload
	}
	return nil
}

func (x *ProcessCrmIssueTrackerIntegrationEventRequest) GetFreshdeskTicketChangePayload() *FreshdeskTicketChangePayload {
	if x, ok := x.GetEventPayload().(*ProcessCrmIssueTrackerIntegrationEventRequest_FreshdeskTicketChangePayload); ok {
		return x.FreshdeskTicketChangePayload
	}
	return nil
}

func (x *ProcessCrmIssueTrackerIntegrationEventRequest) GetFreshdeskConversationPayload() *FreshdeskConversationPayload {
	if x, ok := x.GetEventPayload().(*ProcessCrmIssueTrackerIntegrationEventRequest_FreshdeskConversationPayload); ok {
		return x.FreshdeskConversationPayload
	}
	return nil
}

func (x *ProcessCrmIssueTrackerIntegrationEventRequest) GetMonorailIssueUpdatesCommentsPayload() *MonorailIssueUpdatesCommentsPayload {
	if x, ok := x.GetEventPayload().(*ProcessCrmIssueTrackerIntegrationEventRequest_MonorailIssueUpdatesCommentsPayload); ok {
		return x.MonorailIssueUpdatesCommentsPayload
	}
	return nil
}

type isProcessCrmIssueTrackerIntegrationEventRequest_EventPayload interface {
	isProcessCrmIssueTrackerIntegrationEventRequest_EventPayload()
}

type ProcessCrmIssueTrackerIntegrationEventRequest_FreshdeskTicketChangePayload struct {
	FreshdeskTicketChangePayload *FreshdeskTicketChangePayload `protobuf:"bytes,3,opt,name=freshdesk_ticket_change_payload,json=freshdeskTicketChangePayload,proto3,oneof"`
}

type ProcessCrmIssueTrackerIntegrationEventRequest_FreshdeskConversationPayload struct {
	FreshdeskConversationPayload *FreshdeskConversationPayload `protobuf:"bytes,4,opt,name=freshdesk_conversation_payload,json=freshdeskConversationPayload,proto3,oneof"`
}

type ProcessCrmIssueTrackerIntegrationEventRequest_MonorailIssueUpdatesCommentsPayload struct {
	MonorailIssueUpdatesCommentsPayload *MonorailIssueUpdatesCommentsPayload `protobuf:"bytes,5,opt,name=monorail_issue_updates_comments_payload,json=monorailIssueUpdatesCommentsPayload,proto3,oneof"`
}

func (*ProcessCrmIssueTrackerIntegrationEventRequest_FreshdeskTicketChangePayload) isProcessCrmIssueTrackerIntegrationEventRequest_EventPayload() {
}

func (*ProcessCrmIssueTrackerIntegrationEventRequest_FreshdeskConversationPayload) isProcessCrmIssueTrackerIntegrationEventRequest_EventPayload() {
}

func (*ProcessCrmIssueTrackerIntegrationEventRequest_MonorailIssueUpdatesCommentsPayload) isProcessCrmIssueTrackerIntegrationEventRequest_EventPayload() {
}

type ProcessCrmIssueTrackerIntegrationEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCrmIssueTrackerIntegrationEventResponse) Reset() {
	*x = ProcessCrmIssueTrackerIntegrationEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCrmIssueTrackerIntegrationEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCrmIssueTrackerIntegrationEventResponse) ProtoMessage() {}

func (x *ProcessCrmIssueTrackerIntegrationEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCrmIssueTrackerIntegrationEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCrmIssueTrackerIntegrationEventResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessCrmIssueTrackerIntegrationEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_cx_crm_issue_tracker_integration_consumer_consumer_proto protoreflect.FileDescriptor

var file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x72, 0x6d, 0x5f, 0x69, 0x73, 0x73,
	0x75, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x29,
	0x63, 0x78, 0x2e, 0x63, 0x72, 0x6d, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x78, 0x2f, 0x63, 0x72, 0x6d, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa8, 0x05, 0x0a, 0x2d, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x43, 0x72, 0x6d, 0x49, 0x73, 0x73, 0x75, 0x65, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x53,
	0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x72, 0x6d, 0x5f, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x1f, 0x66, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65, 0x73,
	0x6b, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f,
	0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x72, 0x6d, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x46, 0x72, 0x65, 0x73, 0x68, 0x64,
	0x65, 0x73, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x00, 0x52, 0x1c, 0x66, 0x72, 0x65, 0x73, 0x68, 0x64,
	0x65, 0x73, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x8f, 0x01, 0x0a, 0x1e, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x64, 0x65, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x47, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x72, 0x6d, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x46, 0x72, 0x65, 0x73,
	0x68, 0x64, 0x65, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x00, 0x52, 0x1c, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x64, 0x65, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0xa6, 0x01, 0x0a, 0x27, 0x6d, 0x6f, 0x6e,
	0x6f, 0x72, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x63, 0x78, 0x2e,
	0x63, 0x72, 0x6d, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x6e, 0x6f, 0x72, 0x61, 0x69, 0x6c, 0x49,
	0x73, 0x73, 0x75, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x00, 0x52, 0x23, 0x6d, 0x6f,
	0x6e, 0x6f, 0x72, 0x61, 0x69, 0x6c, 0x49, 0x73, 0x73, 0x75, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x73, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x22, 0x78, 0x0a, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x6d,
	0x49, 0x73, 0x73, 0x75, 0x65, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0x86, 0x02, 0x0a,
	0x22, 0x43, 0x72, 0x6d, 0x49, 0x73, 0x73, 0x75, 0x65, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x12, 0xdf, 0x01, 0x0a, 0x26, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x72, 0x6d, 0x49, 0x73, 0x73, 0x75, 0x65, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x58,
	0x2e, 0x63, 0x78, 0x2e, 0x63, 0x72, 0x6d, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x43, 0x72, 0x6d, 0x49, 0x73, 0x73, 0x75, 0x65, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x59, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x72,
	0x6d, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x6d, 0x49,
	0x73, 0x73, 0x75, 0x65, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x8c, 0x01, 0x0a, 0x44, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x72, 0x6d, 0x5f, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5a, 0x44,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x72,
	0x6d, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDescOnce sync.Once
	file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDescData = file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDesc
)

func file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDescGZIP() []byte {
	file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDescOnce.Do(func() {
		file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDescData)
	})
	return file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDescData
}

var file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_goTypes = []interface{}{
	(*ProcessCrmIssueTrackerIntegrationEventRequest)(nil),  // 0: cx.crm_issue_tracker_integration.consumer.ProcessCrmIssueTrackerIntegrationEventRequest
	(*ProcessCrmIssueTrackerIntegrationEventResponse)(nil), // 1: cx.crm_issue_tracker_integration.consumer.ProcessCrmIssueTrackerIntegrationEventResponse
	(*queue.ConsumerRequestHeader)(nil),                    // 2: queue.ConsumerRequestHeader
	(EventType)(0),                                         // 3: cx.crm_issue_tracker_integration.consumer.EventType
	(*FreshdeskTicketChangePayload)(nil),                   // 4: cx.crm_issue_tracker_integration.consumer.FreshdeskTicketChangePayload
	(*FreshdeskConversationPayload)(nil),                   // 5: cx.crm_issue_tracker_integration.consumer.FreshdeskConversationPayload
	(*MonorailIssueUpdatesCommentsPayload)(nil),            // 6: cx.crm_issue_tracker_integration.consumer.MonorailIssueUpdatesCommentsPayload
	(*queue.ConsumerResponseHeader)(nil),                   // 7: queue.ConsumerResponseHeader
}
var file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_depIdxs = []int32{
	2, // 0: cx.crm_issue_tracker_integration.consumer.ProcessCrmIssueTrackerIntegrationEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	3, // 1: cx.crm_issue_tracker_integration.consumer.ProcessCrmIssueTrackerIntegrationEventRequest.event_type:type_name -> cx.crm_issue_tracker_integration.consumer.EventType
	4, // 2: cx.crm_issue_tracker_integration.consumer.ProcessCrmIssueTrackerIntegrationEventRequest.freshdesk_ticket_change_payload:type_name -> cx.crm_issue_tracker_integration.consumer.FreshdeskTicketChangePayload
	5, // 3: cx.crm_issue_tracker_integration.consumer.ProcessCrmIssueTrackerIntegrationEventRequest.freshdesk_conversation_payload:type_name -> cx.crm_issue_tracker_integration.consumer.FreshdeskConversationPayload
	6, // 4: cx.crm_issue_tracker_integration.consumer.ProcessCrmIssueTrackerIntegrationEventRequest.monorail_issue_updates_comments_payload:type_name -> cx.crm_issue_tracker_integration.consumer.MonorailIssueUpdatesCommentsPayload
	7, // 5: cx.crm_issue_tracker_integration.consumer.ProcessCrmIssueTrackerIntegrationEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0, // 6: cx.crm_issue_tracker_integration.consumer.CrmIssueTrackerIntegrationConsumer.ProcessCrmIssueTrackerIntegrationEvent:input_type -> cx.crm_issue_tracker_integration.consumer.ProcessCrmIssueTrackerIntegrationEventRequest
	1, // 7: cx.crm_issue_tracker_integration.consumer.CrmIssueTrackerIntegrationConsumer.ProcessCrmIssueTrackerIntegrationEvent:output_type -> cx.crm_issue_tracker_integration.consumer.ProcessCrmIssueTrackerIntegrationEventResponse
	7, // [7:8] is the sub-list for method output_type
	6, // [6:7] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_init() }
func file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_init() {
	if File_api_cx_crm_issue_tracker_integration_consumer_consumer_proto != nil {
		return
	}
	file_api_cx_crm_issue_tracker_integration_consumer_events_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCrmIssueTrackerIntegrationEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCrmIssueTrackerIntegrationEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ProcessCrmIssueTrackerIntegrationEventRequest_FreshdeskTicketChangePayload)(nil),
		(*ProcessCrmIssueTrackerIntegrationEventRequest_FreshdeskConversationPayload)(nil),
		(*ProcessCrmIssueTrackerIntegrationEventRequest_MonorailIssueUpdatesCommentsPayload)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_goTypes,
		DependencyIndexes: file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_depIdxs,
		MessageInfos:      file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_msgTypes,
	}.Build()
	File_api_cx_crm_issue_tracker_integration_consumer_consumer_proto = out.File
	file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_rawDesc = nil
	file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_goTypes = nil
	file_api_cx_crm_issue_tracker_integration_consumer_consumer_proto_depIdxs = nil
}
