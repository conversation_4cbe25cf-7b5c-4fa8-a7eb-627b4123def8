// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/liveness_video/liveness_video.proto

package liveness_video

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LivenessVideoParamsForLogging with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessVideoParamsForLogging) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessVideoParamsForLogging with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LivenessVideoParamsForLoggingMultiError, or nil if none found.
func (m *LivenessVideoParamsForLogging) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessVideoParamsForLogging) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestId

	if len(errors) > 0 {
		return LivenessVideoParamsForLoggingMultiError(errors)
	}

	return nil
}

// LivenessVideoParamsForLoggingMultiError is an error wrapping multiple
// validation errors returned by LivenessVideoParamsForLogging.ValidateAll()
// if the designated constraints aren't met.
type LivenessVideoParamsForLoggingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessVideoParamsForLoggingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessVideoParamsForLoggingMultiError) AllErrors() []error { return m }

// LivenessVideoParamsForLoggingValidationError is the validation error
// returned by LivenessVideoParamsForLogging.Validate if the designated
// constraints aren't met.
type LivenessVideoParamsForLoggingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessVideoParamsForLoggingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessVideoParamsForLoggingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessVideoParamsForLoggingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessVideoParamsForLoggingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessVideoParamsForLoggingValidationError) ErrorName() string {
	return "LivenessVideoParamsForLoggingValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessVideoParamsForLoggingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessVideoParamsForLogging.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessVideoParamsForLoggingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessVideoParamsForLoggingValidationError{}
