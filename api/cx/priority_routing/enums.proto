// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";
package cx.priority_routing;

option go_package = "github.com/epifi/gamma/api/cx/priority_routing";
option java_package = "com.github.epifi.gamma.api.cx.priority_routing";

// UserCategory enums represents categories of user required for routing to specific group/agent
// User will be assigned to one of the category based on the stage he is in and other user properties such as account_balance,..
enum UserCategory {
    CATEGORY_UNSPECIFIED = 0;
    // users categorised as high priority based on certain rules on user properties
    HIGH_PRIORITY_ACCOUNT_HOLDERS = 1;
    // users categorised as low priority on the basis of user properties
    LOW_PRIORITY_ACCOUNT_HOLDERS = 2;
    // users who are currently onboarding will be part of this category
    CURRENTLY_ONBOARDING_USERS = 3;
    // users who are current part of salary program
    SALARY_PROGRAM_USERS = 4;
}
