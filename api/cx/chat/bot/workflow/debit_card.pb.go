// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/chat/bot/workflow/debit_card.proto

package workflow

import (
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DebitCardTrackingData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// delivery state of the card
	CardTrackingDeliveryState provisioning.CardTrackingDeliveryState `protobuf:"varint,1,opt,name=card_tracking_delivery_state,json=cardTrackingDeliveryState,proto3,enum=card.provisioning.CardTrackingDeliveryState" json:"card_tracking_delivery_state,omitempty"`
	// card activation state
	CardActivationState provisioning.CardDeliveryTrackingState `protobuf:"varint,2,opt,name=card_activation_state,json=cardActivationState,proto3,enum=card.provisioning.CardDeliveryTrackingState" json:"card_activation_state,omitempty"`
	// card creation timestamp
	CardCreatedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=card_created_at,json=cardCreatedAt,proto3" json:"card_created_at,omitempty"`
	// awb number of the shipment
	AwbNumber string `protobuf:"bytes,4,opt,name=awb_number,json=awbNumber,proto3" json:"awb_number,omitempty"`
	// courier partner of the shipment
	CourierPartner string `protobuf:"bytes,5,opt,name=courier_partner,json=courierPartner,proto3" json:"courier_partner,omitempty"`
}

func (x *DebitCardTrackingData) Reset() {
	*x = DebitCardTrackingData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_bot_workflow_debit_card_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitCardTrackingData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitCardTrackingData) ProtoMessage() {}

func (x *DebitCardTrackingData) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_bot_workflow_debit_card_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitCardTrackingData.ProtoReflect.Descriptor instead.
func (*DebitCardTrackingData) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_bot_workflow_debit_card_proto_rawDescGZIP(), []int{0}
}

func (x *DebitCardTrackingData) GetCardTrackingDeliveryState() provisioning.CardTrackingDeliveryState {
	if x != nil {
		return x.CardTrackingDeliveryState
	}
	return provisioning.CardTrackingDeliveryState(0)
}

func (x *DebitCardTrackingData) GetCardActivationState() provisioning.CardDeliveryTrackingState {
	if x != nil {
		return x.CardActivationState
	}
	return provisioning.CardDeliveryTrackingState(0)
}

func (x *DebitCardTrackingData) GetCardCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CardCreatedAt
	}
	return nil
}

func (x *DebitCardTrackingData) GetAwbNumber() string {
	if x != nil {
		return x.AwbNumber
	}
	return ""
}

func (x *DebitCardTrackingData) GetCourierPartner() string {
	if x != nil {
		return x.CourierPartner
	}
	return ""
}

type DebitCardTrackingParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *DebitCardTrackingParameters) Reset() {
	*x = DebitCardTrackingParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_bot_workflow_debit_card_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitCardTrackingParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitCardTrackingParameters) ProtoMessage() {}

func (x *DebitCardTrackingParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_bot_workflow_debit_card_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitCardTrackingParameters.ProtoReflect.Descriptor instead.
func (*DebitCardTrackingParameters) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_bot_workflow_debit_card_proto_rawDescGZIP(), []int{1}
}

func (x *DebitCardTrackingParameters) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

var File_api_cx_chat_bot_workflow_debit_card_proto protoreflect.FileDescriptor

var file_api_cx_chat_bot_workflow_debit_card_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f,
	0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x64, 0x65, 0x62, 0x69, 0x74,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x78, 0x2e,
	0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf4, 0x02, 0x0a, 0x15, 0x44, 0x65,
	0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x6d, 0x0a, 0x1c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x19, 0x63, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x60, 0x0a, 0x15, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x13, 0x63, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x77, 0x62, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x77,
	0x62, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x72, 0x69,
	0x65, 0x72, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x63, 0x6f, 0x75, 0x72, 0x69, 0x65, 0x72, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x22, 0x38, 0x0a, 0x1b, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74,
	0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5a, 0x2f, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61,
	0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_chat_bot_workflow_debit_card_proto_rawDescOnce sync.Once
	file_api_cx_chat_bot_workflow_debit_card_proto_rawDescData = file_api_cx_chat_bot_workflow_debit_card_proto_rawDesc
)

func file_api_cx_chat_bot_workflow_debit_card_proto_rawDescGZIP() []byte {
	file_api_cx_chat_bot_workflow_debit_card_proto_rawDescOnce.Do(func() {
		file_api_cx_chat_bot_workflow_debit_card_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_chat_bot_workflow_debit_card_proto_rawDescData)
	})
	return file_api_cx_chat_bot_workflow_debit_card_proto_rawDescData
}

var file_api_cx_chat_bot_workflow_debit_card_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_cx_chat_bot_workflow_debit_card_proto_goTypes = []interface{}{
	(*DebitCardTrackingData)(nil),               // 0: cx.chat.bot.workflow.DebitCardTrackingData
	(*DebitCardTrackingParameters)(nil),         // 1: cx.chat.bot.workflow.DebitCardTrackingParameters
	(provisioning.CardTrackingDeliveryState)(0), // 2: card.provisioning.CardTrackingDeliveryState
	(provisioning.CardDeliveryTrackingState)(0), // 3: card.provisioning.CardDeliveryTrackingState
	(*timestamppb.Timestamp)(nil),               // 4: google.protobuf.Timestamp
}
var file_api_cx_chat_bot_workflow_debit_card_proto_depIdxs = []int32{
	2, // 0: cx.chat.bot.workflow.DebitCardTrackingData.card_tracking_delivery_state:type_name -> card.provisioning.CardTrackingDeliveryState
	3, // 1: cx.chat.bot.workflow.DebitCardTrackingData.card_activation_state:type_name -> card.provisioning.CardDeliveryTrackingState
	4, // 2: cx.chat.bot.workflow.DebitCardTrackingData.card_created_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_cx_chat_bot_workflow_debit_card_proto_init() }
func file_api_cx_chat_bot_workflow_debit_card_proto_init() {
	if File_api_cx_chat_bot_workflow_debit_card_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_cx_chat_bot_workflow_debit_card_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitCardTrackingData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_chat_bot_workflow_debit_card_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitCardTrackingParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_chat_bot_workflow_debit_card_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_chat_bot_workflow_debit_card_proto_goTypes,
		DependencyIndexes: file_api_cx_chat_bot_workflow_debit_card_proto_depIdxs,
		MessageInfos:      file_api_cx_chat_bot_workflow_debit_card_proto_msgTypes,
	}.Build()
	File_api_cx_chat_bot_workflow_debit_card_proto = out.File
	file_api_cx_chat_bot_workflow_debit_card_proto_rawDesc = nil
	file_api_cx_chat_bot_workflow_debit_card_proto_goTypes = nil
	file_api_cx_chat_bot_workflow_debit_card_proto_depIdxs = nil
}
