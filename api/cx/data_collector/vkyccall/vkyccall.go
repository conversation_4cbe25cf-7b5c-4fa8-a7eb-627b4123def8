package vkyccall

import alPb "github.com/epifi/gamma/api/cx/audit_log"

func (m *GetAvailableCallsRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetAvailableCallsRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *GetAvailableCallsCountRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetAvailableCallsCountRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *InitiateAgentCallRequest) GetAction() alPb.Action {
	return alPb.Action_EXECUTE
}

func (m *InitiateAgentCallRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *EndVkycCallRequest) GetAction() alPb.Action {
	return alPb.Action_EXECUTE
}

func (m *EndVkycCallRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *ConcludeVkycCallRequest) GetAction() alPb.Action {
	return alPb.Action_EXECUTE
}

func (m *ConcludeVkycCallRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *GetUserLocationRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetUserLocationRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *CaptureScreenshotRequest) GetAction() alPb.Action {
	return alPb.Action_EXECUTE
}

func (m *CaptureScreenshotRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *PerformClientActionRequest) GetAction() alPb.Action {
	return alPb.Action_EXECUTE
}

func (m *PerformClientActionRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *GetOnboardingStagesRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetOnboardingStagesRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *MeetingHealthCheckRequest) GetAction() alPb.Action {
	return alPb.Action_VERIFY
}

func (m *MeetingHealthCheckRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *ExtractDataFromDocumentImageRequest) GetAction() alPb.Action {
	return alPb.Action_EXECUTE
}

func (m *ExtractDataFromDocumentImageRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *GetMatchScoreRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetMatchScoreRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *GetAvailableReportsRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetAvailableReportsRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *GetAvailableReportsCountRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetAvailableReportsCountRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *InitiateAuditorReviewRequest) GetAction() alPb.Action {
	return alPb.Action_EXECUTE
}

func (m *InitiateAuditorReviewRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *ConcludeAuditorReviewRequest) GetAction() alPb.Action {
	return alPb.Action_EXECUTE
}

func (m *ConcludeAuditorReviewRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}

func (m *GenerateVKYCCallReportRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GenerateVKYCCallReportRequest) GetObject() alPb.Object {
	return alPb.Object_VKYC_CALL_DETAILS
}
