package sherlock_actor_activity

import alPb "github.com/epifi/gamma/api/cx/audit_log"

func (m *GetActivitiesRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetActivitiesRequest) GetObject() alPb.Object {
	return alPb.Object_SHERLOCK_ACTOR_ACTIVITY
}

func (m *GetActivityDetailsForSherlockRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetActivityDetailsForSherlockRequest) GetObject() alPb.Object {
	return alPb.Object_SHERLOCK_ACTOR_ACTIVITY
}
