// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/data_collector/p2pinvestment/service.proto

package p2pinvestment

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetInvestmentSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestmentSummaryRequestMultiError, or nil if none found.
func (m *GetInvestmentSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetInvestmentSummaryRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreationDateFilterOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryRequestValidationError{
					field:  "CreationDateFilterOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryRequestValidationError{
					field:  "CreationDateFilterOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreationDateFilterOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryRequestValidationError{
				field:  "CreationDateFilterOption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSchemeFilterOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryRequestValidationError{
					field:  "SchemeFilterOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryRequestValidationError{
					field:  "SchemeFilterOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSchemeFilterOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryRequestValidationError{
				field:  "SchemeFilterOption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnTypeFilterOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryRequestValidationError{
					field:  "TxnTypeFilterOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryRequestValidationError{
					field:  "TxnTypeFilterOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnTypeFilterOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryRequestValidationError{
				field:  "TxnTypeFilterOption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentSummaryRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentSummaryRequestMultiError is an error wrapping multiple
// validation errors returned by GetInvestmentSummaryRequest.ValidateAll() if
// the designated constraints aren't met.
type GetInvestmentSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentSummaryRequestMultiError) AllErrors() []error { return m }

// GetInvestmentSummaryRequestValidationError is the validation error returned
// by GetInvestmentSummaryRequest.Validate if the designated constraints
// aren't met.
type GetInvestmentSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentSummaryRequestValidationError) ErrorName() string {
	return "GetInvestmentSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentSummaryRequestValidationError{}

// Validate checks the field values on GetInvestmentSummaryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentSummaryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestmentSummaryResponseMultiError, or nil if none found.
func (m *GetInvestmentSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInvestmentSummary() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInvestmentSummaryResponseValidationError{
						field:  fmt.Sprintf("InvestmentSummary[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInvestmentSummaryResponseValidationError{
						field:  fmt.Sprintf("InvestmentSummary[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInvestmentSummaryResponseValidationError{
					field:  fmt.Sprintf("InvestmentSummary[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentSummaryResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentSummaryResponseMultiError is an error wrapping multiple
// validation errors returned by GetInvestmentSummaryResponse.ValidateAll() if
// the designated constraints aren't met.
type GetInvestmentSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentSummaryResponseMultiError) AllErrors() []error { return m }

// GetInvestmentSummaryResponseValidationError is the validation error returned
// by GetInvestmentSummaryResponse.Validate if the designated constraints
// aren't met.
type GetInvestmentSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentSummaryResponseValidationError) ErrorName() string {
	return "GetInvestmentSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentSummaryResponseValidationError{}

// Validate checks the field values on GetInvestorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestorRequestMultiError, or nil if none found.
func (m *GetInvestorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetInvestorRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestorRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestorRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestorRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestorRequestMultiError(errors)
	}

	return nil
}

// GetInvestorRequestMultiError is an error wrapping multiple validation errors
// returned by GetInvestorRequest.ValidateAll() if the designated constraints
// aren't met.
type GetInvestorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestorRequestMultiError) AllErrors() []error { return m }

// GetInvestorRequestValidationError is the validation error returned by
// GetInvestorRequest.Validate if the designated constraints aren't met.
type GetInvestorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestorRequestValidationError) ErrorName() string {
	return "GetInvestorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestorRequestValidationError{}

// Validate checks the field values on GetInvestorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestorResponseMultiError, or nil if none found.
func (m *GetInvestorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestorResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestorResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestorResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestorResponseValidationError{
					field:  "Investor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestorResponseValidationError{
					field:  "Investor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestorResponseValidationError{
				field:  "Investor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestorResponseMultiError(errors)
	}

	return nil
}

// GetInvestorResponseMultiError is an error wrapping multiple validation
// errors returned by GetInvestorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestorResponseMultiError) AllErrors() []error { return m }

// GetInvestorResponseValidationError is the validation error returned by
// GetInvestorResponse.Validate if the designated constraints aren't met.
type GetInvestorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestorResponseValidationError) ErrorName() string {
	return "GetInvestorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestorResponseValidationError{}

// Validate checks the field values on
// GetFilterValuesForJumpInvestmentSummaryRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFilterValuesForJumpInvestmentSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFilterValuesForJumpInvestmentSummaryRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetFilterValuesForJumpInvestmentSummaryRequestMultiError, or nil if none found.
func (m *GetFilterValuesForJumpInvestmentSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFilterValuesForJumpInvestmentSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetFilterValuesForJumpInvestmentSummaryRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFilterValuesForJumpInvestmentSummaryRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFilterValuesForJumpInvestmentSummaryRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFilterValuesForJumpInvestmentSummaryRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFilterValuesForJumpInvestmentSummaryRequestMultiError(errors)
	}

	return nil
}

// GetFilterValuesForJumpInvestmentSummaryRequestMultiError is an error
// wrapping multiple validation errors returned by
// GetFilterValuesForJumpInvestmentSummaryRequest.ValidateAll() if the
// designated constraints aren't met.
type GetFilterValuesForJumpInvestmentSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFilterValuesForJumpInvestmentSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFilterValuesForJumpInvestmentSummaryRequestMultiError) AllErrors() []error { return m }

// GetFilterValuesForJumpInvestmentSummaryRequestValidationError is the
// validation error returned by
// GetFilterValuesForJumpInvestmentSummaryRequest.Validate if the designated
// constraints aren't met.
type GetFilterValuesForJumpInvestmentSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFilterValuesForJumpInvestmentSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFilterValuesForJumpInvestmentSummaryRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetFilterValuesForJumpInvestmentSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFilterValuesForJumpInvestmentSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFilterValuesForJumpInvestmentSummaryRequestValidationError) ErrorName() string {
	return "GetFilterValuesForJumpInvestmentSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFilterValuesForJumpInvestmentSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFilterValuesForJumpInvestmentSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFilterValuesForJumpInvestmentSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFilterValuesForJumpInvestmentSummaryRequestValidationError{}

// Validate checks the field values on
// GetFilterValuesForJumpInvestmentSummaryResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFilterValuesForJumpInvestmentSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFilterValuesForJumpInvestmentSummaryResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetFilterValuesForJumpInvestmentSummaryResponseMultiError, or nil if none found.
func (m *GetFilterValuesForJumpInvestmentSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFilterValuesForJumpInvestmentSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFilterValuesForJumpInvestmentSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFilterValuesForJumpInvestmentSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFilterValuesForJumpInvestmentSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFilterValuesForJumpInvestmentSummaryResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFilterValuesForJumpInvestmentSummaryResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFilterValuesForJumpInvestmentSummaryResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFilterValuesForJumpInvestmentSummaryResponseMultiError(errors)
	}

	return nil
}

// GetFilterValuesForJumpInvestmentSummaryResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetFilterValuesForJumpInvestmentSummaryResponse.ValidateAll() if the
// designated constraints aren't met.
type GetFilterValuesForJumpInvestmentSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFilterValuesForJumpInvestmentSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFilterValuesForJumpInvestmentSummaryResponseMultiError) AllErrors() []error { return m }

// GetFilterValuesForJumpInvestmentSummaryResponseValidationError is the
// validation error returned by
// GetFilterValuesForJumpInvestmentSummaryResponse.Validate if the designated
// constraints aren't met.
type GetFilterValuesForJumpInvestmentSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFilterValuesForJumpInvestmentSummaryResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetFilterValuesForJumpInvestmentSummaryResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetFilterValuesForJumpInvestmentSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFilterValuesForJumpInvestmentSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFilterValuesForJumpInvestmentSummaryResponseValidationError) ErrorName() string {
	return "GetFilterValuesForJumpInvestmentSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFilterValuesForJumpInvestmentSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFilterValuesForJumpInvestmentSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFilterValuesForJumpInvestmentSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFilterValuesForJumpInvestmentSummaryResponseValidationError{}
