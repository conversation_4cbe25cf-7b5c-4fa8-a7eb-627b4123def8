// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/mandate/mandate_entity.proto

package mandate

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MandateFieldMask int32

const (
	// unspecified
	MandateFieldMask_MANDATE_FIELD_MASK_UNSPECIFIED MandateFieldMask = 0
	MandateFieldMask_UMN                            MandateFieldMask = 1
	MandateFieldMask_MANDATE_SIGNED_TOKEN           MandateFieldMask = 2
)

// Enum value maps for MandateFieldMask.
var (
	MandateFieldMask_name = map[int32]string{
		0: "MANDATE_FIELD_MASK_UNSPECIFIED",
		1: "UMN",
		2: "MANDATE_SIGNED_TOKEN",
	}
	MandateFieldMask_value = map[string]int32{
		"MANDATE_FIELD_MASK_UNSPECIFIED": 0,
		"UMN":                            1,
		"MANDATE_SIGNED_TOKEN":           2,
	}
)

func (x MandateFieldMask) Enum() *MandateFieldMask {
	p := new(MandateFieldMask)
	*p = x
	return p
}

func (x MandateFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MandateFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_mandate_entity_proto_enumTypes[0].Descriptor()
}

func (MandateFieldMask) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_mandate_entity_proto_enumTypes[0]
}

func (x MandateFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MandateFieldMask.Descriptor instead.
func (MandateFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_mandate_entity_proto_rawDescGZIP(), []int{0}
}

type MandateEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for the mandate
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// id corresponding to recurring payment
	RecurringPaymentId string `protobuf:"bytes,2,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
	// request id for the mandate shared with vendor
	ReqId string `protobuf:"bytes,3,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// unique mandate number of length 32
	// this will be surfaced to the user and will be unique for each mandate across UPI
	Umn string `protobuf:"bytes,4,opt,name=umn,proto3" json:"umn,omitempty"`
	// denotes if the mandate can be revoked or not
	Revokeable bool `protobuf:"varint,5,opt,name=revokeable,proto3" json:"revokeable,omitempty"`
	// if false the payee shouldn't receive notification for the mandate
	// can be false only in case of one time mandate
	ShareToPayee bool `protobuf:"varint,6,opt,name=share_to_payee,json=shareToPayee,proto3" json:"share_to_payee,omitempty"`
	// denotes if the funds should be blocked in the payer's account
	// Applicable only for One time mandate
	BlockFund bool `protobuf:"varint,7,opt,name=block_fund,json=blockFund,proto3" json:"block_fund,omitempty"`
	// denotes if the mandate is initiated by payer/payee
	InitiatedBy MandateInitiatedBy `protobuf:"varint,9,opt,name=initiated_by,json=initiatedBy,proto3,enum=upi.mandate.MandateInitiatedBy" json:"initiated_by,omitempty"`
	// signed token sent by npci for authorisation of mandate execution by payer
	SignedToken string `protobuf:"bytes,10,opt,name=signed_token,json=signedToken,proto3" json:"signed_token,omitempty"`
	// time of creation of mandate request
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last updated time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// time of deletion deleted time
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *MandateEntity) Reset() {
	*x = MandateEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_mandate_entity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MandateEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MandateEntity) ProtoMessage() {}

func (x *MandateEntity) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_mandate_entity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MandateEntity.ProtoReflect.Descriptor instead.
func (*MandateEntity) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_mandate_entity_proto_rawDescGZIP(), []int{0}
}

func (x *MandateEntity) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MandateEntity) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

func (x *MandateEntity) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *MandateEntity) GetUmn() string {
	if x != nil {
		return x.Umn
	}
	return ""
}

func (x *MandateEntity) GetRevokeable() bool {
	if x != nil {
		return x.Revokeable
	}
	return false
}

func (x *MandateEntity) GetShareToPayee() bool {
	if x != nil {
		return x.ShareToPayee
	}
	return false
}

func (x *MandateEntity) GetBlockFund() bool {
	if x != nil {
		return x.BlockFund
	}
	return false
}

func (x *MandateEntity) GetInitiatedBy() MandateInitiatedBy {
	if x != nil {
		return x.InitiatedBy
	}
	return MandateInitiatedBy_MANDATE_INITIATED_BY_UNSPECIFIED
}

func (x *MandateEntity) GetSignedToken() string {
	if x != nil {
		return x.SignedToken
	}
	return ""
}

func (x *MandateEntity) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *MandateEntity) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *MandateEntity) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_upi_mandate_mandate_entity_proto protoreflect.FileDescriptor

var file_api_upi_mandate_mandate_entity_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xf7, 0x03, 0x0a, 0x0d, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x6d, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x6d, 0x6e,
	0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x24, 0x0a, 0x0e, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x70, 0x61, 0x79,
	0x65, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x54,
	0x6f, 0x50, 0x61, 0x79, 0x65, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x66, 0x75, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x46, 0x75, 0x6e, 0x64, 0x12, 0x42, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x52, 0x0b, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x59, 0x0a,
	0x10, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x22, 0x0a, 0x1e, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x4d, 0x4e, 0x10, 0x01, 0x12, 0x18,
	0x0a, 0x14, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x45, 0x44,
	0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x02, 0x42, 0x50, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x70, 0x69, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_upi_mandate_mandate_entity_proto_rawDescOnce sync.Once
	file_api_upi_mandate_mandate_entity_proto_rawDescData = file_api_upi_mandate_mandate_entity_proto_rawDesc
)

func file_api_upi_mandate_mandate_entity_proto_rawDescGZIP() []byte {
	file_api_upi_mandate_mandate_entity_proto_rawDescOnce.Do(func() {
		file_api_upi_mandate_mandate_entity_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_mandate_mandate_entity_proto_rawDescData)
	})
	return file_api_upi_mandate_mandate_entity_proto_rawDescData
}

var file_api_upi_mandate_mandate_entity_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_mandate_mandate_entity_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_upi_mandate_mandate_entity_proto_goTypes = []interface{}{
	(MandateFieldMask)(0),         // 0: upi.mandate.MandateFieldMask
	(*MandateEntity)(nil),         // 1: upi.mandate.MandateEntity
	(MandateInitiatedBy)(0),       // 2: upi.mandate.MandateInitiatedBy
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_api_upi_mandate_mandate_entity_proto_depIdxs = []int32{
	2, // 0: upi.mandate.MandateEntity.initiated_by:type_name -> upi.mandate.MandateInitiatedBy
	3, // 1: upi.mandate.MandateEntity.created_at:type_name -> google.protobuf.Timestamp
	3, // 2: upi.mandate.MandateEntity.updated_at:type_name -> google.protobuf.Timestamp
	3, // 3: upi.mandate.MandateEntity.deleted_at:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_upi_mandate_mandate_entity_proto_init() }
func file_api_upi_mandate_mandate_entity_proto_init() {
	if File_api_upi_mandate_mandate_entity_proto != nil {
		return
	}
	file_api_upi_mandate_mandate_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_upi_mandate_mandate_entity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MandateEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_mandate_mandate_entity_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_mandate_mandate_entity_proto_goTypes,
		DependencyIndexes: file_api_upi_mandate_mandate_entity_proto_depIdxs,
		EnumInfos:         file_api_upi_mandate_mandate_entity_proto_enumTypes,
		MessageInfos:      file_api_upi_mandate_mandate_entity_proto_msgTypes,
	}.Build()
	File_api_upi_mandate_mandate_entity_proto = out.File
	file_api_upi_mandate_mandate_entity_proto_rawDesc = nil
	file_api_upi_mandate_mandate_entity_proto_goTypes = nil
	file_api_upi_mandate_mandate_entity_proto_depIdxs = nil
}
