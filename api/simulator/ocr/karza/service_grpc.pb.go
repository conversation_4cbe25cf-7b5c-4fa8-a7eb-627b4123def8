// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/simulator/ocr/karza/service.proto

package karza

import (
	context "context"
	karza "github.com/epifi/gamma/api/vendors/karza"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	OCR_ExtractPassport_FullMethodName = "/simulator.ocr.karza.OCR/ExtractPassport"
)

// OCRClient is the client API for OCR service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OCRClient interface {
	ExtractPassport(ctx context.Context, in *karza.PassportOcrRequest, opts ...grpc.CallOption) (*karza.PassportOcrResponse, error)
}

type oCRClient struct {
	cc grpc.ClientConnInterface
}

func NewOCRClient(cc grpc.ClientConnInterface) OCRClient {
	return &oCRClient{cc}
}

func (c *oCRClient) ExtractPassport(ctx context.Context, in *karza.PassportOcrRequest, opts ...grpc.CallOption) (*karza.PassportOcrResponse, error) {
	out := new(karza.PassportOcrResponse)
	err := c.cc.Invoke(ctx, OCR_ExtractPassport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OCRServer is the server API for OCR service.
// All implementations should embed UnimplementedOCRServer
// for forward compatibility
type OCRServer interface {
	ExtractPassport(context.Context, *karza.PassportOcrRequest) (*karza.PassportOcrResponse, error)
}

// UnimplementedOCRServer should be embedded to have forward compatible implementations.
type UnimplementedOCRServer struct {
}

func (UnimplementedOCRServer) ExtractPassport(context.Context, *karza.PassportOcrRequest) (*karza.PassportOcrResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtractPassport not implemented")
}

// UnsafeOCRServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OCRServer will
// result in compilation errors.
type UnsafeOCRServer interface {
	mustEmbedUnimplementedOCRServer()
}

func RegisterOCRServer(s grpc.ServiceRegistrar, srv OCRServer) {
	s.RegisterService(&OCR_ServiceDesc, srv)
}

func _OCR_ExtractPassport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(karza.PassportOcrRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OCRServer).ExtractPassport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OCR_ExtractPassport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OCRServer).ExtractPassport(ctx, req.(*karza.PassportOcrRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OCR_ServiceDesc is the grpc.ServiceDesc for OCR service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OCR_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "simulator.ocr.karza.OCR",
	HandlerType: (*OCRServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ExtractPassport",
			Handler:    _OCR_ExtractPassport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/simulator/ocr/karza/service.proto",
}
