// Code generated by MockGen. DO NOT EDIT.
// Source: api/./simulator/namecheck/inhouse/namematch_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	inhouse "github.com/epifi/gamma/api/vendors/inhouse"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockNameMatchClient is a mock of NameMatchClient interface.
type MockNameMatchClient struct {
	ctrl     *gomock.Controller
	recorder *MockNameMatchClientMockRecorder
}

// MockNameMatchClientMockRecorder is the mock recorder for MockNameMatchClient.
type MockNameMatchClientMockRecorder struct {
	mock *MockNameMatchClient
}

// NewMockNameMatchClient creates a new mock instance.
func NewMockNameMatchClient(ctrl *gomock.Controller) *MockNameMatchClient {
	mock := &MockNameMatchClient{ctrl: ctrl}
	mock.recorder = &MockNameMatchClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNameMatchClient) EXPECT() *MockNameMatchClientMockRecorder {
	return m.recorder
}

// NameMatch mocks base method.
func (m *MockNameMatchClient) NameMatch(ctx context.Context, in *inhouse.NameMatchRequest, opts ...grpc.CallOption) (*inhouse.NameMatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NameMatch", varargs...)
	ret0, _ := ret[0].(*inhouse.NameMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NameMatch indicates an expected call of NameMatch.
func (mr *MockNameMatchClientMockRecorder) NameMatch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NameMatch", reflect.TypeOf((*MockNameMatchClient)(nil).NameMatch), varargs...)
}

// MockNameMatchServer is a mock of NameMatchServer interface.
type MockNameMatchServer struct {
	ctrl     *gomock.Controller
	recorder *MockNameMatchServerMockRecorder
}

// MockNameMatchServerMockRecorder is the mock recorder for MockNameMatchServer.
type MockNameMatchServerMockRecorder struct {
	mock *MockNameMatchServer
}

// NewMockNameMatchServer creates a new mock instance.
func NewMockNameMatchServer(ctrl *gomock.Controller) *MockNameMatchServer {
	mock := &MockNameMatchServer{ctrl: ctrl}
	mock.recorder = &MockNameMatchServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNameMatchServer) EXPECT() *MockNameMatchServerMockRecorder {
	return m.recorder
}

// NameMatch mocks base method.
func (m *MockNameMatchServer) NameMatch(arg0 context.Context, arg1 *inhouse.NameMatchRequest) (*inhouse.NameMatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NameMatch", arg0, arg1)
	ret0, _ := ret[0].(*inhouse.NameMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NameMatch indicates an expected call of NameMatch.
func (mr *MockNameMatchServerMockRecorder) NameMatch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NameMatch", reflect.TypeOf((*MockNameMatchServer)(nil).NameMatch), arg0, arg1)
}

// MockUnsafeNameMatchServer is a mock of UnsafeNameMatchServer interface.
type MockUnsafeNameMatchServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeNameMatchServerMockRecorder
}

// MockUnsafeNameMatchServerMockRecorder is the mock recorder for MockUnsafeNameMatchServer.
type MockUnsafeNameMatchServerMockRecorder struct {
	mock *MockUnsafeNameMatchServer
}

// NewMockUnsafeNameMatchServer creates a new mock instance.
func NewMockUnsafeNameMatchServer(ctrl *gomock.Controller) *MockUnsafeNameMatchServer {
	mock := &MockUnsafeNameMatchServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeNameMatchServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeNameMatchServer) EXPECT() *MockUnsafeNameMatchServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedNameMatchServer mocks base method.
func (m *MockUnsafeNameMatchServer) mustEmbedUnimplementedNameMatchServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedNameMatchServer")
}

// mustEmbedUnimplementedNameMatchServer indicates an expected call of mustEmbedUnimplementedNameMatchServer.
func (mr *MockUnsafeNameMatchServerMockRecorder) mustEmbedUnimplementedNameMatchServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedNameMatchServer", reflect.TypeOf((*MockUnsafeNameMatchServer)(nil).mustEmbedUnimplementedNameMatchServer))
}
