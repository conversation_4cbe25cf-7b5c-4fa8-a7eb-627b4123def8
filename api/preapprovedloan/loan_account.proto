syntax = "proto3";

package preapprovedloan;

import "api/preapprovedloan/collateral.proto";
import "api/preapprovedloan/enums.proto";
import "api/preapprovedloan/enums/lms.proto";
import "api/preapprovedloan/enums/program.proto";
import "api/typesv2/bank_account_details.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

//go:generate gen_sql -types=LoanAccount,LoanAmountInfo,LoanAccountDetails
message LoanAccount {
  string id = 1;
  string actor_id = 2;
  Vendor vendor = 3;
  string account_number = 4;
  LoanType loan_type = 5;
  string ifsc_code = 6;
  LoanAmountInfo loan_amount_info = 7;
  google.type.Date loan_end_date = 8;
  google.type.Date maturity_date = 9;
  LoanAccountDetails details = 10;
  LoanAccountStatus status = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  google.protobuf.Timestamp deleted_at = 14;
  LoanProgram loan_program = 15;
  // denotes the Fi lms partner who is managing this loan account.
  enums.LmsPartner lms_partner = 16;
  // denotes a unique id of loan account in partner lms system.
  string lms_partner_loan_id = 17;
}

message LoanAmountInfo {
  google.type.Money loan_amount = 1;
  google.type.Money disbursed_amount = 2;
  // For LAMF_FIFTYFIN outstanding_amount field stores the outstanding principal amount
  google.type.Money outstanding_amount = 3;
  google.type.Money total_payable_amount = 4;
}


message LoanAccountDetails {
  double interest_rate = 1;
  int32 tenure_in_months = 2;
  string loan_name = 3;
  double apr_rate = 4;
  LoanDocuments loan_documents = 5;
  RecurringPaymentDetails recurring_payment_details = 6;
  RepaymentAccountDetails repayment_account_details = 7;
  CollateralDetails collateral_details = 8;
  google.type.Date loan_start_date = 9;
  LenderAccountDetails lender_account_details = 10;
  // denotes the product in partner lms system with which the account is associated.
  string lms_partner_product_id = 11;
  // timestamp when the loan account was last synced with the partner lms system.
  google.protobuf.Timestamp lms_synced_at = 12;
  enums.LoanProgramVersion loan_program_version = 13;
}

message LoanAccountVendorSpecificDetails {
  oneof details {
    MoneyViewLoanAccountDetails moneyview_details = 1;
    AbflLoanAccountDetails abfl_details = 2;
  }
}

message MoneyViewLoanAccountDetails {
  // denotes the id of the lead which was used for initiating the loan application journey, needed for generating pwa url for loan servicing flows.
  string lead_id = 1;
}

message AbflLoanAccountDetails {
  string deal_number = 1;
  string account_id = 2; // Vendor request id
}


// This can be used for storing Loan account details of the loan providing entity.
// Incase of fiftyfin lamf, bajaj is the loan providing entity. This object will store loan account details present with Bajaj.
message LenderAccountDetails {
  string account_number = 1;
  // denotes the vendor specific details for the loan account.
  LoanAccountVendorSpecificDetails vendor_specific_details = 2;
}

message LoanDocuments {
  string account_statement_pdf_url = 1;
}

message RecurringPaymentDetails {
  string id = 1;
  RegistrationStatus registration_status = 2;
  enum RegistrationStatus {
    REGISTRATION_STATUS_UNSPECIFIED = 0;
    REGISTRATION_STATUS_SUCCESS = 1;
    REGISTRATION_STATUS_FAILURE = 2;
    REGISTRATION_STATUS_IN_PROGRESS = 3;
  }
}

message RepaymentAccountDetails {
  oneof AccountDetails {
    FiftyFinRepaymentAccountDetails fifty_fin_repayment_account_details = 1;
  }
}

message FiftyFinRepaymentAccountDetails {
  api.typesv2.BankAccountDetails principal_repayment_account_details = 1;
  api.typesv2.BankAccountDetails interest_repayment_account_details = 2;
}
