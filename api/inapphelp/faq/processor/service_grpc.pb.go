// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/inapphelp/faq/processor/service.proto

package processor

import (
	context "context"
	rpc "github.com/epifi/be-common/api/rpc"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FAQProcessor_RefreshFAQ_FullMethodName        = "/processor.FAQProcessor/RefreshFAQ"
	FAQProcessor_RefreshRelatedFaq_FullMethodName = "/processor.FAQProcessor/RefreshRelatedFaq"
)

// FAQProcessorClient is the client API for FAQProcessor service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FAQProcessorClient interface {
	// This RPC takes FAQ dump from freshdesk and writes to DB
	// This will be called periodically using a cron job
	// clients should not use it for any case
	// Throws internal server error if any error is encountered during the dump
	RefreshFAQ(ctx context.Context, in *RefreshFAQRequest, opts ...grpc.CallOption) (*rpc.Status, error)
	// RPC to refresh related FAQ's mapping in database
	// Reads from S3 buckets and dumps/updates in inapphelp DB
	// Dump is taken on best effort basis
	RefreshRelatedFaq(ctx context.Context, in *RefreshRelatedFaqRequest, opts ...grpc.CallOption) (*RefreshRelatedFaqResponse, error)
}

type fAQProcessorClient struct {
	cc grpc.ClientConnInterface
}

func NewFAQProcessorClient(cc grpc.ClientConnInterface) FAQProcessorClient {
	return &fAQProcessorClient{cc}
}

func (c *fAQProcessorClient) RefreshFAQ(ctx context.Context, in *RefreshFAQRequest, opts ...grpc.CallOption) (*rpc.Status, error) {
	out := new(rpc.Status)
	err := c.cc.Invoke(ctx, FAQProcessor_RefreshFAQ_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fAQProcessorClient) RefreshRelatedFaq(ctx context.Context, in *RefreshRelatedFaqRequest, opts ...grpc.CallOption) (*RefreshRelatedFaqResponse, error) {
	out := new(RefreshRelatedFaqResponse)
	err := c.cc.Invoke(ctx, FAQProcessor_RefreshRelatedFaq_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FAQProcessorServer is the server API for FAQProcessor service.
// All implementations should embed UnimplementedFAQProcessorServer
// for forward compatibility
type FAQProcessorServer interface {
	// This RPC takes FAQ dump from freshdesk and writes to DB
	// This will be called periodically using a cron job
	// clients should not use it for any case
	// Throws internal server error if any error is encountered during the dump
	RefreshFAQ(context.Context, *RefreshFAQRequest) (*rpc.Status, error)
	// RPC to refresh related FAQ's mapping in database
	// Reads from S3 buckets and dumps/updates in inapphelp DB
	// Dump is taken on best effort basis
	RefreshRelatedFaq(context.Context, *RefreshRelatedFaqRequest) (*RefreshRelatedFaqResponse, error)
}

// UnimplementedFAQProcessorServer should be embedded to have forward compatible implementations.
type UnimplementedFAQProcessorServer struct {
}

func (UnimplementedFAQProcessorServer) RefreshFAQ(context.Context, *RefreshFAQRequest) (*rpc.Status, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshFAQ not implemented")
}
func (UnimplementedFAQProcessorServer) RefreshRelatedFaq(context.Context, *RefreshRelatedFaqRequest) (*RefreshRelatedFaqResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshRelatedFaq not implemented")
}

// UnsafeFAQProcessorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FAQProcessorServer will
// result in compilation errors.
type UnsafeFAQProcessorServer interface {
	mustEmbedUnimplementedFAQProcessorServer()
}

func RegisterFAQProcessorServer(s grpc.ServiceRegistrar, srv FAQProcessorServer) {
	s.RegisterService(&FAQProcessor_ServiceDesc, srv)
}

func _FAQProcessor_RefreshFAQ_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshFAQRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FAQProcessorServer).RefreshFAQ(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FAQProcessor_RefreshFAQ_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FAQProcessorServer).RefreshFAQ(ctx, req.(*RefreshFAQRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FAQProcessor_RefreshRelatedFaq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshRelatedFaqRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FAQProcessorServer).RefreshRelatedFaq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FAQProcessor_RefreshRelatedFaq_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FAQProcessorServer).RefreshRelatedFaq(ctx, req.(*RefreshRelatedFaqRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FAQProcessor_ServiceDesc is the grpc.ServiceDesc for FAQProcessor service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FAQProcessor_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processor.FAQProcessor",
	HandlerType: (*FAQProcessorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RefreshFAQ",
			Handler:    _FAQProcessor_RefreshFAQ_Handler,
		},
		{
			MethodName: "RefreshRelatedFaq",
			Handler:    _FAQProcessor_RefreshRelatedFaq_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/inapphelp/faq/processor/service.proto",
}
