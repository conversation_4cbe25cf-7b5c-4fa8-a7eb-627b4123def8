// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/tiering/data_collector.proto

package tiering

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/salaryprogram/enums"

	enums1 "github.com/epifi/gamma/api/tiering/enums"

	kyc "github.com/epifi/gamma/api/kyc"

	salaryprogram "github.com/epifi/gamma/api/salaryprogram"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = enums.SalaryBand(0)

	_ = enums1.Tier(0)

	_ = kyc.KYCLevel(0)

	_ = salaryprogram.SalaryActivationType(0)
)

// Validate checks the field values on CollectedData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CollectedData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectedData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CollectedDataMultiError, or
// nil if none found.
func (m *CollectedData) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectedData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *CollectedData_BalanceData:
		if v == nil {
			err := CollectedDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBalanceData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "BalanceData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "BalanceData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBalanceData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectedDataValidationError{
					field:  "BalanceData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CollectedData_KycData:
		if v == nil {
			err := CollectedDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetKycData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "KycData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "KycData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetKycData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectedDataValidationError{
					field:  "KycData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CollectedData_SalaryData:
		if v == nil {
			err := CollectedDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSalaryData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "SalaryData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "SalaryData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSalaryData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectedDataValidationError{
					field:  "SalaryData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CollectedData_BaseTierData:
		if v == nil {
			err := CollectedDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBaseTierData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "BaseTierData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "BaseTierData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBaseTierData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectedDataValidationError{
					field:  "BaseTierData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CollectedData_UsStocksData:
		if v == nil {
			err := CollectedDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUsStocksData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "UsStocksData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "UsStocksData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUsStocksData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectedDataValidationError{
					field:  "UsStocksData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CollectedData_DepositsData:
		if v == nil {
			err := CollectedDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDepositsData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "DepositsData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectedDataValidationError{
						field:  "DepositsData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDepositsData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectedDataValidationError{
					field:  "DepositsData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CollectedDataMultiError(errors)
	}

	return nil
}

// CollectedDataMultiError is an error wrapping multiple validation errors
// returned by CollectedData.ValidateAll() if the designated constraints
// aren't met.
type CollectedDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectedDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectedDataMultiError) AllErrors() []error { return m }

// CollectedDataValidationError is the validation error returned by
// CollectedData.Validate if the designated constraints aren't met.
type CollectedDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectedDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectedDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectedDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectedDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectedDataValidationError) ErrorName() string { return "CollectedDataValidationError" }

// Error satisfies the builtin error interface
func (e CollectedDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectedData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectedDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectedDataValidationError{}

// Validate checks the field values on BalanceData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BalanceData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BalanceData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BalanceDataMultiError, or
// nil if none found.
func (m *BalanceData) ValidateAll() error {
	return m.validate(true)
}

func (m *BalanceData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAvailableBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceDataValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceDataValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceDataValidationError{
				field:  "AvailableBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLedgerBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceDataValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceDataValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLedgerBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceDataValidationError{
				field:  "LedgerBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBalanceAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceDataValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceDataValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceDataValidationError{
				field:  "BalanceAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BalanceDataMultiError(errors)
	}

	return nil
}

// BalanceDataMultiError is an error wrapping multiple validation errors
// returned by BalanceData.ValidateAll() if the designated constraints aren't met.
type BalanceDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BalanceDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BalanceDataMultiError) AllErrors() []error { return m }

// BalanceDataValidationError is the validation error returned by
// BalanceData.Validate if the designated constraints aren't met.
type BalanceDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BalanceDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BalanceDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BalanceDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BalanceDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BalanceDataValidationError) ErrorName() string { return "BalanceDataValidationError" }

// Error satisfies the builtin error interface
func (e BalanceDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBalanceData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BalanceDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BalanceDataValidationError{}

// Validate checks the field values on KycData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KycData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KycData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in KycDataMultiError, or nil if none found.
func (m *KycData) ValidateAll() error {
	return m.validate(true)
}

func (m *KycData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KycLevel

	// no validation rules for KycProvider

	// no validation rules for KycType

	// no validation rules for KycStatus

	if len(errors) > 0 {
		return KycDataMultiError(errors)
	}

	return nil
}

// KycDataMultiError is an error wrapping multiple validation errors returned
// by KycData.ValidateAll() if the designated constraints aren't met.
type KycDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KycDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KycDataMultiError) AllErrors() []error { return m }

// KycDataValidationError is the validation error returned by KycData.Validate
// if the designated constraints aren't met.
type KycDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KycDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KycDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KycDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KycDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KycDataValidationError) ErrorName() string { return "KycDataValidationError" }

// Error satisfies the builtin error interface
func (e KycDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKycData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KycDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KycDataValidationError{}

// Validate checks the field values on SalaryData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SalaryData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SalaryData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SalaryDataMultiError, or
// nil if none found.
func (m *SalaryData) ValidateAll() error {
	return m.validate(true)
}

func (m *SalaryData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsSalaried

	if all {
		switch v := interface{}(m.GetMinReqAmountForSalary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryDataValidationError{
					field:  "MinReqAmountForSalary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryDataValidationError{
					field:  "MinReqAmountForSalary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinReqAmountForSalary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryDataValidationError{
				field:  "MinReqAmountForSalary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SalaryActivationType

	// no validation rules for SalaryBand

	// no validation rules for B2BSalaryProgramVerificationStatus

	// no validation rules for B2BSalaryBand

	if len(errors) > 0 {
		return SalaryDataMultiError(errors)
	}

	return nil
}

// SalaryDataMultiError is an error wrapping multiple validation errors
// returned by SalaryData.ValidateAll() if the designated constraints aren't met.
type SalaryDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SalaryDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SalaryDataMultiError) AllErrors() []error { return m }

// SalaryDataValidationError is the validation error returned by
// SalaryData.Validate if the designated constraints aren't met.
type SalaryDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SalaryDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SalaryDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SalaryDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SalaryDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SalaryDataValidationError) ErrorName() string { return "SalaryDataValidationError" }

// Error satisfies the builtin error interface
func (e SalaryDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSalaryData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SalaryDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SalaryDataValidationError{}

// Validate checks the field values on BaseTierData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BaseTierData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BaseTierData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BaseTierDataMultiError, or
// nil if none found.
func (m *BaseTierData) ValidateAll() error {
	return m.validate(true)
}

func (m *BaseTierData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorBaseTier

	if len(errors) > 0 {
		return BaseTierDataMultiError(errors)
	}

	return nil
}

// BaseTierDataMultiError is an error wrapping multiple validation errors
// returned by BaseTierData.ValidateAll() if the designated constraints aren't met.
type BaseTierDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BaseTierDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BaseTierDataMultiError) AllErrors() []error { return m }

// BaseTierDataValidationError is the validation error returned by
// BaseTierData.Validate if the designated constraints aren't met.
type BaseTierDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BaseTierDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BaseTierDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BaseTierDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BaseTierDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BaseTierDataValidationError) ErrorName() string { return "BaseTierDataValidationError" }

// Error satisfies the builtin error interface
func (e BaseTierDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBaseTierData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BaseTierDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BaseTierDataValidationError{}

// Validate checks the field values on UsStocksData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UsStocksData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UsStocksData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UsStocksDataMultiError, or
// nil if none found.
func (m *UsStocksData) ValidateAll() error {
	return m.validate(true)
}

func (m *UsStocksData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetWalletAddFundsValueInL30D()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UsStocksDataValidationError{
					field:  "WalletAddFundsValueInL30D",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UsStocksDataValidationError{
					field:  "WalletAddFundsValueInL30D",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWalletAddFundsValueInL30D()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UsStocksDataValidationError{
				field:  "WalletAddFundsValueInL30D",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UsStocksDataMultiError(errors)
	}

	return nil
}

// UsStocksDataMultiError is an error wrapping multiple validation errors
// returned by UsStocksData.ValidateAll() if the designated constraints aren't met.
type UsStocksDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UsStocksDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UsStocksDataMultiError) AllErrors() []error { return m }

// UsStocksDataValidationError is the validation error returned by
// UsStocksData.Validate if the designated constraints aren't met.
type UsStocksDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UsStocksDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UsStocksDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UsStocksDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UsStocksDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UsStocksDataValidationError) ErrorName() string { return "UsStocksDataValidationError" }

// Error satisfies the builtin error interface
func (e UsStocksDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUsStocksData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UsStocksDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UsStocksDataValidationError{}

// Validate checks the field values on DepositsData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DepositsData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DepositsData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DepositsDataMultiError, or
// nil if none found.
func (m *DepositsData) ValidateAll() error {
	return m.validate(true)
}

func (m *DepositsData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTotalDeposits()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositsDataValidationError{
					field:  "TotalDeposits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositsDataValidationError{
					field:  "TotalDeposits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalDeposits()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositsDataValidationError{
				field:  "TotalDeposits",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DepositsDataMultiError(errors)
	}

	return nil
}

// DepositsDataMultiError is an error wrapping multiple validation errors
// returned by DepositsData.ValidateAll() if the designated constraints aren't met.
type DepositsDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DepositsDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DepositsDataMultiError) AllErrors() []error { return m }

// DepositsDataValidationError is the validation error returned by
// DepositsData.Validate if the designated constraints aren't met.
type DepositsDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DepositsDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DepositsDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DepositsDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DepositsDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DepositsDataValidationError) ErrorName() string { return "DepositsDataValidationError" }

// Error satisfies the builtin error interface
func (e DepositsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDepositsData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DepositsDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DepositsDataValidationError{}

// Validate checks the field values on SegmentData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SegmentData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SegmentData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SegmentDataMultiError, or
// nil if none found.
func (m *SegmentData) ValidateAll() error {
	return m.validate(true)
}

func (m *SegmentData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ShouldExcludeSalaryB2CCriteria

	// no validation rules for ShouldExcludeAaSalaryCriteria

	if len(errors) > 0 {
		return SegmentDataMultiError(errors)
	}

	return nil
}

// SegmentDataMultiError is an error wrapping multiple validation errors
// returned by SegmentData.ValidateAll() if the designated constraints aren't met.
type SegmentDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SegmentDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SegmentDataMultiError) AllErrors() []error { return m }

// SegmentDataValidationError is the validation error returned by
// SegmentData.Validate if the designated constraints aren't met.
type SegmentDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SegmentDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SegmentDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SegmentDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SegmentDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SegmentDataValidationError) ErrorName() string { return "SegmentDataValidationError" }

// Error satisfies the builtin error interface
func (e SegmentDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSegmentData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SegmentDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SegmentDataValidationError{}

// Validate checks the field values on GatherDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GatherDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GatherDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GatherDataResponseMultiError, or nil if none found.
func (m *GatherDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GatherDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBalanceData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "BalanceData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "BalanceData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GatherDataResponseValidationError{
				field:  "BalanceData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKycData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "KycData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "KycData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GatherDataResponseValidationError{
				field:  "KycData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSalaryData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "SalaryData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "SalaryData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSalaryData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GatherDataResponseValidationError{
				field:  "SalaryData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBaseTierData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "BaseTierData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "BaseTierData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseTierData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GatherDataResponseValidationError{
				field:  "BaseTierData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUsStocksData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "UsStocksData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "UsStocksData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUsStocksData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GatherDataResponseValidationError{
				field:  "UsStocksData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDepositsData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "DepositsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "DepositsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDepositsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GatherDataResponseValidationError{
				field:  "DepositsData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSegmentData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "SegmentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GatherDataResponseValidationError{
					field:  "SegmentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSegmentData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GatherDataResponseValidationError{
				field:  "SegmentData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GatherDataResponseMultiError(errors)
	}

	return nil
}

// GatherDataResponseMultiError is an error wrapping multiple validation errors
// returned by GatherDataResponse.ValidateAll() if the designated constraints
// aren't met.
type GatherDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GatherDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GatherDataResponseMultiError) AllErrors() []error { return m }

// GatherDataResponseValidationError is the validation error returned by
// GatherDataResponse.Validate if the designated constraints aren't met.
type GatherDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GatherDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GatherDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GatherDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GatherDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GatherDataResponseValidationError) ErrorName() string {
	return "GatherDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GatherDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGatherDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GatherDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GatherDataResponseValidationError{}

// Validate checks the field values on TierOptionType with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TierOptionType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TierOptionType with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TierOptionTypeMultiError,
// or nil if none found.
func (m *TierOptionType) ValidateAll() error {
	return m.validate(true)
}

func (m *TierOptionType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tier

	// no validation rules for CriteriaOptionType

	if len(errors) > 0 {
		return TierOptionTypeMultiError(errors)
	}

	return nil
}

// TierOptionTypeMultiError is an error wrapping multiple validation errors
// returned by TierOptionType.ValidateAll() if the designated constraints
// aren't met.
type TierOptionTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TierOptionTypeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TierOptionTypeMultiError) AllErrors() []error { return m }

// TierOptionTypeValidationError is the validation error returned by
// TierOptionType.Validate if the designated constraints aren't met.
type TierOptionTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TierOptionTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TierOptionTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TierOptionTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TierOptionTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TierOptionTypeValidationError) ErrorName() string { return "TierOptionTypeValidationError" }

// Error satisfies the builtin error interface
func (e TierOptionTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTierOptionType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TierOptionTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TierOptionTypeValidationError{}
