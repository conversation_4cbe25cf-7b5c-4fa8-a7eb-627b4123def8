// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/matrix/internal/customer_application.proto

package matrix

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Gender(0)
)

// Validate checks the field values on CustomerApplication with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomerApplication) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerApplication with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerApplicationMultiError, or nil if none found.
func (m *CustomerApplication) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerApplication) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ApplicantId

	// no validation rules for ClientRequestId

	// no validation rules for Product

	// no validation rules for OrchestrationFlow

	// no validation rules for Status

	// no validation rules for CurrentStage

	if all {
		switch v := interface{}(m.GetOverrideOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerApplicationValidationError{
					field:  "OverrideOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerApplicationValidationError{
					field:  "OverrideOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOverrideOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerApplicationValidationError{
				field:  "OverrideOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpiryAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerApplicationValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerApplicationValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerApplicationValidationError{
				field:  "ExpiryAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetApplicationData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerApplicationValidationError{
					field:  "ApplicationData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerApplicationValidationError{
					field:  "ApplicationData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplicationData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerApplicationValidationError{
				field:  "ApplicationData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrchestrationFlowData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerApplicationValidationError{
					field:  "OrchestrationFlowData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerApplicationValidationError{
					field:  "OrchestrationFlowData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrchestrationFlowData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerApplicationValidationError{
				field:  "OrchestrationFlowData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CustomerApplicationMultiError(errors)
	}

	return nil
}

// CustomerApplicationMultiError is an error wrapping multiple validation
// errors returned by CustomerApplication.ValidateAll() if the designated
// constraints aren't met.
type CustomerApplicationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerApplicationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerApplicationMultiError) AllErrors() []error { return m }

// CustomerApplicationValidationError is the validation error returned by
// CustomerApplication.Validate if the designated constraints aren't met.
type CustomerApplicationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerApplicationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerApplicationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerApplicationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerApplicationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerApplicationValidationError) ErrorName() string {
	return "CustomerApplicationValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerApplicationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerApplication.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerApplicationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerApplicationValidationError{}

// Validate checks the field values on OverrideOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OverrideOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OverrideOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OverrideOptionsMultiError, or nil if none found.
func (m *OverrideOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *OverrideOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return OverrideOptionsMultiError(errors)
	}

	return nil
}

// OverrideOptionsMultiError is an error wrapping multiple validation errors
// returned by OverrideOptions.ValidateAll() if the designated constraints
// aren't met.
type OverrideOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OverrideOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OverrideOptionsMultiError) AllErrors() []error { return m }

// OverrideOptionsValidationError is the validation error returned by
// OverrideOptions.Validate if the designated constraints aren't met.
type OverrideOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OverrideOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OverrideOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OverrideOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OverrideOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OverrideOptionsValidationError) ErrorName() string { return "OverrideOptionsValidationError" }

// Error satisfies the builtin error interface
func (e OverrideOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOverrideOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OverrideOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OverrideOptionsValidationError{}

// Validate checks the field values on ApplicationData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ApplicationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicationDataMultiError, or nil if none found.
func (m *ApplicationData) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDateOfBirth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicationDataValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicationDataValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfBirth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicationDataValidationError{
				field:  "DateOfBirth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPanName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicationDataValidationError{
					field:  "PanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicationDataValidationError{
					field:  "PanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicationDataValidationError{
				field:  "PanName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApplicationDataMultiError(errors)
	}

	return nil
}

// ApplicationDataMultiError is an error wrapping multiple validation errors
// returned by ApplicationData.ValidateAll() if the designated constraints
// aren't met.
type ApplicationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicationDataMultiError) AllErrors() []error { return m }

// ApplicationDataValidationError is the validation error returned by
// ApplicationData.Validate if the designated constraints aren't met.
type ApplicationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicationDataValidationError) ErrorName() string { return "ApplicationDataValidationError" }

// Error satisfies the builtin error interface
func (e ApplicationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicationDataValidationError{}

// Validate checks the field values on OrchestrationFlowData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrchestrationFlowData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrchestrationFlowData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrchestrationFlowDataMultiError, or nil if none found.
func (m *OrchestrationFlowData) ValidateAll() error {
	return m.validate(true)
}

func (m *OrchestrationFlowData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *OrchestrationFlowData_OkycData:
		if v == nil {
			err := OrchestrationFlowDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOkycData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OrchestrationFlowDataValidationError{
						field:  "OkycData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OrchestrationFlowDataValidationError{
						field:  "OkycData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOkycData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OrchestrationFlowDataValidationError{
					field:  "OkycData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return OrchestrationFlowDataMultiError(errors)
	}

	return nil
}

// OrchestrationFlowDataMultiError is an error wrapping multiple validation
// errors returned by OrchestrationFlowData.ValidateAll() if the designated
// constraints aren't met.
type OrchestrationFlowDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrchestrationFlowDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrchestrationFlowDataMultiError) AllErrors() []error { return m }

// OrchestrationFlowDataValidationError is the validation error returned by
// OrchestrationFlowData.Validate if the designated constraints aren't met.
type OrchestrationFlowDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrchestrationFlowDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrchestrationFlowDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrchestrationFlowDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrchestrationFlowDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrchestrationFlowDataValidationError) ErrorName() string {
	return "OrchestrationFlowDataValidationError"
}

// Error satisfies the builtin error interface
func (e OrchestrationFlowDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrchestrationFlowData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrchestrationFlowDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrchestrationFlowDataValidationError{}

// Validate checks the field values on OKYCData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OKYCData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OKYCData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OKYCDataMultiError, or nil
// if none found.
func (m *OKYCData) ValidateAll() error {
	return m.validate(true)
}

func (m *OKYCData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPersonalData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OKYCDataValidationError{
					field:  "PersonalData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OKYCDataValidationError{
					field:  "PersonalData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OKYCDataValidationError{
				field:  "PersonalData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AadhaarXml

	// no validation rules for UserSelfie

	// no validation rules for AadhaarPhoto

	if len(errors) > 0 {
		return OKYCDataMultiError(errors)
	}

	return nil
}

// OKYCDataMultiError is an error wrapping multiple validation errors returned
// by OKYCData.ValidateAll() if the designated constraints aren't met.
type OKYCDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OKYCDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OKYCDataMultiError) AllErrors() []error { return m }

// OKYCDataValidationError is the validation error returned by
// OKYCData.Validate if the designated constraints aren't met.
type OKYCDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OKYCDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OKYCDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OKYCDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OKYCDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OKYCDataValidationError) ErrorName() string { return "OKYCDataValidationError" }

// Error satisfies the builtin error interface
func (e OKYCDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOKYCData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OKYCDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OKYCDataValidationError{}

// Validate checks the field values on OKYCPersonalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OKYCPersonalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OKYCPersonalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OKYCPersonalDetailsMultiError, or nil if none found.
func (m *OKYCPersonalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OKYCPersonalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaskedAadhaar

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OKYCPersonalDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OKYCPersonalDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OKYCPersonalDetailsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OKYCPersonalDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OKYCPersonalDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OKYCPersonalDetailsValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OKYCPersonalDetailsValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OKYCPersonalDetailsValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OKYCPersonalDetailsValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OKYCPersonalDetailsMultiError(errors)
	}

	return nil
}

// OKYCPersonalDetailsMultiError is an error wrapping multiple validation
// errors returned by OKYCPersonalDetails.ValidateAll() if the designated
// constraints aren't met.
type OKYCPersonalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OKYCPersonalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OKYCPersonalDetailsMultiError) AllErrors() []error { return m }

// OKYCPersonalDetailsValidationError is the validation error returned by
// OKYCPersonalDetails.Validate if the designated constraints aren't met.
type OKYCPersonalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OKYCPersonalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OKYCPersonalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OKYCPersonalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OKYCPersonalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OKYCPersonalDetailsValidationError) ErrorName() string {
	return "OKYCPersonalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e OKYCPersonalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOKYCPersonalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OKYCPersonalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OKYCPersonalDetailsValidationError{}
