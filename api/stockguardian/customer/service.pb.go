// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/customer/service.proto

package customer

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	enums "github.com/epifi/gringott/api/stockguardian/customer/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateCustomerResponse_Status int32

const (
	CreateCustomerResponse_OK                           CreateCustomerResponse_Status = 0
	CreateCustomerResponse_DEDUPE_CUSTOMER_PAN          CreateCustomerResponse_Status = 101
	CreateCustomerResponse_DEDUPE_CUSTOMER_PHONE_NUMBER CreateCustomerResponse_Status = 102
)

// Enum value maps for CreateCustomerResponse_Status.
var (
	CreateCustomerResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "DEDUPE_CUSTOMER_PAN",
		102: "DEDUPE_CUSTOMER_PHONE_NUMBER",
	}
	CreateCustomerResponse_Status_value = map[string]int32{
		"OK":                           0,
		"DEDUPE_CUSTOMER_PAN":          101,
		"DEDUPE_CUSTOMER_PHONE_NUMBER": 102,
	}
)

func (x CreateCustomerResponse_Status) Enum() *CreateCustomerResponse_Status {
	p := new(CreateCustomerResponse_Status)
	*p = x
	return p
}

func (x CreateCustomerResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateCustomerResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_customer_service_proto_enumTypes[0].Descriptor()
}

func (CreateCustomerResponse_Status) Type() protoreflect.EnumType {
	return &file_api_stockguardian_customer_service_proto_enumTypes[0]
}

func (x CreateCustomerResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateCustomerResponse_Status.Descriptor instead.
func (CreateCustomerResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_customer_service_proto_rawDescGZIP(), []int{1, 0}
}

type GetCustomerDetailsResponse_Status int32

const (
	GetCustomerDetailsResponse_OK                      GetCustomerDetailsResponse_Status = 0
	GetCustomerDetailsResponse_CUSTOMER_DOES_NOT_EXIST GetCustomerDetailsResponse_Status = 101
)

// Enum value maps for GetCustomerDetailsResponse_Status.
var (
	GetCustomerDetailsResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "CUSTOMER_DOES_NOT_EXIST",
	}
	GetCustomerDetailsResponse_Status_value = map[string]int32{
		"OK":                      0,
		"CUSTOMER_DOES_NOT_EXIST": 101,
	}
)

func (x GetCustomerDetailsResponse_Status) Enum() *GetCustomerDetailsResponse_Status {
	p := new(GetCustomerDetailsResponse_Status)
	*p = x
	return p
}

func (x GetCustomerDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetCustomerDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_customer_service_proto_enumTypes[1].Descriptor()
}

func (GetCustomerDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_stockguardian_customer_service_proto_enumTypes[1]
}

func (x GetCustomerDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetCustomerDetailsResponse_Status.Descriptor instead.
func (GetCustomerDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_customer_service_proto_rawDescGZIP(), []int{4, 0}
}

type DedupeCheckResponse_Status int32

const (
	DedupeCheckResponse_OK                           DedupeCheckResponse_Status = 0
	DedupeCheckResponse_DEDUPE_CUSTOMER_PAN          DedupeCheckResponse_Status = 101
	DedupeCheckResponse_DEDUPE_CUSTOMER_PHONE_NUMBER DedupeCheckResponse_Status = 102
)

// Enum value maps for DedupeCheckResponse_Status.
var (
	DedupeCheckResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "DEDUPE_CUSTOMER_PAN",
		102: "DEDUPE_CUSTOMER_PHONE_NUMBER",
	}
	DedupeCheckResponse_Status_value = map[string]int32{
		"OK":                           0,
		"DEDUPE_CUSTOMER_PAN":          101,
		"DEDUPE_CUSTOMER_PHONE_NUMBER": 102,
	}
)

func (x DedupeCheckResponse_Status) Enum() *DedupeCheckResponse_Status {
	p := new(DedupeCheckResponse_Status)
	*p = x
	return p
}

func (x DedupeCheckResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DedupeCheckResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_customer_service_proto_enumTypes[2].Descriptor()
}

func (DedupeCheckResponse_Status) Type() protoreflect.EnumType {
	return &file_api_stockguardian_customer_service_proto_enumTypes[2]
}

func (x DedupeCheckResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DedupeCheckResponse_Status.Descriptor instead.
func (DedupeCheckResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_customer_service_proto_rawDescGZIP(), []int{6, 0}
}

type CreateCustomerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Id of the applicant for whom the customer is being created (mandatory)
	ApplicantId string `protobuf:"bytes,1,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	// Client request id for tracking the request (mandatory)
	ClientRequestId string `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// customer application id for the kyc process of the user
	ApplicationId string `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *CreateCustomerRequest) Reset() {
	*x = CreateCustomerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_customer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerRequest) ProtoMessage() {}

func (x *CreateCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_customer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_customer_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCustomerRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *CreateCustomerRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *CreateCustomerRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type CreateCustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Customer *Customer   `protobuf:"bytes,2,opt,name=customer,proto3" json:"customer,omitempty"`
}

func (x *CreateCustomerResponse) Reset() {
	*x = CreateCustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_customer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerResponse) ProtoMessage() {}

func (x *CreateCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_customer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_customer_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateCustomerResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateCustomerResponse) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

type GetCustomerDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identifier *GetCustomerDetailsRequestIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// Fields requested in the response
	// If this is empty - only customer id and kyc level will be returned
	RequestedFields []enums.CustomerFieldMask `protobuf:"varint,2,rep,packed,name=requested_fields,json=requestedFields,proto3,enum=stockguardian.customer.enums.CustomerFieldMask" json:"requested_fields,omitempty"`
}

func (x *GetCustomerDetailsRequest) Reset() {
	*x = GetCustomerDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_customer_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerDetailsRequest) ProtoMessage() {}

func (x *GetCustomerDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_customer_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_customer_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetCustomerDetailsRequest) GetIdentifier() *GetCustomerDetailsRequestIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *GetCustomerDetailsRequest) GetRequestedFields() []enums.CustomerFieldMask {
	if x != nil {
		return x.RequestedFields
	}
	return nil
}

type GetCustomerDetailsRequestIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetCustomerDetailsRequestIdentifier_CustomerId
	//	*GetCustomerDetailsRequestIdentifier_ApplicantId
	Identifier isGetCustomerDetailsRequestIdentifier_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetCustomerDetailsRequestIdentifier) Reset() {
	*x = GetCustomerDetailsRequestIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_customer_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerDetailsRequestIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerDetailsRequestIdentifier) ProtoMessage() {}

func (x *GetCustomerDetailsRequestIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_customer_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerDetailsRequestIdentifier.ProtoReflect.Descriptor instead.
func (*GetCustomerDetailsRequestIdentifier) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_customer_service_proto_rawDescGZIP(), []int{3}
}

func (m *GetCustomerDetailsRequestIdentifier) GetIdentifier() isGetCustomerDetailsRequestIdentifier_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetCustomerDetailsRequestIdentifier) GetCustomerId() string {
	if x, ok := x.GetIdentifier().(*GetCustomerDetailsRequestIdentifier_CustomerId); ok {
		return x.CustomerId
	}
	return ""
}

func (x *GetCustomerDetailsRequestIdentifier) GetApplicantId() string {
	if x, ok := x.GetIdentifier().(*GetCustomerDetailsRequestIdentifier_ApplicantId); ok {
		return x.ApplicantId
	}
	return ""
}

type isGetCustomerDetailsRequestIdentifier_Identifier interface {
	isGetCustomerDetailsRequestIdentifier_Identifier()
}

type GetCustomerDetailsRequestIdentifier_CustomerId struct {
	CustomerId string `protobuf:"bytes,1,opt,name=customer_id,json=customerId,proto3,oneof"`
}

type GetCustomerDetailsRequestIdentifier_ApplicantId struct {
	ApplicantId string `protobuf:"bytes,2,opt,name=applicant_id,json=applicantId,proto3,oneof"`
}

func (*GetCustomerDetailsRequestIdentifier_CustomerId) isGetCustomerDetailsRequestIdentifier_Identifier() {
}

func (*GetCustomerDetailsRequestIdentifier_ApplicantId) isGetCustomerDetailsRequestIdentifier_Identifier() {
}

type GetCustomerDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Customer *Customer   `protobuf:"bytes,2,opt,name=customer,proto3" json:"customer,omitempty"`
}

func (x *GetCustomerDetailsResponse) Reset() {
	*x = GetCustomerDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_customer_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerDetailsResponse) ProtoMessage() {}

func (x *GetCustomerDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_customer_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_customer_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetCustomerDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCustomerDetailsResponse) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

type DedupeCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pan         string              `protobuf:"bytes,1,opt,name=pan,proto3" json:"pan,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *DedupeCheckRequest) Reset() {
	*x = DedupeCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_customer_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DedupeCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DedupeCheckRequest) ProtoMessage() {}

func (x *DedupeCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_customer_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DedupeCheckRequest.ProtoReflect.Descriptor instead.
func (*DedupeCheckRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_customer_service_proto_rawDescGZIP(), []int{5}
}

func (x *DedupeCheckRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *DedupeCheckRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

type DedupeCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// If the customer is existing to stock guardian, this will be populated
	CustomerId string `protobuf:"bytes,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *DedupeCheckResponse) Reset() {
	*x = DedupeCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_customer_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DedupeCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DedupeCheckResponse) ProtoMessage() {}

func (x *DedupeCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_customer_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DedupeCheckResponse.ProtoReflect.Descriptor instead.
func (*DedupeCheckResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_customer_service_proto_rawDescGZIP(), []int{6}
}

func (x *DedupeCheckResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DedupeCheckResponse) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

var File_api_stockguardian_customer_service_proto protoreflect.FileDescriptor

var file_api_stockguardian_customer_service_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x22, 0xc8, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x22, 0x4b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x4e, 0x10, 0x65, 0x12, 0x20, 0x0a,
	0x1c, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x66, 0x22,
	0xd4, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a,
	0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x0a,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x5a, 0x0a, 0x10, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x7b, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x21, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x22, 0xae, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x08, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49,
	0x53, 0x54, 0x10, 0x65, 0x22, 0x6a, 0x0a, 0x12, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x42, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x22, 0xa8, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4b,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x4e, 0x10, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x45, 0x44,
	0x55, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x4f,
	0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x66, 0x32, 0xe7, 0x02, 0x0a, 0x0f,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x6f, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x12, 0x2d, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2e, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x7b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x31, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a,
	0x0b, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x2a, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f,
	0x74, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_customer_service_proto_rawDescOnce sync.Once
	file_api_stockguardian_customer_service_proto_rawDescData = file_api_stockguardian_customer_service_proto_rawDesc
)

func file_api_stockguardian_customer_service_proto_rawDescGZIP() []byte {
	file_api_stockguardian_customer_service_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_customer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_customer_service_proto_rawDescData)
	})
	return file_api_stockguardian_customer_service_proto_rawDescData
}

var file_api_stockguardian_customer_service_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_stockguardian_customer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_stockguardian_customer_service_proto_goTypes = []interface{}{
	(CreateCustomerResponse_Status)(0),          // 0: stockguardian.customer.CreateCustomerResponse.Status
	(GetCustomerDetailsResponse_Status)(0),      // 1: stockguardian.customer.GetCustomerDetailsResponse.Status
	(DedupeCheckResponse_Status)(0),             // 2: stockguardian.customer.DedupeCheckResponse.Status
	(*CreateCustomerRequest)(nil),               // 3: stockguardian.customer.CreateCustomerRequest
	(*CreateCustomerResponse)(nil),              // 4: stockguardian.customer.CreateCustomerResponse
	(*GetCustomerDetailsRequest)(nil),           // 5: stockguardian.customer.GetCustomerDetailsRequest
	(*GetCustomerDetailsRequestIdentifier)(nil), // 6: stockguardian.customer.GetCustomerDetailsRequestIdentifier
	(*GetCustomerDetailsResponse)(nil),          // 7: stockguardian.customer.GetCustomerDetailsResponse
	(*DedupeCheckRequest)(nil),                  // 8: stockguardian.customer.DedupeCheckRequest
	(*DedupeCheckResponse)(nil),                 // 9: stockguardian.customer.DedupeCheckResponse
	(*rpc.Status)(nil),                          // 10: rpc.Status
	(*Customer)(nil),                            // 11: stockguardian.customer.Customer
	(enums.CustomerFieldMask)(0),                // 12: stockguardian.customer.enums.CustomerFieldMask
	(*common.PhoneNumber)(nil),                  // 13: api.typesv2.common.PhoneNumber
}
var file_api_stockguardian_customer_service_proto_depIdxs = []int32{
	10, // 0: stockguardian.customer.CreateCustomerResponse.status:type_name -> rpc.Status
	11, // 1: stockguardian.customer.CreateCustomerResponse.customer:type_name -> stockguardian.customer.Customer
	6,  // 2: stockguardian.customer.GetCustomerDetailsRequest.identifier:type_name -> stockguardian.customer.GetCustomerDetailsRequestIdentifier
	12, // 3: stockguardian.customer.GetCustomerDetailsRequest.requested_fields:type_name -> stockguardian.customer.enums.CustomerFieldMask
	10, // 4: stockguardian.customer.GetCustomerDetailsResponse.status:type_name -> rpc.Status
	11, // 5: stockguardian.customer.GetCustomerDetailsResponse.customer:type_name -> stockguardian.customer.Customer
	13, // 6: stockguardian.customer.DedupeCheckRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	10, // 7: stockguardian.customer.DedupeCheckResponse.status:type_name -> rpc.Status
	3,  // 8: stockguardian.customer.CustomerService.CreateCustomer:input_type -> stockguardian.customer.CreateCustomerRequest
	5,  // 9: stockguardian.customer.CustomerService.GetCustomerDetails:input_type -> stockguardian.customer.GetCustomerDetailsRequest
	8,  // 10: stockguardian.customer.CustomerService.DedupeCheck:input_type -> stockguardian.customer.DedupeCheckRequest
	4,  // 11: stockguardian.customer.CustomerService.CreateCustomer:output_type -> stockguardian.customer.CreateCustomerResponse
	7,  // 12: stockguardian.customer.CustomerService.GetCustomerDetails:output_type -> stockguardian.customer.GetCustomerDetailsResponse
	9,  // 13: stockguardian.customer.CustomerService.DedupeCheck:output_type -> stockguardian.customer.DedupeCheckResponse
	11, // [11:14] is the sub-list for method output_type
	8,  // [8:11] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_stockguardian_customer_service_proto_init() }
func file_api_stockguardian_customer_service_proto_init() {
	if File_api_stockguardian_customer_service_proto != nil {
		return
	}
	file_api_stockguardian_customer_internal_customer_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_customer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_customer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_customer_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_customer_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerDetailsRequestIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_customer_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_customer_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DedupeCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_customer_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DedupeCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_stockguardian_customer_service_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*GetCustomerDetailsRequestIdentifier_CustomerId)(nil),
		(*GetCustomerDetailsRequestIdentifier_ApplicantId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_customer_service_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_stockguardian_customer_service_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_customer_service_proto_depIdxs,
		EnumInfos:         file_api_stockguardian_customer_service_proto_enumTypes,
		MessageInfos:      file_api_stockguardian_customer_service_proto_msgTypes,
	}.Build()
	File_api_stockguardian_customer_service_proto = out.File
	file_api_stockguardian_customer_service_proto_rawDesc = nil
	file_api_stockguardian_customer_service_proto_goTypes = nil
	file_api_stockguardian_customer_service_proto_depIdxs = nil
}
