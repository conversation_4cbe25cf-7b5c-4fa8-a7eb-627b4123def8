// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/sginhousebre/service.proto

package sginhousebre

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Bureau(0)
)

// Validate checks the field values on GetCibilReportFeaturesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCibilReportFeaturesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCibilReportFeaturesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCibilReportFeaturesRequestMultiError, or nil if none found.
func (m *GetCibilReportFeaturesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCibilReportFeaturesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for ApplicationId

	// no validation rules for LoanAmount

	// no validation rules for HardpullSkipFlag

	if len(errors) > 0 {
		return GetCibilReportFeaturesRequestMultiError(errors)
	}

	return nil
}

// GetCibilReportFeaturesRequestMultiError is an error wrapping multiple
// validation errors returned by GetCibilReportFeaturesRequest.ValidateAll()
// if the designated constraints aren't met.
type GetCibilReportFeaturesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCibilReportFeaturesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCibilReportFeaturesRequestMultiError) AllErrors() []error { return m }

// GetCibilReportFeaturesRequestValidationError is the validation error
// returned by GetCibilReportFeaturesRequest.Validate if the designated
// constraints aren't met.
type GetCibilReportFeaturesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCibilReportFeaturesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCibilReportFeaturesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCibilReportFeaturesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCibilReportFeaturesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCibilReportFeaturesRequestValidationError) ErrorName() string {
	return "GetCibilReportFeaturesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCibilReportFeaturesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCibilReportFeaturesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCibilReportFeaturesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCibilReportFeaturesRequestValidationError{}

// Validate checks the field values on GetCibilReportFeaturesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCibilReportFeaturesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCibilReportFeaturesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCibilReportFeaturesResponseMultiError, or nil if none found.
func (m *GetCibilReportFeaturesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCibilReportFeaturesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetFeatureValueMap()))
		i := 0
		for key := range m.GetFeatureValueMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFeatureValueMap()[key]
			_ = val

			// no validation rules for FeatureValueMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetCibilReportFeaturesResponseValidationError{
							field:  fmt.Sprintf("FeatureValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetCibilReportFeaturesResponseValidationError{
							field:  fmt.Sprintf("FeatureValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetCibilReportFeaturesResponseValidationError{
						field:  fmt.Sprintf("FeatureValueMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for HardpullTime

	if len(errors) > 0 {
		return GetCibilReportFeaturesResponseMultiError(errors)
	}

	return nil
}

// GetCibilReportFeaturesResponseMultiError is an error wrapping multiple
// validation errors returned by GetCibilReportFeaturesResponse.ValidateAll()
// if the designated constraints aren't met.
type GetCibilReportFeaturesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCibilReportFeaturesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCibilReportFeaturesResponseMultiError) AllErrors() []error { return m }

// GetCibilReportFeaturesResponseValidationError is the validation error
// returned by GetCibilReportFeaturesResponse.Validate if the designated
// constraints aren't met.
type GetCibilReportFeaturesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCibilReportFeaturesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCibilReportFeaturesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCibilReportFeaturesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCibilReportFeaturesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCibilReportFeaturesResponseValidationError) ErrorName() string {
	return "GetCibilReportFeaturesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCibilReportFeaturesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCibilReportFeaturesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCibilReportFeaturesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCibilReportFeaturesResponseValidationError{}

// Validate checks the field values on GetPdScoreRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPdScoreRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdScoreRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdScoreRequestMultiError, or nil if none found.
func (m *GetPdScoreRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdScoreRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for Bureau

	if len(errors) > 0 {
		return GetPdScoreRequestMultiError(errors)
	}

	return nil
}

// GetPdScoreRequestMultiError is an error wrapping multiple validation errors
// returned by GetPdScoreRequest.ValidateAll() if the designated constraints
// aren't met.
type GetPdScoreRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdScoreRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdScoreRequestMultiError) AllErrors() []error { return m }

// GetPdScoreRequestValidationError is the validation error returned by
// GetPdScoreRequest.Validate if the designated constraints aren't met.
type GetPdScoreRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdScoreRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdScoreRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdScoreRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdScoreRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdScoreRequestValidationError) ErrorName() string {
	return "GetPdScoreRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdScoreRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdScoreRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdScoreRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdScoreRequestValidationError{}

// Validate checks the field values on GetPdScoreResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPdScoreResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdScoreResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdScoreResponseMultiError, or nil if none found.
func (m *GetPdScoreResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdScoreResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PdScore

	// no validation rules for PdScoreVersion

	if len(errors) > 0 {
		return GetPdScoreResponseMultiError(errors)
	}

	return nil
}

// GetPdScoreResponseMultiError is an error wrapping multiple validation errors
// returned by GetPdScoreResponse.ValidateAll() if the designated constraints
// aren't met.
type GetPdScoreResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdScoreResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdScoreResponseMultiError) AllErrors() []error { return m }

// GetPdScoreResponseValidationError is the validation error returned by
// GetPdScoreResponse.Validate if the designated constraints aren't met.
type GetPdScoreResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdScoreResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdScoreResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdScoreResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdScoreResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdScoreResponseValidationError) ErrorName() string {
	return "GetPdScoreResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdScoreResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdScoreResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdScoreResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdScoreResponseValidationError{}

// Validate checks the field values on GetCreditReportFeaturesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditReportFeaturesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditReportFeaturesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCreditReportFeaturesRequestMultiError, or nil if none found.
func (m *GetCreditReportFeaturesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditReportFeaturesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for ApplicationId

	// no validation rules for LoanAmount

	// no validation rules for HardpullSkipFlag

	// no validation rules for Bureau

	if len(errors) > 0 {
		return GetCreditReportFeaturesRequestMultiError(errors)
	}

	return nil
}

// GetCreditReportFeaturesRequestMultiError is an error wrapping multiple
// validation errors returned by GetCreditReportFeaturesRequest.ValidateAll()
// if the designated constraints aren't met.
type GetCreditReportFeaturesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditReportFeaturesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditReportFeaturesRequestMultiError) AllErrors() []error { return m }

// GetCreditReportFeaturesRequestValidationError is the validation error
// returned by GetCreditReportFeaturesRequest.Validate if the designated
// constraints aren't met.
type GetCreditReportFeaturesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditReportFeaturesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditReportFeaturesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditReportFeaturesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditReportFeaturesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditReportFeaturesRequestValidationError) ErrorName() string {
	return "GetCreditReportFeaturesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditReportFeaturesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditReportFeaturesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditReportFeaturesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditReportFeaturesRequestValidationError{}

// Validate checks the field values on GetCreditReportFeaturesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCreditReportFeaturesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditReportFeaturesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCreditReportFeaturesResponseMultiError, or nil if none found.
func (m *GetCreditReportFeaturesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditReportFeaturesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetFeatureValueMap()))
		i := 0
		for key := range m.GetFeatureValueMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFeatureValueMap()[key]
			_ = val

			// no validation rules for FeatureValueMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetCreditReportFeaturesResponseValidationError{
							field:  fmt.Sprintf("FeatureValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetCreditReportFeaturesResponseValidationError{
							field:  fmt.Sprintf("FeatureValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetCreditReportFeaturesResponseValidationError{
						field:  fmt.Sprintf("FeatureValueMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for HardpullTime

	if len(errors) > 0 {
		return GetCreditReportFeaturesResponseMultiError(errors)
	}

	return nil
}

// GetCreditReportFeaturesResponseMultiError is an error wrapping multiple
// validation errors returned by GetCreditReportFeaturesResponse.ValidateAll()
// if the designated constraints aren't met.
type GetCreditReportFeaturesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditReportFeaturesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditReportFeaturesResponseMultiError) AllErrors() []error { return m }

// GetCreditReportFeaturesResponseValidationError is the validation error
// returned by GetCreditReportFeaturesResponse.Validate if the designated
// constraints aren't met.
type GetCreditReportFeaturesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditReportFeaturesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditReportFeaturesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditReportFeaturesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditReportFeaturesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditReportFeaturesResponseValidationError) ErrorName() string {
	return "GetCreditReportFeaturesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditReportFeaturesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditReportFeaturesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditReportFeaturesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditReportFeaturesResponseValidationError{}
