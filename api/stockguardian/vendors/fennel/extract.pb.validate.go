// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/fennel/extract.proto

package fennel

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetFeatures with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFeatures) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFeatures with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetFeaturesMultiError, or
// nil if none found.
func (m *GetFeatures) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFeatures) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFeaturesValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFeaturesValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFeaturesValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Log

	// no validation rules for Workflow

	// no validation rules for SamplingRate

	if len(errors) > 0 {
		return GetFeaturesMultiError(errors)
	}

	return nil
}

// GetFeaturesMultiError is an error wrapping multiple validation errors
// returned by GetFeatures.ValidateAll() if the designated constraints aren't met.
type GetFeaturesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFeaturesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFeaturesMultiError) AllErrors() []error { return m }

// GetFeaturesValidationError is the validation error returned by
// GetFeatures.Validate if the designated constraints aren't met.
type GetFeaturesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFeaturesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFeaturesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFeaturesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFeaturesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFeaturesValidationError) ErrorName() string { return "GetFeaturesValidationError" }

// Error satisfies the builtin error interface
func (e GetFeaturesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFeatures.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFeaturesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFeaturesValidationError{}

// Validate checks the field values on InputFeatureValue with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InputFeatureValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InputFeatureValue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InputFeatureValueMultiError, or nil if none found.
func (m *InputFeatureValue) ValidateAll() error {
	return m.validate(true)
}

func (m *InputFeatureValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return InputFeatureValueMultiError(errors)
	}

	return nil
}

// InputFeatureValueMultiError is an error wrapping multiple validation errors
// returned by InputFeatureValue.ValidateAll() if the designated constraints
// aren't met.
type InputFeatureValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InputFeatureValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InputFeatureValueMultiError) AllErrors() []error { return m }

// InputFeatureValueValidationError is the validation error returned by
// InputFeatureValue.Validate if the designated constraints aren't met.
type InputFeatureValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InputFeatureValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InputFeatureValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InputFeatureValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InputFeatureValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InputFeatureValueValidationError) ErrorName() string {
	return "InputFeatureValueValidationError"
}

// Error satisfies the builtin error interface
func (e InputFeatureValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInputFeatureValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InputFeatureValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InputFeatureValueValidationError{}
