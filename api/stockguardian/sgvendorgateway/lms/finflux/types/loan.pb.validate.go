// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/sgvendorgateway/lms/finflux/types/loan.proto

package types

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LoanSummary with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanSummary with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoanSummaryMultiError, or
// nil if none found.
func (m *LoanSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPrincipalDisbursed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalDisbursed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalDisbursed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalDisbursed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PrincipalDisbursed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalPaid()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalPaid()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PrincipalPaid",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalWrittenOff()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalWrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalWrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalWrittenOff()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PrincipalWrittenOff",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PrincipalOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalFromIncomePosting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalFromIncomePosting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalFromIncomePosting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalFromIncomePosting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PrincipalFromIncomePosting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalOverdue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalOverdue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalOverdue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalOverdue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PrincipalOverdue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalNetDisbursed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalNetDisbursed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PrincipalNetDisbursed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalNetDisbursed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PrincipalNetDisbursed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestCharged()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestCharged()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "InterestCharged",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestPaid()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestPaid()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "InterestPaid",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestWaived()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestWaived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestWaived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestWaived()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "InterestWaived",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestWrittenOff()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestWrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestWrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestWrittenOff()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "InterestWrittenOff",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "InterestOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestOverdue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestOverdue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "InterestOverdue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestOverdue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "InterestOverdue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeeChargesCharged()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeeChargesCharged()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "FeeChargesCharged",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeeChargesDueAtDisbursementCharged()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesDueAtDisbursementCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesDueAtDisbursementCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeeChargesDueAtDisbursementCharged()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "FeeChargesDueAtDisbursementCharged",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeeChargesPaid()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeeChargesPaid()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "FeeChargesPaid",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeeChargesWaived()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesWaived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesWaived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeeChargesWaived()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "FeeChargesWaived",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeeChargesWrittenOff()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesWrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesWrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeeChargesWrittenOff()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "FeeChargesWrittenOff",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeeChargesOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeeChargesOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "FeeChargesOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeeChargesOverdue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesOverdue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "FeeChargesOverdue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeeChargesOverdue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "FeeChargesOverdue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyChargesCharged()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesCharged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyChargesCharged()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PenaltyChargesCharged",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyChargesPaid()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyChargesPaid()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PenaltyChargesPaid",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyChargesWaived()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesWaived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesWaived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyChargesWaived()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PenaltyChargesWaived",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyChargesWrittenOff()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesWrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesWrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyChargesWrittenOff()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PenaltyChargesWrittenOff",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyChargesOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyChargesOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PenaltyChargesOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyChargesOverdue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesOverdue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "PenaltyChargesOverdue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyChargesOverdue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "PenaltyChargesOverdue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalExpectedRepayment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalExpectedRepayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalExpectedRepayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalExpectedRepayment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "TotalExpectedRepayment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalRepayment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalRepayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalRepayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalRepayment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "TotalRepayment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalExpectedCostOfLoan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalExpectedCostOfLoan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalExpectedCostOfLoan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalExpectedCostOfLoan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "TotalExpectedCostOfLoan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalCostOfLoan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalCostOfLoan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalCostOfLoan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalCostOfLoan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "TotalCostOfLoan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalWaived()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalWaived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalWaived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalWaived()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "TotalWaived",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalWrittenOff()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalWrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalWrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalWrittenOff()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "TotalWrittenOff",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "TotalOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalOverdue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalOverdue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalOverdue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalOverdue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "TotalOverdue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExcessAmountPaid()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "ExcessAmountPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "ExcessAmountPaid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExcessAmountPaid()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "ExcessAmountPaid",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "AvailableBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpfrontInterestAvailable()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "UpfrontInterestAvailable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "UpfrontInterestAvailable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpfrontInterestAvailable()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "UpfrontInterestAvailable",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRebateApplied()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "RebateApplied",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "RebateApplied",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRebateApplied()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "RebateApplied",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalAdvanceEmiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalAdvanceEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanSummaryValidationError{
					field:  "TotalAdvanceEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalAdvanceEmiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanSummaryValidationError{
				field:  "TotalAdvanceEmiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanSummaryMultiError(errors)
	}

	return nil
}

// LoanSummaryMultiError is an error wrapping multiple validation errors
// returned by LoanSummary.ValidateAll() if the designated constraints aren't met.
type LoanSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanSummaryMultiError) AllErrors() []error { return m }

// LoanSummaryValidationError is the validation error returned by
// LoanSummary.Validate if the designated constraints aren't met.
type LoanSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanSummaryValidationError) ErrorName() string { return "LoanSummaryValidationError" }

// Error satisfies the builtin error interface
func (e LoanSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanSummaryValidationError{}

// Validate checks the field values on LoanTimeline with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanTimeline) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanTimeline with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoanTimelineMultiError, or
// nil if none found.
func (m *LoanTimeline) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanTimeline) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSubmittedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanTimelineValidationError{
					field:  "SubmittedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanTimelineValidationError{
					field:  "SubmittedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubmittedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanTimelineValidationError{
				field:  "SubmittedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetApprovedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanTimelineValidationError{
					field:  "ApprovedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanTimelineValidationError{
					field:  "ApprovedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApprovedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanTimelineValidationError{
				field:  "ApprovedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpectedDisbursementDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanTimelineValidationError{
					field:  "ExpectedDisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanTimelineValidationError{
					field:  "ExpectedDisbursementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedDisbursementDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanTimelineValidationError{
				field:  "ExpectedDisbursementDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisbursedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanTimelineValidationError{
					field:  "DisbursedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanTimelineValidationError{
					field:  "DisbursedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisbursedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanTimelineValidationError{
				field:  "DisbursedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpectedMaturityDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanTimelineValidationError{
					field:  "ExpectedMaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanTimelineValidationError{
					field:  "ExpectedMaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedMaturityDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanTimelineValidationError{
				field:  "ExpectedMaturityDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanTimelineMultiError(errors)
	}

	return nil
}

// LoanTimelineMultiError is an error wrapping multiple validation errors
// returned by LoanTimeline.ValidateAll() if the designated constraints aren't met.
type LoanTimelineMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanTimelineMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanTimelineMultiError) AllErrors() []error { return m }

// LoanTimelineValidationError is the validation error returned by
// LoanTimeline.Validate if the designated constraints aren't met.
type LoanTimelineValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanTimelineValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanTimelineValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanTimelineValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanTimelineValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanTimelineValidationError) ErrorName() string { return "LoanTimelineValidationError" }

// Error satisfies the builtin error interface
func (e LoanTimelineValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanTimeline.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanTimelineValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanTimelineValidationError{}

// Validate checks the field values on LoanDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoanDetailsMultiError, or
// nil if none found.
func (m *LoanDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for AccountNumber

	// no validation rules for Status

	// no validation rules for ClientId

	if all {
		switch v := interface{}(m.GetPrincipalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "PrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "PrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "PrincipalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetApprovedPrincipalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "ApprovedPrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "ApprovedPrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApprovedPrincipalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "ApprovedPrincipalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProposedPrincipalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "ProposedPrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "ProposedPrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProposedPrincipalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "ProposedPrincipalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmountPaidInAdvance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "AmountPaidInAdvance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "AmountPaidInAdvance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountPaidInAdvance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "AmountPaidInAdvance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrokenPeriodInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "BrokenPeriodInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "BrokenPeriodInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrokenPeriodInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "BrokenPeriodInterest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TermFrequency

	// no validation rules for TermFrequencyType

	// no validation rules for NumberOfRepayments

	// no validation rules for RepaymentEvery

	// no validation rules for RepaymentFrequencyType

	// no validation rules for InterestRatePerPeriod

	// no validation rules for InterestRateFrequencyType

	// no validation rules for AnnualInterestRate

	// no validation rules for NumberOfPaidRepayments

	// no validation rules for NumberOfDueRepayments

	// no validation rules for InterestType

	// no validation rules for MinInterestRatePerPeriod

	// no validation rules for MaxInterestRatePerPeriod

	// no validation rules for CurrentInterestRate

	// no validation rules for IsCancellationAllowed

	if all {
		switch v := interface{}(m.GetTimeline()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "Timeline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "Timeline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeline()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "Timeline",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeeChargedAtDisbursement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "FeeChargedAtDisbursement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "FeeChargedAtDisbursement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeeChargedAtDisbursement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "FeeChargedAtDisbursement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InArrears

	// no validation rules for IsNpa

	if all {
		switch v := interface{}(m.GetCalculatedEmiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "CalculatedEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "CalculatedEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCalculatedEmiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "CalculatedEmiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsFldg

	// no validation rules for ActualNumberOfRepayments

	if all {
		switch v := interface{}(m.GetTotalRepaymentExpected()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "TotalRepaymentExpected",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "TotalRepaymentExpected",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalRepaymentExpected()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "TotalRepaymentExpected",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanDetailsMultiError(errors)
	}

	return nil
}

// LoanDetailsMultiError is an error wrapping multiple validation errors
// returned by LoanDetails.ValidateAll() if the designated constraints aren't met.
type LoanDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanDetailsMultiError) AllErrors() []error { return m }

// LoanDetailsValidationError is the validation error returned by
// LoanDetails.Validate if the designated constraints aren't met.
type LoanDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanDetailsValidationError) ErrorName() string { return "LoanDetailsValidationError" }

// Error satisfies the builtin error interface
func (e LoanDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanDetailsValidationError{}

// Validate checks the field values on LoanRepaymentPeriod with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanRepaymentPeriod) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanRepaymentPeriod with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanRepaymentPeriodMultiError, or nil if none found.
func (m *LoanRepaymentPeriod) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanRepaymentPeriod) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SerialNumber

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "DueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DaysInPeriod

	if all {
		switch v := interface{}(m.GetPrincipalComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "PrincipalComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "PrincipalComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "PrincipalComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "InterestComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "InterestComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "InterestComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeeChargesComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "FeeChargesComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "FeeChargesComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeeChargesComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "FeeChargesComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyChargesComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "PenaltyChargesComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "PenaltyChargesComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyChargesComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "PenaltyChargesComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "TotalComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "TotalComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "TotalComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalBalanceOutstandingForLoan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "PrincipalBalanceOutstandingForLoan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "PrincipalBalanceOutstandingForLoan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalBalanceOutstandingForLoan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "PrincipalBalanceOutstandingForLoan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalActualCostOfLoanForPeriod()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "TotalActualCostOfLoanForPeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "TotalActualCostOfLoanForPeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalActualCostOfLoanForPeriod()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "TotalActualCostOfLoanForPeriod",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalInstallmentAmountForPeriod()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "TotalInstallmentAmountForPeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "TotalInstallmentAmountForPeriod",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalInstallmentAmountForPeriod()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "TotalInstallmentAmountForPeriod",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestAdjustedDueToGrace()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "InterestAdjustedDueToGrace",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "InterestAdjustedDueToGrace",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestAdjustedDueToGrace()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "InterestAdjustedDueToGrace",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestAccruable()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "InterestAccruable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "InterestAccruable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestAccruable()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "InterestAccruable",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsComplete

	if all {
		switch v := interface{}(m.GetObligationsMetOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "ObligationsMetOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRepaymentPeriodValidationError{
					field:  "ObligationsMetOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetObligationsMetOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRepaymentPeriodValidationError{
				field:  "ObligationsMetOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanRepaymentPeriodMultiError(errors)
	}

	return nil
}

// LoanRepaymentPeriodMultiError is an error wrapping multiple validation
// errors returned by LoanRepaymentPeriod.ValidateAll() if the designated
// constraints aren't met.
type LoanRepaymentPeriodMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanRepaymentPeriodMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanRepaymentPeriodMultiError) AllErrors() []error { return m }

// LoanRepaymentPeriodValidationError is the validation error returned by
// LoanRepaymentPeriod.Validate if the designated constraints aren't met.
type LoanRepaymentPeriodValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanRepaymentPeriodValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanRepaymentPeriodValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanRepaymentPeriodValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanRepaymentPeriodValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanRepaymentPeriodValidationError) ErrorName() string {
	return "LoanRepaymentPeriodValidationError"
}

// Error satisfies the builtin error interface
func (e LoanRepaymentPeriodValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanRepaymentPeriod.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanRepaymentPeriodValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanRepaymentPeriodValidationError{}

// Validate checks the field values on LoanComponentBreakdown with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanComponentBreakdown) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanComponentBreakdown with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanComponentBreakdownMultiError, or nil if none found.
func (m *LoanComponentBreakdown) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanComponentBreakdown) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOriginalDue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "OriginalDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "OriginalDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOriginalDue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanComponentBreakdownValidationError{
				field:  "OriginalDue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "Due",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "Due",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanComponentBreakdownValidationError{
				field:  "Due",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaid()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "Paid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "Paid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaid()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanComponentBreakdownValidationError{
				field:  "Paid",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWaived()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "Waived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "Waived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWaived()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanComponentBreakdownValidationError{
				field:  "Waived",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWrittenOff()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "WrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "WrittenOff",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWrittenOff()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanComponentBreakdownValidationError{
				field:  "WrittenOff",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "Outstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanComponentBreakdownValidationError{
					field:  "Outstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanComponentBreakdownValidationError{
				field:  "Outstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanComponentBreakdownMultiError(errors)
	}

	return nil
}

// LoanComponentBreakdownMultiError is an error wrapping multiple validation
// errors returned by LoanComponentBreakdown.ValidateAll() if the designated
// constraints aren't met.
type LoanComponentBreakdownMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanComponentBreakdownMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanComponentBreakdownMultiError) AllErrors() []error { return m }

// LoanComponentBreakdownValidationError is the validation error returned by
// LoanComponentBreakdown.Validate if the designated constraints aren't met.
type LoanComponentBreakdownValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanComponentBreakdownValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanComponentBreakdownValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanComponentBreakdownValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanComponentBreakdownValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanComponentBreakdownValidationError) ErrorName() string {
	return "LoanComponentBreakdownValidationError"
}

// Error satisfies the builtin error interface
func (e LoanComponentBreakdownValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanComponentBreakdown.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanComponentBreakdownValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanComponentBreakdownValidationError{}
