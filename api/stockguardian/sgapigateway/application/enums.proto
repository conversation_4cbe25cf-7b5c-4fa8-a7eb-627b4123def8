syntax = "proto3";

package stockguardian.sgapigateway.application;

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapigateway/application";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgapigateway.application";

enum ConsentType {
  CONSENT_TYPE_UNSPECIFIED = 0;
  CONSENT_TYPE_CKYC_DOWNLOAD = 1;
  CONSENT_TYPE_CREDIT_REPORT_HARD_PULL = 2;
  CONSENT_TYPE_DATA_USAGE_FOR_PROMOTIONS = 3;
}

enum LoanApplicationStageName {
  LOAN_APPLICATION_STAGE_NAME_UNSPECIFIED = 0;
  LOAN_APPLICATION_STAGE_NAME_KYC = 1;
  LOAN_APPLICATION_STAGE_NAME_OFFER_GENERATION = 2;
  LOAN_APPLICATION_STAGE_NAME_DRAWDOWN = 3;
  LOAN_APPLICATION_STAGE_NAME_MANDATE = 4;
  LOAN_APPLICATION_STAGE_NAME_ESIGN = 5;
  LOAN_APPLICATION_STAGE_NAME_DISBURSEMENT = 6;
  LOAN_APPLICATION_STAGE_NAME_PENNY_DROP = 7;
}

enum LoanApplicationStageStatus {
  LOAN_APPLICATION_STAGE_STATUS_UNSPECIFIED = 0;
  LOAN_APPLICATION_STAGE_STATUS_CREATED = 1;
  LOAN_APPLICATION_STAGE_STATUS_IN_PROGRESS = 2;
  LOAN_APPLICATION_STAGE_STATUS_SUCCESS = 3;
  LOAN_APPLICATION_STAGE_STATUS_FAILED = 4;
  LOAN_APPLICATION_STAGE_STATUS_MANUAL_INTERVENTION = 5;
  LOAN_APPLICATION_STAGE_STATUS_EXPIRED = 6;
  LOAN_APPLICATION_STAGE_STATUS_CANCELLED = 7;
}

enum UpdateUserDetailsFieldMask {
  UPDATE_USER_DETAILS_FIELD_MASK_UNSPECIFIED = 0;
  UPDATE_USER_DETAILS_FIELD_MASK_ADDRESS = 1;
  UPDATE_USER_DETAILS_FIELD_MASK_EMPLOYMENT_DETAILS = 2;
  UPDATE_USER_DETAILS_FIELD_MASK_BANK_ACCOUNT_DETAILS = 3;
  UPDATE_USER_DETAILS_FIELD_MASK_PERSONAL_DETAILS = 4;
}
