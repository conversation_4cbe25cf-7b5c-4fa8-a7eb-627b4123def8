// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/fittt/service.proto

package fittt

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	action "github.com/epifi/gamma/api/fittt/action"

	sports "github.com/epifi/gamma/api/fittt/sports"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = action.ActionStatus(0)

	_ = sports.SportsType(0)
)

// Validate checks the field values on HandleSharkTankEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleSharkTankEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleSharkTankEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleSharkTankEventRequestMultiError, or nil if none found.
func (m *HandleSharkTankEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleSharkTankEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IndividualJudgeInvestments

	// no validation rules for AllJudgeInvestments

	// no validation rules for EpisodeNo

	// no validation rules for RetrySuffix

	if all {
		switch v := interface{}(m.GetEpisodeDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandleSharkTankEventRequestValidationError{
					field:  "EpisodeDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandleSharkTankEventRequestValidationError{
					field:  "EpisodeDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEpisodeDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandleSharkTankEventRequestValidationError{
				field:  "EpisodeDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HandleSharkTankEventRequestMultiError(errors)
	}

	return nil
}

// HandleSharkTankEventRequestMultiError is an error wrapping multiple
// validation errors returned by HandleSharkTankEventRequest.ValidateAll() if
// the designated constraints aren't met.
type HandleSharkTankEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleSharkTankEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleSharkTankEventRequestMultiError) AllErrors() []error { return m }

// HandleSharkTankEventRequestValidationError is the validation error returned
// by HandleSharkTankEventRequest.Validate if the designated constraints
// aren't met.
type HandleSharkTankEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleSharkTankEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleSharkTankEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleSharkTankEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleSharkTankEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleSharkTankEventRequestValidationError) ErrorName() string {
	return "HandleSharkTankEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HandleSharkTankEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleSharkTankEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleSharkTankEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleSharkTankEventRequestValidationError{}

// Validate checks the field values on HandleSharkTankEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleSharkTankEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleSharkTankEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleSharkTankEventResponseMultiError, or nil if none found.
func (m *HandleSharkTankEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleSharkTankEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandleSharkTankEventResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandleSharkTankEventResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandleSharkTankEventResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HandleSharkTankEventResponseMultiError(errors)
	}

	return nil
}

// HandleSharkTankEventResponseMultiError is an error wrapping multiple
// validation errors returned by HandleSharkTankEventResponse.ValidateAll() if
// the designated constraints aren't met.
type HandleSharkTankEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleSharkTankEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleSharkTankEventResponseMultiError) AllErrors() []error { return m }

// HandleSharkTankEventResponseValidationError is the validation error returned
// by HandleSharkTankEventResponse.Validate if the designated constraints
// aren't met.
type HandleSharkTankEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleSharkTankEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleSharkTankEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleSharkTankEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleSharkTankEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleSharkTankEventResponseValidationError) ErrorName() string {
	return "HandleSharkTankEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e HandleSharkTankEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleSharkTankEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleSharkTankEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleSharkTankEventResponseValidationError{}

// Validate checks the field values on GetRuleDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRuleDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRuleDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRuleDetailsRequestMultiError, or nil if none found.
func (m *GetRuleDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRuleDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActionExecId

	if len(errors) > 0 {
		return GetRuleDetailsRequestMultiError(errors)
	}

	return nil
}

// GetRuleDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetRuleDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRuleDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRuleDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRuleDetailsRequestMultiError) AllErrors() []error { return m }

// GetRuleDetailsRequestValidationError is the validation error returned by
// GetRuleDetailsRequest.Validate if the designated constraints aren't met.
type GetRuleDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRuleDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRuleDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRuleDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRuleDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRuleDetailsRequestValidationError) ErrorName() string {
	return "GetRuleDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRuleDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRuleDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRuleDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRuleDetailsRequestValidationError{}

// Validate checks the field values on GetRuleDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRuleDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRuleDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRuleDetailsResponseMultiError, or nil if none found.
func (m *GetRuleDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRuleDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRuleDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRuleDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRuleDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRuleDetailsResponseValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRuleDetailsResponseValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRuleDetailsResponseValidationError{
				field:  "Rule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubscription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRuleDetailsResponseValidationError{
					field:  "Subscription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRuleDetailsResponseValidationError{
					field:  "Subscription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubscription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRuleDetailsResponseValidationError{
				field:  "Subscription",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRuleDetailsResponseMultiError(errors)
	}

	return nil
}

// GetRuleDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetRuleDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRuleDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRuleDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRuleDetailsResponseMultiError) AllErrors() []error { return m }

// GetRuleDetailsResponseValidationError is the validation error returned by
// GetRuleDetailsResponse.Validate if the designated constraints aren't met.
type GetRuleDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRuleDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRuleDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRuleDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRuleDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRuleDetailsResponseValidationError) ErrorName() string {
	return "GetRuleDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRuleDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRuleDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRuleDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRuleDetailsResponseValidationError{}

// Validate checks the field values on GetAggregatedAmountSummaryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAggregatedAmountSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAggregatedAmountSummaryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAggregatedAmountSummaryRequestMultiError, or nil if none found.
func (m *GetAggregatedAmountSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAggregatedAmountSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if utf8.RuneCountInString(m.GetSubscriptionId()) < 1 {
		err := GetAggregatedAmountSummaryRequestValidationError{
			field:  "SubscriptionId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAggregatedAmountSummaryRequestMultiError(errors)
	}

	return nil
}

// GetAggregatedAmountSummaryRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAggregatedAmountSummaryRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAggregatedAmountSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAggregatedAmountSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAggregatedAmountSummaryRequestMultiError) AllErrors() []error { return m }

// GetAggregatedAmountSummaryRequestValidationError is the validation error
// returned by GetAggregatedAmountSummaryRequest.Validate if the designated
// constraints aren't met.
type GetAggregatedAmountSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAggregatedAmountSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAggregatedAmountSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAggregatedAmountSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAggregatedAmountSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAggregatedAmountSummaryRequestValidationError) ErrorName() string {
	return "GetAggregatedAmountSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAggregatedAmountSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAggregatedAmountSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAggregatedAmountSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAggregatedAmountSummaryRequestValidationError{}

// Validate checks the field values on GetAggregatedAmountSummaryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAggregatedAmountSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAggregatedAmountSummaryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAggregatedAmountSummaryResponseMultiError, or nil if none found.
func (m *GetAggregatedAmountSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAggregatedAmountSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAggregatedAmountSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAggregatedAmountSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAggregatedAmountSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAggregatedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAggregatedAmountSummaryResponseValidationError{
					field:  "AggregatedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAggregatedAmountSummaryResponseValidationError{
					field:  "AggregatedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAggregatedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAggregatedAmountSummaryResponseValidationError{
				field:  "AggregatedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAggregatedAmountSummaryResponseMultiError(errors)
	}

	return nil
}

// GetAggregatedAmountSummaryResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAggregatedAmountSummaryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAggregatedAmountSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAggregatedAmountSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAggregatedAmountSummaryResponseMultiError) AllErrors() []error { return m }

// GetAggregatedAmountSummaryResponseValidationError is the validation error
// returned by GetAggregatedAmountSummaryResponse.Validate if the designated
// constraints aren't met.
type GetAggregatedAmountSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAggregatedAmountSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAggregatedAmountSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAggregatedAmountSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAggregatedAmountSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAggregatedAmountSummaryResponseValidationError) ErrorName() string {
	return "GetAggregatedAmountSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAggregatedAmountSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAggregatedAmountSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAggregatedAmountSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAggregatedAmountSummaryResponseValidationError{}

// Validate checks the field values on
// PublishSportsChallengeWeeklyRewardsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublishSportsChallengeWeeklyRewardsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PublishSportsChallengeWeeklyRewardsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// PublishSportsChallengeWeeklyRewardsRequestMultiError, or nil if none found.
func (m *PublishSportsChallengeWeeklyRewardsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishSportsChallengeWeeklyRewardsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TournamentId

	// no validation rules for WeekNo

	// no validation rules for NoOfUsersQualifiedForWeeklyRewards

	if len(errors) > 0 {
		return PublishSportsChallengeWeeklyRewardsRequestMultiError(errors)
	}

	return nil
}

// PublishSportsChallengeWeeklyRewardsRequestMultiError is an error wrapping
// multiple validation errors returned by
// PublishSportsChallengeWeeklyRewardsRequest.ValidateAll() if the designated
// constraints aren't met.
type PublishSportsChallengeWeeklyRewardsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishSportsChallengeWeeklyRewardsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishSportsChallengeWeeklyRewardsRequestMultiError) AllErrors() []error { return m }

// PublishSportsChallengeWeeklyRewardsRequestValidationError is the validation
// error returned by PublishSportsChallengeWeeklyRewardsRequest.Validate if
// the designated constraints aren't met.
type PublishSportsChallengeWeeklyRewardsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishSportsChallengeWeeklyRewardsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishSportsChallengeWeeklyRewardsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishSportsChallengeWeeklyRewardsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishSportsChallengeWeeklyRewardsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishSportsChallengeWeeklyRewardsRequestValidationError) ErrorName() string {
	return "PublishSportsChallengeWeeklyRewardsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PublishSportsChallengeWeeklyRewardsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishSportsChallengeWeeklyRewardsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishSportsChallengeWeeklyRewardsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishSportsChallengeWeeklyRewardsRequestValidationError{}

// Validate checks the field values on
// PublishSportsChallengeWeeklyRewardsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublishSportsChallengeWeeklyRewardsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PublishSportsChallengeWeeklyRewardsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// PublishSportsChallengeWeeklyRewardsResponseMultiError, or nil if none found.
func (m *PublishSportsChallengeWeeklyRewardsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishSportsChallengeWeeklyRewardsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublishSportsChallengeWeeklyRewardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublishSportsChallengeWeeklyRewardsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublishSportsChallengeWeeklyRewardsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PublishSportsChallengeWeeklyRewardsResponseMultiError(errors)
	}

	return nil
}

// PublishSportsChallengeWeeklyRewardsResponseMultiError is an error wrapping
// multiple validation errors returned by
// PublishSportsChallengeWeeklyRewardsResponse.ValidateAll() if the designated
// constraints aren't met.
type PublishSportsChallengeWeeklyRewardsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishSportsChallengeWeeklyRewardsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishSportsChallengeWeeklyRewardsResponseMultiError) AllErrors() []error { return m }

// PublishSportsChallengeWeeklyRewardsResponseValidationError is the validation
// error returned by PublishSportsChallengeWeeklyRewardsResponse.Validate if
// the designated constraints aren't met.
type PublishSportsChallengeWeeklyRewardsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishSportsChallengeWeeklyRewardsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishSportsChallengeWeeklyRewardsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishSportsChallengeWeeklyRewardsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishSportsChallengeWeeklyRewardsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishSportsChallengeWeeklyRewardsResponseValidationError) ErrorName() string {
	return "PublishSportsChallengeWeeklyRewardsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PublishSportsChallengeWeeklyRewardsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishSportsChallengeWeeklyRewardsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishSportsChallengeWeeklyRewardsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishSportsChallengeWeeklyRewardsResponseValidationError{}

// Validate checks the field values on UpdateSportsChallengeStatsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateSportsChallengeStatsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSportsChallengeStatsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateSportsChallengeStatsRequestMultiError, or nil if none found.
func (m *UpdateSportsChallengeStatsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSportsChallengeStatsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TournamentTagId

	if all {
		switch v := interface{}(m.GetFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSportsChallengeStatsRequestValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSportsChallengeStatsRequestValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSportsChallengeStatsRequestValidationError{
				field:  "From",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSportsChallengeStatsRequestValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSportsChallengeStatsRequestValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSportsChallengeStatsRequestValidationError{
				field:  "To",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SportsType

	if len(errors) > 0 {
		return UpdateSportsChallengeStatsRequestMultiError(errors)
	}

	return nil
}

// UpdateSportsChallengeStatsRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateSportsChallengeStatsRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateSportsChallengeStatsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSportsChallengeStatsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSportsChallengeStatsRequestMultiError) AllErrors() []error { return m }

// UpdateSportsChallengeStatsRequestValidationError is the validation error
// returned by UpdateSportsChallengeStatsRequest.Validate if the designated
// constraints aren't met.
type UpdateSportsChallengeStatsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSportsChallengeStatsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSportsChallengeStatsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSportsChallengeStatsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSportsChallengeStatsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSportsChallengeStatsRequestValidationError) ErrorName() string {
	return "UpdateSportsChallengeStatsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSportsChallengeStatsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSportsChallengeStatsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSportsChallengeStatsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSportsChallengeStatsRequestValidationError{}

// Validate checks the field values on UpdateSportsChallengeStatsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateSportsChallengeStatsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSportsChallengeStatsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateSportsChallengeStatsResponseMultiError, or nil if none found.
func (m *UpdateSportsChallengeStatsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSportsChallengeStatsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSportsChallengeStatsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSportsChallengeStatsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSportsChallengeStatsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSportsChallengeStatsResponseMultiError(errors)
	}

	return nil
}

// UpdateSportsChallengeStatsResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateSportsChallengeStatsResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateSportsChallengeStatsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSportsChallengeStatsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSportsChallengeStatsResponseMultiError) AllErrors() []error { return m }

// UpdateSportsChallengeStatsResponseValidationError is the validation error
// returned by UpdateSportsChallengeStatsResponse.Validate if the designated
// constraints aren't met.
type UpdateSportsChallengeStatsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSportsChallengeStatsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSportsChallengeStatsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSportsChallengeStatsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSportsChallengeStatsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSportsChallengeStatsResponseValidationError) ErrorName() string {
	return "UpdateSportsChallengeStatsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSportsChallengeStatsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSportsChallengeStatsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSportsChallengeStatsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSportsChallengeStatsResponseValidationError{}

// Validate checks the field values on HandleConditionEvaluationResultRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *HandleConditionEvaluationResultRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// HandleConditionEvaluationResultRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// HandleConditionEvaluationResultRequestMultiError, or nil if none found.
func (m *HandleConditionEvaluationResultRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleConditionEvaluationResultRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConditionEvaluationResult

	// no validation rules for ActorId

	{
		sorted_keys := make([]string, len(m.GetCondEvalResult()))
		i := 0
		for key := range m.GetCondEvalResult() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCondEvalResult()[key]
			_ = val

			// no validation rules for CondEvalResult[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, HandleConditionEvaluationResultRequestValidationError{
							field:  fmt.Sprintf("CondEvalResult[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, HandleConditionEvaluationResultRequestValidationError{
							field:  fmt.Sprintf("CondEvalResult[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return HandleConditionEvaluationResultRequestValidationError{
						field:  fmt.Sprintf("CondEvalResult[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return HandleConditionEvaluationResultRequestMultiError(errors)
	}

	return nil
}

// HandleConditionEvaluationResultRequestMultiError is an error wrapping
// multiple validation errors returned by
// HandleConditionEvaluationResultRequest.ValidateAll() if the designated
// constraints aren't met.
type HandleConditionEvaluationResultRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleConditionEvaluationResultRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleConditionEvaluationResultRequestMultiError) AllErrors() []error { return m }

// HandleConditionEvaluationResultRequestValidationError is the validation
// error returned by HandleConditionEvaluationResultRequest.Validate if the
// designated constraints aren't met.
type HandleConditionEvaluationResultRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleConditionEvaluationResultRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleConditionEvaluationResultRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleConditionEvaluationResultRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleConditionEvaluationResultRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleConditionEvaluationResultRequestValidationError) ErrorName() string {
	return "HandleConditionEvaluationResultRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HandleConditionEvaluationResultRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleConditionEvaluationResultRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleConditionEvaluationResultRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleConditionEvaluationResultRequestValidationError{}

// Validate checks the field values on ConditionEvalResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConditionEvalResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConditionEvalResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConditionEvalResultMultiError, or nil if none found.
func (m *ConditionEvalResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ConditionEvalResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Metadata

	if len(errors) > 0 {
		return ConditionEvalResultMultiError(errors)
	}

	return nil
}

// ConditionEvalResultMultiError is an error wrapping multiple validation
// errors returned by ConditionEvalResult.ValidateAll() if the designated
// constraints aren't met.
type ConditionEvalResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionEvalResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionEvalResultMultiError) AllErrors() []error { return m }

// ConditionEvalResultValidationError is the validation error returned by
// ConditionEvalResult.Validate if the designated constraints aren't met.
type ConditionEvalResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionEvalResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionEvalResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConditionEvalResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionEvalResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionEvalResultValidationError) ErrorName() string {
	return "ConditionEvalResultValidationError"
}

// Error satisfies the builtin error interface
func (e ConditionEvalResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConditionEvalResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionEvalResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionEvalResultValidationError{}

// Validate checks the field values on HandleConditionEvaluationResultResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *HandleConditionEvaluationResultResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// HandleConditionEvaluationResultResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// HandleConditionEvaluationResultResponseMultiError, or nil if none found.
func (m *HandleConditionEvaluationResultResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleConditionEvaluationResultResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandleConditionEvaluationResultResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandleConditionEvaluationResultResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandleConditionEvaluationResultResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HandleConditionEvaluationResultResponseMultiError(errors)
	}

	return nil
}

// HandleConditionEvaluationResultResponseMultiError is an error wrapping
// multiple validation errors returned by
// HandleConditionEvaluationResultResponse.ValidateAll() if the designated
// constraints aren't met.
type HandleConditionEvaluationResultResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleConditionEvaluationResultResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleConditionEvaluationResultResponseMultiError) AllErrors() []error { return m }

// HandleConditionEvaluationResultResponseValidationError is the validation
// error returned by HandleConditionEvaluationResultResponse.Validate if the
// designated constraints aren't met.
type HandleConditionEvaluationResultResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleConditionEvaluationResultResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleConditionEvaluationResultResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleConditionEvaluationResultResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleConditionEvaluationResultResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleConditionEvaluationResultResponseValidationError) ErrorName() string {
	return "HandleConditionEvaluationResultResponseValidationError"
}

// Error satisfies the builtin error interface
func (e HandleConditionEvaluationResultResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleConditionEvaluationResultResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleConditionEvaluationResultResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleConditionEvaluationResultResponseValidationError{}

// Validate checks the field values on GetSubscriptionExecutionInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSubscriptionExecutionInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSubscriptionExecutionInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSubscriptionExecutionInfoRequestMultiError, or nil if none found.
func (m *GetSubscriptionExecutionInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSubscriptionExecutionInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetSubscriptionId()) < 1 {
		err := GetSubscriptionExecutionInfoRequestValidationError{
			field:  "SubscriptionId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionExecutionInfoRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContextV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoRequestValidationError{
					field:  "PageContextV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoRequestValidationError{
					field:  "PageContextV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionExecutionInfoRequestValidationError{
				field:  "PageContextV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEventFieldMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoRequestValidationError{
					field:  "EventFieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoRequestValidationError{
					field:  "EventFieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventFieldMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionExecutionInfoRequestValidationError{
				field:  "EventFieldMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetSubscriptionExecutionInfoRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetSubscriptionExecutionInfoRequestMultiError(errors)
	}

	return nil
}

// GetSubscriptionExecutionInfoRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetSubscriptionExecutionInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSubscriptionExecutionInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSubscriptionExecutionInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSubscriptionExecutionInfoRequestMultiError) AllErrors() []error { return m }

// GetSubscriptionExecutionInfoRequestValidationError is the validation error
// returned by GetSubscriptionExecutionInfoRequest.Validate if the designated
// constraints aren't met.
type GetSubscriptionExecutionInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSubscriptionExecutionInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSubscriptionExecutionInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSubscriptionExecutionInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSubscriptionExecutionInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSubscriptionExecutionInfoRequestValidationError) ErrorName() string {
	return "GetSubscriptionExecutionInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSubscriptionExecutionInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSubscriptionExecutionInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSubscriptionExecutionInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSubscriptionExecutionInfoRequestValidationError{}

// Validate checks the field values on GetSubscriptionExecutionInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetSubscriptionExecutionInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSubscriptionExecutionInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSubscriptionExecutionInfoResponseMultiError, or nil if none found.
func (m *GetSubscriptionExecutionInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSubscriptionExecutionInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionExecutionInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionExecutionInfoResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRuleExecution() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSubscriptionExecutionInfoResponseValidationError{
						field:  fmt.Sprintf("RuleExecution[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSubscriptionExecutionInfoResponseValidationError{
						field:  fmt.Sprintf("RuleExecution[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSubscriptionExecutionInfoResponseValidationError{
					field:  fmt.Sprintf("RuleExecution[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContextV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoResponseValidationError{
					field:  "PageContextV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionExecutionInfoResponseValidationError{
					field:  "PageContextV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionExecutionInfoResponseValidationError{
				field:  "PageContextV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSubscriptionExecutionInfoResponseMultiError(errors)
	}

	return nil
}

// GetSubscriptionExecutionInfoResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetSubscriptionExecutionInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSubscriptionExecutionInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSubscriptionExecutionInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSubscriptionExecutionInfoResponseMultiError) AllErrors() []error { return m }

// GetSubscriptionExecutionInfoResponseValidationError is the validation error
// returned by GetSubscriptionExecutionInfoResponse.Validate if the designated
// constraints aren't met.
type GetSubscriptionExecutionInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSubscriptionExecutionInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSubscriptionExecutionInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSubscriptionExecutionInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSubscriptionExecutionInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSubscriptionExecutionInfoResponseValidationError) ErrorName() string {
	return "GetSubscriptionExecutionInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSubscriptionExecutionInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSubscriptionExecutionInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSubscriptionExecutionInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSubscriptionExecutionInfoResponseValidationError{}

// Validate checks the field values on RuleExecution with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RuleExecution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RuleExecution with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RuleExecutionMultiError, or
// nil if none found.
func (m *RuleExecution) ValidateAll() error {
	return m.validate(true)
}

func (m *RuleExecution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HistoryType

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleExecutionValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleExecutionValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleExecutionValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleExecutionValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleExecutionValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleExecutionValidationError{
				field:  "Timestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetActionData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleExecutionValidationError{
					field:  "ActionData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleExecutionValidationError{
					field:  "ActionData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleExecutionValidationError{
				field:  "ActionData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEvent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleExecutionValidationError{
					field:  "Event",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleExecutionValidationError{
					field:  "Event",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEvent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleExecutionValidationError{
				field:  "Event",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	if len(errors) > 0 {
		return RuleExecutionMultiError(errors)
	}

	return nil
}

// RuleExecutionMultiError is an error wrapping multiple validation errors
// returned by RuleExecution.ValidateAll() if the designated constraints
// aren't met.
type RuleExecutionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RuleExecutionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RuleExecutionMultiError) AllErrors() []error { return m }

// RuleExecutionValidationError is the validation error returned by
// RuleExecution.Validate if the designated constraints aren't met.
type RuleExecutionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RuleExecutionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RuleExecutionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RuleExecutionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RuleExecutionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RuleExecutionValidationError) ErrorName() string { return "RuleExecutionValidationError" }

// Error satisfies the builtin error interface
func (e RuleExecutionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRuleExecution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RuleExecutionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RuleExecutionValidationError{}

// Validate checks the field values on GetSubscriptionAggregationInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetSubscriptionAggregationInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSubscriptionAggregationInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSubscriptionAggregationInfoRequestMultiError, or nil if none found.
func (m *GetSubscriptionAggregationInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSubscriptionAggregationInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionId

	if _, ok := _GetSubscriptionAggregationInfoRequest_ActionType_InLookup[m.GetActionType()]; !ok {
		err := GetSubscriptionAggregationInfoRequestValidationError{
			field:  "ActionType",
			reason: "value must be in list [DEPOSIT PURCHASE_MUTUAL_FUND PAYMENT EXECUTE_US_STOCKS_SIP_ACTION]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetFieldMasks()) < 1 {
		err := GetSubscriptionAggregationInfoRequestValidationError{
			field:  "FieldMasks",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionAggregationInfoRequestValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionAggregationInfoRequestValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionAggregationInfoRequestValidationError{
				field:  "From",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionAggregationInfoRequestValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionAggregationInfoRequestValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionAggregationInfoRequestValidationError{
				field:  "To",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSubscriptionAggregationInfoRequestMultiError(errors)
	}

	return nil
}

// GetSubscriptionAggregationInfoRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetSubscriptionAggregationInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSubscriptionAggregationInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSubscriptionAggregationInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSubscriptionAggregationInfoRequestMultiError) AllErrors() []error { return m }

// GetSubscriptionAggregationInfoRequestValidationError is the validation error
// returned by GetSubscriptionAggregationInfoRequest.Validate if the
// designated constraints aren't met.
type GetSubscriptionAggregationInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSubscriptionAggregationInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSubscriptionAggregationInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSubscriptionAggregationInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSubscriptionAggregationInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSubscriptionAggregationInfoRequestValidationError) ErrorName() string {
	return "GetSubscriptionAggregationInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSubscriptionAggregationInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSubscriptionAggregationInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSubscriptionAggregationInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSubscriptionAggregationInfoRequestValidationError{}

var _GetSubscriptionAggregationInfoRequest_ActionType_InLookup = map[action.ActionType]struct{}{
	1:  {},
	7:  {},
	9:  {},
	11: {},
}

// Validate checks the field values on GetSubscriptionAggregationInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetSubscriptionAggregationInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetSubscriptionAggregationInfoResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetSubscriptionAggregationInfoResponseMultiError, or nil if none found.
func (m *GetSubscriptionAggregationInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSubscriptionAggregationInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionAggregationInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionAggregationInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionAggregationInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RuleCategory

	if all {
		switch v := interface{}(m.GetTotalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionAggregationInfoResponseValidationError{
					field:  "TotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionAggregationInfoResponseValidationError{
					field:  "TotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionAggregationInfoResponseValidationError{
				field:  "TotalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExecutionsCount

	if all {
		switch v := interface{}(m.GetLastExecutionTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubscriptionAggregationInfoResponseValidationError{
					field:  "LastExecutionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubscriptionAggregationInfoResponseValidationError{
					field:  "LastExecutionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastExecutionTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubscriptionAggregationInfoResponseValidationError{
				field:  "LastExecutionTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.AggregatedValue.(type) {
	case *GetSubscriptionAggregationInfoResponse_Amount:
		if v == nil {
			err := GetSubscriptionAggregationInfoResponseValidationError{
				field:  "AggregatedValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAmount()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSubscriptionAggregationInfoResponseValidationError{
						field:  "Amount",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSubscriptionAggregationInfoResponseValidationError{
						field:  "Amount",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSubscriptionAggregationInfoResponseValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetSubscriptionAggregationInfoResponse_Count:
		if v == nil {
			err := GetSubscriptionAggregationInfoResponseValidationError{
				field:  "AggregatedValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Count
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetSubscriptionAggregationInfoResponseMultiError(errors)
	}

	return nil
}

// GetSubscriptionAggregationInfoResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetSubscriptionAggregationInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSubscriptionAggregationInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSubscriptionAggregationInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSubscriptionAggregationInfoResponseMultiError) AllErrors() []error { return m }

// GetSubscriptionAggregationInfoResponseValidationError is the validation
// error returned by GetSubscriptionAggregationInfoResponse.Validate if the
// designated constraints aren't met.
type GetSubscriptionAggregationInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSubscriptionAggregationInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSubscriptionAggregationInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSubscriptionAggregationInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSubscriptionAggregationInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSubscriptionAggregationInfoResponseValidationError) ErrorName() string {
	return "GetSubscriptionAggregationInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSubscriptionAggregationInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSubscriptionAggregationInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSubscriptionAggregationInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSubscriptionAggregationInfoResponseValidationError{}

// Validate checks the field values on GetFitttDetailsOfOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFitttDetailsOfOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFitttDetailsOfOrderRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFitttDetailsOfOrderRequestMultiError, or nil if none found.
func (m *GetFitttDetailsOfOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFitttDetailsOfOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return GetFitttDetailsOfOrderRequestMultiError(errors)
	}

	return nil
}

// GetFitttDetailsOfOrderRequestMultiError is an error wrapping multiple
// validation errors returned by GetFitttDetailsOfOrderRequest.ValidateAll()
// if the designated constraints aren't met.
type GetFitttDetailsOfOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFitttDetailsOfOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFitttDetailsOfOrderRequestMultiError) AllErrors() []error { return m }

// GetFitttDetailsOfOrderRequestValidationError is the validation error
// returned by GetFitttDetailsOfOrderRequest.Validate if the designated
// constraints aren't met.
type GetFitttDetailsOfOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFitttDetailsOfOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFitttDetailsOfOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFitttDetailsOfOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFitttDetailsOfOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFitttDetailsOfOrderRequestValidationError) ErrorName() string {
	return "GetFitttDetailsOfOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFitttDetailsOfOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFitttDetailsOfOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFitttDetailsOfOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFitttDetailsOfOrderRequestValidationError{}

// Validate checks the field values on GetFitttDetailsOfOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFitttDetailsOfOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFitttDetailsOfOrderResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFitttDetailsOfOrderResponseMultiError, or nil if none found.
func (m *GetFitttDetailsOfOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFitttDetailsOfOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFitttDetailsOfOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFitttDetailsOfOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFitttDetailsOfOrderResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RuleId

	// no validation rules for RuleCategory

	if all {
		switch v := interface{}(m.GetExecutionTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFitttDetailsOfOrderResponseValidationError{
					field:  "ExecutionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFitttDetailsOfOrderResponseValidationError{
					field:  "ExecutionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecutionTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFitttDetailsOfOrderResponseValidationError{
				field:  "ExecutionTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RuleEvaluationCount

	// no validation rules for SubscriptionId

	if len(errors) > 0 {
		return GetFitttDetailsOfOrderResponseMultiError(errors)
	}

	return nil
}

// GetFitttDetailsOfOrderResponseMultiError is an error wrapping multiple
// validation errors returned by GetFitttDetailsOfOrderResponse.ValidateAll()
// if the designated constraints aren't met.
type GetFitttDetailsOfOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFitttDetailsOfOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFitttDetailsOfOrderResponseMultiError) AllErrors() []error { return m }

// GetFitttDetailsOfOrderResponseValidationError is the validation error
// returned by GetFitttDetailsOfOrderResponse.Validate if the designated
// constraints aren't met.
type GetFitttDetailsOfOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFitttDetailsOfOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFitttDetailsOfOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFitttDetailsOfOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFitttDetailsOfOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFitttDetailsOfOrderResponseValidationError) ErrorName() string {
	return "GetFitttDetailsOfOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFitttDetailsOfOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFitttDetailsOfOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFitttDetailsOfOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFitttDetailsOfOrderResponseValidationError{}

// Validate checks the field values on GetStatsForAppRatingsNudgeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetStatsForAppRatingsNudgeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetStatsForAppRatingsNudgeRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetStatsForAppRatingsNudgeRequestMultiError, or nil if none found.
func (m *GetStatsForAppRatingsNudgeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetStatsForAppRatingsNudgeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetStatsForAppRatingsNudgeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ValidateZenModeOrFootballSubs

	if len(errors) > 0 {
		return GetStatsForAppRatingsNudgeRequestMultiError(errors)
	}

	return nil
}

// GetStatsForAppRatingsNudgeRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetStatsForAppRatingsNudgeRequest.ValidateAll() if the designated
// constraints aren't met.
type GetStatsForAppRatingsNudgeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetStatsForAppRatingsNudgeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetStatsForAppRatingsNudgeRequestMultiError) AllErrors() []error { return m }

// GetStatsForAppRatingsNudgeRequestValidationError is the validation error
// returned by GetStatsForAppRatingsNudgeRequest.Validate if the designated
// constraints aren't met.
type GetStatsForAppRatingsNudgeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetStatsForAppRatingsNudgeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetStatsForAppRatingsNudgeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetStatsForAppRatingsNudgeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetStatsForAppRatingsNudgeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetStatsForAppRatingsNudgeRequestValidationError) ErrorName() string {
	return "GetStatsForAppRatingsNudgeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetStatsForAppRatingsNudgeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetStatsForAppRatingsNudgeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetStatsForAppRatingsNudgeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetStatsForAppRatingsNudgeRequestValidationError{}

// Validate checks the field values on GetStatsForAppRatingsNudgeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetStatsForAppRatingsNudgeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetStatsForAppRatingsNudgeResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetStatsForAppRatingsNudgeResponseMultiError, or nil if none found.
func (m *GetStatsForAppRatingsNudgeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetStatsForAppRatingsNudgeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetStatsForAppRatingsNudgeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetStatsForAppRatingsNudgeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetStatsForAppRatingsNudgeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActiveSubscriptionsCount

	if all {
		switch v := interface{}(m.GetTotalSaved()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetStatsForAppRatingsNudgeResponseValidationError{
					field:  "TotalSaved",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetStatsForAppRatingsNudgeResponseValidationError{
					field:  "TotalSaved",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalSaved()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetStatsForAppRatingsNudgeResponseValidationError{
				field:  "TotalSaved",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HasZenModeOrFootballSubs

	if len(errors) > 0 {
		return GetStatsForAppRatingsNudgeResponseMultiError(errors)
	}

	return nil
}

// GetStatsForAppRatingsNudgeResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetStatsForAppRatingsNudgeResponse.ValidateAll() if the designated
// constraints aren't met.
type GetStatsForAppRatingsNudgeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetStatsForAppRatingsNudgeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetStatsForAppRatingsNudgeResponseMultiError) AllErrors() []error { return m }

// GetStatsForAppRatingsNudgeResponseValidationError is the validation error
// returned by GetStatsForAppRatingsNudgeResponse.Validate if the designated
// constraints aren't met.
type GetStatsForAppRatingsNudgeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetStatsForAppRatingsNudgeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetStatsForAppRatingsNudgeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetStatsForAppRatingsNudgeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetStatsForAppRatingsNudgeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetStatsForAppRatingsNudgeResponseValidationError) ErrorName() string {
	return "GetStatsForAppRatingsNudgeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetStatsForAppRatingsNudgeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetStatsForAppRatingsNudgeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetStatsForAppRatingsNudgeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetStatsForAppRatingsNudgeResponseValidationError{}

// Validate checks the field values on GetSportsTournamentPointsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSportsTournamentPointsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSportsTournamentPointsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSportsTournamentPointsRequestMultiError, or nil if none found.
func (m *GetSportsTournamentPointsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSportsTournamentPointsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for TournamentTag

	if all {
		switch v := interface{}(m.GetEventStartFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSportsTournamentPointsRequestValidationError{
					field:  "EventStartFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSportsTournamentPointsRequestValidationError{
					field:  "EventStartFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventStartFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSportsTournamentPointsRequestValidationError{
				field:  "EventStartFrom",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEventStartTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSportsTournamentPointsRequestValidationError{
					field:  "EventStartTo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSportsTournamentPointsRequestValidationError{
					field:  "EventStartTo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventStartTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSportsTournamentPointsRequestValidationError{
				field:  "EventStartTo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSportsTournamentPointsRequestMultiError(errors)
	}

	return nil
}

// GetSportsTournamentPointsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetSportsTournamentPointsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSportsTournamentPointsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSportsTournamentPointsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSportsTournamentPointsRequestMultiError) AllErrors() []error { return m }

// GetSportsTournamentPointsRequestValidationError is the validation error
// returned by GetSportsTournamentPointsRequest.Validate if the designated
// constraints aren't met.
type GetSportsTournamentPointsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSportsTournamentPointsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSportsTournamentPointsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSportsTournamentPointsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSportsTournamentPointsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSportsTournamentPointsRequestValidationError) ErrorName() string {
	return "GetSportsTournamentPointsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSportsTournamentPointsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSportsTournamentPointsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSportsTournamentPointsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSportsTournamentPointsRequestValidationError{}

// Validate checks the field values on GetSportsTournamentPointsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSportsTournamentPointsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSportsTournamentPointsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSportsTournamentPointsResponseMultiError, or nil if none found.
func (m *GetSportsTournamentPointsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSportsTournamentPointsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSportsTournamentPointsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSportsTournamentPointsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSportsTournamentPointsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TotalPoints

	if len(errors) > 0 {
		return GetSportsTournamentPointsResponseMultiError(errors)
	}

	return nil
}

// GetSportsTournamentPointsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetSportsTournamentPointsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSportsTournamentPointsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSportsTournamentPointsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSportsTournamentPointsResponseMultiError) AllErrors() []error { return m }

// GetSportsTournamentPointsResponseValidationError is the validation error
// returned by GetSportsTournamentPointsResponse.Validate if the designated
// constraints aren't met.
type GetSportsTournamentPointsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSportsTournamentPointsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSportsTournamentPointsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSportsTournamentPointsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSportsTournamentPointsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSportsTournamentPointsResponseValidationError) ErrorName() string {
	return "GetSportsTournamentPointsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSportsTournamentPointsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSportsTournamentPointsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSportsTournamentPointsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSportsTournamentPointsResponseValidationError{}

// Validate checks the field values on GetSportsMatchPointsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSportsMatchPointsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSportsMatchPointsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSportsMatchPointsRequestMultiError, or nil if none found.
func (m *GetSportsMatchPointsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSportsMatchPointsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for VendorMatchId

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetSportsMatchPointsRequestMultiError(errors)
	}

	return nil
}

// GetSportsMatchPointsRequestMultiError is an error wrapping multiple
// validation errors returned by GetSportsMatchPointsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetSportsMatchPointsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSportsMatchPointsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSportsMatchPointsRequestMultiError) AllErrors() []error { return m }

// GetSportsMatchPointsRequestValidationError is the validation error returned
// by GetSportsMatchPointsRequest.Validate if the designated constraints
// aren't met.
type GetSportsMatchPointsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSportsMatchPointsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSportsMatchPointsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSportsMatchPointsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSportsMatchPointsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSportsMatchPointsRequestValidationError) ErrorName() string {
	return "GetSportsMatchPointsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSportsMatchPointsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSportsMatchPointsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSportsMatchPointsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSportsMatchPointsRequestValidationError{}

// Validate checks the field values on GetSportsMatchPointsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSportsMatchPointsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSportsMatchPointsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSportsMatchPointsResponseMultiError, or nil if none found.
func (m *GetSportsMatchPointsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSportsMatchPointsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSportsMatchPointsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSportsMatchPointsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSportsMatchPointsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TotalPoints

	// no validation rules for EventFrequency

	// no validation rules for ExecutionStatus

	if len(errors) > 0 {
		return GetSportsMatchPointsResponseMultiError(errors)
	}

	return nil
}

// GetSportsMatchPointsResponseMultiError is an error wrapping multiple
// validation errors returned by GetSportsMatchPointsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetSportsMatchPointsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSportsMatchPointsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSportsMatchPointsResponseMultiError) AllErrors() []error { return m }

// GetSportsMatchPointsResponseValidationError is the validation error returned
// by GetSportsMatchPointsResponse.Validate if the designated constraints
// aren't met.
type GetSportsMatchPointsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSportsMatchPointsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSportsMatchPointsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSportsMatchPointsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSportsMatchPointsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSportsMatchPointsResponseValidationError) ErrorName() string {
	return "GetSportsMatchPointsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSportsMatchPointsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSportsMatchPointsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSportsMatchPointsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSportsMatchPointsResponseValidationError{}

// Validate checks the field values on GetCricketRuleExecutionCountRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCricketRuleExecutionCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCricketRuleExecutionCountRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCricketRuleExecutionCountRequestMultiError, or nil if none found.
func (m *GetCricketRuleExecutionCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCricketRuleExecutionCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetEventStartFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCricketRuleExecutionCountRequestValidationError{
					field:  "EventStartFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCricketRuleExecutionCountRequestValidationError{
					field:  "EventStartFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventStartFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCricketRuleExecutionCountRequestValidationError{
				field:  "EventStartFrom",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEventStartTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCricketRuleExecutionCountRequestValidationError{
					field:  "EventStartTo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCricketRuleExecutionCountRequestValidationError{
					field:  "EventStartTo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventStartTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCricketRuleExecutionCountRequestValidationError{
				field:  "EventStartTo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCricketRuleExecutionCountRequestMultiError(errors)
	}

	return nil
}

// GetCricketRuleExecutionCountRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCricketRuleExecutionCountRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCricketRuleExecutionCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCricketRuleExecutionCountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCricketRuleExecutionCountRequestMultiError) AllErrors() []error { return m }

// GetCricketRuleExecutionCountRequestValidationError is the validation error
// returned by GetCricketRuleExecutionCountRequest.Validate if the designated
// constraints aren't met.
type GetCricketRuleExecutionCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCricketRuleExecutionCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCricketRuleExecutionCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCricketRuleExecutionCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCricketRuleExecutionCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCricketRuleExecutionCountRequestValidationError) ErrorName() string {
	return "GetCricketRuleExecutionCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCricketRuleExecutionCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCricketRuleExecutionCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCricketRuleExecutionCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCricketRuleExecutionCountRequestValidationError{}

// Validate checks the field values on GetCricketRuleExecutionCountResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCricketRuleExecutionCountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCricketRuleExecutionCountResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCricketRuleExecutionCountResponseMultiError, or nil if none found.
func (m *GetCricketRuleExecutionCountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCricketRuleExecutionCountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCricketRuleExecutionCountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCricketRuleExecutionCountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCricketRuleExecutionCountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Count

	if len(errors) > 0 {
		return GetCricketRuleExecutionCountResponseMultiError(errors)
	}

	return nil
}

// GetCricketRuleExecutionCountResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCricketRuleExecutionCountResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCricketRuleExecutionCountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCricketRuleExecutionCountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCricketRuleExecutionCountResponseMultiError) AllErrors() []error { return m }

// GetCricketRuleExecutionCountResponseValidationError is the validation error
// returned by GetCricketRuleExecutionCountResponse.Validate if the designated
// constraints aren't met.
type GetCricketRuleExecutionCountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCricketRuleExecutionCountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCricketRuleExecutionCountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCricketRuleExecutionCountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCricketRuleExecutionCountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCricketRuleExecutionCountResponseValidationError) ErrorName() string {
	return "GetCricketRuleExecutionCountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCricketRuleExecutionCountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCricketRuleExecutionCountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCricketRuleExecutionCountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCricketRuleExecutionCountResponseValidationError{}

// Validate checks the field values on GetCricketMatchResultEventsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCricketMatchResultEventsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCricketMatchResultEventsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCricketMatchResultEventsRequestMultiError, or nil if none found.
func (m *GetCricketMatchResultEventsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCricketMatchResultEventsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEventStartFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCricketMatchResultEventsRequestValidationError{
					field:  "EventStartFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCricketMatchResultEventsRequestValidationError{
					field:  "EventStartFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventStartFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCricketMatchResultEventsRequestValidationError{
				field:  "EventStartFrom",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEventStartTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCricketMatchResultEventsRequestValidationError{
					field:  "EventStartTo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCricketMatchResultEventsRequestValidationError{
					field:  "EventStartTo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventStartTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCricketMatchResultEventsRequestValidationError{
				field:  "EventStartTo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCricketMatchResultEventsRequestMultiError(errors)
	}

	return nil
}

// GetCricketMatchResultEventsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCricketMatchResultEventsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCricketMatchResultEventsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCricketMatchResultEventsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCricketMatchResultEventsRequestMultiError) AllErrors() []error { return m }

// GetCricketMatchResultEventsRequestValidationError is the validation error
// returned by GetCricketMatchResultEventsRequest.Validate if the designated
// constraints aren't met.
type GetCricketMatchResultEventsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCricketMatchResultEventsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCricketMatchResultEventsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCricketMatchResultEventsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCricketMatchResultEventsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCricketMatchResultEventsRequestValidationError) ErrorName() string {
	return "GetCricketMatchResultEventsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCricketMatchResultEventsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCricketMatchResultEventsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCricketMatchResultEventsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCricketMatchResultEventsRequestValidationError{}

// Validate checks the field values on GetCricketMatchResultEventsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCricketMatchResultEventsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCricketMatchResultEventsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCricketMatchResultEventsResponseMultiError, or nil if none found.
func (m *GetCricketMatchResultEventsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCricketMatchResultEventsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCricketMatchResultEventsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCricketMatchResultEventsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCricketMatchResultEventsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetEvents()))
		i := 0
		for key := range m.GetEvents() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetEvents()[key]
			_ = val

			// no validation rules for Events[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetCricketMatchResultEventsResponseValidationError{
							field:  fmt.Sprintf("Events[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetCricketMatchResultEventsResponseValidationError{
							field:  fmt.Sprintf("Events[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetCricketMatchResultEventsResponseValidationError{
						field:  fmt.Sprintf("Events[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetCricketMatchResultEventsResponseMultiError(errors)
	}

	return nil
}

// GetCricketMatchResultEventsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCricketMatchResultEventsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCricketMatchResultEventsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCricketMatchResultEventsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCricketMatchResultEventsResponseMultiError) AllErrors() []error { return m }

// GetCricketMatchResultEventsResponseValidationError is the validation error
// returned by GetCricketMatchResultEventsResponse.Validate if the designated
// constraints aren't met.
type GetCricketMatchResultEventsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCricketMatchResultEventsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCricketMatchResultEventsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCricketMatchResultEventsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCricketMatchResultEventsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCricketMatchResultEventsResponseValidationError) ErrorName() string {
	return "GetCricketMatchResultEventsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCricketMatchResultEventsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCricketMatchResultEventsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCricketMatchResultEventsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCricketMatchResultEventsResponseValidationError{}

// Validate checks the field values on GetTournamentLeaderboardRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTournamentLeaderboardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTournamentLeaderboardRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTournamentLeaderboardRequestMultiError, or nil if none found.
func (m *GetTournamentLeaderboardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTournamentLeaderboardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for TournamentTagId

	if m.GetRankLimit() < 1 {
		err := GetTournamentLeaderboardRequestValidationError{
			field:  "RankLimit",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for WeekNo

	if len(errors) > 0 {
		return GetTournamentLeaderboardRequestMultiError(errors)
	}

	return nil
}

// GetTournamentLeaderboardRequestMultiError is an error wrapping multiple
// validation errors returned by GetTournamentLeaderboardRequest.ValidateAll()
// if the designated constraints aren't met.
type GetTournamentLeaderboardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTournamentLeaderboardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTournamentLeaderboardRequestMultiError) AllErrors() []error { return m }

// GetTournamentLeaderboardRequestValidationError is the validation error
// returned by GetTournamentLeaderboardRequest.Validate if the designated
// constraints aren't met.
type GetTournamentLeaderboardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTournamentLeaderboardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTournamentLeaderboardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTournamentLeaderboardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTournamentLeaderboardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTournamentLeaderboardRequestValidationError) ErrorName() string {
	return "GetTournamentLeaderboardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTournamentLeaderboardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTournamentLeaderboardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTournamentLeaderboardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTournamentLeaderboardRequestValidationError{}

// Validate checks the field values on GetTournamentLeaderboardResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTournamentLeaderboardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTournamentLeaderboardResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTournamentLeaderboardResponseMultiError, or nil if none found.
func (m *GetTournamentLeaderboardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTournamentLeaderboardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTournamentLeaderboardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTournamentLeaderboardResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTournamentLeaderboardResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetContestantsByRank() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTournamentLeaderboardResponseValidationError{
						field:  fmt.Sprintf("ContestantsByRank[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTournamentLeaderboardResponseValidationError{
						field:  fmt.Sprintf("ContestantsByRank[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTournamentLeaderboardResponseValidationError{
					field:  fmt.Sprintf("ContestantsByRank[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetUserStats()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTournamentLeaderboardResponseValidationError{
					field:  "UserStats",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTournamentLeaderboardResponseValidationError{
					field:  "UserStats",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserStats()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTournamentLeaderboardResponseValidationError{
				field:  "UserStats",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTournamentLeaderboardResponseMultiError(errors)
	}

	return nil
}

// GetTournamentLeaderboardResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTournamentLeaderboardResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTournamentLeaderboardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTournamentLeaderboardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTournamentLeaderboardResponseMultiError) AllErrors() []error { return m }

// GetTournamentLeaderboardResponseValidationError is the validation error
// returned by GetTournamentLeaderboardResponse.Validate if the designated
// constraints aren't met.
type GetTournamentLeaderboardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTournamentLeaderboardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTournamentLeaderboardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTournamentLeaderboardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTournamentLeaderboardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTournamentLeaderboardResponseValidationError) ErrorName() string {
	return "GetTournamentLeaderboardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTournamentLeaderboardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTournamentLeaderboardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTournamentLeaderboardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTournamentLeaderboardResponseValidationError{}

// Validate checks the field values on ContestantStats with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ContestantStats) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContestantStats with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ContestantStatsMultiError, or nil if none found.
func (m *ContestantStats) ValidateAll() error {
	return m.validate(true)
}

func (m *ContestantStats) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Rank

	// no validation rules for Points

	if len(errors) > 0 {
		return ContestantStatsMultiError(errors)
	}

	return nil
}

// ContestantStatsMultiError is an error wrapping multiple validation errors
// returned by ContestantStats.ValidateAll() if the designated constraints
// aren't met.
type ContestantStatsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContestantStatsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContestantStatsMultiError) AllErrors() []error { return m }

// ContestantStatsValidationError is the validation error returned by
// ContestantStats.Validate if the designated constraints aren't met.
type ContestantStatsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContestantStatsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContestantStatsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContestantStatsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContestantStatsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContestantStatsValidationError) ErrorName() string { return "ContestantStatsValidationError" }

// Error satisfies the builtin error interface
func (e ContestantStatsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContestantStats.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContestantStatsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContestantStatsValidationError{}

// Validate checks the field values on
// GetSportsTournamentRewardsClientIdsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetSportsTournamentRewardsClientIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetSportsTournamentRewardsClientIdsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetSportsTournamentRewardsClientIdsRequestMultiError, or nil if none found.
func (m *GetSportsTournamentRewardsClientIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSportsTournamentRewardsClientIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TournamentTagId

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSportsTournamentRewardsClientIdsRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSportsTournamentRewardsClientIdsRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSportsTournamentRewardsClientIdsRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSportsTournamentRewardsClientIdsRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSportsTournamentRewardsClientIdsRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSportsTournamentRewardsClientIdsRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InReverseChronologicalOrder

	if len(errors) > 0 {
		return GetSportsTournamentRewardsClientIdsRequestMultiError(errors)
	}

	return nil
}

// GetSportsTournamentRewardsClientIdsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetSportsTournamentRewardsClientIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSportsTournamentRewardsClientIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSportsTournamentRewardsClientIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSportsTournamentRewardsClientIdsRequestMultiError) AllErrors() []error { return m }

// GetSportsTournamentRewardsClientIdsRequestValidationError is the validation
// error returned by GetSportsTournamentRewardsClientIdsRequest.Validate if
// the designated constraints aren't met.
type GetSportsTournamentRewardsClientIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSportsTournamentRewardsClientIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSportsTournamentRewardsClientIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSportsTournamentRewardsClientIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSportsTournamentRewardsClientIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSportsTournamentRewardsClientIdsRequestValidationError) ErrorName() string {
	return "GetSportsTournamentRewardsClientIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSportsTournamentRewardsClientIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSportsTournamentRewardsClientIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSportsTournamentRewardsClientIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSportsTournamentRewardsClientIdsRequestValidationError{}

// Validate checks the field values on
// GetSportsTournamentRewardsClientIdsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetSportsTournamentRewardsClientIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetSportsTournamentRewardsClientIdsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetSportsTournamentRewardsClientIdsResponseMultiError, or nil if none found.
func (m *GetSportsTournamentRewardsClientIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSportsTournamentRewardsClientIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSportsTournamentRewardsClientIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSportsTournamentRewardsClientIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSportsTournamentRewardsClientIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetWeekNoRewardClientIdsMap() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSportsTournamentRewardsClientIdsResponseValidationError{
						field:  fmt.Sprintf("WeekNoRewardClientIdsMap[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSportsTournamentRewardsClientIdsResponseValidationError{
						field:  fmt.Sprintf("WeekNoRewardClientIdsMap[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSportsTournamentRewardsClientIdsResponseValidationError{
					field:  fmt.Sprintf("WeekNoRewardClientIdsMap[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSportsTournamentRewardsClientIdsResponseMultiError(errors)
	}

	return nil
}

// GetSportsTournamentRewardsClientIdsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetSportsTournamentRewardsClientIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSportsTournamentRewardsClientIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSportsTournamentRewardsClientIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSportsTournamentRewardsClientIdsResponseMultiError) AllErrors() []error { return m }

// GetSportsTournamentRewardsClientIdsResponseValidationError is the validation
// error returned by GetSportsTournamentRewardsClientIdsResponse.Validate if
// the designated constraints aren't met.
type GetSportsTournamentRewardsClientIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSportsTournamentRewardsClientIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSportsTournamentRewardsClientIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSportsTournamentRewardsClientIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSportsTournamentRewardsClientIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSportsTournamentRewardsClientIdsResponseValidationError) ErrorName() string {
	return "GetSportsTournamentRewardsClientIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSportsTournamentRewardsClientIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSportsTournamentRewardsClientIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSportsTournamentRewardsClientIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSportsTournamentRewardsClientIdsResponseValidationError{}

// Validate checks the field values on GetActionExecutionInfosRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActionExecutionInfosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActionExecutionInfosRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetActionExecutionInfosRequestMultiError, or nil if none found.
func (m *GetActionExecutionInfosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActionExecutionInfosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActionExecutionInfosRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActionExecutionInfosRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActionExecutionInfosRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActionFieldMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActionExecutionInfosRequestValidationError{
					field:  "ActionFieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActionExecutionInfosRequestValidationError{
					field:  "ActionFieldMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionFieldMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActionExecutionInfosRequestValidationError{
				field:  "ActionFieldMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Id.(type) {
	case *GetActionExecutionInfosRequest_Ids:
		if v == nil {
			err := GetActionExecutionInfosRequestValidationError{
				field:  "Id",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetActionExecutionInfosRequestValidationError{
						field:  "Ids",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetActionExecutionInfosRequestValidationError{
						field:  "Ids",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetActionExecutionInfosRequestValidationError{
					field:  "Ids",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetActionExecutionInfosRequest_ActorIds:
		if v == nil {
			err := GetActionExecutionInfosRequestValidationError{
				field:  "Id",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActorIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetActionExecutionInfosRequestValidationError{
						field:  "ActorIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetActionExecutionInfosRequestValidationError{
						field:  "ActorIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActorIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetActionExecutionInfosRequestValidationError{
					field:  "ActorIds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetActionExecutionInfosRequest_SubscriptionIds:
		if v == nil {
			err := GetActionExecutionInfosRequestValidationError{
				field:  "Id",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSubscriptionIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetActionExecutionInfosRequestValidationError{
						field:  "SubscriptionIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetActionExecutionInfosRequestValidationError{
						field:  "SubscriptionIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSubscriptionIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetActionExecutionInfosRequestValidationError{
					field:  "SubscriptionIds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetActionExecutionInfosRequest_ExecutionIds:
		if v == nil {
			err := GetActionExecutionInfosRequestValidationError{
				field:  "Id",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExecutionIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetActionExecutionInfosRequestValidationError{
						field:  "ExecutionIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetActionExecutionInfosRequestValidationError{
						field:  "ExecutionIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExecutionIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetActionExecutionInfosRequestValidationError{
					field:  "ExecutionIds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetActionExecutionInfosRequestMultiError(errors)
	}

	return nil
}

// GetActionExecutionInfosRequestMultiError is an error wrapping multiple
// validation errors returned by GetActionExecutionInfosRequest.ValidateAll()
// if the designated constraints aren't met.
type GetActionExecutionInfosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActionExecutionInfosRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActionExecutionInfosRequestMultiError) AllErrors() []error { return m }

// GetActionExecutionInfosRequestValidationError is the validation error
// returned by GetActionExecutionInfosRequest.Validate if the designated
// constraints aren't met.
type GetActionExecutionInfosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActionExecutionInfosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActionExecutionInfosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActionExecutionInfosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActionExecutionInfosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActionExecutionInfosRequestValidationError) ErrorName() string {
	return "GetActionExecutionInfosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActionExecutionInfosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActionExecutionInfosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActionExecutionInfosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActionExecutionInfosRequestValidationError{}

// Validate checks the field values on GetActionExecutionInfosResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActionExecutionInfosResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActionExecutionInfosResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetActionExecutionInfosResponseMultiError, or nil if none found.
func (m *GetActionExecutionInfosResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActionExecutionInfosResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActionExecutionInfosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActionExecutionInfosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActionExecutionInfosResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetActionExecutions()))
		i := 0
		for key := range m.GetActionExecutions() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetActionExecutions()[key]
			_ = val

			// no validation rules for ActionExecutions[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetActionExecutionInfosResponseValidationError{
							field:  fmt.Sprintf("ActionExecutions[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetActionExecutionInfosResponseValidationError{
							field:  fmt.Sprintf("ActionExecutions[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetActionExecutionInfosResponseValidationError{
						field:  fmt.Sprintf("ActionExecutions[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetPageContextResp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActionExecutionInfosResponseValidationError{
					field:  "PageContextResp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActionExecutionInfosResponseValidationError{
					field:  "PageContextResp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActionExecutionInfosResponseValidationError{
				field:  "PageContextResp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetActionExecutionInfosResponseMultiError(errors)
	}

	return nil
}

// GetActionExecutionInfosResponseMultiError is an error wrapping multiple
// validation errors returned by GetActionExecutionInfosResponse.ValidateAll()
// if the designated constraints aren't met.
type GetActionExecutionInfosResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActionExecutionInfosResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActionExecutionInfosResponseMultiError) AllErrors() []error { return m }

// GetActionExecutionInfosResponseValidationError is the validation error
// returned by GetActionExecutionInfosResponse.Validate if the designated
// constraints aren't met.
type GetActionExecutionInfosResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActionExecutionInfosResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActionExecutionInfosResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActionExecutionInfosResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActionExecutionInfosResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActionExecutionInfosResponseValidationError) ErrorName() string {
	return "GetActionExecutionInfosResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActionExecutionInfosResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActionExecutionInfosResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActionExecutionInfosResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActionExecutionInfosResponseValidationError{}

// Validate checks the field values on
// GetSportsTournamentRewardsClientIdsResponse_MapEntry with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetSportsTournamentRewardsClientIdsResponse_MapEntry) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetSportsTournamentRewardsClientIdsResponse_MapEntry with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetSportsTournamentRewardsClientIdsResponse_MapEntryMultiError, or nil if
// none found.
func (m *GetSportsTournamentRewardsClientIdsResponse_MapEntry) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSportsTournamentRewardsClientIdsResponse_MapEntry) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WeekNo

	if len(errors) > 0 {
		return GetSportsTournamentRewardsClientIdsResponse_MapEntryMultiError(errors)
	}

	return nil
}

// GetSportsTournamentRewardsClientIdsResponse_MapEntryMultiError is an error
// wrapping multiple validation errors returned by
// GetSportsTournamentRewardsClientIdsResponse_MapEntry.ValidateAll() if the
// designated constraints aren't met.
type GetSportsTournamentRewardsClientIdsResponse_MapEntryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSportsTournamentRewardsClientIdsResponse_MapEntryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSportsTournamentRewardsClientIdsResponse_MapEntryMultiError) AllErrors() []error { return m }

// GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError is the
// validation error returned by
// GetSportsTournamentRewardsClientIdsResponse_MapEntry.Validate if the
// designated constraints aren't met.
type GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError) ErrorName() string {
	return "GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError"
}

// Error satisfies the builtin error interface
func (e GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSportsTournamentRewardsClientIdsResponse_MapEntry.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSportsTournamentRewardsClientIdsResponse_MapEntryValidationError{}
