//go:generate gen_sql -types=Client,Lender,AssetType,MutualFundNavSource
syntax = "proto3";

package collateralmgrtsp.common;

option go_package = "github.com/epifi/gamma/api/collateralmgrtsp/common";
option java_package = "com.github.epifi.gamma.api.collateralmgrtsp.common";

enum Client {
  CLIENT_UNSPCIFIED = 0;
  CLIENT_EPIFI = 1;
}

enum Lender {
  LENDER_UNSPECIFIED = 0;
  LENDER_FEDERAL = 1;
}

enum AssetType {
  ASSET_TYPE_UNSPECIFIED = 0;
  ASSET_TYPE_MUTUAL_FUNDS = 1;
}

enum MutualFundNavSource {
  MUTUAL_FUND_NAV_SOURCE_UNSPECIFIED = 0;
  MUTUAL_FUND_NAV_SOURCE_MORNINGSTAR = 1;
  MUTUAL_FUND_NAV_SOURCE_MF_CENTRAL = 2;
}
