// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/sherlock/dev/db_state/parameter_meta.proto

package dbstate

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ParameterMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ParameterMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParameterMeta with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ParameterMetaMultiError, or
// nil if none found.
func (m *ParameterMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *ParameterMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Label

	// no validation rules for Type

	// no validation rules for ParameterOption

	if len(errors) > 0 {
		return ParameterMetaMultiError(errors)
	}

	return nil
}

// ParameterMetaMultiError is an error wrapping multiple validation errors
// returned by ParameterMeta.ValidateAll() if the designated constraints
// aren't met.
type ParameterMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParameterMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParameterMetaMultiError) AllErrors() []error { return m }

// ParameterMetaValidationError is the validation error returned by
// ParameterMeta.Validate if the designated constraints aren't met.
type ParameterMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParameterMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParameterMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParameterMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParameterMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParameterMetaValidationError) ErrorName() string { return "ParameterMetaValidationError" }

// Error satisfies the builtin error interface
func (e ParameterMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParameterMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParameterMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParameterMetaValidationError{}
