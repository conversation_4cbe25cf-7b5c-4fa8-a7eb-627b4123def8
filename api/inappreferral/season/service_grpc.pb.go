// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/inappreferrals/season/service.proto

package season

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SeasonService_GetSeasons_FullMethodName   = "/season.SeasonService/GetSeasons"
	SeasonService_CreateSeason_FullMethodName = "/season.SeasonService/CreateSeason"
	SeasonService_UpdateSeason_FullMethodName = "/season.SeasonService/UpdateSeason"
)

// SeasonServiceClient is the client API for SeasonService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SeasonServiceClient interface {
	// GetSeasons returns the seasons
	GetSeasons(ctx context.Context, in *GetSeasonsRequest, opts ...grpc.CallOption) (*GetSeasonsResponse, error)
	// create season
	CreateSeason(ctx context.Context, in *CreateSeasonRequest, opts ...grpc.CallOption) (*CreateSeasonResponse, error)
	// update season details
	UpdateSeason(ctx context.Context, in *UpdateSeasonRequest, opts ...grpc.CallOption) (*UpdateSeasonResponse, error)
}

type seasonServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSeasonServiceClient(cc grpc.ClientConnInterface) SeasonServiceClient {
	return &seasonServiceClient{cc}
}

func (c *seasonServiceClient) GetSeasons(ctx context.Context, in *GetSeasonsRequest, opts ...grpc.CallOption) (*GetSeasonsResponse, error) {
	out := new(GetSeasonsResponse)
	err := c.cc.Invoke(ctx, SeasonService_GetSeasons_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seasonServiceClient) CreateSeason(ctx context.Context, in *CreateSeasonRequest, opts ...grpc.CallOption) (*CreateSeasonResponse, error) {
	out := new(CreateSeasonResponse)
	err := c.cc.Invoke(ctx, SeasonService_CreateSeason_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seasonServiceClient) UpdateSeason(ctx context.Context, in *UpdateSeasonRequest, opts ...grpc.CallOption) (*UpdateSeasonResponse, error) {
	out := new(UpdateSeasonResponse)
	err := c.cc.Invoke(ctx, SeasonService_UpdateSeason_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SeasonServiceServer is the server API for SeasonService service.
// All implementations should embed UnimplementedSeasonServiceServer
// for forward compatibility
type SeasonServiceServer interface {
	// GetSeasons returns the seasons
	GetSeasons(context.Context, *GetSeasonsRequest) (*GetSeasonsResponse, error)
	// create season
	CreateSeason(context.Context, *CreateSeasonRequest) (*CreateSeasonResponse, error)
	// update season details
	UpdateSeason(context.Context, *UpdateSeasonRequest) (*UpdateSeasonResponse, error)
}

// UnimplementedSeasonServiceServer should be embedded to have forward compatible implementations.
type UnimplementedSeasonServiceServer struct {
}

func (UnimplementedSeasonServiceServer) GetSeasons(context.Context, *GetSeasonsRequest) (*GetSeasonsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeasons not implemented")
}
func (UnimplementedSeasonServiceServer) CreateSeason(context.Context, *CreateSeasonRequest) (*CreateSeasonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSeason not implemented")
}
func (UnimplementedSeasonServiceServer) UpdateSeason(context.Context, *UpdateSeasonRequest) (*UpdateSeasonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSeason not implemented")
}

// UnsafeSeasonServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SeasonServiceServer will
// result in compilation errors.
type UnsafeSeasonServiceServer interface {
	mustEmbedUnimplementedSeasonServiceServer()
}

func RegisterSeasonServiceServer(s grpc.ServiceRegistrar, srv SeasonServiceServer) {
	s.RegisterService(&SeasonService_ServiceDesc, srv)
}

func _SeasonService_GetSeasons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeasonsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeasonServiceServer).GetSeasons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SeasonService_GetSeasons_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeasonServiceServer).GetSeasons(ctx, req.(*GetSeasonsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeasonService_CreateSeason_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSeasonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeasonServiceServer).CreateSeason(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SeasonService_CreateSeason_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeasonServiceServer).CreateSeason(ctx, req.(*CreateSeasonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeasonService_UpdateSeason_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSeasonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeasonServiceServer).UpdateSeason(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SeasonService_UpdateSeason_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeasonServiceServer).UpdateSeason(ctx, req.(*UpdateSeasonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SeasonService_ServiceDesc is the grpc.ServiceDesc for SeasonService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SeasonService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "season.SeasonService",
	HandlerType: (*SeasonServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSeasons",
			Handler:    _SeasonService_GetSeasons_Handler,
		},
		{
			MethodName: "CreateSeason",
			Handler:    _SeasonService_CreateSeason_Handler,
		},
		{
			MethodName: "UpdateSeason",
			Handler:    _SeasonService_UpdateSeason_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/inappreferrals/season/service.proto",
}
