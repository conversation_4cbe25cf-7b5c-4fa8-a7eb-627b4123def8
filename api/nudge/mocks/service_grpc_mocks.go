// Code generated by MockGen. DO NOT EDIT.
// Source: api/nudge/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	nudge "github.com/epifi/gamma/api/nudge"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockNudgeServiceClient is a mock of NudgeServiceClient interface.
type MockNudgeServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockNudgeServiceClientMockRecorder
}

// MockNudgeServiceClientMockRecorder is the mock recorder for MockNudgeServiceClient.
type MockNudgeServiceClientMockRecorder struct {
	mock *MockNudgeServiceClient
}

// NewMockNudgeServiceClient creates a new mock instance.
func NewMockNudgeServiceClient(ctrl *gomock.Controller) *MockNudgeServiceClient {
	mock := &MockNudgeServiceClient{ctrl: ctrl}
	mock.recorder = &MockNudgeServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNudgeServiceClient) EXPECT() *MockNudgeServiceClientMockRecorder {
	return m.recorder
}

// BulkCreateNudgeEntryForActor mocks base method.
func (m *MockNudgeServiceClient) BulkCreateNudgeEntryForActor(ctx context.Context, in *nudge.BulkCreateNudgeEntryForActorRequest, opts ...grpc.CallOption) (*nudge.BulkCreateNudgeEntryForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkCreateNudgeEntryForActor", varargs...)
	ret0, _ := ret[0].(*nudge.BulkCreateNudgeEntryForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkCreateNudgeEntryForActor indicates an expected call of BulkCreateNudgeEntryForActor.
func (mr *MockNudgeServiceClientMockRecorder) BulkCreateNudgeEntryForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkCreateNudgeEntryForActor", reflect.TypeOf((*MockNudgeServiceClient)(nil).BulkCreateNudgeEntryForActor), varargs...)
}

// CreateNudge mocks base method.
func (m *MockNudgeServiceClient) CreateNudge(ctx context.Context, in *nudge.CreateNudgeRequest, opts ...grpc.CallOption) (*nudge.CreateNudgeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateNudge", varargs...)
	ret0, _ := ret[0].(*nudge.CreateNudgeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNudge indicates an expected call of CreateNudge.
func (mr *MockNudgeServiceClientMockRecorder) CreateNudge(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNudge", reflect.TypeOf((*MockNudgeServiceClient)(nil).CreateNudge), varargs...)
}

// DismissNudgeForActor mocks base method.
func (m *MockNudgeServiceClient) DismissNudgeForActor(ctx context.Context, in *nudge.DismissNudgeForActorRequest, opts ...grpc.CallOption) (*nudge.DismissNudgeForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DismissNudgeForActor", varargs...)
	ret0, _ := ret[0].(*nudge.DismissNudgeForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DismissNudgeForActor indicates an expected call of DismissNudgeForActor.
func (mr *MockNudgeServiceClientMockRecorder) DismissNudgeForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DismissNudgeForActor", reflect.TypeOf((*MockNudgeServiceClient)(nil).DismissNudgeForActor), varargs...)
}

// EditNudge mocks base method.
func (m *MockNudgeServiceClient) EditNudge(ctx context.Context, in *nudge.EditNudgeRequest, opts ...grpc.CallOption) (*nudge.EditNudgeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EditNudge", varargs...)
	ret0, _ := ret[0].(*nudge.EditNudgeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditNudge indicates an expected call of EditNudge.
func (mr *MockNudgeServiceClientMockRecorder) EditNudge(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditNudge", reflect.TypeOf((*MockNudgeServiceClient)(nil).EditNudge), varargs...)
}

// FetchDisplayableNudges mocks base method.
func (m *MockNudgeServiceClient) FetchDisplayableNudges(ctx context.Context, in *nudge.FetchDisplayableNudgesRequest, opts ...grpc.CallOption) (*nudge.FetchDisplayableNudgesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchDisplayableNudges", varargs...)
	ret0, _ := ret[0].(*nudge.FetchDisplayableNudgesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDisplayableNudges indicates an expected call of FetchDisplayableNudges.
func (mr *MockNudgeServiceClientMockRecorder) FetchDisplayableNudges(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDisplayableNudges", reflect.TypeOf((*MockNudgeServiceClient)(nil).FetchDisplayableNudges), varargs...)
}

// FetchNudgeInstances mocks base method.
func (m *MockNudgeServiceClient) FetchNudgeInstances(ctx context.Context, in *nudge.FetchNudgeInstancesRequest, opts ...grpc.CallOption) (*nudge.FetchNudgeInstancesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchNudgeInstances", varargs...)
	ret0, _ := ret[0].(*nudge.FetchNudgeInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchNudgeInstances indicates an expected call of FetchNudgeInstances.
func (mr *MockNudgeServiceClientMockRecorder) FetchNudgeInstances(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchNudgeInstances", reflect.TypeOf((*MockNudgeServiceClient)(nil).FetchNudgeInstances), varargs...)
}

// FetchNudgesByIds mocks base method.
func (m *MockNudgeServiceClient) FetchNudgesByIds(ctx context.Context, in *nudge.FetchNudgesByIdsRequest, opts ...grpc.CallOption) (*nudge.FetchNudgesByIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchNudgesByIds", varargs...)
	ret0, _ := ret[0].(*nudge.FetchNudgesByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchNudgesByIds indicates an expected call of FetchNudgesByIds.
func (mr *MockNudgeServiceClientMockRecorder) FetchNudgesByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchNudgesByIds", reflect.TypeOf((*MockNudgeServiceClient)(nil).FetchNudgesByIds), varargs...)
}

// PerformNudgeEntryForActor mocks base method.
func (m *MockNudgeServiceClient) PerformNudgeEntryForActor(ctx context.Context, in *nudge.PerformNudgeEntryForActorRequest, opts ...grpc.CallOption) (*nudge.PerformNudgeEntryForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PerformNudgeEntryForActor", varargs...)
	ret0, _ := ret[0].(*nudge.PerformNudgeEntryForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PerformNudgeEntryForActor indicates an expected call of PerformNudgeEntryForActor.
func (mr *MockNudgeServiceClientMockRecorder) PerformNudgeEntryForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PerformNudgeEntryForActor", reflect.TypeOf((*MockNudgeServiceClient)(nil).PerformNudgeEntryForActor), varargs...)
}

// PerformNudgeExitForActor mocks base method.
func (m *MockNudgeServiceClient) PerformNudgeExitForActor(ctx context.Context, in *nudge.PerformNudgeExitForActorRequest, opts ...grpc.CallOption) (*nudge.PerformNudgeExitForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PerformNudgeExitForActor", varargs...)
	ret0, _ := ret[0].(*nudge.PerformNudgeExitForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PerformNudgeExitForActor indicates an expected call of PerformNudgeExitForActor.
func (mr *MockNudgeServiceClientMockRecorder) PerformNudgeExitForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PerformNudgeExitForActor", reflect.TypeOf((*MockNudgeServiceClient)(nil).PerformNudgeExitForActor), varargs...)
}

// UpdateNudgeStatus mocks base method.
func (m *MockNudgeServiceClient) UpdateNudgeStatus(ctx context.Context, in *nudge.UpdateNudgeStatusRequest, opts ...grpc.CallOption) (*nudge.UpdateNudgeStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateNudgeStatus", varargs...)
	ret0, _ := ret[0].(*nudge.UpdateNudgeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNudgeStatus indicates an expected call of UpdateNudgeStatus.
func (mr *MockNudgeServiceClientMockRecorder) UpdateNudgeStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNudgeStatus", reflect.TypeOf((*MockNudgeServiceClient)(nil).UpdateNudgeStatus), varargs...)
}

// MockNudgeServiceServer is a mock of NudgeServiceServer interface.
type MockNudgeServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockNudgeServiceServerMockRecorder
}

// MockNudgeServiceServerMockRecorder is the mock recorder for MockNudgeServiceServer.
type MockNudgeServiceServerMockRecorder struct {
	mock *MockNudgeServiceServer
}

// NewMockNudgeServiceServer creates a new mock instance.
func NewMockNudgeServiceServer(ctrl *gomock.Controller) *MockNudgeServiceServer {
	mock := &MockNudgeServiceServer{ctrl: ctrl}
	mock.recorder = &MockNudgeServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNudgeServiceServer) EXPECT() *MockNudgeServiceServerMockRecorder {
	return m.recorder
}

// BulkCreateNudgeEntryForActor mocks base method.
func (m *MockNudgeServiceServer) BulkCreateNudgeEntryForActor(arg0 context.Context, arg1 *nudge.BulkCreateNudgeEntryForActorRequest) (*nudge.BulkCreateNudgeEntryForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkCreateNudgeEntryForActor", arg0, arg1)
	ret0, _ := ret[0].(*nudge.BulkCreateNudgeEntryForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkCreateNudgeEntryForActor indicates an expected call of BulkCreateNudgeEntryForActor.
func (mr *MockNudgeServiceServerMockRecorder) BulkCreateNudgeEntryForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkCreateNudgeEntryForActor", reflect.TypeOf((*MockNudgeServiceServer)(nil).BulkCreateNudgeEntryForActor), arg0, arg1)
}

// CreateNudge mocks base method.
func (m *MockNudgeServiceServer) CreateNudge(arg0 context.Context, arg1 *nudge.CreateNudgeRequest) (*nudge.CreateNudgeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNudge", arg0, arg1)
	ret0, _ := ret[0].(*nudge.CreateNudgeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNudge indicates an expected call of CreateNudge.
func (mr *MockNudgeServiceServerMockRecorder) CreateNudge(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNudge", reflect.TypeOf((*MockNudgeServiceServer)(nil).CreateNudge), arg0, arg1)
}

// DismissNudgeForActor mocks base method.
func (m *MockNudgeServiceServer) DismissNudgeForActor(arg0 context.Context, arg1 *nudge.DismissNudgeForActorRequest) (*nudge.DismissNudgeForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DismissNudgeForActor", arg0, arg1)
	ret0, _ := ret[0].(*nudge.DismissNudgeForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DismissNudgeForActor indicates an expected call of DismissNudgeForActor.
func (mr *MockNudgeServiceServerMockRecorder) DismissNudgeForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DismissNudgeForActor", reflect.TypeOf((*MockNudgeServiceServer)(nil).DismissNudgeForActor), arg0, arg1)
}

// EditNudge mocks base method.
func (m *MockNudgeServiceServer) EditNudge(arg0 context.Context, arg1 *nudge.EditNudgeRequest) (*nudge.EditNudgeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EditNudge", arg0, arg1)
	ret0, _ := ret[0].(*nudge.EditNudgeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditNudge indicates an expected call of EditNudge.
func (mr *MockNudgeServiceServerMockRecorder) EditNudge(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditNudge", reflect.TypeOf((*MockNudgeServiceServer)(nil).EditNudge), arg0, arg1)
}

// FetchDisplayableNudges mocks base method.
func (m *MockNudgeServiceServer) FetchDisplayableNudges(arg0 context.Context, arg1 *nudge.FetchDisplayableNudgesRequest) (*nudge.FetchDisplayableNudgesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchDisplayableNudges", arg0, arg1)
	ret0, _ := ret[0].(*nudge.FetchDisplayableNudgesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDisplayableNudges indicates an expected call of FetchDisplayableNudges.
func (mr *MockNudgeServiceServerMockRecorder) FetchDisplayableNudges(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDisplayableNudges", reflect.TypeOf((*MockNudgeServiceServer)(nil).FetchDisplayableNudges), arg0, arg1)
}

// FetchNudgeInstances mocks base method.
func (m *MockNudgeServiceServer) FetchNudgeInstances(arg0 context.Context, arg1 *nudge.FetchNudgeInstancesRequest) (*nudge.FetchNudgeInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchNudgeInstances", arg0, arg1)
	ret0, _ := ret[0].(*nudge.FetchNudgeInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchNudgeInstances indicates an expected call of FetchNudgeInstances.
func (mr *MockNudgeServiceServerMockRecorder) FetchNudgeInstances(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchNudgeInstances", reflect.TypeOf((*MockNudgeServiceServer)(nil).FetchNudgeInstances), arg0, arg1)
}

// FetchNudgesByIds mocks base method.
func (m *MockNudgeServiceServer) FetchNudgesByIds(arg0 context.Context, arg1 *nudge.FetchNudgesByIdsRequest) (*nudge.FetchNudgesByIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchNudgesByIds", arg0, arg1)
	ret0, _ := ret[0].(*nudge.FetchNudgesByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchNudgesByIds indicates an expected call of FetchNudgesByIds.
func (mr *MockNudgeServiceServerMockRecorder) FetchNudgesByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchNudgesByIds", reflect.TypeOf((*MockNudgeServiceServer)(nil).FetchNudgesByIds), arg0, arg1)
}

// PerformNudgeEntryForActor mocks base method.
func (m *MockNudgeServiceServer) PerformNudgeEntryForActor(arg0 context.Context, arg1 *nudge.PerformNudgeEntryForActorRequest) (*nudge.PerformNudgeEntryForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PerformNudgeEntryForActor", arg0, arg1)
	ret0, _ := ret[0].(*nudge.PerformNudgeEntryForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PerformNudgeEntryForActor indicates an expected call of PerformNudgeEntryForActor.
func (mr *MockNudgeServiceServerMockRecorder) PerformNudgeEntryForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PerformNudgeEntryForActor", reflect.TypeOf((*MockNudgeServiceServer)(nil).PerformNudgeEntryForActor), arg0, arg1)
}

// PerformNudgeExitForActor mocks base method.
func (m *MockNudgeServiceServer) PerformNudgeExitForActor(arg0 context.Context, arg1 *nudge.PerformNudgeExitForActorRequest) (*nudge.PerformNudgeExitForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PerformNudgeExitForActor", arg0, arg1)
	ret0, _ := ret[0].(*nudge.PerformNudgeExitForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PerformNudgeExitForActor indicates an expected call of PerformNudgeExitForActor.
func (mr *MockNudgeServiceServerMockRecorder) PerformNudgeExitForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PerformNudgeExitForActor", reflect.TypeOf((*MockNudgeServiceServer)(nil).PerformNudgeExitForActor), arg0, arg1)
}

// UpdateNudgeStatus mocks base method.
func (m *MockNudgeServiceServer) UpdateNudgeStatus(arg0 context.Context, arg1 *nudge.UpdateNudgeStatusRequest) (*nudge.UpdateNudgeStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNudgeStatus", arg0, arg1)
	ret0, _ := ret[0].(*nudge.UpdateNudgeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNudgeStatus indicates an expected call of UpdateNudgeStatus.
func (mr *MockNudgeServiceServerMockRecorder) UpdateNudgeStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNudgeStatus", reflect.TypeOf((*MockNudgeServiceServer)(nil).UpdateNudgeStatus), arg0, arg1)
}

// MockUnsafeNudgeServiceServer is a mock of UnsafeNudgeServiceServer interface.
type MockUnsafeNudgeServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeNudgeServiceServerMockRecorder
}

// MockUnsafeNudgeServiceServerMockRecorder is the mock recorder for MockUnsafeNudgeServiceServer.
type MockUnsafeNudgeServiceServerMockRecorder struct {
	mock *MockUnsafeNudgeServiceServer
}

// NewMockUnsafeNudgeServiceServer creates a new mock instance.
func NewMockUnsafeNudgeServiceServer(ctrl *gomock.Controller) *MockUnsafeNudgeServiceServer {
	mock := &MockUnsafeNudgeServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeNudgeServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeNudgeServiceServer) EXPECT() *MockUnsafeNudgeServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedNudgeServiceServer mocks base method.
func (m *MockUnsafeNudgeServiceServer) mustEmbedUnimplementedNudgeServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedNudgeServiceServer")
}

// mustEmbedUnimplementedNudgeServiceServer indicates an expected call of mustEmbedUnimplementedNudgeServiceServer.
func (mr *MockUnsafeNudgeServiceServerMockRecorder) mustEmbedUnimplementedNudgeServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedNudgeServiceServer", reflect.TypeOf((*MockUnsafeNudgeServiceServer)(nil).mustEmbedUnimplementedNudgeServiceServer))
}
