syntax = "proto3";

package firefly;

import "api/firefly/enums/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/firefly";
option java_package = "com.github.epifi.gamma.api.firefly";

//go:generate gen_sql -types=FeatureOverrideInfo
message CreditCardSkuOverride {
  // id of the actor for whom the feature override is being done
  string actor_id = 1;
  // enum denoting the variant of card
  firefly.enums.CardSKUType card_sku_type = 2;
  // Jsonb containing feature information specific to an actor
  firefly.FeatureOverrideInfo feature_override_info = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  google.protobuf.Timestamp deleted_at = 6;
}

message FeatureOverrideInfo {
  oneof Feature {
    FreeCardReplacements replacements = 1;
  }
}

message FreeCardReplacements {
  int32 replacement_count = 1;
}
