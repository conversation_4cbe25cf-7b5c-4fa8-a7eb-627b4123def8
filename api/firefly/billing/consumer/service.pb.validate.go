// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/firefly/billing/consumer/service.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/firefly/billing/enums"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.CreditCardStatementNotificationType(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on
// ProcessStatementGeneratedNotificationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessStatementGeneratedNotificationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessStatementGeneratedNotificationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessStatementGeneratedNotificationRequestMultiError, or nil if none found.
func (m *ProcessStatementGeneratedNotificationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessStatementGeneratedNotificationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessStatementGeneratedNotificationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessStatementGeneratedNotificationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessStatementGeneratedNotificationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntityId

	// no validation rules for StatementNotificationType

	if all {
		switch v := interface{}(m.GetStatementDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessStatementGeneratedNotificationRequestValidationError{
					field:  "StatementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessStatementGeneratedNotificationRequestValidationError{
					field:  "StatementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatementDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessStatementGeneratedNotificationRequestValidationError{
				field:  "StatementDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	if len(errors) > 0 {
		return ProcessStatementGeneratedNotificationRequestMultiError(errors)
	}

	return nil
}

// ProcessStatementGeneratedNotificationRequestMultiError is an error wrapping
// multiple validation errors returned by
// ProcessStatementGeneratedNotificationRequest.ValidateAll() if the
// designated constraints aren't met.
type ProcessStatementGeneratedNotificationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessStatementGeneratedNotificationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessStatementGeneratedNotificationRequestMultiError) AllErrors() []error { return m }

// ProcessStatementGeneratedNotificationRequestValidationError is the
// validation error returned by
// ProcessStatementGeneratedNotificationRequest.Validate if the designated
// constraints aren't met.
type ProcessStatementGeneratedNotificationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessStatementGeneratedNotificationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessStatementGeneratedNotificationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessStatementGeneratedNotificationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessStatementGeneratedNotificationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessStatementGeneratedNotificationRequestValidationError) ErrorName() string {
	return "ProcessStatementGeneratedNotificationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessStatementGeneratedNotificationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessStatementGeneratedNotificationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessStatementGeneratedNotificationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessStatementGeneratedNotificationRequestValidationError{}

// Validate checks the field values on
// ProcessStatementGeneratedNotificationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessStatementGeneratedNotificationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessStatementGeneratedNotificationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessStatementGeneratedNotificationResponseMultiError, or nil if none found.
func (m *ProcessStatementGeneratedNotificationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessStatementGeneratedNotificationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessStatementGeneratedNotificationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessStatementGeneratedNotificationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessStatementGeneratedNotificationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessStatementGeneratedNotificationResponseMultiError(errors)
	}

	return nil
}

// ProcessStatementGeneratedNotificationResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessStatementGeneratedNotificationResponse.ValidateAll() if the
// designated constraints aren't met.
type ProcessStatementGeneratedNotificationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessStatementGeneratedNotificationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessStatementGeneratedNotificationResponseMultiError) AllErrors() []error { return m }

// ProcessStatementGeneratedNotificationResponseValidationError is the
// validation error returned by
// ProcessStatementGeneratedNotificationResponse.Validate if the designated
// constraints aren't met.
type ProcessStatementGeneratedNotificationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessStatementGeneratedNotificationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessStatementGeneratedNotificationResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProcessStatementGeneratedNotificationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessStatementGeneratedNotificationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessStatementGeneratedNotificationResponseValidationError) ErrorName() string {
	return "ProcessStatementGeneratedNotificationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessStatementGeneratedNotificationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessStatementGeneratedNotificationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessStatementGeneratedNotificationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessStatementGeneratedNotificationResponseValidationError{}
