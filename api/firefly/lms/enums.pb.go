//go:generate gen_sql -types=LoanAccountStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/firefly/lms/enums/enums.proto

package lms

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoanAccountStatus int32

const (
	LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED       LoanAccountStatus = 0
	LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE            LoanAccountStatus = 1
	LoanAccountStatus_LOAN_ACCOUNT_STATUS_PENDING_AT_VENDOR LoanAccountStatus = 2
	LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED            LoanAccountStatus = 3
	LoanAccountStatus_LOAN_ACCOUNT_STATUS_PRE_CLOSED        LoanAccountStatus = 4
	LoanAccountStatus_LOAN_ACCOUNT_STATUS_CANCELLED         LoanAccountStatus = 5
)

// Enum value maps for LoanAccountStatus.
var (
	LoanAccountStatus_name = map[int32]string{
		0: "LOAN_ACCOUNT_STATUS_UNSPECIFIED",
		1: "LOAN_ACCOUNT_STATUS_ACTIVE",
		2: "LOAN_ACCOUNT_STATUS_PENDING_AT_VENDOR",
		3: "LOAN_ACCOUNT_STATUS_CLOSED",
		4: "LOAN_ACCOUNT_STATUS_PRE_CLOSED",
		5: "LOAN_ACCOUNT_STATUS_CANCELLED",
	}
	LoanAccountStatus_value = map[string]int32{
		"LOAN_ACCOUNT_STATUS_UNSPECIFIED":       0,
		"LOAN_ACCOUNT_STATUS_ACTIVE":            1,
		"LOAN_ACCOUNT_STATUS_PENDING_AT_VENDOR": 2,
		"LOAN_ACCOUNT_STATUS_CLOSED":            3,
		"LOAN_ACCOUNT_STATUS_PRE_CLOSED":        4,
		"LOAN_ACCOUNT_STATUS_CANCELLED":         5,
	}
)

func (x LoanAccountStatus) Enum() *LoanAccountStatus {
	p := new(LoanAccountStatus)
	*p = x
	return p
}

func (x LoanAccountStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanAccountStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_lms_enums_enums_proto_enumTypes[0].Descriptor()
}

func (LoanAccountStatus) Type() protoreflect.EnumType {
	return &file_api_firefly_lms_enums_enums_proto_enumTypes[0]
}

func (x LoanAccountStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanAccountStatus.Descriptor instead.
func (LoanAccountStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_lms_enums_enums_proto_rawDescGZIP(), []int{0}
}

type LoanAccountRequestType int32

const (
	LoanAccountRequestType_LOAN_ACCOUNT_REQUEST_TYPE_UNSPECIFIED     LoanAccountRequestType = 0
	LoanAccountRequestType_LOAN_ACCOUNT_REQUEST_TYPE_TOP_ACCOUNTS    LoanAccountRequestType = 1
	LoanAccountRequestType_LOAN_ACCOUNT_REQUEST_TYPE_ACTIVE_ACCOUNTS LoanAccountRequestType = 2
	LoanAccountRequestType_LOAN_ACCOUNT_REQUEST_TYPE_CLOSED_ACCOUNTS LoanAccountRequestType = 3
)

// Enum value maps for LoanAccountRequestType.
var (
	LoanAccountRequestType_name = map[int32]string{
		0: "LOAN_ACCOUNT_REQUEST_TYPE_UNSPECIFIED",
		1: "LOAN_ACCOUNT_REQUEST_TYPE_TOP_ACCOUNTS",
		2: "LOAN_ACCOUNT_REQUEST_TYPE_ACTIVE_ACCOUNTS",
		3: "LOAN_ACCOUNT_REQUEST_TYPE_CLOSED_ACCOUNTS",
	}
	LoanAccountRequestType_value = map[string]int32{
		"LOAN_ACCOUNT_REQUEST_TYPE_UNSPECIFIED":     0,
		"LOAN_ACCOUNT_REQUEST_TYPE_TOP_ACCOUNTS":    1,
		"LOAN_ACCOUNT_REQUEST_TYPE_ACTIVE_ACCOUNTS": 2,
		"LOAN_ACCOUNT_REQUEST_TYPE_CLOSED_ACCOUNTS": 3,
	}
)

func (x LoanAccountRequestType) Enum() *LoanAccountRequestType {
	p := new(LoanAccountRequestType)
	*p = x
	return p
}

func (x LoanAccountRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanAccountRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_lms_enums_enums_proto_enumTypes[1].Descriptor()
}

func (LoanAccountRequestType) Type() protoreflect.EnumType {
	return &file_api_firefly_lms_enums_enums_proto_enumTypes[1]
}

func (x LoanAccountRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanAccountRequestType.Descriptor instead.
func (LoanAccountRequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_lms_enums_enums_proto_rawDescGZIP(), []int{1}
}

type TransactionLoanOffersFieldMask int32

const (
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_UNSPECIFIED            TransactionLoanOffersFieldMask = 0
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_ID                     TransactionLoanOffersFieldMask = 1
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_ACTOR_ID               TransactionLoanOffersFieldMask = 2
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_TRANSACTION_ID         TransactionLoanOffersFieldMask = 3
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_TENURE_IN_MONTHS       TransactionLoanOffersFieldMask = 4
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_VENDOR_LOAN_REQUEST_ID TransactionLoanOffersFieldMask = 5
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_AMOUNT_INFO            TransactionLoanOffersFieldMask = 6
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_INTEREST_INFO          TransactionLoanOffersFieldMask = 7
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_PROCESSING_FEE_INFO    TransactionLoanOffersFieldMask = 8
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_SUMMARY                TransactionLoanOffersFieldMask = 9
	TransactionLoanOffersFieldMask_TRANSACTION_LOAN_OFFERS_FIELD_MASK_UPDATED_AT             TransactionLoanOffersFieldMask = 10
)

// Enum value maps for TransactionLoanOffersFieldMask.
var (
	TransactionLoanOffersFieldMask_name = map[int32]string{
		0:  "TRANSACTION_LOAN_OFFERS_FIELD_MASK_UNSPECIFIED",
		1:  "TRANSACTION_LOAN_OFFERS_FIELD_MASK_ID",
		2:  "TRANSACTION_LOAN_OFFERS_FIELD_MASK_ACTOR_ID",
		3:  "TRANSACTION_LOAN_OFFERS_FIELD_MASK_TRANSACTION_ID",
		4:  "TRANSACTION_LOAN_OFFERS_FIELD_MASK_TENURE_IN_MONTHS",
		5:  "TRANSACTION_LOAN_OFFERS_FIELD_MASK_VENDOR_LOAN_REQUEST_ID",
		6:  "TRANSACTION_LOAN_OFFERS_FIELD_MASK_AMOUNT_INFO",
		7:  "TRANSACTION_LOAN_OFFERS_FIELD_MASK_INTEREST_INFO",
		8:  "TRANSACTION_LOAN_OFFERS_FIELD_MASK_PROCESSING_FEE_INFO",
		9:  "TRANSACTION_LOAN_OFFERS_FIELD_MASK_SUMMARY",
		10: "TRANSACTION_LOAN_OFFERS_FIELD_MASK_UPDATED_AT",
	}
	TransactionLoanOffersFieldMask_value = map[string]int32{
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_UNSPECIFIED":            0,
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_ID":                     1,
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_ACTOR_ID":               2,
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_TRANSACTION_ID":         3,
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_TENURE_IN_MONTHS":       4,
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_VENDOR_LOAN_REQUEST_ID": 5,
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_AMOUNT_INFO":            6,
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_INTEREST_INFO":          7,
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_PROCESSING_FEE_INFO":    8,
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_SUMMARY":                9,
		"TRANSACTION_LOAN_OFFERS_FIELD_MASK_UPDATED_AT":             10,
	}
)

func (x TransactionLoanOffersFieldMask) Enum() *TransactionLoanOffersFieldMask {
	p := new(TransactionLoanOffersFieldMask)
	*p = x
	return p
}

func (x TransactionLoanOffersFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionLoanOffersFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_lms_enums_enums_proto_enumTypes[2].Descriptor()
}

func (TransactionLoanOffersFieldMask) Type() protoreflect.EnumType {
	return &file_api_firefly_lms_enums_enums_proto_enumTypes[2]
}

func (x TransactionLoanOffersFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionLoanOffersFieldMask.Descriptor instead.
func (TransactionLoanOffersFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_lms_enums_enums_proto_rawDescGZIP(), []int{2}
}

type LoanAccountFieldMask int32

const (
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_UNSPECIFIED                LoanAccountFieldMask = 0
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_ID                         LoanAccountFieldMask = 1
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_ACTOR_ID                   LoanAccountFieldMask = 2
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_TRANSACTION_LOAN_OFFERS_ID LoanAccountFieldMask = 3
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_VENDOR_LOAN_ID             LoanAccountFieldMask = 4
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_TENURE_IN_MONTHS           LoanAccountFieldMask = 5
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_AMOUNT_INFO                LoanAccountFieldMask = 6
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_INTEREST_INFO              LoanAccountFieldMask = 7
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_FEE_INFO                   LoanAccountFieldMask = 8
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_REPAYMENT_INFO             LoanAccountFieldMask = 9
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_STATUS                     LoanAccountFieldMask = 10
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_SCHEDULE              LoanAccountFieldMask = 11
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_SUMMARY                    LoanAccountFieldMask = 12
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_DISBURSED_DATE             LoanAccountFieldMask = 13
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE              LoanAccountFieldMask = 14
	LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_UPDATED_AT                 LoanAccountFieldMask = 15
)

// Enum value maps for LoanAccountFieldMask.
var (
	LoanAccountFieldMask_name = map[int32]string{
		0:  "LOAN_ACCOUNT_FIELD_MASK_UNSPECIFIED",
		1:  "LOAN_ACCOUNT_FIELD_MASK_ID",
		2:  "LOAN_ACCOUNT_FIELD_MASK_ACTOR_ID",
		3:  "LOAN_ACCOUNT_FIELD_MASK_TRANSACTION_LOAN_OFFERS_ID",
		4:  "LOAN_ACCOUNT_FIELD_MASK_VENDOR_LOAN_ID",
		5:  "LOAN_ACCOUNT_FIELD_MASK_TENURE_IN_MONTHS",
		6:  "LOAN_ACCOUNT_FIELD_MASK_AMOUNT_INFO",
		7:  "LOAN_ACCOUNT_FIELD_MASK_INTEREST_INFO",
		8:  "LOAN_ACCOUNT_FIELD_MASK_FEE_INFO",
		9:  "LOAN_ACCOUNT_FIELD_MASK_REPAYMENT_INFO",
		10: "LOAN_ACCOUNT_FIELD_MASK_STATUS",
		11: "LOAN_ACCOUNT_FIELD_MASK_LOAN_SCHEDULE",
		12: "LOAN_ACCOUNT_FIELD_MASK_SUMMARY",
		13: "LOAN_ACCOUNT_FIELD_MASK_DISBURSED_DATE",
		14: "LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE",
		15: "LOAN_ACCOUNT_FIELD_MASK_UPDATED_AT",
	}
	LoanAccountFieldMask_value = map[string]int32{
		"LOAN_ACCOUNT_FIELD_MASK_UNSPECIFIED":                0,
		"LOAN_ACCOUNT_FIELD_MASK_ID":                         1,
		"LOAN_ACCOUNT_FIELD_MASK_ACTOR_ID":                   2,
		"LOAN_ACCOUNT_FIELD_MASK_TRANSACTION_LOAN_OFFERS_ID": 3,
		"LOAN_ACCOUNT_FIELD_MASK_VENDOR_LOAN_ID":             4,
		"LOAN_ACCOUNT_FIELD_MASK_TENURE_IN_MONTHS":           5,
		"LOAN_ACCOUNT_FIELD_MASK_AMOUNT_INFO":                6,
		"LOAN_ACCOUNT_FIELD_MASK_INTEREST_INFO":              7,
		"LOAN_ACCOUNT_FIELD_MASK_FEE_INFO":                   8,
		"LOAN_ACCOUNT_FIELD_MASK_REPAYMENT_INFO":             9,
		"LOAN_ACCOUNT_FIELD_MASK_STATUS":                     10,
		"LOAN_ACCOUNT_FIELD_MASK_LOAN_SCHEDULE":              11,
		"LOAN_ACCOUNT_FIELD_MASK_SUMMARY":                    12,
		"LOAN_ACCOUNT_FIELD_MASK_DISBURSED_DATE":             13,
		"LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE":              14,
		"LOAN_ACCOUNT_FIELD_MASK_UPDATED_AT":                 15,
	}
)

func (x LoanAccountFieldMask) Enum() *LoanAccountFieldMask {
	p := new(LoanAccountFieldMask)
	*p = x
	return p
}

func (x LoanAccountFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanAccountFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_lms_enums_enums_proto_enumTypes[3].Descriptor()
}

func (LoanAccountFieldMask) Type() protoreflect.EnumType {
	return &file_api_firefly_lms_enums_enums_proto_enumTypes[3]
}

func (x LoanAccountFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanAccountFieldMask.Descriptor instead.
func (LoanAccountFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_lms_enums_enums_proto_rawDescGZIP(), []int{3}
}

type EligibleTransactionRequestType int32

const (
	EligibleTransactionRequestType_ELIGIBLE_TRANSACTION_REQUEST_TYPE_UNSPECIFIED      EligibleTransactionRequestType = 0
	EligibleTransactionRequestType_ELIGIBLE_TRANSACTION_REQUEST_TYPE_TOP_TRANSACTIONS EligibleTransactionRequestType = 1
	EligibleTransactionRequestType_ELIGIBLE_TRANSACTION_REQUEST_TYPE_ALL_TRANSACTIONS EligibleTransactionRequestType = 2
)

// Enum value maps for EligibleTransactionRequestType.
var (
	EligibleTransactionRequestType_name = map[int32]string{
		0: "ELIGIBLE_TRANSACTION_REQUEST_TYPE_UNSPECIFIED",
		1: "ELIGIBLE_TRANSACTION_REQUEST_TYPE_TOP_TRANSACTIONS",
		2: "ELIGIBLE_TRANSACTION_REQUEST_TYPE_ALL_TRANSACTIONS",
	}
	EligibleTransactionRequestType_value = map[string]int32{
		"ELIGIBLE_TRANSACTION_REQUEST_TYPE_UNSPECIFIED":      0,
		"ELIGIBLE_TRANSACTION_REQUEST_TYPE_TOP_TRANSACTIONS": 1,
		"ELIGIBLE_TRANSACTION_REQUEST_TYPE_ALL_TRANSACTIONS": 2,
	}
)

func (x EligibleTransactionRequestType) Enum() *EligibleTransactionRequestType {
	p := new(EligibleTransactionRequestType)
	*p = x
	return p
}

func (x EligibleTransactionRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EligibleTransactionRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_lms_enums_enums_proto_enumTypes[4].Descriptor()
}

func (EligibleTransactionRequestType) Type() protoreflect.EnumType {
	return &file_api_firefly_lms_enums_enums_proto_enumTypes[4]
}

func (x EligibleTransactionRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EligibleTransactionRequestType.Descriptor instead.
func (EligibleTransactionRequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_lms_enums_enums_proto_rawDescGZIP(), []int{4}
}

type EmiCommsType int32

const (
	EmiCommsType_EMI_COMMS_TYPE_UNSPECIFIED EmiCommsType = 0
	EmiCommsType_EMI_COMMS_TYPE_CREATED     EmiCommsType = 1
	EmiCommsType_EMI_COMMS_TYPE_CLOSED      EmiCommsType = 2
	EmiCommsType_EMI_COMMS_TYPE_PRE_CLOSED  EmiCommsType = 3
	EmiCommsType_EMI_COMMS_TYPE_CANCELLED   EmiCommsType = 4
)

// Enum value maps for EmiCommsType.
var (
	EmiCommsType_name = map[int32]string{
		0: "EMI_COMMS_TYPE_UNSPECIFIED",
		1: "EMI_COMMS_TYPE_CREATED",
		2: "EMI_COMMS_TYPE_CLOSED",
		3: "EMI_COMMS_TYPE_PRE_CLOSED",
		4: "EMI_COMMS_TYPE_CANCELLED",
	}
	EmiCommsType_value = map[string]int32{
		"EMI_COMMS_TYPE_UNSPECIFIED": 0,
		"EMI_COMMS_TYPE_CREATED":     1,
		"EMI_COMMS_TYPE_CLOSED":      2,
		"EMI_COMMS_TYPE_PRE_CLOSED":  3,
		"EMI_COMMS_TYPE_CANCELLED":   4,
	}
)

func (x EmiCommsType) Enum() *EmiCommsType {
	p := new(EmiCommsType)
	*p = x
	return p
}

func (x EmiCommsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmiCommsType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_firefly_lms_enums_enums_proto_enumTypes[5].Descriptor()
}

func (EmiCommsType) Type() protoreflect.EnumType {
	return &file_api_firefly_lms_enums_enums_proto_enumTypes[5]
}

func (x EmiCommsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmiCommsType.Descriptor instead.
func (EmiCommsType) EnumDescriptor() ([]byte, []int) {
	return file_api_firefly_lms_enums_enums_proto_rawDescGZIP(), []int{5}
}

var File_api_firefly_lms_enums_enums_proto protoreflect.FileDescriptor

var file_api_firefly_lms_enums_enums_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x6c, 0x6d,
	0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x6c, 0x6d, 0x73,
	0x2a, 0xea, 0x01, 0x0a, 0x11, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c,
	0x4f, 0x53, 0x45, 0x44, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52,
	0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x2a, 0xcd, 0x01,
	0x0a, 0x16, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x54, 0x4f, 0x50, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x01, 0x12,
	0x2d, 0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x56, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x02, 0x12, 0x2d,
	0x0a, 0x29, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53,
	0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x03, 0x2a, 0xe8, 0x04,
	0x0a, 0x1e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x61,
	0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x12, 0x32, 0x0a, 0x2e, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12,
	0x2f, 0x0a, 0x2b, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02,
	0x12, 0x35, 0x0a, 0x31, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x37, 0x0a, 0x33, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x45,
	0x4e, 0x55, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x53, 0x10, 0x04,
	0x12, 0x3d, 0x0a, 0x39, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x05, 0x12,
	0x32, 0x0a, 0x2e, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x10, 0x06, 0x12, 0x34, 0x0a, 0x30, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45,
	0x53, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x07, 0x12, 0x3a, 0x0a, 0x36, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x45, 0x45, 0x5f, 0x49,
	0x4e, 0x46, 0x4f, 0x10, 0x08, 0x12, 0x2e, 0x0a, 0x2a, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x4d, 0x4d,
	0x41, 0x52, 0x59, 0x10, 0x09, 0x12, 0x31, 0x0a, 0x2d, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x2a, 0xb0, 0x05, 0x0a, 0x14, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02,
	0x12, 0x36, 0x0a, 0x32, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x53, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x49, 0x44, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x54, 0x45, 0x4e, 0x55, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x53,
	0x10, 0x05, 0x12, 0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x4d,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x06, 0x12, 0x29, 0x0a, 0x25, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x10, 0x07, 0x12, 0x24, 0x0a, 0x20, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x46, 0x45, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x08, 0x12, 0x2a, 0x0a, 0x26,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x09, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x0a, 0x12, 0x29, 0x0a, 0x25,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x43, 0x48,
	0x45, 0x44, 0x55, 0x4c, 0x45, 0x10, 0x0b, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x0c, 0x12, 0x2a, 0x0a, 0x26,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x45,
	0x44, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x0d, 0x12, 0x29, 0x0a, 0x25, 0x4c, 0x4f, 0x41, 0x4e,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x45, 0x4e, 0x44, 0x5f, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x0e, 0x12, 0x26, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0f, 0x2a, 0xc3, 0x01, 0x0a, 0x1e,
	0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31,
	0x0a, 0x2d, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x36, 0x0a, 0x32, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x50, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x36, 0x0a, 0x32, 0x45, 0x4c, 0x49,
	0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41,
	0x4c, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10,
	0x02, 0x2a, 0xa2, 0x01, 0x0a, 0x0c, 0x45, 0x6d, 0x69, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x4d, 0x49, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x4d, 0x49, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x19,
	0x0a, 0x15, 0x45, 0x4d, 0x49, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x4d, 0x49,
	0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x5f,
	0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4d, 0x49, 0x5f,
	0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45,
	0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x42, 0x50, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x6c, 0x6d, 0x73,
	0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2f, 0x6c, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_firefly_lms_enums_enums_proto_rawDescOnce sync.Once
	file_api_firefly_lms_enums_enums_proto_rawDescData = file_api_firefly_lms_enums_enums_proto_rawDesc
)

func file_api_firefly_lms_enums_enums_proto_rawDescGZIP() []byte {
	file_api_firefly_lms_enums_enums_proto_rawDescOnce.Do(func() {
		file_api_firefly_lms_enums_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_firefly_lms_enums_enums_proto_rawDescData)
	})
	return file_api_firefly_lms_enums_enums_proto_rawDescData
}

var file_api_firefly_lms_enums_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_firefly_lms_enums_enums_proto_goTypes = []interface{}{
	(LoanAccountStatus)(0),              // 0: firefly.lms.LoanAccountStatus
	(LoanAccountRequestType)(0),         // 1: firefly.lms.LoanAccountRequestType
	(TransactionLoanOffersFieldMask)(0), // 2: firefly.lms.TransactionLoanOffersFieldMask
	(LoanAccountFieldMask)(0),           // 3: firefly.lms.LoanAccountFieldMask
	(EligibleTransactionRequestType)(0), // 4: firefly.lms.EligibleTransactionRequestType
	(EmiCommsType)(0),                   // 5: firefly.lms.EmiCommsType
}
var file_api_firefly_lms_enums_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_firefly_lms_enums_enums_proto_init() }
func file_api_firefly_lms_enums_enums_proto_init() {
	if File_api_firefly_lms_enums_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_firefly_lms_enums_enums_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_firefly_lms_enums_enums_proto_goTypes,
		DependencyIndexes: file_api_firefly_lms_enums_enums_proto_depIdxs,
		EnumInfos:         file_api_firefly_lms_enums_enums_proto_enumTypes,
	}.Build()
	File_api_firefly_lms_enums_enums_proto = out.File
	file_api_firefly_lms_enums_enums_proto_rawDesc = nil
	file_api_firefly_lms_enums_enums_proto_goTypes = nil
	file_api_firefly_lms_enums_enums_proto_depIdxs = nil
}
