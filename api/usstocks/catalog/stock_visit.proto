//go:generate gen_sql -types=VisitedAction

syntax = "proto3";

package api.usstocks.catalog;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/catalog";
option java_package = "com.github.epifi.gamma.api.usstocks.catalog";

// StockVisit table contains all the user's stock visits.
// Index on last_viewed_at, stock_id, actor_id can help fetch all relevant actors eligible for notifications
message StockVisit {
  // uuid - unique identifier
  string id = 1;
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  string stock_id = 3 [(validate.rules).string.min_len = 1];
  VisitedAction visited_action = 4 [(validate.rules).enum = {not_in: [0]}];
  VisitedStock stock = 5;
  // created_at is used to track the first visit
  google.protobuf.Timestamp created_at = 6;
  // updated_at is used to track the subsequent visits/investments/watchlist
  google.protobuf.Timestamp updated_at = 7;
  // last_viewed_at is used to track the last visit to a stock, this will be used for latest activity
  // Given this is specific for us stocks, this date will be stored using Eastern time zone
  google.type.Date last_viewed_at = 8;
}


// VisitedStock is a wrapper over Stock to add additional fields specific to visited stock
message VisitedStock {
  double price = 1;
}

// Visited action will store the action in increasing order of priority
// If a stock is viewed, whitelisted and invested - we will store multiple rows (1 row per event) and
// update the time for every visit.
enum VisitedAction {
  VISITED_ACTION_UNSPECIFIED = 0;
  // stock only viewed by a user
  VISITED_ACTION_VIEW = 1;
  // stock added to watchlist by a user
  VISITED_ACTION_WATCHLIST = 2;
  // stock invested by a user
  VISITED_ACTION_INVEST = 3;
}

enum StockVisitFieldMask {
  STOCK_VISIT_FIELD_MASK_UNSPECIFIED = 0;
  STOCK_VISIT_FIELD_MASK_ID = 1;
  STOCK_VISIT_FIELD_MASK_ACTOR_ID = 2;
  STOCK_VISIT_FIELD_MASK_STOCK_ID = 3;
  STOCK_VISIT_FIELD_MASK_STOCK = 4;
  STOCK_VISIT_FIELD_MASK_VISITED_ACTION = 5;
  STOCK_VISIT_FIELD_MASK_CREATED_AT = 6;
  STOCK_VISIT_FIELD_MASK_UPDATED_AT = 7;
  STOCK_VISIT_FIELD_MASK_LAST_VIEWED_AT = 8;
}
