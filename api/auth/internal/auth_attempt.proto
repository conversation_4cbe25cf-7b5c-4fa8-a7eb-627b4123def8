syntax = "proto3";
package auth;

import "google/protobuf/timestamp.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/errors/error_view.proto";
import "api/typesv2/common/phone_number.proto";

option go_package = "github.com/epifi/gamma/api/auth";
option java_package = "com.github.epifi.gamma.api.auth";

message AuthAttemptData {
  string id = 1;
  // actor id of the user
  string actor_id = 2;
  // phone number of the user
  api.typesv2.common.PhoneNumber phone_number = 3;
  // grpc status code returned by addOauthAccount flow
  string status_code = 4;
  // status_message  is the debug/error message
  string status_message = 5;
  // error_code
  string error_code = 6;
  // error_message
  string error_message = 7;
  // next action to the user
  frontend.deeplink.Deeplink next_action = 8;
  // error view passed to the client in case of errors
  frontend.errors.ErrorView error_view = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
}
