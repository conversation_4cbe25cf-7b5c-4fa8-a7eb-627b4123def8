package paymentinstrument

import (
	"database/sql/driver"
	"fmt"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x Source) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *Source) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := Source_value[val]
	*x = Source(valInt)
	return nil
}
