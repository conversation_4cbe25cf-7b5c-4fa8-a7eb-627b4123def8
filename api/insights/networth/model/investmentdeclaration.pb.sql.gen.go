// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/insights/networth/model/investmentdeclaration.pb.go

package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"google.golang.org/protobuf/encoding/protojson"
)

// Valuer interface implementation for storing the Source in string format in DB
func (p Source) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Source while reading from DB
func (p *Source) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Source_value[val]
	if !ok {
		return fmt.Errorf("unexpected Source value: %s", val)
	}
	*p = Source(valInt)
	return nil
}

// Marshaler interface implementation for Source
func (x Source) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Source
func (x *Source) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Source(Source_value[val])
	return nil
}

// Scanner interface implementation for parsing OtherDeclarationDetails while reading from DB
func (a *OtherDeclarationDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the OtherDeclarationDetails in string format in DB
func (a *OtherDeclarationDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for OtherDeclarationDetails
func (a *OtherDeclarationDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for OtherDeclarationDetails
func (a *OtherDeclarationDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
