// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/insights/story/web_elements.proto

package story

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ValueCard with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ValueCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValueCard with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ValueCardMultiError, or nil
// if none found.
func (m *ValueCard) ValidateAll() error {
	return m.validate(true)
}

func (m *ValueCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValueCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValueCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValueCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValueCardValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValueCardValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValueCardValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValueCardValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValueCardValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValueCardValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValueCardValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValueCardValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValueCardValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValueCardValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValueCardValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValueCardValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValueCardMultiError(errors)
	}

	return nil
}

// ValueCardMultiError is an error wrapping multiple validation errors returned
// by ValueCard.ValidateAll() if the designated constraints aren't met.
type ValueCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValueCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValueCardMultiError) AllErrors() []error { return m }

// ValueCardValidationError is the validation error returned by
// ValueCard.Validate if the designated constraints aren't met.
type ValueCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValueCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValueCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValueCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValueCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValueCardValidationError) ErrorName() string { return "ValueCardValidationError" }

// Error satisfies the builtin error interface
func (e ValueCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValueCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValueCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValueCardValidationError{}

// Validate checks the field values on ImageCard with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImageCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageCard with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImageCardMultiError, or nil
// if none found.
func (m *ImageCard) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageCardValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageCardValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageCardValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Image

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageCardValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageCardValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageCardValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageCardValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageCardValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageCardValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DescriptionPreIcon

	if len(errors) > 0 {
		return ImageCardMultiError(errors)
	}

	return nil
}

// ImageCardMultiError is an error wrapping multiple validation errors returned
// by ImageCard.ValidateAll() if the designated constraints aren't met.
type ImageCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageCardMultiError) AllErrors() []error { return m }

// ImageCardValidationError is the validation error returned by
// ImageCard.Validate if the designated constraints aren't met.
type ImageCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageCardValidationError) ErrorName() string { return "ImageCardValidationError" }

// Error satisfies the builtin error interface
func (e ImageCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageCardValidationError{}

// Validate checks the field values on BottomSheetLineItemsView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BottomSheetLineItemsView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BottomSheetLineItemsView with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BottomSheetLineItemsViewMultiError, or nil if none found.
func (m *BottomSheetLineItemsView) ValidateAll() error {
	return m.validate(true)
}

func (m *BottomSheetLineItemsView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BottomSheetLineItemsViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BottomSheetLineItemsViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BottomSheetLineItemsViewValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrimaryText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BottomSheetLineItemsViewValidationError{
					field:  "PrimaryText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BottomSheetLineItemsViewValidationError{
					field:  "PrimaryText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrimaryText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BottomSheetLineItemsViewValidationError{
				field:  "PrimaryText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BottomSheetLineItemsViewValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BottomSheetLineItemsViewValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BottomSheetLineItemsViewValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BannerUrl

	// no validation rules for BannerActionId

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BottomSheetLineItemsViewValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BottomSheetLineItemsViewValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BottomSheetLineItemsViewValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BottomSheetLineItemsViewMultiError(errors)
	}

	return nil
}

// BottomSheetLineItemsViewMultiError is an error wrapping multiple validation
// errors returned by BottomSheetLineItemsView.ValidateAll() if the designated
// constraints aren't met.
type BottomSheetLineItemsViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BottomSheetLineItemsViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BottomSheetLineItemsViewMultiError) AllErrors() []error { return m }

// BottomSheetLineItemsViewValidationError is the validation error returned by
// BottomSheetLineItemsView.Validate if the designated constraints aren't met.
type BottomSheetLineItemsViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BottomSheetLineItemsViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BottomSheetLineItemsViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BottomSheetLineItemsViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BottomSheetLineItemsViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BottomSheetLineItemsViewValidationError) ErrorName() string {
	return "BottomSheetLineItemsViewValidationError"
}

// Error satisfies the builtin error interface
func (e BottomSheetLineItemsViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBottomSheetLineItemsView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BottomSheetLineItemsViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BottomSheetLineItemsViewValidationError{}

// Validate checks the field values on LineItemData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LineItemData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LineItemData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LineItemDataMultiError, or
// nil if none found.
func (m *LineItemData) ValidateAll() error {
	return m.validate(true)
}

func (m *LineItemData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Icon

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemDataValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemDataValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemDataValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemDataValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemDataValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemDataValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemDataValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemDataValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemDataValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LineItemDataMultiError(errors)
	}

	return nil
}

// LineItemDataMultiError is an error wrapping multiple validation errors
// returned by LineItemData.ValidateAll() if the designated constraints aren't met.
type LineItemDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LineItemDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LineItemDataMultiError) AllErrors() []error { return m }

// LineItemDataValidationError is the validation error returned by
// LineItemData.Validate if the designated constraints aren't met.
type LineItemDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LineItemDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LineItemDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LineItemDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LineItemDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LineItemDataValidationError) ErrorName() string { return "LineItemDataValidationError" }

// Error satisfies the builtin error interface
func (e LineItemDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLineItemData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LineItemDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LineItemDataValidationError{}

// Validate checks the field values on TextWithIconsElement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TextWithIconsElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TextWithIconsElement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TextWithIconsElementMultiError, or nil if none found.
func (m *TextWithIconsElement) ValidateAll() error {
	return m.validate(true)
}

func (m *TextWithIconsElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TextWithIconsElementValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TextWithIconsElementValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TextWithIconsElementValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PreIcon

	// no validation rules for PostIcon

	if len(errors) > 0 {
		return TextWithIconsElementMultiError(errors)
	}

	return nil
}

// TextWithIconsElementMultiError is an error wrapping multiple validation
// errors returned by TextWithIconsElement.ValidateAll() if the designated
// constraints aren't met.
type TextWithIconsElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TextWithIconsElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TextWithIconsElementMultiError) AllErrors() []error { return m }

// TextWithIconsElementValidationError is the validation error returned by
// TextWithIconsElement.Validate if the designated constraints aren't met.
type TextWithIconsElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TextWithIconsElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TextWithIconsElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TextWithIconsElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TextWithIconsElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TextWithIconsElementValidationError) ErrorName() string {
	return "TextWithIconsElementValidationError"
}

// Error satisfies the builtin error interface
func (e TextWithIconsElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTextWithIconsElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TextWithIconsElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TextWithIconsElementValidationError{}

// Validate checks the field values on IconsWithTextElement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IconsWithTextElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IconsWithTextElement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IconsWithTextElementMultiError, or nil if none found.
func (m *IconsWithTextElement) ValidateAll() error {
	return m.validate(true)
}

func (m *IconsWithTextElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPreText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IconsWithTextElementValidationError{
					field:  "PreText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IconsWithTextElementValidationError{
					field:  "PreText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IconsWithTextElementValidationError{
				field:  "PreText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IconsWithTextElementValidationError{
					field:  "PostText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IconsWithTextElementValidationError{
					field:  "PostText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IconsWithTextElementValidationError{
				field:  "PostText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IconsWithTextElementMultiError(errors)
	}

	return nil
}

// IconsWithTextElementMultiError is an error wrapping multiple validation
// errors returned by IconsWithTextElement.ValidateAll() if the designated
// constraints aren't met.
type IconsWithTextElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IconsWithTextElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IconsWithTextElementMultiError) AllErrors() []error { return m }

// IconsWithTextElementValidationError is the validation error returned by
// IconsWithTextElement.Validate if the designated constraints aren't met.
type IconsWithTextElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IconsWithTextElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IconsWithTextElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IconsWithTextElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IconsWithTextElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IconsWithTextElementValidationError) ErrorName() string {
	return "IconsWithTextElementValidationError"
}

// Error satisfies the builtin error interface
func (e IconsWithTextElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIconsWithTextElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IconsWithTextElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IconsWithTextElementValidationError{}

// Validate checks the field values on ImageCardList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImageCardList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageCardList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImageCardListMultiError, or
// nil if none found.
func (m *ImageCardList) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageCardList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetImageCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ImageCardListValidationError{
						field:  fmt.Sprintf("ImageCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ImageCardListValidationError{
						field:  fmt.Sprintf("ImageCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ImageCardListValidationError{
					field:  fmt.Sprintf("ImageCards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ImageCardListMultiError(errors)
	}

	return nil
}

// ImageCardListMultiError is an error wrapping multiple validation errors
// returned by ImageCardList.ValidateAll() if the designated constraints
// aren't met.
type ImageCardListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageCardListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageCardListMultiError) AllErrors() []error { return m }

// ImageCardListValidationError is the validation error returned by
// ImageCardList.Validate if the designated constraints aren't met.
type ImageCardListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageCardListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageCardListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageCardListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageCardListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageCardListValidationError) ErrorName() string { return "ImageCardListValidationError" }

// Error satisfies the builtin error interface
func (e ImageCardListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageCardList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageCardListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageCardListValidationError{}

// Validate checks the field values on BottomSheetCenterImageView with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BottomSheetCenterImageView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BottomSheetCenterImageView with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BottomSheetCenterImageViewMultiError, or nil if none found.
func (m *BottomSheetCenterImageView) ValidateAll() error {
	return m.validate(true)
}

func (m *BottomSheetCenterImageView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Image

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BottomSheetCenterImageViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BottomSheetCenterImageViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BottomSheetCenterImageViewValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BottomSheetCenterImageViewValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BottomSheetCenterImageViewValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BottomSheetCenterImageViewValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCtaButtonText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BottomSheetCenterImageViewValidationError{
					field:  "CtaButtonText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BottomSheetCenterImageViewValidationError{
					field:  "CtaButtonText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCtaButtonText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BottomSheetCenterImageViewValidationError{
				field:  "CtaButtonText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CtaButtonActionId

	if len(errors) > 0 {
		return BottomSheetCenterImageViewMultiError(errors)
	}

	return nil
}

// BottomSheetCenterImageViewMultiError is an error wrapping multiple
// validation errors returned by BottomSheetCenterImageView.ValidateAll() if
// the designated constraints aren't met.
type BottomSheetCenterImageViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BottomSheetCenterImageViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BottomSheetCenterImageViewMultiError) AllErrors() []error { return m }

// BottomSheetCenterImageViewValidationError is the validation error returned
// by BottomSheetCenterImageView.Validate if the designated constraints aren't met.
type BottomSheetCenterImageViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BottomSheetCenterImageViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BottomSheetCenterImageViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BottomSheetCenterImageViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BottomSheetCenterImageViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BottomSheetCenterImageViewValidationError) ErrorName() string {
	return "BottomSheetCenterImageViewValidationError"
}

// Error satisfies the builtin error interface
func (e BottomSheetCenterImageViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBottomSheetCenterImageView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BottomSheetCenterImageViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BottomSheetCenterImageViewValidationError{}
