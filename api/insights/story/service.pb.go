// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/insights/story/service.proto

package story

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	story1 "github.com/epifi/gamma/api/frontend/insights/story"
	model "github.com/epifi/gamma/api/insights/story/model"
	story "github.com/epifi/gamma/api/webfe/story"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetStoryGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId    string               `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	StoryGroup model.StoryGroupName `protobuf:"varint,2,opt,name=story_group,json=storyGroup,proto3,enum=insights.story.model.StoryGroupName" json:"story_group,omitempty"`
}

func (x *GetStoryGroupRequest) Reset() {
	*x = GetStoryGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStoryGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStoryGroupRequest) ProtoMessage() {}

func (x *GetStoryGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStoryGroupRequest.ProtoReflect.Descriptor instead.
func (*GetStoryGroupRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetStoryGroupRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetStoryGroupRequest) GetStoryGroup() model.StoryGroupName {
	if x != nil {
		return x.StoryGroup
	}
	return model.StoryGroupName(0)
}

type GetStoryGroupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            *rpc.Status     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	StoryGroupDetails *StoryGroupData `protobuf:"bytes,2,opt,name=story_group_details,json=storyGroupDetails,proto3" json:"story_group_details,omitempty"`
}

func (x *GetStoryGroupResponse) Reset() {
	*x = GetStoryGroupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStoryGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStoryGroupResponse) ProtoMessage() {}

func (x *GetStoryGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStoryGroupResponse.ProtoReflect.Descriptor instead.
func (*GetStoryGroupResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetStoryGroupResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetStoryGroupResponse) GetStoryGroupDetails() *StoryGroupData {
	if x != nil {
		return x.StoryGroupDetails
	}
	return nil
}

type StoryGroupData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoryGroupId   string                    `protobuf:"bytes,1,opt,name=story_group_id,json=storyGroupId,proto3" json:"story_group_id,omitempty"`
	DisplayName    string                    `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	StoryData      []*StoryData              `protobuf:"bytes,3,rep,name=story_data,json=storyData,proto3" json:"story_data,omitempty"`
	DisplayDetails *StoryGroupDisplayDetails `protobuf:"bytes,4,opt,name=display_details,json=displayDetails,proto3" json:"display_details,omitempty"`
	ShareDetails   *model.ShareDetails       `protobuf:"bytes,5,opt,name=share_details,json=shareDetails,proto3" json:"share_details,omitempty"`
}

func (x *StoryGroupData) Reset() {
	*x = StoryGroupData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoryGroupData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryGroupData) ProtoMessage() {}

func (x *StoryGroupData) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryGroupData.ProtoReflect.Descriptor instead.
func (*StoryGroupData) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{2}
}

func (x *StoryGroupData) GetStoryGroupId() string {
	if x != nil {
		return x.StoryGroupId
	}
	return ""
}

func (x *StoryGroupData) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *StoryGroupData) GetStoryData() []*StoryData {
	if x != nil {
		return x.StoryData
	}
	return nil
}

func (x *StoryGroupData) GetDisplayDetails() *StoryGroupDisplayDetails {
	if x != nil {
		return x.DisplayDetails
	}
	return nil
}

func (x *StoryGroupData) GetShareDetails() *model.ShareDetails {
	if x != nil {
		return x.ShareDetails
	}
	return nil
}

type StoryGroupDisplayDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *model.StoryGroupHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *StoryGroupDisplayDetails) Reset() {
	*x = StoryGroupDisplayDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoryGroupDisplayDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryGroupDisplayDetails) ProtoMessage() {}

func (x *StoryGroupDisplayDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryGroupDisplayDetails.ProtoReflect.Descriptor instead.
func (*StoryGroupDisplayDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{3}
}

func (x *StoryGroupDisplayDetails) GetHeader() *model.StoryGroupHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

type StoryData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoryId    string              `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	TemplateId story.StoryTemplate `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3,enum=webfe.story.StoryTemplate" json:"template_id,omitempty"`
	// values for variables present in story web template.
	Values map[string]string `protobuf:"bytes,4,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// identifier to action mapping needed by app
	AppActions map[string]*story1.StoryAppAction `protobuf:"bytes,5,rep,name=app_actions,json=appActions,proto3" json:"app_actions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *StoryData) Reset() {
	*x = StoryData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoryData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryData) ProtoMessage() {}

func (x *StoryData) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryData.ProtoReflect.Descriptor instead.
func (*StoryData) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{4}
}

func (x *StoryData) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *StoryData) GetTemplateId() story.StoryTemplate {
	if x != nil {
		return x.TemplateId
	}
	return story.StoryTemplate(0)
}

func (x *StoryData) GetValues() map[string]string {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *StoryData) GetAppActions() map[string]*story1.StoryAppAction {
	if x != nil {
		return x.AppActions
	}
	return nil
}

type RecordStoryGroupEngagementDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId      string           `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	StoryGroupId string           `protobuf:"bytes,2,opt,name=story_group_id,json=storyGroupId,proto3" json:"story_group_id,omitempty"`
	ViewStatus   model.ViewStatus `protobuf:"varint,3,opt,name=view_status,json=viewStatus,proto3,enum=insights.story.model.ViewStatus" json:"view_status,omitempty"`
}

func (x *RecordStoryGroupEngagementDetailsRequest) Reset() {
	*x = RecordStoryGroupEngagementDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordStoryGroupEngagementDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordStoryGroupEngagementDetailsRequest) ProtoMessage() {}

func (x *RecordStoryGroupEngagementDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordStoryGroupEngagementDetailsRequest.ProtoReflect.Descriptor instead.
func (*RecordStoryGroupEngagementDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{5}
}

func (x *RecordStoryGroupEngagementDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RecordStoryGroupEngagementDetailsRequest) GetStoryGroupId() string {
	if x != nil {
		return x.StoryGroupId
	}
	return ""
}

func (x *RecordStoryGroupEngagementDetailsRequest) GetViewStatus() model.ViewStatus {
	if x != nil {
		return x.ViewStatus
	}
	return model.ViewStatus(0)
}

type RecordStoryGroupEngagementDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RecordStoryGroupEngagementDetailsResponse) Reset() {
	*x = RecordStoryGroupEngagementDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordStoryGroupEngagementDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordStoryGroupEngagementDetailsResponse) ProtoMessage() {}

func (x *RecordStoryGroupEngagementDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordStoryGroupEngagementDetailsResponse.ProtoReflect.Descriptor instead.
func (*RecordStoryGroupEngagementDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{6}
}

func (x *RecordStoryGroupEngagementDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetViewStatusOfStoryGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId      string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	StoryGroupId string `protobuf:"bytes,2,opt,name=story_group_id,json=storyGroupId,proto3" json:"story_group_id,omitempty"`
}

func (x *GetViewStatusOfStoryGroupRequest) Reset() {
	*x = GetViewStatusOfStoryGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetViewStatusOfStoryGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetViewStatusOfStoryGroupRequest) ProtoMessage() {}

func (x *GetViewStatusOfStoryGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetViewStatusOfStoryGroupRequest.ProtoReflect.Descriptor instead.
func (*GetViewStatusOfStoryGroupRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetViewStatusOfStoryGroupRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetViewStatusOfStoryGroupRequest) GetStoryGroupId() string {
	if x != nil {
		return x.StoryGroupId
	}
	return ""
}

type GetViewStatusOfStoryGroupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ViewStatus model.ViewStatus `protobuf:"varint,2,opt,name=view_status,json=viewStatus,proto3,enum=insights.story.model.ViewStatus" json:"view_status,omitempty"`
}

func (x *GetViewStatusOfStoryGroupResponse) Reset() {
	*x = GetViewStatusOfStoryGroupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetViewStatusOfStoryGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetViewStatusOfStoryGroupResponse) ProtoMessage() {}

func (x *GetViewStatusOfStoryGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetViewStatusOfStoryGroupResponse.ProtoReflect.Descriptor instead.
func (*GetViewStatusOfStoryGroupResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetViewStatusOfStoryGroupResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetViewStatusOfStoryGroupResponse) GetViewStatus() model.ViewStatus {
	if x != nil {
		return x.ViewStatus
	}
	return model.ViewStatus(0)
}

type GetActiveStoryGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoryGroupName []model.StoryGroupName `protobuf:"varint,1,rep,packed,name=story_group_name,json=storyGroupName,proto3,enum=insights.story.model.StoryGroupName" json:"story_group_name,omitempty"`
}

func (x *GetActiveStoryGroupRequest) Reset() {
	*x = GetActiveStoryGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveStoryGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveStoryGroupRequest) ProtoMessage() {}

func (x *GetActiveStoryGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveStoryGroupRequest.ProtoReflect.Descriptor instead.
func (*GetActiveStoryGroupRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetActiveStoryGroupRequest) GetStoryGroupName() []model.StoryGroupName {
	if x != nil {
		return x.StoryGroupName
	}
	return nil
}

type GetActiveStoryGroupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	StoryGroups []*StoryGroupDetail `protobuf:"bytes,2,rep,name=story_groups,json=storyGroups,proto3" json:"story_groups,omitempty"`
}

func (x *GetActiveStoryGroupResponse) Reset() {
	*x = GetActiveStoryGroupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveStoryGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveStoryGroupResponse) ProtoMessage() {}

func (x *GetActiveStoryGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveStoryGroupResponse.ProtoReflect.Descriptor instead.
func (*GetActiveStoryGroupResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetActiveStoryGroupResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActiveStoryGroupResponse) GetStoryGroups() []*StoryGroupDetail {
	if x != nil {
		return x.StoryGroups
	}
	return nil
}

type StoryGroupDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoryGroupName model.StoryGroupName `protobuf:"varint,1,opt,name=story_group_name,json=storyGroupName,proto3,enum=insights.story.model.StoryGroupName" json:"story_group_name,omitempty"`
	StoryGroupId   string               `protobuf:"bytes,2,opt,name=story_group_id,json=storyGroupId,proto3" json:"story_group_id,omitempty"`
}

func (x *StoryGroupDetail) Reset() {
	*x = StoryGroupDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_story_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoryGroupDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryGroupDetail) ProtoMessage() {}

func (x *StoryGroupDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_story_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryGroupDetail.ProtoReflect.Descriptor instead.
func (*StoryGroupDetail) Descriptor() ([]byte, []int) {
	return file_api_insights_story_service_proto_rawDescGZIP(), []int{11}
}

func (x *StoryGroupDetail) GetStoryGroupName() model.StoryGroupName {
	if x != nil {
		return x.StoryGroupName
	}
	return model.StoryGroupName(0)
}

func (x *StoryGroupDetail) GetStoryGroupId() string {
	if x != nil {
		return x.StoryGroupId
	}
	return ""
}

var File_api_insights_story_service_proto protoreflect.FileDescriptor

var file_api_insights_story_service_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2f,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61,
	0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x24, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x78,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x45, 0x0a, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74,
	0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x8c, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4e, 0x0a, 0x13, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x11, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xaf, 0x02, 0x0a, 0x0e, 0x53, 0x74, 0x6f, 0x72,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x51, 0x0a,
	0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x47, 0x0a, 0x0d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x5a, 0x0a, 0x18, 0x53, 0x74, 0x6f,
	0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x6f,
	0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x91, 0x03, 0x0a, 0x09, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x3b,
	0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52,
	0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x53, 0x74, 0x6f,
	0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x4a, 0x0a, 0x0b, 0x61, 0x70,
	0x70, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x2e, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x70, 0x70, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x66, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e,
	0x53, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x70, 0x70, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xca, 0x01, 0x0a, 0x28, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0e, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0b, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x76, 0x69, 0x65, 0x77,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x50, 0x0a, 0x29, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x75, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4f, 0x66, 0x53, 0x74, 0x6f, 0x72, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x0e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22,
	0x8b, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x4f, 0x66, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x0b, 0x76, 0x69,
	0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0a, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6c, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x10, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x6f,
	0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0e, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x43, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x88, 0x01, 0x0a, 0x10, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4e, 0x0a, 0x10, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x6f, 0x72,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0e, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x32, 0xfb, 0x03, 0x0a, 0x05, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x5e, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x24, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x25, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x21, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x38, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x45, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4f, 0x66, 0x53, 0x74, 0x6f, 0x72, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x30, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x4f, 0x66, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4f, 0x66, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x12, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74,
	0x6f, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2b, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x56,
	0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5a, 0x29, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2f, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_insights_story_service_proto_rawDescOnce sync.Once
	file_api_insights_story_service_proto_rawDescData = file_api_insights_story_service_proto_rawDesc
)

func file_api_insights_story_service_proto_rawDescGZIP() []byte {
	file_api_insights_story_service_proto_rawDescOnce.Do(func() {
		file_api_insights_story_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_insights_story_service_proto_rawDescData)
	})
	return file_api_insights_story_service_proto_rawDescData
}

var file_api_insights_story_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_insights_story_service_proto_goTypes = []interface{}{
	(*GetStoryGroupRequest)(nil),                      // 0: insights.story.GetStoryGroupRequest
	(*GetStoryGroupResponse)(nil),                     // 1: insights.story.GetStoryGroupResponse
	(*StoryGroupData)(nil),                            // 2: insights.story.StoryGroupData
	(*StoryGroupDisplayDetails)(nil),                  // 3: insights.story.StoryGroupDisplayDetails
	(*StoryData)(nil),                                 // 4: insights.story.StoryData
	(*RecordStoryGroupEngagementDetailsRequest)(nil),  // 5: insights.story.RecordStoryGroupEngagementDetailsRequest
	(*RecordStoryGroupEngagementDetailsResponse)(nil), // 6: insights.story.RecordStoryGroupEngagementDetailsResponse
	(*GetViewStatusOfStoryGroupRequest)(nil),          // 7: insights.story.GetViewStatusOfStoryGroupRequest
	(*GetViewStatusOfStoryGroupResponse)(nil),         // 8: insights.story.GetViewStatusOfStoryGroupResponse
	(*GetActiveStoryGroupRequest)(nil),                // 9: insights.story.GetActiveStoryGroupRequest
	(*GetActiveStoryGroupResponse)(nil),               // 10: insights.story.GetActiveStoryGroupResponse
	(*StoryGroupDetail)(nil),                          // 11: insights.story.StoryGroupDetail
	nil,                                               // 12: insights.story.StoryData.ValuesEntry
	nil,                                               // 13: insights.story.StoryData.AppActionsEntry
	(model.StoryGroupName)(0),                         // 14: insights.story.model.StoryGroupName
	(*rpc.Status)(nil),                                // 15: rpc.Status
	(*model.ShareDetails)(nil),                        // 16: insights.story.model.ShareDetails
	(*model.StoryGroupHeader)(nil),                    // 17: insights.story.model.StoryGroupHeader
	(story.StoryTemplate)(0),                          // 18: webfe.story.StoryTemplate
	(model.ViewStatus)(0),                             // 19: insights.story.model.ViewStatus
	(*story1.StoryAppAction)(nil),                     // 20: frontend.insights.story.StoryAppAction
}
var file_api_insights_story_service_proto_depIdxs = []int32{
	14, // 0: insights.story.GetStoryGroupRequest.story_group:type_name -> insights.story.model.StoryGroupName
	15, // 1: insights.story.GetStoryGroupResponse.status:type_name -> rpc.Status
	2,  // 2: insights.story.GetStoryGroupResponse.story_group_details:type_name -> insights.story.StoryGroupData
	4,  // 3: insights.story.StoryGroupData.story_data:type_name -> insights.story.StoryData
	3,  // 4: insights.story.StoryGroupData.display_details:type_name -> insights.story.StoryGroupDisplayDetails
	16, // 5: insights.story.StoryGroupData.share_details:type_name -> insights.story.model.ShareDetails
	17, // 6: insights.story.StoryGroupDisplayDetails.header:type_name -> insights.story.model.StoryGroupHeader
	18, // 7: insights.story.StoryData.template_id:type_name -> webfe.story.StoryTemplate
	12, // 8: insights.story.StoryData.values:type_name -> insights.story.StoryData.ValuesEntry
	13, // 9: insights.story.StoryData.app_actions:type_name -> insights.story.StoryData.AppActionsEntry
	19, // 10: insights.story.RecordStoryGroupEngagementDetailsRequest.view_status:type_name -> insights.story.model.ViewStatus
	15, // 11: insights.story.RecordStoryGroupEngagementDetailsResponse.status:type_name -> rpc.Status
	15, // 12: insights.story.GetViewStatusOfStoryGroupResponse.status:type_name -> rpc.Status
	19, // 13: insights.story.GetViewStatusOfStoryGroupResponse.view_status:type_name -> insights.story.model.ViewStatus
	14, // 14: insights.story.GetActiveStoryGroupRequest.story_group_name:type_name -> insights.story.model.StoryGroupName
	15, // 15: insights.story.GetActiveStoryGroupResponse.status:type_name -> rpc.Status
	11, // 16: insights.story.GetActiveStoryGroupResponse.story_groups:type_name -> insights.story.StoryGroupDetail
	14, // 17: insights.story.StoryGroupDetail.story_group_name:type_name -> insights.story.model.StoryGroupName
	20, // 18: insights.story.StoryData.AppActionsEntry.value:type_name -> frontend.insights.story.StoryAppAction
	0,  // 19: insights.story.Story.GetStoryGroup:input_type -> insights.story.GetStoryGroupRequest
	5,  // 20: insights.story.Story.RecordStoryGroupEngagementDetails:input_type -> insights.story.RecordStoryGroupEngagementDetailsRequest
	7,  // 21: insights.story.Story.GetViewStatusOfStoryGroup:input_type -> insights.story.GetViewStatusOfStoryGroupRequest
	9,  // 22: insights.story.Story.GetActiveStoryGroup:input_type -> insights.story.GetActiveStoryGroupRequest
	1,  // 23: insights.story.Story.GetStoryGroup:output_type -> insights.story.GetStoryGroupResponse
	6,  // 24: insights.story.Story.RecordStoryGroupEngagementDetails:output_type -> insights.story.RecordStoryGroupEngagementDetailsResponse
	8,  // 25: insights.story.Story.GetViewStatusOfStoryGroup:output_type -> insights.story.GetViewStatusOfStoryGroupResponse
	10, // 26: insights.story.Story.GetActiveStoryGroup:output_type -> insights.story.GetActiveStoryGroupResponse
	23, // [23:27] is the sub-list for method output_type
	19, // [19:23] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_api_insights_story_service_proto_init() }
func file_api_insights_story_service_proto_init() {
	if File_api_insights_story_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_insights_story_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStoryGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStoryGroupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoryGroupData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoryGroupDisplayDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoryData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordStoryGroupEngagementDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordStoryGroupEngagementDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetViewStatusOfStoryGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetViewStatusOfStoryGroupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveStoryGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveStoryGroupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_story_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoryGroupDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_insights_story_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_insights_story_service_proto_goTypes,
		DependencyIndexes: file_api_insights_story_service_proto_depIdxs,
		MessageInfos:      file_api_insights_story_service_proto_msgTypes,
	}.Build()
	File_api_insights_story_service_proto = out.File
	file_api_insights_story_service_proto_rawDesc = nil
	file_api_insights_story_service_proto_goTypes = nil
	file_api_insights_story_service_proto_depIdxs = nil
}
