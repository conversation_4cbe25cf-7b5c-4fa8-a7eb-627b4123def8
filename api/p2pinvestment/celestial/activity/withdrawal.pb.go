// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/p2pinvestment/celestial/activity/withdrawal.proto

package activity

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// activity for creating withdrawal transaction at vendor
// returns transient failure if transaction creation at vendor fails due to unknown reasons
// part of P2P_INVESTMENT_WITHDRAWAL_CREATION_AT_VENDOR celestial stage
type CreateWithdrawalAtVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// primary id of the withdrawal transaction
	TxnId string `protobuf:"bytes,2,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
}

func (x *CreateWithdrawalAtVendorRequest) Reset() {
	*x = CreateWithdrawalAtVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWithdrawalAtVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWithdrawalAtVendorRequest) ProtoMessage() {}

func (x *CreateWithdrawalAtVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWithdrawalAtVendorRequest.ProtoReflect.Descriptor instead.
func (*CreateWithdrawalAtVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescGZIP(), []int{0}
}

func (x *CreateWithdrawalAtVendorRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CreateWithdrawalAtVendorRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

type CreateWithdrawalAtVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *CreateWithdrawalAtVendorResponse) Reset() {
	*x = CreateWithdrawalAtVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWithdrawalAtVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWithdrawalAtVendorResponse) ProtoMessage() {}

func (x *CreateWithdrawalAtVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWithdrawalAtVendorResponse.ProtoReflect.Descriptor instead.
func (*CreateWithdrawalAtVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescGZIP(), []int{1}
}

func (x *CreateWithdrawalAtVendorResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// activity for polling the status of withdrawal with vendor and marking the txn in our DB as success/failure
// returns permanent failure if vendor transaction creation fails due to known reasons
// part of P2P_INVESTMENT_WITHDRAWAL_SETTLEMENT celestial stage
type SettleWithdrawalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// primary id of the withdrawal transaction
	TxnId string `protobuf:"bytes,2,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
}

func (x *SettleWithdrawalRequest) Reset() {
	*x = SettleWithdrawalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettleWithdrawalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettleWithdrawalRequest) ProtoMessage() {}

func (x *SettleWithdrawalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettleWithdrawalRequest.ProtoReflect.Descriptor instead.
func (*SettleWithdrawalRequest) Descriptor() ([]byte, []int) {
	return file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescGZIP(), []int{2}
}

func (x *SettleWithdrawalRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *SettleWithdrawalRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

type SettleWithdrawalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *SettleWithdrawalResponse) Reset() {
	*x = SettleWithdrawalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettleWithdrawalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettleWithdrawalResponse) ProtoMessage() {}

func (x *SettleWithdrawalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettleWithdrawalResponse.ProtoReflect.Descriptor instead.
func (*SettleWithdrawalResponse) Descriptor() ([]byte, []int) {
	return file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescGZIP(), []int{3}
}

func (x *SettleWithdrawalResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// activity for verifying credit status. Credit status will be updated while parsing credit notification
// part of P2P_INVESTMENT_WITHDRAWAL_CREDIT_VERIFICATION celestial stage
type VerifyCreditForWithdrawalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// primary id of the withdrawal transaction
	TxnId string `protobuf:"bytes,2,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
}

func (x *VerifyCreditForWithdrawalRequest) Reset() {
	*x = VerifyCreditForWithdrawalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyCreditForWithdrawalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyCreditForWithdrawalRequest) ProtoMessage() {}

func (x *VerifyCreditForWithdrawalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyCreditForWithdrawalRequest.ProtoReflect.Descriptor instead.
func (*VerifyCreditForWithdrawalRequest) Descriptor() ([]byte, []int) {
	return file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescGZIP(), []int{4}
}

func (x *VerifyCreditForWithdrawalRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *VerifyCreditForWithdrawalRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

type VerifyCreditForWithdrawalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *VerifyCreditForWithdrawalResponse) Reset() {
	*x = VerifyCreditForWithdrawalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyCreditForWithdrawalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyCreditForWithdrawalResponse) ProtoMessage() {}

func (x *VerifyCreditForWithdrawalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyCreditForWithdrawalResponse.ProtoReflect.Descriptor instead.
func (*VerifyCreditForWithdrawalResponse) Descriptor() ([]byte, []int) {
	return file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescGZIP(), []int{5}
}

func (x *VerifyCreditForWithdrawalResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_p2pinvestment_celestial_activity_withdrawal_proto protoreflect.FileDescriptor

var file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x32, 0x70, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x70, 0x32, 0x70, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x82,
	0x01, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61,
	0x77, 0x61, 0x6c, 0x41, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06,
	0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78,
	0x6e, 0x49, 0x64, 0x22, 0x6f, 0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x69, 0x74,
	0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x41, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x22, 0x7a, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x57, 0x69,
	0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64,
	0x22, 0x67, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72,
	0x61, 0x77, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x83, 0x01, 0x0a, 0x20, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x57, 0x69, 0x74,
	0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x22,
	0x70, 0x0a, 0x21, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46,
	0x6f, 0x72, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x42, 0x7a, 0x0a, 0x3b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x32, 0x70, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x5a, 0x3b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x32, 0x70,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescOnce sync.Once
	file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescData = file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDesc
)

func file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescGZIP() []byte {
	file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescOnce.Do(func() {
		file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescData)
	})
	return file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDescData
}

var file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_p2pinvestment_celestial_activity_withdrawal_proto_goTypes = []interface{}{
	(*CreateWithdrawalAtVendorRequest)(nil),   // 0: p2pinvestment.celestial.activity.CreateWithdrawalAtVendorRequest
	(*CreateWithdrawalAtVendorResponse)(nil),  // 1: p2pinvestment.celestial.activity.CreateWithdrawalAtVendorResponse
	(*SettleWithdrawalRequest)(nil),           // 2: p2pinvestment.celestial.activity.SettleWithdrawalRequest
	(*SettleWithdrawalResponse)(nil),          // 3: p2pinvestment.celestial.activity.SettleWithdrawalResponse
	(*VerifyCreditForWithdrawalRequest)(nil),  // 4: p2pinvestment.celestial.activity.VerifyCreditForWithdrawalRequest
	(*VerifyCreditForWithdrawalResponse)(nil), // 5: p2pinvestment.celestial.activity.VerifyCreditForWithdrawalResponse
	(*activity.RequestHeader)(nil),            // 6: celestial.activity.RequestHeader
	(*activity.ResponseHeader)(nil),           // 7: celestial.activity.ResponseHeader
}
var file_api_p2pinvestment_celestial_activity_withdrawal_proto_depIdxs = []int32{
	6, // 0: p2pinvestment.celestial.activity.CreateWithdrawalAtVendorRequest.request_header:type_name -> celestial.activity.RequestHeader
	7, // 1: p2pinvestment.celestial.activity.CreateWithdrawalAtVendorResponse.response_header:type_name -> celestial.activity.ResponseHeader
	6, // 2: p2pinvestment.celestial.activity.SettleWithdrawalRequest.request_header:type_name -> celestial.activity.RequestHeader
	7, // 3: p2pinvestment.celestial.activity.SettleWithdrawalResponse.response_header:type_name -> celestial.activity.ResponseHeader
	6, // 4: p2pinvestment.celestial.activity.VerifyCreditForWithdrawalRequest.request_header:type_name -> celestial.activity.RequestHeader
	7, // 5: p2pinvestment.celestial.activity.VerifyCreditForWithdrawalResponse.response_header:type_name -> celestial.activity.ResponseHeader
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_p2pinvestment_celestial_activity_withdrawal_proto_init() }
func file_api_p2pinvestment_celestial_activity_withdrawal_proto_init() {
	if File_api_p2pinvestment_celestial_activity_withdrawal_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWithdrawalAtVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWithdrawalAtVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettleWithdrawalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettleWithdrawalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyCreditForWithdrawalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyCreditForWithdrawalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_p2pinvestment_celestial_activity_withdrawal_proto_goTypes,
		DependencyIndexes: file_api_p2pinvestment_celestial_activity_withdrawal_proto_depIdxs,
		MessageInfos:      file_api_p2pinvestment_celestial_activity_withdrawal_proto_msgTypes,
	}.Build()
	File_api_p2pinvestment_celestial_activity_withdrawal_proto = out.File
	file_api_p2pinvestment_celestial_activity_withdrawal_proto_rawDesc = nil
	file_api_p2pinvestment_celestial_activity_withdrawal_proto_goTypes = nil
	file_api_p2pinvestment_celestial_activity_withdrawal_proto_depIdxs = nil
}
