package recon

import (
	"database/sql/driver"
	"fmt"
)

// Valuer interface implementation for storing the data in string format in DB
func (t ReconStatus) Value() (driver.Value, error) {
	return t.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (t *ReconStatus) Scan(src interface{}) error {

	val, ok := src.(string)
	if !ok {
		err := fmt.Errorf("expected string got %T", src)
		return err
	}

	valInt, ok := ReconStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected ReconStatus value: %s", val)
	}

	*t = ReconStatus(valInt)
	return nil
}
