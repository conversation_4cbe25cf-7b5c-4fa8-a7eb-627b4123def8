syntax = "proto3";

package order.domain;

import "api/typesv2/common/ownership.proto";
import "api/vendorgateway/request_source.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/order/domain";
option java_package = "com.github.epifi.gamma.api.order.domain";

// Represents status of domain RPC call initiated by order service for processing
// The order orchestrator takes certain actions based on this status
enum DomainProcessingStatus {
  STATUS_UNSPECIFIED = 0;

  // Domain service business logic processing is in progress.
  // In case all the retries are exhausted for the domain processing and domain service
  // returns IN_PROGRESS, the corresponding order moves to the MANUAL_INTERVENTION state.
  // Domain service can return IN_PROGRESS to the OMS under various circumstances but not limited to:
  // * The domain has initiated a request with vendor and is waiting for the request to move to a terminal state.
  // * Domain service receives a network error after request initiation or status check for the request and the current
  //   status of the request is not known.
  IN_PROGRESS = 1;

  // Domain service successfully processed the business logic
  SUCCESS = 2;

  // For transient failures which can be retried from the OMS
  // In case all the retries are exhausted for the domain processing and domain service
  // returns TRANSIENT_FAILURE, the corresponding order moves to FAILURE state depending on the current stage of the order.
  //  Domain service can return TRANSIENT_FAILURE to the OMS under various circumstances but not limited to:
  //  * domain gets any network error before initiating the request processing at vendor's end
  //  * domain gets a failure from the vendor post request initiation and this error can be mitigated after retrying another
  //    request initiation.
  TRANSIENT_FAILURE = 3;

  // For permanent failures which can't be retried
  // e.g. errors like record not found etc or certain vendor status codes mapped to permanent failure come under this.
  PERMANENT_FAILURE = 4;

  // Domain service processing didn't proceed ahead
  // In this case order orchestrator shouldn't update any state
  // It should simple retry
  NO_OP = 5;
}

// A set of all the common attributes to be contained in a domain RPC request
message DomainRequestHeader {
  // Unique id per order maintained at order's end
  // Domain checks if the processing has been done at its end for this client_request_id
  // Note: In payment leg this field is populated with order-id
  // TODO(vivek/nitesh): Fix this in order orchestrator to pass client-request-id in this field
  string client_request_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // flag indicating to domain if current attempt is the last attempt from the order orchestrator
  // Based on this flag domain can build logic to take action on last attempt. e.g. sending alerts, etc.
  bool is_last_attempt = 2;

  // flag indicating to domain if it should force process order even after last attempt or manual intervention
  // state. This will be set in case in case order attempt is triggered MANUALLY from sherlock action.
  bool should_force_process_order = 3;

  // flag indicating to domain if it should override the terminal state based on the enquiry response from vendor.
  // This is typically set in an extremely rare scenario and triggered MANUALLY from sherlock action.
  // e.g. there is a discrepancy in the request terminal status between vendor and epiFi.
  bool should_override_terminal_state = 4;

  // attempt number for processing the domain request from the order orchestrator
  // Based on this domain can build-best effort logics such as suppressing an error
  int32 attempt_number = 5;
  // ownership under which the domain request has to be processed . This will be used to
  // identify the DB to which the queries have to be done
  api.typesv2.common.Ownership entity_ownership = 6;

  // Request source indicating the source or flow of the request. E.g. LoansFlow.
  // Currently used to segregate the vendor (Federal currently) api credentials based on the request source if applicable for this Order.
  // If not provided, the default request source will be used which corresponds to the default credentials.
  api.vendorgateway.RequestSource request_source = 7;
}

// A set of all the common attributes to be contained in a domain RPC response
message DomainResponseHeader {
  DomainProcessingStatus status = 1;
}
