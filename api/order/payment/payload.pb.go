// protolint:disable MAX_LINE_LENGTH
// This file defines different messages that are passed along with order creation request as order payload.
// The order central retry orchestrator then sends the details to the payment service as it is for processing

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/order/payment/payload.proto

package payment

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Contains all the meta data required to process B2C fund transfer
type B2CFundTransfer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment instrument from which asset need to be debited
	PiFrom string `protobuf:"bytes,1,opt,name=pi_from,json=piFrom,proto3" json:"pi_from,omitempty"`
	// payment instrument to which asset needs to be credited
	PiTo string `protobuf:"bytes,2,opt,name=pi_to,json=piTo,proto3" json:"pi_to,omitempty"`
	// amount of money that needs to be transferred
	Amount *money.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// user remarks / auto generated remarks
	Remarks string `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// client's preferred payment protocol for the transaction.
	// It might get over written by payment service based on various factors,
	// but not limited to-
	// 1. health of the partner bank
	// 2. success rate for given transactions with given protocol
	PreferredProtocol PaymentProtocol `protobuf:"varint,6,opt,name=preferred_protocol,json=preferredProtocol,proto3,enum=order.payment.PaymentProtocol" json:"preferred_protocol,omitempty"`
	// partner bank through whom money needs to be transferred
	Partner vendorgateway.Vendor `protobuf:"varint,7,opt,name=partner,proto3,enum=vendorgateway.Vendor" json:"partner,omitempty"`
	// in case of imps penny drop, we might not have the user's account available with us.
	// we need to override the savings account fetch for such cases in b2c workflow
	OverrideSavingsAccountCheck bool `protobuf:"varint,8,opt,name=override_savings_account_check,json=overrideSavingsAccountCheck,proto3" json:"override_savings_account_check,omitempty"`
	// [OPTIONAL] needed for cases such as penny drop where we need to validate the given name
	// with the account holder name. This will be empty in cases where the beneficiary identity
	// is not in question and we are certain that the user is legit
	BeneficiaryName string `protobuf:"bytes,9,opt,name=beneficiary_name,json=beneficiaryName,proto3" json:"beneficiary_name,omitempty"`
}

func (x *B2CFundTransfer) Reset() {
	*x = B2CFundTransfer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_payment_payload_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *B2CFundTransfer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*B2CFundTransfer) ProtoMessage() {}

func (x *B2CFundTransfer) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_payment_payload_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use B2CFundTransfer.ProtoReflect.Descriptor instead.
func (*B2CFundTransfer) Descriptor() ([]byte, []int) {
	return file_api_order_payment_payload_proto_rawDescGZIP(), []int{0}
}

func (x *B2CFundTransfer) GetPiFrom() string {
	if x != nil {
		return x.PiFrom
	}
	return ""
}

func (x *B2CFundTransfer) GetPiTo() string {
	if x != nil {
		return x.PiTo
	}
	return ""
}

func (x *B2CFundTransfer) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *B2CFundTransfer) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *B2CFundTransfer) GetPreferredProtocol() PaymentProtocol {
	if x != nil {
		return x.PreferredProtocol
	}
	return PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED
}

func (x *B2CFundTransfer) GetPartner() vendorgateway.Vendor {
	if x != nil {
		return x.Partner
	}
	return vendorgateway.Vendor(0)
}

func (x *B2CFundTransfer) GetOverrideSavingsAccountCheck() bool {
	if x != nil {
		return x.OverrideSavingsAccountCheck
	}
	return false
}

func (x *B2CFundTransfer) GetBeneficiaryName() string {
	if x != nil {
		return x.BeneficiaryName
	}
	return ""
}

// Contains all the meta data required to process a add funds collect order
type AddFundsCollect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PaymentDetails *PaymentDetails `protobuf:"bytes,1,opt,name=payment_details,json=paymentDetails,proto3" json:"payment_details,omitempty"`
}

func (x *AddFundsCollect) Reset() {
	*x = AddFundsCollect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_payment_payload_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddFundsCollect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFundsCollect) ProtoMessage() {}

func (x *AddFundsCollect) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_payment_payload_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFundsCollect.ProtoReflect.Descriptor instead.
func (*AddFundsCollect) Descriptor() ([]byte, []int) {
	return file_api_order_payment_payload_proto_rawDescGZIP(), []int{1}
}

func (x *AddFundsCollect) GetPaymentDetails() *PaymentDetails {
	if x != nil {
		return x.PaymentDetails
	}
	return nil
}

// PaymentDetails contains all the data needed to initiate a payment with the bank.
type PaymentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment instrument from which transaction should result in a debit.
	PiFrom string `protobuf:"bytes,1,opt,name=pi_from,json=piFrom,proto3" json:"pi_from,omitempty"`
	// payment instrument to which transaction should result in a credit.
	PiTo string `protobuf:"bytes,2,opt,name=pi_to,json=piTo,proto3" json:"pi_to,omitempty"`
	// amount of money that needs to be transferred
	Amount *money.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// user remarks / auto generated remarks for transaction(s) belonging to the order
	Remarks string `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// an optional field to be used in certain workflow where transaction req id needs to be generated by external merchant
	// system e.g. dynamic QR scan and intent based payment
	//
	// This req id is passed to all the external systems to uniquely identify an system transaction.
	// For UPI based payments req_id is passed as `txnId` to NPCI via partner bank.
	// while for non-UPI payments req_id is passed as `transaction request id` to partner banks.
	ReqId string `protobuf:"bytes,5,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// payment protocol chosen by decision engine for a particular transaction.
	PaymentProtocol PaymentProtocol `protobuf:"varint,6,opt,name=payment_protocol,json=paymentProtocol,proto3,enum=order.payment.PaymentProtocol" json:"payment_protocol,omitempty"`
	// an optional field to be used in certain workflow where merchant ref id needs to be generated by external merchant
	// merchant ref id passed to associate a transaction with merchant's order system.
	//
	// In certain flows, e.g UPI dynamic QR scans or UPI intent based payments,
	// a merchant_ref_id can be present in the QR or the intent and it is mandatory
	// for epiFi to consume this information while initiating the transaction as per NPCI guidelines to PSP.
	//
	// Typically, this could be order number, subscription number, Bill ID,
	// booking ID, insurance, renewal reference, etc. from the merchant's system.
	// merchant_ref_id is only valid for UPI payments
	MerchantRefId string `protobuf:"bytes,7,opt,name=merchant_ref_id,json=merchantRefId,proto3" json:"merchant_ref_id,omitempty"`
	// transaction reference url should be a URL when clicked provides customer with further transaction details
	// like complete bill details, bill copy, order copy, ticket details, etc.
	//
	// for dynamic QR and intent based payments.. merchant system can send this information
	// in other cases we will be using a default string which may redirect to epifi
	ReferenceUrl string `protobuf:"bytes,8,opt,name=reference_url,json=referenceUrl,proto3" json:"reference_url,omitempty"`
	// initiation mode for the payment
	// 00=Default txn
	// 01=QR Code
	// 02=Secure QR Code
	// 04=Intent
	// 05=Secure Intent
	// 06=NFC
	// 07=BLE (Bluetooth)
	// 08=UHF(Ultra High
	InitiationMode string `protobuf:"bytes,9,opt,name=initiation_mode,json=initiationMode,proto3" json:"initiation_mode,omitempty"`
	// purpose of the transaction
	// 00 - Default
	// 01 - SEBI
	// 02 - AMC
	// 03 - Travel
	// 04 - Hospitality
	// 05 – Hospital
	// 06 – Telecom
	// 07 – Insurance
	// 08 – Education
	// 09- Gifting
	// 10-Others
	Purpose string `protobuf:"bytes,10,opt,name=purpose,proto3" json:"purpose,omitempty"`
	// merchant code
	// for QR and intent based payments.. merchant system can send this information
	// in other cases we will be using the default code -  "0000"
	Mcc string `protobuf:"bytes,11,opt,name=mcc,proto3" json:"mcc,omitempty"`
	// merchant id
	// for QR and intent based payments.. merchant system can send this information
	// for all other cases this field will be empty
	MerchantId string `protobuf:"bytes,12,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	// store id of the merchant
	// for QR and intent based payments.. merchant system can send this information
	// for all other cases this field will be empty
	MerchantStoreId string `protobuf:"bytes,13,opt,name=merchant_store_id,json=merchantStoreId,proto3" json:"merchant_store_id,omitempty"`
	// terminal id of the merchant
	// for QR and intent based payments.. merchant system can send this information
	// for all other cases this field will be empty
	MerchantTerminalId string `protobuf:"bytes,14,opt,name=merchant_terminal_id,json=merchantTerminalId,proto3" json:"merchant_terminal_id,omitempty"`
	// If the transaction is initiated by any PSP app then the respective orgID needs to be passed.
	// for merchant initiated intent ‘000000’ will be used.
	// for non QR and non intent based payments epifi org id will be used
	OrgId string `protobuf:"bytes,15,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	// genre of the merchant eg. offline, online
	// will be populated only in case a merchant is involved in the txn
	MerchantGenre string `protobuf:"bytes,16,opt,name=merchant_genre,json=merchantGenre,proto3" json:"merchant_genre,omitempty"`
}

func (x *PaymentDetails) Reset() {
	*x = PaymentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_payment_payload_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentDetails) ProtoMessage() {}

func (x *PaymentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_payment_payload_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentDetails.ProtoReflect.Descriptor instead.
func (*PaymentDetails) Descriptor() ([]byte, []int) {
	return file_api_order_payment_payload_proto_rawDescGZIP(), []int{2}
}

func (x *PaymentDetails) GetPiFrom() string {
	if x != nil {
		return x.PiFrom
	}
	return ""
}

func (x *PaymentDetails) GetPiTo() string {
	if x != nil {
		return x.PiTo
	}
	return ""
}

func (x *PaymentDetails) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PaymentDetails) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *PaymentDetails) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *PaymentDetails) GetPaymentProtocol() PaymentProtocol {
	if x != nil {
		return x.PaymentProtocol
	}
	return PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED
}

func (x *PaymentDetails) GetMerchantRefId() string {
	if x != nil {
		return x.MerchantRefId
	}
	return ""
}

func (x *PaymentDetails) GetReferenceUrl() string {
	if x != nil {
		return x.ReferenceUrl
	}
	return ""
}

func (x *PaymentDetails) GetInitiationMode() string {
	if x != nil {
		return x.InitiationMode
	}
	return ""
}

func (x *PaymentDetails) GetPurpose() string {
	if x != nil {
		return x.Purpose
	}
	return ""
}

func (x *PaymentDetails) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *PaymentDetails) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *PaymentDetails) GetMerchantStoreId() string {
	if x != nil {
		return x.MerchantStoreId
	}
	return ""
}

func (x *PaymentDetails) GetMerchantTerminalId() string {
	if x != nil {
		return x.MerchantTerminalId
	}
	return ""
}

func (x *PaymentDetails) GetOrgId() string {
	if x != nil {
		return x.OrgId
	}
	return ""
}

func (x *PaymentDetails) GetMerchantGenre() string {
	if x != nil {
		return x.MerchantGenre
	}
	return ""
}

var File_api_order_payment_payload_proto protoreflect.FileDescriptor

var file_api_order_payment_payload_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf5, 0x02, 0x0a,
	0x0f, 0x42, 0x32, 0x43, 0x46, 0x75, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x70, 0x69, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x70, 0x69, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x13, 0x0a, 0x05, 0x70, 0x69, 0x5f,
	0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x69, 0x54, 0x6f, 0x12, 0x2a,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x73, 0x12, 0x4d, 0x0a, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1e, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x52, 0x11, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x12, 0x2f, 0x0a, 0x07, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x07, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x1e, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x5f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x59, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x46, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22,
	0xeb, 0x04, 0x0a, 0x0e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x70, 0x69, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x70, 0x69,
	0x46, 0x72, 0x6f, 0x6d, 0x12, 0x1c, 0x0a, 0x05, 0x70, 0x69, 0x5f, 0x74, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x70, 0x69,
	0x54, 0x6f, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x10, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x26,
	0x0a, 0x0f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x63, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x63, 0x63,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a,
	0x14, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x67, 0x65, 0x6e, 0x72, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x72, 0x65, 0x42, 0x54, 0x0a,
	0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_order_payment_payload_proto_rawDescOnce sync.Once
	file_api_order_payment_payload_proto_rawDescData = file_api_order_payment_payload_proto_rawDesc
)

func file_api_order_payment_payload_proto_rawDescGZIP() []byte {
	file_api_order_payment_payload_proto_rawDescOnce.Do(func() {
		file_api_order_payment_payload_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_order_payment_payload_proto_rawDescData)
	})
	return file_api_order_payment_payload_proto_rawDescData
}

var file_api_order_payment_payload_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_order_payment_payload_proto_goTypes = []interface{}{
	(*B2CFundTransfer)(nil),   // 0: order.payment.B2CFundTransfer
	(*AddFundsCollect)(nil),   // 1: order.payment.AddFundsCollect
	(*PaymentDetails)(nil),    // 2: order.payment.PaymentDetails
	(*money.Money)(nil),       // 3: google.type.Money
	(PaymentProtocol)(0),      // 4: order.payment.PaymentProtocol
	(vendorgateway.Vendor)(0), // 5: vendorgateway.Vendor
}
var file_api_order_payment_payload_proto_depIdxs = []int32{
	3, // 0: order.payment.B2CFundTransfer.amount:type_name -> google.type.Money
	4, // 1: order.payment.B2CFundTransfer.preferred_protocol:type_name -> order.payment.PaymentProtocol
	5, // 2: order.payment.B2CFundTransfer.partner:type_name -> vendorgateway.Vendor
	2, // 3: order.payment.AddFundsCollect.payment_details:type_name -> order.payment.PaymentDetails
	3, // 4: order.payment.PaymentDetails.amount:type_name -> google.type.Money
	4, // 5: order.payment.PaymentDetails.payment_protocol:type_name -> order.payment.PaymentProtocol
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_order_payment_payload_proto_init() }
func file_api_order_payment_payload_proto_init() {
	if File_api_order_payment_payload_proto != nil {
		return
	}
	file_api_order_payment_payment_protocol_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_order_payment_payload_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*B2CFundTransfer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_payment_payload_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddFundsCollect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_payment_payload_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_order_payment_payload_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_order_payment_payload_proto_goTypes,
		DependencyIndexes: file_api_order_payment_payload_proto_depIdxs,
		MessageInfos:      file_api_order_payment_payload_proto_msgTypes,
	}.Build()
	File_api_order_payment_payload_proto = out.File
	file_api_order_payment_payload_proto_rawDesc = nil
	file_api_order_payment_payload_proto_goTypes = nil
	file_api_order_payment_payload_proto_depIdxs = nil
}
