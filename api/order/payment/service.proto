// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package order.payment;

import "api/order/payment/accounting_entry_type.proto";
import "api/order/payment/payment_protocol.proto";
import "api/order/payment/transaction.proto";
import "api/order/domain/request.proto";
import "api/accounts/account_type.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/common/phone_number.proto";
import "api/upi/cred_block.proto";
import "api/upi/device.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/order/payment";
option java_package = "com.github.epifi.gamma.api.order.payment";

// Defines the GRPC service to process a transaction in the system.
// NOTE: This service is supposed to own transaction entity.
service Payment {

  // CreateTransaction creates a new transaction entry in the system.
  rpc CreateTransaction (CreateTransactionRequest) returns (CreateTransactionResponse) {}

  // InitiateTransaction is an async API, which initiates a transaction in the system.
  // It makes the vendor call to make payment and schedule an event to fetch transaction status from the servers as
  // a fallback in case vendor callbacks didn't come yet.
  // Also, publishes an order event specifying payment has started.

  // Positive acknowledgment from the API DOESN'T MEAN successful commencement of the payment.
  // A client must fetch the status from the server.
  rpc InitiateTransaction (InitiateTransactionRequest) returns (InitiateTransactionResponse) {}

  // GetTransaction returns transaction details for a given transaction identifier
  // also returns full payment request info in case `get_req_info` is set to true.
  rpc GetTransaction (GetTransactionRequest) returns (GetTransactionResponse) {}

  // RPC method to update a transaction as per the field mask provided in the request
  // It ensures only fields specified in field masks are updated.
  // Also publishes an event to the order orchestration queue so that associated order can
  // also be updated asynchronously.
  rpc UpdateTransaction (UpdateTransactionRequest) returns (UpdateTransactionResponse) {}

  // GetOrderId returns an order id associated with a given transaction
  rpc GetOrderId (GetOrderIdRequest) returns (GetOrderIdResponse) {}

  // GetTxnsByPi returns a list of all transactions filtered by piFrom and piTo. As both these
  // fields are not mandatory, the RPC can also be used to fetch transactions by just one of piFrom or piTo. If none of
  // the fields are specified, the RPC returns all transactions limited by the page_size and offset fields.
  //
  // The from_timestamp field in the request message can be specified to fetch all transactions after that timestamp.
  // The to_timestamp filed in the request message can be specified to fetch all transactions till that timestamp.
  // Both these fields are not mandatory, and if not specified the RPC will ignore these constraint. A combination of
  // both the fields can be used to fetch transactions for a range of timestamp.
  //
  // The sort_by field specifies the column by which to sort the fetched transactions, and the order of the sorting can
  // be specified by the sort_desc flag. If set to true, transactions will be sorted in descending order.
  //
  // As the number of transactions could grow huge, the RPC returns the list of transactions in pages.
  // The number of transactions returned will be bounded by the page_size specified, whereas offset can be used to
  // fetch the next set of entries to the already returned ones.
  //
  // The statuses field can be used to filter txns for specific statuses, ex: CREATED, SUCCESS, etc.
  rpc GetTxnsByPi (GetTxnsByPiRequest) returns (GetTxnsByPiResponse) {}

  // RewardsSdAddFundsMakeDepositOwnFundTransferPayment initiates the fund transfer request to vendor for a deposit account.
  //
  // The RPC is called by central order retry orchestrator, for processing REWARDS_SD_ADD_FUNDS workflow's second leg, i.e.
  // adding funds from user's savings account to user's deposit account (Smart Deposit).
  //
  // The RPC is responsible for initiating the deposit own fund transfer request at vendor and also checking the status
  // in case request has already been raised. The RPC is idempotent in nature using order's clientRequestId.
  // 1. Creates an entry in the transaction schema if not already exists and initiates payment with the partner bank.
  // 2. In case entry already exists then, transaction status is checked and action is taken accordingly.
  //    a. If the transaction is in a terminal state, then the corresponding order domain status is returned. i.e.
  //       if txn was successful then DomainProcessingStatus SUCCESS is returned and if txn failed then DomainProcessingStatus
  //       PERMANENT_FAILURE is returned.
  //    b. If the transaction is in suspected state, then status for the request is checked with the vendor and after
  //       updating the state machine of the transaction one of DomainProcessingStatus is returned in the response.
  rpc RewardsSdAddFundsMakeDepositOwnFundTransferPayment (order.domain.ProcessFulfilmentRequest) returns (order.domain.ProcessFulfilmentResponse) {}

  // AddFundsSdMakeDepositOwnFundTransferPayment initiates the fund transfer request to vendor for a deposit account.
  //
  // The RPC is called by central order retry orchestrator, for processing ADD_FUNDS_SD workflow i.e.
  // adding funds from user's savings account to user's deposit account (Smart Deposit).
  //
  // The RPC is responsible for initiating the deposit own fund transfer request at vendor and also checking the status
  // in case request has already been raised. The RPC is idempotent in nature using order's clientRequestId.
  // 1. Creates an entry in the transaction schema if not already exists and initiates payment with the partner bank.
  // 2. In case entry already exists then, transaction status is checked and action is taken accordingly.
  //    a. If the transaction is in a terminal state, then the corresponding order domain status is returned. i.e.
  //       if txn was successful then DomainProcessingStatus SUCCESS is returned and if txn failed then DomainProcessingStatus
  //       PERMANENT_FAILURE is returned.
  //    b. If the transaction is in suspected state, then status for the request is checked with the vendor and after
  //       updating the state machine of the transaction one of DomainProcessingStatus is returned in the response.
  rpc AddFundsSdMakeDepositOwnFundTransferPayment (order.domain.ProcessPaymentRequest) returns (order.domain.ProcessPaymentResponse) {}

  // ProcessAddFundTransaction check the transaction status of add fund transaction id at vendor and create a success/fail transaction accordingly.
  // This RPC is idempotent in nature using UrnTransferInfo.TxnId passed in request payload.
  // 1. Creates an entry in the transaction schema if not already exists with paymentReqInfo = "ADD_FUND"+UrnTransferInfo.TxnId
  //    and initiates status check with partner bank.
  // 2. In case already exists then, transaction status is checked and action is taken accordingly.
  //    a. If the transaction is in a terminal state and transaction is SUCCESS/FAIL then Domain process status with SUCCESS/PERMANENT_FAILURE will be returned
  //    b. If the transaction is in suspected state, then IN_PROGRESS will be updated with txn and corresponding domain processing status will be returned.
  // Note: ADD_FUND prefix will be added while storing paymentRequestInfo transactionId but while sending it to vendor prefix need to be stripped off.
  rpc ProcessAddFundTransaction (order.domain.ProcessSettlementRequest) returns (order.domain.ProcessSettlementResponse);

  // ReInitiateTransactionEnquiry is an async API, which initiates a transaction enquiry in the system.
  // It publishes an event to payment enquiry queue for the respective protocol
  //
  // Positive acknowledgment from the API DOESN'T MEAN successful commencement of the payment.
  // A client must fetch the status from the server.
  rpc ReInitiateTransactionEnquiry (ReInitiateTransactionEnquiryRequest) returns (ReInitiateTransactionEnquiryResponse) {}
  // rpc to initiate a collect request for ios add funds with the vendor
  // the rpc is idempotent in nature and will initiate a request with the vendor only if not done already
  // Will return permanent failure in case collect initiation to the vendor fails.
  // Will return transient failure if there is some intermittent internal processing error
  // In case we get an unknown status/transient error from vendor rpc will return success, the order will be moved to the next stage
  // i.e payment stahe where we will enquire for the txn status.
  rpc InitiateAddFundsCollect (order.domain.ProcessCollectRequest) returns (order.domain.ProcessCollectResponse) {}

  // GetOrdersInfoForTransactions returns batch of ordersId along with transactionsId for the batch of transaction identifier requests
  // which have entries in DB and ignore the requests for which ordersId or transactionsId doesnot exist.
  // It will return record not found if there is no entries for any of requests.
  rpc GetOrdersInfoForTransactions (GetOrdersInfoForTransactionsRequest) returns (GetOrdersInfoForTransactionsResponse) {}

  // GetTransactionsCount returns the count of transactions for given actor. If pi_ids of the actor is passed instead
  // of the actor ID, then the transactions corresponding to that PI are fetched instead.
  // The startTime & endTime fields are mandatory and the interval between the start & end times must be less than or
  // equal to 2 days otherwise InvalidArgument error is returned.
  // The transaction count returned may be less than the true value within the given window for certain edge
  // cases (documented in GetTransactionsCount DAO method implementation in gamma/order/dao/transaction.go). This was a
  // conscious call taken to keep the RPC performant enough for older time-windows.
  // Deprecated: In favour of pay.GetTransactionAggregates. Query for this RPC will impact CRDB cluster.
  rpc GetTransactionsCount (GetTransactionsCountRequest) returns (GetTransactionsCountResponse);
}

message CreateTransactionRequest {
  // payment instrument from which asset need to be debited
  string pi_from = 1 [(validate.rules).string.min_len = 1];

  // payment instrument to which asset needs to be credited
  string pi_to = 2 [(validate.rules).string.min_len = 1];

  // amount of money that needs to be transferred
  google.type.Money amount = 3 [(validate.rules).message.required = true];

  // user remarks / auto generated remarks
  string remarks = 4;

  // order against which payment is being made
  string order_id = 5 [(validate.rules).string.min_len = 1];

  // payment protocol chosen by decision engine for a particular transaction.
  order.payment.PaymentProtocol payment_protocol = 6 [(validate.rules).enum = {not_in: [0]}];

  // status of transaction to be created.
  // in certain scenarios like incoming payment, a transaction can be created with
  // intermittent state IN_PROGRESS or terminal states
  order.payment.TransactionStatus status = 7 [(validate.rules).enum = {not_in: [0]}];

  // optional: protocol status to be specified while creating a transaction
  // in certain scenarios like incoming payment, a transaction can be created with
  // intermittent state or terminal states
  order.payment.TransactionProtocolStatus protocol_status = 8;

  // contains necessary information related to bank payment request,
  // that can be inferred for sending requests and response while processing a transaction
  order.payment.PaymentRequestInformation req_info = 9;

  // OPTIONAL: transaction execution timestamp
  // To be used by client in case transaction entry is created post
  // payment is in terminal state
  google.protobuf.Timestamp executed_at = 10 [deprecated = true];

  // Transaction detailed status contains raw feedback we get from banks.
  // it's important to store this detailed data, as it will help us to understand and debug the reason of failure of a transaction.
  // e.g. A transaction can fail due to multiple reasons, broadly we can categorize them as transient and permanent failure -
  // 1. Permanent failure - Insufficient balance, Debit Frozen, Account Frozen, Transfer limit exceeded, etc.
  // 2. Transient failure - CBS connection failure, CBS response timeout, Server is down please try after sometime.
  // all of these detailed status are stores in below message.
  //
  // for certain use cases like upi transactions, we need to create a transaction when we get a reqTxnConfirmation
  // hence we also need to add detailed status while creating the transaction
  order.payment.TransactionDetailedStatus detailed_status = 11;

  // Optional: Unique Transaction Reference for Online Txns like NEFT/IMPS/UPI
  // Can be RRN in case of card payments
  string utr = 12;

  // OPTIONAL: timestamp at which the amount is debited
  // To be used  in case transaction entry is created post
  // amount is debited
  google.protobuf.Timestamp debited_at = 13;

  // OPTIONAL: timestamp at which the amount is credited
  // To be used  in case transaction entry is created post
  // amount is credited
  //
  // Will store the reversal transaction's `executed_at` timestamp if the transaction is reversed
  google.protobuf.Timestamp credited_at = 14;

  // ownership under which transaction data is persisted
  api.typesv2.common.Ownership ownership = 15;

  // amount of money that needs to be transferred
  google.type.Money base_amount_quote_currency = 16;

  // ID of the actor that has initiated the request
  string current_actor_id = 17;
}

message CreateTransactionResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;

  order.payment.Transaction transaction = 2;

  order.payment.PaymentRequestInformation req_info = 3;
}

message InitiateTransactionRequest {

  message AuthHeader {
    // Device ID or fingerprint of the device that is registered with the partner bank
    upi.Device device = 1 [(validate.rules).message.required = true];

    // Credential contains a cred block that acts as second auth factor for the transactions
    // which require another factor of authentication
    //
    // Cred block can either be generated through NPCI CL or Partner Bank's SDK
    // They typically contain Salt parameters in addition to PIN such as, but not limited to:
    // 1. Transaction ID
    // 2. Amount
    // 3. Timestamp
    oneof credential {
      // Financial transactions such as Intra Bank, NEFT, IMPS RTGS require `Secure PIN`
      // as second factor authentication. Secure PIN is the same as UPI PIN
      //
      // However, the encrypted PIN is generated through Partner bank's SDK and is a
      // Base64 encoded JSON string that can be passed as it is
      //
      // Partner bank evaluates the risk of transaction based on multiple attributes e.g:
      //  - Amount of the transaction
      //  - Time when the transaction is initiated
      //  - Location from where the transaction is initiated
      // Based on the evaluated risk, `cred_block` may be required to complete the transaction
      // In absence of `cred_block` being set, appropriate error may be returned by the partner bank,
      // if `cred_block` is required
      string partner_sdk_cred_block = 6;

      // All the UPI transactions require Cred block generated through NPCI common library
      // Cred block is to be passed to NPCI in XML format
      // To prevent errors in data transformation and to keep business logic out of client,
      // frontend expects NPCI's cred block in a structured fashion
      // Deprecated in favour of NpciCredBlocks
      upi.CredBlock npci_cred_block = 7 [deprecated = true];

      // All the UPI transactions require Cred block generated through NPCI common library
      // Cred block is to be passed to NPCI in XML format
      // To prevent errors in data transformation and to keep business logic out of client,
      // frontend expects NPCI's cred block in a structured fashion
      // There can be cases where we might need multiple cred block to pass in request, for e.g.
      // in case of UPI Lite client passes the set of UPI PIN and ARQC
      NpciCredBlocks npci_cred_blocks = 8;
    }

    // Encrypted PIN block that may be required to authorise the transaction
    // This PIN is to be entered by the user at the time of transaction initiation on the client application
    // PIN is then encrypted by Client application and tunnelled all the way from the app to partner bank
    //
    // Partner bank evaluates the risk of transaction based on multiple attributes e.g:
    //  - Amount of the transaction
    //  - Time when the transaction is initiated
    //  - Location from where the transaction is initiated
    // Based on the evaluated risk, `cred_block` may be required to complete the transaction
    // In absence of `cred_block` being set, appropriate error may be returned by the partner bank,
    // if `cred_block` is required
    upi.CredBlock cred_block = 2 [deprecated = true];

    // Device token is issued by the federal bank at the time of device registration
    string device_token = 3;

    // User ID assigned to a user by epiFi
    string user_profile_id = 4;

    // unique customer identifier provided by bank
    string customer_id = 5;
  }
  // A set of authentication attributes that are common across Open banking requests
  AuthHeader payment_auth = 1 [(validate.rules).message.required = true];

  // unique request id per transaction (Surrogate key).
  // This req id is passed to all the external systems to uniquely identify an system transaction.
  // req id can be both server side as well as client side generated.
  string transaction_req_id = 2 [(validate.rules).string.min_len = 1];

  // order id against which transaction is being initialised.
  // It is typically used to validate if the order is mapped to the given transaction_req_id before initialisation.
  string order_id = 3 [(validate.rules).string.min_len = 1];

  // A flag that denotes if the intent of the transaction request is to:
  // - True: Collect from others
  // - False: Pay to others
  bool is_collect_request = 4;

  // Actor who is initiating the transaction
  string actor_id = 5;

  // urn[optional]
  // - UPI urn passed during create order
  // - required in some flows where qr payload needs to be passed to vendor
  //   in order to initiate payment
  //   E.g. upi international payments
  string urn = 6;

  // base_amount_quote_currency[optional]:
  // amount in foreign currency that needs to be transferred
  google.type.Money base_amount_quote_currency = 7;
}

message InitiateTransactionResponse {
  enum Status {
    OK = 0;

    // transaction failed to initiate.
    // this can happen due to various reasons e.g. in-sufficient funds, account frozen temporarily.
    TRANSACTION_FAILED = 100;

    // transaction is in in-valid state
    // it can be mean either transaction is in terminal state or it has already been initiated.
    INVALID_TRANSACTION_STATE = 101;
  }

  rpc.Status status = 1;

  order.payment.Transaction transaction = 2;
}

message GetTransactionRequest {
  oneof Identifier {
    // unique identifier of a transaction in our system.
    string transaction_id = 1;

    // unique identifier of a transaction in partner bank's system.
    string partner_ref_id = 2;

    // unique identifier of a transaction across all banking systems.
    string utr = 3;

    // server side generated unique request id per transaction (Surrogate key).
    // This req id is passed to all the external systems to uniquely identify an system transaction.
    string req_id = 4;
  }

  // boolean flag if set true, returns full transaction's payment request details including msg-id, merchant-ref-id, etc.
  bool get_req_info = 5;
}

message GetTransactionResponse {
  enum Status {
    OK = 0;

    // transaction record not found
    RECORD_NOT_FOUND = 5;

    // internal server error. Can be due to various reason e.g. DB unavailable
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // transaction details fetched with the identifier sent in the request.
  order.payment.Transaction transaction = 2;

  // payment request info linked to a transaction
  order.payment.PaymentRequestInformation req_info = 3;
}

message UpdateTransactionRequest {

  // unique identifier of a transaction in our system.
  // note: id is a must for updating a transaction
  order.payment.Transaction transaction = 1 [(validate.rules).message.required = true];

  order.payment.PaymentRequestInformation req_info = 2;

  // txn field masks to ensure only desired fields are updated in transaction.
  repeated order.payment.TransactionFieldMask field_masks = 3 [(validate.rules).repeated.min_items = 1];
}

message UpdateTransactionResponse {
  enum Status {
    OK = 0;

    // internal server error. Can be due to various reason e.g. DB unavailable
    INTERNAL = 13;
  }

  rpc.Status status = 1;
}

message GetOrderIdRequest {
  string transaction_id = 1;
}

message GetOrderIdResponse {
  enum Status {
    OK = 0;

    // order-id not found for the given transaction id
    NOT_FOUND = 5;

    // internal server error. Can be due to various reason e.g. DB unavailable
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  string order_id = 2;
}

message GetTxnsByPiRequest {
  // piFrom for which transactions are to be fetched
  string pi_from = 1;

  // piTo for which the transactions are to be fetched.
  string pi_to = 2;

  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp from_timestamp = 3;

  // timestamp till which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp to_timestamp = 4;

  // Transaction field by which to sort the fetched transactions
  order.payment.TransactionFieldMask sort_by = 5 [(validate.rules).enum = {not_in: [0]}];

  // flag to determine if transactions are to be sorted in descending order or ascending
  bool sort_desc = 6;

  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  // Page size must be in the range [10, 40]
  // minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
  int32 page_size = 7 [(validate.rules).int32 = {gte: 10, lte: 40}];

  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 offset = 8 [(validate.rules).int32.gte = 0];

  // List of status to fetch txns for
  // If not specified, the RPC returns txns for all possible statuses for the PIs mentioned
  repeated order.payment.TransactionStatus statuses = 9;
}

message GetTxnsByPiResponse {
  enum Status {
    OK = 0;

    // in case transactions are not found for a given set of request params.
    RECORD_NOT_FOUND = 5;

    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // list of all the transactions for the given set of request parameters.
  repeated order.payment.Transaction transactions = 2;
}

message ReInitiateTransactionEnquiryRequest {
  // id of the transaction for which enquiry needs to be initiated
  string transaction_id = 1 [(validate.rules).string.min_len = 1];

  // customer id of user returned by bank while onboarding
  // Note: This field is required for now only for deposit payment status
  string customer_id = 2;

  // Actor who has initiated the transaction
  string actor_id = 3 [(validate.rules).string.min_len = 1];

  // phone number of the initiator actor
  api.typesv2.common.PhoneNumber phone_number = 4 [(validate.rules).message.required = true];
}

message ReInitiateTransactionEnquiryResponse {
  enum Status {
    OK = 0;

    // in case transactions are not found for a given set of request params.
    RECORD_NOT_FOUND = 5;

    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;
}

message OrderTransactionId {
  string order_id = 1;
  string transaction_id = 2;
}

message TransactionIdentifier {
  // unique identifier for a transaction
  oneof identifier {
    // Unique Transaction Reference for Online Txns like NEFT/IMPS/UPI
    // Can be RRN in case of card payments
    string utr = 1;
  }
}

message GetOrdersInfoForTransactionsRequest {
  repeated TransactionIdentifier transaction_identifiers = 1;
}

message GetOrdersInfoForTransactionsResponse {
  enum Status {
    OK = 0;
    // order or transaction doesn't exist
    NOT_FOUND = 5;
    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;
  // list of orderId with TransactionId for the request
  repeated OrderTransactionId orderTransactionId = 2;
}

message GetTransactionsCountRequest {
  // actor id of the user
  string actor_id = 1;

  // optional: list of pi ids
  repeated string pi_ids = 2;

  // optional: list of other pi ids
  repeated string other_pi_ids = 3;

  // starting time from which we want the transactions
  google.protobuf.Timestamp start_time = 4;
  // ending time until which we want the transactions
  google.protobuf.Timestamp end_time = 5;

  // optional: transaction type wrt the given actor.
  // The caller can use this to filter out the results corresponding to a particular transactionType.
  // If kept empty, by default all the orders (credit or debit) related to the actor are returned
  // If TransactionType is TRANSACTION_TYPE_UNSPECIFIED, all the orders related to the actor are returned.
  order.payment.AccountingEntryType transaction_type = 6;

  // optional: payment_protocol filter. orders with payment_protocol in this list are considered
  repeated order.payment.PaymentProtocol payment_protocols = 7;

  // optional : filtering the transactions based on the transaction status
  // If kept empty , by default transactions with all status are considered in count
  repeated order.payment.TransactionStatus transaction_status = 8;

  // type of accounts (savings,sd,fd etc) for which we want to find the total amount
  repeated .accounts.Type account_types = 9;
}

message GetTransactionsCountResponse {
  enum Status {
    OK = 0;

    // internal error while processing the request
    INTERNAL = 13;
  }

  // denotes the status of the GetTransaction Request
  rpc.Status status = 1;

  int64 count = 2;
}

// As per new requirements seen in UPI Lite now its possible that client can send multiple cred blocks in case of upi payments
// In Upi Lite during top up client will pass a set of two cred blocks : UPI PIN and ARQC
message NpciCredBlocks {
  repeated upi.CredBlock cred_blocks = 1;
}
