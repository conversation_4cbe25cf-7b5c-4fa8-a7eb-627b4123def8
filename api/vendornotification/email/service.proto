// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.email;


import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "api/vendors/email/sendgrid/sendgrid.proto";
import "api/vendors/email/ses/ses.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/email";
option java_package = "com.github.epifi.gamma.api.vendornotification.email";

// service to receive email callbacks from vendors
service EmailCallback {

  // TODO - need to add sendgrid email callback method

  rpc SesCallback (vendors.email.ses.AwsSesCallback) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/email/callback/ses"
      body: "*"
   };
  }

  rpc SendgridCallback (vendors.email.sendgrid.SendGridCallbackList) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/email/callback/sendgrid"
      body: "*"
   };
  }
}
