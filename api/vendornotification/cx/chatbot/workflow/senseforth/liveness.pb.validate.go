// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendornotification/cx/chatbot/workflow/senseforth/liveness.proto

package senseforth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LivenessData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LivenessData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LivenessDataMultiError, or
// nil if none found.
func (m *LivenessData) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LivenessStatus

	if len(errors) > 0 {
		return LivenessDataMultiError(errors)
	}

	return nil
}

// LivenessDataMultiError is an error wrapping multiple validation errors
// returned by LivenessData.ValidateAll() if the designated constraints aren't met.
type LivenessDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessDataMultiError) AllErrors() []error { return m }

// LivenessDataValidationError is the validation error returned by
// LivenessData.Validate if the designated constraints aren't met.
type LivenessDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessDataValidationError) ErrorName() string { return "LivenessDataValidationError" }

// Error satisfies the builtin error interface
func (e LivenessDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessDataValidationError{}

// Validate checks the field values on LivenessDataParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LivenessDataParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessDataParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessDataParamsMultiError, or nil if none found.
func (m *LivenessDataParams) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessDataParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LivenessDataParamsMultiError(errors)
	}

	return nil
}

// LivenessDataParamsMultiError is an error wrapping multiple validation errors
// returned by LivenessDataParams.ValidateAll() if the designated constraints
// aren't met.
type LivenessDataParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessDataParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessDataParamsMultiError) AllErrors() []error { return m }

// LivenessDataParamsValidationError is the validation error returned by
// LivenessDataParams.Validate if the designated constraints aren't met.
type LivenessDataParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessDataParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessDataParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessDataParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessDataParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessDataParamsValidationError) ErrorName() string {
	return "LivenessDataParamsValidationError"
}

// Error satisfies the builtin error interface
func (e LivenessDataParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessDataParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessDataParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessDataParamsValidationError{}
