// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendornotification/lending/bre/inhouse/service.proto

package inhouse

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetFeaturesDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// features list for which data needs to be fetched from feature store
	// NOTE : feature store currently only supports fetching data for features which have same set of request identifiers
	// Example : Say a feature F1 has actorId and model name as request identifier, F2 has actorId as request identifier and F3 has actorId and
	// model name as request identifier, then in a single api call we can either support [F2] or [F1,F3].
	FeatureNameList []string `protobuf:"bytes,1,rep,name=feature_name_list,json=FeatureNameList,proto3" json:"feature_name_list,omitempty"`
	// request identifiers containing list of Identifiers for each request
	// we will evaluate the above feature list for each request identifier
	// Request Identifiers should have same set of list of identifiers for each request as mentioned above
	RequestIdentifiersList []*RequestIdentifiers `protobuf:"bytes,2,rep,name=request_identifiers_list,json=RequestIdentifiersList,proto3" json:"request_identifiers_list,omitempty"`
}

func (x *GetFeaturesDataRequest) Reset() {
	*x = GetFeaturesDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeaturesDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeaturesDataRequest) ProtoMessage() {}

func (x *GetFeaturesDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeaturesDataRequest.ProtoReflect.Descriptor instead.
func (*GetFeaturesDataRequest) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetFeaturesDataRequest) GetFeatureNameList() []string {
	if x != nil {
		return x.FeatureNameList
	}
	return nil
}

func (x *GetFeaturesDataRequest) GetRequestIdentifiersList() []*RequestIdentifiers {
	if x != nil {
		return x.RequestIdentifiersList
	}
	return nil
}

type RequestIdentifiers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifiers for which data needs to be fetched
	// Currently the supported identifiers are actorId, accountId or Model Name and combination of the same
	Identifiers *Identifiers `protobuf:"bytes,2,opt,name=identifiers,json=Identifiers,proto3" json:"identifiers,omitempty"`
}

func (x *RequestIdentifiers) Reset() {
	*x = RequestIdentifiers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestIdentifiers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestIdentifiers) ProtoMessage() {}

func (x *RequestIdentifiers) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestIdentifiers.ProtoReflect.Descriptor instead.
func (*RequestIdentifiers) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescGZIP(), []int{1}
}

func (x *RequestIdentifiers) GetIdentifiers() *Identifiers {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

type GetFeaturesDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of features data for identifiers sent in the request
	FeaturesResponseDataList []*FeaturesResponseData `protobuf:"bytes,1,rep,name=features_response_data_list,json=FeaturesResponseDataList,proto3" json:"features_response_data_list,omitempty"`
}

func (x *GetFeaturesDataResponse) Reset() {
	*x = GetFeaturesDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeaturesDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeaturesDataResponse) ProtoMessage() {}

func (x *GetFeaturesDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeaturesDataResponse.ProtoReflect.Descriptor instead.
func (*GetFeaturesDataResponse) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetFeaturesDataResponse) GetFeaturesResponseDataList() []*FeaturesResponseData {
	if x != nil {
		return x.FeaturesResponseDataList
	}
	return nil
}

type FeaturesResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifiers sent in the request
	Identifiers *Identifiers `protobuf:"bytes,1,opt,name=identifiers,json=Identifiers,proto3" json:"identifiers,omitempty"`
	// List of features value for the identifier
	FeatureValueMap map[string]*structpb.Value `protobuf:"bytes,2,rep,name=feature_value_map,json=FeatureValueMap,proto3" json:"feature_value_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FeaturesResponseData) Reset() {
	*x = FeaturesResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeaturesResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeaturesResponseData) ProtoMessage() {}

func (x *FeaturesResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeaturesResponseData.ProtoReflect.Descriptor instead.
func (*FeaturesResponseData) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescGZIP(), []int{3}
}

func (x *FeaturesResponseData) GetIdentifiers() *Identifiers {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

func (x *FeaturesResponseData) GetFeatureValueMap() map[string]*structpb.Value {
	if x != nil {
		return x.FeatureValueMap
	}
	return nil
}

// Identifiers for which data is fetched from feature store
// Based on the feature we can have any combination of these identifiers
type Identifiers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor Id
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=ActorId,proto3" json:"actor_id,omitempty"`
	// account Id
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=AccountId,proto3" json:"account_id,omitempty"`
	// Model name
	ModelName string `protobuf:"bytes,3,opt,name=model_name,json=ModelName,proto3" json:"model_name,omitempty"`
}

func (x *Identifiers) Reset() {
	*x = Identifiers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Identifiers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Identifiers) ProtoMessage() {}

func (x *Identifiers) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Identifiers.ProtoReflect.Descriptor instead.
func (*Identifiers) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescGZIP(), []int{4}
}

func (x *Identifiers) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Identifiers) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Identifiers) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

var File_api_vendornotification_lending_bre_inhouse_service_proto protoreflect.FileDescriptor

var file_api_vendornotification_lending_bre_inhouse_service_proto_rawDesc = []byte{
	0x0a, 0x38, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2f, 0x62, 0x72, 0x65, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x26, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75,
	0x73, 0x65, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xba,
	0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x74, 0x0a, 0x18, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x73, 0x52, 0x16, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x6b, 0x0a, 0x12, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x73, 0x12, 0x55, 0x0a, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x52, 0x0b, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x1b, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75,
	0x73, 0x65, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x18, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0xc8, 0x02, 0x0a, 0x14, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x0b, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x33, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x72, 0x65,
	0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x73, 0x52, 0x0b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x73, 0x12, 0x7d, 0x0a, 0x11, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0f, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70,
	0x1a, 0x5a, 0x0a, 0x14, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x66, 0x0a, 0x0b,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x32, 0xcb, 0x01, 0x0a, 0x03, 0x42, 0x72, 0x65, 0x12, 0xc3, 0x01, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x72,
	0x65, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x72,
	0x65, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x62, 0x72, 0x65, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75,
	0x73, 0x65, 0x2f, 0x67, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x42, 0x86, 0x01, 0x0a, 0x41, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x62, 0x72, 0x65,
	0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f,
	0x62, 0x72, 0x65, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescOnce sync.Once
	file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescData = file_api_vendornotification_lending_bre_inhouse_service_proto_rawDesc
)

func file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescGZIP() []byte {
	file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescOnce.Do(func() {
		file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescData)
	})
	return file_api_vendornotification_lending_bre_inhouse_service_proto_rawDescData
}

var file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_vendornotification_lending_bre_inhouse_service_proto_goTypes = []interface{}{
	(*GetFeaturesDataRequest)(nil),  // 0: vendornotification.lending.bre.inhouse.GetFeaturesDataRequest
	(*RequestIdentifiers)(nil),      // 1: vendornotification.lending.bre.inhouse.RequestIdentifiers
	(*GetFeaturesDataResponse)(nil), // 2: vendornotification.lending.bre.inhouse.GetFeaturesDataResponse
	(*FeaturesResponseData)(nil),    // 3: vendornotification.lending.bre.inhouse.FeaturesResponseData
	(*Identifiers)(nil),             // 4: vendornotification.lending.bre.inhouse.Identifiers
	nil,                             // 5: vendornotification.lending.bre.inhouse.FeaturesResponseData.FeatureValueMapEntry
	(*structpb.Value)(nil),          // 6: google.protobuf.Value
}
var file_api_vendornotification_lending_bre_inhouse_service_proto_depIdxs = []int32{
	1, // 0: vendornotification.lending.bre.inhouse.GetFeaturesDataRequest.request_identifiers_list:type_name -> vendornotification.lending.bre.inhouse.RequestIdentifiers
	4, // 1: vendornotification.lending.bre.inhouse.RequestIdentifiers.identifiers:type_name -> vendornotification.lending.bre.inhouse.Identifiers
	3, // 2: vendornotification.lending.bre.inhouse.GetFeaturesDataResponse.features_response_data_list:type_name -> vendornotification.lending.bre.inhouse.FeaturesResponseData
	4, // 3: vendornotification.lending.bre.inhouse.FeaturesResponseData.identifiers:type_name -> vendornotification.lending.bre.inhouse.Identifiers
	5, // 4: vendornotification.lending.bre.inhouse.FeaturesResponseData.feature_value_map:type_name -> vendornotification.lending.bre.inhouse.FeaturesResponseData.FeatureValueMapEntry
	6, // 5: vendornotification.lending.bre.inhouse.FeaturesResponseData.FeatureValueMapEntry.value:type_name -> google.protobuf.Value
	0, // 6: vendornotification.lending.bre.inhouse.Bre.GetFeaturesData:input_type -> vendornotification.lending.bre.inhouse.GetFeaturesDataRequest
	2, // 7: vendornotification.lending.bre.inhouse.Bre.GetFeaturesData:output_type -> vendornotification.lending.bre.inhouse.GetFeaturesDataResponse
	7, // [7:8] is the sub-list for method output_type
	6, // [6:7] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_vendornotification_lending_bre_inhouse_service_proto_init() }
func file_api_vendornotification_lending_bre_inhouse_service_proto_init() {
	if File_api_vendornotification_lending_bre_inhouse_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeaturesDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestIdentifiers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeaturesDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeaturesResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Identifiers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendornotification_lending_bre_inhouse_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendornotification_lending_bre_inhouse_service_proto_goTypes,
		DependencyIndexes: file_api_vendornotification_lending_bre_inhouse_service_proto_depIdxs,
		MessageInfos:      file_api_vendornotification_lending_bre_inhouse_service_proto_msgTypes,
	}.Build()
	File_api_vendornotification_lending_bre_inhouse_service_proto = out.File
	file_api_vendornotification_lending_bre_inhouse_service_proto_rawDesc = nil
	file_api_vendornotification_lending_bre_inhouse_service_proto_goTypes = nil
	file_api_vendornotification_lending_bre_inhouse_service_proto_depIdxs = nil
}
