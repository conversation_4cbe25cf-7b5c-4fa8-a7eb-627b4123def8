syntax = "proto3";

package api.typesv2.common;

option go_package = "github.com/epifi/be-common/api/typesv2/common";
option java_package = "com.github.epifi.gamma.api.typesv2.common";

enum PoliticallyExposedStatus {
  POLITICALLY_EXPOSED_STATUS_UNSPECIFIED = 0;
  POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE = 1;
  POLITICALLY_EXPOSED_PERSON = 2;
  RELATED_TO_POLITICALLY_EXPOSED_PERSON = 3;
  // this is for other that non individual cases
  POLITICALLY_EXPOSED_STATUS_OTHER = 4;
}
