syntax = "proto3";

package api.typesv2;

import "api/typesv2/common/ownership.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// PaymentGatewayProgram is a construct to communicate the PG requirements between domain
// services and pay platform. This will be used to identify authentication keys that has to
// be used for the PG APIs. This will be a shared concept between pay platform and the domain
// services.
message PaymentGatewayProgram {
  // payment gateway provider via which the PG txns will be going through
  vendorgateway.Vendor pg_vendor = 1;
  // pi id of the beneficiary account.
  string pi_id = 2;
  // entity ownership of the account.
  typesv2.common.Ownership entity_ownership = 3;
}
