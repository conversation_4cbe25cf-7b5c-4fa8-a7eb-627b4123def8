package typesv2

import (
	"database/sql/driver"
	"fmt"
)

// Value Valuer interface implementation for storing the data in JSONB format in DB
func (x AddressType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *AddressType) Scan(input interface{}) error {
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", input)
	}

	valInt, ok := AddressType_value[val]
	if !ok {
		return fmt.Errorf("unexpected value: %s", val)
	}

	*x = AddressType(valInt)
	return nil
}
