// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/sa_closure/screen_options.proto

package sa_closure

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	enums "github.com/epifi/gamma/api/frontend/account/enums"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	info "github.com/epifi/gamma/api/typesv2/info"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=233-8188&mode=design&t=d6fSyshwKwwbCWJ8-4
type SaClosureUserFeedbackScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	PageTitle *common.Text                               `protobuf:"bytes,2,opt,name=page_title,json=pageTitle,proto3" json:"page_title,omitempty"`
	// feedback option the user can select
	UserFeedbackOptions []*UserFeedbackOption `protobuf:"bytes,3,rep,name=user_feedback_options,json=userFeedbackOptions,proto3" json:"user_feedback_options,omitempty"`
	// cta to submit the feedback
	Cta *deeplink.Cta `protobuf:"bytes,4,opt,name=cta,proto3" json:"cta,omitempty"`
}

func (x *SaClosureUserFeedbackScreenOptions) Reset() {
	*x = SaClosureUserFeedbackScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaClosureUserFeedbackScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaClosureUserFeedbackScreenOptions) ProtoMessage() {}

func (x *SaClosureUserFeedbackScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaClosureUserFeedbackScreenOptions.ProtoReflect.Descriptor instead.
func (*SaClosureUserFeedbackScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *SaClosureUserFeedbackScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SaClosureUserFeedbackScreenOptions) GetPageTitle() *common.Text {
	if x != nil {
		return x.PageTitle
	}
	return nil
}

func (x *SaClosureUserFeedbackScreenOptions) GetUserFeedbackOptions() []*UserFeedbackOption {
	if x != nil {
		return x.UserFeedbackOptions
	}
	return nil
}

func (x *SaClosureUserFeedbackScreenOptions) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

type UserFeedbackOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// text for the feedback option
	Text *common.Text `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// Optional: deeplink to take user when clicked on feedback option
	Action *deeplink.Deeplink `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	// flag to load text box for custom feedback
	IsCustomFeedback bool `protobuf:"varint,3,opt,name=is_custom_feedback,json=isCustomFeedback,proto3" json:"is_custom_feedback,omitempty"`
	// Optional: placeholder field to show in text box for getting custom feedback from user
	// applicable when IsCustomFeedback is true
	CustomFeedbackPlaceholder string `protobuf:"bytes,4,opt,name=custom_feedback_placeholder,json=customFeedbackPlaceholder,proto3" json:"custom_feedback_placeholder,omitempty"`
}

func (x *UserFeedbackOption) Reset() {
	*x = UserFeedbackOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserFeedbackOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserFeedbackOption) ProtoMessage() {}

func (x *UserFeedbackOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserFeedbackOption.ProtoReflect.Descriptor instead.
func (*UserFeedbackOption) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *UserFeedbackOption) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *UserFeedbackOption) GetAction() *deeplink.Deeplink {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *UserFeedbackOption) GetIsCustomFeedback() bool {
	if x != nil {
		return x.IsCustomFeedback
	}
	return false
}

func (x *UserFeedbackOption) GetCustomFeedbackPlaceholder() string {
	if x != nil {
		return x.CustomFeedbackPlaceholder
	}
	return ""
}

// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=218-3163&mode=design&t=dL7LJ7PujX6uecF3-4
type PanDobInputScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                    *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Heading                   *common.Text                               `protobuf:"bytes,2,opt,name=heading,proto3" json:"heading,omitempty"`
	Subheading                *common.Text                               `protobuf:"bytes,3,opt,name=subheading,proto3" json:"subheading,omitempty"`
	PanNumberInputPlaceholder string                                     `protobuf:"bytes,4,opt,name=pan_number_input_placeholder,json=panNumberInputPlaceholder,proto3" json:"pan_number_input_placeholder,omitempty"`
	DobInputPlaceholder       string                                     `protobuf:"bytes,5,opt,name=dob_input_placeholder,json=dobInputPlaceholder,proto3" json:"dob_input_placeholder,omitempty"`
	SubmitCta                 *deeplink.Cta                              `protobuf:"bytes,6,opt,name=submit_cta,json=submitCta,proto3" json:"submit_cta,omitempty"`
	BackgroundColor           string                                     `protobuf:"bytes,7,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
}

func (x *PanDobInputScreenOptions) Reset() {
	*x = PanDobInputScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PanDobInputScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PanDobInputScreenOptions) ProtoMessage() {}

func (x *PanDobInputScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PanDobInputScreenOptions.ProtoReflect.Descriptor instead.
func (*PanDobInputScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *PanDobInputScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PanDobInputScreenOptions) GetHeading() *common.Text {
	if x != nil {
		return x.Heading
	}
	return nil
}

func (x *PanDobInputScreenOptions) GetSubheading() *common.Text {
	if x != nil {
		return x.Subheading
	}
	return nil
}

func (x *PanDobInputScreenOptions) GetPanNumberInputPlaceholder() string {
	if x != nil {
		return x.PanNumberInputPlaceholder
	}
	return ""
}

func (x *PanDobInputScreenOptions) GetDobInputPlaceholder() string {
	if x != nil {
		return x.DobInputPlaceholder
	}
	return ""
}

func (x *PanDobInputScreenOptions) GetSubmitCta() *deeplink.Cta {
	if x != nil {
		return x.SubmitCta
	}
	return nil
}

func (x *PanDobInputScreenOptions) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=576-20238&mode=design&t=dL7LJ7PujX6uecF3-4
type SaClosureSubmitRequestSwipeActionScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// element for Image, Title and subtitle to be shown in the Swipe action screen
	ImageTitleSubtitle *widget.VisualElementTitleSubtitleElement `protobuf:"bytes,2,opt,name=image_title_subtitle,json=imageTitleSubtitle,proto3" json:"image_title_subtitle,omitempty"`
	SwipeButton        *ui.SwipeButton                           `protobuf:"bytes,3,opt,name=swipe_button,json=swipeButton,proto3" json:"swipe_button,omitempty"`
	CancelCta          *deeplink.Cta                             `protobuf:"bytes,4,opt,name=cancel_cta,json=cancelCta,proto3" json:"cancel_cta,omitempty"`
}

func (x *SaClosureSubmitRequestSwipeActionScreenOptions) Reset() {
	*x = SaClosureSubmitRequestSwipeActionScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaClosureSubmitRequestSwipeActionScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaClosureSubmitRequestSwipeActionScreenOptions) ProtoMessage() {}

func (x *SaClosureSubmitRequestSwipeActionScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaClosureSubmitRequestSwipeActionScreenOptions.ProtoReflect.Descriptor instead.
func (*SaClosureSubmitRequestSwipeActionScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *SaClosureSubmitRequestSwipeActionScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SaClosureSubmitRequestSwipeActionScreenOptions) GetImageTitleSubtitle() *widget.VisualElementTitleSubtitleElement {
	if x != nil {
		return x.ImageTitleSubtitle
	}
	return nil
}

func (x *SaClosureSubmitRequestSwipeActionScreenOptions) GetSwipeButton() *ui.SwipeButton {
	if x != nil {
		return x.SwipeButton
	}
	return nil
}

func (x *SaClosureSubmitRequestSwipeActionScreenOptions) GetCancelCta() *deeplink.Cta {
	if x != nil {
		return x.CancelCta
	}
	return nil
}

type SaClosureRequestSubmittedScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header             *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FullScreenInfoView *info.FullScreenInfoView                   `protobuf:"bytes,2,opt,name=full_screen_info_view,json=fullScreenInfoView,proto3" json:"full_screen_info_view,omitempty"`
	// counter to show the number of days remaining before cancelling colosure request
	CounterElement *ItcTitleSubtitleElement `protobuf:"bytes,3,opt,name=counter_element,json=counterElement,proto3" json:"counter_element,omitempty"`
}

func (x *SaClosureRequestSubmittedScreenOptions) Reset() {
	*x = SaClosureRequestSubmittedScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaClosureRequestSubmittedScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaClosureRequestSubmittedScreenOptions) ProtoMessage() {}

func (x *SaClosureRequestSubmittedScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaClosureRequestSubmittedScreenOptions.ProtoReflect.Descriptor instead.
func (*SaClosureRequestSubmittedScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescGZIP(), []int{4}
}

func (x *SaClosureRequestSubmittedScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SaClosureRequestSubmittedScreenOptions) GetFullScreenInfoView() *info.FullScreenInfoView {
	if x != nil {
		return x.FullScreenInfoView
	}
	return nil
}

func (x *SaClosureRequestSubmittedScreenOptions) GetCounterElement() *ItcTitleSubtitleElement {
	if x != nil {
		return x.CounterElement
	}
	return nil
}

type ItcTitleSubtitleElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Counter         *ui.IconTextComponent `protobuf:"bytes,1,opt,name=counter,proto3" json:"counter,omitempty"`
	Title           *common.Text          `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle        *common.Text          `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	BackgroundColor string                `protobuf:"bytes,4,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
}

func (x *ItcTitleSubtitleElement) Reset() {
	*x = ItcTitleSubtitleElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItcTitleSubtitleElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItcTitleSubtitleElement) ProtoMessage() {}

func (x *ItcTitleSubtitleElement) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItcTitleSubtitleElement.ProtoReflect.Descriptor instead.
func (*ItcTitleSubtitleElement) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescGZIP(), []int{5}
}

func (x *ItcTitleSubtitleElement) GetCounter() *ui.IconTextComponent {
	if x != nil {
		return x.Counter
	}
	return nil
}

func (x *ItcTitleSubtitleElement) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ItcTitleSubtitleElement) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *ItcTitleSubtitleElement) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

type SaClosureResolveIssuesScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header             *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FullScreenInfoView *info.FullScreenInfoView                   `protobuf:"bytes,2,opt,name=full_screen_info_view,json=fullScreenInfoView,proto3" json:"full_screen_info_view,omitempty"`
	// feedback selected by the user
	UserFeedbackText string `protobuf:"bytes,3,opt,name=user_feedback_text,json=userFeedbackText,proto3" json:"user_feedback_text,omitempty"`
	// additional info to be shown to the user
	AdditionalInfo *ui.KeyValuePair `protobuf:"bytes,4,opt,name=additional_info,json=additionalInfo,proto3" json:"additional_info,omitempty"`
	// text to be shown on top of cta
	BottomText *common.Text `protobuf:"bytes,5,opt,name=bottom_text,json=bottomText,proto3" json:"bottom_text,omitempty"`
}

func (x *SaClosureResolveIssuesScreenOptions) Reset() {
	*x = SaClosureResolveIssuesScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaClosureResolveIssuesScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaClosureResolveIssuesScreenOptions) ProtoMessage() {}

func (x *SaClosureResolveIssuesScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaClosureResolveIssuesScreenOptions.ProtoReflect.Descriptor instead.
func (*SaClosureResolveIssuesScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescGZIP(), []int{6}
}

func (x *SaClosureResolveIssuesScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SaClosureResolveIssuesScreenOptions) GetFullScreenInfoView() *info.FullScreenInfoView {
	if x != nil {
		return x.FullScreenInfoView
	}
	return nil
}

func (x *SaClosureResolveIssuesScreenOptions) GetUserFeedbackText() string {
	if x != nil {
		return x.UserFeedbackText
	}
	return ""
}

func (x *SaClosureResolveIssuesScreenOptions) GetAdditionalInfo() *ui.KeyValuePair {
	if x != nil {
		return x.AdditionalInfo
	}
	return nil
}

func (x *SaClosureResolveIssuesScreenOptions) GetBottomText() *common.Text {
	if x != nil {
		return x.BottomText
	}
	return nil
}

type SaClosureBenefitScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header     *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	EntryPoint enums.SAClosureRequestEntryPoint           `protobuf:"varint,2,opt,name=entry_point,json=entryPoint,proto3,enum=frontend.account.enums.SAClosureRequestEntryPoint" json:"entry_point,omitempty"`
}

func (x *SaClosureBenefitScreenOptions) Reset() {
	*x = SaClosureBenefitScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaClosureBenefitScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaClosureBenefitScreenOptions) ProtoMessage() {}

func (x *SaClosureBenefitScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaClosureBenefitScreenOptions.ProtoReflect.Descriptor instead.
func (*SaClosureBenefitScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescGZIP(), []int{7}
}

func (x *SaClosureBenefitScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SaClosureBenefitScreenOptions) GetEntryPoint() enums.SAClosureRequestEntryPoint {
	if x != nil {
		return x.EntryPoint
	}
	return enums.SAClosureRequestEntryPoint(0)
}

var File_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDesc = []byte{
	0x0a, 0x42, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2d, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f,
	0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65,
	0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x75, 0x69, 0x2f, 0x73, 0x77, 0x69, 0x70, 0x65, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xce, 0x02, 0x0a, 0x22, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x75, 0x0a, 0x15, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63,
	0x6b, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x75, 0x73, 0x65, 0x72, 0x46, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x03,
	0x63, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74,
	0x61, 0x52, 0x03, 0x63, 0x74, 0x61, 0x22, 0xe5, 0x01, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a,
	0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x33, 0x0a, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x66, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x3e,
	0x0a, 0x1b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x19, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x22, 0xaf,
	0x03, 0x0a, 0x18, 0x50, 0x61, 0x6e, 0x44, 0x6f, 0x62, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x07, 0x68,
	0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x38, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0a, 0x73,
	0x75, 0x62, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x3f, 0x0a, 0x1c, 0x70, 0x61, 0x6e,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x19, 0x70, 0x61, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x50,
	0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x15, 0x64, 0x6f,
	0x62, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x64, 0x6f, 0x62, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x35,
	0x0a, 0x0a, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x09, 0x73, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x43, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x22, 0xea, 0x02, 0x0a, 0x2e, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x77, 0x69, 0x70, 0x65,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x71, 0x0a, 0x14, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x12, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x53, 0x75,
	0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x73, 0x77, 0x69, 0x70, 0x65, 0x5f,
	0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x77,
	0x69, 0x70, 0x65, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x0b, 0x73, 0x77, 0x69, 0x70, 0x65,
	0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x0a, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x5f, 0x63, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43,
	0x74, 0x61, 0x52, 0x09, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x43, 0x74, 0x61, 0x22, 0xc2, 0x02,
	0x0a, 0x26, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x57, 0x0a, 0x15, 0x66, 0x75, 0x6c, 0x6c,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x76, 0x69, 0x65,
	0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x65, 0x77, 0x52, 0x12, 0x66,
	0x75, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x6f, 0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x49, 0x74, 0x63, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0e, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x22, 0xe7, 0x01, 0x0a, 0x17, 0x49, 0x74, 0x63, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x53,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3b,
	0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x73,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xfe, 0x02, 0x0a,
	0x23, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x49, 0x73, 0x73, 0x75, 0x65, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x57, 0x0a, 0x15, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x65, 0x77, 0x52, 0x12, 0x66, 0x75, 0x6c, 0x6c, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x65, 0x77, 0x12, 0x2c, 0x0a,
	0x12, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x75, 0x73, 0x65, 0x72, 0x46,
	0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x65, 0x78, 0x74, 0x12, 0x45, 0x0a, 0x0f, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61,
	0x69, 0x72, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x39, 0x0a, 0x0b, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x0a, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x54, 0x65, 0x78, 0x74, 0x22, 0xc4, 0x01,
	0x0a, 0x1d, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x53, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x53, 0x41,
	0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x42, 0x8e, 0x01, 0x0a, 0x44, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x50, 0x01, 0x5a,
	0x44, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x61, 0x5f, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_goTypes = []interface{}{
	(*SaClosureUserFeedbackScreenOptions)(nil),             // 0: api.typesv2.deeplink_screen_option.sa_closure.SaClosureUserFeedbackScreenOptions
	(*UserFeedbackOption)(nil),                             // 1: api.typesv2.deeplink_screen_option.sa_closure.UserFeedbackOption
	(*PanDobInputScreenOptions)(nil),                       // 2: api.typesv2.deeplink_screen_option.sa_closure.PanDobInputScreenOptions
	(*SaClosureSubmitRequestSwipeActionScreenOptions)(nil), // 3: api.typesv2.deeplink_screen_option.sa_closure.SaClosureSubmitRequestSwipeActionScreenOptions
	(*SaClosureRequestSubmittedScreenOptions)(nil),         // 4: api.typesv2.deeplink_screen_option.sa_closure.SaClosureRequestSubmittedScreenOptions
	(*ItcTitleSubtitleElement)(nil),                        // 5: api.typesv2.deeplink_screen_option.sa_closure.ItcTitleSubtitleElement
	(*SaClosureResolveIssuesScreenOptions)(nil),            // 6: api.typesv2.deeplink_screen_option.sa_closure.SaClosureResolveIssuesScreenOptions
	(*SaClosureBenefitScreenOptions)(nil),                  // 7: api.typesv2.deeplink_screen_option.sa_closure.SaClosureBenefitScreenOptions
	(*deeplink_screen_option.ScreenOptionHeader)(nil),      // 8: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*common.Text)(nil),                                    // 9: api.typesv2.common.Text
	(*deeplink.Cta)(nil),                                   // 10: frontend.deeplink.Cta
	(*deeplink.Deeplink)(nil),                              // 11: frontend.deeplink.Deeplink
	(*widget.VisualElementTitleSubtitleElement)(nil),       // 12: api.typesv2.common.ui.widget.VisualElementTitleSubtitleElement
	(*ui.SwipeButton)(nil),                                 // 13: api.typesv2.ui.SwipeButton
	(*info.FullScreenInfoView)(nil),                        // 14: api.typesv2.info.FullScreenInfoView
	(*ui.IconTextComponent)(nil),                           // 15: api.typesv2.ui.IconTextComponent
	(*ui.KeyValuePair)(nil),                                // 16: api.typesv2.ui.KeyValuePair
	(enums.SAClosureRequestEntryPoint)(0),                  // 17: frontend.account.enums.SAClosureRequestEntryPoint
}
var file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_depIdxs = []int32{
	8,  // 0: api.typesv2.deeplink_screen_option.sa_closure.SaClosureUserFeedbackScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	9,  // 1: api.typesv2.deeplink_screen_option.sa_closure.SaClosureUserFeedbackScreenOptions.page_title:type_name -> api.typesv2.common.Text
	1,  // 2: api.typesv2.deeplink_screen_option.sa_closure.SaClosureUserFeedbackScreenOptions.user_feedback_options:type_name -> api.typesv2.deeplink_screen_option.sa_closure.UserFeedbackOption
	10, // 3: api.typesv2.deeplink_screen_option.sa_closure.SaClosureUserFeedbackScreenOptions.cta:type_name -> frontend.deeplink.Cta
	9,  // 4: api.typesv2.deeplink_screen_option.sa_closure.UserFeedbackOption.text:type_name -> api.typesv2.common.Text
	11, // 5: api.typesv2.deeplink_screen_option.sa_closure.UserFeedbackOption.action:type_name -> frontend.deeplink.Deeplink
	8,  // 6: api.typesv2.deeplink_screen_option.sa_closure.PanDobInputScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	9,  // 7: api.typesv2.deeplink_screen_option.sa_closure.PanDobInputScreenOptions.heading:type_name -> api.typesv2.common.Text
	9,  // 8: api.typesv2.deeplink_screen_option.sa_closure.PanDobInputScreenOptions.subheading:type_name -> api.typesv2.common.Text
	10, // 9: api.typesv2.deeplink_screen_option.sa_closure.PanDobInputScreenOptions.submit_cta:type_name -> frontend.deeplink.Cta
	8,  // 10: api.typesv2.deeplink_screen_option.sa_closure.SaClosureSubmitRequestSwipeActionScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	12, // 11: api.typesv2.deeplink_screen_option.sa_closure.SaClosureSubmitRequestSwipeActionScreenOptions.image_title_subtitle:type_name -> api.typesv2.common.ui.widget.VisualElementTitleSubtitleElement
	13, // 12: api.typesv2.deeplink_screen_option.sa_closure.SaClosureSubmitRequestSwipeActionScreenOptions.swipe_button:type_name -> api.typesv2.ui.SwipeButton
	10, // 13: api.typesv2.deeplink_screen_option.sa_closure.SaClosureSubmitRequestSwipeActionScreenOptions.cancel_cta:type_name -> frontend.deeplink.Cta
	8,  // 14: api.typesv2.deeplink_screen_option.sa_closure.SaClosureRequestSubmittedScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	14, // 15: api.typesv2.deeplink_screen_option.sa_closure.SaClosureRequestSubmittedScreenOptions.full_screen_info_view:type_name -> api.typesv2.info.FullScreenInfoView
	5,  // 16: api.typesv2.deeplink_screen_option.sa_closure.SaClosureRequestSubmittedScreenOptions.counter_element:type_name -> api.typesv2.deeplink_screen_option.sa_closure.ItcTitleSubtitleElement
	15, // 17: api.typesv2.deeplink_screen_option.sa_closure.ItcTitleSubtitleElement.counter:type_name -> api.typesv2.ui.IconTextComponent
	9,  // 18: api.typesv2.deeplink_screen_option.sa_closure.ItcTitleSubtitleElement.title:type_name -> api.typesv2.common.Text
	9,  // 19: api.typesv2.deeplink_screen_option.sa_closure.ItcTitleSubtitleElement.subtitle:type_name -> api.typesv2.common.Text
	8,  // 20: api.typesv2.deeplink_screen_option.sa_closure.SaClosureResolveIssuesScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	14, // 21: api.typesv2.deeplink_screen_option.sa_closure.SaClosureResolveIssuesScreenOptions.full_screen_info_view:type_name -> api.typesv2.info.FullScreenInfoView
	16, // 22: api.typesv2.deeplink_screen_option.sa_closure.SaClosureResolveIssuesScreenOptions.additional_info:type_name -> api.typesv2.ui.KeyValuePair
	9,  // 23: api.typesv2.deeplink_screen_option.sa_closure.SaClosureResolveIssuesScreenOptions.bottom_text:type_name -> api.typesv2.common.Text
	8,  // 24: api.typesv2.deeplink_screen_option.sa_closure.SaClosureBenefitScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	17, // 25: api.typesv2.deeplink_screen_option.sa_closure.SaClosureBenefitScreenOptions.entry_point:type_name -> frontend.account.enums.SAClosureRequestEntryPoint
	26, // [26:26] is the sub-list for method output_type
	26, // [26:26] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_init() }
func file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaClosureUserFeedbackScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserFeedbackOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PanDobInputScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaClosureSubmitRequestSwipeActionScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaClosureRequestSubmittedScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItcTitleSubtitleElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaClosureResolveIssuesScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaClosureBenefitScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_depIdxs,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_sa_closure_screen_options_proto_depIdxs = nil
}
