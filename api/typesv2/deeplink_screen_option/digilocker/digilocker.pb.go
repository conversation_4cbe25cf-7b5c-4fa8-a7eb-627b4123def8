// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/digilocker/digilocker.proto

package digilocker

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InitiateDigilockerAuthScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *InitiateDigilockerAuthScreenOptions) Reset() {
	*x = InitiateDigilockerAuthScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateDigilockerAuthScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateDigilockerAuthScreenOptions) ProtoMessage() {}

func (x *InitiateDigilockerAuthScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateDigilockerAuthScreenOptions.ProtoReflect.Descriptor instead.
func (*InitiateDigilockerAuthScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDescGZIP(), []int{0}
}

func (x *InitiateDigilockerAuthScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

type DigilockerIntroScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Digilocker Image
	HeaderImage *common.VisualElement `protobuf:"bytes,2,opt,name=header_image,json=headerImage,proto3" json:"header_image,omitempty"`
	// Title - Ex : "Verify your identity instantly with DigiLocker"
	Title *common.Text `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// Subtitle - Ex : "Your data is safe with us"
	SubTitle *ui.IconTextComponent `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// Process steps :
	// 1. Continue to your DigiLocker account
	// 2. Review and confirm your details
	// 3. Get verified instantly - no hassles!
	ProcessSteps []*ui.IconTextComponent `protobuf:"bytes,5,rep,name=process_steps,json=processSteps,proto3" json:"process_steps,omitempty"` // Each step with icon and text
	// Action buttons using frontend.deeplink.Cta
	PrimaryCta   *ui.IconTextComponent `protobuf:"bytes,6,opt,name=primary_cta,json=primaryCta,proto3" json:"primary_cta,omitempty"`       // "Continue on Digilocker"
	SecondaryCta *ui.IconTextComponent `protobuf:"bytes,7,opt,name=secondary_cta,json=secondaryCta,proto3" json:"secondary_cta,omitempty"` // "Explore a different Method"
}

func (x *DigilockerIntroScreenOptions) Reset() {
	*x = DigilockerIntroScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DigilockerIntroScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DigilockerIntroScreenOptions) ProtoMessage() {}

func (x *DigilockerIntroScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DigilockerIntroScreenOptions.ProtoReflect.Descriptor instead.
func (*DigilockerIntroScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDescGZIP(), []int{1}
}

func (x *DigilockerIntroScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DigilockerIntroScreenOptions) GetHeaderImage() *common.VisualElement {
	if x != nil {
		return x.HeaderImage
	}
	return nil
}

func (x *DigilockerIntroScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *DigilockerIntroScreenOptions) GetSubTitle() *ui.IconTextComponent {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *DigilockerIntroScreenOptions) GetProcessSteps() []*ui.IconTextComponent {
	if x != nil {
		return x.ProcessSteps
	}
	return nil
}

func (x *DigilockerIntroScreenOptions) GetPrimaryCta() *ui.IconTextComponent {
	if x != nil {
		return x.PrimaryCta
	}
	return nil
}

func (x *DigilockerIntroScreenOptions) GetSecondaryCta() *ui.IconTextComponent {
	if x != nil {
		return x.SecondaryCta
	}
	return nil
}

var File_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x64, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x2f,
	0x64, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x2d, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x64, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x1a,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x75, 0x0a, 0x23, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x44, 0x69,
	0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xf8, 0x03, 0x0a, 0x1c, 0x44, 0x69,
	0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x0c, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x3e, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x46, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x65, 0x70,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x0a, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x0d,
	0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72,
	0x79, 0x43, 0x74, 0x61, 0x42, 0x8e, 0x01, 0x0a, 0x44, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x64, 0x69, 0x67, 0x69, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x50, 0x01, 0x5a,
	0x44, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x64, 0x69, 0x67, 0x69, 0x6c,
	0x6f, 0x63, 0x6b, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDescData = file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_goTypes = []interface{}{
	(*InitiateDigilockerAuthScreenOptions)(nil),       // 0: api.typesv2.deeplink_screen_option.digilocker.InitiateDigilockerAuthScreenOptions
	(*DigilockerIntroScreenOptions)(nil),              // 1: api.typesv2.deeplink_screen_option.digilocker.DigilockerIntroScreenOptions
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 2: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*common.VisualElement)(nil),                      // 3: api.typesv2.common.VisualElement
	(*common.Text)(nil),                               // 4: api.typesv2.common.Text
	(*ui.IconTextComponent)(nil),                      // 5: api.typesv2.ui.IconTextComponent
}
var file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_depIdxs = []int32{
	2, // 0: api.typesv2.deeplink_screen_option.digilocker.InitiateDigilockerAuthScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	2, // 1: api.typesv2.deeplink_screen_option.digilocker.DigilockerIntroScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	3, // 2: api.typesv2.deeplink_screen_option.digilocker.DigilockerIntroScreenOptions.header_image:type_name -> api.typesv2.common.VisualElement
	4, // 3: api.typesv2.deeplink_screen_option.digilocker.DigilockerIntroScreenOptions.title:type_name -> api.typesv2.common.Text
	5, // 4: api.typesv2.deeplink_screen_option.digilocker.DigilockerIntroScreenOptions.sub_title:type_name -> api.typesv2.ui.IconTextComponent
	5, // 5: api.typesv2.deeplink_screen_option.digilocker.DigilockerIntroScreenOptions.process_steps:type_name -> api.typesv2.ui.IconTextComponent
	5, // 6: api.typesv2.deeplink_screen_option.digilocker.DigilockerIntroScreenOptions.primary_cta:type_name -> api.typesv2.ui.IconTextComponent
	5, // 7: api.typesv2.deeplink_screen_option.digilocker.DigilockerIntroScreenOptions.secondary_cta:type_name -> api.typesv2.ui.IconTextComponent
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_init() }
func file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_init() {
	if File_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateDigilockerAuthScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DigilockerIntroScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_depIdxs,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto = out.File
	file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_digilocker_digilocker_proto_depIdxs = nil
}
