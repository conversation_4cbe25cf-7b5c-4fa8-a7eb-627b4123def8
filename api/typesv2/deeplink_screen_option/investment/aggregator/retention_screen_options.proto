syntax = "proto3";

package api.typesv2.deeplink_screen_option.investment.aggregator;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/frontend/investment/aggregator/retention_screen/retention_screen.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/investment/aggregator";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.investment.aggregator";
// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message RetentionScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;

  // params that needs to be passed as input to GetInvestmentRetentionScreen rpc.
  frontend.investment.aggregator.retention_screen.RetentionScreenParams params = 2;
}

