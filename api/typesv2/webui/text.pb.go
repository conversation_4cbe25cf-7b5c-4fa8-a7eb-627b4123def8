// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/webui/text.proto

package webui

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LabelValueV2_DataType int32

const (
	LabelValueV2_DATA_TYPE_UNSPECIFIED LabelValueV2_DataType = 0
	LabelValueV2_DATA_TYPE_STRING      LabelValueV2_DataType = 1
	LabelValueV2_DATA_TYPE_TABLE       LabelValueV2_DataType = 2
)

// Enum value maps for LabelValueV2_DataType.
var (
	LabelValueV2_DataType_name = map[int32]string{
		0: "DATA_TYPE_UNSPECIFIED",
		1: "DATA_TYPE_STRING",
		2: "DATA_TYPE_TABLE",
	}
	LabelValueV2_DataType_value = map[string]int32{
		"DATA_TYPE_UNSPECIFIED": 0,
		"DATA_TYPE_STRING":      1,
		"DATA_TYPE_TABLE":       2,
	}
)

func (x LabelValueV2_DataType) Enum() *LabelValueV2_DataType {
	p := new(LabelValueV2_DataType)
	*p = x
	return p
}

func (x LabelValueV2_DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LabelValueV2_DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_webui_text_proto_enumTypes[0].Descriptor()
}

func (LabelValueV2_DataType) Type() protoreflect.EnumType {
	return &file_api_typesv2_webui_text_proto_enumTypes[0]
}

func (x LabelValueV2_DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LabelValueV2_DataType.Descriptor instead.
func (LabelValueV2_DataType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_webui_text_proto_rawDescGZIP(), []int{1, 0}
}

// LabelValue represents the key-value pair format to be shown on UI
type LabelValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	// value supports valid HTML strings as well so that BE can control minor UI elements like bold, hyperlinks, etc.
	Value []string `protobuf:"bytes,2,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *LabelValue) Reset() {
	*x = LabelValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_webui_text_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelValue) ProtoMessage() {}

func (x *LabelValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_webui_text_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelValue.ProtoReflect.Descriptor instead.
func (*LabelValue) Descriptor() ([]byte, []int) {
	return file_api_typesv2_webui_text_proto_rawDescGZIP(), []int{0}
}

func (x *LabelValue) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *LabelValue) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

type LabelValueV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label    string                `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	DataType LabelValueV2_DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=api.typesv2.webui.LabelValueV2_DataType" json:"data_type,omitempty"`
	// Types that are assignable to Value:
	//
	//	*LabelValueV2_StringValue
	//	*LabelValueV2_TableValue
	Value isLabelValueV2_Value `protobuf_oneof:"value"`
}

func (x *LabelValueV2) Reset() {
	*x = LabelValueV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_webui_text_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelValueV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelValueV2) ProtoMessage() {}

func (x *LabelValueV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_webui_text_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelValueV2.ProtoReflect.Descriptor instead.
func (*LabelValueV2) Descriptor() ([]byte, []int) {
	return file_api_typesv2_webui_text_proto_rawDescGZIP(), []int{1}
}

func (x *LabelValueV2) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *LabelValueV2) GetDataType() LabelValueV2_DataType {
	if x != nil {
		return x.DataType
	}
	return LabelValueV2_DATA_TYPE_UNSPECIFIED
}

func (m *LabelValueV2) GetValue() isLabelValueV2_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *LabelValueV2) GetStringValue() string {
	if x, ok := x.GetValue().(*LabelValueV2_StringValue); ok {
		return x.StringValue
	}
	return ""
}

func (x *LabelValueV2) GetTableValue() *Table {
	if x, ok := x.GetValue().(*LabelValueV2_TableValue); ok {
		return x.TableValue
	}
	return nil
}

type isLabelValueV2_Value interface {
	isLabelValueV2_Value()
}

type LabelValueV2_StringValue struct {
	StringValue string `protobuf:"bytes,3,opt,name=string_value,json=stringValue,proto3,oneof"`
}

type LabelValueV2_TableValue struct {
	TableValue *Table `protobuf:"bytes,4,opt,name=table_value,json=tableValue,proto3,oneof"`
}

func (*LabelValueV2_StringValue) isLabelValueV2_Value() {}

func (*LabelValueV2_TableValue) isLabelValueV2_Value() {}

// Section is a combination of title and list of key value pairs
type Section struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title       string          `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	LabelValues []*LabelValueV2 `protobuf:"bytes,2,rep,name=label_values,json=labelValues,proto3" json:"label_values,omitempty"`
}

func (x *Section) Reset() {
	*x = Section{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_webui_text_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Section) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Section) ProtoMessage() {}

func (x *Section) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_webui_text_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Section.ProtoReflect.Descriptor instead.
func (*Section) Descriptor() ([]byte, []int) {
	return file_api_typesv2_webui_text_proto_rawDescGZIP(), []int{2}
}

func (x *Section) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Section) GetLabelValues() []*LabelValueV2 {
	if x != nil {
		return x.LabelValues
	}
	return nil
}

var File_api_typesv2_webui_text_proto protoreflect.FileDescriptor

var file_api_typesv2_webui_text_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x77, 0x65,
	0x62, 0x75, 0x69, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75,
	0x69, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x77,
	0x65, 0x62, 0x75, 0x69, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x38, 0x0a, 0x0a, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xa8, 0x02, 0x0a, 0x0c, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x56, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x45, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x56, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3b, 0x0a,
	0x0b, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0a,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x50, 0x0a, 0x08, 0x44, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x42, 0x07, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x63, 0x0a, 0x07, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x56, 0x32, 0x52, 0x0b, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x77, 0x65, 0x62, 0x75, 0x69,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_webui_text_proto_rawDescOnce sync.Once
	file_api_typesv2_webui_text_proto_rawDescData = file_api_typesv2_webui_text_proto_rawDesc
)

func file_api_typesv2_webui_text_proto_rawDescGZIP() []byte {
	file_api_typesv2_webui_text_proto_rawDescOnce.Do(func() {
		file_api_typesv2_webui_text_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_webui_text_proto_rawDescData)
	})
	return file_api_typesv2_webui_text_proto_rawDescData
}

var file_api_typesv2_webui_text_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_webui_text_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_typesv2_webui_text_proto_goTypes = []interface{}{
	(LabelValueV2_DataType)(0), // 0: api.typesv2.webui.LabelValueV2.DataType
	(*LabelValue)(nil),         // 1: api.typesv2.webui.LabelValue
	(*LabelValueV2)(nil),       // 2: api.typesv2.webui.LabelValueV2
	(*Section)(nil),            // 3: api.typesv2.webui.Section
	(*Table)(nil),              // 4: api.typesv2.webui.Table
}
var file_api_typesv2_webui_text_proto_depIdxs = []int32{
	0, // 0: api.typesv2.webui.LabelValueV2.data_type:type_name -> api.typesv2.webui.LabelValueV2.DataType
	4, // 1: api.typesv2.webui.LabelValueV2.table_value:type_name -> api.typesv2.webui.Table
	2, // 2: api.typesv2.webui.Section.label_values:type_name -> api.typesv2.webui.LabelValueV2
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_typesv2_webui_text_proto_init() }
func file_api_typesv2_webui_text_proto_init() {
	if File_api_typesv2_webui_text_proto != nil {
		return
	}
	file_api_typesv2_webui_table_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_webui_text_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_webui_text_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelValueV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_webui_text_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Section); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_webui_text_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*LabelValueV2_StringValue)(nil),
		(*LabelValueV2_TableValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_webui_text_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_webui_text_proto_goTypes,
		DependencyIndexes: file_api_typesv2_webui_text_proto_depIdxs,
		EnumInfos:         file_api_typesv2_webui_text_proto_enumTypes,
		MessageInfos:      file_api_typesv2_webui_text_proto_msgTypes,
	}.Build()
	File_api_typesv2_webui_text_proto = out.File
	file_api_typesv2_webui_text_proto_rawDesc = nil
	file_api_typesv2_webui_text_proto_goTypes = nil
	file_api_typesv2_webui_text_proto_depIdxs = nil
}
