// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/form/field_value.proto

package form

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the values that have to be populated in the InputField
type FieldValueType int32

const (
	FieldValueType_FIELD_VALUE_TYPE_UNSPECIFIED FieldValueType = 0
	// maps to string value in FieldValue
	FieldValueType_FIELD_VALUE_TYPE_STRING FieldValueType = 1
	// maps to date value in FieldValue
	FieldValueType_FIELD_VALUE_TYPE_DATE FieldValueType = 2
	// maps to boolean value in FieldValue
	FieldValueType_FIELD_VALUE_TYPE_BOOLEAN FieldValueType = 3
	// maps to map_value in FieldValue
	FieldValueType_FIELD_VALUE_TYPE_MAP FieldValueType = 4
)

// Enum value maps for FieldValueType.
var (
	FieldValueType_name = map[int32]string{
		0: "FIELD_VALUE_TYPE_UNSPECIFIED",
		1: "FIELD_VALUE_TYPE_STRING",
		2: "FIELD_VALUE_TYPE_DATE",
		3: "FIELD_VALUE_TYPE_BOOLEAN",
		4: "FIELD_VALUE_TYPE_MAP",
	}
	FieldValueType_value = map[string]int32{
		"FIELD_VALUE_TYPE_UNSPECIFIED": 0,
		"FIELD_VALUE_TYPE_STRING":      1,
		"FIELD_VALUE_TYPE_DATE":        2,
		"FIELD_VALUE_TYPE_BOOLEAN":     3,
		"FIELD_VALUE_TYPE_MAP":         4,
	}
)

func (x FieldValueType) Enum() *FieldValueType {
	p := new(FieldValueType)
	*p = x
	return p
}

func (x FieldValueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FieldValueType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_form_field_value_proto_enumTypes[0].Descriptor()
}

func (FieldValueType) Type() protoreflect.EnumType {
	return &file_api_typesv2_form_field_value_proto_enumTypes[0]
}

func (x FieldValueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FieldValueType.Descriptor instead.
func (FieldValueType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_form_field_value_proto_rawDescGZIP(), []int{0}
}

type FieldValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type FieldValueType `protobuf:"varint,1,opt,name=type,proto3,enum=api.typesv2.form.FieldValueType" json:"type,omitempty"`
	// 1:1 mapping with FieldValueType
	//
	// Types that are assignable to Value:
	//
	//	*FieldValue_StringValue
	//	*FieldValue_DateValue
	//	*FieldValue_BooleanValue
	//	*FieldValue_MapValue
	Value isFieldValue_Value `protobuf_oneof:"value"`
}

func (x *FieldValue) Reset() {
	*x = FieldValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_form_field_value_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldValue) ProtoMessage() {}

func (x *FieldValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_form_field_value_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldValue.ProtoReflect.Descriptor instead.
func (*FieldValue) Descriptor() ([]byte, []int) {
	return file_api_typesv2_form_field_value_proto_rawDescGZIP(), []int{0}
}

func (x *FieldValue) GetType() FieldValueType {
	if x != nil {
		return x.Type
	}
	return FieldValueType_FIELD_VALUE_TYPE_UNSPECIFIED
}

func (m *FieldValue) GetValue() isFieldValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *FieldValue) GetStringValue() string {
	if x, ok := x.GetValue().(*FieldValue_StringValue); ok {
		return x.StringValue
	}
	return ""
}

func (x *FieldValue) GetDateValue() *typesv2.Date {
	if x, ok := x.GetValue().(*FieldValue_DateValue); ok {
		return x.DateValue
	}
	return nil
}

func (x *FieldValue) GetBooleanValue() common.BooleanEnum {
	if x, ok := x.GetValue().(*FieldValue_BooleanValue); ok {
		return x.BooleanValue
	}
	return common.BooleanEnum(0)
}

func (x *FieldValue) GetMapValue() *Map {
	if x, ok := x.GetValue().(*FieldValue_MapValue); ok {
		return x.MapValue
	}
	return nil
}

type isFieldValue_Value interface {
	isFieldValue_Value()
}

type FieldValue_StringValue struct {
	StringValue string `protobuf:"bytes,2,opt,name=string_value,json=stringValue,proto3,oneof"`
}

type FieldValue_DateValue struct {
	DateValue *typesv2.Date `protobuf:"bytes,3,opt,name=date_value,json=dateValue,proto3,oneof"`
}

type FieldValue_BooleanValue struct {
	BooleanValue common.BooleanEnum `protobuf:"varint,4,opt,name=boolean_value,json=booleanValue,proto3,enum=api.typesv2.common.BooleanEnum,oneof"`
}

type FieldValue_MapValue struct {
	MapValue *Map `protobuf:"bytes,5,opt,name=map_value,json=mapValue,proto3,oneof"`
}

func (*FieldValue_StringValue) isFieldValue_Value() {}

func (*FieldValue_DateValue) isFieldValue_Value() {}

func (*FieldValue_BooleanValue) isFieldValue_Value() {}

func (*FieldValue_MapValue) isFieldValue_Value() {}

var File_api_typesv2_form_field_value_proto protoreflect.FileDescriptor

var file_api_typesv2_form_field_value_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x6d, 0x61, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa2, 0x02, 0x0a,
	0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x23, 0x0a, 0x0c, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x32, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52,
	0x09, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x46, 0x0a, 0x0d, 0x62, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x48, 0x00, 0x52, 0x0c, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x6d, 0x61, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x4d, 0x61, 0x70, 0x48, 0x00, 0x52, 0x08,
	0x6d, 0x61, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x2a, 0xa2, 0x01, 0x0a, 0x0e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x56, 0x41,
	0x4c, 0x55, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x56, 0x41, 0x4c,
	0x55, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x02, 0x12, 0x1c,
	0x0a, 0x18, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4c, 0x45, 0x41, 0x4e, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4d, 0x41, 0x50, 0x10, 0x04, 0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x66, 0x6f, 0x72,
	0x6d, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_typesv2_form_field_value_proto_rawDescOnce sync.Once
	file_api_typesv2_form_field_value_proto_rawDescData = file_api_typesv2_form_field_value_proto_rawDesc
)

func file_api_typesv2_form_field_value_proto_rawDescGZIP() []byte {
	file_api_typesv2_form_field_value_proto_rawDescOnce.Do(func() {
		file_api_typesv2_form_field_value_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_form_field_value_proto_rawDescData)
	})
	return file_api_typesv2_form_field_value_proto_rawDescData
}

var file_api_typesv2_form_field_value_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_form_field_value_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_typesv2_form_field_value_proto_goTypes = []interface{}{
	(FieldValueType)(0),     // 0: api.typesv2.form.FieldValueType
	(*FieldValue)(nil),      // 1: api.typesv2.form.FieldValue
	(*typesv2.Date)(nil),    // 2: api.typesv2.Date
	(common.BooleanEnum)(0), // 3: api.typesv2.common.BooleanEnum
	(*Map)(nil),             // 4: api.typesv2.form.Map
}
var file_api_typesv2_form_field_value_proto_depIdxs = []int32{
	0, // 0: api.typesv2.form.FieldValue.type:type_name -> api.typesv2.form.FieldValueType
	2, // 1: api.typesv2.form.FieldValue.date_value:type_name -> api.typesv2.Date
	3, // 2: api.typesv2.form.FieldValue.boolean_value:type_name -> api.typesv2.common.BooleanEnum
	4, // 3: api.typesv2.form.FieldValue.map_value:type_name -> api.typesv2.form.Map
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_typesv2_form_field_value_proto_init() }
func file_api_typesv2_form_field_value_proto_init() {
	if File_api_typesv2_form_field_value_proto != nil {
		return
	}
	file_api_typesv2_form_map_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_form_field_value_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_form_field_value_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*FieldValue_StringValue)(nil),
		(*FieldValue_DateValue)(nil),
		(*FieldValue_BooleanValue)(nil),
		(*FieldValue_MapValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_form_field_value_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_form_field_value_proto_goTypes,
		DependencyIndexes: file_api_typesv2_form_field_value_proto_depIdxs,
		EnumInfos:         file_api_typesv2_form_field_value_proto_enumTypes,
		MessageInfos:      file_api_typesv2_form_field_value_proto_msgTypes,
	}.Build()
	File_api_typesv2_form_field_value_proto = out.File
	file_api_typesv2_form_field_value_proto_rawDesc = nil
	file_api_typesv2_form_field_value_proto_goTypes = nil
	file_api_typesv2_form_field_value_proto_depIdxs = nil
}
