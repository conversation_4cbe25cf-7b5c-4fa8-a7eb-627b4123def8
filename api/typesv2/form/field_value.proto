syntax = "proto3";

package api.typesv2.form;

import "api/typesv2/common/boolean.proto";
import "api/typesv2/date.proto";
import "api/typesv2/form/map.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/form";
option java_package = "com.github.epifi.gamma.api.typesv2.form";

// Represents the values that have to be populated in the InputField
enum FieldValueType {
  FIELD_VALUE_TYPE_UNSPECIFIED = 0;
  // maps to string value in FieldValue
  FIELD_VALUE_TYPE_STRING = 1;
  // maps to date value in FieldValue
  FIELD_VALUE_TYPE_DATE = 2;
  // maps to boolean value in FieldValue
  FIELD_VALUE_TYPE_BOOLEAN = 3;
  // maps to map_value in FieldValue
  FIELD_VALUE_TYPE_MAP = 4;
}

message FieldValue {
  FieldValueType type = 1;
  // 1:1 mapping with FieldValueType
  oneof value {
    string string_value = 2;
    typesv2.Date date_value = 3;
    typesv2.common.BooleanEnum boolean_value = 4;
    Map map_value = 5;
  }
}
