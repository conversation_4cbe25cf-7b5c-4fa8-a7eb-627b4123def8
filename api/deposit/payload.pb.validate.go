// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/deposit/payload.proto

package deposit

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on DepositInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DepositInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DepositInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DepositInfoMultiError, or
// nil if none found.
func (m *DepositInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DepositInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Name

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositInfoValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTerm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositInfoValidationError{
					field:  "Term",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositInfoValidationError{
					field:  "Term",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTerm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositInfoValidationError{
				field:  "Term",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRenewInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositInfoValidationError{
					field:  "RenewInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositInfoValidationError{
					field:  "RenewInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRenewInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositInfoValidationError{
				field:  "RenewInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OperativeAccountNumber

	// no validation rules for RepayAccountNumber

	// no validation rules for Vendor

	// no validation rules for InterestPayout

	// no validation rules for AccountId

	// no validation rules for RequestType

	if all {
		switch v := interface{}(m.GetNomineeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositInfoValidationError{
					field:  "NomineeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositInfoValidationError{
					field:  "NomineeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNomineeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositInfoValidationError{
				field:  "NomineeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailedDepositRequestId

	// no validation rules for DepositAccountProvenance

	// no validation rules for TemplateId

	// no validation rules for GoalId

	// no validation rules for SchemeCode

	if all {
		switch v := interface{}(m.GetAutoSaveParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositInfoValidationError{
					field:  "AutoSaveParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositInfoValidationError{
					field:  "AutoSaveParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAutoSaveParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositInfoValidationError{
				field:  "AutoSaveParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DepositInfoMultiError(errors)
	}

	return nil
}

// DepositInfoMultiError is an error wrapping multiple validation errors
// returned by DepositInfo.ValidateAll() if the designated constraints aren't met.
type DepositInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DepositInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DepositInfoMultiError) AllErrors() []error { return m }

// DepositInfoValidationError is the validation error returned by
// DepositInfo.Validate if the designated constraints aren't met.
type DepositInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DepositInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DepositInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DepositInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DepositInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DepositInfoValidationError) ErrorName() string { return "DepositInfoValidationError" }

// Error satisfies the builtin error interface
func (e DepositInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDepositInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DepositInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DepositInfoValidationError{}

// Validate checks the field values on DepositTemplate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DepositTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DepositTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DepositTemplateMultiError, or nil if none found.
func (m *DepositTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *DepositTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositTemplateValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositTemplateValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositTemplateValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsAmountEditable

	// no validation rules for Type

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetTerm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositTemplateValidationError{
					field:  "Term",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositTemplateValidationError{
					field:  "Term",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTerm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositTemplateValidationError{
				field:  "Term",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsTermEditable

	// no validation rules for RenewOption

	// no validation rules for InterestPayout

	// no validation rules for Tag

	// no validation rules for IconUrl

	// no validation rules for DepositTemplateType

	// no validation rules for Id

	// no validation rules for IsNameEditable

	if all {
		switch v := interface{}(m.GetBonusTemplateFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositTemplateValidationError{
					field:  "BonusTemplateFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositTemplateValidationError{
					field:  "BonusTemplateFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBonusTemplateFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositTemplateValidationError{
				field:  "BonusTemplateFields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsInterestPayoutEditable

	// no validation rules for SchemeCode

	// no validation rules for DisclaimerText

	if all {
		switch v := interface{}(m.GetDetailsCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositTemplateValidationError{
					field:  "DetailsCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositTemplateValidationError{
					field:  "DetailsCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailsCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositTemplateValidationError{
				field:  "DetailsCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositTemplateValidationError{
					field:  "ConsentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositTemplateValidationError{
					field:  "ConsentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositTemplateValidationError{
				field:  "ConsentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsMaturityActionDisabled

	if len(errors) > 0 {
		return DepositTemplateMultiError(errors)
	}

	return nil
}

// DepositTemplateMultiError is an error wrapping multiple validation errors
// returned by DepositTemplate.ValidateAll() if the designated constraints
// aren't met.
type DepositTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DepositTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DepositTemplateMultiError) AllErrors() []error { return m }

// DepositTemplateValidationError is the validation error returned by
// DepositTemplate.Validate if the designated constraints aren't met.
type DepositTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DepositTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DepositTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DepositTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DepositTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DepositTemplateValidationError) ErrorName() string { return "DepositTemplateValidationError" }

// Error satisfies the builtin error interface
func (e DepositTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDepositTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DepositTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DepositTemplateValidationError{}

// Validate checks the field values on DepositTemplate_BonusTemplateFields with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DepositTemplate_BonusTemplateFields) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DepositTemplate_BonusTemplateFields
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DepositTemplate_BonusTemplateFieldsMultiError, or nil if none found.
func (m *DepositTemplate_BonusTemplateFields) ValidateAll() error {
	return m.validate(true)
}

func (m *DepositTemplate_BonusTemplateFields) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExtraInterest

	if len(errors) > 0 {
		return DepositTemplate_BonusTemplateFieldsMultiError(errors)
	}

	return nil
}

// DepositTemplate_BonusTemplateFieldsMultiError is an error wrapping multiple
// validation errors returned by
// DepositTemplate_BonusTemplateFields.ValidateAll() if the designated
// constraints aren't met.
type DepositTemplate_BonusTemplateFieldsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DepositTemplate_BonusTemplateFieldsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DepositTemplate_BonusTemplateFieldsMultiError) AllErrors() []error { return m }

// DepositTemplate_BonusTemplateFieldsValidationError is the validation error
// returned by DepositTemplate_BonusTemplateFields.Validate if the designated
// constraints aren't met.
type DepositTemplate_BonusTemplateFieldsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DepositTemplate_BonusTemplateFieldsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DepositTemplate_BonusTemplateFieldsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DepositTemplate_BonusTemplateFieldsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DepositTemplate_BonusTemplateFieldsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DepositTemplate_BonusTemplateFieldsValidationError) ErrorName() string {
	return "DepositTemplate_BonusTemplateFieldsValidationError"
}

// Error satisfies the builtin error interface
func (e DepositTemplate_BonusTemplateFieldsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDepositTemplate_BonusTemplateFields.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DepositTemplate_BonusTemplateFieldsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DepositTemplate_BonusTemplateFieldsValidationError{}
