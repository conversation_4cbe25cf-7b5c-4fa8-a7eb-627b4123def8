// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/securities/catalog/service.proto

package catalog

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SecuritiesCatalog_GetSecurity_FullMethodName                    = "/api.securities.catalog.SecuritiesCatalog/GetSecurity"
	SecuritiesCatalog_GetSecurities_FullMethodName                  = "/api.securities.catalog.SecuritiesCatalog/GetSecurities"
	SecuritiesCatalog_GetSecurityListing_FullMethodName             = "/api.securities.catalog.SecuritiesCatalog/GetSecurityListing"
	SecuritiesCatalog_GetSecurityListings_FullMethodName            = "/api.securities.catalog.SecuritiesCatalog/GetSecurityListings"
	SecuritiesCatalog_GetPriceByDateAndSecListingIDs_FullMethodName = "/api.securities.catalog.SecuritiesCatalog/GetPriceByDateAndSecListingIDs"
	SecuritiesCatalog_GetSecListingIdsByISINs_FullMethodName        = "/api.securities.catalog.SecuritiesCatalog/GetSecListingIdsByISINs"
	SecuritiesCatalog_AddSecurityWithISINs_FullMethodName           = "/api.securities.catalog.SecuritiesCatalog/AddSecurityWithISINs"
)

// SecuritiesCatalogClient is the client API for SecuritiesCatalog service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SecuritiesCatalogClient interface {
	// Retrieves market information about a single security using its ID.
	// This includes company details, financials, internal status for a equity security and ETF Details for an ETF.
	//
	// Use this when you need full context on the underlying security (e.g., for display, analysis, or enrichment).
	//
	// Typical use cases:
	// - Display security page with full company profile and financial metrics
	GetSecurity(ctx context.Context, in *GetSecurityRequest, opts ...grpc.CallOption) (*GetSecurityResponse, error)
	// Retrieves market information about a multiple securities using thiers IDs.
	// This includes company details, financials, internal status for a equity security and ETF Details for an ETF.
	//
	// Use this when you need full context on the underlying security (e.g., for display, analysis, or enrichment).
	//
	// Typical use cases:
	// - Display security pages with full company profile and financial metrics
	GetSecurities(ctx context.Context, in *GetSecuritiesRequest, opts ...grpc.CallOption) (*GetSecuritiesResponse, error)
	// Fetches a specific security listing (i.e., how a security is traded on a specific exchange).
	// Listings include stock symbols, trading item IDs, price data, and exchange-level details.
	//
	// Use this when working with exchange-level trading data such as tickers, ISIN mappings,
	// or historical price series.
	//
	// Typical use cases:
	// - Fetch stock symbol and exchange metadata
	// - Fetch exchange-level historical pricing, mcap etc.
	GetSecurityListing(ctx context.Context, in *GetSecurityListingRequest, opts ...grpc.CallOption) (*GetSecurityListingResponse, error)
	// Fetches security listings in bulk (i.e., how a security is traded on a specific exchange).
	// Listings include stock symbols, trading item IDs, price data, and exchange-level details.
	//
	// Use this when working with exchange-level trading data such as tickers, ISIN mappings,
	// or historical price series.
	//
	// Typical use cases:
	// - Fetch stock symbol and exchange metadata
	// - Fetch exchange-level historical pricing, mcap etc.
	GetSecurityListings(ctx context.Context, in *GetSecurityListingsRequest, opts ...grpc.CallOption) (*GetSecurityListingsResponse, error)
	// Retrieves historical price data for securities on a specified date.
	// This includes closing prices, vendor information, and relevant timestamps.
	//
	// Use this when you need historical price information for securities on a specific date.
	//
	// Typical use cases:
	// - Display historical price charts
	// - Calculate historical returns
	// - Analyze price movements
	GetPriceByDateAndSecListingIDs(ctx context.Context, in *GetPriceByDateAndSecListingIDsRequest, opts ...grpc.CallOption) (*GetPriceByDateAndSecListingIDsResponse, error)
	// Retrieves security listing IDs for given ISINs and exchanges.
	// This helps in mapping ISINs to their specific exchange listing IDs.
	//
	// Use this when you need to get the security listing IDs for specific exchanges using ISINs.
	//
	// Typical use cases:
	// - Map multiple ISINs to exchange-specific listing IDs
	// - Handle different exchange listings (NSE/BSE) for multiple securities
	GetSecListingIdsByISINs(ctx context.Context, in *GetSecListingIdsByISINsRequest, opts ...grpc.CallOption) (*GetSecListingIdsByISINsResponse, error)
	// AddSecurityWithISINs attempts to ensure that each ISIN in the request is correctly mapped to a security.
	//
	// For each ISIN in the request
	// - The RPC fetches security data from the vendor.
	// - If a matching security already exists in the system, it creates a mapping between the ISIN and that security.
	// - If no matching security exists, it creates both a new security record and a new security listing record.
	//
	// Use this when you need to add mapping for a security/listing with an ISIN
	AddSecurityWithISINs(ctx context.Context, in *AddSecurityWithISINsRequest, opts ...grpc.CallOption) (*AddSecurityWithISINsResponse, error)
}

type securitiesCatalogClient struct {
	cc grpc.ClientConnInterface
}

func NewSecuritiesCatalogClient(cc grpc.ClientConnInterface) SecuritiesCatalogClient {
	return &securitiesCatalogClient{cc}
}

func (c *securitiesCatalogClient) GetSecurity(ctx context.Context, in *GetSecurityRequest, opts ...grpc.CallOption) (*GetSecurityResponse, error) {
	out := new(GetSecurityResponse)
	err := c.cc.Invoke(ctx, SecuritiesCatalog_GetSecurity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securitiesCatalogClient) GetSecurities(ctx context.Context, in *GetSecuritiesRequest, opts ...grpc.CallOption) (*GetSecuritiesResponse, error) {
	out := new(GetSecuritiesResponse)
	err := c.cc.Invoke(ctx, SecuritiesCatalog_GetSecurities_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securitiesCatalogClient) GetSecurityListing(ctx context.Context, in *GetSecurityListingRequest, opts ...grpc.CallOption) (*GetSecurityListingResponse, error) {
	out := new(GetSecurityListingResponse)
	err := c.cc.Invoke(ctx, SecuritiesCatalog_GetSecurityListing_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securitiesCatalogClient) GetSecurityListings(ctx context.Context, in *GetSecurityListingsRequest, opts ...grpc.CallOption) (*GetSecurityListingsResponse, error) {
	out := new(GetSecurityListingsResponse)
	err := c.cc.Invoke(ctx, SecuritiesCatalog_GetSecurityListings_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securitiesCatalogClient) GetPriceByDateAndSecListingIDs(ctx context.Context, in *GetPriceByDateAndSecListingIDsRequest, opts ...grpc.CallOption) (*GetPriceByDateAndSecListingIDsResponse, error) {
	out := new(GetPriceByDateAndSecListingIDsResponse)
	err := c.cc.Invoke(ctx, SecuritiesCatalog_GetPriceByDateAndSecListingIDs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securitiesCatalogClient) GetSecListingIdsByISINs(ctx context.Context, in *GetSecListingIdsByISINsRequest, opts ...grpc.CallOption) (*GetSecListingIdsByISINsResponse, error) {
	out := new(GetSecListingIdsByISINsResponse)
	err := c.cc.Invoke(ctx, SecuritiesCatalog_GetSecListingIdsByISINs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securitiesCatalogClient) AddSecurityWithISINs(ctx context.Context, in *AddSecurityWithISINsRequest, opts ...grpc.CallOption) (*AddSecurityWithISINsResponse, error) {
	out := new(AddSecurityWithISINsResponse)
	err := c.cc.Invoke(ctx, SecuritiesCatalog_AddSecurityWithISINs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SecuritiesCatalogServer is the server API for SecuritiesCatalog service.
// All implementations should embed UnimplementedSecuritiesCatalogServer
// for forward compatibility
type SecuritiesCatalogServer interface {
	// Retrieves market information about a single security using its ID.
	// This includes company details, financials, internal status for a equity security and ETF Details for an ETF.
	//
	// Use this when you need full context on the underlying security (e.g., for display, analysis, or enrichment).
	//
	// Typical use cases:
	// - Display security page with full company profile and financial metrics
	GetSecurity(context.Context, *GetSecurityRequest) (*GetSecurityResponse, error)
	// Retrieves market information about a multiple securities using thiers IDs.
	// This includes company details, financials, internal status for a equity security and ETF Details for an ETF.
	//
	// Use this when you need full context on the underlying security (e.g., for display, analysis, or enrichment).
	//
	// Typical use cases:
	// - Display security pages with full company profile and financial metrics
	GetSecurities(context.Context, *GetSecuritiesRequest) (*GetSecuritiesResponse, error)
	// Fetches a specific security listing (i.e., how a security is traded on a specific exchange).
	// Listings include stock symbols, trading item IDs, price data, and exchange-level details.
	//
	// Use this when working with exchange-level trading data such as tickers, ISIN mappings,
	// or historical price series.
	//
	// Typical use cases:
	// - Fetch stock symbol and exchange metadata
	// - Fetch exchange-level historical pricing, mcap etc.
	GetSecurityListing(context.Context, *GetSecurityListingRequest) (*GetSecurityListingResponse, error)
	// Fetches security listings in bulk (i.e., how a security is traded on a specific exchange).
	// Listings include stock symbols, trading item IDs, price data, and exchange-level details.
	//
	// Use this when working with exchange-level trading data such as tickers, ISIN mappings,
	// or historical price series.
	//
	// Typical use cases:
	// - Fetch stock symbol and exchange metadata
	// - Fetch exchange-level historical pricing, mcap etc.
	GetSecurityListings(context.Context, *GetSecurityListingsRequest) (*GetSecurityListingsResponse, error)
	// Retrieves historical price data for securities on a specified date.
	// This includes closing prices, vendor information, and relevant timestamps.
	//
	// Use this when you need historical price information for securities on a specific date.
	//
	// Typical use cases:
	// - Display historical price charts
	// - Calculate historical returns
	// - Analyze price movements
	GetPriceByDateAndSecListingIDs(context.Context, *GetPriceByDateAndSecListingIDsRequest) (*GetPriceByDateAndSecListingIDsResponse, error)
	// Retrieves security listing IDs for given ISINs and exchanges.
	// This helps in mapping ISINs to their specific exchange listing IDs.
	//
	// Use this when you need to get the security listing IDs for specific exchanges using ISINs.
	//
	// Typical use cases:
	// - Map multiple ISINs to exchange-specific listing IDs
	// - Handle different exchange listings (NSE/BSE) for multiple securities
	GetSecListingIdsByISINs(context.Context, *GetSecListingIdsByISINsRequest) (*GetSecListingIdsByISINsResponse, error)
	// AddSecurityWithISINs attempts to ensure that each ISIN in the request is correctly mapped to a security.
	//
	// For each ISIN in the request
	// - The RPC fetches security data from the vendor.
	// - If a matching security already exists in the system, it creates a mapping between the ISIN and that security.
	// - If no matching security exists, it creates both a new security record and a new security listing record.
	//
	// Use this when you need to add mapping for a security/listing with an ISIN
	AddSecurityWithISINs(context.Context, *AddSecurityWithISINsRequest) (*AddSecurityWithISINsResponse, error)
}

// UnimplementedSecuritiesCatalogServer should be embedded to have forward compatible implementations.
type UnimplementedSecuritiesCatalogServer struct {
}

func (UnimplementedSecuritiesCatalogServer) GetSecurity(context.Context, *GetSecurityRequest) (*GetSecurityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecurity not implemented")
}
func (UnimplementedSecuritiesCatalogServer) GetSecurities(context.Context, *GetSecuritiesRequest) (*GetSecuritiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecurities not implemented")
}
func (UnimplementedSecuritiesCatalogServer) GetSecurityListing(context.Context, *GetSecurityListingRequest) (*GetSecurityListingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecurityListing not implemented")
}
func (UnimplementedSecuritiesCatalogServer) GetSecurityListings(context.Context, *GetSecurityListingsRequest) (*GetSecurityListingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecurityListings not implemented")
}
func (UnimplementedSecuritiesCatalogServer) GetPriceByDateAndSecListingIDs(context.Context, *GetPriceByDateAndSecListingIDsRequest) (*GetPriceByDateAndSecListingIDsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPriceByDateAndSecListingIDs not implemented")
}
func (UnimplementedSecuritiesCatalogServer) GetSecListingIdsByISINs(context.Context, *GetSecListingIdsByISINsRequest) (*GetSecListingIdsByISINsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecListingIdsByISINs not implemented")
}
func (UnimplementedSecuritiesCatalogServer) AddSecurityWithISINs(context.Context, *AddSecurityWithISINsRequest) (*AddSecurityWithISINsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddSecurityWithISINs not implemented")
}

// UnsafeSecuritiesCatalogServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SecuritiesCatalogServer will
// result in compilation errors.
type UnsafeSecuritiesCatalogServer interface {
	mustEmbedUnimplementedSecuritiesCatalogServer()
}

func RegisterSecuritiesCatalogServer(s grpc.ServiceRegistrar, srv SecuritiesCatalogServer) {
	s.RegisterService(&SecuritiesCatalog_ServiceDesc, srv)
}

func _SecuritiesCatalog_GetSecurity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecurityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecuritiesCatalogServer).GetSecurity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecuritiesCatalog_GetSecurity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecuritiesCatalogServer).GetSecurity(ctx, req.(*GetSecurityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecuritiesCatalog_GetSecurities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecuritiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecuritiesCatalogServer).GetSecurities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecuritiesCatalog_GetSecurities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecuritiesCatalogServer).GetSecurities(ctx, req.(*GetSecuritiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecuritiesCatalog_GetSecurityListing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecurityListingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecuritiesCatalogServer).GetSecurityListing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecuritiesCatalog_GetSecurityListing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecuritiesCatalogServer).GetSecurityListing(ctx, req.(*GetSecurityListingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecuritiesCatalog_GetSecurityListings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecurityListingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecuritiesCatalogServer).GetSecurityListings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecuritiesCatalog_GetSecurityListings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecuritiesCatalogServer).GetSecurityListings(ctx, req.(*GetSecurityListingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecuritiesCatalog_GetPriceByDateAndSecListingIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPriceByDateAndSecListingIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecuritiesCatalogServer).GetPriceByDateAndSecListingIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecuritiesCatalog_GetPriceByDateAndSecListingIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecuritiesCatalogServer).GetPriceByDateAndSecListingIDs(ctx, req.(*GetPriceByDateAndSecListingIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecuritiesCatalog_GetSecListingIdsByISINs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecListingIdsByISINsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecuritiesCatalogServer).GetSecListingIdsByISINs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecuritiesCatalog_GetSecListingIdsByISINs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecuritiesCatalogServer).GetSecListingIdsByISINs(ctx, req.(*GetSecListingIdsByISINsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecuritiesCatalog_AddSecurityWithISINs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSecurityWithISINsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecuritiesCatalogServer).AddSecurityWithISINs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecuritiesCatalog_AddSecurityWithISINs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecuritiesCatalogServer).AddSecurityWithISINs(ctx, req.(*AddSecurityWithISINsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SecuritiesCatalog_ServiceDesc is the grpc.ServiceDesc for SecuritiesCatalog service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SecuritiesCatalog_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.securities.catalog.SecuritiesCatalog",
	HandlerType: (*SecuritiesCatalogServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSecurity",
			Handler:    _SecuritiesCatalog_GetSecurity_Handler,
		},
		{
			MethodName: "GetSecurities",
			Handler:    _SecuritiesCatalog_GetSecurities_Handler,
		},
		{
			MethodName: "GetSecurityListing",
			Handler:    _SecuritiesCatalog_GetSecurityListing_Handler,
		},
		{
			MethodName: "GetSecurityListings",
			Handler:    _SecuritiesCatalog_GetSecurityListings_Handler,
		},
		{
			MethodName: "GetPriceByDateAndSecListingIDs",
			Handler:    _SecuritiesCatalog_GetPriceByDateAndSecListingIDs_Handler,
		},
		{
			MethodName: "GetSecListingIdsByISINs",
			Handler:    _SecuritiesCatalog_GetSecListingIdsByISINs_Handler,
		},
		{
			MethodName: "AddSecurityWithISINs",
			Handler:    _SecuritiesCatalog_AddSecurityWithISINs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/securities/catalog/service.proto",
}
