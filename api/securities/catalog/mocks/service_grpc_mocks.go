// Code generated by MockGen. DO NOT EDIT.
// Source: api/securities/catalog/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	catalog "github.com/epifi/gamma/api/securities/catalog"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockSecuritiesCatalogClient is a mock of SecuritiesCatalogClient interface.
type MockSecuritiesCatalogClient struct {
	ctrl     *gomock.Controller
	recorder *MockSecuritiesCatalogClientMockRecorder
}

// MockSecuritiesCatalogClientMockRecorder is the mock recorder for MockSecuritiesCatalogClient.
type MockSecuritiesCatalogClientMockRecorder struct {
	mock *MockSecuritiesCatalogClient
}

// NewMockSecuritiesCatalogClient creates a new mock instance.
func NewMockSecuritiesCatalogClient(ctrl *gomock.Controller) *MockSecuritiesCatalogClient {
	mock := &MockSecuritiesCatalogClient{ctrl: ctrl}
	mock.recorder = &MockSecuritiesCatalogClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSecuritiesCatalogClient) EXPECT() *MockSecuritiesCatalogClientMockRecorder {
	return m.recorder
}

// AddSecurityWithISINs mocks base method.
func (m *MockSecuritiesCatalogClient) AddSecurityWithISINs(ctx context.Context, in *catalog.AddSecurityWithISINsRequest, opts ...grpc.CallOption) (*catalog.AddSecurityWithISINsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddSecurityWithISINs", varargs...)
	ret0, _ := ret[0].(*catalog.AddSecurityWithISINsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSecurityWithISINs indicates an expected call of AddSecurityWithISINs.
func (mr *MockSecuritiesCatalogClientMockRecorder) AddSecurityWithISINs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSecurityWithISINs", reflect.TypeOf((*MockSecuritiesCatalogClient)(nil).AddSecurityWithISINs), varargs...)
}

// GetPriceByDateAndSecListingIDs mocks base method.
func (m *MockSecuritiesCatalogClient) GetPriceByDateAndSecListingIDs(ctx context.Context, in *catalog.GetPriceByDateAndSecListingIDsRequest, opts ...grpc.CallOption) (*catalog.GetPriceByDateAndSecListingIDsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPriceByDateAndSecListingIDs", varargs...)
	ret0, _ := ret[0].(*catalog.GetPriceByDateAndSecListingIDsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPriceByDateAndSecListingIDs indicates an expected call of GetPriceByDateAndSecListingIDs.
func (mr *MockSecuritiesCatalogClientMockRecorder) GetPriceByDateAndSecListingIDs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPriceByDateAndSecListingIDs", reflect.TypeOf((*MockSecuritiesCatalogClient)(nil).GetPriceByDateAndSecListingIDs), varargs...)
}

// GetSecListingIdsByISINs mocks base method.
func (m *MockSecuritiesCatalogClient) GetSecListingIdsByISINs(ctx context.Context, in *catalog.GetSecListingIdsByISINsRequest, opts ...grpc.CallOption) (*catalog.GetSecListingIdsByISINsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSecListingIdsByISINs", varargs...)
	ret0, _ := ret[0].(*catalog.GetSecListingIdsByISINsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecListingIdsByISINs indicates an expected call of GetSecListingIdsByISINs.
func (mr *MockSecuritiesCatalogClientMockRecorder) GetSecListingIdsByISINs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecListingIdsByISINs", reflect.TypeOf((*MockSecuritiesCatalogClient)(nil).GetSecListingIdsByISINs), varargs...)
}

// GetSecurities mocks base method.
func (m *MockSecuritiesCatalogClient) GetSecurities(ctx context.Context, in *catalog.GetSecuritiesRequest, opts ...grpc.CallOption) (*catalog.GetSecuritiesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSecurities", varargs...)
	ret0, _ := ret[0].(*catalog.GetSecuritiesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurities indicates an expected call of GetSecurities.
func (mr *MockSecuritiesCatalogClientMockRecorder) GetSecurities(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurities", reflect.TypeOf((*MockSecuritiesCatalogClient)(nil).GetSecurities), varargs...)
}

// GetSecurity mocks base method.
func (m *MockSecuritiesCatalogClient) GetSecurity(ctx context.Context, in *catalog.GetSecurityRequest, opts ...grpc.CallOption) (*catalog.GetSecurityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSecurity", varargs...)
	ret0, _ := ret[0].(*catalog.GetSecurityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurity indicates an expected call of GetSecurity.
func (mr *MockSecuritiesCatalogClientMockRecorder) GetSecurity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurity", reflect.TypeOf((*MockSecuritiesCatalogClient)(nil).GetSecurity), varargs...)
}

// GetSecurityListing mocks base method.
func (m *MockSecuritiesCatalogClient) GetSecurityListing(ctx context.Context, in *catalog.GetSecurityListingRequest, opts ...grpc.CallOption) (*catalog.GetSecurityListingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSecurityListing", varargs...)
	ret0, _ := ret[0].(*catalog.GetSecurityListingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurityListing indicates an expected call of GetSecurityListing.
func (mr *MockSecuritiesCatalogClientMockRecorder) GetSecurityListing(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurityListing", reflect.TypeOf((*MockSecuritiesCatalogClient)(nil).GetSecurityListing), varargs...)
}

// GetSecurityListings mocks base method.
func (m *MockSecuritiesCatalogClient) GetSecurityListings(ctx context.Context, in *catalog.GetSecurityListingsRequest, opts ...grpc.CallOption) (*catalog.GetSecurityListingsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSecurityListings", varargs...)
	ret0, _ := ret[0].(*catalog.GetSecurityListingsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurityListings indicates an expected call of GetSecurityListings.
func (mr *MockSecuritiesCatalogClientMockRecorder) GetSecurityListings(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurityListings", reflect.TypeOf((*MockSecuritiesCatalogClient)(nil).GetSecurityListings), varargs...)
}

// MockSecuritiesCatalogServer is a mock of SecuritiesCatalogServer interface.
type MockSecuritiesCatalogServer struct {
	ctrl     *gomock.Controller
	recorder *MockSecuritiesCatalogServerMockRecorder
}

// MockSecuritiesCatalogServerMockRecorder is the mock recorder for MockSecuritiesCatalogServer.
type MockSecuritiesCatalogServerMockRecorder struct {
	mock *MockSecuritiesCatalogServer
}

// NewMockSecuritiesCatalogServer creates a new mock instance.
func NewMockSecuritiesCatalogServer(ctrl *gomock.Controller) *MockSecuritiesCatalogServer {
	mock := &MockSecuritiesCatalogServer{ctrl: ctrl}
	mock.recorder = &MockSecuritiesCatalogServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSecuritiesCatalogServer) EXPECT() *MockSecuritiesCatalogServerMockRecorder {
	return m.recorder
}

// AddSecurityWithISINs mocks base method.
func (m *MockSecuritiesCatalogServer) AddSecurityWithISINs(arg0 context.Context, arg1 *catalog.AddSecurityWithISINsRequest) (*catalog.AddSecurityWithISINsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSecurityWithISINs", arg0, arg1)
	ret0, _ := ret[0].(*catalog.AddSecurityWithISINsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSecurityWithISINs indicates an expected call of AddSecurityWithISINs.
func (mr *MockSecuritiesCatalogServerMockRecorder) AddSecurityWithISINs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSecurityWithISINs", reflect.TypeOf((*MockSecuritiesCatalogServer)(nil).AddSecurityWithISINs), arg0, arg1)
}

// GetPriceByDateAndSecListingIDs mocks base method.
func (m *MockSecuritiesCatalogServer) GetPriceByDateAndSecListingIDs(arg0 context.Context, arg1 *catalog.GetPriceByDateAndSecListingIDsRequest) (*catalog.GetPriceByDateAndSecListingIDsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPriceByDateAndSecListingIDs", arg0, arg1)
	ret0, _ := ret[0].(*catalog.GetPriceByDateAndSecListingIDsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPriceByDateAndSecListingIDs indicates an expected call of GetPriceByDateAndSecListingIDs.
func (mr *MockSecuritiesCatalogServerMockRecorder) GetPriceByDateAndSecListingIDs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPriceByDateAndSecListingIDs", reflect.TypeOf((*MockSecuritiesCatalogServer)(nil).GetPriceByDateAndSecListingIDs), arg0, arg1)
}

// GetSecListingIdsByISINs mocks base method.
func (m *MockSecuritiesCatalogServer) GetSecListingIdsByISINs(arg0 context.Context, arg1 *catalog.GetSecListingIdsByISINsRequest) (*catalog.GetSecListingIdsByISINsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSecListingIdsByISINs", arg0, arg1)
	ret0, _ := ret[0].(*catalog.GetSecListingIdsByISINsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecListingIdsByISINs indicates an expected call of GetSecListingIdsByISINs.
func (mr *MockSecuritiesCatalogServerMockRecorder) GetSecListingIdsByISINs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecListingIdsByISINs", reflect.TypeOf((*MockSecuritiesCatalogServer)(nil).GetSecListingIdsByISINs), arg0, arg1)
}

// GetSecurities mocks base method.
func (m *MockSecuritiesCatalogServer) GetSecurities(arg0 context.Context, arg1 *catalog.GetSecuritiesRequest) (*catalog.GetSecuritiesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSecurities", arg0, arg1)
	ret0, _ := ret[0].(*catalog.GetSecuritiesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurities indicates an expected call of GetSecurities.
func (mr *MockSecuritiesCatalogServerMockRecorder) GetSecurities(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurities", reflect.TypeOf((*MockSecuritiesCatalogServer)(nil).GetSecurities), arg0, arg1)
}

// GetSecurity mocks base method.
func (m *MockSecuritiesCatalogServer) GetSecurity(arg0 context.Context, arg1 *catalog.GetSecurityRequest) (*catalog.GetSecurityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSecurity", arg0, arg1)
	ret0, _ := ret[0].(*catalog.GetSecurityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurity indicates an expected call of GetSecurity.
func (mr *MockSecuritiesCatalogServerMockRecorder) GetSecurity(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurity", reflect.TypeOf((*MockSecuritiesCatalogServer)(nil).GetSecurity), arg0, arg1)
}

// GetSecurityListing mocks base method.
func (m *MockSecuritiesCatalogServer) GetSecurityListing(arg0 context.Context, arg1 *catalog.GetSecurityListingRequest) (*catalog.GetSecurityListingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSecurityListing", arg0, arg1)
	ret0, _ := ret[0].(*catalog.GetSecurityListingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurityListing indicates an expected call of GetSecurityListing.
func (mr *MockSecuritiesCatalogServerMockRecorder) GetSecurityListing(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurityListing", reflect.TypeOf((*MockSecuritiesCatalogServer)(nil).GetSecurityListing), arg0, arg1)
}

// GetSecurityListings mocks base method.
func (m *MockSecuritiesCatalogServer) GetSecurityListings(arg0 context.Context, arg1 *catalog.GetSecurityListingsRequest) (*catalog.GetSecurityListingsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSecurityListings", arg0, arg1)
	ret0, _ := ret[0].(*catalog.GetSecurityListingsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurityListings indicates an expected call of GetSecurityListings.
func (mr *MockSecuritiesCatalogServerMockRecorder) GetSecurityListings(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurityListings", reflect.TypeOf((*MockSecuritiesCatalogServer)(nil).GetSecurityListings), arg0, arg1)
}

// MockUnsafeSecuritiesCatalogServer is a mock of UnsafeSecuritiesCatalogServer interface.
type MockUnsafeSecuritiesCatalogServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeSecuritiesCatalogServerMockRecorder
}

// MockUnsafeSecuritiesCatalogServerMockRecorder is the mock recorder for MockUnsafeSecuritiesCatalogServer.
type MockUnsafeSecuritiesCatalogServerMockRecorder struct {
	mock *MockUnsafeSecuritiesCatalogServer
}

// NewMockUnsafeSecuritiesCatalogServer creates a new mock instance.
func NewMockUnsafeSecuritiesCatalogServer(ctrl *gomock.Controller) *MockUnsafeSecuritiesCatalogServer {
	mock := &MockUnsafeSecuritiesCatalogServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeSecuritiesCatalogServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeSecuritiesCatalogServer) EXPECT() *MockUnsafeSecuritiesCatalogServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedSecuritiesCatalogServer mocks base method.
func (m *MockUnsafeSecuritiesCatalogServer) mustEmbedUnimplementedSecuritiesCatalogServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSecuritiesCatalogServer")
}

// mustEmbedUnimplementedSecuritiesCatalogServer indicates an expected call of mustEmbedUnimplementedSecuritiesCatalogServer.
func (mr *MockUnsafeSecuritiesCatalogServerMockRecorder) mustEmbedUnimplementedSecuritiesCatalogServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSecuritiesCatalogServer", reflect.TypeOf((*MockUnsafeSecuritiesCatalogServer)(nil).mustEmbedUnimplementedSecuritiesCatalogServer))
}
