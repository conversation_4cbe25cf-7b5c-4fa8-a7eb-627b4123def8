// Code generated by MockGen. DO NOT EDIT.
// Source: api/casbin/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	casbin "github.com/epifi/gamma/api/casbin"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCasbinClient is a mock of CasbinClient interface.
type MockCasbinClient struct {
	ctrl     *gomock.Controller
	recorder *MockCasbinClientMockRecorder
}

// MockCasbinClientMockRecorder is the mock recorder for MockCasbinClient.
type MockCasbinClientMockRecorder struct {
	mock *MockCasbinClient
}

// NewMockCasbinClient creates a new mock instance.
func NewMockCasbinClient(ctrl *gomock.Controller) *MockCasbinClient {
	mock := &MockCasbinClient{ctrl: ctrl}
	mock.recorder = &MockCasbinClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCasbinClient) EXPECT() *MockCasbinClientMockRecorder {
	return m.recorder
}

// AddGroupingPolicy mocks base method.
func (m *MockCasbinClient) AddGroupingPolicy(ctx context.Context, in *casbin.GroupingPolicyRequest, opts ...grpc.CallOption) (*casbin.GroupingPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddGroupingPolicy", varargs...)
	ret0, _ := ret[0].(*casbin.GroupingPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddGroupingPolicy indicates an expected call of AddGroupingPolicy.
func (mr *MockCasbinClientMockRecorder) AddGroupingPolicy(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGroupingPolicy", reflect.TypeOf((*MockCasbinClient)(nil).AddGroupingPolicy), varargs...)
}

// AddPolicy mocks base method.
func (m *MockCasbinClient) AddPolicy(ctx context.Context, in *casbin.PolicyRequest, opts ...grpc.CallOption) (*casbin.PolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPolicy", varargs...)
	ret0, _ := ret[0].(*casbin.PolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPolicy indicates an expected call of AddPolicy.
func (mr *MockCasbinClientMockRecorder) AddPolicy(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPolicy", reflect.TypeOf((*MockCasbinClient)(nil).AddPolicy), varargs...)
}

// AddUserPermissions mocks base method.
func (m *MockCasbinClient) AddUserPermissions(ctx context.Context, in *casbin.AddUserPermissionsRequest, opts ...grpc.CallOption) (*casbin.AddUserPermissionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserPermissions", varargs...)
	ret0, _ := ret[0].(*casbin.AddUserPermissionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserPermissions indicates an expected call of AddUserPermissions.
func (mr *MockCasbinClientMockRecorder) AddUserPermissions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserPermissions", reflect.TypeOf((*MockCasbinClient)(nil).AddUserPermissions), varargs...)
}

// AddUserPermissionsList mocks base method.
func (m *MockCasbinClient) AddUserPermissionsList(ctx context.Context, in *casbin.AddUserPermissionsListRequest, opts ...grpc.CallOption) (*casbin.AddUserPermissionsListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserPermissionsList", varargs...)
	ret0, _ := ret[0].(*casbin.AddUserPermissionsListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserPermissionsList indicates an expected call of AddUserPermissionsList.
func (mr *MockCasbinClientMockRecorder) AddUserPermissionsList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserPermissionsList", reflect.TypeOf((*MockCasbinClient)(nil).AddUserPermissionsList), varargs...)
}

// BulkCheckResourceAccessibility mocks base method.
func (m *MockCasbinClient) BulkCheckResourceAccessibility(ctx context.Context, in *casbin.BulkCheckResourceAccessibilityRequest, opts ...grpc.CallOption) (*casbin.BulkCheckResourceAccessibilityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkCheckResourceAccessibility", varargs...)
	ret0, _ := ret[0].(*casbin.BulkCheckResourceAccessibilityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkCheckResourceAccessibility indicates an expected call of BulkCheckResourceAccessibility.
func (mr *MockCasbinClientMockRecorder) BulkCheckResourceAccessibility(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkCheckResourceAccessibility", reflect.TypeOf((*MockCasbinClient)(nil).BulkCheckResourceAccessibility), varargs...)
}

// GetPermissionsForUser mocks base method.
func (m *MockCasbinClient) GetPermissionsForUser(ctx context.Context, in *casbin.GetPermissionsForUserRequest, opts ...grpc.CallOption) (*casbin.GetPermissionsForUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPermissionsForUser", varargs...)
	ret0, _ := ret[0].(*casbin.GetPermissionsForUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPermissionsForUser indicates an expected call of GetPermissionsForUser.
func (mr *MockCasbinClientMockRecorder) GetPermissionsForUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPermissionsForUser", reflect.TypeOf((*MockCasbinClient)(nil).GetPermissionsForUser), varargs...)
}

// RemoveGroupingPolicy mocks base method.
func (m *MockCasbinClient) RemoveGroupingPolicy(ctx context.Context, in *casbin.GroupingPolicyRequest, opts ...grpc.CallOption) (*casbin.GroupingPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveGroupingPolicy", varargs...)
	ret0, _ := ret[0].(*casbin.GroupingPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveGroupingPolicy indicates an expected call of RemoveGroupingPolicy.
func (mr *MockCasbinClientMockRecorder) RemoveGroupingPolicy(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveGroupingPolicy", reflect.TypeOf((*MockCasbinClient)(nil).RemoveGroupingPolicy), varargs...)
}

// RemovePolicy mocks base method.
func (m *MockCasbinClient) RemovePolicy(ctx context.Context, in *casbin.PolicyRequest, opts ...grpc.CallOption) (*casbin.PolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemovePolicy", varargs...)
	ret0, _ := ret[0].(*casbin.PolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemovePolicy indicates an expected call of RemovePolicy.
func (mr *MockCasbinClientMockRecorder) RemovePolicy(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemovePolicy", reflect.TypeOf((*MockCasbinClient)(nil).RemovePolicy), varargs...)
}

// RemoveUserPermissions mocks base method.
func (m *MockCasbinClient) RemoveUserPermissions(ctx context.Context, in *casbin.RemoveUserPermissionsRequest, opts ...grpc.CallOption) (*casbin.RemoveUserPermissionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveUserPermissions", varargs...)
	ret0, _ := ret[0].(*casbin.RemoveUserPermissionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveUserPermissions indicates an expected call of RemoveUserPermissions.
func (mr *MockCasbinClientMockRecorder) RemoveUserPermissions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveUserPermissions", reflect.TypeOf((*MockCasbinClient)(nil).RemoveUserPermissions), varargs...)
}

// VerifyUserPermissions mocks base method.
func (m *MockCasbinClient) VerifyUserPermissions(ctx context.Context, in *casbin.VerifyUserPermissionsRequest, opts ...grpc.CallOption) (*casbin.VerifyUserPermissionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyUserPermissions", varargs...)
	ret0, _ := ret[0].(*casbin.VerifyUserPermissionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyUserPermissions indicates an expected call of VerifyUserPermissions.
func (mr *MockCasbinClientMockRecorder) VerifyUserPermissions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyUserPermissions", reflect.TypeOf((*MockCasbinClient)(nil).VerifyUserPermissions), varargs...)
}

// MockCasbinServer is a mock of CasbinServer interface.
type MockCasbinServer struct {
	ctrl     *gomock.Controller
	recorder *MockCasbinServerMockRecorder
}

// MockCasbinServerMockRecorder is the mock recorder for MockCasbinServer.
type MockCasbinServerMockRecorder struct {
	mock *MockCasbinServer
}

// NewMockCasbinServer creates a new mock instance.
func NewMockCasbinServer(ctrl *gomock.Controller) *MockCasbinServer {
	mock := &MockCasbinServer{ctrl: ctrl}
	mock.recorder = &MockCasbinServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCasbinServer) EXPECT() *MockCasbinServerMockRecorder {
	return m.recorder
}

// AddGroupingPolicy mocks base method.
func (m *MockCasbinServer) AddGroupingPolicy(arg0 context.Context, arg1 *casbin.GroupingPolicyRequest) (*casbin.GroupingPolicyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddGroupingPolicy", arg0, arg1)
	ret0, _ := ret[0].(*casbin.GroupingPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddGroupingPolicy indicates an expected call of AddGroupingPolicy.
func (mr *MockCasbinServerMockRecorder) AddGroupingPolicy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGroupingPolicy", reflect.TypeOf((*MockCasbinServer)(nil).AddGroupingPolicy), arg0, arg1)
}

// AddPolicy mocks base method.
func (m *MockCasbinServer) AddPolicy(arg0 context.Context, arg1 *casbin.PolicyRequest) (*casbin.PolicyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPolicy", arg0, arg1)
	ret0, _ := ret[0].(*casbin.PolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPolicy indicates an expected call of AddPolicy.
func (mr *MockCasbinServerMockRecorder) AddPolicy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPolicy", reflect.TypeOf((*MockCasbinServer)(nil).AddPolicy), arg0, arg1)
}

// AddUserPermissions mocks base method.
func (m *MockCasbinServer) AddUserPermissions(arg0 context.Context, arg1 *casbin.AddUserPermissionsRequest) (*casbin.AddUserPermissionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserPermissions", arg0, arg1)
	ret0, _ := ret[0].(*casbin.AddUserPermissionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserPermissions indicates an expected call of AddUserPermissions.
func (mr *MockCasbinServerMockRecorder) AddUserPermissions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserPermissions", reflect.TypeOf((*MockCasbinServer)(nil).AddUserPermissions), arg0, arg1)
}

// AddUserPermissionsList mocks base method.
func (m *MockCasbinServer) AddUserPermissionsList(arg0 context.Context, arg1 *casbin.AddUserPermissionsListRequest) (*casbin.AddUserPermissionsListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserPermissionsList", arg0, arg1)
	ret0, _ := ret[0].(*casbin.AddUserPermissionsListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserPermissionsList indicates an expected call of AddUserPermissionsList.
func (mr *MockCasbinServerMockRecorder) AddUserPermissionsList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserPermissionsList", reflect.TypeOf((*MockCasbinServer)(nil).AddUserPermissionsList), arg0, arg1)
}

// BulkCheckResourceAccessibility mocks base method.
func (m *MockCasbinServer) BulkCheckResourceAccessibility(arg0 context.Context, arg1 *casbin.BulkCheckResourceAccessibilityRequest) (*casbin.BulkCheckResourceAccessibilityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkCheckResourceAccessibility", arg0, arg1)
	ret0, _ := ret[0].(*casbin.BulkCheckResourceAccessibilityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkCheckResourceAccessibility indicates an expected call of BulkCheckResourceAccessibility.
func (mr *MockCasbinServerMockRecorder) BulkCheckResourceAccessibility(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkCheckResourceAccessibility", reflect.TypeOf((*MockCasbinServer)(nil).BulkCheckResourceAccessibility), arg0, arg1)
}

// GetPermissionsForUser mocks base method.
func (m *MockCasbinServer) GetPermissionsForUser(arg0 context.Context, arg1 *casbin.GetPermissionsForUserRequest) (*casbin.GetPermissionsForUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPermissionsForUser", arg0, arg1)
	ret0, _ := ret[0].(*casbin.GetPermissionsForUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPermissionsForUser indicates an expected call of GetPermissionsForUser.
func (mr *MockCasbinServerMockRecorder) GetPermissionsForUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPermissionsForUser", reflect.TypeOf((*MockCasbinServer)(nil).GetPermissionsForUser), arg0, arg1)
}

// RemoveGroupingPolicy mocks base method.
func (m *MockCasbinServer) RemoveGroupingPolicy(arg0 context.Context, arg1 *casbin.GroupingPolicyRequest) (*casbin.GroupingPolicyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveGroupingPolicy", arg0, arg1)
	ret0, _ := ret[0].(*casbin.GroupingPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveGroupingPolicy indicates an expected call of RemoveGroupingPolicy.
func (mr *MockCasbinServerMockRecorder) RemoveGroupingPolicy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveGroupingPolicy", reflect.TypeOf((*MockCasbinServer)(nil).RemoveGroupingPolicy), arg0, arg1)
}

// RemovePolicy mocks base method.
func (m *MockCasbinServer) RemovePolicy(arg0 context.Context, arg1 *casbin.PolicyRequest) (*casbin.PolicyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemovePolicy", arg0, arg1)
	ret0, _ := ret[0].(*casbin.PolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemovePolicy indicates an expected call of RemovePolicy.
func (mr *MockCasbinServerMockRecorder) RemovePolicy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemovePolicy", reflect.TypeOf((*MockCasbinServer)(nil).RemovePolicy), arg0, arg1)
}

// RemoveUserPermissions mocks base method.
func (m *MockCasbinServer) RemoveUserPermissions(arg0 context.Context, arg1 *casbin.RemoveUserPermissionsRequest) (*casbin.RemoveUserPermissionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveUserPermissions", arg0, arg1)
	ret0, _ := ret[0].(*casbin.RemoveUserPermissionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveUserPermissions indicates an expected call of RemoveUserPermissions.
func (mr *MockCasbinServerMockRecorder) RemoveUserPermissions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveUserPermissions", reflect.TypeOf((*MockCasbinServer)(nil).RemoveUserPermissions), arg0, arg1)
}

// VerifyUserPermissions mocks base method.
func (m *MockCasbinServer) VerifyUserPermissions(arg0 context.Context, arg1 *casbin.VerifyUserPermissionsRequest) (*casbin.VerifyUserPermissionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyUserPermissions", arg0, arg1)
	ret0, _ := ret[0].(*casbin.VerifyUserPermissionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyUserPermissions indicates an expected call of VerifyUserPermissions.
func (mr *MockCasbinServerMockRecorder) VerifyUserPermissions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyUserPermissions", reflect.TypeOf((*MockCasbinServer)(nil).VerifyUserPermissions), arg0, arg1)
}

// MockUnsafeCasbinServer is a mock of UnsafeCasbinServer interface.
type MockUnsafeCasbinServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCasbinServerMockRecorder
}

// MockUnsafeCasbinServerMockRecorder is the mock recorder for MockUnsafeCasbinServer.
type MockUnsafeCasbinServerMockRecorder struct {
	mock *MockUnsafeCasbinServer
}

// NewMockUnsafeCasbinServer creates a new mock instance.
func NewMockUnsafeCasbinServer(ctrl *gomock.Controller) *MockUnsafeCasbinServer {
	mock := &MockUnsafeCasbinServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCasbinServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCasbinServer) EXPECT() *MockUnsafeCasbinServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCasbinServer mocks base method.
func (m *MockUnsafeCasbinServer) mustEmbedUnimplementedCasbinServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCasbinServer")
}

// mustEmbedUnimplementedCasbinServer indicates an expected call of mustEmbedUnimplementedCasbinServer.
func (mr *MockUnsafeCasbinServerMockRecorder) mustEmbedUnimplementedCasbinServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCasbinServer", reflect.TypeOf((*MockUnsafeCasbinServer)(nil).mustEmbedUnimplementedCasbinServer))
}
