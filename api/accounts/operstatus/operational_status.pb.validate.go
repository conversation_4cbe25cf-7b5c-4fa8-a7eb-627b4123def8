// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/accounts/operstatus/operational_status.proto

package operstatus

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	enums "github.com/epifi/gamma/api/accounts/enums"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = enums.OperationalStatus(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on OperationalStatusInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OperationalStatusInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OperationalStatusInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OperationalStatusInfoMultiError, or nil if none found.
func (m *OperationalStatusInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OperationalStatusInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _OperationalStatusInfo_AccountType_NotInLookup[m.GetAccountType()]; ok {
		err := OperationalStatusInfoValidationError{
			field:  "AccountType",
			reason: "value must not be in list [TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAccountId()) < 1 {
		err := OperationalStatusInfoValidationError{
			field:  "AccountId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _OperationalStatusInfo_PartnerBank_InLookup[m.GetPartnerBank()]; !ok {
		err := OperationalStatusInfoValidationError{
			field:  "PartnerBank",
			reason: "value must be in list [FEDERAL_BANK]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAccountOpenedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "AccountOpenedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "AccountOpenedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountOpenedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OperationalStatusInfoValidationError{
				field:  "AccountOpenedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountClosedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "AccountClosedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "AccountClosedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountClosedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OperationalStatusInfoValidationError{
				field:  "AccountClosedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OperationalStatus

	// no validation rules for FreezeStatus

	if all {
		switch v := interface{}(m.GetTotalLienMarking()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "TotalLienMarking",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "TotalLienMarking",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalLienMarking()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OperationalStatusInfoValidationError{
				field:  "TotalLienMarking",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKYCComplianceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "KYCComplianceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "KYCComplianceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKYCComplianceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OperationalStatusInfoValidationError{
				field:  "KYCComplianceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "VendorResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "VendorResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OperationalStatusInfoValidationError{
				field:  "VendorResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OperationalStatusInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OperationalStatusInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OperationalStatusInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeletedAtUnix

	if len(errors) > 0 {
		return OperationalStatusInfoMultiError(errors)
	}

	return nil
}

// OperationalStatusInfoMultiError is an error wrapping multiple validation
// errors returned by OperationalStatusInfo.ValidateAll() if the designated
// constraints aren't met.
type OperationalStatusInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OperationalStatusInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OperationalStatusInfoMultiError) AllErrors() []error { return m }

// OperationalStatusInfoValidationError is the validation error returned by
// OperationalStatusInfo.Validate if the designated constraints aren't met.
type OperationalStatusInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OperationalStatusInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OperationalStatusInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OperationalStatusInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OperationalStatusInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OperationalStatusInfoValidationError) ErrorName() string {
	return "OperationalStatusInfoValidationError"
}

// Error satisfies the builtin error interface
func (e OperationalStatusInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOperationalStatusInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OperationalStatusInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OperationalStatusInfoValidationError{}

var _OperationalStatusInfo_AccountType_NotInLookup = map[accounts.Type]struct{}{
	0: {},
}

var _OperationalStatusInfo_PartnerBank_InLookup = map[vendorgateway.Vendor]struct{}{
	1: {},
}

// Validate checks the field values on OperationalStatusVendorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OperationalStatusVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OperationalStatusVendorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// OperationalStatusVendorResponseMultiError, or nil if none found.
func (m *OperationalStatusVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *OperationalStatusVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.VendorResponse.(type) {
	case *OperationalStatusVendorResponse_FederalAccountStatusEnquiryResponse:
		if v == nil {
			err := OperationalStatusVendorResponseValidationError{
				field:  "VendorResponse",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFederalAccountStatusEnquiryResponse()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OperationalStatusVendorResponseValidationError{
						field:  "FederalAccountStatusEnquiryResponse",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OperationalStatusVendorResponseValidationError{
						field:  "FederalAccountStatusEnquiryResponse",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFederalAccountStatusEnquiryResponse()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OperationalStatusVendorResponseValidationError{
					field:  "FederalAccountStatusEnquiryResponse",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OperationalStatusVendorResponse_FederalAccountStatusCallBackData:
		if v == nil {
			err := OperationalStatusVendorResponseValidationError{
				field:  "VendorResponse",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFederalAccountStatusCallBackData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OperationalStatusVendorResponseValidationError{
						field:  "FederalAccountStatusCallBackData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OperationalStatusVendorResponseValidationError{
						field:  "FederalAccountStatusCallBackData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFederalAccountStatusCallBackData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OperationalStatusVendorResponseValidationError{
					field:  "FederalAccountStatusCallBackData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return OperationalStatusVendorResponseMultiError(errors)
	}

	return nil
}

// OperationalStatusVendorResponseMultiError is an error wrapping multiple
// validation errors returned by OperationalStatusVendorResponse.ValidateAll()
// if the designated constraints aren't met.
type OperationalStatusVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OperationalStatusVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OperationalStatusVendorResponseMultiError) AllErrors() []error { return m }

// OperationalStatusVendorResponseValidationError is the validation error
// returned by OperationalStatusVendorResponse.Validate if the designated
// constraints aren't met.
type OperationalStatusVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OperationalStatusVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OperationalStatusVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OperationalStatusVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OperationalStatusVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OperationalStatusVendorResponseValidationError) ErrorName() string {
	return "OperationalStatusVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e OperationalStatusVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOperationalStatusVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OperationalStatusVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OperationalStatusVendorResponseValidationError{}

// Validate checks the field values on KYCCompliance with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KYCCompliance) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KYCCompliance with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KYCComplianceMultiError, or
// nil if none found.
func (m *KYCCompliance) ValidateAll() error {
	return m.validate(true)
}

func (m *KYCCompliance) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetKYCReviewDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCComplianceValidationError{
					field:  "KYCReviewDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCComplianceValidationError{
					field:  "KYCReviewDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKYCReviewDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCComplianceValidationError{
				field:  "KYCReviewDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKYCDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCComplianceValidationError{
					field:  "KYCDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCComplianceValidationError{
					field:  "KYCDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKYCDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCComplianceValidationError{
				field:  "KYCDueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKYCGracePeriodDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCComplianceValidationError{
					field:  "KYCGracePeriodDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCComplianceValidationError{
					field:  "KYCGracePeriodDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKYCGracePeriodDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCComplianceValidationError{
				field:  "KYCGracePeriodDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KYCComplianceMultiError(errors)
	}

	return nil
}

// KYCComplianceMultiError is an error wrapping multiple validation errors
// returned by KYCCompliance.ValidateAll() if the designated constraints
// aren't met.
type KYCComplianceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KYCComplianceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KYCComplianceMultiError) AllErrors() []error { return m }

// KYCComplianceValidationError is the validation error returned by
// KYCCompliance.Validate if the designated constraints aren't met.
type KYCComplianceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KYCComplianceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KYCComplianceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KYCComplianceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KYCComplianceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KYCComplianceValidationError) ErrorName() string { return "KYCComplianceValidationError" }

// Error satisfies the builtin error interface
func (e KYCComplianceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKYCCompliance.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KYCComplianceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KYCComplianceValidationError{}
