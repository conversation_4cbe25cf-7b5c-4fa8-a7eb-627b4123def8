// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/categorizer/service.proto

package categorizer

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	header "github.com/epifi/gamma/api/frontend/header"
	pay "github.com/epifi/gamma/api/frontend/pay"
	widget "github.com/epifi/gamma/api/frontend/search/widget"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateSimilarBoxState int32

const (
	UpdateSimilarBoxState_UPDATE_SIMILAR_BOX_STATE_UNSPECIFIED UpdateSimilarBoxState = 0
	UpdateSimilarBoxState_UPDATE_SIMILAR_BOX_STATE_CHECKED     UpdateSimilarBoxState = 1
	UpdateSimilarBoxState_UPDATE_SIMILAR_BOX_STATE_UNCHECKED   UpdateSimilarBoxState = 2
)

// Enum value maps for UpdateSimilarBoxState.
var (
	UpdateSimilarBoxState_name = map[int32]string{
		0: "UPDATE_SIMILAR_BOX_STATE_UNSPECIFIED",
		1: "UPDATE_SIMILAR_BOX_STATE_CHECKED",
		2: "UPDATE_SIMILAR_BOX_STATE_UNCHECKED",
	}
	UpdateSimilarBoxState_value = map[string]int32{
		"UPDATE_SIMILAR_BOX_STATE_UNSPECIFIED": 0,
		"UPDATE_SIMILAR_BOX_STATE_CHECKED":     1,
		"UPDATE_SIMILAR_BOX_STATE_UNCHECKED":   2,
	}
)

func (x UpdateSimilarBoxState) Enum() *UpdateSimilarBoxState {
	p := new(UpdateSimilarBoxState)
	*p = x
	return p
}

func (x UpdateSimilarBoxState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateSimilarBoxState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_categorizer_service_proto_enumTypes[0].Descriptor()
}

func (UpdateSimilarBoxState) Type() protoreflect.EnumType {
	return &file_api_frontend_categorizer_service_proto_enumTypes[0]
}

func (x UpdateSimilarBoxState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateSimilarBoxState.Descriptor instead.
func (UpdateSimilarBoxState) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{0}
}

type ListCategoriesForRecategorizationResponse_Status int32

const (
	ListCategoriesForRecategorizationResponse_OK        ListCategoriesForRecategorizationResponse_Status = 0
	ListCategoriesForRecategorizationResponse_CANCELLED ListCategoriesForRecategorizationResponse_Status = 1
	// thrown when the user does not have permission to view the categories
	ListCategoriesForRecategorizationResponse_PERMISSION_DENIED ListCategoriesForRecategorizationResponse_Status = 7
	// internal error while processing the request.
	ListCategoriesForRecategorizationResponse_INTERNAL        ListCategoriesForRecategorizationResponse_Status = 13
	ListCategoriesForRecategorizationResponse_UNAUTHENTICATED ListCategoriesForRecategorizationResponse_Status = 16
)

// Enum value maps for ListCategoriesForRecategorizationResponse_Status.
var (
	ListCategoriesForRecategorizationResponse_Status_name = map[int32]string{
		0:  "OK",
		1:  "CANCELLED",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
		16: "UNAUTHENTICATED",
	}
	ListCategoriesForRecategorizationResponse_Status_value = map[string]int32{
		"OK":                0,
		"CANCELLED":         1,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
		"UNAUTHENTICATED":   16,
	}
)

func (x ListCategoriesForRecategorizationResponse_Status) Enum() *ListCategoriesForRecategorizationResponse_Status {
	p := new(ListCategoriesForRecategorizationResponse_Status)
	*p = x
	return p
}

func (x ListCategoriesForRecategorizationResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCategoriesForRecategorizationResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_categorizer_service_proto_enumTypes[1].Descriptor()
}

func (ListCategoriesForRecategorizationResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_categorizer_service_proto_enumTypes[1]
}

func (x ListCategoriesForRecategorizationResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCategoriesForRecategorizationResponse_Status.Descriptor instead.
func (ListCategoriesForRecategorizationResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{1, 0}
}

type UpdateCategoriesResponse_Status int32

const (
	UpdateCategoriesResponse_OK        UpdateCategoriesResponse_Status = 0
	UpdateCategoriesResponse_CANCELLED UpdateCategoriesResponse_Status = 1
	// thrown when the user does not have permission to recategorize any of the transactions
	UpdateCategoriesResponse_PERMISSION_DENIED UpdateCategoriesResponse_Status = 7
	// internal error while processing the request.
	UpdateCategoriesResponse_INTERNAL        UpdateCategoriesResponse_Status = 13
	UpdateCategoriesResponse_UNAUTHENTICATED UpdateCategoriesResponse_Status = 16
)

// Enum value maps for UpdateCategoriesResponse_Status.
var (
	UpdateCategoriesResponse_Status_name = map[int32]string{
		0:  "OK",
		1:  "CANCELLED",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
		16: "UNAUTHENTICATED",
	}
	UpdateCategoriesResponse_Status_value = map[string]int32{
		"OK":                0,
		"CANCELLED":         1,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
		"UNAUTHENTICATED":   16,
	}
)

func (x UpdateCategoriesResponse_Status) Enum() *UpdateCategoriesResponse_Status {
	p := new(UpdateCategoriesResponse_Status)
	*p = x
	return p
}

func (x UpdateCategoriesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateCategoriesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_categorizer_service_proto_enumTypes[2].Descriptor()
}

func (UpdateCategoriesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_categorizer_service_proto_enumTypes[2]
}

func (x UpdateCategoriesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateCategoriesResponse_Status.Descriptor instead.
func (UpdateCategoriesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{4, 0}
}

// statuses
type GetSimilarActivitiesResponse_Status int32

const (
	GetSimilarActivitiesResponse_OK        GetSimilarActivitiesResponse_Status = 0
	GetSimilarActivitiesResponse_CANCELLED GetSimilarActivitiesResponse_Status = 1
	// thrown when the user does not have permission to fetch the similar transactions on the orderId specified in the request.
	// eg. if the orderId does not belong to the particular user, then we send the permission denied error.
	GetSimilarActivitiesResponse_PERMISSION_DENIED GetSimilarActivitiesResponse_Status = 7
	// internal error while processing the request.
	GetSimilarActivitiesResponse_INTERNAL        GetSimilarActivitiesResponse_Status = 13
	GetSimilarActivitiesResponse_UNAUTHENTICATED GetSimilarActivitiesResponse_Status = 16
)

// Enum value maps for GetSimilarActivitiesResponse_Status.
var (
	GetSimilarActivitiesResponse_Status_name = map[int32]string{
		0:  "OK",
		1:  "CANCELLED",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
		16: "UNAUTHENTICATED",
	}
	GetSimilarActivitiesResponse_Status_value = map[string]int32{
		"OK":                0,
		"CANCELLED":         1,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
		"UNAUTHENTICATED":   16,
	}
)

func (x GetSimilarActivitiesResponse_Status) Enum() *GetSimilarActivitiesResponse_Status {
	p := new(GetSimilarActivitiesResponse_Status)
	*p = x
	return p
}

func (x GetSimilarActivitiesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetSimilarActivitiesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_categorizer_service_proto_enumTypes[3].Descriptor()
}

func (GetSimilarActivitiesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_categorizer_service_proto_enumTypes[3]
}

func (x GetSimilarActivitiesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetSimilarActivitiesResponse_Status.Descriptor instead.
func (GetSimilarActivitiesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{6, 0}
}

// statuses
type GetCategoryDetailsBatchResponse_Status int32

const (
	GetCategoryDetailsBatchResponse_OK                GetCategoryDetailsBatchResponse_Status = 0
	GetCategoryDetailsBatchResponse_NOT_FOUND         GetCategoryDetailsBatchResponse_Status = 5
	GetCategoryDetailsBatchResponse_PERMISSION_DENIED GetCategoryDetailsBatchResponse_Status = 7
	GetCategoryDetailsBatchResponse_INTERNAL          GetCategoryDetailsBatchResponse_Status = 13
	GetCategoryDetailsBatchResponse_UNAUTHENTICATED   GetCategoryDetailsBatchResponse_Status = 16
)

// Enum value maps for GetCategoryDetailsBatchResponse_Status.
var (
	GetCategoryDetailsBatchResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
		16: "UNAUTHENTICATED",
	}
	GetCategoryDetailsBatchResponse_Status_value = map[string]int32{
		"OK":                0,
		"NOT_FOUND":         5,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
		"UNAUTHENTICATED":   16,
	}
)

func (x GetCategoryDetailsBatchResponse_Status) Enum() *GetCategoryDetailsBatchResponse_Status {
	p := new(GetCategoryDetailsBatchResponse_Status)
	*p = x
	return p
}

func (x GetCategoryDetailsBatchResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetCategoryDetailsBatchResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_categorizer_service_proto_enumTypes[4].Descriptor()
}

func (GetCategoryDetailsBatchResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_categorizer_service_proto_enumTypes[4]
}

func (x GetCategoryDetailsBatchResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetCategoryDetailsBatchResponse_Status.Descriptor instead.
func (GetCategoryDetailsBatchResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{9, 0}
}

// statuses
type GetSimilarActivitiesCountDetailsResponse_Status int32

const (
	GetSimilarActivitiesCountDetailsResponse_OK                GetSimilarActivitiesCountDetailsResponse_Status = 0
	GetSimilarActivitiesCountDetailsResponse_NOT_FOUND         GetSimilarActivitiesCountDetailsResponse_Status = 5
	GetSimilarActivitiesCountDetailsResponse_PERMISSION_DENIED GetSimilarActivitiesCountDetailsResponse_Status = 7
	GetSimilarActivitiesCountDetailsResponse_INTERNAL          GetSimilarActivitiesCountDetailsResponse_Status = 13
	GetSimilarActivitiesCountDetailsResponse_UNAUTHENTICATED   GetSimilarActivitiesCountDetailsResponse_Status = 16
)

// Enum value maps for GetSimilarActivitiesCountDetailsResponse_Status.
var (
	GetSimilarActivitiesCountDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
		16: "UNAUTHENTICATED",
	}
	GetSimilarActivitiesCountDetailsResponse_Status_value = map[string]int32{
		"OK":                0,
		"NOT_FOUND":         5,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
		"UNAUTHENTICATED":   16,
	}
)

func (x GetSimilarActivitiesCountDetailsResponse_Status) Enum() *GetSimilarActivitiesCountDetailsResponse_Status {
	p := new(GetSimilarActivitiesCountDetailsResponse_Status)
	*p = x
	return p
}

func (x GetSimilarActivitiesCountDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetSimilarActivitiesCountDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_categorizer_service_proto_enumTypes[5].Descriptor()
}

func (GetSimilarActivitiesCountDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_categorizer_service_proto_enumTypes[5]
}

func (x GetSimilarActivitiesCountDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetSimilarActivitiesCountDetailsResponse_Status.Descriptor instead.
func (GetSimilarActivitiesCountDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{11, 0}
}

type ListCategoriesForRecategorizationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/categorizer/service.proto.
	OrderId    string                `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Req        *header.RequestHeader `protobuf:"bytes,2,opt,name=req,proto3" json:"req,omitempty"`
	ActivityId *ActivityId           `protobuf:"bytes,3,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
}

func (x *ListCategoriesForRecategorizationRequest) Reset() {
	*x = ListCategoriesForRecategorizationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCategoriesForRecategorizationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCategoriesForRecategorizationRequest) ProtoMessage() {}

func (x *ListCategoriesForRecategorizationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCategoriesForRecategorizationRequest.ProtoReflect.Descriptor instead.
func (*ListCategoriesForRecategorizationRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{0}
}

// Deprecated: Marked as deprecated in api/frontend/categorizer/service.proto.
func (x *ListCategoriesForRecategorizationRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *ListCategoriesForRecategorizationRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *ListCategoriesForRecategorizationRequest) GetActivityId() *ActivityId {
	if x != nil {
		return x.ActivityId
	}
	return nil
}

type ListCategoriesForRecategorizationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/categorizer/service.proto.
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// at max only 5 suggested categories will be sent
	SuggestedCategories []*pay.TransactionCategory `protobuf:"bytes,2,rep,name=suggested_categories,json=suggestedCategories,proto3" json:"suggested_categories,omitempty"`
	// all the available categories will be sent for user to choose from.
	// the categories present in suggested_categories will also be present in all_categories.
	AllCategories []*pay.TransactionCategory `protobuf:"bytes,3,rep,name=all_categories,json=allCategories,proto3" json:"all_categories,omitempty"`
	// The user should not be allowed to select more categories than the limit specified by category_selection_limit.
	// If category_selection_limit = -1 ,then the user is allowed to select any number of categories for recategorization
	// category_selection_limit should be >= -1
	CategorySelectionLimit int32                  `protobuf:"varint,4,opt,name=category_selection_limit,json=categorySelectionLimit,proto3" json:"category_selection_limit,omitempty"`
	RespHeader             *header.ResponseHeader `protobuf:"bytes,5,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// info about the different options to provide for user future txn cat (for similar transactions)
	UserFutureTxnCatInfo *UserFutureTxnCatInfo `protobuf:"bytes,6,opt,name=user_future_txn_cat_info,json=userFutureTxnCatInfo,proto3" json:"user_future_txn_cat_info,omitempty"`
}

func (x *ListCategoriesForRecategorizationResponse) Reset() {
	*x = ListCategoriesForRecategorizationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCategoriesForRecategorizationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCategoriesForRecategorizationResponse) ProtoMessage() {}

func (x *ListCategoriesForRecategorizationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCategoriesForRecategorizationResponse.ProtoReflect.Descriptor instead.
func (*ListCategoriesForRecategorizationResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{1}
}

// Deprecated: Marked as deprecated in api/frontend/categorizer/service.proto.
func (x *ListCategoriesForRecategorizationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListCategoriesForRecategorizationResponse) GetSuggestedCategories() []*pay.TransactionCategory {
	if x != nil {
		return x.SuggestedCategories
	}
	return nil
}

func (x *ListCategoriesForRecategorizationResponse) GetAllCategories() []*pay.TransactionCategory {
	if x != nil {
		return x.AllCategories
	}
	return nil
}

func (x *ListCategoriesForRecategorizationResponse) GetCategorySelectionLimit() int32 {
	if x != nil {
		return x.CategorySelectionLimit
	}
	return 0
}

func (x *ListCategoriesForRecategorizationResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *ListCategoriesForRecategorizationResponse) GetUserFutureTxnCatInfo() *UserFutureTxnCatInfo {
	if x != nil {
		return x.UserFutureTxnCatInfo
	}
	return nil
}

type UserFutureTxnCatInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// flag to determine if user can set the recat categories to all future transactions
	// In case the otherPi involved in the current txn has a Generic Pi id, saving future txn cat is not helpful,
	// so backend will set this flag to false in those cases and therefore client will not
	// show the future txn cat check-box on UI
	CanUserSetFutureTxnCategories bool `protobuf:"varint,1,opt,name=can_user_set_future_txn_categories,json=canUserSetFutureTxnCategories,proto3" json:"can_user_set_future_txn_categories,omitempty"`
	// default state of future txn cat check box
	IsFutureTxnCatSetByDefault bool `protobuf:"varint,2,opt,name=is_future_txn_cat_set_by_default,json=isFutureTxnCatSetByDefault,proto3" json:"is_future_txn_cat_set_by_default,omitempty"`
}

func (x *UserFutureTxnCatInfo) Reset() {
	*x = UserFutureTxnCatInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserFutureTxnCatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserFutureTxnCatInfo) ProtoMessage() {}

func (x *UserFutureTxnCatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserFutureTxnCatInfo.ProtoReflect.Descriptor instead.
func (*UserFutureTxnCatInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{2}
}

func (x *UserFutureTxnCatInfo) GetCanUserSetFutureTxnCategories() bool {
	if x != nil {
		return x.CanUserSetFutureTxnCategories
	}
	return false
}

func (x *UserFutureTxnCatInfo) GetIsFutureTxnCatSetByDefault() bool {
	if x != nil {
		return x.IsFutureTxnCatSetByDefault
	}
	return false
}

type UpdateCategoriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// The list of order ids for which categories needs to be updated.
	// Since UI works with orders, we are accepting order ids, but internally the transactions within these orders are recategorized.
	// Use activity ids instead.
	//
	// Deprecated: Marked as deprecated in api/frontend/categorizer/service.proto.
	OrderIds []string `protobuf:"bytes,2,rep,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	// List of activities which needs to be recategorised
	ActivityIds []*ActivityId `protobuf:"bytes,5,rep,name=activity_ids,json=activityIds,proto3" json:"activity_ids,omitempty"`
	// List of categories which will be updated for the list of orders.
	CategoryIds []string `protobuf:"bytes,3,rep,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	// flag if set to true will enable given display category to appear for all future similar transactions (orders / aa txns)
	ApplyCategoriesForFutureSimilarTxn bool `protobuf:"varint,4,opt,name=apply_categories_for_future_similar_txn,json=applyCategoriesForFutureSimilarTxn,proto3" json:"apply_categories_for_future_similar_txn,omitempty"`
}

func (x *UpdateCategoriesRequest) Reset() {
	*x = UpdateCategoriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCategoriesRequest) ProtoMessage() {}

func (x *UpdateCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCategoriesRequest.ProtoReflect.Descriptor instead.
func (*UpdateCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateCategoriesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/categorizer/service.proto.
func (x *UpdateCategoriesRequest) GetOrderIds() []string {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

func (x *UpdateCategoriesRequest) GetActivityIds() []*ActivityId {
	if x != nil {
		return x.ActivityIds
	}
	return nil
}

func (x *UpdateCategoriesRequest) GetCategoryIds() []string {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *UpdateCategoriesRequest) GetApplyCategoriesForFutureSimilarTxn() bool {
	if x != nil {
		return x.ApplyCategoriesForFutureSimilarTxn
	}
	return false
}

type UpdateCategoriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/categorizer/service.proto.
	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,2,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *UpdateCategoriesResponse) Reset() {
	*x = UpdateCategoriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCategoriesResponse) ProtoMessage() {}

func (x *UpdateCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCategoriesResponse.ProtoReflect.Descriptor instead.
func (*UpdateCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{4}
}

// Deprecated: Marked as deprecated in api/frontend/categorizer/service.proto.
func (x *UpdateCategoriesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateCategoriesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

// GetSimilarActivitiesRequest uses token based pagination. To fetch the latest set of activities, latest_page token needs to be passed.
// For fetching the before or after activities, before_token or after_token needs to be passed respectively. These are sent to client in the response itself.
type GetSimilarActivitiesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Similar activities will be fetched on basis of this order Id.
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// A page token is generated after base64 encoding a JSON serialize string. This can contain any data relevant to the
	// server in order to send the next page.
	// e.g. {last_activity_timestamp: <last_order_timestamp> } can be returned as after token
	// and {first_activity_timestamp: <first_order_timestamp> } can be returned as before token
	//
	// Types that are assignable to Token:
	//
	//	*GetSimilarActivitiesRequest_LatestPage
	//	*GetSimilarActivitiesRequest_BeforeToken
	//	*GetSimilarActivitiesRequest_AfterToken
	Token isGetSimilarActivitiesRequest_Token `protobuf_oneof:"token"`
	// number of activities to be returned in the response. If page_size if not passed then the default page size will be 20.
	PageSize uint32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *GetSimilarActivitiesRequest) Reset() {
	*x = GetSimilarActivitiesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSimilarActivitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSimilarActivitiesRequest) ProtoMessage() {}

func (x *GetSimilarActivitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSimilarActivitiesRequest.ProtoReflect.Descriptor instead.
func (*GetSimilarActivitiesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetSimilarActivitiesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetSimilarActivitiesRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (m *GetSimilarActivitiesRequest) GetToken() isGetSimilarActivitiesRequest_Token {
	if m != nil {
		return m.Token
	}
	return nil
}

func (x *GetSimilarActivitiesRequest) GetLatestPage() bool {
	if x, ok := x.GetToken().(*GetSimilarActivitiesRequest_LatestPage); ok {
		return x.LatestPage
	}
	return false
}

func (x *GetSimilarActivitiesRequest) GetBeforeToken() string {
	if x, ok := x.GetToken().(*GetSimilarActivitiesRequest_BeforeToken); ok {
		return x.BeforeToken
	}
	return ""
}

func (x *GetSimilarActivitiesRequest) GetAfterToken() string {
	if x, ok := x.GetToken().(*GetSimilarActivitiesRequest_AfterToken); ok {
		return x.AfterToken
	}
	return ""
}

func (x *GetSimilarActivitiesRequest) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type isGetSimilarActivitiesRequest_Token interface {
	isGetSimilarActivitiesRequest_Token()
}

type GetSimilarActivitiesRequest_LatestPage struct {
	// For cases when client is un-aware of page token. This can be due to various reasons
	// e.g. DB corruption, app uninstall etc.
	// Client can set this flag to fetch the last page (latest actor activities).
	// In this case activities will be returned in DESCENDING order of activity time from current timestamp.
	// Note- this flag will always return the last page of the activities (latest activities).
	// Further pages has to be fetched using the after_token/before token. These will be passed in the response to this api.
	LatestPage bool `protobuf:"varint,3,opt,name=latest_page,json=latestPage,proto3,oneof"`
}

type GetSimilarActivitiesRequest_BeforeToken struct {
	// before token is to be passed if client wants to fetch activities that happened before previous page.
	// in this case the activities will be ordered in DESCENDING order of activity time.
	BeforeToken string `protobuf:"bytes,4,opt,name=before_token,json=beforeToken,proto3,oneof"`
}

type GetSimilarActivitiesRequest_AfterToken struct {
	// after token is to be passed if client wants to fetch activities that happened after previous page.
	// in this case the events will be ordered in ASCENDING order of activity time.
	AfterToken string `protobuf:"bytes,5,opt,name=after_token,json=afterToken,proto3,oneof"`
}

func (*GetSimilarActivitiesRequest_LatestPage) isGetSimilarActivitiesRequest_Token() {}

func (*GetSimilarActivitiesRequest_BeforeToken) isGetSimilarActivitiesRequest_Token() {}

func (*GetSimilarActivitiesRequest_AfterToken) isGetSimilarActivitiesRequest_Token() {}

type GetSimilarActivitiesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/categorizer/service.proto.
	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,2,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// It contains the list of similar activities. The size of this list will be based on the pagination details defined in the request.
	Activities []*widget.TransactionsWidget `protobuf:"bytes,3,rep,name=activities,proto3" json:"activities,omitempty"`
	// before_token to be used when client wants to fetch activities that happened before the current page.
	BeforeToken string `protobuf:"bytes,4,opt,name=before_token,json=beforeToken,proto3" json:"before_token,omitempty"`
	// after_token to be used when client wants to fetch activities that happened after the current page.
	AfterToken string `protobuf:"bytes,5,opt,name=after_token,json=afterToken,proto3" json:"after_token,omitempty"`
}

func (x *GetSimilarActivitiesResponse) Reset() {
	*x = GetSimilarActivitiesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSimilarActivitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSimilarActivitiesResponse) ProtoMessage() {}

func (x *GetSimilarActivitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSimilarActivitiesResponse.ProtoReflect.Descriptor instead.
func (*GetSimilarActivitiesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{6}
}

// Deprecated: Marked as deprecated in api/frontend/categorizer/service.proto.
func (x *GetSimilarActivitiesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSimilarActivitiesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSimilarActivitiesResponse) GetActivities() []*widget.TransactionsWidget {
	if x != nil {
		return x.Activities
	}
	return nil
}

func (x *GetSimilarActivitiesResponse) GetBeforeToken() string {
	if x != nil {
		return x.BeforeToken
	}
	return ""
}

func (x *GetSimilarActivitiesResponse) GetAfterToken() string {
	if x != nil {
		return x.AfterToken
	}
	return ""
}

type GetCategoryDetailsBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req             *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	DisplayCategory []string              `protobuf:"bytes,2,rep,name=display_category,json=displayCategory,proto3" json:"display_category,omitempty"`
}

func (x *GetCategoryDetailsBatchRequest) Reset() {
	*x = GetCategoryDetailsBatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCategoryDetailsBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryDetailsBatchRequest) ProtoMessage() {}

func (x *GetCategoryDetailsBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryDetailsBatchRequest.ProtoReflect.Descriptor instead.
func (*GetCategoryDetailsBatchRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetCategoryDetailsBatchRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetCategoryDetailsBatchRequest) GetDisplayCategory() []string {
	if x != nil {
		return x.DisplayCategory
	}
	return nil
}

type CategoryDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayName string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// url to solid icon
	IconUrl            string `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	TransparentIconUrl string `protobuf:"bytes,3,opt,name=transparent_icon_url,json=transparentIconUrl,proto3" json:"transparent_icon_url,omitempty"`
}

func (x *CategoryDetails) Reset() {
	*x = CategoryDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryDetails) ProtoMessage() {}

func (x *CategoryDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryDetails.ProtoReflect.Descriptor instead.
func (*CategoryDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{8}
}

func (x *CategoryDetails) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CategoryDetails) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *CategoryDetails) GetTransparentIconUrl() string {
	if x != nil {
		return x.TransparentIconUrl
	}
	return ""
}

type GetCategoryDetailsBatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader      *header.ResponseHeader      `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	CategoryDetails map[string]*CategoryDetails `protobuf:"bytes,2,rep,name=category_details,json=categoryDetails,proto3" json:"category_details,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetCategoryDetailsBatchResponse) Reset() {
	*x = GetCategoryDetailsBatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCategoryDetailsBatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryDetailsBatchResponse) ProtoMessage() {}

func (x *GetCategoryDetailsBatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryDetailsBatchResponse.ProtoReflect.Descriptor instead.
func (*GetCategoryDetailsBatchResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetCategoryDetailsBatchResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetCategoryDetailsBatchResponse) GetCategoryDetails() map[string]*CategoryDetails {
	if x != nil {
		return x.CategoryDetails
	}
	return nil
}

type GetSimilarActivitiesCountDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// id of the fi order or the Aa txn the user is re-categorizing
	ActivityId string `protobuf:"bytes,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	// timestamp before which consider all similar activities (in last duration Window months) wil be considered
	// NOTE: this needs to be same across the get and update rpc as well
	EndTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_timestamp,json=endTimestamp,proto3" json:"end_timestamp,omitempty"`
}

func (x *GetSimilarActivitiesCountDetailsRequest) Reset() {
	*x = GetSimilarActivitiesCountDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSimilarActivitiesCountDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSimilarActivitiesCountDetailsRequest) ProtoMessage() {}

func (x *GetSimilarActivitiesCountDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSimilarActivitiesCountDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetSimilarActivitiesCountDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetSimilarActivitiesCountDetailsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetSimilarActivitiesCountDetailsRequest) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *GetSimilarActivitiesCountDetailsRequest) GetEndTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTimestamp
	}
	return nil
}

type GetSimilarActivitiesCountDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// title and body text on the bottom sheet view (for similar txn recat)
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Body  string `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	// count of similar activities that user can recat
	SimilarActivitiesCount int32 `protobuf:"varint,4,opt,name=similar_activities_count,json=similarActivitiesCount,proto3" json:"similar_activities_count,omitempty"`
}

func (x *GetSimilarActivitiesCountDetailsResponse) Reset() {
	*x = GetSimilarActivitiesCountDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSimilarActivitiesCountDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSimilarActivitiesCountDetailsResponse) ProtoMessage() {}

func (x *GetSimilarActivitiesCountDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSimilarActivitiesCountDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetSimilarActivitiesCountDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetSimilarActivitiesCountDetailsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSimilarActivitiesCountDetailsResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetSimilarActivitiesCountDetailsResponse) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *GetSimilarActivitiesCountDetailsResponse) GetSimilarActivitiesCount() int32 {
	if x != nil {
		return x.SimilarActivitiesCount
	}
	return 0
}

type UpdateSimilarActivitiesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req        *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	ActivityId string                `protobuf:"bytes,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	// state of check box
	// if some activities are unselected (from initial state) then the state of box is checked with some unselected_activity_ids
	// if some activities are selected from all unchecked state then the state of box is unchecked with some selected_activity_ids
	UpdateSimilarBoxState UpdateSimilarBoxState `protobuf:"varint,3,opt,name=update_similar_box_state,json=updateSimilarBoxState,proto3,enum=frontend.categorizer.UpdateSimilarBoxState" json:"update_similar_box_state,omitempty"`
	// selected_activity_ids should be present if state of UpdateSimilarBoxState is unchecked
	SelectedActivityIds []string `protobuf:"bytes,4,rep,name=selected_activity_ids,json=selectedActivityIds,proto3" json:"selected_activity_ids,omitempty"`
	// unselected_activity_ids can only be present if state of UpdateSimilarBoxState is checked
	UnselectedActivityIds []string `protobuf:"bytes,5,rep,name=unselected_activity_ids,json=unselectedActivityIds,proto3" json:"unselected_activity_ids,omitempty"`
	// list of display category to apply to activities
	DisplayCategories []string `protobuf:"bytes,6,rep,name=display_categories,json=displayCategories,proto3" json:"display_categories,omitempty"`
	// timestamp before which consider all similar activities (in last duration Window months) wil be considered
	// NOTE: this needs to be same across the get and update rpc as well
	EndTimestamp *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=end_timestamp,json=endTimestamp,proto3" json:"end_timestamp,omitempty"`
}

func (x *UpdateSimilarActivitiesRequest) Reset() {
	*x = UpdateSimilarActivitiesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSimilarActivitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSimilarActivitiesRequest) ProtoMessage() {}

func (x *UpdateSimilarActivitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSimilarActivitiesRequest.ProtoReflect.Descriptor instead.
func (*UpdateSimilarActivitiesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateSimilarActivitiesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *UpdateSimilarActivitiesRequest) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *UpdateSimilarActivitiesRequest) GetUpdateSimilarBoxState() UpdateSimilarBoxState {
	if x != nil {
		return x.UpdateSimilarBoxState
	}
	return UpdateSimilarBoxState_UPDATE_SIMILAR_BOX_STATE_UNSPECIFIED
}

func (x *UpdateSimilarActivitiesRequest) GetSelectedActivityIds() []string {
	if x != nil {
		return x.SelectedActivityIds
	}
	return nil
}

func (x *UpdateSimilarActivitiesRequest) GetUnselectedActivityIds() []string {
	if x != nil {
		return x.UnselectedActivityIds
	}
	return nil
}

func (x *UpdateSimilarActivitiesRequest) GetDisplayCategories() []string {
	if x != nil {
		return x.DisplayCategories
	}
	return nil
}

func (x *UpdateSimilarActivitiesRequest) GetEndTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTimestamp
	}
	return nil
}

type UpdateSimilarActivitiesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *UpdateSimilarActivitiesResponse) Reset() {
	*x = UpdateSimilarActivitiesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSimilarActivitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSimilarActivitiesResponse) ProtoMessage() {}

func (x *UpdateSimilarActivitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSimilarActivitiesResponse.ProtoReflect.Descriptor instead.
func (*UpdateSimilarActivitiesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateSimilarActivitiesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type ActivityId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Id:
	//
	//	*ActivityId_TxnId
	//	*ActivityId_OrderId
	//	*ActivityId_AaTxnId
	//	*ActivityId_FiCardTxnId
	Id isActivityId_Id `protobuf_oneof:"id"`
}

func (x *ActivityId) Reset() {
	*x = ActivityId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_categorizer_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityId) ProtoMessage() {}

func (x *ActivityId) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_categorizer_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityId.ProtoReflect.Descriptor instead.
func (*ActivityId) Descriptor() ([]byte, []int) {
	return file_api_frontend_categorizer_service_proto_rawDescGZIP(), []int{14}
}

func (m *ActivityId) GetId() isActivityId_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *ActivityId) GetTxnId() string {
	if x, ok := x.GetId().(*ActivityId_TxnId); ok {
		return x.TxnId
	}
	return ""
}

func (x *ActivityId) GetOrderId() string {
	if x, ok := x.GetId().(*ActivityId_OrderId); ok {
		return x.OrderId
	}
	return ""
}

func (x *ActivityId) GetAaTxnId() string {
	if x, ok := x.GetId().(*ActivityId_AaTxnId); ok {
		return x.AaTxnId
	}
	return ""
}

func (x *ActivityId) GetFiCardTxnId() string {
	if x, ok := x.GetId().(*ActivityId_FiCardTxnId); ok {
		return x.FiCardTxnId
	}
	return ""
}

type isActivityId_Id interface {
	isActivityId_Id()
}

type ActivityId_TxnId struct {
	// if activity is a transaction type then use txn_id
	TxnId string `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3,oneof"`
}

type ActivityId_OrderId struct {
	// if activity is an order type then use order_id
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3,oneof"`
}

type ActivityId_AaTxnId struct {
	// if activity is an aa_txn type then use aa_txn_id
	AaTxnId string `protobuf:"bytes,3,opt,name=aa_txn_id,json=aaTxnId,proto3,oneof"`
}

type ActivityId_FiCardTxnId struct {
	// FiCardTxnId should be passed if categories for FI credit card txn are required
	FiCardTxnId string `protobuf:"bytes,4,opt,name=fi_card_txn_id,json=fiCardTxnId,proto3,oneof"`
}

func (*ActivityId_TxnId) isActivityId_Id() {}

func (*ActivityId_OrderId) isActivityId_Id() {}

func (*ActivityId_AaTxnId) isActivityId_Id() {}

func (*ActivityId_FiCardTxnId) isActivityId_Id() {}

var File_api_frontend_categorizer_service_proto protoreflect.FileDescriptor

var file_api_frontend_categorizer_service_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x1a, 0x21,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x70, 0x61, 0x79, 0x2f, 0x74, 0x78, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xbe, 0x01, 0x0a, 0x28, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x41, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x22, 0xaf, 0x04, 0x0a, 0x29, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x27, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x54, 0x0a, 0x14, 0x73, 0x75,
	0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x13, 0x73, 0x75, 0x67,
	0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x48, 0x0a, 0x0e, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0d, 0x61, 0x6c, 0x6c,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x62, 0x0a, 0x18, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66,
	0x75, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65, 0x54, 0x78, 0x6e, 0x43, 0x61, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x14, 0x75, 0x73, 0x65, 0x72, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65,
	0x54, 0x78, 0x6e, 0x43, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x59, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09,
	0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x50,
	0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44,
	0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d,
	0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x10, 0x22, 0xa7, 0x01, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x72, 0x46, 0x75,
	0x74, 0x75, 0x72, 0x65, 0x54, 0x78, 0x6e, 0x43, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49,
	0x0a, 0x22, 0x63, 0x61, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x66,
	0x75, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1d, 0x63, 0x61, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x65, 0x74, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65, 0x54, 0x78, 0x6e, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x44, 0x0a, 0x20, 0x69, 0x73, 0x5f,
	0x66, 0x75, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x5f, 0x73,
	0x65, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1a, 0x69, 0x73, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65, 0x54, 0x78, 0x6e,
	0x43, 0x61, 0x74, 0x53, 0x65, 0x74, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22,
	0xa9, 0x02, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1f, 0x0a,
	0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x43,
	0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x49, 0x64, 0x73, 0x12, 0x53, 0x0a, 0x27, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x66,
	0x75, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x5f, 0x74, 0x78,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x22, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x46, 0x75, 0x74, 0x75, 0x72,
	0x65, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x54, 0x78, 0x6e, 0x22, 0xe0, 0x01, 0x0a, 0x18,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x27, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x22, 0x59, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x41,
	0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54, 0x45, 0x44, 0x10, 0x10, 0x22, 0xfb,
	0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30,
	0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0b, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x0a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x23,
	0x0a, 0x0c, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x21, 0x0a, 0x0b, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x66, 0x74, 0x65,
	0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xf4, 0x02, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x27, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4a, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x65, 0x66, 0x6f,
	0x72, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x66,
	0x74, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x59, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41,
	0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52,
	0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x07,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x13,
	0x0a, 0x0f, 0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54, 0x45,
	0x44, 0x10, 0x10, 0x22, 0x89, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x64, 0x52, 0x0f,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22,
	0x81, 0x01, 0x0a, 0x0f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72,
	0x6c, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x63, 0x6f, 0x6e,
	0x55, 0x72, 0x6c, 0x22, 0xa0, 0x03, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x75, 0x0a, 0x10, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x1a, 0x69, 0x0a, 0x14, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72,
	0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x59, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11,
	0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45,
	0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x10, 0x22, 0xbd, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x53, 0x69,
	0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0d, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xab, 0x02, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x53, 0x69,
	0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62,
	0x6f, 0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12,
	0x38, 0x0a, 0x18, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x16, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x59, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45,
	0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10,
	0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12,
	0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x10, 0x22, 0xb5, 0x03, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x64, 0x0a, 0x18, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x5f, 0x62, 0x6f, 0x78,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x7a, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61,
	0x72, 0x42, 0x6f, 0x78, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x15, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x42, 0x6f, 0x78, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x32, 0x0a, 0x15, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x13, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x75, 0x6e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x75, 0x6e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2d, 0x0a, 0x12,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x0d, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x63, 0x0a, 0x1f,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0x8d, 0x01, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x61, 0x5f, 0x74, 0x78, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x61, 0x54,
	0x78, 0x6e, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x69, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b,
	0x66, 0x69, 0x43, 0x61, 0x72, 0x64, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x42, 0x04, 0x0a, 0x02, 0x69,
	0x64, 0x2a, 0x8f, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x69,
	0x6c, 0x61, 0x72, 0x42, 0x6f, 0x78, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x49, 0x4d, 0x49, 0x4c, 0x41, 0x52, 0x5f, 0x42, 0x4f,
	0x58, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x53, 0x49, 0x4d, 0x49, 0x4c, 0x41, 0x52, 0x5f, 0x42, 0x4f, 0x58, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x49, 0x4d, 0x49, 0x4c, 0x41, 0x52, 0x5f, 0x42, 0x4f,
	0x58, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45,
	0x44, 0x10, 0x02, 0x32, 0xcc, 0x07, 0x0a, 0x0e, 0x54, 0x78, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x12, 0xb5, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x7a, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x7a, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80,
	0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x82,
	0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e,
	0xd7, 0x0a, 0x00, 0x12, 0x8e, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c,
	0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x31, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x7a, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61,
	0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0,
	0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x9c, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x12, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80,
	0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e,
	0xd7, 0x0a, 0x00, 0x12, 0xb2, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c,
	0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e,
	0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x97, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x12, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65,
	0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x7a, 0x65, 0x72, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_categorizer_service_proto_rawDescOnce sync.Once
	file_api_frontend_categorizer_service_proto_rawDescData = file_api_frontend_categorizer_service_proto_rawDesc
)

func file_api_frontend_categorizer_service_proto_rawDescGZIP() []byte {
	file_api_frontend_categorizer_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_categorizer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_categorizer_service_proto_rawDescData)
	})
	return file_api_frontend_categorizer_service_proto_rawDescData
}

var file_api_frontend_categorizer_service_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_frontend_categorizer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_api_frontend_categorizer_service_proto_goTypes = []interface{}{
	(UpdateSimilarBoxState)(0),                            // 0: frontend.categorizer.UpdateSimilarBoxState
	(ListCategoriesForRecategorizationResponse_Status)(0), // 1: frontend.categorizer.ListCategoriesForRecategorizationResponse.Status
	(UpdateCategoriesResponse_Status)(0),                  // 2: frontend.categorizer.UpdateCategoriesResponse.Status
	(GetSimilarActivitiesResponse_Status)(0),              // 3: frontend.categorizer.GetSimilarActivitiesResponse.Status
	(GetCategoryDetailsBatchResponse_Status)(0),           // 4: frontend.categorizer.GetCategoryDetailsBatchResponse.Status
	(GetSimilarActivitiesCountDetailsResponse_Status)(0),  // 5: frontend.categorizer.GetSimilarActivitiesCountDetailsResponse.Status
	(*ListCategoriesForRecategorizationRequest)(nil),      // 6: frontend.categorizer.ListCategoriesForRecategorizationRequest
	(*ListCategoriesForRecategorizationResponse)(nil),     // 7: frontend.categorizer.ListCategoriesForRecategorizationResponse
	(*UserFutureTxnCatInfo)(nil),                          // 8: frontend.categorizer.UserFutureTxnCatInfo
	(*UpdateCategoriesRequest)(nil),                       // 9: frontend.categorizer.UpdateCategoriesRequest
	(*UpdateCategoriesResponse)(nil),                      // 10: frontend.categorizer.UpdateCategoriesResponse
	(*GetSimilarActivitiesRequest)(nil),                   // 11: frontend.categorizer.GetSimilarActivitiesRequest
	(*GetSimilarActivitiesResponse)(nil),                  // 12: frontend.categorizer.GetSimilarActivitiesResponse
	(*GetCategoryDetailsBatchRequest)(nil),                // 13: frontend.categorizer.GetCategoryDetailsBatchRequest
	(*CategoryDetails)(nil),                               // 14: frontend.categorizer.CategoryDetails
	(*GetCategoryDetailsBatchResponse)(nil),               // 15: frontend.categorizer.GetCategoryDetailsBatchResponse
	(*GetSimilarActivitiesCountDetailsRequest)(nil),       // 16: frontend.categorizer.GetSimilarActivitiesCountDetailsRequest
	(*GetSimilarActivitiesCountDetailsResponse)(nil),      // 17: frontend.categorizer.GetSimilarActivitiesCountDetailsResponse
	(*UpdateSimilarActivitiesRequest)(nil),                // 18: frontend.categorizer.UpdateSimilarActivitiesRequest
	(*UpdateSimilarActivitiesResponse)(nil),               // 19: frontend.categorizer.UpdateSimilarActivitiesResponse
	(*ActivityId)(nil),                                    // 20: frontend.categorizer.ActivityId
	nil,                                                   // 21: frontend.categorizer.GetCategoryDetailsBatchResponse.CategoryDetailsEntry
	(*header.RequestHeader)(nil),                          // 22: frontend.header.RequestHeader
	(*rpc.Status)(nil),                                    // 23: rpc.Status
	(*pay.TransactionCategory)(nil),                       // 24: frontend.pay.TransactionCategory
	(*header.ResponseHeader)(nil),                         // 25: frontend.header.ResponseHeader
	(*widget.TransactionsWidget)(nil),                     // 26: frontend.search.widget.TransactionsWidget
	(*timestamppb.Timestamp)(nil),                         // 27: google.protobuf.Timestamp
}
var file_api_frontend_categorizer_service_proto_depIdxs = []int32{
	22, // 0: frontend.categorizer.ListCategoriesForRecategorizationRequest.req:type_name -> frontend.header.RequestHeader
	20, // 1: frontend.categorizer.ListCategoriesForRecategorizationRequest.activity_id:type_name -> frontend.categorizer.ActivityId
	23, // 2: frontend.categorizer.ListCategoriesForRecategorizationResponse.status:type_name -> rpc.Status
	24, // 3: frontend.categorizer.ListCategoriesForRecategorizationResponse.suggested_categories:type_name -> frontend.pay.TransactionCategory
	24, // 4: frontend.categorizer.ListCategoriesForRecategorizationResponse.all_categories:type_name -> frontend.pay.TransactionCategory
	25, // 5: frontend.categorizer.ListCategoriesForRecategorizationResponse.resp_header:type_name -> frontend.header.ResponseHeader
	8,  // 6: frontend.categorizer.ListCategoriesForRecategorizationResponse.user_future_txn_cat_info:type_name -> frontend.categorizer.UserFutureTxnCatInfo
	22, // 7: frontend.categorizer.UpdateCategoriesRequest.req:type_name -> frontend.header.RequestHeader
	20, // 8: frontend.categorizer.UpdateCategoriesRequest.activity_ids:type_name -> frontend.categorizer.ActivityId
	23, // 9: frontend.categorizer.UpdateCategoriesResponse.status:type_name -> rpc.Status
	25, // 10: frontend.categorizer.UpdateCategoriesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	22, // 11: frontend.categorizer.GetSimilarActivitiesRequest.req:type_name -> frontend.header.RequestHeader
	23, // 12: frontend.categorizer.GetSimilarActivitiesResponse.status:type_name -> rpc.Status
	25, // 13: frontend.categorizer.GetSimilarActivitiesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	26, // 14: frontend.categorizer.GetSimilarActivitiesResponse.activities:type_name -> frontend.search.widget.TransactionsWidget
	22, // 15: frontend.categorizer.GetCategoryDetailsBatchRequest.req:type_name -> frontend.header.RequestHeader
	25, // 16: frontend.categorizer.GetCategoryDetailsBatchResponse.resp_header:type_name -> frontend.header.ResponseHeader
	21, // 17: frontend.categorizer.GetCategoryDetailsBatchResponse.category_details:type_name -> frontend.categorizer.GetCategoryDetailsBatchResponse.CategoryDetailsEntry
	22, // 18: frontend.categorizer.GetSimilarActivitiesCountDetailsRequest.req:type_name -> frontend.header.RequestHeader
	27, // 19: frontend.categorizer.GetSimilarActivitiesCountDetailsRequest.end_timestamp:type_name -> google.protobuf.Timestamp
	25, // 20: frontend.categorizer.GetSimilarActivitiesCountDetailsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	22, // 21: frontend.categorizer.UpdateSimilarActivitiesRequest.req:type_name -> frontend.header.RequestHeader
	0,  // 22: frontend.categorizer.UpdateSimilarActivitiesRequest.update_similar_box_state:type_name -> frontend.categorizer.UpdateSimilarBoxState
	27, // 23: frontend.categorizer.UpdateSimilarActivitiesRequest.end_timestamp:type_name -> google.protobuf.Timestamp
	25, // 24: frontend.categorizer.UpdateSimilarActivitiesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	14, // 25: frontend.categorizer.GetCategoryDetailsBatchResponse.CategoryDetailsEntry.value:type_name -> frontend.categorizer.CategoryDetails
	6,  // 26: frontend.categorizer.TxnCategorizer.ListCategoriesForRecategorization:input_type -> frontend.categorizer.ListCategoriesForRecategorizationRequest
	9,  // 27: frontend.categorizer.TxnCategorizer.UpdateCategories:input_type -> frontend.categorizer.UpdateCategoriesRequest
	11, // 28: frontend.categorizer.TxnCategorizer.GetSimilarActivities:input_type -> frontend.categorizer.GetSimilarActivitiesRequest
	13, // 29: frontend.categorizer.TxnCategorizer.GetCategoryDetailsBatch:input_type -> frontend.categorizer.GetCategoryDetailsBatchRequest
	16, // 30: frontend.categorizer.TxnCategorizer.GetSimilarActivitiesCountDetails:input_type -> frontend.categorizer.GetSimilarActivitiesCountDetailsRequest
	18, // 31: frontend.categorizer.TxnCategorizer.UpdateSimilarActivities:input_type -> frontend.categorizer.UpdateSimilarActivitiesRequest
	7,  // 32: frontend.categorizer.TxnCategorizer.ListCategoriesForRecategorization:output_type -> frontend.categorizer.ListCategoriesForRecategorizationResponse
	10, // 33: frontend.categorizer.TxnCategorizer.UpdateCategories:output_type -> frontend.categorizer.UpdateCategoriesResponse
	12, // 34: frontend.categorizer.TxnCategorizer.GetSimilarActivities:output_type -> frontend.categorizer.GetSimilarActivitiesResponse
	15, // 35: frontend.categorizer.TxnCategorizer.GetCategoryDetailsBatch:output_type -> frontend.categorizer.GetCategoryDetailsBatchResponse
	17, // 36: frontend.categorizer.TxnCategorizer.GetSimilarActivitiesCountDetails:output_type -> frontend.categorizer.GetSimilarActivitiesCountDetailsResponse
	19, // 37: frontend.categorizer.TxnCategorizer.UpdateSimilarActivities:output_type -> frontend.categorizer.UpdateSimilarActivitiesResponse
	32, // [32:38] is the sub-list for method output_type
	26, // [26:32] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_api_frontend_categorizer_service_proto_init() }
func file_api_frontend_categorizer_service_proto_init() {
	if File_api_frontend_categorizer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_categorizer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCategoriesForRecategorizationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCategoriesForRecategorizationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserFutureTxnCatInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCategoriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCategoriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSimilarActivitiesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSimilarActivitiesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCategoryDetailsBatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCategoryDetailsBatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSimilarActivitiesCountDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSimilarActivitiesCountDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSimilarActivitiesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSimilarActivitiesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_categorizer_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_categorizer_service_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*GetSimilarActivitiesRequest_LatestPage)(nil),
		(*GetSimilarActivitiesRequest_BeforeToken)(nil),
		(*GetSimilarActivitiesRequest_AfterToken)(nil),
	}
	file_api_frontend_categorizer_service_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*ActivityId_TxnId)(nil),
		(*ActivityId_OrderId)(nil),
		(*ActivityId_AaTxnId)(nil),
		(*ActivityId_FiCardTxnId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_categorizer_service_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_categorizer_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_categorizer_service_proto_depIdxs,
		EnumInfos:         file_api_frontend_categorizer_service_proto_enumTypes,
		MessageInfos:      file_api_frontend_categorizer_service_proto_msgTypes,
	}.Build()
	File_api_frontend_categorizer_service_proto = out.File
	file_api_frontend_categorizer_service_proto_rawDesc = nil
	file_api_frontend_categorizer_service_proto_goTypes = nil
	file_api_frontend_categorizer_service_proto_depIdxs = nil
}
