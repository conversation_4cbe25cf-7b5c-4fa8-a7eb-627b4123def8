// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/kyc/vkyc/vkyc.proto

package vkyc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	vkyc "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/vkyc"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = vkyc.VKYCQualityCheck(0)
)

// Validate checks the field values on DateToTimePeriod with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DateToTimePeriod) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DateToTimePeriod with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DateToTimePeriodMultiError, or nil if none found.
func (m *DateToTimePeriod) ValidateAll() error {
	return m.validate(true)
}

func (m *DateToTimePeriod) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Day

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DateToTimePeriodValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DateToTimePeriodValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DateToTimePeriodValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisplayText

	// no validation rules for IsAvailable

	for idx, item := range m.GetTimePeriodToSlotDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DateToTimePeriodValidationError{
						field:  fmt.Sprintf("TimePeriodToSlotDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DateToTimePeriodValidationError{
						field:  fmt.Sprintf("TimePeriodToSlotDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DateToTimePeriodValidationError{
					field:  fmt.Sprintf("TimePeriodToSlotDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DateToTimePeriodMultiError(errors)
	}

	return nil
}

// DateToTimePeriodMultiError is an error wrapping multiple validation errors
// returned by DateToTimePeriod.ValidateAll() if the designated constraints
// aren't met.
type DateToTimePeriodMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DateToTimePeriodMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DateToTimePeriodMultiError) AllErrors() []error { return m }

// DateToTimePeriodValidationError is the validation error returned by
// DateToTimePeriod.Validate if the designated constraints aren't met.
type DateToTimePeriodValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DateToTimePeriodValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DateToTimePeriodValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DateToTimePeriodValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DateToTimePeriodValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DateToTimePeriodValidationError) ErrorName() string { return "DateToTimePeriodValidationError" }

// Error satisfies the builtin error interface
func (e DateToTimePeriodValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDateToTimePeriod.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DateToTimePeriodValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DateToTimePeriodValidationError{}

// Validate checks the field values on TimePeriodToSlotDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TimePeriodToSlotDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimePeriodToSlotDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TimePeriodToSlotDetailsMultiError, or nil if none found.
func (m *TimePeriodToSlotDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *TimePeriodToSlotDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TimePeriod

	if all {
		switch v := interface{}(m.GetPeriodStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimePeriodToSlotDetailsValidationError{
					field:  "PeriodStartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimePeriodToSlotDetailsValidationError{
					field:  "PeriodStartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPeriodStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimePeriodToSlotDetailsValidationError{
				field:  "PeriodStartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPeriodEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimePeriodToSlotDetailsValidationError{
					field:  "PeriodEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimePeriodToSlotDetailsValidationError{
					field:  "PeriodEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPeriodEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimePeriodToSlotDetailsValidationError{
				field:  "PeriodEndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisplayText

	// no validation rules for IsAvailable

	for idx, item := range m.GetSlotDetailsList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TimePeriodToSlotDetailsValidationError{
						field:  fmt.Sprintf("SlotDetailsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TimePeriodToSlotDetailsValidationError{
						field:  fmt.Sprintf("SlotDetailsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TimePeriodToSlotDetailsValidationError{
					field:  fmt.Sprintf("SlotDetailsList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TimePeriodToSlotDetailsMultiError(errors)
	}

	return nil
}

// TimePeriodToSlotDetailsMultiError is an error wrapping multiple validation
// errors returned by TimePeriodToSlotDetails.ValidateAll() if the designated
// constraints aren't met.
type TimePeriodToSlotDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimePeriodToSlotDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimePeriodToSlotDetailsMultiError) AllErrors() []error { return m }

// TimePeriodToSlotDetailsValidationError is the validation error returned by
// TimePeriodToSlotDetails.Validate if the designated constraints aren't met.
type TimePeriodToSlotDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimePeriodToSlotDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimePeriodToSlotDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimePeriodToSlotDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimePeriodToSlotDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimePeriodToSlotDetailsValidationError) ErrorName() string {
	return "TimePeriodToSlotDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e TimePeriodToSlotDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimePeriodToSlotDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimePeriodToSlotDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimePeriodToSlotDetailsValidationError{}

// Validate checks the field values on SlotDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SlotDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SlotDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SlotDetailsMultiError, or
// nil if none found.
func (m *SlotDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *SlotDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SlotId

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SlotDetailsValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SlotDetailsValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SlotDetailsValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SlotDetailsValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SlotDetailsValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SlotDetailsValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisplayText

	// no validation rules for IsAvailable

	if len(errors) > 0 {
		return SlotDetailsMultiError(errors)
	}

	return nil
}

// SlotDetailsMultiError is an error wrapping multiple validation errors
// returned by SlotDetails.ValidateAll() if the designated constraints aren't met.
type SlotDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SlotDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SlotDetailsMultiError) AllErrors() []error { return m }

// SlotDetailsValidationError is the validation error returned by
// SlotDetails.Validate if the designated constraints aren't met.
type SlotDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SlotDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SlotDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SlotDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SlotDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SlotDetailsValidationError) ErrorName() string { return "SlotDetailsValidationError" }

// Error satisfies the builtin error interface
func (e SlotDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSlotDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SlotDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SlotDetailsValidationError{}

// Validate checks the field values on CTA with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *CTA) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CTA with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CTAMultiError, or nil if none found.
func (m *CTA) ValidateAll() error {
	return m.validate(true)
}

func (m *CTA) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CTAValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CTAValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CTAValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CTAMultiError(errors)
	}

	return nil
}

// CTAMultiError is an error wrapping multiple validation errors returned by
// CTA.ValidateAll() if the designated constraints aren't met.
type CTAMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CTAMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CTAMultiError) AllErrors() []error { return m }

// CTAValidationError is the validation error returned by CTA.Validate if the
// designated constraints aren't met.
type CTAValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CTAValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CTAValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CTAValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CTAValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CTAValidationError) ErrorName() string { return "CTAValidationError" }

// Error satisfies the builtin error interface
func (e CTAValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCTA.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CTAValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CTAValidationError{}

// Validate checks the field values on VKYCTile with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VKYCTile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VKYCTile with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VKYCTileMultiError, or nil
// if none found.
func (m *VKYCTile) ValidateAll() error {
	return m.validate(true)
}

func (m *VKYCTile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IconUrl

	// no validation rules for Title

	// no validation rules for Details

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VKYCTileValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VKYCTileValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VKYCTileValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShowTile

	for idx, item := range m.GetCtaList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VKYCTileValidationError{
						field:  fmt.Sprintf("CtaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VKYCTileValidationError{
						field:  fmt.Sprintf("CtaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VKYCTileValidationError{
					field:  fmt.Sprintf("CtaList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCtaListNewUser() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VKYCTileValidationError{
						field:  fmt.Sprintf("CtaListNewUser[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VKYCTileValidationError{
						field:  fmt.Sprintf("CtaListNewUser[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VKYCTileValidationError{
					field:  fmt.Sprintf("CtaListNewUser[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PopupTileType

	// no validation rules for BackgroundColorHex

	for idx, item := range m.GetLandingPageBlockList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VKYCTileValidationError{
						field:  fmt.Sprintf("LandingPageBlockList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VKYCTileValidationError{
						field:  fmt.Sprintf("LandingPageBlockList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VKYCTileValidationError{
					field:  fmt.Sprintf("LandingPageBlockList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for HomeBannerBgColorHex

	if len(errors) > 0 {
		return VKYCTileMultiError(errors)
	}

	return nil
}

// VKYCTileMultiError is an error wrapping multiple validation errors returned
// by VKYCTile.ValidateAll() if the designated constraints aren't met.
type VKYCTileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VKYCTileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VKYCTileMultiError) AllErrors() []error { return m }

// VKYCTileValidationError is the validation error returned by
// VKYCTile.Validate if the designated constraints aren't met.
type VKYCTileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VKYCTileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VKYCTileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VKYCTileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VKYCTileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VKYCTileValidationError) ErrorName() string { return "VKYCTileValidationError" }

// Error satisfies the builtin error interface
func (e VKYCTileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVKYCTile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VKYCTileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VKYCTileValidationError{}

// Validate checks the field values on VKYCCallQualityDatum with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VKYCCallQualityDatum) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VKYCCallQualityDatum with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VKYCCallQualityDatumMultiError, or nil if none found.
func (m *VKYCCallQualityDatum) ValidateAll() error {
	return m.validate(true)
}

func (m *VKYCCallQualityDatum) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CheckType

	if all {
		switch v := interface{}(m.GetTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VKYCCallQualityDatumValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VKYCCallQualityDatumValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VKYCCallQualityDatumValidationError{
				field:  "Timestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsCheckPassed

	switch v := m.CheckResultMetadata.(type) {
	case *VKYCCallQualityDatum_LonePresenceResult:
		if v == nil {
			err := VKYCCallQualityDatumValidationError{
				field:  "CheckResultMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLonePresenceResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VKYCCallQualityDatumValidationError{
						field:  "LonePresenceResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VKYCCallQualityDatumValidationError{
						field:  "LonePresenceResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLonePresenceResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VKYCCallQualityDatumValidationError{
					field:  "LonePresenceResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VKYCCallQualityDatum_NoiseResult:
		if v == nil {
			err := VKYCCallQualityDatumValidationError{
				field:  "CheckResultMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNoiseResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VKYCCallQualityDatumValidationError{
						field:  "NoiseResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VKYCCallQualityDatumValidationError{
						field:  "NoiseResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNoiseResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VKYCCallQualityDatumValidationError{
					field:  "NoiseResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VKYCCallQualityDatum_ClearBackgroundResult:
		if v == nil {
			err := VKYCCallQualityDatumValidationError{
				field:  "CheckResultMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetClearBackgroundResult()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VKYCCallQualityDatumValidationError{
						field:  "ClearBackgroundResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VKYCCallQualityDatumValidationError{
						field:  "ClearBackgroundResult",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetClearBackgroundResult()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VKYCCallQualityDatumValidationError{
					field:  "ClearBackgroundResult",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return VKYCCallQualityDatumMultiError(errors)
	}

	return nil
}

// VKYCCallQualityDatumMultiError is an error wrapping multiple validation
// errors returned by VKYCCallQualityDatum.ValidateAll() if the designated
// constraints aren't met.
type VKYCCallQualityDatumMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VKYCCallQualityDatumMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VKYCCallQualityDatumMultiError) AllErrors() []error { return m }

// VKYCCallQualityDatumValidationError is the validation error returned by
// VKYCCallQualityDatum.Validate if the designated constraints aren't met.
type VKYCCallQualityDatumValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VKYCCallQualityDatumValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VKYCCallQualityDatumValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VKYCCallQualityDatumValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VKYCCallQualityDatumValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VKYCCallQualityDatumValidationError) ErrorName() string {
	return "VKYCCallQualityDatumValidationError"
}

// Error satisfies the builtin error interface
func (e VKYCCallQualityDatumValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVKYCCallQualityDatum.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VKYCCallQualityDatumValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VKYCCallQualityDatumValidationError{}

// Validate checks the field values on LonePresenceCheckResultMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LonePresenceCheckResultMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LonePresenceCheckResultMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LonePresenceCheckResultMetadataMultiError, or nil if none found.
func (m *LonePresenceCheckResultMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *LonePresenceCheckResultMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NumberOfFacesFound

	if len(errors) > 0 {
		return LonePresenceCheckResultMetadataMultiError(errors)
	}

	return nil
}

// LonePresenceCheckResultMetadataMultiError is an error wrapping multiple
// validation errors returned by LonePresenceCheckResultMetadata.ValidateAll()
// if the designated constraints aren't met.
type LonePresenceCheckResultMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LonePresenceCheckResultMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LonePresenceCheckResultMetadataMultiError) AllErrors() []error { return m }

// LonePresenceCheckResultMetadataValidationError is the validation error
// returned by LonePresenceCheckResultMetadata.Validate if the designated
// constraints aren't met.
type LonePresenceCheckResultMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LonePresenceCheckResultMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LonePresenceCheckResultMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LonePresenceCheckResultMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LonePresenceCheckResultMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LonePresenceCheckResultMetadataValidationError) ErrorName() string {
	return "LonePresenceCheckResultMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e LonePresenceCheckResultMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLonePresenceCheckResultMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LonePresenceCheckResultMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LonePresenceCheckResultMetadataValidationError{}

// Validate checks the field values on NoiseCheckResultMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoiseCheckResultMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoiseCheckResultMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoiseCheckResultMetadataMultiError, or nil if none found.
func (m *NoiseCheckResultMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *NoiseCheckResultMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NoiseLevel

	if len(errors) > 0 {
		return NoiseCheckResultMetadataMultiError(errors)
	}

	return nil
}

// NoiseCheckResultMetadataMultiError is an error wrapping multiple validation
// errors returned by NoiseCheckResultMetadata.ValidateAll() if the designated
// constraints aren't met.
type NoiseCheckResultMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoiseCheckResultMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoiseCheckResultMetadataMultiError) AllErrors() []error { return m }

// NoiseCheckResultMetadataValidationError is the validation error returned by
// NoiseCheckResultMetadata.Validate if the designated constraints aren't met.
type NoiseCheckResultMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoiseCheckResultMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoiseCheckResultMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoiseCheckResultMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoiseCheckResultMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoiseCheckResultMetadataValidationError) ErrorName() string {
	return "NoiseCheckResultMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e NoiseCheckResultMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoiseCheckResultMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoiseCheckResultMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoiseCheckResultMetadataValidationError{}

// Validate checks the field values on ClearBackgroundCheckResultMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ClearBackgroundCheckResultMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClearBackgroundCheckResultMetadata
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ClearBackgroundCheckResultMetadataMultiError, or nil if none found.
func (m *ClearBackgroundCheckResultMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *ClearBackgroundCheckResultMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LuminanceValue

	if len(errors) > 0 {
		return ClearBackgroundCheckResultMetadataMultiError(errors)
	}

	return nil
}

// ClearBackgroundCheckResultMetadataMultiError is an error wrapping multiple
// validation errors returned by
// ClearBackgroundCheckResultMetadata.ValidateAll() if the designated
// constraints aren't met.
type ClearBackgroundCheckResultMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClearBackgroundCheckResultMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClearBackgroundCheckResultMetadataMultiError) AllErrors() []error { return m }

// ClearBackgroundCheckResultMetadataValidationError is the validation error
// returned by ClearBackgroundCheckResultMetadata.Validate if the designated
// constraints aren't met.
type ClearBackgroundCheckResultMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClearBackgroundCheckResultMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClearBackgroundCheckResultMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClearBackgroundCheckResultMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClearBackgroundCheckResultMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClearBackgroundCheckResultMetadataValidationError) ErrorName() string {
	return "ClearBackgroundCheckResultMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e ClearBackgroundCheckResultMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClearBackgroundCheckResultMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClearBackgroundCheckResultMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClearBackgroundCheckResultMetadataValidationError{}
