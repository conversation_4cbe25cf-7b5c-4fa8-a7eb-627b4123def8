// Code generated by MockGen. DO NOT EDIT.
// Source: api/./frontend/wealthonboarding/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	wealthonboarding "github.com/epifi/gamma/api/frontend/wealthonboarding"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

// MockWealthOnboardingClient is a mock of WealthOnboardingClient interface.
type MockWealthOnboardingClient struct {
	ctrl     *gomock.Controller
	recorder *MockWealthOnboardingClientMockRecorder
}

// MockWealthOnboardingClientMockRecorder is the mock recorder for MockWealthOnboardingClient.
type MockWealthOnboardingClientMockRecorder struct {
	mock *MockWealthOnboardingClient
}

// NewMockWealthOnboardingClient creates a new mock instance.
func NewMockWealthOnboardingClient(ctrl *gomock.Controller) *MockWealthOnboardingClient {
	mock := &MockWealthOnboardingClient{ctrl: ctrl}
	mock.recorder = &MockWealthOnboardingClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWealthOnboardingClient) EXPECT() *MockWealthOnboardingClientMockRecorder {
	return m.recorder
}

// CollectDataFromCustomer mocks base method.
func (m *MockWealthOnboardingClient) CollectDataFromCustomer(ctx context.Context, in *wealthonboarding.CollectDataFromCustomerRequest, opts ...grpc.CallOption) (*wealthonboarding.CollectDataFromCustomerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CollectDataFromCustomer", varargs...)
	ret0, _ := ret[0].(*wealthonboarding.CollectDataFromCustomerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectDataFromCustomer indicates an expected call of CollectDataFromCustomer.
func (mr *MockWealthOnboardingClientMockRecorder) CollectDataFromCustomer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectDataFromCustomer", reflect.TypeOf((*MockWealthOnboardingClient)(nil).CollectDataFromCustomer), varargs...)
}

// CollectDataFromCustomerWithStream mocks base method.
func (m *MockWealthOnboardingClient) CollectDataFromCustomerWithStream(ctx context.Context, opts ...grpc.CallOption) (wealthonboarding.WealthOnboarding_CollectDataFromCustomerWithStreamClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CollectDataFromCustomerWithStream", varargs...)
	ret0, _ := ret[0].(wealthonboarding.WealthOnboarding_CollectDataFromCustomerWithStreamClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectDataFromCustomerWithStream indicates an expected call of CollectDataFromCustomerWithStream.
func (mr *MockWealthOnboardingClientMockRecorder) CollectDataFromCustomerWithStream(ctx interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectDataFromCustomerWithStream", reflect.TypeOf((*MockWealthOnboardingClient)(nil).CollectDataFromCustomerWithStream), varargs...)
}

// ConfirmComplianceData mocks base method.
func (m *MockWealthOnboardingClient) ConfirmComplianceData(ctx context.Context, in *wealthonboarding.ConfirmComplianceDataRequest, opts ...grpc.CallOption) (*wealthonboarding.ConfirmComplianceDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmComplianceData", varargs...)
	ret0, _ := ret[0].(*wealthonboarding.ConfirmComplianceDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmComplianceData indicates an expected call of ConfirmComplianceData.
func (mr *MockWealthOnboardingClientMockRecorder) ConfirmComplianceData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmComplianceData", reflect.TypeOf((*MockWealthOnboardingClient)(nil).ConfirmComplianceData), varargs...)
}

// DownloadDigilockerDocs mocks base method.
func (m *MockWealthOnboardingClient) DownloadDigilockerDocs(ctx context.Context, in *wealthonboarding.DownloadDigilockerDocsRequest, opts ...grpc.CallOption) (*wealthonboarding.DownloadDigilockerDocsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DownloadDigilockerDocs", varargs...)
	ret0, _ := ret[0].(*wealthonboarding.DownloadDigilockerDocsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadDigilockerDocs indicates an expected call of DownloadDigilockerDocs.
func (mr *MockWealthOnboardingClientMockRecorder) DownloadDigilockerDocs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadDigilockerDocs", reflect.TypeOf((*MockWealthOnboardingClient)(nil).DownloadDigilockerDocs), varargs...)
}

// GenerateOTP mocks base method.
func (m *MockWealthOnboardingClient) GenerateOTP(ctx context.Context, in *wealthonboarding.GenerateOTPRequest, opts ...grpc.CallOption) (*wealthonboarding.GenerateOTPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateOTP", varargs...)
	ret0, _ := ret[0].(*wealthonboarding.GenerateOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOTP indicates an expected call of GenerateOTP.
func (mr *MockWealthOnboardingClientMockRecorder) GenerateOTP(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOTP", reflect.TypeOf((*MockWealthOnboardingClient)(nil).GenerateOTP), varargs...)
}

// GetNextOnboardingStep mocks base method.
func (m *MockWealthOnboardingClient) GetNextOnboardingStep(ctx context.Context, in *wealthonboarding.GetNextOnboardingStatusRequest, opts ...grpc.CallOption) (*wealthonboarding.GetNextOnboardingStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNextOnboardingStep", varargs...)
	ret0, _ := ret[0].(*wealthonboarding.GetNextOnboardingStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNextOnboardingStep indicates an expected call of GetNextOnboardingStep.
func (mr *MockWealthOnboardingClientMockRecorder) GetNextOnboardingStep(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextOnboardingStep", reflect.TypeOf((*MockWealthOnboardingClient)(nil).GetNextOnboardingStep), varargs...)
}

// GetOnboardingStatus mocks base method.
func (m *MockWealthOnboardingClient) GetOnboardingStatus(ctx context.Context, in *wealthonboarding.GetOnboardingStatusRequest, opts ...grpc.CallOption) (*wealthonboarding.GetOnboardingStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOnboardingStatus", varargs...)
	ret0, _ := ret[0].(*wealthonboarding.GetOnboardingStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOnboardingStatus indicates an expected call of GetOnboardingStatus.
func (mr *MockWealthOnboardingClientMockRecorder) GetOnboardingStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnboardingStatus", reflect.TypeOf((*MockWealthOnboardingClient)(nil).GetOnboardingStatus), varargs...)
}

// GetWealthProfileDetails mocks base method.
func (m *MockWealthOnboardingClient) GetWealthProfileDetails(ctx context.Context, in *wealthonboarding.GetWealthProfileDetailsRequest, opts ...grpc.CallOption) (*wealthonboarding.GetWealthProfileDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWealthProfileDetails", varargs...)
	ret0, _ := ret[0].(*wealthonboarding.GetWealthProfileDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthProfileDetails indicates an expected call of GetWealthProfileDetails.
func (mr *MockWealthOnboardingClientMockRecorder) GetWealthProfileDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthProfileDetails", reflect.TypeOf((*MockWealthOnboardingClient)(nil).GetWealthProfileDetails), varargs...)
}

// TriggerNextOnboardingStepAsync mocks base method.
func (m *MockWealthOnboardingClient) TriggerNextOnboardingStepAsync(ctx context.Context, in *wealthonboarding.TriggerNextOnboardingStepAsyncRequest, opts ...grpc.CallOption) (*wealthonboarding.TriggerNextOnboardingStepAsyncResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerNextOnboardingStepAsync", varargs...)
	ret0, _ := ret[0].(*wealthonboarding.TriggerNextOnboardingStepAsyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerNextOnboardingStepAsync indicates an expected call of TriggerNextOnboardingStepAsync.
func (mr *MockWealthOnboardingClientMockRecorder) TriggerNextOnboardingStepAsync(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerNextOnboardingStepAsync", reflect.TypeOf((*MockWealthOnboardingClient)(nil).TriggerNextOnboardingStepAsync), varargs...)
}

// MockWealthOnboarding_CollectDataFromCustomerWithStreamClient is a mock of WealthOnboarding_CollectDataFromCustomerWithStreamClient interface.
type MockWealthOnboarding_CollectDataFromCustomerWithStreamClient struct {
	ctrl     *gomock.Controller
	recorder *MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder
}

// MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder is the mock recorder for MockWealthOnboarding_CollectDataFromCustomerWithStreamClient.
type MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder struct {
	mock *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient
}

// NewMockWealthOnboarding_CollectDataFromCustomerWithStreamClient creates a new mock instance.
func NewMockWealthOnboarding_CollectDataFromCustomerWithStreamClient(ctrl *gomock.Controller) *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient {
	mock := &MockWealthOnboarding_CollectDataFromCustomerWithStreamClient{ctrl: ctrl}
	mock.recorder = &MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient) EXPECT() *MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder {
	return m.recorder
}

// CloseAndRecv mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient) CloseAndRecv() (*wealthonboarding.CollectDataFromCustomerWithStreamResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseAndRecv")
	ret0, _ := ret[0].(*wealthonboarding.CollectDataFromCustomerWithStreamResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CloseAndRecv indicates an expected call of CloseAndRecv.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder) CloseAndRecv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseAndRecv", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamClient)(nil).CloseAndRecv))
}

// CloseSend mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient) CloseSend() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseSend")
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseSend indicates an expected call of CloseSend.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder) CloseSend() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseSend", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamClient)(nil).CloseSend))
}

// Context mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamClient)(nil).Context))
}

// Header mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient) Header() (metadata.MD, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Header")
	ret0, _ := ret[0].(metadata.MD)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Header indicates an expected call of Header.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder) Header() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Header", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamClient)(nil).Header))
}

// RecvMsg mocks base method.
func (m_2 *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamClient)(nil).RecvMsg), m)
}

// Send mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient) Send(arg0 *wealthonboarding.CollectDataFromCustomerWithStreamRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamClient)(nil).Send), arg0)
}

// SendMsg mocks base method.
func (m_2 *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamClient)(nil).SendMsg), m)
}

// Trailer mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamClient) Trailer() metadata.MD {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trailer")
	ret0, _ := ret[0].(metadata.MD)
	return ret0
}

// Trailer indicates an expected call of Trailer.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamClientMockRecorder) Trailer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trailer", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamClient)(nil).Trailer))
}

// MockWealthOnboardingServer is a mock of WealthOnboardingServer interface.
type MockWealthOnboardingServer struct {
	ctrl     *gomock.Controller
	recorder *MockWealthOnboardingServerMockRecorder
}

// MockWealthOnboardingServerMockRecorder is the mock recorder for MockWealthOnboardingServer.
type MockWealthOnboardingServerMockRecorder struct {
	mock *MockWealthOnboardingServer
}

// NewMockWealthOnboardingServer creates a new mock instance.
func NewMockWealthOnboardingServer(ctrl *gomock.Controller) *MockWealthOnboardingServer {
	mock := &MockWealthOnboardingServer{ctrl: ctrl}
	mock.recorder = &MockWealthOnboardingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWealthOnboardingServer) EXPECT() *MockWealthOnboardingServerMockRecorder {
	return m.recorder
}

// CollectDataFromCustomer mocks base method.
func (m *MockWealthOnboardingServer) CollectDataFromCustomer(arg0 context.Context, arg1 *wealthonboarding.CollectDataFromCustomerRequest) (*wealthonboarding.CollectDataFromCustomerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CollectDataFromCustomer", arg0, arg1)
	ret0, _ := ret[0].(*wealthonboarding.CollectDataFromCustomerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectDataFromCustomer indicates an expected call of CollectDataFromCustomer.
func (mr *MockWealthOnboardingServerMockRecorder) CollectDataFromCustomer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectDataFromCustomer", reflect.TypeOf((*MockWealthOnboardingServer)(nil).CollectDataFromCustomer), arg0, arg1)
}

// CollectDataFromCustomerWithStream mocks base method.
func (m *MockWealthOnboardingServer) CollectDataFromCustomerWithStream(arg0 wealthonboarding.WealthOnboarding_CollectDataFromCustomerWithStreamServer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CollectDataFromCustomerWithStream", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CollectDataFromCustomerWithStream indicates an expected call of CollectDataFromCustomerWithStream.
func (mr *MockWealthOnboardingServerMockRecorder) CollectDataFromCustomerWithStream(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectDataFromCustomerWithStream", reflect.TypeOf((*MockWealthOnboardingServer)(nil).CollectDataFromCustomerWithStream), arg0)
}

// ConfirmComplianceData mocks base method.
func (m *MockWealthOnboardingServer) ConfirmComplianceData(arg0 context.Context, arg1 *wealthonboarding.ConfirmComplianceDataRequest) (*wealthonboarding.ConfirmComplianceDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmComplianceData", arg0, arg1)
	ret0, _ := ret[0].(*wealthonboarding.ConfirmComplianceDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmComplianceData indicates an expected call of ConfirmComplianceData.
func (mr *MockWealthOnboardingServerMockRecorder) ConfirmComplianceData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmComplianceData", reflect.TypeOf((*MockWealthOnboardingServer)(nil).ConfirmComplianceData), arg0, arg1)
}

// DownloadDigilockerDocs mocks base method.
func (m *MockWealthOnboardingServer) DownloadDigilockerDocs(arg0 context.Context, arg1 *wealthonboarding.DownloadDigilockerDocsRequest) (*wealthonboarding.DownloadDigilockerDocsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadDigilockerDocs", arg0, arg1)
	ret0, _ := ret[0].(*wealthonboarding.DownloadDigilockerDocsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadDigilockerDocs indicates an expected call of DownloadDigilockerDocs.
func (mr *MockWealthOnboardingServerMockRecorder) DownloadDigilockerDocs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadDigilockerDocs", reflect.TypeOf((*MockWealthOnboardingServer)(nil).DownloadDigilockerDocs), arg0, arg1)
}

// GenerateOTP mocks base method.
func (m *MockWealthOnboardingServer) GenerateOTP(arg0 context.Context, arg1 *wealthonboarding.GenerateOTPRequest) (*wealthonboarding.GenerateOTPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateOTP", arg0, arg1)
	ret0, _ := ret[0].(*wealthonboarding.GenerateOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOTP indicates an expected call of GenerateOTP.
func (mr *MockWealthOnboardingServerMockRecorder) GenerateOTP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOTP", reflect.TypeOf((*MockWealthOnboardingServer)(nil).GenerateOTP), arg0, arg1)
}

// GetNextOnboardingStep mocks base method.
func (m *MockWealthOnboardingServer) GetNextOnboardingStep(arg0 context.Context, arg1 *wealthonboarding.GetNextOnboardingStatusRequest) (*wealthonboarding.GetNextOnboardingStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNextOnboardingStep", arg0, arg1)
	ret0, _ := ret[0].(*wealthonboarding.GetNextOnboardingStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNextOnboardingStep indicates an expected call of GetNextOnboardingStep.
func (mr *MockWealthOnboardingServerMockRecorder) GetNextOnboardingStep(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextOnboardingStep", reflect.TypeOf((*MockWealthOnboardingServer)(nil).GetNextOnboardingStep), arg0, arg1)
}

// GetOnboardingStatus mocks base method.
func (m *MockWealthOnboardingServer) GetOnboardingStatus(arg0 context.Context, arg1 *wealthonboarding.GetOnboardingStatusRequest) (*wealthonboarding.GetOnboardingStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnboardingStatus", arg0, arg1)
	ret0, _ := ret[0].(*wealthonboarding.GetOnboardingStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOnboardingStatus indicates an expected call of GetOnboardingStatus.
func (mr *MockWealthOnboardingServerMockRecorder) GetOnboardingStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnboardingStatus", reflect.TypeOf((*MockWealthOnboardingServer)(nil).GetOnboardingStatus), arg0, arg1)
}

// GetWealthProfileDetails mocks base method.
func (m *MockWealthOnboardingServer) GetWealthProfileDetails(arg0 context.Context, arg1 *wealthonboarding.GetWealthProfileDetailsRequest) (*wealthonboarding.GetWealthProfileDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWealthProfileDetails", arg0, arg1)
	ret0, _ := ret[0].(*wealthonboarding.GetWealthProfileDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWealthProfileDetails indicates an expected call of GetWealthProfileDetails.
func (mr *MockWealthOnboardingServerMockRecorder) GetWealthProfileDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWealthProfileDetails", reflect.TypeOf((*MockWealthOnboardingServer)(nil).GetWealthProfileDetails), arg0, arg1)
}

// TriggerNextOnboardingStepAsync mocks base method.
func (m *MockWealthOnboardingServer) TriggerNextOnboardingStepAsync(arg0 context.Context, arg1 *wealthonboarding.TriggerNextOnboardingStepAsyncRequest) (*wealthonboarding.TriggerNextOnboardingStepAsyncResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerNextOnboardingStepAsync", arg0, arg1)
	ret0, _ := ret[0].(*wealthonboarding.TriggerNextOnboardingStepAsyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerNextOnboardingStepAsync indicates an expected call of TriggerNextOnboardingStepAsync.
func (mr *MockWealthOnboardingServerMockRecorder) TriggerNextOnboardingStepAsync(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerNextOnboardingStepAsync", reflect.TypeOf((*MockWealthOnboardingServer)(nil).TriggerNextOnboardingStepAsync), arg0, arg1)
}

// MockUnsafeWealthOnboardingServer is a mock of UnsafeWealthOnboardingServer interface.
type MockUnsafeWealthOnboardingServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeWealthOnboardingServerMockRecorder
}

// MockUnsafeWealthOnboardingServerMockRecorder is the mock recorder for MockUnsafeWealthOnboardingServer.
type MockUnsafeWealthOnboardingServerMockRecorder struct {
	mock *MockUnsafeWealthOnboardingServer
}

// NewMockUnsafeWealthOnboardingServer creates a new mock instance.
func NewMockUnsafeWealthOnboardingServer(ctrl *gomock.Controller) *MockUnsafeWealthOnboardingServer {
	mock := &MockUnsafeWealthOnboardingServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeWealthOnboardingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeWealthOnboardingServer) EXPECT() *MockUnsafeWealthOnboardingServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedWealthOnboardingServer mocks base method.
func (m *MockUnsafeWealthOnboardingServer) mustEmbedUnimplementedWealthOnboardingServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedWealthOnboardingServer")
}

// mustEmbedUnimplementedWealthOnboardingServer indicates an expected call of mustEmbedUnimplementedWealthOnboardingServer.
func (mr *MockUnsafeWealthOnboardingServerMockRecorder) mustEmbedUnimplementedWealthOnboardingServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedWealthOnboardingServer", reflect.TypeOf((*MockUnsafeWealthOnboardingServer)(nil).mustEmbedUnimplementedWealthOnboardingServer))
}

// MockWealthOnboarding_CollectDataFromCustomerWithStreamServer is a mock of WealthOnboarding_CollectDataFromCustomerWithStreamServer interface.
type MockWealthOnboarding_CollectDataFromCustomerWithStreamServer struct {
	ctrl     *gomock.Controller
	recorder *MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder
}

// MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder is the mock recorder for MockWealthOnboarding_CollectDataFromCustomerWithStreamServer.
type MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder struct {
	mock *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer
}

// NewMockWealthOnboarding_CollectDataFromCustomerWithStreamServer creates a new mock instance.
func NewMockWealthOnboarding_CollectDataFromCustomerWithStreamServer(ctrl *gomock.Controller) *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer {
	mock := &MockWealthOnboarding_CollectDataFromCustomerWithStreamServer{ctrl: ctrl}
	mock.recorder = &MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer) EXPECT() *MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder {
	return m.recorder
}

// Context mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamServer)(nil).Context))
}

// Recv mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer) Recv() (*wealthonboarding.CollectDataFromCustomerWithStreamRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*wealthonboarding.CollectDataFromCustomerWithStreamRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamServer)(nil).Recv))
}

// RecvMsg mocks base method.
func (m_2 *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamServer)(nil).RecvMsg), m)
}

// SendAndClose mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer) SendAndClose(arg0 *wealthonboarding.CollectDataFromCustomerWithStreamResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendAndClose", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendAndClose indicates an expected call of SendAndClose.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder) SendAndClose(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAndClose", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamServer)(nil).SendAndClose), arg0)
}

// SendHeader mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer) SendHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendHeader indicates an expected call of SendHeader.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder) SendHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendHeader", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamServer)(nil).SendHeader), arg0)
}

// SendMsg mocks base method.
func (m_2 *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamServer)(nil).SendMsg), m)
}

// SetHeader mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer) SetHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder) SetHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamServer)(nil).SetHeader), arg0)
}

// SetTrailer mocks base method.
func (m *MockWealthOnboarding_CollectDataFromCustomerWithStreamServer) SetTrailer(arg0 metadata.MD) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTrailer", arg0)
}

// SetTrailer indicates an expected call of SetTrailer.
func (mr *MockWealthOnboarding_CollectDataFromCustomerWithStreamServerMockRecorder) SetTrailer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTrailer", reflect.TypeOf((*MockWealthOnboarding_CollectDataFromCustomerWithStreamServer)(nil).SetTrailer), arg0)
}
