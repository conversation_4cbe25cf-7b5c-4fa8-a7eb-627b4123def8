// protolint:disable MAX_LINE_LENGTH

// Lists the common messages related to card limit RPC for FE.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/card/card_limit.proto

package card

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	common "github.com/epifi/be-common/api/typesv2/common"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Different category of txn type for limits.
type LimitCategory int32

const (
	LimitCategory_LIMIT_CATEGORY_UNSPECIFIED LimitCategory = 0
	// ATM_WITHDRAWAL will fall under this category.
	LimitCategory_ATM_USAGE LimitCategory = 1
	// NFC, ECOM, POS will fall under this limit category.
	LimitCategory_PURCHASE_LIMIT LimitCategory = 2
)

// Enum value maps for LimitCategory.
var (
	LimitCategory_name = map[int32]string{
		0: "LIMIT_CATEGORY_UNSPECIFIED",
		1: "ATM_USAGE",
		2: "PURCHASE_LIMIT",
	}
	LimitCategory_value = map[string]int32{
		"LIMIT_CATEGORY_UNSPECIFIED": 0,
		"ATM_USAGE":                  1,
		"PURCHASE_LIMIT":             2,
	}
)

func (x LimitCategory) Enum() *LimitCategory {
	p := new(LimitCategory)
	*p = x
	return p
}

func (x LimitCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LimitCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_card_card_limit_proto_enumTypes[0].Descriptor()
}

func (LimitCategory) Type() protoreflect.EnumType {
	return &file_api_frontend_card_card_limit_proto_enumTypes[0]
}

func (x LimitCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LimitCategory.Descriptor instead.
func (LimitCategory) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_card_card_limit_proto_rawDescGZIP(), []int{0}
}

// CardLimitTile holds information to render card limit detail page.
type CardLimitTile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// maximum amount allowed to spend per day on card.
	CurDailyLimit *typesv2.Money `protobuf:"bytes,1,opt,name=cur_daily_limit,json=curDailyLimit,proto3" json:"cur_daily_limit,omitempty"`
	// maximum amount allowed to spend per day on card on different usage type / medium (ex. ATM withdrawal, POS etc)
	CategorisedGrossLimit []*CategorisedGrossCardLimit `protobuf:"bytes,2,rep,name=categorised_gross_limit,json=categorisedGrossLimit,proto3" json:"categorised_gross_limit,omitempty"`
	// info for user understand card limit settings
	//
	// Deprecated: Marked as deprecated in api/frontend/card/card_limit.proto.
	Infos []string `protobuf:"bytes,4,rep,name=infos,proto3" json:"infos,omitempty"`
	// Card limit settings to render individual card settings by transaction type , location type.
	// This will also hold currently configured card limits and provide option to user to set new limits.
	//
	// Deprecated: Marked as deprecated in api/frontend/card/card_limit.proto.
	CardLimitSettings []*CardLimitSetting `protobuf:"bytes,5,rep,name=card_limit_settings,json=cardLimitSettings,proto3" json:"card_limit_settings,omitempty"`
}

func (x *CardLimitTile) Reset() {
	*x = CardLimitTile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_card_card_limit_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardLimitTile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardLimitTile) ProtoMessage() {}

func (x *CardLimitTile) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_card_card_limit_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardLimitTile.ProtoReflect.Descriptor instead.
func (*CardLimitTile) Descriptor() ([]byte, []int) {
	return file_api_frontend_card_card_limit_proto_rawDescGZIP(), []int{0}
}

func (x *CardLimitTile) GetCurDailyLimit() *typesv2.Money {
	if x != nil {
		return x.CurDailyLimit
	}
	return nil
}

func (x *CardLimitTile) GetCategorisedGrossLimit() []*CategorisedGrossCardLimit {
	if x != nil {
		return x.CategorisedGrossLimit
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/card/card_limit.proto.
func (x *CardLimitTile) GetInfos() []string {
	if x != nil {
		return x.Infos
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/card/card_limit.proto.
func (x *CardLimitTile) GetCardLimitSettings() []*CardLimitSetting {
	if x != nil {
		return x.CardLimitSettings
	}
	return nil
}

// CardLimitSetting holds information of currently configured card limit categorised by location type.
// deprecated
type CardLimitSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Location where the limit is applicable(DOMESTIC/INTERNATIONAL).
	//
	// Deprecated: Marked as deprecated in api/frontend/card/card_limit.proto.
	LocType CardUsageLocationType `protobuf:"varint,1,opt,name=loc_type,json=locType,proto3,enum=frontend.card.CardUsageLocationType" json:"loc_type,omitempty"`
	// all limits configured for different category of limit type.
	//
	// Deprecated: Marked as deprecated in api/frontend/card/card_limit.proto.
	CategorisedLimit []*CategorisedLimit `protobuf:"bytes,2,rep,name=categorised_limit,json=categorisedLimit,proto3" json:"categorised_limit,omitempty"`
}

func (x *CardLimitSetting) Reset() {
	*x = CardLimitSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_card_card_limit_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardLimitSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardLimitSetting) ProtoMessage() {}

func (x *CardLimitSetting) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_card_card_limit_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardLimitSetting.ProtoReflect.Descriptor instead.
func (*CardLimitSetting) Descriptor() ([]byte, []int) {
	return file_api_frontend_card_card_limit_proto_rawDescGZIP(), []int{1}
}

// Deprecated: Marked as deprecated in api/frontend/card/card_limit.proto.
func (x *CardLimitSetting) GetLocType() CardUsageLocationType {
	if x != nil {
		return x.LocType
	}
	return CardUsageLocationType_LOCATION_TYPE_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/frontend/card/card_limit.proto.
func (x *CardLimitSetting) GetCategorisedLimit() []*CategorisedLimit {
	if x != nil {
		return x.CategorisedLimit
	}
	return nil
}

// Categorised limit holds info of all limits by limit category.
type CategorisedLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Category in which this limit is fall (ex. ATM_USAGE / PURCHASE_LIMIT)
	//
	// Deprecated: Marked as deprecated in api/frontend/card/card_limit.proto.
	LimitCategory LimitCategory `protobuf:"varint,1,opt,name=limit_category,json=limitCategory,proto3,enum=frontend.card.LimitCategory" json:"limit_category,omitempty"`
	// individual card limit fall under the category and current and max value configured for them.
	CardLimitDetails []*CardLimitDetail `protobuf:"bytes,3,rep,name=card_limit_details,json=cardLimitDetails,proto3" json:"card_limit_details,omitempty"`
	// Location where the limit is applicable(DOMESTIC/INTERNATIONAL).
	LocType CardUsageLocationType `protobuf:"varint,4,opt,name=loc_type,json=locType,proto3,enum=frontend.card.CardUsageLocationType" json:"loc_type,omitempty"`
	// text to be shown in case if any of the card limit details in card_limit_details is inactive
	InfoString string `protobuf:"bytes,5,opt,name=info_string,json=infoString,proto3" json:"info_string,omitempty"`
	// Used in case of international ATM_USAGE
	InternationalLimitDetails []*InternationalLimitDetail `protobuf:"bytes,6,rep,name=international_limit_details,json=internationalLimitDetails,proto3" json:"international_limit_details,omitempty"`
	// Used in case of international ATM_USAGE
	DefaultSelectedCountry *InternationalLimitDetail `protobuf:"bytes,7,opt,name=default_selected_country,json=defaultSelectedCountry,proto3" json:"default_selected_country,omitempty"`
	// step value for slider; the slider will move by given value
	SliderStep int32 `protobuf:"varint,8,opt,name=slider_step,json=sliderStep,proto3" json:"slider_step,omitempty"`
}

func (x *CategorisedLimit) Reset() {
	*x = CategorisedLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_card_card_limit_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategorisedLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategorisedLimit) ProtoMessage() {}

func (x *CategorisedLimit) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_card_card_limit_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategorisedLimit.ProtoReflect.Descriptor instead.
func (*CategorisedLimit) Descriptor() ([]byte, []int) {
	return file_api_frontend_card_card_limit_proto_rawDescGZIP(), []int{2}
}

// Deprecated: Marked as deprecated in api/frontend/card/card_limit.proto.
func (x *CategorisedLimit) GetLimitCategory() LimitCategory {
	if x != nil {
		return x.LimitCategory
	}
	return LimitCategory_LIMIT_CATEGORY_UNSPECIFIED
}

func (x *CategorisedLimit) GetCardLimitDetails() []*CardLimitDetail {
	if x != nil {
		return x.CardLimitDetails
	}
	return nil
}

func (x *CategorisedLimit) GetLocType() CardUsageLocationType {
	if x != nil {
		return x.LocType
	}
	return CardUsageLocationType_LOCATION_TYPE_UNSPECIFIED
}

func (x *CategorisedLimit) GetInfoString() string {
	if x != nil {
		return x.InfoString
	}
	return ""
}

func (x *CategorisedLimit) GetInternationalLimitDetails() []*InternationalLimitDetail {
	if x != nil {
		return x.InternationalLimitDetails
	}
	return nil
}

func (x *CategorisedLimit) GetDefaultSelectedCountry() *InternationalLimitDetail {
	if x != nil {
		return x.DefaultSelectedCountry
	}
	return nil
}

func (x *CategorisedLimit) GetSliderStep() int32 {
	if x != nil {
		return x.SliderStep
	}
	return 0
}

// https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=22011-47084&t=cL69XohnQCMqDVPV-4
type InternationalLimitDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountryName     *common.Text   `protobuf:"bytes,1,opt,name=country_name,json=countryName,proto3" json:"country_name,omitempty"`
	Flag            *common.Text   `protobuf:"bytes,2,opt,name=flag,proto3" json:"flag,omitempty"`
	CountryMaxLimit *typesv2.Money `protobuf:"bytes,3,opt,name=country_max_limit,json=countryMaxLimit,proto3" json:"country_max_limit,omitempty"`
	// Use this text to display limit info for the selected country
	// replace {{country_max_limit}}, {{country_name}} and {{user_max_limit}} with actual values
	// show this only if `CategorisedLimit.info_string` is empty
	InfoText *common.Text `protobuf:"bytes,4,opt,name=info_text,json=infoText,proto3" json:"info_text,omitempty"`
	// code unique to each country, which can be used as request params in other.
	// The ID will be populated from https://github.com/epiFi/gamma/blob/a0fbb54faa0e453c033405bb29fc33283365a671/webfe/travel/constant.go
	// Any further usage of this ID must match with the CountryInfoMap keys
	CountryCode string `protobuf:"bytes,5,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
}

func (x *InternationalLimitDetail) Reset() {
	*x = InternationalLimitDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_card_card_limit_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternationalLimitDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternationalLimitDetail) ProtoMessage() {}

func (x *InternationalLimitDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_card_card_limit_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternationalLimitDetail.ProtoReflect.Descriptor instead.
func (*InternationalLimitDetail) Descriptor() ([]byte, []int) {
	return file_api_frontend_card_card_limit_proto_rawDescGZIP(), []int{3}
}

func (x *InternationalLimitDetail) GetCountryName() *common.Text {
	if x != nil {
		return x.CountryName
	}
	return nil
}

func (x *InternationalLimitDetail) GetFlag() *common.Text {
	if x != nil {
		return x.Flag
	}
	return nil
}

func (x *InternationalLimitDetail) GetCountryMaxLimit() *typesv2.Money {
	if x != nil {
		return x.CountryMaxLimit
	}
	return nil
}

func (x *InternationalLimitDetail) GetInfoText() *common.Text {
	if x != nil {
		return x.InfoText
	}
	return nil
}

func (x *InternationalLimitDetail) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

type CardLimitDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// current amount set for the limit. User can't use money more than this amount on txnType+locType channel.
	CurrentAllowedAmount *typesv2.Money `protobuf:"bytes,1,opt,name=current_allowed_amount,json=currentAllowedAmount,proto3" json:"current_allowed_amount,omitempty"`
	// max amount that can be set in current amount for limit.
	MaxAllowedAmount *typesv2.Money `protobuf:"bytes,2,opt,name=max_allowed_amount,json=maxAllowedAmount,proto3" json:"max_allowed_amount,omitempty"`
	// Transaction type for which card limit is applicable.
	TxnType CardTransactionType `protobuf:"varint,3,opt,name=txn_type,json=txnType,proto3,enum=frontend.card.CardTransactionType" json:"txn_type,omitempty"`
	// Boolean to mark if limit can be changed or not.
	// True -> Corresponding limit change api can be invoked, False -> UI to disable any action (eg: no click allowed)
	Active bool `protobuf:"varint,4,opt,name=active,proto3" json:"active,omitempty"`
	// minimum amount allowed to set as a limit
	MinAllowedAmount *typesv2.Money `protobuf:"bytes,5,opt,name=min_allowed_amount,json=minAllowedAmount,proto3" json:"min_allowed_amount,omitempty"`
}

func (x *CardLimitDetail) Reset() {
	*x = CardLimitDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_card_card_limit_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardLimitDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardLimitDetail) ProtoMessage() {}

func (x *CardLimitDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_card_card_limit_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardLimitDetail.ProtoReflect.Descriptor instead.
func (*CardLimitDetail) Descriptor() ([]byte, []int) {
	return file_api_frontend_card_card_limit_proto_rawDescGZIP(), []int{4}
}

func (x *CardLimitDetail) GetCurrentAllowedAmount() *typesv2.Money {
	if x != nil {
		return x.CurrentAllowedAmount
	}
	return nil
}

func (x *CardLimitDetail) GetMaxAllowedAmount() *typesv2.Money {
	if x != nil {
		return x.MaxAllowedAmount
	}
	return nil
}

func (x *CardLimitDetail) GetTxnType() CardTransactionType {
	if x != nil {
		return x.TxnType
	}
	return CardTransactionType_CARD_TXN_TYPE_UNSPECIFIED
}

func (x *CardLimitDetail) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *CardLimitDetail) GetMinAllowedAmount() *typesv2.Money {
	if x != nil {
		return x.MinAllowedAmount
	}
	return nil
}

// CategorisedGrossCardLimit holds information of gross limit applicable for each category.
type CategorisedGrossCardLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// limit category
	LimitCategory LimitCategory `protobuf:"varint,1,opt,name=limit_category,json=limitCategory,proto3,enum=frontend.card.LimitCategory" json:"limit_category,omitempty"`
	// maximum amount allowed for all txn type falls under this category.
	MaxLimit *typesv2.Money `protobuf:"bytes,2,opt,name=max_limit,json=maxLimit,proto3" json:"max_limit,omitempty"`
	// Display text need to use for this category.
	DisplayText string `protobuf:"bytes,3,opt,name=display_text,json=displayText,proto3" json:"display_text,omitempty"`
	// description of category
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// all limits configured for this category of limit type.
	CategorisedLimits []*CategorisedLimit `protobuf:"bytes,5,rep,name=categorised_limits,json=categorisedLimits,proto3" json:"categorised_limits,omitempty"`
}

func (x *CategorisedGrossCardLimit) Reset() {
	*x = CategorisedGrossCardLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_card_card_limit_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategorisedGrossCardLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategorisedGrossCardLimit) ProtoMessage() {}

func (x *CategorisedGrossCardLimit) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_card_card_limit_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategorisedGrossCardLimit.ProtoReflect.Descriptor instead.
func (*CategorisedGrossCardLimit) Descriptor() ([]byte, []int) {
	return file_api_frontend_card_card_limit_proto_rawDescGZIP(), []int{5}
}

func (x *CategorisedGrossCardLimit) GetLimitCategory() LimitCategory {
	if x != nil {
		return x.LimitCategory
	}
	return LimitCategory_LIMIT_CATEGORY_UNSPECIFIED
}

func (x *CategorisedGrossCardLimit) GetMaxLimit() *typesv2.Money {
	if x != nil {
		return x.MaxLimit
	}
	return nil
}

func (x *CategorisedGrossCardLimit) GetDisplayText() string {
	if x != nil {
		return x.DisplayText
	}
	return ""
}

func (x *CategorisedGrossCardLimit) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CategorisedGrossCardLimit) GetCategorisedLimits() []*CategorisedLimit {
	if x != nil {
		return x.CategorisedLimits
	}
	return nil
}

type UpdateCardLimitDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// new amount the user want to set as a limit
	UpdatedAllowedAmount *typesv2.Money `protobuf:"bytes,1,opt,name=updated_allowed_amount,json=updatedAllowedAmount,proto3" json:"updated_allowed_amount,omitempty"`
	// location type for which limit will be applicable.
	LocType CardUsageLocationType `protobuf:"varint,2,opt,name=loc_type,json=locType,proto3,enum=frontend.card.CardUsageLocationType" json:"loc_type,omitempty"`
	// transaction type for which card limit is applicable.
	TxnType CardTransactionType `protobuf:"varint,3,opt,name=txn_type,json=txnType,proto3,enum=frontend.card.CardTransactionType" json:"txn_type,omitempty"`
}

func (x *UpdateCardLimitDetail) Reset() {
	*x = UpdateCardLimitDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_card_card_limit_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCardLimitDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCardLimitDetail) ProtoMessage() {}

func (x *UpdateCardLimitDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_card_card_limit_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCardLimitDetail.ProtoReflect.Descriptor instead.
func (*UpdateCardLimitDetail) Descriptor() ([]byte, []int) {
	return file_api_frontend_card_card_limit_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateCardLimitDetail) GetUpdatedAllowedAmount() *typesv2.Money {
	if x != nil {
		return x.UpdatedAllowedAmount
	}
	return nil
}

func (x *UpdateCardLimitDetail) GetLocType() CardUsageLocationType {
	if x != nil {
		return x.LocType
	}
	return CardUsageLocationType_LOCATION_TYPE_UNSPECIFIED
}

func (x *UpdateCardLimitDetail) GetTxnType() CardTransactionType {
	if x != nil {
		return x.TxnType
	}
	return CardTransactionType_CARD_TXN_TYPE_UNSPECIFIED
}

var File_api_frontend_card_card_limit_proto protoreflect.FileDescriptor

var file_api_frontend_card_card_limit_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x9c, 0x02, 0x0a, 0x0d, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x54, 0x69, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x5f, 0x64, 0x61, 0x69, 0x6c,
	0x79, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x60, 0x0a, 0x17, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x64, 0x5f,
	0x67, 0x72, 0x6f, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x64, 0x47, 0x72, 0x6f,
	0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x15, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x73, 0x73, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x18, 0x0a, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x53, 0x0a, 0x13,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11,
	0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x22, 0xa9, 0x01, 0x0a, 0x10, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x43, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x55, 0x73, 0x61,
	0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x50, 0x0a, 0x11, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73,
	0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xf8, 0x03,
	0x0a, 0x10, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x64, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x47, 0x0a, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x4c, 0x0a, 0x12, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x10, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3f, 0x0a, 0x08, 0x6c, 0x6f, 0x63,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x55, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e,
	0x66, 0x6f, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x69, 0x6e, 0x66, 0x6f, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x67, 0x0a, 0x1b, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x61, 0x0a, 0x18, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x16, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6c, 0x69, 0x64, 0x65,
	0x72, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x6c,
	0x69, 0x64, 0x65, 0x72, 0x53, 0x74, 0x65, 0x70, 0x22, 0x9f, 0x02, 0x0a, 0x18, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x3b, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x66, 0x6c, 0x61, 0x67,
	0x12, 0x3e, 0x0a, 0x11, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6d, 0x61, 0x78, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4d, 0x61, 0x78, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x35, 0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x69,
	0x6e, 0x66, 0x6f, 0x54, 0x65, 0x78, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xb6, 0x02, 0x0a, 0x0f, 0x43,
	0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x48,
	0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x40, 0x0a, 0x12, 0x6d, 0x61, 0x78, 0x5f,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x6d, 0x61, 0x78, 0x41, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x08, 0x74, 0x78,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x74, 0x78, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x12, 0x40, 0x0a, 0x12, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x10, 0x6d, 0x69, 0x6e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xa6, 0x02, 0x0a, 0x19, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x73, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x43, 0x0a, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x2f, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x6d,
	0x61, 0x78, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x65, 0x78, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x12,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x73, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x11, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x73, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x22, 0xf5, 0x01, 0x0a,
	0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x48, 0x0a, 0x16, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x49, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x55, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x74,
	0x78, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x07, 0x74, 0x78, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x2a, 0x52, 0x0a, 0x0d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x54, 0x4d, 0x5f, 0x55, 0x53, 0x41,
	0x47, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45,
	0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x02, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_card_card_limit_proto_rawDescOnce sync.Once
	file_api_frontend_card_card_limit_proto_rawDescData = file_api_frontend_card_card_limit_proto_rawDesc
)

func file_api_frontend_card_card_limit_proto_rawDescGZIP() []byte {
	file_api_frontend_card_card_limit_proto_rawDescOnce.Do(func() {
		file_api_frontend_card_card_limit_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_card_card_limit_proto_rawDescData)
	})
	return file_api_frontend_card_card_limit_proto_rawDescData
}

var file_api_frontend_card_card_limit_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_frontend_card_card_limit_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_frontend_card_card_limit_proto_goTypes = []interface{}{
	(LimitCategory)(0),                // 0: frontend.card.LimitCategory
	(*CardLimitTile)(nil),             // 1: frontend.card.CardLimitTile
	(*CardLimitSetting)(nil),          // 2: frontend.card.CardLimitSetting
	(*CategorisedLimit)(nil),          // 3: frontend.card.CategorisedLimit
	(*InternationalLimitDetail)(nil),  // 4: frontend.card.InternationalLimitDetail
	(*CardLimitDetail)(nil),           // 5: frontend.card.CardLimitDetail
	(*CategorisedGrossCardLimit)(nil), // 6: frontend.card.CategorisedGrossCardLimit
	(*UpdateCardLimitDetail)(nil),     // 7: frontend.card.UpdateCardLimitDetail
	(*typesv2.Money)(nil),             // 8: api.typesv2.Money
	(CardUsageLocationType)(0),        // 9: frontend.card.CardUsageLocationType
	(*common.Text)(nil),               // 10: api.typesv2.common.Text
	(CardTransactionType)(0),          // 11: frontend.card.CardTransactionType
}
var file_api_frontend_card_card_limit_proto_depIdxs = []int32{
	8,  // 0: frontend.card.CardLimitTile.cur_daily_limit:type_name -> api.typesv2.Money
	6,  // 1: frontend.card.CardLimitTile.categorised_gross_limit:type_name -> frontend.card.CategorisedGrossCardLimit
	2,  // 2: frontend.card.CardLimitTile.card_limit_settings:type_name -> frontend.card.CardLimitSetting
	9,  // 3: frontend.card.CardLimitSetting.loc_type:type_name -> frontend.card.CardUsageLocationType
	3,  // 4: frontend.card.CardLimitSetting.categorised_limit:type_name -> frontend.card.CategorisedLimit
	0,  // 5: frontend.card.CategorisedLimit.limit_category:type_name -> frontend.card.LimitCategory
	5,  // 6: frontend.card.CategorisedLimit.card_limit_details:type_name -> frontend.card.CardLimitDetail
	9,  // 7: frontend.card.CategorisedLimit.loc_type:type_name -> frontend.card.CardUsageLocationType
	4,  // 8: frontend.card.CategorisedLimit.international_limit_details:type_name -> frontend.card.InternationalLimitDetail
	4,  // 9: frontend.card.CategorisedLimit.default_selected_country:type_name -> frontend.card.InternationalLimitDetail
	10, // 10: frontend.card.InternationalLimitDetail.country_name:type_name -> api.typesv2.common.Text
	10, // 11: frontend.card.InternationalLimitDetail.flag:type_name -> api.typesv2.common.Text
	8,  // 12: frontend.card.InternationalLimitDetail.country_max_limit:type_name -> api.typesv2.Money
	10, // 13: frontend.card.InternationalLimitDetail.info_text:type_name -> api.typesv2.common.Text
	8,  // 14: frontend.card.CardLimitDetail.current_allowed_amount:type_name -> api.typesv2.Money
	8,  // 15: frontend.card.CardLimitDetail.max_allowed_amount:type_name -> api.typesv2.Money
	11, // 16: frontend.card.CardLimitDetail.txn_type:type_name -> frontend.card.CardTransactionType
	8,  // 17: frontend.card.CardLimitDetail.min_allowed_amount:type_name -> api.typesv2.Money
	0,  // 18: frontend.card.CategorisedGrossCardLimit.limit_category:type_name -> frontend.card.LimitCategory
	8,  // 19: frontend.card.CategorisedGrossCardLimit.max_limit:type_name -> api.typesv2.Money
	3,  // 20: frontend.card.CategorisedGrossCardLimit.categorised_limits:type_name -> frontend.card.CategorisedLimit
	8,  // 21: frontend.card.UpdateCardLimitDetail.updated_allowed_amount:type_name -> api.typesv2.Money
	9,  // 22: frontend.card.UpdateCardLimitDetail.loc_type:type_name -> frontend.card.CardUsageLocationType
	11, // 23: frontend.card.UpdateCardLimitDetail.txn_type:type_name -> frontend.card.CardTransactionType
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_api_frontend_card_card_limit_proto_init() }
func file_api_frontend_card_card_limit_proto_init() {
	if File_api_frontend_card_card_limit_proto != nil {
		return
	}
	file_api_frontend_card_card_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_card_card_limit_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardLimitTile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_card_card_limit_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardLimitSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_card_card_limit_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategorisedLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_card_card_limit_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternationalLimitDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_card_card_limit_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardLimitDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_card_card_limit_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategorisedGrossCardLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_card_card_limit_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCardLimitDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_card_card_limit_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_card_card_limit_proto_goTypes,
		DependencyIndexes: file_api_frontend_card_card_limit_proto_depIdxs,
		EnumInfos:         file_api_frontend_card_card_limit_proto_enumTypes,
		MessageInfos:      file_api_frontend_card_card_limit_proto_msgTypes,
	}.Build()
	File_api_frontend_card_card_limit_proto = out.File
	file_api_frontend_card_card_limit_proto_rawDesc = nil
	file_api_frontend_card_card_limit_proto_goTypes = nil
	file_api_frontend_card_card_limit_proto_depIdxs = nil
}
