package signup

// These methods are added so that attribute rate limiter can extract the device id from the request object

func (r *FetchAccessTokenRequest) GetDeviceId() string {
	if r != nil {
		return r.GetReq().GetAuth().GetDevice().GetDeviceId()
	}
	return ""
}

func (r *LoginWithOAuthRequest) GetDeviceId() string {
	if r != nil {
		return r.GetReq().GetAuth().GetDevice().GetDeviceId()
	}
	return ""
}

func (r *AddOAuthRequest) GetDeviceId() string {
	if r != nil {
		return r.GetReq().GetAuth().GetDevice().GetDeviceId()
	}
	return ""
}
