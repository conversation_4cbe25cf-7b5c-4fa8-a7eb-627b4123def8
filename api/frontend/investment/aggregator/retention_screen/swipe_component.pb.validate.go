// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/investment/aggregator/retention_screen/swipe_component.proto

package retention_screen

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SwipeComponent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SwipeComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SwipeComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SwipeComponentMultiError,
// or nil if none found.
func (m *SwipeComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *SwipeComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDisplayText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwipeComponentValidationError{
					field:  "DisplayText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwipeComponentValidationError{
					field:  "DisplayText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwipeComponentValidationError{
				field:  "DisplayText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSwipeButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwipeComponentValidationError{
					field:  "SwipeButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwipeComponentValidationError{
					field:  "SwipeButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSwipeButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwipeComponentValidationError{
				field:  "SwipeButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SwipeComponentMultiError(errors)
	}

	return nil
}

// SwipeComponentMultiError is an error wrapping multiple validation errors
// returned by SwipeComponent.ValidateAll() if the designated constraints
// aren't met.
type SwipeComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SwipeComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SwipeComponentMultiError) AllErrors() []error { return m }

// SwipeComponentValidationError is the validation error returned by
// SwipeComponent.Validate if the designated constraints aren't met.
type SwipeComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SwipeComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SwipeComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SwipeComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SwipeComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SwipeComponentValidationError) ErrorName() string { return "SwipeComponentValidationError" }

// Error satisfies the builtin error interface
func (e SwipeComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSwipeComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SwipeComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SwipeComponentValidationError{}
