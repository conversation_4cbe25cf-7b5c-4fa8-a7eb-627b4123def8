// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/insights/networth/internal/config.proto

package networth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on NetWorthDashboardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetWorthDashboardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetWorthDashboardConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetWorthDashboardConfigMultiError, or nil if none found.
func (m *NetWorthDashboardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *NetWorthDashboardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetWorthDashboardConfigValidationError{
						field:  fmt.Sprintf("Sections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetWorthDashboardConfigValidationError{
						field:  fmt.Sprintf("Sections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetWorthDashboardConfigValidationError{
					field:  fmt.Sprintf("Sections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AssetsTitle

	if len(errors) > 0 {
		return NetWorthDashboardConfigMultiError(errors)
	}

	return nil
}

// NetWorthDashboardConfigMultiError is an error wrapping multiple validation
// errors returned by NetWorthDashboardConfig.ValidateAll() if the designated
// constraints aren't met.
type NetWorthDashboardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetWorthDashboardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetWorthDashboardConfigMultiError) AllErrors() []error { return m }

// NetWorthDashboardConfigValidationError is the validation error returned by
// NetWorthDashboardConfig.Validate if the designated constraints aren't met.
type NetWorthDashboardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetWorthDashboardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetWorthDashboardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetWorthDashboardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetWorthDashboardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetWorthDashboardConfigValidationError) ErrorName() string {
	return "NetWorthDashboardConfigValidationError"
}

// Error satisfies the builtin error interface
func (e NetWorthDashboardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetWorthDashboardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetWorthDashboardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetWorthDashboardConfigValidationError{}

// Validate checks the field values on SectionDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SectionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SectionDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SectionDetailsMultiError,
// or nil if none found.
func (m *SectionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *SectionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Title

	// no validation rules for TooltipTitle

	// no validation rules for TooltipBody

	// no validation rules for SectionType

	for idx, item := range m.GetWidgets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionDetailsValidationError{
						field:  fmt.Sprintf("Widgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionDetailsValidationError{
						field:  fmt.Sprintf("Widgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionDetailsValidationError{
					field:  fmt.Sprintf("Widgets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AddMorePageTitle

	// no validation rules for WealthLandingSectionId

	if len(errors) > 0 {
		return SectionDetailsMultiError(errors)
	}

	return nil
}

// SectionDetailsMultiError is an error wrapping multiple validation errors
// returned by SectionDetails.ValidateAll() if the designated constraints
// aren't met.
type SectionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionDetailsMultiError) AllErrors() []error { return m }

// SectionDetailsValidationError is the validation error returned by
// SectionDetails.Validate if the designated constraints aren't met.
type SectionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionDetailsValidationError) ErrorName() string { return "SectionDetailsValidationError" }

// Error satisfies the builtin error interface
func (e SectionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSectionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionDetailsValidationError{}

// Validate checks the field values on WidgetDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WidgetDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WidgetDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WidgetDetailsMultiError, or
// nil if none found.
func (m *WidgetDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *WidgetDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Category

	// no validation rules for Title

	// no validation rules for IconUrl

	// no validation rules for GreyIconUrl

	// no validation rules for WidgetState

	// no validation rules for FeatureFlagName

	// no validation rules for WealthLandingWidgetId

	// no validation rules for WealthBuilderLandingIconUrl

	if len(errors) > 0 {
		return WidgetDetailsMultiError(errors)
	}

	return nil
}

// WidgetDetailsMultiError is an error wrapping multiple validation errors
// returned by WidgetDetails.ValidateAll() if the designated constraints
// aren't met.
type WidgetDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WidgetDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WidgetDetailsMultiError) AllErrors() []error { return m }

// WidgetDetailsValidationError is the validation error returned by
// WidgetDetails.Validate if the designated constraints aren't met.
type WidgetDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WidgetDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WidgetDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WidgetDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WidgetDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WidgetDetailsValidationError) ErrorName() string { return "WidgetDetailsValidationError" }

// Error satisfies the builtin error interface
func (e WidgetDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWidgetDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WidgetDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WidgetDetailsValidationError{}
