// Code generated by MockGen. DO NOT EDIT.
// Source: api/frontend/insights/accessinfo/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	accessinfo "github.com/epifi/gamma/api/frontend/insights/accessinfo"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAccessInfoClient is a mock of AccessInfoClient interface.
type MockAccessInfoClient struct {
	ctrl     *gomock.Controller
	recorder *MockAccessInfoClientMockRecorder
}

// MockAccessInfoClientMockRecorder is the mock recorder for MockAccessInfoClient.
type MockAccessInfoClientMockRecorder struct {
	mock *MockAccessInfoClient
}

// NewMockAccessInfoClient creates a new mock instance.
func NewMockAccessInfoClient(ctrl *gomock.Controller) *MockAccessInfoClient {
	mock := &MockAccessInfoClient{ctrl: ctrl}
	mock.recorder = &MockAccessInfoClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccessInfoClient) EXPECT() *MockAccessInfoClientMockRecorder {
	return m.recorder
}

// AddEmailAccessInfo mocks base method.
func (m *MockAccessInfoClient) AddEmailAccessInfo(ctx context.Context, in *accessinfo.AddEmailAccessInfoRequest, opts ...grpc.CallOption) (*accessinfo.AddEmailAccessInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddEmailAccessInfo", varargs...)
	ret0, _ := ret[0].(*accessinfo.AddEmailAccessInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddEmailAccessInfo indicates an expected call of AddEmailAccessInfo.
func (mr *MockAccessInfoClientMockRecorder) AddEmailAccessInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEmailAccessInfo", reflect.TypeOf((*MockAccessInfoClient)(nil).AddEmailAccessInfo), varargs...)
}

// GetAllLinkedEmailIds mocks base method.
func (m *MockAccessInfoClient) GetAllLinkedEmailIds(ctx context.Context, in *accessinfo.GetAllLinkedEmailIdsRequest, opts ...grpc.CallOption) (*accessinfo.GetAllLinkedEmailIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllLinkedEmailIds", varargs...)
	ret0, _ := ret[0].(*accessinfo.GetAllLinkedEmailIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLinkedEmailIds indicates an expected call of GetAllLinkedEmailIds.
func (mr *MockAccessInfoClientMockRecorder) GetAllLinkedEmailIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLinkedEmailIds", reflect.TypeOf((*MockAccessInfoClient)(nil).GetAllLinkedEmailIds), varargs...)
}

// GetAppInsightsAccessToken mocks base method.
func (m *MockAccessInfoClient) GetAppInsightsAccessToken(ctx context.Context, in *accessinfo.GetAppInsightsAccessTokenRequest, opts ...grpc.CallOption) (*accessinfo.GetAppInsightsAccessTokenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppInsightsAccessToken", varargs...)
	ret0, _ := ret[0].(*accessinfo.GetAppInsightsAccessTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppInsightsAccessToken indicates an expected call of GetAppInsightsAccessToken.
func (mr *MockAccessInfoClientMockRecorder) GetAppInsightsAccessToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppInsightsAccessToken", reflect.TypeOf((*MockAccessInfoClient)(nil).GetAppInsightsAccessToken), varargs...)
}

// ShowAddEmailAccountBanner mocks base method.
func (m *MockAccessInfoClient) ShowAddEmailAccountBanner(ctx context.Context, in *accessinfo.ShowAddEmailAccountBannerRequest, opts ...grpc.CallOption) (*accessinfo.ShowAddEmailAccountBannerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ShowAddEmailAccountBanner", varargs...)
	ret0, _ := ret[0].(*accessinfo.ShowAddEmailAccountBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowAddEmailAccountBanner indicates an expected call of ShowAddEmailAccountBanner.
func (mr *MockAccessInfoClientMockRecorder) ShowAddEmailAccountBanner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowAddEmailAccountBanner", reflect.TypeOf((*MockAccessInfoClient)(nil).ShowAddEmailAccountBanner), varargs...)
}

// MockAccessInfoServer is a mock of AccessInfoServer interface.
type MockAccessInfoServer struct {
	ctrl     *gomock.Controller
	recorder *MockAccessInfoServerMockRecorder
}

// MockAccessInfoServerMockRecorder is the mock recorder for MockAccessInfoServer.
type MockAccessInfoServerMockRecorder struct {
	mock *MockAccessInfoServer
}

// NewMockAccessInfoServer creates a new mock instance.
func NewMockAccessInfoServer(ctrl *gomock.Controller) *MockAccessInfoServer {
	mock := &MockAccessInfoServer{ctrl: ctrl}
	mock.recorder = &MockAccessInfoServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccessInfoServer) EXPECT() *MockAccessInfoServerMockRecorder {
	return m.recorder
}

// AddEmailAccessInfo mocks base method.
func (m *MockAccessInfoServer) AddEmailAccessInfo(arg0 context.Context, arg1 *accessinfo.AddEmailAccessInfoRequest) (*accessinfo.AddEmailAccessInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddEmailAccessInfo", arg0, arg1)
	ret0, _ := ret[0].(*accessinfo.AddEmailAccessInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddEmailAccessInfo indicates an expected call of AddEmailAccessInfo.
func (mr *MockAccessInfoServerMockRecorder) AddEmailAccessInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEmailAccessInfo", reflect.TypeOf((*MockAccessInfoServer)(nil).AddEmailAccessInfo), arg0, arg1)
}

// GetAllLinkedEmailIds mocks base method.
func (m *MockAccessInfoServer) GetAllLinkedEmailIds(arg0 context.Context, arg1 *accessinfo.GetAllLinkedEmailIdsRequest) (*accessinfo.GetAllLinkedEmailIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLinkedEmailIds", arg0, arg1)
	ret0, _ := ret[0].(*accessinfo.GetAllLinkedEmailIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLinkedEmailIds indicates an expected call of GetAllLinkedEmailIds.
func (mr *MockAccessInfoServerMockRecorder) GetAllLinkedEmailIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLinkedEmailIds", reflect.TypeOf((*MockAccessInfoServer)(nil).GetAllLinkedEmailIds), arg0, arg1)
}

// GetAppInsightsAccessToken mocks base method.
func (m *MockAccessInfoServer) GetAppInsightsAccessToken(arg0 context.Context, arg1 *accessinfo.GetAppInsightsAccessTokenRequest) (*accessinfo.GetAppInsightsAccessTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppInsightsAccessToken", arg0, arg1)
	ret0, _ := ret[0].(*accessinfo.GetAppInsightsAccessTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppInsightsAccessToken indicates an expected call of GetAppInsightsAccessToken.
func (mr *MockAccessInfoServerMockRecorder) GetAppInsightsAccessToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppInsightsAccessToken", reflect.TypeOf((*MockAccessInfoServer)(nil).GetAppInsightsAccessToken), arg0, arg1)
}

// ShowAddEmailAccountBanner mocks base method.
func (m *MockAccessInfoServer) ShowAddEmailAccountBanner(arg0 context.Context, arg1 *accessinfo.ShowAddEmailAccountBannerRequest) (*accessinfo.ShowAddEmailAccountBannerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShowAddEmailAccountBanner", arg0, arg1)
	ret0, _ := ret[0].(*accessinfo.ShowAddEmailAccountBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShowAddEmailAccountBanner indicates an expected call of ShowAddEmailAccountBanner.
func (mr *MockAccessInfoServerMockRecorder) ShowAddEmailAccountBanner(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShowAddEmailAccountBanner", reflect.TypeOf((*MockAccessInfoServer)(nil).ShowAddEmailAccountBanner), arg0, arg1)
}

// MockUnsafeAccessInfoServer is a mock of UnsafeAccessInfoServer interface.
type MockUnsafeAccessInfoServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAccessInfoServerMockRecorder
}

// MockUnsafeAccessInfoServerMockRecorder is the mock recorder for MockUnsafeAccessInfoServer.
type MockUnsafeAccessInfoServerMockRecorder struct {
	mock *MockUnsafeAccessInfoServer
}

// NewMockUnsafeAccessInfoServer creates a new mock instance.
func NewMockUnsafeAccessInfoServer(ctrl *gomock.Controller) *MockUnsafeAccessInfoServer {
	mock := &MockUnsafeAccessInfoServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAccessInfoServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAccessInfoServer) EXPECT() *MockUnsafeAccessInfoServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAccessInfoServer mocks base method.
func (m *MockUnsafeAccessInfoServer) mustEmbedUnimplementedAccessInfoServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAccessInfoServer")
}

// mustEmbedUnimplementedAccessInfoServer indicates an expected call of mustEmbedUnimplementedAccessInfoServer.
func (mr *MockUnsafeAccessInfoServerMockRecorder) mustEmbedUnimplementedAccessInfoServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAccessInfoServer", reflect.TypeOf((*MockUnsafeAccessInfoServer)(nil).mustEmbedUnimplementedAccessInfoServer))
}
