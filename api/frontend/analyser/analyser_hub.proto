syntax = "proto3";
package frontend.analyser;

option go_package = "github.com/epifi/gamma/api/frontend/analyser";
option java_package = "com.github.epifi.gamma.api.frontend.analyser";

import "api/frontend/analyser/internal/analyser_section_name.proto";
import "api/frontend/analyser/text.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "google/protobuf/wrappers.proto";

message AnalyserHub {
  // The Title of the page
  string title = 1;
  // Background color of the
  string background_color = 2;
  // List of analyser hub sections that need to be shown in the page.
  repeated AnalyserHubSection sections = 3;
  // Experiment Name will define the type of Analyser Hub. This is required for A/B Testing
  string experiment_name = 4;
  // banners that appear on top of analyser hub before the sections
  repeated AnalyserHubBanner top_banners = 5;
}

message AnalyserHubSection {
  // Title of the analyser section.
  TextElement title = 1;
  // analyser section name enum is a field which can be used to differentiate between various sections
  AnalyserHubSectionName section_name = 2;
  // optional background color, if the color is empty the parent element's background color should be used
  string background_color = 3;
  // List of analysers that will be included in the section
  repeated AnalyserHubWidget widgets = 4;
}

message AnalyserHubWidget {
  AnalyserHubWidgetType widget_type = 1;
  oneof widget {
    AnalyserWidgetCard card = 2;
  }
  // mandatory: internal identifier for the widget.
  // This identifier will be added in the analyser hub client events.
  string identifier = 3;
}

enum AnalyserHubWidgetType {
  ANALYSER_HUB_WIDGET_TYPE_UNSPECIFIED = 0;
  ANALYSER_HUB_WIDGET_TYPE_CARD = 1;
}

message AnalyserWidgetCard {
  // Title of the analyser.
  TextElement title = 1;
  // Body could give a brief description of the analyser or it could be an insight related to the corresponding analyser.
  TextElement body = 2;
  // The url for the icon that needs to be shown at the top of the title.
  string icon_url = 3;
  // optional background color, if the color is empty the parent element's background color should be used.
  string background_color = 4;
  // List of tags to show below the body eg. coming soon etc.
  repeated TextElement tags = 5;
  // deeplink to the next screen where user should be directed on tapping the analyser widget. May also contain the deeplink for the locked state, given the card is locked
  deeplink.Deeplink deeplink = 6;
  // CTA Details
  CTA cta = 7;
  // optional: header to be shown on top of widget
  WidgetHeader header = 8;
  // optional: footer to be shown on bottom of widget
  WidgetFooter footer = 9;
  // configuration of the locked state of the widget
  WidgetOverlayConfig overlay_config = 10;
}

enum WidgetHeaderType {
  WIDGET_HEADER_TYPE_UNSPECIFIED = 0;
  WIDGET_HEADER_TYPE_STATIC = 1;
}

message WidgetHeader {
  WidgetHeaderType type = 1;
  oneof header {
    StaticWidgetHeader static_header = 2;
  }
}

// StaticWidgetHeader represents a static header at the top of widget
// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=6333-16450&t=kJpqcGD2letzmERZ-4
enum StaticWidgetHeader {
  STATIC_WIDGET_HEADER_UNSPECIFIED = 0;
  STATIC_WIDGET_HEADER_NEW = 1;
}

// WidgetFooter contains underlying text to be shown under a widget
// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=6531-16655&t=kJpqcGD2letzmERZ-4
message WidgetFooter {
  api.typesv2.common.Text text = 1;
  string background_color = 2;
}

message CTA {
  CTAType type = 1;
  TextElement text = 2;
}

enum CTAType{
  CTA_TYPE_UNSPECIFIED = 0;
  CTA_TYPE_BOTTOM_LEFT_WITHOUT_TEXT = 1;
  CTA_TYPE_BOTTOM_LEFT_WITH_TEXT = 2;
  CTA_TYPE_BOTTOM_RIGHT_OF_BODY_WITHOUT_TEXT = 3;
}

enum ExperimentName {
  EXPERIMENT_NAME_UNSPECIFIED = 0;
  EXPERIMENT_NAME_CTA_BOTTOM_LEFT_WITHOUT_TEXT = 1;
  EXPERIMENT_NAME_CTA_BOTTOM_LEFT_WITH_TEXT = 2;
  EXPERIMENT_NAME_CTA_BOTTOM_RIGHT_OF_BODY_WITHOUT_TEXT = 3;
  EXPERIMENT_NAME_NO_CTA = 4;
}


message AnalyserHubBanner {
  AnalyserHubBannerType banner_type = 1;
  oneof banner {
    HighlightableHubBanner highlightable_banner = 2;
  }
}

enum AnalyserHubBannerType {
  ANALYSER_HUB_BANNER_TYPE_UNSPECIFIED = 0;
  ANALYSER_HUB_BANNER_TYPE_HIGHLIGHTABLE_BANNER = 1;
}


message HighlightableHubBanner {
  TextElement title = 1;
  api.typesv2.common.Image image_url = 2;
  analyser.Cta button_cta = 3;
  // flag if set to true indicates client to highlight the outline border of banner
  bool highlight_banner = 4;
  deeplink.Deeplink banner_deeplink = 5;
  // background color of banner
  string background_color = 6;
}

message WidgetOverlayConfig {
  // The hexcode of the overlay color
  string overlay_color = 1;
  // The opacity of the  overlay color should range between 0 and 1
  google.protobuf.DoubleValue opacity = 2;
  // The icon that appears at the top right corner of the card
  api.typesv2.common.VisualElement top_trailing_icon = 3;
}
