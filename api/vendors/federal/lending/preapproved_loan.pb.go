// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/federal/lending/preapproved_loan.proto

package lending

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetInstantLoanClosureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespUrl               string `protobuf:"bytes,1,opt,name=resp_url,json=respUrl,proto3" json:"resp_url,omitempty"`
	AccessId              string `protobuf:"bytes,2,opt,name=access_id,json=ServiceAccessId,proto3" json:"access_id,omitempty"`
	AccessCode            string `protobuf:"bytes,3,opt,name=access_code,json=ServiceAccessCode,proto3" json:"access_code,omitempty"`
	SenderCode            string `protobuf:"bytes,4,opt,name=sender_code,json=SenderCode,proto3" json:"sender_code,omitempty"`
	RequestId             string `protobuf:"bytes,5,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	ValueDate             string `protobuf:"bytes,6,opt,name=value_date,json=ValueDate,proto3" json:"value_date,omitempty"` // (DD-MM-YYYY)
	CustomerAccountNumber string `protobuf:"bytes,7,opt,name=customer_account_number,json=CustomerAccNum,proto3" json:"customer_account_number,omitempty"`
	LoanAccountNumber     string `protobuf:"bytes,8,opt,name=loan_account_number,json=LoanAccNumber,proto3" json:"loan_account_number,omitempty"`
	NetPayAmount          string `protobuf:"bytes,9,opt,name=net_pay_amount,json=NetPayAmount,proto3" json:"net_pay_amount,omitempty"`
	PayAmountId           string `protobuf:"bytes,10,opt,name=pay_amount_id,json=NetPayAmt_Id,proto3" json:"pay_amount_id,omitempty"`
	Remarks               string `protobuf:"bytes,11,opt,name=remarks,json=Remarks,proto3" json:"remarks,omitempty"`
}

func (x *GetInstantLoanClosureRequest) Reset() {
	*x = GetInstantLoanClosureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanClosureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanClosureRequest) ProtoMessage() {}

func (x *GetInstantLoanClosureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanClosureRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanClosureRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{0}
}

func (x *GetInstantLoanClosureRequest) GetRespUrl() string {
	if x != nil {
		return x.RespUrl
	}
	return ""
}

func (x *GetInstantLoanClosureRequest) GetAccessId() string {
	if x != nil {
		return x.AccessId
	}
	return ""
}

func (x *GetInstantLoanClosureRequest) GetAccessCode() string {
	if x != nil {
		return x.AccessCode
	}
	return ""
}

func (x *GetInstantLoanClosureRequest) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetInstantLoanClosureRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetInstantLoanClosureRequest) GetValueDate() string {
	if x != nil {
		return x.ValueDate
	}
	return ""
}

func (x *GetInstantLoanClosureRequest) GetCustomerAccountNumber() string {
	if x != nil {
		return x.CustomerAccountNumber
	}
	return ""
}

func (x *GetInstantLoanClosureRequest) GetLoanAccountNumber() string {
	if x != nil {
		return x.LoanAccountNumber
	}
	return ""
}

func (x *GetInstantLoanClosureRequest) GetNetPayAmount() string {
	if x != nil {
		return x.NetPayAmount
	}
	return ""
}

func (x *GetInstantLoanClosureRequest) GetPayAmountId() string {
	if x != nil {
		return x.PayAmountId
	}
	return ""
}

func (x *GetInstantLoanClosureRequest) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

type GetInstantLoanClosureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId         string `protobuf:"bytes,1,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	LoanAccountNumber string `protobuf:"bytes,2,opt,name=loan_account_number,json=LoanAccNumber,proto3" json:"loan_account_number,omitempty"`
	LoanCloseDate     string `protobuf:"bytes,3,opt,name=loan_close_date,json=LoanCloseDate,proto3" json:"loan_close_date,omitempty"`
	TransactionId     string `protobuf:"bytes,4,opt,name=transaction_id,json=TranRefId,proto3" json:"transaction_id,omitempty"` //Only in Successful Response
	ResponseAction    string `protobuf:"bytes,5,opt,name=response_action,json=ResponseAction,proto3" json:"response_action,omitempty"`
	ResponseCode      string `protobuf:"bytes,6,opt,name=response_code,json=ResponseCode,proto3" json:"response_code,omitempty"`
	ResponseReason    string `protobuf:"bytes,7,opt,name=response_reason,json=ResponseReason,proto3" json:"response_reason,omitempty"`
	NetPayAmount      string `protobuf:"bytes,8,opt,name=net_pay_amount,json=NetPayAmount,proto3" json:"net_pay_amount,omitempty"` //Only in Acknowledge Response
	NetPayId          string `protobuf:"bytes,9,opt,name=net_pay_id,json=NetPayAmt_Id,proto3" json:"net_pay_id,omitempty"`         //Only in some of the Failure responses
}

func (x *GetInstantLoanClosureResponse) Reset() {
	*x = GetInstantLoanClosureResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanClosureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanClosureResponse) ProtoMessage() {}

func (x *GetInstantLoanClosureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanClosureResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanClosureResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{1}
}

func (x *GetInstantLoanClosureResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetInstantLoanClosureResponse) GetLoanAccountNumber() string {
	if x != nil {
		return x.LoanAccountNumber
	}
	return ""
}

func (x *GetInstantLoanClosureResponse) GetLoanCloseDate() string {
	if x != nil {
		return x.LoanCloseDate
	}
	return ""
}

func (x *GetInstantLoanClosureResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *GetInstantLoanClosureResponse) GetResponseAction() string {
	if x != nil {
		return x.ResponseAction
	}
	return ""
}

func (x *GetInstantLoanClosureResponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *GetInstantLoanClosureResponse) GetResponseReason() string {
	if x != nil {
		return x.ResponseReason
	}
	return ""
}

func (x *GetInstantLoanClosureResponse) GetNetPayAmount() string {
	if x != nil {
		return x.NetPayAmount
	}
	return ""
}

func (x *GetInstantLoanClosureResponse) GetNetPayId() string {
	if x != nil {
		return x.NetPayId
	}
	return ""
}

type GetInstantLoanClosureEnquiryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Will be using fields and URL of Gold Loan Calculate Closure Amount API
	AccountNumber string `protobuf:"bytes,1,opt,name=account_number,json=AccountNumber,proto3" json:"account_number,omitempty"`
	Date          string `protobuf:"bytes,2,opt,name=date,json=Date,proto3" json:"date,omitempty"` // (DD-MM-YYYY)
}

func (x *GetInstantLoanClosureEnquiryRequest) Reset() {
	*x = GetInstantLoanClosureEnquiryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanClosureEnquiryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanClosureEnquiryRequest) ProtoMessage() {}

func (x *GetInstantLoanClosureEnquiryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanClosureEnquiryRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanClosureEnquiryRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{2}
}

func (x *GetInstantLoanClosureEnquiryRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *GetInstantLoanClosureEnquiryRequest) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

type GetInstantLoanClosureEnquiryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Will be using fields and URL of Gold Loan Calculate Closure Amount API
	AccountNumber string `protobuf:"bytes,1,opt,name=account_number,json=AccountNumber,proto3" json:"account_number,omitempty"`
	ClosureDate   string `protobuf:"bytes,2,opt,name=closure_date,json=ClosureDate,proto3" json:"closure_date,omitempty"` // (DD-MM-YYYY)
	ClosureAmount string `protobuf:"bytes,3,opt,name=closure_amount,json=ClosureAmount,proto3" json:"closure_amount,omitempty"`
	// will be populated for failure responses
	Error string `protobuf:"bytes,4,opt,name=error,json=Error,proto3" json:"error,omitempty"`
}

func (x *GetInstantLoanClosureEnquiryResponse) Reset() {
	*x = GetInstantLoanClosureEnquiryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanClosureEnquiryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanClosureEnquiryResponse) ProtoMessage() {}

func (x *GetInstantLoanClosureEnquiryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanClosureEnquiryResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanClosureEnquiryResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{3}
}

func (x *GetInstantLoanClosureEnquiryResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *GetInstantLoanClosureEnquiryResponse) GetClosureDate() string {
	if x != nil {
		return x.ClosureDate
	}
	return ""
}

func (x *GetInstantLoanClosureEnquiryResponse) GetClosureAmount() string {
	if x != nil {
		return x.ClosureAmount
	}
	return ""
}

func (x *GetInstantLoanClosureEnquiryResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type GetInstantLoanBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId        string `protobuf:"bytes,1,opt,name=user_id,json=userid,proto3" json:"user_id,omitempty"`
	Password      string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	SenderCode    string `protobuf:"bytes,3,opt,name=sender_code,json=sendercd,proto3" json:"sender_code,omitempty"`
	ReferenceId   string `protobuf:"bytes,4,opt,name=reference_id,json=ReferenceId,proto3" json:"reference_id,omitempty"`
	AccountNumber string `protobuf:"bytes,5,opt,name=account_number,json=Account_Number,proto3" json:"account_number,omitempty"`
}

func (x *GetInstantLoanBalanceRequest) Reset() {
	*x = GetInstantLoanBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanBalanceRequest) ProtoMessage() {}

func (x *GetInstantLoanBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanBalanceRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanBalanceRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{4}
}

func (x *GetInstantLoanBalanceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetInstantLoanBalanceRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *GetInstantLoanBalanceRequest) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetInstantLoanBalanceRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *GetInstantLoanBalanceRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

type GetInstantLoanBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will not be present for failure responses
	AccountNumber      string `protobuf:"bytes,1,opt,name=account_number,json=Account_Number,proto3" json:"account_number,omitempty"`
	AccountStatus      string `protobuf:"bytes,2,opt,name=account_status,json=Account_Status,proto3" json:"account_status,omitempty"`
	AccountType        string `protobuf:"bytes,3,opt,name=account_type,json=Account_Type,proto3" json:"account_type,omitempty"`
	AvailableBalance   string `protobuf:"bytes,4,opt,name=available_balance,json=AvailableBalance,proto3" json:"available_balance,omitempty"`
	BalanceCurrency    string `protobuf:"bytes,5,opt,name=balance_currency,json=BalCurrencycode,proto3" json:"balance_currency,omitempty"`
	CustomerName       string `protobuf:"bytes,6,opt,name=customer_name,json=CustomerName,proto3" json:"customer_name,omitempty"`
	FfdBalance         string `protobuf:"bytes,7,opt,name=ffd_balance,json=FFDBalance,proto3" json:"ffd_balance,omitempty"`
	FloatBalance       string `protobuf:"bytes,8,opt,name=float_balance,json=FloatBalance,proto3" json:"float_balance,omitempty"`
	LedgerBalance      string `protobuf:"bytes,9,opt,name=ledger_balance,json=LedgerBalance,proto3" json:"ledger_balance,omitempty"`
	Reason             string `protobuf:"bytes,10,opt,name=reason,json=Reason,proto3" json:"reason,omitempty"`
	ReferenceId        string `protobuf:"bytes,11,opt,name=reference_id,json=ReferenceId,proto3" json:"reference_id,omitempty"`
	Response           string `protobuf:"bytes,12,opt,name=response,json=Response,proto3" json:"response,omitempty"`
	UserDefinedBalance string `protobuf:"bytes,13,opt,name=user_defined_balance,json=UserDefinedBalance,proto3" json:"user_defined_balance,omitempty"`
	SenderCode         string `protobuf:"bytes,14,opt,name=sender_code,json=sendercd,proto3" json:"sender_code,omitempty"`
	// will not be present for success responses
	Error *GetInstantLoanBalanceResponse_ErrorResponse `protobuf:"bytes,15,opt,name=error,json=errorResponse,proto3" json:"error,omitempty"`
}

func (x *GetInstantLoanBalanceResponse) Reset() {
	*x = GetInstantLoanBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanBalanceResponse) ProtoMessage() {}

func (x *GetInstantLoanBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanBalanceResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanBalanceResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{5}
}

func (x *GetInstantLoanBalanceResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetAccountStatus() string {
	if x != nil {
		return x.AccountStatus
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetAvailableBalance() string {
	if x != nil {
		return x.AvailableBalance
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetBalanceCurrency() string {
	if x != nil {
		return x.BalanceCurrency
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetFfdBalance() string {
	if x != nil {
		return x.FfdBalance
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetFloatBalance() string {
	if x != nil {
		return x.FloatBalance
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetLedgerBalance() string {
	if x != nil {
		return x.LedgerBalance
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetUserDefinedBalance() string {
	if x != nil {
		return x.UserDefinedBalance
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse) GetError() *GetInstantLoanBalanceResponse_ErrorResponse {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetInstantLoanStatusEnquiryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanStatusEnquiryRequest *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest `protobuf:"bytes,1,opt,name=instant_loan_status_enquiry_request,json=InstantLoanAPIStatusEnqRequest,proto3" json:"instant_loan_status_enquiry_request,omitempty"`
}

func (x *GetInstantLoanStatusEnquiryRequest) Reset() {
	*x = GetInstantLoanStatusEnquiryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanStatusEnquiryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanStatusEnquiryRequest) ProtoMessage() {}

func (x *GetInstantLoanStatusEnquiryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanStatusEnquiryRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanStatusEnquiryRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{6}
}

func (x *GetInstantLoanStatusEnquiryRequest) GetInstantLoanStatusEnquiryRequest() *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest {
	if x != nil {
		return x.InstantLoanStatusEnquiryRequest
	}
	return nil
}

type GetInstantLoanStatusEnquiryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanStatusEnquiryResponse *GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse `protobuf:"bytes,1,opt,name=instant_loan_status_enquiry_response,json=InstantLoanAPIStatusEnqResponse,proto3" json:"instant_loan_status_enquiry_response,omitempty"`
}

func (x *GetInstantLoanStatusEnquiryResponse) Reset() {
	*x = GetInstantLoanStatusEnquiryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanStatusEnquiryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanStatusEnquiryResponse) ProtoMessage() {}

func (x *GetInstantLoanStatusEnquiryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanStatusEnquiryResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanStatusEnquiryResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{7}
}

func (x *GetInstantLoanStatusEnquiryResponse) GetInstantLoanStatusEnquiryResponse() *GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse {
	if x != nil {
		return x.InstantLoanStatusEnquiryResponse
	}
	return nil
}

type GetInstantLoanUnblockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanUnblockRequest *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest `protobuf:"bytes,1,opt,name=instant_loan_unblock_request,json=InstantLoanUnBlockRequest,proto3" json:"instant_loan_unblock_request,omitempty"`
}

func (x *GetInstantLoanUnblockRequest) Reset() {
	*x = GetInstantLoanUnblockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanUnblockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanUnblockRequest) ProtoMessage() {}

func (x *GetInstantLoanUnblockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanUnblockRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanUnblockRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{8}
}

func (x *GetInstantLoanUnblockRequest) GetInstantLoanUnblockRequest() *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest {
	if x != nil {
		return x.InstantLoanUnblockRequest
	}
	return nil
}

type GetInstantLoanUnblockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanUnblockResponse *GetInstantLoanUnblockResponse_InstantLoanUnblockResponse `protobuf:"bytes,1,opt,name=instant_loan_unblock_response,json=InstantLoanUnBlockResponse,proto3" json:"instant_loan_unblock_response,omitempty"`
}

func (x *GetInstantLoanUnblockResponse) Reset() {
	*x = GetInstantLoanUnblockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanUnblockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanUnblockResponse) ProtoMessage() {}

func (x *GetInstantLoanUnblockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanUnblockResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanUnblockResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{9}
}

func (x *GetInstantLoanUnblockResponse) GetInstantLoanUnblockResponse() *GetInstantLoanUnblockResponse_InstantLoanUnblockResponse {
	if x != nil {
		return x.InstantLoanUnblockResponse
	}
	return nil
}

type GetInstantLoanInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanInfoRequest *GetInstantLoanInfoRequest_InstantLoanInfoRequest `protobuf:"bytes,1,opt,name=instant_loan_info_request,json=InstantLoanInfoRequest,proto3" json:"instant_loan_info_request,omitempty"`
}

func (x *GetInstantLoanInfoRequest) Reset() {
	*x = GetInstantLoanInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanInfoRequest) ProtoMessage() {}

func (x *GetInstantLoanInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanInfoRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{10}
}

func (x *GetInstantLoanInfoRequest) GetInstantLoanInfoRequest() *GetInstantLoanInfoRequest_InstantLoanInfoRequest {
	if x != nil {
		return x.InstantLoanInfoRequest
	}
	return nil
}

type GetInstantLoanInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanInfoResponse *GetInstantLoanInfoResponse_InstantLoanInfoResponse `protobuf:"bytes,1,opt,name=instant_loan_info_response,json=InstantLoanInfoResponse,proto3" json:"instant_loan_info_response,omitempty"`
}

func (x *GetInstantLoanInfoResponse) Reset() {
	*x = GetInstantLoanInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanInfoResponse) ProtoMessage() {}

func (x *GetInstantLoanInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanInfoResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{11}
}

func (x *GetInstantLoanInfoResponse) GetInstantLoanInfoResponse() *GetInstantLoanInfoResponse_InstantLoanInfoResponse {
	if x != nil {
		return x.InstantLoanInfoResponse
	}
	return nil
}

type GetInstantLoanApplicationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanProcessRequest *GetInstantLoanApplicationRequest_InstantLoanProcessRequest `protobuf:"bytes,1,opt,name=instant_loan_process_request,json=InstantLoanProcessRequest,proto3" json:"instant_loan_process_request,omitempty"`
}

func (x *GetInstantLoanApplicationRequest) Reset() {
	*x = GetInstantLoanApplicationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanApplicationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanApplicationRequest) ProtoMessage() {}

func (x *GetInstantLoanApplicationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanApplicationRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanApplicationRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{12}
}

func (x *GetInstantLoanApplicationRequest) GetInstantLoanProcessRequest() *GetInstantLoanApplicationRequest_InstantLoanProcessRequest {
	if x != nil {
		return x.InstantLoanProcessRequest
	}
	return nil
}

type GetInstantLoanApplicationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanProcessResponse *GetInstantLoanApplicationResponse_InstantLoanProcessResponse `protobuf:"bytes,1,opt,name=instant_loan_process_response,json=InstantLoanProcessResponse,proto3" json:"instant_loan_process_response,omitempty"`
}

func (x *GetInstantLoanApplicationResponse) Reset() {
	*x = GetInstantLoanApplicationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanApplicationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanApplicationResponse) ProtoMessage() {}

func (x *GetInstantLoanApplicationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanApplicationResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanApplicationResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{13}
}

func (x *GetInstantLoanApplicationResponse) GetInstantLoanProcessResponse() *GetInstantLoanApplicationResponse_InstantLoanProcessResponse {
	if x != nil {
		return x.InstantLoanProcessResponse
	}
	return nil
}

type GetInstantLoanOTPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanOtpRequest *GetInstantLoanOTPRequest_InstantLoanOtpRequest `protobuf:"bytes,1,opt,name=instant_loan_otp_request,json=InstantLoanOTPRequest,proto3" json:"instant_loan_otp_request,omitempty"`
}

func (x *GetInstantLoanOTPRequest) Reset() {
	*x = GetInstantLoanOTPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOTPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOTPRequest) ProtoMessage() {}

func (x *GetInstantLoanOTPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOTPRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOTPRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{14}
}

func (x *GetInstantLoanOTPRequest) GetInstantLoanOtpRequest() *GetInstantLoanOTPRequest_InstantLoanOtpRequest {
	if x != nil {
		return x.InstantLoanOtpRequest
	}
	return nil
}

type GetInstantLoanOTPResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanOtpResponse *GetInstantLoanOTPResponse_InstantLoanOtpResponse `protobuf:"bytes,1,opt,name=instant_loan_otp_response,json=InstantLoanOTPResponse,proto3" json:"instant_loan_otp_response,omitempty"`
}

func (x *GetInstantLoanOTPResponse) Reset() {
	*x = GetInstantLoanOTPResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOTPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOTPResponse) ProtoMessage() {}

func (x *GetInstantLoanOTPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOTPResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOTPResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{15}
}

func (x *GetInstantLoanOTPResponse) GetInstantLoanOtpResponse() *GetInstantLoanOTPResponse_InstantLoanOtpResponse {
	if x != nil {
		return x.InstantLoanOtpResponse
	}
	return nil
}

type GetInstantLoanOffersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanOffersRequest *GetInstantLoanOffersRequest_InstantLoanOffersRequest `protobuf:"bytes,1,opt,name=instant_loan_offers_request,json=InstantLoanOffersRequest,proto3" json:"instant_loan_offers_request,omitempty"`
}

func (x *GetInstantLoanOffersRequest) Reset() {
	*x = GetInstantLoanOffersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOffersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOffersRequest) ProtoMessage() {}

func (x *GetInstantLoanOffersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOffersRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOffersRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{16}
}

func (x *GetInstantLoanOffersRequest) GetInstantLoanOffersRequest() *GetInstantLoanOffersRequest_InstantLoanOffersRequest {
	if x != nil {
		return x.InstantLoanOffersRequest
	}
	return nil
}

type GetInstantLoanOffersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstantLoanOffersResponse *GetInstantLoanOffersResponse_InstantLoanOffersResponse `protobuf:"bytes,1,opt,name=instant_loan_offers_response,json=InstantLoanOffersResponse,proto3" json:"instant_loan_offers_response,omitempty"`
}

func (x *GetInstantLoanOffersResponse) Reset() {
	*x = GetInstantLoanOffersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOffersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOffersResponse) ProtoMessage() {}

func (x *GetInstantLoanOffersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOffersResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOffersResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{17}
}

func (x *GetInstantLoanOffersResponse) GetInstantLoanOffersResponse() *GetInstantLoanOffersResponse_InstantLoanOffersResponse {
	if x != nil {
		return x.InstantLoanOffersResponse
	}
	return nil
}

type Header struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName string `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *Header) Reset() {
	*x = Header{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{18}
}

func (x *Header) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *Header) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type FetchLoanDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId  string `protobuf:"bytes,1,opt,name=customer_id,json=Customer_Id,proto3" json:"customer_id,omitempty"`
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=Mobile_No,proto3" json:"phone_number,omitempty"`
	SenderCd    string `protobuf:"bytes,3,opt,name=sender_cd,json=Sender_Cd,proto3" json:"sender_cd,omitempty"`
}

func (x *FetchLoanDetailsRequest) Reset() {
	*x = FetchLoanDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLoanDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLoanDetailsRequest) ProtoMessage() {}

func (x *FetchLoanDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLoanDetailsRequest.ProtoReflect.Descriptor instead.
func (*FetchLoanDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{19}
}

func (x *FetchLoanDetailsRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *FetchLoanDetailsRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *FetchLoanDetailsRequest) GetSenderCd() string {
	if x != nil {
		return x.SenderCd
	}
	return ""
}

type FetchLoanDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId    string                                 `protobuf:"bytes,1,opt,name=request_id,json=RequestID,proto3" json:"request_id,omitempty"`
	SenderId     string                                 `protobuf:"bytes,2,opt,name=sender_id,json=SenderId,proto3" json:"sender_id,omitempty"`
	SenderCode   string                                 `protobuf:"bytes,3,opt,name=sender_code,json=Sender_Code,proto3" json:"sender_code,omitempty"`
	Response     *FetchLoanDetailsResponse_Response     `protobuf:"bytes,4,opt,name=response,json=Response,proto3" json:"response,omitempty"`
	ErrorDetails *FetchLoanDetailsResponse_ErrorDetails `protobuf:"bytes,5,opt,name=error_details,json=ErrorDetails,proto3" json:"error_details,omitempty"`
}

func (x *FetchLoanDetailsResponse) Reset() {
	*x = FetchLoanDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLoanDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLoanDetailsResponse) ProtoMessage() {}

func (x *FetchLoanDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLoanDetailsResponse.ProtoReflect.Descriptor instead.
func (*FetchLoanDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{20}
}

func (x *FetchLoanDetailsResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *FetchLoanDetailsResponse) GetSenderId() string {
	if x != nil {
		return x.SenderId
	}
	return ""
}

func (x *FetchLoanDetailsResponse) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *FetchLoanDetailsResponse) GetResponse() *FetchLoanDetailsResponse_Response {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *FetchLoanDetailsResponse) GetErrorDetails() *FetchLoanDetailsResponse_ErrorDetails {
	if x != nil {
		return x.ErrorDetails
	}
	return nil
}

type FetchLoanDetailsErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId    string                                      `protobuf:"bytes,1,opt,name=request_id,json=RequestID,proto3" json:"request_id,omitempty"`
	SenderId     string                                      `protobuf:"bytes,2,opt,name=sender_id,json=SenderId,proto3" json:"sender_id,omitempty"`
	SenderCode   string                                      `protobuf:"bytes,3,opt,name=sender_code,json=Sender_Code,proto3" json:"sender_code,omitempty"`
	Response     string                                      `protobuf:"bytes,4,opt,name=response,json=Response,proto3" json:"response,omitempty"`
	ErrorDetails *FetchLoanDetailsErrorResponse_ErrorDetails `protobuf:"bytes,5,opt,name=error_details,json=ErrorDetails,proto3" json:"error_details,omitempty"`
}

func (x *FetchLoanDetailsErrorResponse) Reset() {
	*x = FetchLoanDetailsErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLoanDetailsErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLoanDetailsErrorResponse) ProtoMessage() {}

func (x *FetchLoanDetailsErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLoanDetailsErrorResponse.ProtoReflect.Descriptor instead.
func (*FetchLoanDetailsErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{21}
}

func (x *FetchLoanDetailsErrorResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *FetchLoanDetailsErrorResponse) GetSenderId() string {
	if x != nil {
		return x.SenderId
	}
	return ""
}

func (x *FetchLoanDetailsErrorResponse) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *FetchLoanDetailsErrorResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *FetchLoanDetailsErrorResponse) GetErrorDetails() *FetchLoanDetailsErrorResponse_ErrorDetails {
	if x != nil {
		return x.ErrorDetails
	}
	return nil
}

type GetRepaymentScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestUuid      string `protobuf:"bytes,1,opt,name=request_uuid,json=requestUUID,proto3" json:"request_uuid,omitempty"`
	ServiceRequestId string `protobuf:"bytes,2,opt,name=service_request_id,json=serviceRequestId,proto3" json:"service_request_id,omitempty"`
	MessageDatetime  string `protobuf:"bytes,3,opt,name=message_datetime,json=messageDatetime,proto3" json:"message_datetime,omitempty"`
	AccountNumber    string `protobuf:"bytes,4,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	SolId            string `protobuf:"bytes,5,opt,name=sol_id,json=solId,proto3" json:"sol_id,omitempty"`
}

func (x *GetRepaymentScheduleRequest) Reset() {
	*x = GetRepaymentScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleRequest) ProtoMessage() {}

func (x *GetRepaymentScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleRequest.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{22}
}

func (x *GetRepaymentScheduleRequest) GetRequestUuid() string {
	if x != nil {
		return x.RequestUuid
	}
	return ""
}

func (x *GetRepaymentScheduleRequest) GetServiceRequestId() string {
	if x != nil {
		return x.ServiceRequestId
	}
	return ""
}

func (x *GetRepaymentScheduleRequest) GetMessageDatetime() string {
	if x != nil {
		return x.MessageDatetime
	}
	return ""
}

func (x *GetRepaymentScheduleRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *GetRepaymentScheduleRequest) GetSolId() string {
	if x != nil {
		return x.SolId
	}
	return ""
}

type GetRepaymentScheduleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Body *GetRepaymentScheduleResponse_Body `protobuf:"bytes,1,opt,name=body,json=Body,proto3" json:"body,omitempty"`
}

func (x *GetRepaymentScheduleResponse) Reset() {
	*x = GetRepaymentScheduleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse) ProtoMessage() {}

func (x *GetRepaymentScheduleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23}
}

func (x *GetRepaymentScheduleResponse) GetBody() *GetRepaymentScheduleResponse_Body {
	if x != nil {
		return x.Body
	}
	return nil
}

type LoanDisbursementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PartnerName              string `protobuf:"bytes,1,opt,name=partner_name,json=PartnerName,proto3" json:"partner_name,omitempty"`
	PartnerCode              string `protobuf:"bytes,2,opt,name=partner_code,json=PartnerCode,proto3" json:"partner_code,omitempty"`
	SenderCode               string `protobuf:"bytes,3,opt,name=sender_code,json=SenderCode,proto3" json:"sender_code,omitempty"`
	RequestId                string `protobuf:"bytes,4,opt,name=request_id,json=Request_ID,proto3" json:"request_id,omitempty"`
	CustomerId               string `protobuf:"bytes,5,opt,name=customer_id,json=CustomerId,proto3" json:"customer_id,omitempty"`
	LoanAccountNumber        string `protobuf:"bytes,6,opt,name=loan_account_number,json=LoanAccountNumber,proto3" json:"loan_account_number,omitempty"`
	LoanAmount               int64  `protobuf:"varint,7,opt,name=loan_amount,json=LoanAmount,proto3" json:"loan_amount,omitempty"`
	OperativeAccountNumber   string `protobuf:"bytes,8,opt,name=operative_account_number,json=OperativeAccountNumber,proto3" json:"operative_account_number,omitempty"`
	BeneficiaryIfsc          string `protobuf:"bytes,9,opt,name=beneficiary_ifsc,json=BeneficiaryIFSC,proto3" json:"beneficiary_ifsc,omitempty"`
	BeneficiaryAccountNumber string `protobuf:"bytes,10,opt,name=beneficiary_account_number,json=BeneficiaryAccountNumber,proto3" json:"beneficiary_account_number,omitempty"`
	BeneficiaryAccountName   string `protobuf:"bytes,11,opt,name=beneficiary_account_name,json=BeneficiaryAccountName,proto3" json:"beneficiary_account_name,omitempty"`
	PennydropReqId           string `protobuf:"bytes,12,opt,name=pennydrop_req_id,json=PennydropReqId,proto3" json:"pennydrop_req_id,omitempty"`
	PennydropTranDate        string `protobuf:"bytes,13,opt,name=pennydrop_tran_date,json=PennydropTranDate,proto3" json:"pennydrop_tran_date,omitempty"`
	HunterReqId              string `protobuf:"bytes,14,opt,name=hunter_req_id,json=HunterReqId,proto3" json:"hunter_req_id,omitempty"`
	BreReqId                 string `protobuf:"bytes,15,opt,name=bre_req_id,json=BreReqId,proto3" json:"bre_req_id,omitempty"`
	PaymentMode              string `protobuf:"bytes,16,opt,name=payment_mode,json=PaymentMode,proto3" json:"payment_mode,omitempty"`
	ReservedField2           string `protobuf:"bytes,17,opt,name=reserved_field2,json=ReservedField2,proto3" json:"reserved_field2,omitempty"`
	ReservedField3           string `protobuf:"bytes,18,opt,name=reserved_field3,json=ReservedField3,proto3" json:"reserved_field3,omitempty"`
	ReservedField4           string `protobuf:"bytes,19,opt,name=reserved_field4,json=ReservedField4,proto3" json:"reserved_field4,omitempty"`
	ReservedField5           string `protobuf:"bytes,20,opt,name=reserved_field5,json=ReservedField5,proto3" json:"reserved_field5,omitempty"`
	ReservedField6           string `protobuf:"bytes,21,opt,name=reserved_field6,json=ReservedField6,proto3" json:"reserved_field6,omitempty"`
	ReservedField7           string `protobuf:"bytes,22,opt,name=reserved_field7,json=ReservedField7,proto3" json:"reserved_field7,omitempty"`
	ReservedField8           string `protobuf:"bytes,23,opt,name=reserved_field8,json=ReservedField8,proto3" json:"reserved_field8,omitempty"`
	ReservedField10          string `protobuf:"bytes,24,opt,name=reserved_field10,json=ReservedField10,proto3" json:"reserved_field10,omitempty"`
}

func (x *LoanDisbursementRequest) Reset() {
	*x = LoanDisbursementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanDisbursementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanDisbursementRequest) ProtoMessage() {}

func (x *LoanDisbursementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanDisbursementRequest.ProtoReflect.Descriptor instead.
func (*LoanDisbursementRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{24}
}

func (x *LoanDisbursementRequest) GetPartnerName() string {
	if x != nil {
		return x.PartnerName
	}
	return ""
}

func (x *LoanDisbursementRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *LoanDisbursementRequest) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *LoanDisbursementRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LoanDisbursementRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *LoanDisbursementRequest) GetLoanAccountNumber() string {
	if x != nil {
		return x.LoanAccountNumber
	}
	return ""
}

func (x *LoanDisbursementRequest) GetLoanAmount() int64 {
	if x != nil {
		return x.LoanAmount
	}
	return 0
}

func (x *LoanDisbursementRequest) GetOperativeAccountNumber() string {
	if x != nil {
		return x.OperativeAccountNumber
	}
	return ""
}

func (x *LoanDisbursementRequest) GetBeneficiaryIfsc() string {
	if x != nil {
		return x.BeneficiaryIfsc
	}
	return ""
}

func (x *LoanDisbursementRequest) GetBeneficiaryAccountNumber() string {
	if x != nil {
		return x.BeneficiaryAccountNumber
	}
	return ""
}

func (x *LoanDisbursementRequest) GetBeneficiaryAccountName() string {
	if x != nil {
		return x.BeneficiaryAccountName
	}
	return ""
}

func (x *LoanDisbursementRequest) GetPennydropReqId() string {
	if x != nil {
		return x.PennydropReqId
	}
	return ""
}

func (x *LoanDisbursementRequest) GetPennydropTranDate() string {
	if x != nil {
		return x.PennydropTranDate
	}
	return ""
}

func (x *LoanDisbursementRequest) GetHunterReqId() string {
	if x != nil {
		return x.HunterReqId
	}
	return ""
}

func (x *LoanDisbursementRequest) GetBreReqId() string {
	if x != nil {
		return x.BreReqId
	}
	return ""
}

func (x *LoanDisbursementRequest) GetPaymentMode() string {
	if x != nil {
		return x.PaymentMode
	}
	return ""
}

func (x *LoanDisbursementRequest) GetReservedField2() string {
	if x != nil {
		return x.ReservedField2
	}
	return ""
}

func (x *LoanDisbursementRequest) GetReservedField3() string {
	if x != nil {
		return x.ReservedField3
	}
	return ""
}

func (x *LoanDisbursementRequest) GetReservedField4() string {
	if x != nil {
		return x.ReservedField4
	}
	return ""
}

func (x *LoanDisbursementRequest) GetReservedField5() string {
	if x != nil {
		return x.ReservedField5
	}
	return ""
}

func (x *LoanDisbursementRequest) GetReservedField6() string {
	if x != nil {
		return x.ReservedField6
	}
	return ""
}

func (x *LoanDisbursementRequest) GetReservedField7() string {
	if x != nil {
		return x.ReservedField7
	}
	return ""
}

func (x *LoanDisbursementRequest) GetReservedField8() string {
	if x != nil {
		return x.ReservedField8
	}
	return ""
}

func (x *LoanDisbursementRequest) GetReservedField10() string {
	if x != nil {
		return x.ReservedField10
	}
	return ""
}

type LoanDisbursementReponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReferenceNumber string `protobuf:"bytes,1,opt,name=reference_number,proto3" json:"reference_number,omitempty"`
	ResponseCode    string `protobuf:"bytes,2,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	Message         string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *LoanDisbursementReponse) Reset() {
	*x = LoanDisbursementReponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanDisbursementReponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanDisbursementReponse) ProtoMessage() {}

func (x *LoanDisbursementReponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanDisbursementReponse.ProtoReflect.Descriptor instead.
func (*LoanDisbursementReponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{25}
}

func (x *LoanDisbursementReponse) GetReferenceNumber() string {
	if x != nil {
		return x.ReferenceNumber
	}
	return ""
}

func (x *LoanDisbursementReponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *LoanDisbursementReponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type LoanDisbursementEnquiryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PartnerName   string `protobuf:"bytes,1,opt,name=partner_name,json=PartnerName,proto3" json:"partner_name,omitempty"`
	PartnerCode   string `protobuf:"bytes,2,opt,name=partner_code,json=PartnerCode,proto3" json:"partner_code,omitempty"`
	SenderCode    string `protobuf:"bytes,3,opt,name=sender_code,json=SenderCode,proto3" json:"sender_code,omitempty"`
	RequestId     string `protobuf:"bytes,4,opt,name=request_id,json=Request_ID,proto3" json:"request_id,omitempty"`
	LoanRequestId string `protobuf:"bytes,5,opt,name=loan_request_id,json=LoanRequestId,proto3" json:"loan_request_id,omitempty"`
}

func (x *LoanDisbursementEnquiryRequest) Reset() {
	*x = LoanDisbursementEnquiryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanDisbursementEnquiryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanDisbursementEnquiryRequest) ProtoMessage() {}

func (x *LoanDisbursementEnquiryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanDisbursementEnquiryRequest.ProtoReflect.Descriptor instead.
func (*LoanDisbursementEnquiryRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{26}
}

func (x *LoanDisbursementEnquiryRequest) GetPartnerName() string {
	if x != nil {
		return x.PartnerName
	}
	return ""
}

func (x *LoanDisbursementEnquiryRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *LoanDisbursementEnquiryRequest) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *LoanDisbursementEnquiryRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LoanDisbursementEnquiryRequest) GetLoanRequestId() string {
	if x != nil {
		return x.LoanRequestId
	}
	return ""
}

type LoanDisbursementEnquiryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReferenceNumber string `protobuf:"bytes,1,opt,name=reference_number,proto3" json:"reference_number,omitempty"`
	ResponseCode    string `protobuf:"bytes,2,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	Message         string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *LoanDisbursementEnquiryResponse) Reset() {
	*x = LoanDisbursementEnquiryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanDisbursementEnquiryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanDisbursementEnquiryResponse) ProtoMessage() {}

func (x *LoanDisbursementEnquiryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanDisbursementEnquiryResponse.ProtoReflect.Descriptor instead.
func (*LoanDisbursementEnquiryResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{27}
}

func (x *LoanDisbursementEnquiryResponse) GetReferenceNumber() string {
	if x != nil {
		return x.ReferenceNumber
	}
	return ""
}

func (x *LoanDisbursementEnquiryResponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *LoanDisbursementEnquiryResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetInstantLoanBalanceResponse_ErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionTime string `protobuf:"bytes,1,opt,name=transaction_time,json=trantimeStamp,proto3" json:"transaction_time,omitempty"`
	StatusCode      string `protobuf:"bytes,2,opt,name=status_code,json=statuscode,proto3" json:"status_code,omitempty"`
	StatusReason    string `protobuf:"bytes,3,opt,name=status_reason,json=statusreason,proto3" json:"status_reason,omitempty"`
	CustomCode      string `protobuf:"bytes,4,opt,name=custom_code,json=customcode,proto3" json:"custom_code,omitempty"`
	CustomReason    string `protobuf:"bytes,5,opt,name=custom_reason,json=customreason,proto3" json:"custom_reason,omitempty"`
	TransactionId   string `protobuf:"bytes,6,opt,name=transaction_id,json=tranId,proto3" json:"transaction_id,omitempty"`
	Description     string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *GetInstantLoanBalanceResponse_ErrorResponse) Reset() {
	*x = GetInstantLoanBalanceResponse_ErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanBalanceResponse_ErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanBalanceResponse_ErrorResponse) ProtoMessage() {}

func (x *GetInstantLoanBalanceResponse_ErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanBalanceResponse_ErrorResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanBalanceResponse_ErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{5, 0}
}

func (x *GetInstantLoanBalanceResponse_ErrorResponse) GetTransactionTime() string {
	if x != nil {
		return x.TransactionTime
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse_ErrorResponse) GetStatusCode() string {
	if x != nil {
		return x.StatusCode
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse_ErrorResponse) GetStatusReason() string {
	if x != nil {
		return x.StatusReason
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse_ErrorResponse) GetCustomCode() string {
	if x != nil {
		return x.CustomCode
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse_ErrorResponse) GetCustomReason() string {
	if x != nil {
		return x.CustomReason
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse_ErrorResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *GetInstantLoanBalanceResponse_ErrorResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Deprecated: Marked as deprecated in api/vendors/federal/lending/preapproved_loan.proto.
	Body   *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body   `protobuf:"bytes,2,opt,name=body,json=body_deprecated,proto3" json:"body,omitempty"`
	BodyV2 *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2 `protobuf:"bytes,3,opt,name=body_v2,json=body,proto3" json:"body_v2,omitempty"`
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest) Reset() {
	*x = GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest) ProtoMessage() {}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{6, 0}
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

// Deprecated: Marked as deprecated in api/vendors/federal/lending/preapproved_loan.proto.
func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest) GetBody() *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest) GetBodyV2() *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2 {
	if x != nil {
		return x.BodyV2
	}
	return nil
}

type GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode    string `protobuf:"bytes,1,opt,name=sender_code,json=senderCode,proto3" json:"sender_code,omitempty"`
	ApplicationId string `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ApiType       string `protobuf:"bytes,3,opt,name=api_type,json=apiType,proto3" json:"api_type,omitempty"`
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body) Reset() {
	*x = GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body) ProtoMessage() {}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body.ProtoReflect.Descriptor instead.
func (*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{6, 0, 0}
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body) GetApiType() string {
	if x != nil {
		return x.ApiType
	}
	return ""
}

type GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode    string `protobuf:"bytes,1,opt,name=sender_code,json=senderCode,proto3" json:"sender_code,omitempty"`
	ApiType       string `protobuf:"bytes,2,opt,name=api_type,json=apiType,proto3" json:"api_type,omitempty"`
	ApplicationId string `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2) Reset() {
	*x = GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2) ProtoMessage() {}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2.ProtoReflect.Descriptor instead.
func (*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{6, 0, 1}
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2) GetApiType() string {
	if x != nil {
		return x.ApiType
	}
	return ""
}

func (x *GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseCode   string `protobuf:"bytes,1,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	ResponseReason string `protobuf:"bytes,2,opt,name=response_reason,json=responseReason,proto3" json:"response_reason,omitempty"`
	ApplicationId  string `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse) Reset() {
	*x = GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse) ProtoMessage() {}

func (x *GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{7, 0}
}

func (x *GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse) GetResponseReason() string {
	if x != nil {
		return x.ResponseReason
	}
	return ""
}

func (x *GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type GetInstantLoanUnblockRequest_InstantLoanUnblockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *Header                                                      `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Body   *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest) Reset() {
	*x = GetInstantLoanUnblockRequest_InstantLoanUnblockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanUnblockRequest_InstantLoanUnblockRequest) ProtoMessage() {}

func (x *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanUnblockRequest_InstantLoanUnblockRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanUnblockRequest_InstantLoanUnblockRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{8, 0}
}

func (x *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest) GetBody() *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body {
	if x != nil {
		return x.Body
	}
	return nil
}

type GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode    string `protobuf:"bytes,1,opt,name=sender_code,json=senderCode,proto3" json:"sender_code,omitempty"`
	ApplicationId string `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body) Reset() {
	*x = GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body) ProtoMessage() {}

func (x *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body.ProtoReflect.Descriptor instead.
func (*GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{8, 0, 0}
}

func (x *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type GetInstantLoanUnblockResponse_InstantLoanUnblockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseCode   string `protobuf:"bytes,1,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	ResponseReason string `protobuf:"bytes,2,opt,name=response_reason,json=responseReason,proto3" json:"response_reason,omitempty"`
	ApplicationId  string `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetInstantLoanUnblockResponse_InstantLoanUnblockResponse) Reset() {
	*x = GetInstantLoanUnblockResponse_InstantLoanUnblockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanUnblockResponse_InstantLoanUnblockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanUnblockResponse_InstantLoanUnblockResponse) ProtoMessage() {}

func (x *GetInstantLoanUnblockResponse_InstantLoanUnblockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanUnblockResponse_InstantLoanUnblockResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanUnblockResponse_InstantLoanUnblockResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{9, 0}
}

func (x *GetInstantLoanUnblockResponse_InstantLoanUnblockResponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *GetInstantLoanUnblockResponse_InstantLoanUnblockResponse) GetResponseReason() string {
	if x != nil {
		return x.ResponseReason
	}
	return ""
}

func (x *GetInstantLoanUnblockResponse_InstantLoanUnblockResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type GetInstantLoanInfoRequest_InstantLoanInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *Header                                                `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Body   *GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *GetInstantLoanInfoRequest_InstantLoanInfoRequest) Reset() {
	*x = GetInstantLoanInfoRequest_InstantLoanInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanInfoRequest_InstantLoanInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanInfoRequest_InstantLoanInfoRequest) ProtoMessage() {}

func (x *GetInstantLoanInfoRequest_InstantLoanInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanInfoRequest_InstantLoanInfoRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanInfoRequest_InstantLoanInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{10, 0}
}

func (x *GetInstantLoanInfoRequest_InstantLoanInfoRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetInstantLoanInfoRequest_InstantLoanInfoRequest) GetBody() *GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body {
	if x != nil {
		return x.Body
	}
	return nil
}

type GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode    string `protobuf:"bytes,1,opt,name=sender_code,json=senderCode,proto3" json:"sender_code,omitempty"`
	ApplicationId string `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body) Reset() {
	*x = GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body) ProtoMessage() {}

func (x *GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body.ProtoReflect.Descriptor instead.
func (*GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{10, 0, 0}
}

func (x *GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type GetInstantLoanInfoResponse_InstantLoanInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseCode   string `protobuf:"bytes,1,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	ResponseReason string `protobuf:"bytes,2,opt,name=response_reason,json=responseReason,proto3" json:"response_reason,omitempty"`
	ApplicationId  string `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	LoanState      string `protobuf:"bytes,4,opt,name=loan_state,json=loanState,proto3" json:"loan_state,omitempty"`
}

func (x *GetInstantLoanInfoResponse_InstantLoanInfoResponse) Reset() {
	*x = GetInstantLoanInfoResponse_InstantLoanInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanInfoResponse_InstantLoanInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanInfoResponse_InstantLoanInfoResponse) ProtoMessage() {}

func (x *GetInstantLoanInfoResponse_InstantLoanInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanInfoResponse_InstantLoanInfoResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanInfoResponse_InstantLoanInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{11, 0}
}

func (x *GetInstantLoanInfoResponse_InstantLoanInfoResponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *GetInstantLoanInfoResponse_InstantLoanInfoResponse) GetResponseReason() string {
	if x != nil {
		return x.ResponseReason
	}
	return ""
}

func (x *GetInstantLoanInfoResponse_InstantLoanInfoResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetInstantLoanInfoResponse_InstantLoanInfoResponse) GetLoanState() string {
	if x != nil {
		return x.LoanState
	}
	return ""
}

type GetInstantLoanApplicationRequest_InstantLoanProcessRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *Header                                                          `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Body   *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest) Reset() {
	*x = GetInstantLoanApplicationRequest_InstantLoanProcessRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanApplicationRequest_InstantLoanProcessRequest) ProtoMessage() {}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanApplicationRequest_InstantLoanProcessRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanApplicationRequest_InstantLoanProcessRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{12, 0}
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest) GetBody() *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body {
	if x != nil {
		return x.Body
	}
	return nil
}

type GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode              string  `protobuf:"bytes,1,opt,name=sender_code,json=senderCode,proto3" json:"sender_code,omitempty"`
	Otp                     string  `protobuf:"bytes,2,opt,name=otp,proto3" json:"otp,omitempty"`
	OfferId                 string  `protobuf:"bytes,3,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	PhoneNumber             string  `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	MaskedAccountNumber     string  `protobuf:"bytes,5,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
	ProcessingFee           float64 `protobuf:"fixed64,6,opt,name=processing_fee,json=processingFee,proto3" json:"processing_fee,omitempty"`
	CustomerIpDeviceDetails string  `protobuf:"bytes,7,opt,name=customer_ip_device_details,json=custIp_Devicedetails,proto3" json:"customer_ip_device_details,omitempty"`
	ApplicationId           string  `protobuf:"bytes,8,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	LoanAmount              float64 `protobuf:"fixed64,9,opt,name=loan_amount,json=loanAmount,proto3" json:"loan_amount,omitempty"`
	EmiAmount               float64 `protobuf:"fixed64,10,opt,name=emi_amount,json=emiAmount,proto3" json:"emi_amount,omitempty"`
	InterestRate            float64 `protobuf:"fixed64,11,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	TenureMonths            int32   `protobuf:"varint,12,opt,name=tenure_months,json=tenureMonths,proto3" json:"tenure_months,omitempty"`
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) Reset() {
	*x = GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) ProtoMessage() {}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body.ProtoReflect.Descriptor instead.
func (*GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{12, 0, 0}
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetProcessingFee() float64 {
	if x != nil {
		return x.ProcessingFee
	}
	return 0
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetCustomerIpDeviceDetails() string {
	if x != nil {
		return x.CustomerIpDeviceDetails
	}
	return ""
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetLoanAmount() float64 {
	if x != nil {
		return x.LoanAmount
	}
	return 0
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetEmiAmount() float64 {
	if x != nil {
		return x.EmiAmount
	}
	return 0
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body) GetTenureMonths() int32 {
	if x != nil {
		return x.TenureMonths
	}
	return 0
}

type GetInstantLoanApplicationResponse_InstantLoanProcessResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseCode   string `protobuf:"bytes,1,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	ResponseReason string `protobuf:"bytes,2,opt,name=response_reason,json=responseReason,proto3" json:"response_reason,omitempty"`
	ApplicationId  string `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetInstantLoanApplicationResponse_InstantLoanProcessResponse) Reset() {
	*x = GetInstantLoanApplicationResponse_InstantLoanProcessResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanApplicationResponse_InstantLoanProcessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanApplicationResponse_InstantLoanProcessResponse) ProtoMessage() {}

func (x *GetInstantLoanApplicationResponse_InstantLoanProcessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanApplicationResponse_InstantLoanProcessResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanApplicationResponse_InstantLoanProcessResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{13, 0}
}

func (x *GetInstantLoanApplicationResponse_InstantLoanProcessResponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *GetInstantLoanApplicationResponse_InstantLoanProcessResponse) GetResponseReason() string {
	if x != nil {
		return x.ResponseReason
	}
	return ""
}

func (x *GetInstantLoanApplicationResponse_InstantLoanProcessResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type GetInstantLoanOTPRequest_InstantLoanOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *Header                                              `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Body   *GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest) Reset() {
	*x = GetInstantLoanOTPRequest_InstantLoanOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOTPRequest_InstantLoanOtpRequest) ProtoMessage() {}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOTPRequest_InstantLoanOtpRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOTPRequest_InstantLoanOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{14, 0}
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest) GetBody() *GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body {
	if x != nil {
		return x.Body
	}
	return nil
}

type GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode    string `protobuf:"bytes,1,opt,name=sender_code,json=senderCode,proto3" json:"sender_code,omitempty"`
	OfferId       string `protobuf:"bytes,2,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	ApplicationId string `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	OtpToken      string `protobuf:"bytes,4,opt,name=otp_token,json=otpToken,proto3" json:"otp_token,omitempty"`
	PhoneNumber   string `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body) Reset() {
	*x = GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body) ProtoMessage() {}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{14, 0, 0}
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body) GetOtpToken() string {
	if x != nil {
		return x.OtpToken
	}
	return ""
}

func (x *GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type GetInstantLoanOTPResponse_InstantLoanOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseCode   string `protobuf:"bytes,1,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	ResponseReason string `protobuf:"bytes,2,opt,name=response_reason,json=responseReason,proto3" json:"response_reason,omitempty"`
	ApplicationId  string `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Otp            int64  `protobuf:"varint,4,opt,name=otp,json=OTP,proto3" json:"otp,omitempty"`
}

func (x *GetInstantLoanOTPResponse_InstantLoanOtpResponse) Reset() {
	*x = GetInstantLoanOTPResponse_InstantLoanOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOTPResponse_InstantLoanOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOTPResponse_InstantLoanOtpResponse) ProtoMessage() {}

func (x *GetInstantLoanOTPResponse_InstantLoanOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOTPResponse_InstantLoanOtpResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOTPResponse_InstantLoanOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{15, 0}
}

func (x *GetInstantLoanOTPResponse_InstantLoanOtpResponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *GetInstantLoanOTPResponse_InstantLoanOtpResponse) GetResponseReason() string {
	if x != nil {
		return x.ResponseReason
	}
	return ""
}

func (x *GetInstantLoanOTPResponse_InstantLoanOtpResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetInstantLoanOTPResponse_InstantLoanOtpResponse) GetOtp() int64 {
	if x != nil {
		return x.Otp
	}
	return 0
}

type GetInstantLoanOffersRequest_InstantLoanOffersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *Header                                                    `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Body   *GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest) Reset() {
	*x = GetInstantLoanOffersRequest_InstantLoanOffersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOffersRequest_InstantLoanOffersRequest) ProtoMessage() {}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOffersRequest_InstantLoanOffersRequest.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOffersRequest_InstantLoanOffersRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{16, 0}
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest) GetBody() *GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body {
	if x != nil {
		return x.Body
	}
	return nil
}

type GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode    string `protobuf:"bytes,1,opt,name=sender_code,json=senderCode,proto3" json:"sender_code,omitempty"`
	ReqType       string `protobuf:"bytes,2,opt,name=req_type,json=ReqType,proto3" json:"req_type,omitempty"`
	ApplicationId string `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Pan           string `protobuf:"bytes,4,opt,name=pan,json=PAN,proto3" json:"pan,omitempty"`
	PhoneNumber   string `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body) Reset() {
	*x = GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body) ProtoMessage() {}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{16, 0, 0}
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body) GetReqType() string {
	if x != nil {
		return x.ReqType
	}
	return ""
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type GetInstantLoanOffersResponse_InstantLoanOffersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseCode               string                                                                `protobuf:"bytes,1,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	ResponseReason             string                                                                `protobuf:"bytes,2,opt,name=response_reason,json=responseReason,proto3" json:"response_reason,omitempty"`
	LoanEligibleFlag           string                                                                `protobuf:"bytes,3,opt,name=loan_eligible_flag,json=loanEligibleFlag,proto3" json:"loan_eligible_flag,omitempty"`
	OfferId                    string                                                                `protobuf:"bytes,4,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	MaxAmount                  float64                                                               `protobuf:"fixed64,5,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	MaxAllowedEmi              float64                                                               `protobuf:"fixed64,6,opt,name=max_allowed_emi,json=maxAllowedEMI,proto3" json:"max_allowed_emi,omitempty"`
	MaxTenureMonths            float64                                                               `protobuf:"fixed64,7,opt,name=max_tenure_months,json=maxTenureMonths,proto3" json:"max_tenure_months,omitempty"`
	InterestRate               []*GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom `protobuf:"bytes,8,rep,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	ProcessingFeePercentage    []*GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom `protobuf:"bytes,9,rep,name=processing_fee_percentage,json=processingFeePercentage,proto3" json:"processing_fee_percentage,omitempty"`
	ServiceTaxGst              float64                                                               `protobuf:"fixed64,10,opt,name=service_tax_gst,json=serviceTax_GST,proto3" json:"service_tax_gst,omitempty"`
	ExpiryDateTimestampSeconds int64                                                                 `protobuf:"varint,11,opt,name=expiry_date_timestamp_seconds,json=expiryDateTimestampSeconds,proto3" json:"expiry_date_timestamp_seconds,omitempty"`
	ApplicationId              string                                                                `protobuf:"bytes,12,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) Reset() {
	*x = GetInstantLoanOffersResponse_InstantLoanOffersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOffersResponse_InstantLoanOffersResponse) ProtoMessage() {}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOffersResponse_InstantLoanOffersResponse.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOffersResponse_InstantLoanOffersResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{17, 0}
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetResponseReason() string {
	if x != nil {
		return x.ResponseReason
	}
	return ""
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetLoanEligibleFlag() string {
	if x != nil {
		return x.LoanEligibleFlag
	}
	return ""
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetMaxAmount() float64 {
	if x != nil {
		return x.MaxAmount
	}
	return 0
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetMaxAllowedEmi() float64 {
	if x != nil {
		return x.MaxAllowedEmi
	}
	return 0
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetMaxTenureMonths() float64 {
	if x != nil {
		return x.MaxTenureMonths
	}
	return 0
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetInterestRate() []*GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom {
	if x != nil {
		return x.InterestRate
	}
	return nil
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetProcessingFeePercentage() []*GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom {
	if x != nil {
		return x.ProcessingFeePercentage
	}
	return nil
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetServiceTaxGst() float64 {
	if x != nil {
		return x.ServiceTaxGst
	}
	return 0
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetExpiryDateTimestampSeconds() int64 {
	if x != nil {
		return x.ExpiryDateTimestampSeconds
	}
	return 0
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RangeFrom float64 `protobuf:"fixed64,1,opt,name=range_from,json=rangeFrom,proto3" json:"range_from,omitempty"`
	RangeTo   float64 `protobuf:"fixed64,2,opt,name=range_to,json=rangeTo,proto3" json:"range_to,omitempty"`
	Type      string  `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Value     float64 `protobuf:"fixed64,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom) Reset() {
	*x = GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom) ProtoMessage() {}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom.ProtoReflect.Descriptor instead.
func (*GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{17, 0, 0}
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom) GetRangeFrom() float64 {
	if x != nil {
		return x.RangeFrom
	}
	return 0
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom) GetRangeTo() float64 {
	if x != nil {
		return x.RangeTo
	}
	return 0
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type FetchLoanDetailsResponse_Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fetch *FetchLoanDetailsResponse_Response_Fetch `protobuf:"bytes,1,opt,name=fetch,json=FETCH,proto3" json:"fetch,omitempty"`
}

func (x *FetchLoanDetailsResponse_Response) Reset() {
	*x = FetchLoanDetailsResponse_Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLoanDetailsResponse_Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLoanDetailsResponse_Response) ProtoMessage() {}

func (x *FetchLoanDetailsResponse_Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLoanDetailsResponse_Response.ProtoReflect.Descriptor instead.
func (*FetchLoanDetailsResponse_Response) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{20, 0}
}

func (x *FetchLoanDetailsResponse_Response) GetFetch() *FetchLoanDetailsResponse_Response_Fetch {
	if x != nil {
		return x.Fetch
	}
	return nil
}

type FetchLoanDetailsResponse_ErrorDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   string `protobuf:"bytes,1,opt,name=code,json=Error_Code,proto3" json:"code,omitempty"`
	Reason string `protobuf:"bytes,2,opt,name=reason,json=Error_Reason,proto3" json:"reason,omitempty"`
}

func (x *FetchLoanDetailsResponse_ErrorDetails) Reset() {
	*x = FetchLoanDetailsResponse_ErrorDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLoanDetailsResponse_ErrorDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLoanDetailsResponse_ErrorDetails) ProtoMessage() {}

func (x *FetchLoanDetailsResponse_ErrorDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLoanDetailsResponse_ErrorDetails.ProtoReflect.Descriptor instead.
func (*FetchLoanDetailsResponse_ErrorDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{20, 1}
}

func (x *FetchLoanDetailsResponse_ErrorDetails) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *FetchLoanDetailsResponse_ErrorDetails) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type FetchLoanDetailsResponse_Response_Fetch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanDetails []*FetchLoanDetailsResponse_Response_Fetch_LoanDetails `protobuf:"bytes,1,rep,name=loan_details,json=loandetails,proto3" json:"loan_details,omitempty"`
}

func (x *FetchLoanDetailsResponse_Response_Fetch) Reset() {
	*x = FetchLoanDetailsResponse_Response_Fetch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLoanDetailsResponse_Response_Fetch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLoanDetailsResponse_Response_Fetch) ProtoMessage() {}

func (x *FetchLoanDetailsResponse_Response_Fetch) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLoanDetailsResponse_Response_Fetch.ProtoReflect.Descriptor instead.
func (*FetchLoanDetailsResponse_Response_Fetch) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{20, 0, 0}
}

func (x *FetchLoanDetailsResponse_Response_Fetch) GetLoanDetails() []*FetchLoanDetailsResponse_Response_Fetch_LoanDetails {
	if x != nil {
		return x.LoanDetails
	}
	return nil
}

type FetchLoanDetailsResponse_Response_Fetch_LoanDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountNumber     string `protobuf:"bytes,1,opt,name=account_number,json=ACCNTNUM,proto3" json:"account_number,omitempty"`
	PhoneNumber       string `protobuf:"bytes,2,opt,name=phone_number,json=MOBILE_NUM,proto3" json:"phone_number,omitempty"`
	LoanPeriod        string `protobuf:"bytes,3,opt,name=loan_period,json=LOAN_PERIOD,proto3" json:"loan_period,omitempty"`
	OpenDate          string `protobuf:"bytes,4,opt,name=open_date,json=OPEN_DATE,proto3" json:"open_date,omitempty"`
	LoanAmount        string `protobuf:"bytes,5,opt,name=loan_amount,json=LOAN_AMOUNT,proto3" json:"loan_amount,omitempty"`
	EndDate           string `protobuf:"bytes,6,opt,name=end_date,json=END_DATE,proto3" json:"end_date,omitempty"`
	InterestRate      string `protobuf:"bytes,7,opt,name=interest_rate,json=INT_RATE,proto3" json:"interest_rate,omitempty"`
	AccountType       string `protobuf:"bytes,8,opt,name=account_type,json=ACCNTTYPE,proto3" json:"account_type,omitempty"`
	SchemeDescription string `protobuf:"bytes,9,opt,name=scheme_description,json=SCHM_DESC,proto3" json:"scheme_description,omitempty"`
	NextPayDate       string `protobuf:"bytes,10,opt,name=next_pay_date,json=NEXT_PAY_DATE,proto3" json:"next_pay_date,omitempty"`
	NextPayAmount     string `protobuf:"bytes,11,opt,name=next_pay_amount,json=NEXT_PAY_AMNT,proto3" json:"next_pay_amount,omitempty"`
	DueAmount         string `protobuf:"bytes,12,opt,name=due_amount,json=DUE_AMNT,proto3" json:"due_amount,omitempty"`
	Status            string `protobuf:"bytes,13,opt,name=status,json=STATUS,proto3" json:"status,omitempty"`
	LedgerBalance     string `protobuf:"bytes,14,opt,name=ledger_balance,json=LEDGERBALANCE,proto3" json:"ledger_balance,omitempty"`
	FreeText1         string `protobuf:"bytes,15,opt,name=free_text1,json=FREETEXT1,proto3" json:"free_text1,omitempty"`
	FreeText2         string `protobuf:"bytes,16,opt,name=free_text2,json=FREETEXT2,proto3" json:"free_text2,omitempty"`
	FreeText3         string `protobuf:"bytes,17,opt,name=free_text3,json=FREETEXT3,proto3" json:"free_text3,omitempty"`
	FreeText4         string `protobuf:"bytes,18,opt,name=free_text4,json=FREETEXT4,proto3" json:"free_text4,omitempty"`
	FreeText5         string `protobuf:"bytes,19,opt,name=free_text5,json=FREETEXT5,proto3" json:"free_text5,omitempty"`
	FreeText6         string `protobuf:"bytes,20,opt,name=free_text6,json=FREETEXT6,proto3" json:"free_text6,omitempty"`
	FreeText7         string `protobuf:"bytes,21,opt,name=free_text7,json=FREETEXT7,proto3" json:"free_text7,omitempty"`
	FreeText8         string `protobuf:"bytes,22,opt,name=free_text8,json=FREETEXT8,proto3" json:"free_text8,omitempty"`
	FreeText9         string `protobuf:"bytes,23,opt,name=free_text9,json=FREETEXT9,proto3" json:"free_text9,omitempty"`
	FreeText10        string `protobuf:"bytes,24,opt,name=free_text10,json=FREETEXT10,proto3" json:"free_text10,omitempty"`
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) Reset() {
	*x = FetchLoanDetailsResponse_Response_Fetch_LoanDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLoanDetailsResponse_Response_Fetch_LoanDetails) ProtoMessage() {}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLoanDetailsResponse_Response_Fetch_LoanDetails.ProtoReflect.Descriptor instead.
func (*FetchLoanDetailsResponse_Response_Fetch_LoanDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{20, 0, 0, 0}
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetLoanPeriod() string {
	if x != nil {
		return x.LoanPeriod
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetOpenDate() string {
	if x != nil {
		return x.OpenDate
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetLoanAmount() string {
	if x != nil {
		return x.LoanAmount
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetInterestRate() string {
	if x != nil {
		return x.InterestRate
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetSchemeDescription() string {
	if x != nil {
		return x.SchemeDescription
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetNextPayDate() string {
	if x != nil {
		return x.NextPayDate
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetNextPayAmount() string {
	if x != nil {
		return x.NextPayAmount
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetDueAmount() string {
	if x != nil {
		return x.DueAmount
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetLedgerBalance() string {
	if x != nil {
		return x.LedgerBalance
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetFreeText1() string {
	if x != nil {
		return x.FreeText1
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetFreeText2() string {
	if x != nil {
		return x.FreeText2
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetFreeText3() string {
	if x != nil {
		return x.FreeText3
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetFreeText4() string {
	if x != nil {
		return x.FreeText4
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetFreeText5() string {
	if x != nil {
		return x.FreeText5
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetFreeText6() string {
	if x != nil {
		return x.FreeText6
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetFreeText7() string {
	if x != nil {
		return x.FreeText7
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetFreeText8() string {
	if x != nil {
		return x.FreeText8
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetFreeText9() string {
	if x != nil {
		return x.FreeText9
	}
	return ""
}

func (x *FetchLoanDetailsResponse_Response_Fetch_LoanDetails) GetFreeText10() string {
	if x != nil {
		return x.FreeText10
	}
	return ""
}

type FetchLoanDetailsErrorResponse_ErrorDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   string `protobuf:"bytes,1,opt,name=code,json=Error_Code,proto3" json:"code,omitempty"`
	Reason string `protobuf:"bytes,2,opt,name=reason,json=Error_Reason,proto3" json:"reason,omitempty"`
}

func (x *FetchLoanDetailsErrorResponse_ErrorDetails) Reset() {
	*x = FetchLoanDetailsErrorResponse_ErrorDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLoanDetailsErrorResponse_ErrorDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLoanDetailsErrorResponse_ErrorDetails) ProtoMessage() {}

func (x *FetchLoanDetailsErrorResponse_ErrorDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLoanDetailsErrorResponse_ErrorDetails.ProtoReflect.Descriptor instead.
func (*FetchLoanDetailsErrorResponse_ErrorDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{21, 0}
}

func (x *FetchLoanDetailsErrorResponse_ErrorDetails) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *FetchLoanDetailsErrorResponse_ErrorDetails) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type GetRepaymentScheduleResponse_Body struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AmortScheduleResponse *GetRepaymentScheduleResponse_Body_AmortScheduleResponse `protobuf:"bytes,1,opt,name=amort_schedule_response,json=findAmortScheduleResponse,proto3" json:"amort_schedule_response,omitempty"`
	ErrorResponse         *GetRepaymentScheduleResponse_Body_ErrorResponse         `protobuf:"bytes,2,opt,name=error_response,json=Error,proto3" json:"error_response,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Body) Reset() {
	*x = GetRepaymentScheduleResponse_Body{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Body) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Body) ProtoMessage() {}

func (x *GetRepaymentScheduleResponse_Body) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Body.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Body) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0}
}

func (x *GetRepaymentScheduleResponse_Body) GetAmortScheduleResponse() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse {
	if x != nil {
		return x.AmortScheduleResponse
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Body) GetErrorResponse() *GetRepaymentScheduleResponse_Body_ErrorResponse {
	if x != nil {
		return x.ErrorResponse
	}
	return nil
}

type GetRepaymentScheduleResponse_Body_AmortScheduleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AmortScheduleOutStruct *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct `protobuf:"bytes,1,opt,name=amort_schedule_out_struct,json=AmortShdlOutStruct,proto3" json:"amort_schedule_out_struct,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse) Reset() {
	*x = GetRepaymentScheduleResponse_Body_AmortScheduleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse) ProtoMessage() {}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Body_AmortScheduleResponse.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0, 0}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse) GetAmortScheduleOutStruct() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct {
	if x != nil {
		return x.AmortScheduleOutStruct
	}
	return nil
}

type GetRepaymentScheduleResponse_Body_ErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FiBusinessException *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException `protobuf:"bytes,1,opt,name=fi_business_exception,json=FIBusinessException,proto3" json:"fi_business_exception,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse) Reset() {
	*x = GetRepaymentScheduleResponse_Body_ErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Body_ErrorResponse) ProtoMessage() {}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Body_ErrorResponse.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Body_ErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0, 1}
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse) GetFiBusinessException() *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException {
	if x != nil {
		return x.FiBusinessException
	}
	return nil
}

type GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutLl []*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl `protobuf:"bytes,1,rep,name=out_ll,json=ooutLL,proto3" json:"out_ll,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct) Reset() {
	*x = GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct) ProtoMessage() {}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0, 0, 0}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct) GetOutLl() []*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl {
	if x != nil {
		return x.OutLl
	}
	return nil
}

type GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AmortScheduleDetails *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails `protobuf:"bytes,1,opt,name=amort_schedule_details,json=amortShdlDtls,proto3" json:"amort_schedule_details,omitempty"`
	Key                  *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key                  `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl) Reset() {
	*x = GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl) ProtoMessage() {
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0, 0, 0, 0}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl) GetAmortScheduleDetails() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails {
	if x != nil {
		return x.AmortScheduleDetails
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl) GetKey() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key {
	if x != nil {
		return x.Key
	}
	return nil
}

type GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CombInstlAmount            *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount `protobuf:"bytes,1,opt,name=comb_instl_amount,json=combInstlAmt,proto3" json:"comb_instl_amount,omitempty"`
	CummIntAmount              *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount `protobuf:"bytes,2,opt,name=cumm_int_amount,json=cummIntAmt,proto3" json:"cumm_int_amount,omitempty"`
	CummPrincipalAmount        *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount `protobuf:"bytes,3,opt,name=cumm_principal_amount,json=cummPrincAmt,proto3" json:"cumm_principal_amount,omitempty"`
	FlowDate                   string                                                                                                        `protobuf:"bytes,4,opt,name=flow_date,json=flowDate,proto3" json:"flow_date,omitempty"`
	InstallmentAmount          *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount `protobuf:"bytes,5,opt,name=installment_amount,json=instlAmt,proto3" json:"installment_amount,omitempty"`
	IntAmount                  *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount `protobuf:"bytes,6,opt,name=int_amount,json=intAmt,proto3" json:"int_amount,omitempty"`
	PrincipalAmount            *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount `protobuf:"bytes,7,opt,name=principal_amount,json=princAmt,proto3" json:"principal_amount,omitempty"`
	PrincipalOutstandingAmount *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount `protobuf:"bytes,8,opt,name=principal_outstanding_amount,json=princOutStanding,proto3" json:"principal_outstanding_amount,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) Reset() {
	*x = GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) ProtoMessage() {
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0, 0, 0, 0, 0}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) GetCombInstlAmount() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount {
	if x != nil {
		return x.CombInstlAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) GetCummIntAmount() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount {
	if x != nil {
		return x.CummIntAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) GetCummPrincipalAmount() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount {
	if x != nil {
		return x.CummPrincipalAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) GetFlowDate() string {
	if x != nil {
		return x.FlowDate
	}
	return ""
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) GetInstallmentAmount() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount {
	if x != nil {
		return x.InstallmentAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) GetIntAmount() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount {
	if x != nil {
		return x.IntAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) GetPrincipalAmount() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount {
	if x != nil {
		return x.PrincipalAmount
	}
	return nil
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails) GetPrincipalOutstandingAmount() *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount {
	if x != nil {
		return x.PrincipalOutstandingAmount
	}
	return nil
}

type GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SerialNumber string `protobuf:"bytes,1,opt,name=serial_number,json=serial_num,proto3" json:"serial_number,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key) Reset() {
	*x = GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key) ProtoMessage() {
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0, 0, 0, 0, 1}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

type GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrencyCode string `protobuf:"bytes,1,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	AmountValue  string `protobuf:"bytes,2,opt,name=amount_value,json=amountValue,proto3" json:"amount_value,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount) Reset() {
	*x = GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount) ProtoMessage() {
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0, 0, 0, 0, 0, 0}
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount) GetAmountValue() string {
	if x != nil {
		return x.AmountValue
	}
	return ""
}

type GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorDetails *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails `protobuf:"bytes,1,opt,name=error_details,json=ErrorDetail,proto3" json:"error_details,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException) Reset() {
	*x = GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException) ProtoMessage() {}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0, 1, 0}
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException) GetErrorDetails() *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails {
	if x != nil {
		return x.ErrorDetails
	}
	return nil
}

type GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode  string `protobuf:"bytes,1,opt,name=error_code,json=ErrorCode,proto3" json:"error_code,omitempty"`
	ErrorDessc string `protobuf:"bytes,2,opt,name=error_dessc,json=ErrorDesc,proto3" json:"error_dessc,omitempty"`
	ErrorSrc   string `protobuf:"bytes,3,opt,name=error_src,json=ErrorSource,proto3" json:"error_src,omitempty"`
	ErrorTyp   string `protobuf:"bytes,4,opt,name=error_typ,json=ErrorType,proto3" json:"error_typ,omitempty"`
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails) Reset() {
	*x = GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails) ProtoMessage() {
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails.ProtoReflect.Descriptor instead.
func (*GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP(), []int{23, 0, 1, 0, 0}
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails) GetErrorDessc() string {
	if x != nil {
		return x.ErrorDessc
	}
	return ""
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails) GetErrorSrc() string {
	if x != nil {
		return x.ErrorSrc
	}
	return ""
}

func (x *GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails) GetErrorTyp() string {
	if x != nil {
		return x.ErrorTyp
	}
	return ""
}

var File_api_vendors_federal_lending_preapproved_loan_proto protoreflect.FileDescriptor

var file_api_vendors_federal_lending_preapproved_loan_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22, 0xa6, 0x03,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x65, 0x73, 0x70, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x09, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x17, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41,
	0x63, 0x63, 0x4e, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x65, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4e, 0x65, 0x74, 0x50, 0x61,
	0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x61, 0x79, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x4e, 0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x6d, 0x74, 0x5f, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x52,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x22, 0xf4, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x63, 0x6c, 0x6f, 0x73,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4c, 0x6f,
	0x61, 0x6e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x54, 0x72, 0x61, 0x6e, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x27,
	0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x65, 0x74, 0x5f, 0x70, 0x61, 0x79,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4e,
	0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0a, 0x6e,
	0x65, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x4e, 0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x6d, 0x74, 0x5f, 0x49, 0x64, 0x22, 0x60, 0x0a,
	0x23, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x43,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x61, 0x74, 0x65, 0x22,
	0xad, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22,
	0xbd, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x63, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22,
	0x92, 0x07, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x42, 0x61,
	0x6c, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x66, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x46, 0x46, 0x44, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x62, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x46, 0x6c, 0x6f, 0x61,
	0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x72, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x63, 0x64, 0x12, 0x62, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0d, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0x86, 0x02, 0x0a, 0x0d,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x27, 0x0a,
	0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x72, 0x61, 0x6e,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x85, 0x06, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x71,
	0x75, 0x69, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0xa8, 0x01, 0x0a, 0x23,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x65, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x1e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x41, 0x50, 0x49, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x71, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0xb3, 0x04, 0x0a, 0x1f, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x71, 0x75,
	0x69, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x83, 0x01, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x60, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64,
	0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x42, 0x6f, 0x64, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x64,
	0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x79, 0x0a, 0x07, 0x62, 0x6f, 0x64,
	0x79, 0x5f, 0x76, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x62, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x56, 0x32, 0x52, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x1a, 0x69, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x69, 0x54, 0x79, 0x70, 0x65, 0x1a,
	0x6b, 0x0a, 0x06, 0x42, 0x6f, 0x64, 0x79, 0x56, 0x32, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70,
	0x69, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70,
	0x69, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xee, 0x02, 0x0a,
	0x23, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xac, 0x01, 0x0a, 0x24, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x65, 0x6e, 0x71,
	0x75, 0x69, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x5d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x1f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x41,
	0x50, 0x49, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x71, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x1a, 0x97, 0x01, 0x0a, 0x20, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xc2, 0x03,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x90,
	0x01, 0x0a, 0x1c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x75, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x6e,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x19, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x55, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x8e, 0x02, 0x0a, 0x19, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x37, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x68, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55,
	0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x52, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x1a, 0x4e, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x22, 0xca, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x1d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x75, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x1a, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x6e, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0x91, 0x01, 0x0a, 0x1a,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x6e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22,
	0xaa, 0x03, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x84, 0x01,
	0x0a, 0x19, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x49, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x16, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x85, 0x02, 0x0a, 0x16, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x37, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x62, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0x4e, 0x0a, 0x04,
	0x42, 0x6f, 0x64, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xd7, 0x02, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x1a,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x4b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72,
	0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x17, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0xad, 0x01, 0x0a, 0x17, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x61,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0xbe, 0x06, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x1c,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x53, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64,
	0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x19, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x82, 0x05, 0x0a, 0x19, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x37, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72,
	0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x6c, 0x0a, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x58, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x64,
	0x79, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a, 0xbd, 0x03, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6f, 0x74, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x32, 0x0a, 0x15, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x12, 0x38, 0x0a, 0x1a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x5f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x63, 0x75, 0x73, 0x74, 0x49, 0x70, 0x5f, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x65, 0x6d, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x09, 0x65, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x6d, 0x6f, 0x6e,
	0x74, 0x68, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x65, 0x6e, 0x75, 0x72,
	0x65, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x22, 0xd2, 0x02, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x98, 0x01,
	0x0a, 0x1d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x1a, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0x91, 0x01, 0x0a, 0x1a, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xfe, 0x03, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x80, 0x01, 0x0a, 0x18, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x74, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x15, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0xde, 0x02, 0x0a,
	0x15, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x60, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x52, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x1a, 0xa9, 0x01, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x6f, 0x74, 0x70, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6f, 0x74, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xc4, 0x02,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x19,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x74, 0x70,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x49, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x16, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x1a, 0x9f, 0x01, 0x0a, 0x16, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x4f, 0x54, 0x50, 0x22, 0x8b, 0x04, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x8c, 0x01, 0x0a, 0x1b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x18, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0xdc, 0x02, 0x0a, 0x18, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x37, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72,
	0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x66, 0x0a, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x52, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x1a, 0x9e, 0x01, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72,
	0x65, 0x71, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x52,
	0x65, 0x71, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x70, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x50, 0x41, 0x4e, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x22, 0xfc, 0x07, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x1c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x19, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0xc8, 0x06, 0x0a, 0x19, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x6c, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x6c, 0x6f, 0x61, 0x6e, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x6d, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x61,
	0x78, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x65, 0x6d, 0x69, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x45,
	0x4d, 0x49, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x6d,
	0x61, 0x78, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x80,
	0x01, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x6f, 0x46,
	0x72, 0x6f, 0x6d, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74,
	0x65, 0x12, 0x97, 0x01, 0x0a, 0x19, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x65, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x6f, 0x46, 0x72,
	0x6f, 0x6d, 0x52, 0x17, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65,
	0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x67, 0x73, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x61, 0x78,
	0x5f, 0x47, 0x53, 0x54, 0x12, 0x41, 0x0a, 0x1d, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1a, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x1a, 0x71,
	0x0a, 0x0b, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x6f, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x19, 0x0a, 0x08,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x41, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x22, 0x7a, 0x0a, 0x17, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x6f, 0x61,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f,
	0x4e, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x43, 0x64,
	0x22, 0xfb, 0x0a, 0x0a, 0x18, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x44, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x08, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x6f, 0x61,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xfb, 0x07, 0x0a, 0x08, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x05, 0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66,
	0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x05, 0x46, 0x45, 0x54, 0x43, 0x48, 0x1a, 0x96, 0x07,
	0x0a, 0x05, 0x46, 0x65, 0x74, 0x63, 0x68, 0x12, 0x6f, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x6f, 0x61,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x6c, 0x6f, 0x61,
	0x6e, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x9b, 0x06, 0x0a, 0x0b, 0x4c, 0x6f, 0x61,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x20, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x41, 0x43, 0x43, 0x4e, 0x54, 0x4e, 0x55, 0x4d, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x12, 0x20, 0x0a, 0x0b,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x45, 0x52, 0x49, 0x4f, 0x44, 0x12, 0x1c,
	0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x12, 0x20, 0x0a, 0x0b,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x12, 0x1a,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x45, 0x4e, 0x44, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x12, 0x1f, 0x0a, 0x0d, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x12, 0x1f, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x41, 0x43, 0x43, 0x4e, 0x54, 0x54, 0x59, 0x50, 0x45, 0x12, 0x25, 0x0a, 0x12,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x43, 0x48, 0x4d, 0x5f, 0x44,
	0x45, 0x53, 0x43, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4e, 0x45, 0x58, 0x54,
	0x5f, 0x50, 0x41, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x41, 0x4d, 0x4e,
	0x54, 0x12, 0x1c, 0x0a, 0x0a, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x55, 0x45, 0x5f, 0x41, 0x4d, 0x4e, 0x54, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x72, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x52, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x12, 0x1d,
	0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x31, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x46, 0x52, 0x45, 0x45, 0x54, 0x45, 0x58, 0x54, 0x31, 0x12, 0x1d, 0x0a,
	0x0a, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x32, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x46, 0x52, 0x45, 0x45, 0x54, 0x45, 0x58, 0x54, 0x32, 0x12, 0x1d, 0x0a, 0x0a,
	0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x33, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x46, 0x52, 0x45, 0x45, 0x54, 0x45, 0x58, 0x54, 0x33, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x34, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x46, 0x52, 0x45, 0x45, 0x54, 0x45, 0x58, 0x54, 0x34, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72,
	0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x35, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x46, 0x52, 0x45, 0x45, 0x54, 0x45, 0x58, 0x54, 0x35, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x65,
	0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x36, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x46,
	0x52, 0x45, 0x45, 0x54, 0x45, 0x58, 0x54, 0x36, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x37, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x46, 0x52,
	0x45, 0x45, 0x54, 0x45, 0x58, 0x54, 0x37, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x38, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x46, 0x52, 0x45,
	0x45, 0x54, 0x45, 0x58, 0x54, 0x38, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x39, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x46, 0x52, 0x45, 0x45,
	0x54, 0x45, 0x58, 0x54, 0x39, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x31, 0x30, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x46, 0x52, 0x45, 0x45,
	0x54, 0x45, 0x58, 0x54, 0x31, 0x30, 0x1a, 0x46, 0x0a, 0x0c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1c, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xcb,
	0x02, 0x0a, 0x1d, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x44, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x0d, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x1a, 0x46, 0x0a, 0x0c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1c,
	0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xd7, 0x01, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x55, 0x49, 0x44, 0x12,
	0x2c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x29, 0x0a,
	0x10, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x15, 0x0a, 0x06, 0x73, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x6f, 0x6c, 0x49, 0x64, 0x22, 0xe1, 0x16, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64,
	0x79, 0x52, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x1a, 0xf0, 0x15, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79,
	0x12, 0x8c, 0x01, 0x0a, 0x17, 0x61, 0x6d, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x50, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64,
	0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x41,
	0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x19, 0x66, 0x69, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x67, 0x0a, 0x0e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42,
	0x6f, 0x64, 0x79, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0x94, 0x10, 0x0a, 0x15, 0x41, 0x6d, 0x6f,
	0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x19, 0x61, 0x6d, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x63, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f,
	0x64, 0x79, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x68,
	0x64, 0x6c, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x12, 0x41, 0x6d, 0x6f,
	0x72, 0x74, 0x53, 0x68, 0x64, 0x6c, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x1a,
	0xdd, 0x0e, 0x0a, 0x12, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x68, 0x64, 0x6c, 0x4f, 0x75, 0x74,
	0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x12, 0x81, 0x01, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x5f, 0x6c,
	0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42,
	0x6f, 0x64, 0x79, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53,
	0x68, 0x64, 0x6c, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x4f, 0x75, 0x74,
	0x4c, 0x6c, 0x52, 0x06, 0x6f, 0x6f, 0x75, 0x74, 0x4c, 0x4c, 0x1a, 0xc2, 0x0d, 0x0a, 0x05, 0x4f,
	0x75, 0x74, 0x4c, 0x6c, 0x12, 0xad, 0x01, 0x0a, 0x16, 0x61, 0x6d, 0x6f, 0x72, 0x74, 0x5f, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x7e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64,
	0x79, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x68, 0x64,
	0x6c, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x4f, 0x75, 0x74, 0x4c, 0x6c,
	0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x61, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x68, 0x64, 0x6c,
	0x44, 0x74, 0x6c, 0x73, 0x12, 0x7f, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x6d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x41, 0x6d,
	0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x68, 0x64, 0x6c, 0x4f, 0x75, 0x74,
	0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x4f, 0x75, 0x74, 0x4c, 0x6c, 0x2e, 0x4b, 0x65, 0x79,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x1a, 0xdd, 0x0a, 0x0a, 0x14, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0xaf,
	0x01, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x62, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x6c, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x85, 0x01, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d,
	0x6f, 0x72, 0x74, 0x53, 0x68, 0x64, 0x6c, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x2e, 0x4f, 0x75, 0x74, 0x4c, 0x6c, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x62, 0x49, 0x6e, 0x73, 0x74, 0x6c, 0x41, 0x6d, 0x74,
	0x12, 0xab, 0x01, 0x0a, 0x0f, 0x63, 0x75, 0x6d, 0x6d, 0x5f, 0x69, 0x6e, 0x74, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x85, 0x01, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d,
	0x6f, 0x72, 0x74, 0x53, 0x68, 0x64, 0x6c, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x2e, 0x4f, 0x75, 0x74, 0x4c, 0x6c, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x0a, 0x63, 0x75, 0x6d, 0x6d, 0x49, 0x6e, 0x74, 0x41, 0x6d, 0x74, 0x12, 0xb3,
	0x01, 0x0a, 0x15, 0x63, 0x75, 0x6d, 0x6d, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61,
	0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x85,
	0x01, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x41, 0x6d, 0x6f, 0x72,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x68, 0x64, 0x6c, 0x4f, 0x75, 0x74, 0x53, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x4f, 0x75, 0x74, 0x4c, 0x6c, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x6d, 0x6d, 0x50, 0x72, 0x69, 0x6e,
	0x63, 0x41, 0x6d, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x61, 0x74,
	0x65, 0x12, 0xac, 0x01, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x85,
	0x01, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x41, 0x6d, 0x6f, 0x72,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x68, 0x64, 0x6c, 0x4f, 0x75, 0x74, 0x53, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x4f, 0x75, 0x74, 0x4c, 0x6c, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x6c, 0x41, 0x6d, 0x74,
	0x12, 0xa2, 0x01, 0x0a, 0x0a, 0x69, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x85, 0x01, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f,
	0x64, 0x79, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x68,
	0x64, 0x6c, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x4f, 0x75, 0x74, 0x4c,
	0x6c, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x06, 0x69,
	0x6e, 0x74, 0x41, 0x6d, 0x74, 0x12, 0xaa, 0x01, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69,
	0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x85, 0x01, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x41, 0x6d,
	0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x68, 0x64, 0x6c, 0x4f, 0x75, 0x74,
	0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x4f, 0x75, 0x74, 0x4c, 0x6c, 0x2e, 0x41, 0x6d, 0x6f,
	0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x41,
	0x6d, 0x74, 0x12, 0xbe, 0x01, 0x0a, 0x1c, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c,
	0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x85, 0x01, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d, 0x6f,
	0x72, 0x74, 0x53, 0x68, 0x64, 0x6c, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x4f, 0x75, 0x74, 0x4c, 0x6c, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x10, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x1a, 0x50, 0x0a, 0x06, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x28, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0d,
	0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x1a,
	0xd8, 0x03, 0x0a, 0x0d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x90, 0x01, 0x0a, 0x15, 0x66, 0x69, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x5c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x69, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x13, 0x46, 0x49, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x45, 0x78, 0x63, 0x65, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xb3, 0x02, 0x0a, 0x13, 0x46, 0x69, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x8d, 0x01, 0x0a,
	0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66,
	0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x64, 0x79,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46,
	0x69, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x1a, 0x8b, 0x01, 0x0a,
	0x0c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0b,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x73, 0x63, 0x12, 0x1e, 0x0a, 0x09,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73, 0x72, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0xf8, 0x07, 0x0a, 0x17, 0x4c,
	0x6f, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x49, 0x44, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2e,
	0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f,
	0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x38, 0x0a, 0x18, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x16, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x5f, 0x69, 0x66, 0x73, 0x63, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79,
	0x49, 0x46, 0x53, 0x43, 0x12, 0x3c, 0x0a, 0x1a, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69,
	0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x63, 0x69, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x38, 0x0a, 0x18, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72,
	0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72,
	0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x70, 0x65, 0x6e, 0x6e, 0x79, 0x64, 0x72, 0x6f, 0x70, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x50, 0x65, 0x6e, 0x6e, 0x79, 0x64, 0x72, 0x6f,
	0x70, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x65, 0x6e, 0x6e, 0x79, 0x64,
	0x72, 0x6f, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x50, 0x65, 0x6e, 0x6e, 0x79, 0x64, 0x72, 0x6f, 0x70, 0x54, 0x72,
	0x61, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x68, 0x75, 0x6e, 0x74, 0x65, 0x72,
	0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x48,
	0x75, 0x6e, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x72,
	0x65, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x42, 0x72, 0x65, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x32, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64,
	0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x33, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x33, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x34,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x35, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x35, 0x12,
	0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x36, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x36, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x37, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x37, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x38, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x38, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x31, 0x30, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x31, 0x30, 0x22, 0x84, 0x01, 0x0a, 0x17, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x69,
	0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xcf, 0x01, 0x0a,
	0x1e, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x8c,
	0x01, 0x0a, 0x1f, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x68, 0x0a,
	0x32, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_federal_lending_preapproved_loan_proto_rawDescOnce sync.Once
	file_api_vendors_federal_lending_preapproved_loan_proto_rawDescData = file_api_vendors_federal_lending_preapproved_loan_proto_rawDesc
)

func file_api_vendors_federal_lending_preapproved_loan_proto_rawDescGZIP() []byte {
	file_api_vendors_federal_lending_preapproved_loan_proto_rawDescOnce.Do(func() {
		file_api_vendors_federal_lending_preapproved_loan_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_federal_lending_preapproved_loan_proto_rawDescData)
	})
	return file_api_vendors_federal_lending_preapproved_loan_proto_rawDescData
}

var file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes = make([]protoimpl.MessageInfo, 64)
var file_api_vendors_federal_lending_preapproved_loan_proto_goTypes = []interface{}{
	(*GetInstantLoanClosureRequest)(nil),                                                                                 // 0: vendors.federal.lending.GetInstantLoanClosureRequest
	(*GetInstantLoanClosureResponse)(nil),                                                                                // 1: vendors.federal.lending.GetInstantLoanClosureResponse
	(*GetInstantLoanClosureEnquiryRequest)(nil),                                                                          // 2: vendors.federal.lending.GetInstantLoanClosureEnquiryRequest
	(*GetInstantLoanClosureEnquiryResponse)(nil),                                                                         // 3: vendors.federal.lending.GetInstantLoanClosureEnquiryResponse
	(*GetInstantLoanBalanceRequest)(nil),                                                                                 // 4: vendors.federal.lending.GetInstantLoanBalanceRequest
	(*GetInstantLoanBalanceResponse)(nil),                                                                                // 5: vendors.federal.lending.GetInstantLoanBalanceResponse
	(*GetInstantLoanStatusEnquiryRequest)(nil),                                                                           // 6: vendors.federal.lending.GetInstantLoanStatusEnquiryRequest
	(*GetInstantLoanStatusEnquiryResponse)(nil),                                                                          // 7: vendors.federal.lending.GetInstantLoanStatusEnquiryResponse
	(*GetInstantLoanUnblockRequest)(nil),                                                                                 // 8: vendors.federal.lending.GetInstantLoanUnblockRequest
	(*GetInstantLoanUnblockResponse)(nil),                                                                                // 9: vendors.federal.lending.GetInstantLoanUnblockResponse
	(*GetInstantLoanInfoRequest)(nil),                                                                                    // 10: vendors.federal.lending.GetInstantLoanInfoRequest
	(*GetInstantLoanInfoResponse)(nil),                                                                                   // 11: vendors.federal.lending.GetInstantLoanInfoResponse
	(*GetInstantLoanApplicationRequest)(nil),                                                                             // 12: vendors.federal.lending.GetInstantLoanApplicationRequest
	(*GetInstantLoanApplicationResponse)(nil),                                                                            // 13: vendors.federal.lending.GetInstantLoanApplicationResponse
	(*GetInstantLoanOTPRequest)(nil),                                                                                     // 14: vendors.federal.lending.GetInstantLoanOTPRequest
	(*GetInstantLoanOTPResponse)(nil),                                                                                    // 15: vendors.federal.lending.GetInstantLoanOTPResponse
	(*GetInstantLoanOffersRequest)(nil),                                                                                  // 16: vendors.federal.lending.GetInstantLoanOffersRequest
	(*GetInstantLoanOffersResponse)(nil),                                                                                 // 17: vendors.federal.lending.GetInstantLoanOffersResponse
	(*Header)(nil),                                                                                                       // 18: vendors.federal.lending.Header
	(*FetchLoanDetailsRequest)(nil),                                                                                      // 19: vendors.federal.lending.FetchLoanDetailsRequest
	(*FetchLoanDetailsResponse)(nil),                                                                                     // 20: vendors.federal.lending.FetchLoanDetailsResponse
	(*FetchLoanDetailsErrorResponse)(nil),                                                                                // 21: vendors.federal.lending.FetchLoanDetailsErrorResponse
	(*GetRepaymentScheduleRequest)(nil),                                                                                  // 22: vendors.federal.lending.GetRepaymentScheduleRequest
	(*GetRepaymentScheduleResponse)(nil),                                                                                 // 23: vendors.federal.lending.GetRepaymentScheduleResponse
	(*LoanDisbursementRequest)(nil),                                                                                      // 24: vendors.federal.lending.LoanDisbursementRequest
	(*LoanDisbursementReponse)(nil),                                                                                      // 25: vendors.federal.lending.LoanDisbursementReponse
	(*LoanDisbursementEnquiryRequest)(nil),                                                                               // 26: vendors.federal.lending.LoanDisbursementEnquiryRequest
	(*LoanDisbursementEnquiryResponse)(nil),                                                                              // 27: vendors.federal.lending.LoanDisbursementEnquiryResponse
	(*GetInstantLoanBalanceResponse_ErrorResponse)(nil),                                                                  // 28: vendors.federal.lending.GetInstantLoanBalanceResponse.ErrorResponse
	(*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest)(nil),                                           // 29: vendors.federal.lending.GetInstantLoanStatusEnquiryRequest.InstantLoanStatusEnquiryRequest
	(*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body)(nil),                                      // 30: vendors.federal.lending.GetInstantLoanStatusEnquiryRequest.InstantLoanStatusEnquiryRequest.Body
	(*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2)(nil),                                    // 31: vendors.federal.lending.GetInstantLoanStatusEnquiryRequest.InstantLoanStatusEnquiryRequest.BodyV2
	(*GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse)(nil),                                         // 32: vendors.federal.lending.GetInstantLoanStatusEnquiryResponse.InstantLoanStatusEnquiryResponse
	(*GetInstantLoanUnblockRequest_InstantLoanUnblockRequest)(nil),                                                       // 33: vendors.federal.lending.GetInstantLoanUnblockRequest.InstantLoanUnblockRequest
	(*GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body)(nil),                                                  // 34: vendors.federal.lending.GetInstantLoanUnblockRequest.InstantLoanUnblockRequest.Body
	(*GetInstantLoanUnblockResponse_InstantLoanUnblockResponse)(nil),                                                     // 35: vendors.federal.lending.GetInstantLoanUnblockResponse.InstantLoanUnblockResponse
	(*GetInstantLoanInfoRequest_InstantLoanInfoRequest)(nil),                                                             // 36: vendors.federal.lending.GetInstantLoanInfoRequest.InstantLoanInfoRequest
	(*GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body)(nil),                                                        // 37: vendors.federal.lending.GetInstantLoanInfoRequest.InstantLoanInfoRequest.Body
	(*GetInstantLoanInfoResponse_InstantLoanInfoResponse)(nil),                                                           // 38: vendors.federal.lending.GetInstantLoanInfoResponse.InstantLoanInfoResponse
	(*GetInstantLoanApplicationRequest_InstantLoanProcessRequest)(nil),                                                   // 39: vendors.federal.lending.GetInstantLoanApplicationRequest.InstantLoanProcessRequest
	(*GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body)(nil),                                              // 40: vendors.federal.lending.GetInstantLoanApplicationRequest.InstantLoanProcessRequest.Body
	(*GetInstantLoanApplicationResponse_InstantLoanProcessResponse)(nil),                                                 // 41: vendors.federal.lending.GetInstantLoanApplicationResponse.InstantLoanProcessResponse
	(*GetInstantLoanOTPRequest_InstantLoanOtpRequest)(nil),                                                               // 42: vendors.federal.lending.GetInstantLoanOTPRequest.InstantLoanOtpRequest
	(*GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body)(nil),                                                          // 43: vendors.federal.lending.GetInstantLoanOTPRequest.InstantLoanOtpRequest.Body
	(*GetInstantLoanOTPResponse_InstantLoanOtpResponse)(nil),                                                             // 44: vendors.federal.lending.GetInstantLoanOTPResponse.InstantLoanOtpResponse
	(*GetInstantLoanOffersRequest_InstantLoanOffersRequest)(nil),                                                         // 45: vendors.federal.lending.GetInstantLoanOffersRequest.InstantLoanOffersRequest
	(*GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body)(nil),                                                    // 46: vendors.federal.lending.GetInstantLoanOffersRequest.InstantLoanOffersRequest.Body
	(*GetInstantLoanOffersResponse_InstantLoanOffersResponse)(nil),                                                       // 47: vendors.federal.lending.GetInstantLoanOffersResponse.InstantLoanOffersResponse
	(*GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom)(nil),                                           // 48: vendors.federal.lending.GetInstantLoanOffersResponse.InstantLoanOffersResponse.RangeToFrom
	(*FetchLoanDetailsResponse_Response)(nil),                                                                            // 49: vendors.federal.lending.FetchLoanDetailsResponse.Response
	(*FetchLoanDetailsResponse_ErrorDetails)(nil),                                                                        // 50: vendors.federal.lending.FetchLoanDetailsResponse.ErrorDetails
	(*FetchLoanDetailsResponse_Response_Fetch)(nil),                                                                      // 51: vendors.federal.lending.FetchLoanDetailsResponse.Response.Fetch
	(*FetchLoanDetailsResponse_Response_Fetch_LoanDetails)(nil),                                                          // 52: vendors.federal.lending.FetchLoanDetailsResponse.Response.Fetch.LoanDetails
	(*FetchLoanDetailsErrorResponse_ErrorDetails)(nil),                                                                   // 53: vendors.federal.lending.FetchLoanDetailsErrorResponse.ErrorDetails
	(*GetRepaymentScheduleResponse_Body)(nil),                                                                            // 54: vendors.federal.lending.GetRepaymentScheduleResponse.Body
	(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse)(nil),                                                      // 55: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse
	(*GetRepaymentScheduleResponse_Body_ErrorResponse)(nil),                                                              // 56: vendors.federal.lending.GetRepaymentScheduleResponse.Body.ErrorResponse
	(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct)(nil),                                   // 57: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct
	(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl)(nil),                             // 58: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl
	(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails)(nil),        // 59: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails
	(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key)(nil),                         // 60: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.Key
	(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount)(nil), // 61: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.Amount
	(*GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException)(nil),                                          // 62: vendors.federal.lending.GetRepaymentScheduleResponse.Body.ErrorResponse.FiBusinessException
	(*GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails)(nil),                             // 63: vendors.federal.lending.GetRepaymentScheduleResponse.Body.ErrorResponse.FiBusinessException.ErrorDetails
}
var file_api_vendors_federal_lending_preapproved_loan_proto_depIdxs = []int32{
	28, // 0: vendors.federal.lending.GetInstantLoanBalanceResponse.error:type_name -> vendors.federal.lending.GetInstantLoanBalanceResponse.ErrorResponse
	29, // 1: vendors.federal.lending.GetInstantLoanStatusEnquiryRequest.instant_loan_status_enquiry_request:type_name -> vendors.federal.lending.GetInstantLoanStatusEnquiryRequest.InstantLoanStatusEnquiryRequest
	32, // 2: vendors.federal.lending.GetInstantLoanStatusEnquiryResponse.instant_loan_status_enquiry_response:type_name -> vendors.federal.lending.GetInstantLoanStatusEnquiryResponse.InstantLoanStatusEnquiryResponse
	33, // 3: vendors.federal.lending.GetInstantLoanUnblockRequest.instant_loan_unblock_request:type_name -> vendors.federal.lending.GetInstantLoanUnblockRequest.InstantLoanUnblockRequest
	35, // 4: vendors.federal.lending.GetInstantLoanUnblockResponse.instant_loan_unblock_response:type_name -> vendors.federal.lending.GetInstantLoanUnblockResponse.InstantLoanUnblockResponse
	36, // 5: vendors.federal.lending.GetInstantLoanInfoRequest.instant_loan_info_request:type_name -> vendors.federal.lending.GetInstantLoanInfoRequest.InstantLoanInfoRequest
	38, // 6: vendors.federal.lending.GetInstantLoanInfoResponse.instant_loan_info_response:type_name -> vendors.federal.lending.GetInstantLoanInfoResponse.InstantLoanInfoResponse
	39, // 7: vendors.federal.lending.GetInstantLoanApplicationRequest.instant_loan_process_request:type_name -> vendors.federal.lending.GetInstantLoanApplicationRequest.InstantLoanProcessRequest
	41, // 8: vendors.federal.lending.GetInstantLoanApplicationResponse.instant_loan_process_response:type_name -> vendors.federal.lending.GetInstantLoanApplicationResponse.InstantLoanProcessResponse
	42, // 9: vendors.federal.lending.GetInstantLoanOTPRequest.instant_loan_otp_request:type_name -> vendors.federal.lending.GetInstantLoanOTPRequest.InstantLoanOtpRequest
	44, // 10: vendors.federal.lending.GetInstantLoanOTPResponse.instant_loan_otp_response:type_name -> vendors.federal.lending.GetInstantLoanOTPResponse.InstantLoanOtpResponse
	45, // 11: vendors.federal.lending.GetInstantLoanOffersRequest.instant_loan_offers_request:type_name -> vendors.federal.lending.GetInstantLoanOffersRequest.InstantLoanOffersRequest
	47, // 12: vendors.federal.lending.GetInstantLoanOffersResponse.instant_loan_offers_response:type_name -> vendors.federal.lending.GetInstantLoanOffersResponse.InstantLoanOffersResponse
	49, // 13: vendors.federal.lending.FetchLoanDetailsResponse.response:type_name -> vendors.federal.lending.FetchLoanDetailsResponse.Response
	50, // 14: vendors.federal.lending.FetchLoanDetailsResponse.error_details:type_name -> vendors.federal.lending.FetchLoanDetailsResponse.ErrorDetails
	53, // 15: vendors.federal.lending.FetchLoanDetailsErrorResponse.error_details:type_name -> vendors.federal.lending.FetchLoanDetailsErrorResponse.ErrorDetails
	54, // 16: vendors.federal.lending.GetRepaymentScheduleResponse.body:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body
	18, // 17: vendors.federal.lending.GetInstantLoanStatusEnquiryRequest.InstantLoanStatusEnquiryRequest.header:type_name -> vendors.federal.lending.Header
	30, // 18: vendors.federal.lending.GetInstantLoanStatusEnquiryRequest.InstantLoanStatusEnquiryRequest.body:type_name -> vendors.federal.lending.GetInstantLoanStatusEnquiryRequest.InstantLoanStatusEnquiryRequest.Body
	31, // 19: vendors.federal.lending.GetInstantLoanStatusEnquiryRequest.InstantLoanStatusEnquiryRequest.body_v2:type_name -> vendors.federal.lending.GetInstantLoanStatusEnquiryRequest.InstantLoanStatusEnquiryRequest.BodyV2
	18, // 20: vendors.federal.lending.GetInstantLoanUnblockRequest.InstantLoanUnblockRequest.header:type_name -> vendors.federal.lending.Header
	34, // 21: vendors.federal.lending.GetInstantLoanUnblockRequest.InstantLoanUnblockRequest.body:type_name -> vendors.federal.lending.GetInstantLoanUnblockRequest.InstantLoanUnblockRequest.Body
	18, // 22: vendors.federal.lending.GetInstantLoanInfoRequest.InstantLoanInfoRequest.header:type_name -> vendors.federal.lending.Header
	37, // 23: vendors.federal.lending.GetInstantLoanInfoRequest.InstantLoanInfoRequest.body:type_name -> vendors.federal.lending.GetInstantLoanInfoRequest.InstantLoanInfoRequest.Body
	18, // 24: vendors.federal.lending.GetInstantLoanApplicationRequest.InstantLoanProcessRequest.header:type_name -> vendors.federal.lending.Header
	40, // 25: vendors.federal.lending.GetInstantLoanApplicationRequest.InstantLoanProcessRequest.body:type_name -> vendors.federal.lending.GetInstantLoanApplicationRequest.InstantLoanProcessRequest.Body
	18, // 26: vendors.federal.lending.GetInstantLoanOTPRequest.InstantLoanOtpRequest.header:type_name -> vendors.federal.lending.Header
	43, // 27: vendors.federal.lending.GetInstantLoanOTPRequest.InstantLoanOtpRequest.body:type_name -> vendors.federal.lending.GetInstantLoanOTPRequest.InstantLoanOtpRequest.Body
	18, // 28: vendors.federal.lending.GetInstantLoanOffersRequest.InstantLoanOffersRequest.header:type_name -> vendors.federal.lending.Header
	46, // 29: vendors.federal.lending.GetInstantLoanOffersRequest.InstantLoanOffersRequest.body:type_name -> vendors.federal.lending.GetInstantLoanOffersRequest.InstantLoanOffersRequest.Body
	48, // 30: vendors.federal.lending.GetInstantLoanOffersResponse.InstantLoanOffersResponse.interest_rate:type_name -> vendors.federal.lending.GetInstantLoanOffersResponse.InstantLoanOffersResponse.RangeToFrom
	48, // 31: vendors.federal.lending.GetInstantLoanOffersResponse.InstantLoanOffersResponse.processing_fee_percentage:type_name -> vendors.federal.lending.GetInstantLoanOffersResponse.InstantLoanOffersResponse.RangeToFrom
	51, // 32: vendors.federal.lending.FetchLoanDetailsResponse.Response.fetch:type_name -> vendors.federal.lending.FetchLoanDetailsResponse.Response.Fetch
	52, // 33: vendors.federal.lending.FetchLoanDetailsResponse.Response.Fetch.loan_details:type_name -> vendors.federal.lending.FetchLoanDetailsResponse.Response.Fetch.LoanDetails
	55, // 34: vendors.federal.lending.GetRepaymentScheduleResponse.Body.amort_schedule_response:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse
	56, // 35: vendors.federal.lending.GetRepaymentScheduleResponse.Body.error_response:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.ErrorResponse
	57, // 36: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.amort_schedule_out_struct:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct
	62, // 37: vendors.federal.lending.GetRepaymentScheduleResponse.Body.ErrorResponse.fi_business_exception:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.ErrorResponse.FiBusinessException
	58, // 38: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.out_ll:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl
	59, // 39: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.amort_schedule_details:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails
	60, // 40: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.key:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.Key
	61, // 41: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.comb_instl_amount:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.Amount
	61, // 42: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.cumm_int_amount:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.Amount
	61, // 43: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.cumm_principal_amount:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.Amount
	61, // 44: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.installment_amount:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.Amount
	61, // 45: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.int_amount:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.Amount
	61, // 46: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.principal_amount:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.Amount
	61, // 47: vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.principal_outstanding_amount:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.AmortScheduleResponse.AmortShdlOutStruct.OutLl.AmortScheduleDetails.Amount
	63, // 48: vendors.federal.lending.GetRepaymentScheduleResponse.Body.ErrorResponse.FiBusinessException.error_details:type_name -> vendors.federal.lending.GetRepaymentScheduleResponse.Body.ErrorResponse.FiBusinessException.ErrorDetails
	49, // [49:49] is the sub-list for method output_type
	49, // [49:49] is the sub-list for method input_type
	49, // [49:49] is the sub-list for extension type_name
	49, // [49:49] is the sub-list for extension extendee
	0,  // [0:49] is the sub-list for field type_name
}

func init() { file_api_vendors_federal_lending_preapproved_loan_proto_init() }
func file_api_vendors_federal_lending_preapproved_loan_proto_init() {
	if File_api_vendors_federal_lending_preapproved_loan_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanClosureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanClosureResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanClosureEnquiryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanClosureEnquiryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanStatusEnquiryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanStatusEnquiryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanUnblockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanUnblockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanApplicationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanApplicationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOTPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOTPResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOffersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOffersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Header); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLoanDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLoanDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLoanDetailsErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanDisbursementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanDisbursementReponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanDisbursementEnquiryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanDisbursementEnquiryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanBalanceResponse_ErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_Body); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanStatusEnquiryRequest_InstantLoanStatusEnquiryRequest_BodyV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanStatusEnquiryResponse_InstantLoanStatusEnquiryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanUnblockRequest_InstantLoanUnblockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanUnblockRequest_InstantLoanUnblockRequest_Body); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanUnblockResponse_InstantLoanUnblockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanInfoRequest_InstantLoanInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanInfoRequest_InstantLoanInfoRequest_Body); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanInfoResponse_InstantLoanInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanApplicationRequest_InstantLoanProcessRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanApplicationRequest_InstantLoanProcessRequest_Body); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanApplicationResponse_InstantLoanProcessResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOTPRequest_InstantLoanOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOTPRequest_InstantLoanOtpRequest_Body); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOTPResponse_InstantLoanOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOffersRequest_InstantLoanOffersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOffersRequest_InstantLoanOffersRequest_Body); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOffersResponse_InstantLoanOffersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstantLoanOffersResponse_InstantLoanOffersResponse_RangeToFrom); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLoanDetailsResponse_Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLoanDetailsResponse_ErrorDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLoanDetailsResponse_Response_Fetch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLoanDetailsResponse_Response_Fetch_LoanDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLoanDetailsErrorResponse_ErrorDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Body); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Body_ErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_Key); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Body_AmortScheduleResponse_AmortShdlOutStruct_OutLl_AmortScheduleDetails_Amount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentScheduleResponse_Body_ErrorResponse_FiBusinessException_ErrorDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_federal_lending_preapproved_loan_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   64,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_federal_lending_preapproved_loan_proto_goTypes,
		DependencyIndexes: file_api_vendors_federal_lending_preapproved_loan_proto_depIdxs,
		MessageInfos:      file_api_vendors_federal_lending_preapproved_loan_proto_msgTypes,
	}.Build()
	File_api_vendors_federal_lending_preapproved_loan_proto = out.File
	file_api_vendors_federal_lending_preapproved_loan_proto_rawDesc = nil
	file_api_vendors_federal_lending_preapproved_loan_proto_goTypes = nil
	file_api_vendors_federal_lending_preapproved_loan_proto_depIdxs = nil
}
