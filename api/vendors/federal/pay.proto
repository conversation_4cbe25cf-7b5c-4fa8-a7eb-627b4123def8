// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal/payment";
option java_package = "com.github.epifi.gamma.api.vendors.federal/payment";

message DepositAccountTransferRequest {

  // Epi<PERSON>'s credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 1 [json_name = "SenderCode"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];

  // Registered customer device ID
  string device_id = 4 [json_name="DeviceId"];

  // Device token that is issued by the federal bank at the time of device registration.
  // It will be available in device registration response.
  string device_token = 5 [json_name="DeviceToken"];

  // Encrypted PIN block that is required to authorise the transaction
  // All transaction requests except for low risk ones need Secure PIN for authorisation
  //
  // This is non-mandatory field and can be left blank.
  string secure_pin = 6 [json_name="CredBlock"];

  // Client should pass unique value to each call, to identify the request
  string request_id = 7 [json_name="RequestId"];

  // Only numeric values are allowed.
  // Unique identifier for customer
  string customer_id= 8 [json_name = "CustomerId"];

  // savings account number from which amount will be debited
  string operative_account_no = 9 [json_name = "OperativeAccountNum"];

  // deposit account number to which amount will be credited
  string deposit_account_no = 10 [json_name = "DepositAccountNum"];

  // Only numeric values are allowed.
  //  Amount to be Transferred in xxx.yy Format E.g 123.45 INR
  string amount = 11 [json_name = "Amount"];

  // registered phone number of user
  string phone_number = 12 [json_name = "MobileNumber"];
}

message DepositAccountTransferResponse {
  // Epifi's credentials user code provided by Federal bank
  string sender_code = 1 [json_name = "SenderCode"];

  // Device token sent in API request
  string device_token = 2 [json_name="DeviceToken"];

  // Request ID that is passed in the request
  string request_id = 3 [json_name="RequestId"];

  // unique customerId passed in the request
  string customer_id = 4 [json_name="CustomerId"];

  // saving account number from which amount is deducted
  string operative_account_no = 5 [json_name="OperativeAccountNum"];

  // deposit account number to which amount will be credited
  string deposit_account_no = 6 [json_name="DepositAccountNum"];

  // amount transferred
  string amount = 7 [json_name="Amount"];

  // unique transfer reference
  string utr = 8 [json_name="UTR"];

  // Transaction Timestamp
  string trans_timestamp = 9 [json_name="TranTimeStamp"];

  // Response Code for the Transaction
  string response_code = 10 [json_name="ResponseCode"];

  // Response Description for the Transaction
  string response_desc = 11 [json_name="ResponseReason"];

  // action need to take based on response code.
  string response_action = 12 [json_name="ResponseAction"];
}
