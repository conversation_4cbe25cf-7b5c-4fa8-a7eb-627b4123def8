syntax = "proto3";

package vendors.mfcentral;

import "api/vendors/mfcentral/error.proto";

option go_package = "github.com/epifi/gamma/api/vendors/mfcentral";

message MFCentralPayload {
  string req_id = 1 [json_name = "reqId"];
  string pan = 2 [json_name = "pan"];
  string pekrn = 3 [json_name = "pekrn"];
  string email = 4 [json_name = "email"];
  string from_date = 5 [json_name = "fromDate"];
  string to_date = 6 [json_name = "toDate"];
  repeated Data data = 7 [json_name = "data"];
  InvestorDetails investor_details = 8 [json_name = "investorDetails"];
  repeated Error errors = 9 [json_name = "errors"];
}


message Data{
  repeated DtTransaction dt_transaction = 1 [json_name = "dtTransaction"];
  repeated DtSummary dt_summary = 2 [json_name = "dtSummary"];
}

message DtTransaction {
  string email = 1 [json_name = "email"];
  string amc = 2 [json_name = "amc"];
  string amc_name = 3 [json_name = "amcName"];
  string folio = 4 [json_name = "folio"];
  string check_digit = 5 [json_name = "checkDigit"];
  string transaction_date = 6 [json_name = "trxnDate"];
  string posted_date = 7 [json_name = "postedDate"];
  string scheme = 8 [json_name = "scheme"];
  string scheme_name = 9 [json_name = "schemeName"];
  string trxn_desc = 10 [json_name = "trxnDesc"];
  string trxn_amount = 11 [json_name = "trxnAmount"];
  string trxn_units = 12 [json_name = "trxnUnits"];
  string stt_tax = 13 [json_name = "sttTax"];
  string tax = 14 [json_name = "tax"];
  string total_tax = 15 [json_name = "totalTax"];
  string trxn_mode = 16 [json_name = "trxnMode"];
  string purchase_price = 17 [json_name = "purchasePrice"];
  string stamp_duty = 18 [json_name = "stampDuty"];
  string trxn_charge = 19 [json_name = "trxnCharge"];
  string trxn_type_flag = 20 [json_name = "trxnTypeFlag"];
  string isin = 21 [json_name = "isin"];
}

message DtSummary {
  string email = 1 [json_name = "email"];
  string amc = 2 [json_name = "amc"];
  string amc_name = 3 [json_name = "amcName"];
  string folio = 4 [json_name = "folio"];
  string scheme = 5 [json_name = "scheme"];
  string scheme_name = 6 [json_name = "schemeName"];
  string kyc_status = 7 [json_name = "kycStatus"];
  string broker_code = 8 [json_name = "brokerCode"];
  string broker_name = 9 [json_name = "brokerName"];
  string rta_code = 10 [json_name = "rtaCode"];
  string decimal_units = 11 [json_name = "decimalUnits"];
  string decimal_amount = 12 [json_name = "decimalAmount"];
  string decimal_nav = 13 [json_name = "decimalNav"];
  string last_trxn_date = 14 [json_name = "lastTrxnDate"];
  string opening_bal = 15 [json_name = "openingBal"];
  string market_value = 16 [json_name = "marketValue"];
  string nav = 17 [json_name = "nav"];
  string closing_balance = 18 [json_name = "closingBalance"];
  string last_nav_date = 19 [json_name = "lastNavDate"];
  string is_demat = 20 [json_name = "isDemat"];
  string asset_type = 21 [json_name = "assetType"];
  string isin = 22 [json_name = "isin"];
  string nominee_status = 23 [json_name = "nomineeStatus"];
}

message InvestorDetails{
  Address address = 1   [json_name = "address"];
  string email = 2   [json_name = "email"];
  string mobile = 3   [json_name = "mobile"];
  string investor_first_name = 4   [json_name = "investorFirstName"];
  string investor_middle_name = 5   [json_name = "investorMiddleName"];
  string investor_last_name = 6   [json_name = "investorLastName"];
}

message Address{
  string address_line_1 = 1   [json_name = "address1"];
  string address_line_2 = 2   [json_name = "address2"];
  string address_line_3 = 3   [json_name = "address3"];
  string city = 4   [json_name = "city"];
  string state = 5   [json_name = "state"];
  string district = 6   [json_name = "district"];
  string pincode = 7   [json_name = "pincode"];
  string country = 8   [json_name = "country"];
}
