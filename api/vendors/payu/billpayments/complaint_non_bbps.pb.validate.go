// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/payu/billpayments/complaint_non_bbps.proto

package billpayments

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RaiseComplaintNonBBPSRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RaiseComplaintNonBBPSRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaiseComplaintNonBBPSRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RaiseComplaintNonBBPSRequestMultiError, or nil if none found.
func (m *RaiseComplaintNonBBPSRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RaiseComplaintNonBBPSRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BillerId

	// no validation rules for Type

	// no validation rules for ComplaintMessage

	// no validation rules for RefId

	if len(errors) > 0 {
		return RaiseComplaintNonBBPSRequestMultiError(errors)
	}

	return nil
}

// RaiseComplaintNonBBPSRequestMultiError is an error wrapping multiple
// validation errors returned by RaiseComplaintNonBBPSRequest.ValidateAll() if
// the designated constraints aren't met.
type RaiseComplaintNonBBPSRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaiseComplaintNonBBPSRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaiseComplaintNonBBPSRequestMultiError) AllErrors() []error { return m }

// RaiseComplaintNonBBPSRequestValidationError is the validation error returned
// by RaiseComplaintNonBBPSRequest.Validate if the designated constraints
// aren't met.
type RaiseComplaintNonBBPSRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaiseComplaintNonBBPSRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaiseComplaintNonBBPSRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaiseComplaintNonBBPSRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaiseComplaintNonBBPSRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaiseComplaintNonBBPSRequestValidationError) ErrorName() string {
	return "RaiseComplaintNonBBPSRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RaiseComplaintNonBBPSRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaiseComplaintNonBBPSRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaiseComplaintNonBBPSRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaiseComplaintNonBBPSRequestValidationError{}

// Validate checks the field values on RaiseComplaintNonBBPSResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RaiseComplaintNonBBPSResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaiseComplaintNonBBPSResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RaiseComplaintNonBBPSResponseMultiError, or nil if none found.
func (m *RaiseComplaintNonBBPSResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RaiseComplaintNonBBPSResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetSuccessPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseComplaintNonBBPSResponseValidationError{
					field:  "SuccessPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseComplaintNonBBPSResponseValidationError{
					field:  "SuccessPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSuccessPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseComplaintNonBBPSResponseValidationError{
				field:  "SuccessPayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RaiseComplaintNonBBPSResponseMultiError(errors)
	}

	return nil
}

// RaiseComplaintNonBBPSResponseMultiError is an error wrapping multiple
// validation errors returned by RaiseComplaintNonBBPSResponse.ValidateAll()
// if the designated constraints aren't met.
type RaiseComplaintNonBBPSResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaiseComplaintNonBBPSResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaiseComplaintNonBBPSResponseMultiError) AllErrors() []error { return m }

// RaiseComplaintNonBBPSResponseValidationError is the validation error
// returned by RaiseComplaintNonBBPSResponse.Validate if the designated
// constraints aren't met.
type RaiseComplaintNonBBPSResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaiseComplaintNonBBPSResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaiseComplaintNonBBPSResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaiseComplaintNonBBPSResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaiseComplaintNonBBPSResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaiseComplaintNonBBPSResponseValidationError) ErrorName() string {
	return "RaiseComplaintNonBBPSResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RaiseComplaintNonBBPSResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaiseComplaintNonBBPSResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaiseComplaintNonBBPSResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaiseComplaintNonBBPSResponseValidationError{}

// Validate checks the field values on CheckComplaintStatusNonBBPSRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckComplaintStatusNonBBPSRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckComplaintStatusNonBBPSRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckComplaintStatusNonBBPSRequestMultiError, or nil if none found.
func (m *CheckComplaintStatusNonBBPSRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckComplaintStatusNonBBPSRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComplainId

	if len(errors) > 0 {
		return CheckComplaintStatusNonBBPSRequestMultiError(errors)
	}

	return nil
}

// CheckComplaintStatusNonBBPSRequestMultiError is an error wrapping multiple
// validation errors returned by
// CheckComplaintStatusNonBBPSRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckComplaintStatusNonBBPSRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckComplaintStatusNonBBPSRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckComplaintStatusNonBBPSRequestMultiError) AllErrors() []error { return m }

// CheckComplaintStatusNonBBPSRequestValidationError is the validation error
// returned by CheckComplaintStatusNonBBPSRequest.Validate if the designated
// constraints aren't met.
type CheckComplaintStatusNonBBPSRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckComplaintStatusNonBBPSRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckComplaintStatusNonBBPSRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckComplaintStatusNonBBPSRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckComplaintStatusNonBBPSRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckComplaintStatusNonBBPSRequestValidationError) ErrorName() string {
	return "CheckComplaintStatusNonBBPSRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckComplaintStatusNonBBPSRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckComplaintStatusNonBBPSRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckComplaintStatusNonBBPSRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckComplaintStatusNonBBPSRequestValidationError{}

// Validate checks the field values on CheckComplaintStatusNonBBPSResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckComplaintStatusNonBBPSResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckComplaintStatusNonBBPSResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckComplaintStatusNonBBPSResponseMultiError, or nil if none found.
func (m *CheckComplaintStatusNonBBPSResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckComplaintStatusNonBBPSResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckComplaintStatusNonBBPSResponseValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckComplaintStatusNonBBPSResponseValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckComplaintStatusNonBBPSResponseValidationError{
				field:  "Payload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckComplaintStatusNonBBPSResponseMultiError(errors)
	}

	return nil
}

// CheckComplaintStatusNonBBPSResponseMultiError is an error wrapping multiple
// validation errors returned by
// CheckComplaintStatusNonBBPSResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckComplaintStatusNonBBPSResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckComplaintStatusNonBBPSResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckComplaintStatusNonBBPSResponseMultiError) AllErrors() []error { return m }

// CheckComplaintStatusNonBBPSResponseValidationError is the validation error
// returned by CheckComplaintStatusNonBBPSResponse.Validate if the designated
// constraints aren't met.
type CheckComplaintStatusNonBBPSResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckComplaintStatusNonBBPSResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckComplaintStatusNonBBPSResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckComplaintStatusNonBBPSResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckComplaintStatusNonBBPSResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckComplaintStatusNonBBPSResponseValidationError) ErrorName() string {
	return "CheckComplaintStatusNonBBPSResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckComplaintStatusNonBBPSResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckComplaintStatusNonBBPSResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckComplaintStatusNonBBPSResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckComplaintStatusNonBBPSResponseValidationError{}

// Validate checks the field values on RaiseComplaintNonBBPSResponse_Payload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RaiseComplaintNonBBPSResponse_Payload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaiseComplaintNonBBPSResponse_Payload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RaiseComplaintNonBBPSResponse_PayloadMultiError, or nil if none found.
func (m *RaiseComplaintNonBBPSResponse_Payload) ValidateAll() error {
	return m.validate(true)
}

func (m *RaiseComplaintNonBBPSResponse_Payload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RefId

	// no validation rules for ComplaintStatus

	// no validation rules for ComplaintId

	for idx, item := range m.GetErrors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RaiseComplaintNonBBPSResponse_PayloadValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RaiseComplaintNonBBPSResponse_PayloadValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RaiseComplaintNonBBPSResponse_PayloadValidationError{
					field:  fmt.Sprintf("Errors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Type

	// no validation rules for Message

	// no validation rules for AdditionalParams

	if len(errors) > 0 {
		return RaiseComplaintNonBBPSResponse_PayloadMultiError(errors)
	}

	return nil
}

// RaiseComplaintNonBBPSResponse_PayloadMultiError is an error wrapping
// multiple validation errors returned by
// RaiseComplaintNonBBPSResponse_Payload.ValidateAll() if the designated
// constraints aren't met.
type RaiseComplaintNonBBPSResponse_PayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaiseComplaintNonBBPSResponse_PayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaiseComplaintNonBBPSResponse_PayloadMultiError) AllErrors() []error { return m }

// RaiseComplaintNonBBPSResponse_PayloadValidationError is the validation error
// returned by RaiseComplaintNonBBPSResponse_Payload.Validate if the
// designated constraints aren't met.
type RaiseComplaintNonBBPSResponse_PayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaiseComplaintNonBBPSResponse_PayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaiseComplaintNonBBPSResponse_PayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaiseComplaintNonBBPSResponse_PayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaiseComplaintNonBBPSResponse_PayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaiseComplaintNonBBPSResponse_PayloadValidationError) ErrorName() string {
	return "RaiseComplaintNonBBPSResponse_PayloadValidationError"
}

// Error satisfies the builtin error interface
func (e RaiseComplaintNonBBPSResponse_PayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaiseComplaintNonBBPSResponse_Payload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaiseComplaintNonBBPSResponse_PayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaiseComplaintNonBBPSResponse_PayloadValidationError{}

// Validate checks the field values on
// CheckComplaintStatusNonBBPSResponse_Payload with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckComplaintStatusNonBBPSResponse_Payload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckComplaintStatusNonBBPSResponse_Payload with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckComplaintStatusNonBBPSResponse_PayloadMultiError, or nil if none found.
func (m *CheckComplaintStatusNonBBPSResponse_Payload) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckComplaintStatusNonBBPSResponse_Payload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RefId

	// no validation rules for BillerReply

	// no validation rules for ComplaintId

	// no validation rules for BillerId

	// no validation rules for ComplaintStatus

	for idx, item := range m.GetErrors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckComplaintStatusNonBBPSResponse_PayloadValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckComplaintStatusNonBBPSResponse_PayloadValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckComplaintStatusNonBBPSResponse_PayloadValidationError{
					field:  fmt.Sprintf("Errors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Type

	// no validation rules for Message

	// no validation rules for AdditionalParams

	if len(errors) > 0 {
		return CheckComplaintStatusNonBBPSResponse_PayloadMultiError(errors)
	}

	return nil
}

// CheckComplaintStatusNonBBPSResponse_PayloadMultiError is an error wrapping
// multiple validation errors returned by
// CheckComplaintStatusNonBBPSResponse_Payload.ValidateAll() if the designated
// constraints aren't met.
type CheckComplaintStatusNonBBPSResponse_PayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckComplaintStatusNonBBPSResponse_PayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckComplaintStatusNonBBPSResponse_PayloadMultiError) AllErrors() []error { return m }

// CheckComplaintStatusNonBBPSResponse_PayloadValidationError is the validation
// error returned by CheckComplaintStatusNonBBPSResponse_Payload.Validate if
// the designated constraints aren't met.
type CheckComplaintStatusNonBBPSResponse_PayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckComplaintStatusNonBBPSResponse_PayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckComplaintStatusNonBBPSResponse_PayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckComplaintStatusNonBBPSResponse_PayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckComplaintStatusNonBBPSResponse_PayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckComplaintStatusNonBBPSResponse_PayloadValidationError) ErrorName() string {
	return "CheckComplaintStatusNonBBPSResponse_PayloadValidationError"
}

// Error satisfies the builtin error interface
func (e CheckComplaintStatusNonBBPSResponse_PayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckComplaintStatusNonBBPSResponse_Payload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckComplaintStatusNonBBPSResponse_PayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckComplaintStatusNonBBPSResponse_PayloadValidationError{}
