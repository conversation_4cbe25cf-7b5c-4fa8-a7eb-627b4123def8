// A service for handling communication with users through notifications.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/karvy/nctcf_token.proto

package karvy

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// NCTCFToken represent the authentication token which is used currently for Nominee Update API.
type NCTCFToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token      string                 `protobuf:"bytes,1,opt,name=Token,proto3" json:"Token,omitempty"`
	ExpiryDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=ExpiryDate,proto3" json:"ExpiryDate,omitempty"`
	// this is unused as of now as the token type is always a bearer-token.
	Type string `protobuf:"bytes,3,opt,name=Type,proto3" json:"Type,omitempty"`
}

func (x *NCTCFToken) Reset() {
	*x = NCTCFToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_karvy_nctcf_token_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NCTCFToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NCTCFToken) ProtoMessage() {}

func (x *NCTCFToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_karvy_nctcf_token_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NCTCFToken.ProtoReflect.Descriptor instead.
func (*NCTCFToken) Descriptor() ([]byte, []int) {
	return file_api_vendors_karvy_nctcf_token_proto_rawDescGZIP(), []int{0}
}

func (x *NCTCFToken) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *NCTCFToken) GetExpiryDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryDate
	}
	return nil
}

func (x *NCTCFToken) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

var File_api_vendors_karvy_nctcf_token_proto protoreflect.FileDescriptor

var file_api_vendors_karvy_nctcf_token_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6b, 0x61,
	0x72, 0x76, 0x79, 0x2f, 0x6e, 0x63, 0x74, 0x63, 0x66, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6b,
	0x61, 0x72, 0x76, 0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x72, 0x0a, 0x0a, 0x4e, 0x43, 0x54, 0x43, 0x46, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3a, 0x0a, 0x0a, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x6b, 0x61, 0x72, 0x76, 0x79, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6b, 0x61, 0x72, 0x76, 0x79, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_karvy_nctcf_token_proto_rawDescOnce sync.Once
	file_api_vendors_karvy_nctcf_token_proto_rawDescData = file_api_vendors_karvy_nctcf_token_proto_rawDesc
)

func file_api_vendors_karvy_nctcf_token_proto_rawDescGZIP() []byte {
	file_api_vendors_karvy_nctcf_token_proto_rawDescOnce.Do(func() {
		file_api_vendors_karvy_nctcf_token_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_karvy_nctcf_token_proto_rawDescData)
	})
	return file_api_vendors_karvy_nctcf_token_proto_rawDescData
}

var file_api_vendors_karvy_nctcf_token_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_vendors_karvy_nctcf_token_proto_goTypes = []interface{}{
	(*NCTCFToken)(nil),            // 0: vendors.karvy.NCTCFToken
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_api_vendors_karvy_nctcf_token_proto_depIdxs = []int32{
	1, // 0: vendors.karvy.NCTCFToken.ExpiryDate:type_name -> google.protobuf.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_vendors_karvy_nctcf_token_proto_init() }
func file_api_vendors_karvy_nctcf_token_proto_init() {
	if File_api_vendors_karvy_nctcf_token_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_karvy_nctcf_token_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NCTCFToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_karvy_nctcf_token_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_karvy_nctcf_token_proto_goTypes,
		DependencyIndexes: file_api_vendors_karvy_nctcf_token_proto_depIdxs,
		MessageInfos:      file_api_vendors_karvy_nctcf_token_proto_msgTypes,
	}.Build()
	File_api_vendors_karvy_nctcf_token_proto = out.File
	file_api_vendors_karvy_nctcf_token_proto_rawDesc = nil
	file_api_vendors_karvy_nctcf_token_proto_goTypes = nil
	file_api_vendors_karvy_nctcf_token_proto_depIdxs = nil
}
