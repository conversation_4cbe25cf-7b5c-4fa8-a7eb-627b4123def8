syntax = "proto3";

package vendors.uqudo.passport;

option go_package = "github.com/epifi/gamma/api/vendors/uqudo/passport";
option java_package = "com.github.epifi.gamma.api.vendors.uqudo.passport";

message Front {
  string first_name = 1 [json_name = "firstName"];
  string secondary_id = 2 [json_name = "secondaryId"];
  string last_name = 3 [json_name = "lastName"];
  string primary_id = 4 [json_name = "primaryId"];
  string date_of_birth = 5 [json_name = "dateOfBirth"];
  string date_of_birth_formatted = 6 [json_name = "dateOfBirthFormatted"];
  string date_of_birth_full = 7 [json_name = "dateOfBirthFull"];
  string place_of_birth = 8 [json_name = "placeOfBirth"];
  string issue_date = 9 [json_name = "issueDate"];
  string issue_date_formatted = 10 [json_name = "issueDateFormatted"];
  string date_of_expiry = 11 [json_name = "dateOfExpiry"];
  string date_of_expiry_formatted = 12 [json_name = "dateOfExpiryFormatted"];
  string date_of_expiry_full = 13 [json_name = "dateOfExpiryFull"];
  string date_of_expiry_full_formatted = 14 [json_name = "dateOfExpiryFullFormatted"];
  string document_number = 15 [json_name = "documentNumber"];
  string nationality = 16 [json_name = "nationality"];
  string issuer = 17 [json_name = "issuer"];
  string place_of_issue = 18 [json_name = "placeOfIssue"];
  string sex = 19 [json_name = "sex"];
  string document_code = 20 [json_name = "documentCode"];
  string mrz_text = 21 [json_name = "mrzText"];
  bool mrz_verified = 22 [json_name = "mrzVerified"];
  string opt1 = 23 [json_name = "opt1"];
  bool chip_available = 24 [json_name = "chipAvailable"];
}

message Scan {
  Front front = 1 [json_name = "front"];
  string face_image_id = 2 [json_name = "faceImageId"];
  string front_image_id = 3 [json_name = "frontImageId"];
  string back_image_id = 4 [json_name = "backImageId"];
  string session_scan_configuration = 5 [json_name = "sessionScanConfiguration"];
}

message Document {
  string document_type = 1 [json_name = "documentType"];
  Scan scan = 2 [json_name = "scan"];
}

message SourceField {
  string source = 1 [json_name = "source"];
  string document_side = 2 [json_name = "documentSide"];
  string name = 3 [json_name = "name"];
  string value = 4 [json_name = "value"];
}

message Field {
  string name = 1 [json_name = "name"];
  string match = 2 [json_name = "match"];
  repeated SourceField sources = 3 [json_name = "sources"];
}

message DataConsistencyCheck {
  bool enabled = 1 [json_name = "enabled"];
  repeated Field fields = 2 [json_name = "fields"];
}

message SourceDetection {
  bool enabled = 1 [json_name = "enabled"];
  bool allow_non_physical_documents = 2 [json_name = "allowNonPhysicalDocuments"];
  string selected_resolution = 3 [json_name = "selectedResolution"];
  bool optimal_resolution = 4 [json_name = "optimalResolution"];
}

message IdScreenDetection {
  bool enabled = 1 [json_name = "enabled"];
  float score = 2 [json_name = "score"];
}

message IdPrintDetection {
  bool enabled = 1 [json_name = "enabled"];
  float score = 2 [json_name = "score"];
}

message IdPhotoTamperingDetection {
  bool enabled = 1 [json_name = "enabled"];
  float score = 2 [json_name = "score"];
}

message CheckDigit {
  string field_name = 1 [json_name = "fieldName"];
  string field_value = 2 [json_name = "fieldValue"];
  string check_digit = 3 [json_name = "checkDigit"];
  bool valid = 4 [json_name = "valid"];
}

message MrzChecksum {
  bool enabled = 1 [json_name = "enabled"];
  string final_check_digit = 2 [json_name = "finalCheckDigit"];
  bool valid = 3 [json_name = "valid"];
  repeated CheckDigit check_digits = 4 [json_name = "checkDigits"];
}

message Verifications {
  string document_type = 1 [json_name = "documentType"];
  DataConsistencyCheck data_consistency_check = 2 [json_name = "dataConsistencyCheck"];
  SourceDetection source_detection = 3 [json_name = "sourceDetection"];
  IdScreenDetection id_screen_detection = 4 [json_name = "idScreenDetection"];
  IdPrintDetection id_print_detection = 5 [json_name = "idPrintDetection"];
  IdPhotoTamperingDetection id_photo_tampering_detection = 6 [json_name = "idPhotoTamperingDetection"];
  MrzChecksum mrz_checksum = 7 [json_name = "mrzChecksum"];

  message Reading {
    bool enabled = 1 [json_name = "enabled"];
  }

  message Biometric {
    bool enabled = 1 [json_name = "enabled"];
  }

  message Lookup {
    bool enabled = 1 [json_name = "enabled"];
  }

  Reading reading = 8 [json_name = "reading"];
  Biometric biometric = 9 [json_name = "biometric"];
  Lookup lookup = 10 [json_name = "lookup"];
}

message Data {
  repeated Document documents = 1 [json_name = "documents"];
  repeated Verifications verifications = 2 [json_name = "verifications"];
}
