syntax = "proto3";

package vendors.moneyview;

option go_package = "github.com/epifi/gamma/api/vendors/moneyview";
option java_package = "com.github.epifi.gamma.api.vendors.moneyview";

message GetLeadStatusRequest {
}

message GetLeadStatusResponse {
  string lead_status = 1 [json_name = "leadStatus"];
  string status = 2 [json_name = "status"];
  string message = 3 [json_name = "message"];
  string code = 4 [json_name = "code"];
  double final_loan_amount = 5 [json_name = "finalLoanAmount"];
  double disbursed_amount = 6 [json_name = "disbursedAmount"];
  string disbursal_date = 7 [json_name = "disbursalDate"];
}
