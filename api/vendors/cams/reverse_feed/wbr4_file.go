package reverse_feed

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"reflect"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
)

type WBR4File struct {
	FOLIOCHK            string // It is equivalent to FolioNumber
	INV_NAME            string
	ADDRESS1            string
	ADDRESS2            string
	ADDRESS3            string
	CITY                string
	PINCODE             string
	PRODUCT             string
	SCH_NAME            string
	REP_DATE            string // It is equivalent to ReportDate
	CLOS_BAL            string // It is equivalent to Units
	RUPEE_BAL           string
	SUBBROK             string
	REINV_FLAG          string
	JOINT1_NAME         string
	JOINT2_NAME         string
	PHONE_OFF           string
	PHONE_RES           string
	EMAIL               string
	HOLDING_NATURE      string
	UIN_NO              string
	BROKER_CODE         string
	PAN_NO              string
	JOINT1_PAN          string
	JOINT2_PAN          string
	GUARD_PAN           string
	TAX_STATUS          string
	INV_IIN             string
	ALTFOLIO            string
	EUIN                string
	EXCHANGE_FLAG       string
	TPA_LINKED          string
	FH_CKYC_NO          string
	JH1_CKYC            string
	JH2_CKYC            string
	G_CKYC_NO           string
	JH1_DOB             string
	JH2_DOB             string
	GUARDIAN_DOB        string
	AMC_CODE            string
	GST_STATE_CODE      string
	FOLIO_OLD           string
	SCHEME_FOLIO_NUMBER string
	COUNTRY             string
}

var WBR4FieldMap = map[int]string{
	0:  "FOLIOCHK", // Changed Parameter
	1:  "INV_NAME",
	2:  "ADDRESS1",
	3:  "ADDRESS2",
	4:  "ADDRESS3",
	5:  "CITY",
	6:  "PINCODE",
	7:  "PRODUCT",
	8:  "SCH_NAME",
	9:  "REP_DATE", // Changed Parameter
	10: "CLOS_BAL", // Changed Parameter
	11: "RUPEE_BAL",
	12: "SUBBROK",
	13: "REINV_FLAG",
	14: "JOINT1_NAME",
	15: "JOINT2_NAME",
	16: "PHONE_OFF",
	17: "PHONE_RES",
	18: "EMAIL",
	19: "HOLDING_NATURE",
	20: "UIN_NO",
	21: "BROKER_CODE",
	22: "PAN_NO",
	23: "JOINT1_PAN",
	24: "JOINT2_PAN",
	25: "GUARD_PAN",
	26: "TAX_STATUS",
	27: "INV_IIN",
	28: "ALTFOLIO",
	29: "EUIN",
	30: "EXCHANGE_FLAG",
	31: "TPA_LINKED",
	32: "FH_CKYC_NO",
	33: "JH1_CKYC",
	34: "JH2_CKYC",
	35: "G_CKYC_NO",
	36: "JH1_DOB",
	37: "JH2_DOB",
	38: "GUARDIAN_DOB",
	39: "AMC_CODE",
	40: "GST_STATE_CODE",
	41: "FOLIO_OLD",
	42: "SCHEME_FOLIO_NUMBER",
	43: "COUNTRY",
}

//nolint:dupl
func ParseWBR4File(ctx context.Context, fileContent []byte) ([]*WBR4File, error) {
	// Replace single quotes with double quotes
	// Needs to be done to allow csv reader to read multi-line and comma containing address in files
	contentStr := strings.ReplaceAll(string(fileContent), "'", "\"")
	reader := csv.NewReader(strings.NewReader(contentStr))
	reader.LazyQuotes = true
	reader.TrimLeadingSpace = true
	var wbr4Rows []*WBR4File
	// Todo(Pratyush): check if we can use ReuseRecord in Read() for efficient processing

	headers, err := reader.Read()
	if err != nil {
		return nil, fmt.Errorf("failed to read headers: %v", err)
	}

	// Create a dynamic field map based on the CSV headers
	fieldIndexMap := make(map[int]string)
	for idx, header := range headers {
		for _, structField := range WBR4FieldMap {
			if structField == header {
				fieldIndexMap[idx] = structField
				break
			}
		}
	}

	for {
		// Read one line at a time
		feed, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error while reading line: %s", err.Error()))
			continue
		}
		wbr4File := WBR4File{}
		for keyIdx, value := range feed {
			if keyIdx >= len(WBR4FieldMap) {
				break
			}

			keyName, keyOk := fieldIndexMap[keyIdx]
			if !keyOk {
				logger.ErrorNoCtx(fmt.Sprintf("error while fetching key name using index, keyIdx: %v", keyIdx))
				continue
			}
			reflect.ValueOf(&wbr4File).Elem().FieldByName(keyName).SetString(value)
		}
		logger.Info(ctx, "value of struct", zap.Any("struct", wbr4File), zap.String(logger.FOLIO_ID, wbr4File.FOLIOCHK))
		wbr4Rows = append(wbr4Rows, &wbr4File)
	}
	return wbr4Rows[0:], nil
}
