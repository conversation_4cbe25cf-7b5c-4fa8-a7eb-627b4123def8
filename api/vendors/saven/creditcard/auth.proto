syntax = "proto3";

package api.vendors.saven.creditcard;

option go_package = "github.com/epifi/gamma/api/vendors/saven/creditcard";
option java_package = "com.github.epifi.gamma.api.vendors.saven.creditcard";

message GenerateCreditCardSdkAuthTokenRequest {
  // Applicant type (ETB, NTB, PRE-APPROVED) - default CMS
  string applicant_type = 1 [json_name = "applicantType"];

  // User information
  UserInfo user_info = 2 [json_name = "userInfo"];

  // Device information
  DeviceInfo device_info = 3 [json_name = "deviceInfo"];

  // PAN information (optional)
  PanInfo pan_info = 4 [json_name = "panInfo"];

  // Consent information (optional)
  ConsentInfo consent_info = 5 [json_name = "consentInfo"];

  // Pre-approved information (conditional optional)
  PreApprovedInfo pre_approved_info = 6 [json_name = "preApprovedInfo"];
}

message UserInfo {
  // Email address (required)
  string email_address = 1 [json_name = "emailAddress"];
  // Phone number (required)
  string phone_number = 2 [json_name = "phoneNumber"];
  // Phone type (optional)
  string phone_type = 3 [json_name = "phoneType"];
  // Internal user ID (required)
  string internal_user_id = 4 [json_name = "internalUserId"];
}

message DeviceInfo {
  // FI app version (required)
  string fi_app_version = 1 [json_name = "fiAppVersion"];
  // Device ID (required)
  string device_id = 2 [json_name = "deviceId"];
  // IP address (required)
  string ip_address = 3 [json_name = "ipAddress"];
  // Timestamp (required)
  string timestamp = 4 [json_name = "timestamp"];
  // Device details (required)
  string device_details = 5 [json_name = "deviceDetails"];
}

message PanInfo {
  // Permanent account number (optional)
  string permanent_account_number = 1 [json_name = "permanentAccountNumber"];
  // PAN full name (optional)
  string pan_full_name = 2 [json_name = "panFullName"];
}

message ConsentInfo {
  repeated Consent consents = 1 [json_name = "consents"];
}

message Consent {
  // Indicates if the consent is recorded.
  bool is_consent_recorded = 1 [json_name = "isConsentRecorded"];
  // Type of the consent.
  string consent_type = 2 [json_name = "consentType"];
  // Category of the consent.
  string consent_category = 3 [json_name = "consentCategory"];
}

message PreApprovedInfo {
  // Pre-approved limit (conditional optional)
  string pre_approved_limit = 1 [json_name = "preApprovedLimit"];
  // Pre-approved expiration (conditional optional)
  string pre_approved_exp = 2 [json_name = "preApprovedExp"];
}


message GenerateCreditCardSdkAuthTokenResponse {
  // Status of the request.
  string status = 1;

  // Data containing the module name, generated auth token, and workflow details.
  Data data = 2 [json_name = "data"];
  Error error = 3 [json_name = "error"];

  message Error {
    string error_code = 1 [json_name = "code"];
    string error_type = 2 [json_name = "name"];
    string error_message = 3 [json_name = "message"];
  }

  message Data {
    // Module name.
    string module_name = 1 [json_name = "moduleName"];
    // Authentication token.
    string token = 2;
    // Workflow details.
    Workflow workflow = 3;

    message Workflow {
      // Workflow identifier.
      string workflow_id = 1 [json_name = "workflowId"];
      // User local identifier (nullable).
      string user_local_id = 2 [json_name = "userLocalId"];
      // Handshake user local identifier.
      string handshake_user_local_id = 3 [json_name = "handshakeUserLocalId"];
      // Workflow status.
      string workflow_status = 4 [json_name = "workflowStatus"];
      // Workflow state.
      string workflow_state = 5 [json_name = "workflowState"];
      // Workflow type.
      string workflow_type = 6 [json_name = "workflowType"];
      // Workflow version.
      string workflow_version = 7 [json_name = "workflowVersion"];
      // Workflow message (nullable).
      string workflow_message = 8 [json_name = "workflowMessage"];
      // Creation timestamp.
      string created_at = 9 [json_name = "createdAt"];
      // Update timestamp.
      string updated_at = 10 [json_name = "updatedAt"];
    }
  }
}

message CreditCardSdkAuthTokenizedRequest {
  string token = 1;
}
