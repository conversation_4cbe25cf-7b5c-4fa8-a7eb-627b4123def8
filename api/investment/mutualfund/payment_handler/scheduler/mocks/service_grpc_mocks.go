// Code generated by MockGen. DO NOT EDIT.
// Source: api/investment/mutualfund/payment_handler/scheduler/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	scheduler "github.com/epifi/gamma/api/investment/mutualfund/payment_handler/scheduler"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockSchedulerClient is a mock of SchedulerClient interface.
type MockSchedulerClient struct {
	ctrl     *gomock.Controller
	recorder *MockSchedulerClientMockRecorder
}

// MockSchedulerClientMockRecorder is the mock recorder for MockSchedulerClient.
type MockSchedulerClientMockRecorder struct {
	mock *MockSchedulerClient
}

// NewMockSchedulerClient creates a new mock instance.
func NewMockSchedulerClient(ctrl *gomock.Controller) *MockSchedulerClient {
	mock := &MockSchedulerClient{ctrl: ctrl}
	mock.recorder = &MockSchedulerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSchedulerClient) EXPECT() *MockSchedulerClientMockRecorder {
	return m.recorder
}

// BackFillPaymentDetailsFromTransaction mocks base method.
func (m *MockSchedulerClient) BackFillPaymentDetailsFromTransaction(ctx context.Context, in *scheduler.BackFillPaymentDetailsFromTransactionRequest, opts ...grpc.CallOption) (*scheduler.BackFillPaymentDetailsFromTransactionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BackFillPaymentDetailsFromTransaction", varargs...)
	ret0, _ := ret[0].(*scheduler.BackFillPaymentDetailsFromTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BackFillPaymentDetailsFromTransaction indicates an expected call of BackFillPaymentDetailsFromTransaction.
func (mr *MockSchedulerClientMockRecorder) BackFillPaymentDetailsFromTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BackFillPaymentDetailsFromTransaction", reflect.TypeOf((*MockSchedulerClient)(nil).BackFillPaymentDetailsFromTransaction), varargs...)
}

// MockSchedulerServer is a mock of SchedulerServer interface.
type MockSchedulerServer struct {
	ctrl     *gomock.Controller
	recorder *MockSchedulerServerMockRecorder
}

// MockSchedulerServerMockRecorder is the mock recorder for MockSchedulerServer.
type MockSchedulerServerMockRecorder struct {
	mock *MockSchedulerServer
}

// NewMockSchedulerServer creates a new mock instance.
func NewMockSchedulerServer(ctrl *gomock.Controller) *MockSchedulerServer {
	mock := &MockSchedulerServer{ctrl: ctrl}
	mock.recorder = &MockSchedulerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSchedulerServer) EXPECT() *MockSchedulerServerMockRecorder {
	return m.recorder
}

// BackFillPaymentDetailsFromTransaction mocks base method.
func (m *MockSchedulerServer) BackFillPaymentDetailsFromTransaction(arg0 context.Context, arg1 *scheduler.BackFillPaymentDetailsFromTransactionRequest) (*scheduler.BackFillPaymentDetailsFromTransactionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BackFillPaymentDetailsFromTransaction", arg0, arg1)
	ret0, _ := ret[0].(*scheduler.BackFillPaymentDetailsFromTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BackFillPaymentDetailsFromTransaction indicates an expected call of BackFillPaymentDetailsFromTransaction.
func (mr *MockSchedulerServerMockRecorder) BackFillPaymentDetailsFromTransaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BackFillPaymentDetailsFromTransaction", reflect.TypeOf((*MockSchedulerServer)(nil).BackFillPaymentDetailsFromTransaction), arg0, arg1)
}

// MockUnsafeSchedulerServer is a mock of UnsafeSchedulerServer interface.
type MockUnsafeSchedulerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeSchedulerServerMockRecorder
}

// MockUnsafeSchedulerServerMockRecorder is the mock recorder for MockUnsafeSchedulerServer.
type MockUnsafeSchedulerServerMockRecorder struct {
	mock *MockUnsafeSchedulerServer
}

// NewMockUnsafeSchedulerServer creates a new mock instance.
func NewMockUnsafeSchedulerServer(ctrl *gomock.Controller) *MockUnsafeSchedulerServer {
	mock := &MockUnsafeSchedulerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeSchedulerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeSchedulerServer) EXPECT() *MockUnsafeSchedulerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedSchedulerServer mocks base method.
func (m *MockUnsafeSchedulerServer) mustEmbedUnimplementedSchedulerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSchedulerServer")
}

// mustEmbedUnimplementedSchedulerServer indicates an expected call of mustEmbedUnimplementedSchedulerServer.
func (mr *MockUnsafeSchedulerServerMockRecorder) mustEmbedUnimplementedSchedulerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSchedulerServer", reflect.TypeOf((*MockUnsafeSchedulerServer)(nil).mustEmbedUnimplementedSchedulerServer))
}
