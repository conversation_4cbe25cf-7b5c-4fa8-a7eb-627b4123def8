syntax = "proto3";

package api.investment.mutualfund.payment_handler;

import "google/type/money.proto";
import "api/rpc/status.proto";
import "api/investment/mutualfund/payment_handler/payment.proto";
import "api/order/domain/request.proto";
import "api/recurringpayment/recurring_payment.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund/payment_handler";

service PaymentHandler {
  /*
    ExecutePayment rpc takes in an orderId and initiates the payment execution for that particular order. The status of the
    payment being done for the order is returned. This rpc depending on the type of payment can be sync or async.

    For payment_mode = PAYMENT_MODE_SI, the rpc is async and if the status of the payment in the response is
    PAYMENT_STATUS_PENDING, then the upstream service is expected to do a status check.
  */
  rpc ExecutePayment(.order.domain.ProcessPaymentRequest) returns (.order.domain.ProcessPaymentResponse);
  /*
    GetPaymentDetails fetches the latest payment details of an order id.
  */
  rpc GetPaymentDetails(GetPaymentDetailsRequest) returns (GetPaymentDetailsResponse);

  /*
    GetPaymentInfoForActorAndMutualFund fetches the payment details and constraints for any
    mutual fund transaction between an actor and a mutual fund
  */
  rpc GetPaymentInfoForActorAndMutualFund(GetPaymentInfoForActorAndMutualFundRequest) returns (GetPaymentInfoForActorAndMutualFundResponse);
  /*
    CreatePaymentForInvestmentOrder would create a new Payment entry for that order.
    It is used to create an investment payment corresponding to an OMS payment order
  */
  rpc CreatePaymentForInvestmentOrder(CreatePaymentForInvestmentOrderRequest) returns (CreatePaymentForInvestmentOrderResponse);

  // GetBatchPaymentStatus fetches the payment status of a list of orderIds.
  rpc GetBatchPaymentDetails(GetBatchPaymentDetailsRequest) returns (GetBatchPaymentDetailsResponse);

  // GetOrderDetailsByPaymentId fetches mutual funds details and order details for a payment-id
  rpc GetOrderAndMFDetailsByPaymentId(GetOrderAndMFDetailsByPaymentIdRequest) returns (GetOrderAndMFDetailsByPaymentIdResponse);

  //CreatePaymentForAutoInvestOrder is responsible for creating payments for auto-invest Orders
  rpc CreatePaymentForAutoInvestOrder(CreatePaymentForAutoInvestOrderRequest) returns (CreatePaymentForAutoInvestOrderResponse);
}


message GetPaymentDetailsRequest {
  string order_id = 1;
  payment_handler.PaymentMode payment_mode = 2;
}

message GetPaymentDetailsResponse {
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  payment_handler.PaymentStatus payment_status = 2;

  // represents the UTR Reference Number. This will be present only when the payment_status is PAYMENT_STATUS_SUCCESSFUL
  string utr_ref_number = 3;
  // represents the transaction time at which the payment became successful. This will be present only when the payment_status is PAYMENT_STATUS_SUCCESSFUL
  google.protobuf.Timestamp transaction_time = 4;

  // failure_message represents the payment failure reason if the payment is either failed or stuck in manual intervention
  string failure_message = 5;
}

message GetPaymentInfoForActorAndMutualFundRequest{
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  string mutual_fund_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];
}
message GetPaymentInfoForActorAndMutualFundResponse{
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  recurringpayment.RecurringPayment recurring_payment_info = 2;
}


message CreatePaymentForInvestmentOrderRequest {
  // Investment order for which the payment has to be created
  string investment_order_id = 1;
  // Payment mode for the transaction
  PaymentMode payment_mode = 2;
  // Transaction amount
  google.type.Money amount = 3;
  // OMS order id which corresponds to the order which would have the payment transaction
  string payment_order_id = 4;
  // Idempotency key for preventing duplicate creations from the client side.
  // Would be mapped to 'attempt_id' in the payment
  string client_id = 5;
}

message CreatePaymentForInvestmentOrderResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    ALREADY_EXISTS = 6;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  Payment payment = 2;
}

enum PaymentFieldMask {
  PAYMENT_FIELD_MASK_UNSPECIFIED = 0;
  PAYMENT_FIELD_MASK_Order_ID = 1;
  PAYMENT_FIELD_MASK_PAYMENT_ID = 2;
  PAYMENT_FIELD_MASK_STATUS = 3;
  PAYMENT_FIELD_MASK_IS_ACTIVE = 4;
  PAYMENT_FIELD_MASK_META_DATA = 5;
  PAYMENT_FIELD_MASK_DELETED_AT = 6;
  PAYMENT_FIELD_MASK_TXN_PROTOCOL = 7;
  PAYMENT_FIELD_MASK_TXN_FAILURE_CODE = 8;
  PAYMENT_FIELD_MASK_UTR_NUMBER = 9;
  PAYMENT_FIELD_MASK_TXN_COMPLETED_AT = 10;
}

message GetBatchPaymentDetailsRequest {
  enum DataFreshness{
    DATA_FRESHNESS_UNSPECIFIED = 0;
    // Returns the data available at mf_payments and will not query downstream for fetching the details.
    // utr number will not be returned for this as downstream services are not queried.
    STALE = 1;
  }
  // enum used to give options to client to decide on how much stale response is acceptable
  DataFreshness data_freshness = 1;

  repeated string order_ids = 2;
}


message PaymentDetails {
  Payment payment = 1;
  string utr_number = 2;
}

message GetBatchPaymentDetailsResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  map<string, PaymentDetails> order_id_to_payment_details = 2;
}

message GetOrderAndMFDetailsByPaymentIdRequest{
   // payment id, usually is the oms order id for the payment
    string payment_id = 1;
}

message GetOrderAndMFDetailsByPaymentIdResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // MutualFundDetails contains trivial details about a mutual-fund like short-name, mfId
  MutualFundDetails mutual_fund_details = 2;
  // OrderDetails contains trivial details about a mutual-fund order like orderId, amount, units etc.
  OrderDetails order_details =3;
}

// MutualFundDetails is a subset of mutual fund proto
// We can add any more fields corresponding to Mutual fund proto here as and when needed
message MutualFundDetails{
  string short_name = 1;
  string amc_name = 2;
  string mutual_fund_id = 3;
}

// OrderDetails is a subset of order proto
// We can add any more fields corresponding to Order proto here as and when needed
message OrderDetails{
  string order_id = 1;
  string actor_id = 2;
  double units = 3;
  google.type.Money amount = 4;
}

message CreatePaymentForAutoInvestOrderRequest{
  string investment_order_id = 1;
  google.type.Money amount = 2;
}

message CreatePaymentForAutoInvestOrderResponse{
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    ALREADY_EXISTS = 6;
    NOT_FOUND = 5;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  Payment payment = 2;
}
