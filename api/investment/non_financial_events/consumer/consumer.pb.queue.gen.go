// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/investment/non_financial_events/consumer
package consumer

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessNonFinancialEventMethod = "ProcessNonFinancialEvent"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &ProcessNonFinancialEventRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessNonFinancialEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessNonFinancialEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessNonFinancialEventMethodToSubscriber(subscriber queue.Subscriber, srv NonFinancialEventConsumerServer) {
	subscriber.RegisterService(&NonFinancialEventConsumer_ServiceDesc, srv, ProcessNonFinancialEventMethod)
}
