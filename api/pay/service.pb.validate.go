// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/pay/service.proto

package pay

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/pay/enums"

	order "github.com/epifi/gamma/api/order"

	payment "github.com/epifi/gamma/api/order/payment"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"

	vendorgateway1 "github.com/epifi/gamma/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = common.Ownership(0)

	_ = enums.PaymentScope(0)

	_ = order.OrderWorkflow(0)

	_ = payment.TransactionDetailedStatus_DetailedStatus_API(0)

	_ = vendorgateway.Vendor(0)

	_ = vendorgateway1.RequestSource(0)
)

// Validate checks the field values on EnrichOrderAndTransactionsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *EnrichOrderAndTransactionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnrichOrderAndTransactionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// EnrichOrderAndTransactionsRequestMultiError, or nil if none found.
func (m *EnrichOrderAndTransactionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnrichOrderAndTransactionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _EnrichOrderAndTransactionsRequest_EnrichmentSource_NotInLookup[m.GetEnrichmentSource()]; ok {
		err := EnrichOrderAndTransactionsRequestValidationError{
			field:  "EnrichmentSource",
			reason: "value must not be in list [ENRICHMENT_SOURCE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.EnrichmentSourcePayload.(type) {
	case *EnrichOrderAndTransactionsRequest_DebitCardSwitchPayload_:
		if v == nil {
			err := EnrichOrderAndTransactionsRequestValidationError{
				field:  "EnrichmentSourcePayload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDebitCardSwitchPayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EnrichOrderAndTransactionsRequestValidationError{
						field:  "DebitCardSwitchPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EnrichOrderAndTransactionsRequestValidationError{
						field:  "DebitCardSwitchPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDebitCardSwitchPayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EnrichOrderAndTransactionsRequestValidationError{
					field:  "DebitCardSwitchPayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return EnrichOrderAndTransactionsRequestMultiError(errors)
	}

	return nil
}

// EnrichOrderAndTransactionsRequestMultiError is an error wrapping multiple
// validation errors returned by
// EnrichOrderAndTransactionsRequest.ValidateAll() if the designated
// constraints aren't met.
type EnrichOrderAndTransactionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnrichOrderAndTransactionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnrichOrderAndTransactionsRequestMultiError) AllErrors() []error { return m }

// EnrichOrderAndTransactionsRequestValidationError is the validation error
// returned by EnrichOrderAndTransactionsRequest.Validate if the designated
// constraints aren't met.
type EnrichOrderAndTransactionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnrichOrderAndTransactionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnrichOrderAndTransactionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnrichOrderAndTransactionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnrichOrderAndTransactionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnrichOrderAndTransactionsRequestValidationError) ErrorName() string {
	return "EnrichOrderAndTransactionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EnrichOrderAndTransactionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnrichOrderAndTransactionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnrichOrderAndTransactionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnrichOrderAndTransactionsRequestValidationError{}

var _EnrichOrderAndTransactionsRequest_EnrichmentSource_NotInLookup = map[EnrichOrderAndTransactionsRequest_EnrichmentSource]struct{}{
	0: {},
}

// Validate checks the field values on EnrichOrderAndTransactionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *EnrichOrderAndTransactionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnrichOrderAndTransactionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// EnrichOrderAndTransactionsResponseMultiError, or nil if none found.
func (m *EnrichOrderAndTransactionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EnrichOrderAndTransactionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnrichOrderAndTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnrichOrderAndTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnrichOrderAndTransactionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderWithTransactions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnrichOrderAndTransactionsResponseValidationError{
					field:  "OrderWithTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnrichOrderAndTransactionsResponseValidationError{
					field:  "OrderWithTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderWithTransactions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnrichOrderAndTransactionsResponseValidationError{
				field:  "OrderWithTransactions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnrichOrderAndTransactionsResponseMultiError(errors)
	}

	return nil
}

// EnrichOrderAndTransactionsResponseMultiError is an error wrapping multiple
// validation errors returned by
// EnrichOrderAndTransactionsResponse.ValidateAll() if the designated
// constraints aren't met.
type EnrichOrderAndTransactionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnrichOrderAndTransactionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnrichOrderAndTransactionsResponseMultiError) AllErrors() []error { return m }

// EnrichOrderAndTransactionsResponseValidationError is the validation error
// returned by EnrichOrderAndTransactionsResponse.Validate if the designated
// constraints aren't met.
type EnrichOrderAndTransactionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnrichOrderAndTransactionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnrichOrderAndTransactionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnrichOrderAndTransactionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnrichOrderAndTransactionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnrichOrderAndTransactionsResponseValidationError) ErrorName() string {
	return "EnrichOrderAndTransactionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EnrichOrderAndTransactionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnrichOrderAndTransactionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnrichOrderAndTransactionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnrichOrderAndTransactionsResponseValidationError{}

// Validate checks the field values on GetPlainDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlainDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlainDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlainDataRequestMultiError, or nil if none found.
func (m *GetPlainDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlainDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetSignedData()) < 1 {
		err := GetPlainDataRequestValidationError{
			field:  "SignedData",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetPlainDataRequestMultiError(errors)
	}

	return nil
}

// GetPlainDataRequestMultiError is an error wrapping multiple validation
// errors returned by GetPlainDataRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPlainDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlainDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlainDataRequestMultiError) AllErrors() []error { return m }

// GetPlainDataRequestValidationError is the validation error returned by
// GetPlainDataRequest.Validate if the designated constraints aren't met.
type GetPlainDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlainDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlainDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlainDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlainDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlainDataRequestValidationError) ErrorName() string {
	return "GetPlainDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlainDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlainDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlainDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlainDataRequestValidationError{}

// Validate checks the field values on GetPlainDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlainDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlainDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlainDataResponseMultiError, or nil if none found.
func (m *GetPlainDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlainDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPlainDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPlainDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPlainDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlainData

	if len(errors) > 0 {
		return GetPlainDataResponseMultiError(errors)
	}

	return nil
}

// GetPlainDataResponseMultiError is an error wrapping multiple validation
// errors returned by GetPlainDataResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPlainDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlainDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlainDataResponseMultiError) AllErrors() []error { return m }

// GetPlainDataResponseValidationError is the validation error returned by
// GetPlainDataResponse.Validate if the designated constraints aren't met.
type GetPlainDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlainDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlainDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlainDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlainDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlainDataResponseValidationError) ErrorName() string {
	return "GetPlainDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlainDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlainDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlainDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlainDataResponseValidationError{}

// Validate checks the field values on GetSignedDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSignedDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSignedDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSignedDataRequestMultiError, or nil if none found.
func (m *GetSignedDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSignedDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetData()) < 1 {
		err := GetSignedDataRequestValidationError{
			field:  "Data",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetSignedDataRequestMultiError(errors)
	}

	return nil
}

// GetSignedDataRequestMultiError is an error wrapping multiple validation
// errors returned by GetSignedDataRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSignedDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSignedDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSignedDataRequestMultiError) AllErrors() []error { return m }

// GetSignedDataRequestValidationError is the validation error returned by
// GetSignedDataRequest.Validate if the designated constraints aren't met.
type GetSignedDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSignedDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSignedDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSignedDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSignedDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSignedDataRequestValidationError) ErrorName() string {
	return "GetSignedDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSignedDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSignedDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSignedDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSignedDataRequestValidationError{}

// Validate checks the field values on GetSignedDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSignedDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSignedDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSignedDataResponseMultiError, or nil if none found.
func (m *GetSignedDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSignedDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSignedDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSignedDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSignedDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SignedData

	if len(errors) > 0 {
		return GetSignedDataResponseMultiError(errors)
	}

	return nil
}

// GetSignedDataResponseMultiError is an error wrapping multiple validation
// errors returned by GetSignedDataResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSignedDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSignedDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSignedDataResponseMultiError) AllErrors() []error { return m }

// GetSignedDataResponseValidationError is the validation error returned by
// GetSignedDataResponse.Validate if the designated constraints aren't met.
type GetSignedDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSignedDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSignedDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSignedDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSignedDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSignedDataResponseValidationError) ErrorName() string {
	return "GetSignedDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSignedDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSignedDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSignedDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSignedDataResponseValidationError{}

// Validate checks the field values on InitiateOffAppUpiWorkflowRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateOffAppUpiWorkflowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateOffAppUpiWorkflowRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// InitiateOffAppUpiWorkflowRequestMultiError, or nil if none found.
func (m *InitiateOffAppUpiWorkflowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateOffAppUpiWorkflowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetParsedTxnParticulars() == nil {
		err := InitiateOffAppUpiWorkflowRequestValidationError{
			field:  "ParsedTxnParticulars",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetParsedTxnParticulars()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateOffAppUpiWorkflowRequestValidationError{
					field:  "ParsedTxnParticulars",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateOffAppUpiWorkflowRequestValidationError{
					field:  "ParsedTxnParticulars",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParsedTxnParticulars()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateOffAppUpiWorkflowRequestValidationError{
				field:  "ParsedTxnParticulars",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if m.GetVendorTxnDetails() == nil {
		err := InitiateOffAppUpiWorkflowRequestValidationError{
			field:  "VendorTxnDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetVendorTxnDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateOffAppUpiWorkflowRequestValidationError{
					field:  "VendorTxnDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateOffAppUpiWorkflowRequestValidationError{
					field:  "VendorTxnDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorTxnDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateOffAppUpiWorkflowRequestValidationError{
				field:  "VendorTxnDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _InitiateOffAppUpiWorkflowRequest_PartnerBank_NotInLookup[m.GetPartnerBank()]; ok {
		err := InitiateOffAppUpiWorkflowRequestValidationError{
			field:  "PartnerBank",
			reason: "value must not be in list [VENDOR_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _InitiateOffAppUpiWorkflowRequest_AccountType_NotInLookup[m.GetAccountType()]; ok {
		err := InitiateOffAppUpiWorkflowRequestValidationError{
			field:  "AccountType",
			reason: "value must not be in list [TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetInternalAccountId()) < 1 {
		err := InitiateOffAppUpiWorkflowRequestValidationError{
			field:  "InternalAccountId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetInternalActorId()) < 1 {
		err := InitiateOffAppUpiWorkflowRequestValidationError{
			field:  "InternalActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _InitiateOffAppUpiWorkflowRequest_Source_NotInLookup[m.GetSource()]; ok {
		err := InitiateOffAppUpiWorkflowRequestValidationError{
			field:  "Source",
			reason: "value must not be in list [API_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReceivedFromVendorAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateOffAppUpiWorkflowRequestValidationError{
					field:  "ReceivedFromVendorAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateOffAppUpiWorkflowRequestValidationError{
					field:  "ReceivedFromVendorAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReceivedFromVendorAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateOffAppUpiWorkflowRequestValidationError{
				field:  "ReceivedFromVendorAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ForceSkipEnquiry

	if len(errors) > 0 {
		return InitiateOffAppUpiWorkflowRequestMultiError(errors)
	}

	return nil
}

// InitiateOffAppUpiWorkflowRequestMultiError is an error wrapping multiple
// validation errors returned by
// InitiateOffAppUpiWorkflowRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateOffAppUpiWorkflowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateOffAppUpiWorkflowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateOffAppUpiWorkflowRequestMultiError) AllErrors() []error { return m }

// InitiateOffAppUpiWorkflowRequestValidationError is the validation error
// returned by InitiateOffAppUpiWorkflowRequest.Validate if the designated
// constraints aren't met.
type InitiateOffAppUpiWorkflowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateOffAppUpiWorkflowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateOffAppUpiWorkflowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateOffAppUpiWorkflowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateOffAppUpiWorkflowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateOffAppUpiWorkflowRequestValidationError) ErrorName() string {
	return "InitiateOffAppUpiWorkflowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateOffAppUpiWorkflowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateOffAppUpiWorkflowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateOffAppUpiWorkflowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateOffAppUpiWorkflowRequestValidationError{}

var _InitiateOffAppUpiWorkflowRequest_PartnerBank_NotInLookup = map[vendorgateway.Vendor]struct{}{
	0: {},
}

var _InitiateOffAppUpiWorkflowRequest_AccountType_NotInLookup = map[accounts.Type]struct{}{
	0: {},
}

var _InitiateOffAppUpiWorkflowRequest_Source_NotInLookup = map[payment.TransactionDetailedStatus_DetailedStatus_API]struct{}{
	0: {},
}

// Validate checks the field values on InitiateOffAppUpiWorkflowResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateOffAppUpiWorkflowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateOffAppUpiWorkflowResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiateOffAppUpiWorkflowResponseMultiError, or nil if none found.
func (m *InitiateOffAppUpiWorkflowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateOffAppUpiWorkflowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateOffAppUpiWorkflowResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateOffAppUpiWorkflowResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateOffAppUpiWorkflowResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for WorkflowId

	// no validation rules for RunId

	if len(errors) > 0 {
		return InitiateOffAppUpiWorkflowResponseMultiError(errors)
	}

	return nil
}

// InitiateOffAppUpiWorkflowResponseMultiError is an error wrapping multiple
// validation errors returned by
// InitiateOffAppUpiWorkflowResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateOffAppUpiWorkflowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateOffAppUpiWorkflowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateOffAppUpiWorkflowResponseMultiError) AllErrors() []error { return m }

// InitiateOffAppUpiWorkflowResponseValidationError is the validation error
// returned by InitiateOffAppUpiWorkflowResponse.Validate if the designated
// constraints aren't met.
type InitiateOffAppUpiWorkflowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateOffAppUpiWorkflowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateOffAppUpiWorkflowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateOffAppUpiWorkflowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateOffAppUpiWorkflowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateOffAppUpiWorkflowResponseValidationError) ErrorName() string {
	return "InitiateOffAppUpiWorkflowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateOffAppUpiWorkflowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateOffAppUpiWorkflowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateOffAppUpiWorkflowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateOffAppUpiWorkflowResponseValidationError{}

// Validate checks the field values on GetTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionRequestMultiError, or nil if none found.
func (m *GetTransactionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransactionId

	if len(errors) > 0 {
		return GetTransactionRequestMultiError(errors)
	}

	return nil
}

// GetTransactionRequestMultiError is an error wrapping multiple validation
// errors returned by GetTransactionRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionRequestMultiError) AllErrors() []error { return m }

// GetTransactionRequestValidationError is the validation error returned by
// GetTransactionRequest.Validate if the designated constraints aren't met.
type GetTransactionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionRequestValidationError) ErrorName() string {
	return "GetTransactionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionRequestValidationError{}

// Validate checks the field values on GetTransactionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionResponseMultiError, or nil if none found.
func (m *GetTransactionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionResponseValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionResponseValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionResponseValidationError{
				field:  "Transaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTransactionResponseMultiError(errors)
	}

	return nil
}

// GetTransactionResponseMultiError is an error wrapping multiple validation
// errors returned by GetTransactionResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionResponseMultiError) AllErrors() []error { return m }

// GetTransactionResponseValidationError is the validation error returned by
// GetTransactionResponse.Validate if the designated constraints aren't met.
type GetTransactionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionResponseValidationError) ErrorName() string {
	return "GetTransactionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionResponseValidationError{}

// Validate checks the field values on UpdateAndChangeTransactionStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateAndChangeTransactionStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateAndChangeTransactionStatusRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateAndChangeTransactionStatusRequestMultiError, or nil if none found.
func (m *UpdateAndChangeTransactionStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAndChangeTransactionStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAndChangeTransactionStatusRequestValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAndChangeTransactionStatusRequestValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAndChangeTransactionStatusRequestValidationError{
				field:  "Transaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReqInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAndChangeTransactionStatusRequestValidationError{
					field:  "ReqInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAndChangeTransactionStatusRequestValidationError{
					field:  "ReqInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReqInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAndChangeTransactionStatusRequestValidationError{
				field:  "ReqInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentStatus

	// no validation rules for NextStatus

	if len(errors) > 0 {
		return UpdateAndChangeTransactionStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateAndChangeTransactionStatusRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateAndChangeTransactionStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAndChangeTransactionStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAndChangeTransactionStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAndChangeTransactionStatusRequestMultiError) AllErrors() []error { return m }

// UpdateAndChangeTransactionStatusRequestValidationError is the validation
// error returned by UpdateAndChangeTransactionStatusRequest.Validate if the
// designated constraints aren't met.
type UpdateAndChangeTransactionStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAndChangeTransactionStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAndChangeTransactionStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAndChangeTransactionStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAndChangeTransactionStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAndChangeTransactionStatusRequestValidationError) ErrorName() string {
	return "UpdateAndChangeTransactionStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAndChangeTransactionStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAndChangeTransactionStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAndChangeTransactionStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAndChangeTransactionStatusRequestValidationError{}

// Validate checks the field values on UpdateAndChangeTransactionStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateAndChangeTransactionStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateAndChangeTransactionStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateAndChangeTransactionStatusResponseMultiError, or nil if none found.
func (m *UpdateAndChangeTransactionStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAndChangeTransactionStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAndChangeTransactionStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAndChangeTransactionStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAndChangeTransactionStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAndChangeTransactionStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateAndChangeTransactionStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// UpdateAndChangeTransactionStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateAndChangeTransactionStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAndChangeTransactionStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAndChangeTransactionStatusResponseMultiError) AllErrors() []error { return m }

// UpdateAndChangeTransactionStatusResponseValidationError is the validation
// error returned by UpdateAndChangeTransactionStatusResponse.Validate if the
// designated constraints aren't met.
type UpdateAndChangeTransactionStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAndChangeTransactionStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAndChangeTransactionStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAndChangeTransactionStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAndChangeTransactionStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAndChangeTransactionStatusResponseValidationError) ErrorName() string {
	return "UpdateAndChangeTransactionStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAndChangeTransactionStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAndChangeTransactionStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAndChangeTransactionStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAndChangeTransactionStatusResponseValidationError{}

// Validate checks the field values on GetTransactionByDedupeIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionByDedupeIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionByDedupeIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTransactionByDedupeIdRequestMultiError, or nil if none found.
func (m *GetTransactionByDedupeIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionByDedupeIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDedupeId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionByDedupeIdRequestValidationError{
					field:  "DedupeId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionByDedupeIdRequestValidationError{
					field:  "DedupeId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDedupeId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionByDedupeIdRequestValidationError{
				field:  "DedupeId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTransactionByDedupeIdRequestMultiError(errors)
	}

	return nil
}

// GetTransactionByDedupeIdRequestMultiError is an error wrapping multiple
// validation errors returned by GetTransactionByDedupeIdRequest.ValidateAll()
// if the designated constraints aren't met.
type GetTransactionByDedupeIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionByDedupeIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionByDedupeIdRequestMultiError) AllErrors() []error { return m }

// GetTransactionByDedupeIdRequestValidationError is the validation error
// returned by GetTransactionByDedupeIdRequest.Validate if the designated
// constraints aren't met.
type GetTransactionByDedupeIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionByDedupeIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionByDedupeIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionByDedupeIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionByDedupeIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionByDedupeIdRequestValidationError) ErrorName() string {
	return "GetTransactionByDedupeIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionByDedupeIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionByDedupeIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionByDedupeIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionByDedupeIdRequestValidationError{}

// Validate checks the field values on GetTransactionByDedupeIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTransactionByDedupeIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionByDedupeIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTransactionByDedupeIdResponseMultiError, or nil if none found.
func (m *GetTransactionByDedupeIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionByDedupeIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionByDedupeIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionByDedupeIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionByDedupeIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionByDedupeIdResponseValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionByDedupeIdResponseValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionByDedupeIdResponseValidationError{
				field:  "Transaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTransactionByDedupeIdResponseMultiError(errors)
	}

	return nil
}

// GetTransactionByDedupeIdResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTransactionByDedupeIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionByDedupeIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionByDedupeIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionByDedupeIdResponseMultiError) AllErrors() []error { return m }

// GetTransactionByDedupeIdResponseValidationError is the validation error
// returned by GetTransactionByDedupeIdResponse.Validate if the designated
// constraints aren't met.
type GetTransactionByDedupeIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionByDedupeIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionByDedupeIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionByDedupeIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionByDedupeIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionByDedupeIdResponseValidationError) ErrorName() string {
	return "GetTransactionByDedupeIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionByDedupeIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionByDedupeIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionByDedupeIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionByDedupeIdResponseValidationError{}

// Validate checks the field values on CreateTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTransactionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTransactionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTransactionRequestMultiError, or nil if none found.
func (m *CreateTransactionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTransactionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTransactionRequestValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTransactionRequestValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTransactionRequestValidationError{
				field:  "Transaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReqInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTransactionRequestValidationError{
					field:  "ReqInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTransactionRequestValidationError{
					field:  "ReqInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReqInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTransactionRequestValidationError{
				field:  "ReqInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTransactionRequestMultiError(errors)
	}

	return nil
}

// CreateTransactionRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTransactionRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTransactionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTransactionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTransactionRequestMultiError) AllErrors() []error { return m }

// CreateTransactionRequestValidationError is the validation error returned by
// CreateTransactionRequest.Validate if the designated constraints aren't met.
type CreateTransactionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTransactionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTransactionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTransactionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTransactionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTransactionRequestValidationError) ErrorName() string {
	return "CreateTransactionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTransactionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTransactionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTransactionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTransactionRequestValidationError{}

// Validate checks the field values on CreateTransactionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTransactionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTransactionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTransactionResponseMultiError, or nil if none found.
func (m *CreateTransactionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTransactionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTransactionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTransactionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTransactionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTransactionResponseValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTransactionResponseValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTransactionResponseValidationError{
				field:  "Transaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTransactionResponseMultiError(errors)
	}

	return nil
}

// CreateTransactionResponseMultiError is an error wrapping multiple validation
// errors returned by CreateTransactionResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateTransactionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTransactionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTransactionResponseMultiError) AllErrors() []error { return m }

// CreateTransactionResponseValidationError is the validation error returned by
// CreateTransactionResponse.Validate if the designated constraints aren't met.
type CreateTransactionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTransactionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTransactionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTransactionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTransactionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTransactionResponseValidationError) ErrorName() string {
	return "CreateTransactionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTransactionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTransactionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTransactionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTransactionResponseValidationError{}

// Validate checks the field values on CreateOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOrderRequestMultiError, or nil if none found.
func (m *CreateOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrderRequestValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrderRequestValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrderRequestValidationError{
				field:  "Order",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetDomainOrderData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrderRequestValidationError{
					field:  "DomainOrderData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrderRequestValidationError{
					field:  "DomainOrderData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDomainOrderData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrderRequestValidationError{
				field:  "DomainOrderData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrderRequestValidationError{
					field:  "RecurringPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrderRequestValidationError{
					field:  "RecurringPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrderRequestValidationError{
				field:  "RecurringPaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Ownership

	if all {
		switch v := interface{}(m.GetAutoCaptureTimeoutOverride()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrderRequestValidationError{
					field:  "AutoCaptureTimeoutOverride",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrderRequestValidationError{
					field:  "AutoCaptureTimeoutOverride",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAutoCaptureTimeoutOverride()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrderRequestValidationError{
				field:  "AutoCaptureTimeoutOverride",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateOrderRequestMultiError(errors)
	}

	return nil
}

// CreateOrderRequestMultiError is an error wrapping multiple validation errors
// returned by CreateOrderRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrderRequestMultiError) AllErrors() []error { return m }

// CreateOrderRequestValidationError is the validation error returned by
// CreateOrderRequest.Validate if the designated constraints aren't met.
type CreateOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrderRequestValidationError) ErrorName() string {
	return "CreateOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrderRequestValidationError{}

// Validate checks the field values on RecurringPaymentDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecurringPaymentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecurringPaymentDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecurringPaymentDetailsMultiError, or nil if none found.
func (m *RecurringPaymentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RecurringPaymentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	// no validation rules for VendorCustomerId

	if len(errors) > 0 {
		return RecurringPaymentDetailsMultiError(errors)
	}

	return nil
}

// RecurringPaymentDetailsMultiError is an error wrapping multiple validation
// errors returned by RecurringPaymentDetails.ValidateAll() if the designated
// constraints aren't met.
type RecurringPaymentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecurringPaymentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecurringPaymentDetailsMultiError) AllErrors() []error { return m }

// RecurringPaymentDetailsValidationError is the validation error returned by
// RecurringPaymentDetails.Validate if the designated constraints aren't met.
type RecurringPaymentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecurringPaymentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecurringPaymentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecurringPaymentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecurringPaymentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecurringPaymentDetailsValidationError) ErrorName() string {
	return "RecurringPaymentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e RecurringPaymentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurringPaymentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecurringPaymentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecurringPaymentDetailsValidationError{}

// Validate checks the field values on CreateOrderResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrderResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOrderResponseMultiError, or nil if none found.
func (m *CreateOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrderResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrderResponseValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrderResponseValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrderResponseValidationError{
				field:  "Order",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VendorOrderId

	// no validation rules for VendorAuthKey

	if len(errors) > 0 {
		return CreateOrderResponseMultiError(errors)
	}

	return nil
}

// CreateOrderResponseMultiError is an error wrapping multiple validation
// errors returned by CreateOrderResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrderResponseMultiError) AllErrors() []error { return m }

// CreateOrderResponseValidationError is the validation error returned by
// CreateOrderResponse.Validate if the designated constraints aren't met.
type CreateOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrderResponseValidationError) ErrorName() string {
	return "CreateOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrderResponseValidationError{}

// Validate checks the field values on RecordOffAppPaymentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordOffAppPaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordOffAppPaymentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordOffAppPaymentRequestMultiError, or nil if none found.
func (m *RecordOffAppPaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordOffAppPaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SavingsAccountId

	// no validation rules for InternalActorId

	// no validation rules for AccountType

	if all {
		switch v := interface{}(m.GetTxnDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordOffAppPaymentRequestValidationError{
					field:  "TxnDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordOffAppPaymentRequestValidationError{
					field:  "TxnDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordOffAppPaymentRequestValidationError{
				field:  "TxnDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetParsedTxnParticulars()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordOffAppPaymentRequestValidationError{
					field:  "ParsedTxnParticulars",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordOffAppPaymentRequestValidationError{
					field:  "ParsedTxnParticulars",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParsedTxnParticulars()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordOffAppPaymentRequestValidationError{
				field:  "ParsedTxnParticulars",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerBank

	// no validation rules for TransactionStatus

	if all {
		switch v := interface{}(m.GetTransactionDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordOffAppPaymentRequestValidationError{
					field:  "TransactionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordOffAppPaymentRequestValidationError{
					field:  "TransactionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordOffAppPaymentRequestValidationError{
				field:  "TransactionDetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsLastAttempt

	// no validation rules for OrderWorkflow

	if all {
		switch v := interface{}(m.GetReceivedFromVendorAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordOffAppPaymentRequestValidationError{
					field:  "ReceivedFromVendorAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordOffAppPaymentRequestValidationError{
					field:  "ReceivedFromVendorAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReceivedFromVendorAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordOffAppPaymentRequestValidationError{
				field:  "ReceivedFromVendorAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AttemptRemitterInfoBackfill

	if len(errors) > 0 {
		return RecordOffAppPaymentRequestMultiError(errors)
	}

	return nil
}

// RecordOffAppPaymentRequestMultiError is an error wrapping multiple
// validation errors returned by RecordOffAppPaymentRequest.ValidateAll() if
// the designated constraints aren't met.
type RecordOffAppPaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordOffAppPaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordOffAppPaymentRequestMultiError) AllErrors() []error { return m }

// RecordOffAppPaymentRequestValidationError is the validation error returned
// by RecordOffAppPaymentRequest.Validate if the designated constraints aren't met.
type RecordOffAppPaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordOffAppPaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordOffAppPaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordOffAppPaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordOffAppPaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordOffAppPaymentRequestValidationError) ErrorName() string {
	return "RecordOffAppPaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordOffAppPaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordOffAppPaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordOffAppPaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordOffAppPaymentRequestValidationError{}

// Validate checks the field values on RecordOffAppPaymentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordOffAppPaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordOffAppPaymentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordOffAppPaymentResponseMultiError, or nil if none found.
func (m *RecordOffAppPaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordOffAppPaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordOffAppPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordOffAppPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordOffAppPaymentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderWithTransactions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordOffAppPaymentResponseValidationError{
					field:  "OrderWithTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordOffAppPaymentResponseValidationError{
					field:  "OrderWithTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderWithTransactions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordOffAppPaymentResponseValidationError{
				field:  "OrderWithTransactions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordOffAppPaymentResponseMultiError(errors)
	}

	return nil
}

// RecordOffAppPaymentResponseMultiError is an error wrapping multiple
// validation errors returned by RecordOffAppPaymentResponse.ValidateAll() if
// the designated constraints aren't met.
type RecordOffAppPaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordOffAppPaymentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordOffAppPaymentResponseMultiError) AllErrors() []error { return m }

// RecordOffAppPaymentResponseValidationError is the validation error returned
// by RecordOffAppPaymentResponse.Validate if the designated constraints
// aren't met.
type RecordOffAppPaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordOffAppPaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordOffAppPaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordOffAppPaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordOffAppPaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordOffAppPaymentResponseValidationError) ErrorName() string {
	return "RecordOffAppPaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordOffAppPaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordOffAppPaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordOffAppPaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordOffAppPaymentResponseValidationError{}

// Validate checks the field values on CreateFundTransferOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateFundTransferOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFundTransferOrderRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateFundTransferOrderRequestMultiError, or nil if none found.
func (m *CreateFundTransferOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFundTransferOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayerActorId

	// no validation rules for PayeeActorId

	if m.GetAmount() == nil {
		err := CreateFundTransferOrderRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Provenance

	// no validation rules for Workflow

	// no validation rules for ClientRequestId

	// no validation rules for Remarks

	// no validation rules for HardPreferredPaymentProtocol

	if all {
		switch v := interface{}(m.GetPostAuthorisationAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PostAuthorisationAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PostAuthorisationAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostAuthorisationAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "PostAuthorisationAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientWorkflowSignalId

	if all {
		switch v := interface{}(m.GetClientWorkflowClientReqId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "ClientWorkflowClientReqId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "ClientWorkflowClientReqId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientWorkflowClientReqId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "ClientWorkflowClientReqId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostFundTransferDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PostFundTransferDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PostFundTransferDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostFundTransferDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "PostFundTransferDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayerIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayerIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayerIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayerIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "PayerIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayeeIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayeeIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "PayeeIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayeeIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "PayeeIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpireAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderRequestValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpireAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderRequestValidationError{
				field:  "ExpireAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UiEntryPoint

	// no validation rules for PurposeCode

	if len(errors) > 0 {
		return CreateFundTransferOrderRequestMultiError(errors)
	}

	return nil
}

// CreateFundTransferOrderRequestMultiError is an error wrapping multiple
// validation errors returned by CreateFundTransferOrderRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateFundTransferOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFundTransferOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFundTransferOrderRequestMultiError) AllErrors() []error { return m }

// CreateFundTransferOrderRequestValidationError is the validation error
// returned by CreateFundTransferOrderRequest.Validate if the designated
// constraints aren't met.
type CreateFundTransferOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFundTransferOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFundTransferOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFundTransferOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFundTransferOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFundTransferOrderRequestValidationError) ErrorName() string {
	return "CreateFundTransferOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFundTransferOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFundTransferOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFundTransferOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFundTransferOrderRequestValidationError{}

// Validate checks the field values on CreateFundTransferOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateFundTransferOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFundTransferOrderResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateFundTransferOrderResponseMultiError, or nil if none found.
func (m *CreateFundTransferOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFundTransferOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderResponseValidationError{
				field:  "Order",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTxnAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateFundTransferOrderResponseValidationError{
						field:  fmt.Sprintf("TxnAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateFundTransferOrderResponseValidationError{
						field:  fmt.Sprintf("TxnAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateFundTransferOrderResponseValidationError{
					field:  fmt.Sprintf("TxnAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateFundTransferOrderResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateFundTransferOrderResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayErrorCodeForPayer

	if len(errors) > 0 {
		return CreateFundTransferOrderResponseMultiError(errors)
	}

	return nil
}

// CreateFundTransferOrderResponseMultiError is an error wrapping multiple
// validation errors returned by CreateFundTransferOrderResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateFundTransferOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFundTransferOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFundTransferOrderResponseMultiError) AllErrors() []error { return m }

// CreateFundTransferOrderResponseValidationError is the validation error
// returned by CreateFundTransferOrderResponse.Validate if the designated
// constraints aren't met.
type CreateFundTransferOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFundTransferOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFundTransferOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFundTransferOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFundTransferOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFundTransferOrderResponseValidationError) ErrorName() string {
	return "CreateFundTransferOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFundTransferOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFundTransferOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFundTransferOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFundTransferOrderResponseValidationError{}

// Validate checks the field values on AuthoriseFundTransferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthoriseFundTransferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthoriseFundTransferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthoriseFundTransferRequestMultiError, or nil if none found.
func (m *AuthoriseFundTransferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseFundTransferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPaymentAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseFundTransferRequestValidationError{
					field:  "PaymentAuth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseFundTransferRequestValidationError{
					field:  "PaymentAuth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseFundTransferRequestValidationError{
				field:  "PaymentAuth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	// no validation rules for CurrentActorId

	if all {
		switch v := interface{}(m.GetPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseFundTransferRequestValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseFundTransferRequestValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseFundTransferRequestValidationError{
				field:  "PaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseFundTransferRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseFundTransferRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseFundTransferRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseFundTransferRequestMultiError(errors)
	}

	return nil
}

// AuthoriseFundTransferRequestMultiError is an error wrapping multiple
// validation errors returned by AuthoriseFundTransferRequest.ValidateAll() if
// the designated constraints aren't met.
type AuthoriseFundTransferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseFundTransferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseFundTransferRequestMultiError) AllErrors() []error { return m }

// AuthoriseFundTransferRequestValidationError is the validation error returned
// by AuthoriseFundTransferRequest.Validate if the designated constraints
// aren't met.
type AuthoriseFundTransferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseFundTransferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseFundTransferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseFundTransferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseFundTransferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseFundTransferRequestValidationError) ErrorName() string {
	return "AuthoriseFundTransferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseFundTransferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseFundTransferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseFundTransferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseFundTransferRequestValidationError{}

// Validate checks the field values on AuthoriseFundTransferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthoriseFundTransferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthoriseFundTransferResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AuthoriseFundTransferResponseMultiError, or nil if none found.
func (m *AuthoriseFundTransferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseFundTransferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseFundTransferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseFundTransferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseFundTransferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostAuthoriseDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseFundTransferResponseValidationError{
					field:  "PostAuthoriseDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseFundTransferResponseValidationError{
					field:  "PostAuthoriseDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostAuthoriseDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseFundTransferResponseValidationError{
				field:  "PostAuthoriseDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseFundTransferResponseMultiError(errors)
	}

	return nil
}

// AuthoriseFundTransferResponseMultiError is an error wrapping multiple
// validation errors returned by AuthoriseFundTransferResponse.ValidateAll()
// if the designated constraints aren't met.
type AuthoriseFundTransferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseFundTransferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseFundTransferResponseMultiError) AllErrors() []error { return m }

// AuthoriseFundTransferResponseValidationError is the validation error
// returned by AuthoriseFundTransferResponse.Validate if the designated
// constraints aren't met.
type AuthoriseFundTransferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseFundTransferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseFundTransferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseFundTransferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseFundTransferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseFundTransferResponseValidationError) ErrorName() string {
	return "AuthoriseFundTransferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseFundTransferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseFundTransferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseFundTransferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseFundTransferResponseValidationError{}

// Validate checks the field values on GetFundTransferAttributesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFundTransferAttributesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundTransferAttributesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFundTransferAttributesRequestMultiError, or nil if none found.
func (m *GetFundTransferAttributesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundTransferAttributesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayerActorId

	// no validation rules for PayeeActorId

	if m.GetAmount() == nil {
		err := GetFundTransferAttributesRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundTransferAttributesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundTransferAttributesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundTransferAttributesRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HardPreferredPaymentProtocol

	if all {
		switch v := interface{}(m.GetPayerIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundTransferAttributesRequestValidationError{
					field:  "PayerIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundTransferAttributesRequestValidationError{
					field:  "PayerIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayerIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundTransferAttributesRequestValidationError{
				field:  "PayerIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayeeIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundTransferAttributesRequestValidationError{
					field:  "PayeeIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundTransferAttributesRequestValidationError{
					field:  "PayeeIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayeeIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundTransferAttributesRequestValidationError{
				field:  "PayeeIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFundTransferAttributesRequestMultiError(errors)
	}

	return nil
}

// GetFundTransferAttributesRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetFundTransferAttributesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFundTransferAttributesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundTransferAttributesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundTransferAttributesRequestMultiError) AllErrors() []error { return m }

// GetFundTransferAttributesRequestValidationError is the validation error
// returned by GetFundTransferAttributesRequest.Validate if the designated
// constraints aren't met.
type GetFundTransferAttributesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundTransferAttributesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundTransferAttributesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundTransferAttributesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundTransferAttributesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundTransferAttributesRequestValidationError) ErrorName() string {
	return "GetFundTransferAttributesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundTransferAttributesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundTransferAttributesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundTransferAttributesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundTransferAttributesRequestValidationError{}

// Validate checks the field values on GetFundTransferAttributesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFundTransferAttributesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundTransferAttributesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFundTransferAttributesResponseMultiError, or nil if none found.
func (m *GetFundTransferAttributesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundTransferAttributesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundTransferAttributesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundTransferAttributesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundTransferAttributesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTxnAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFundTransferAttributesResponseValidationError{
						field:  fmt.Sprintf("TxnAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFundTransferAttributesResponseValidationError{
						field:  fmt.Sprintf("TxnAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFundTransferAttributesResponseValidationError{
					field:  fmt.Sprintf("TxnAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PayErrorCodeForPayer

	if len(errors) > 0 {
		return GetFundTransferAttributesResponseMultiError(errors)
	}

	return nil
}

// GetFundTransferAttributesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetFundTransferAttributesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFundTransferAttributesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundTransferAttributesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundTransferAttributesResponseMultiError) AllErrors() []error { return m }

// GetFundTransferAttributesResponseValidationError is the validation error
// returned by GetFundTransferAttributesResponse.Validate if the designated
// constraints aren't met.
type GetFundTransferAttributesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundTransferAttributesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundTransferAttributesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundTransferAttributesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundTransferAttributesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundTransferAttributesResponseValidationError) ErrorName() string {
	return "GetFundTransferAttributesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundTransferAttributesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundTransferAttributesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundTransferAttributesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundTransferAttributesResponseValidationError{}

// Validate checks the field values on MakeB2CFundTransferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MakeB2CFundTransferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MakeB2CFundTransferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MakeB2CFundTransferRequestMultiError, or nil if none found.
func (m *MakeB2CFundTransferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MakeB2CFundTransferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetPayerActorId()); l < 4 || l > 35 {
		err := MakeB2CFundTransferRequestValidationError{
			field:  "PayerActorId",
			reason: "value length must be between 4 and 35 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetPayeeActorId()); l < 4 || l > 100 {
		err := MakeB2CFundTransferRequestValidationError{
			field:  "PayeeActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAmount() == nil {
		err := MakeB2CFundTransferRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MakeB2CFundTransferRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MakeB2CFundTransferRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MakeB2CFundTransferRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Provenance

	if m.GetClientRequestId() == nil {
		err := MakeB2CFundTransferRequestValidationError{
			field:  "ClientRequestId",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetClientRequestId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MakeB2CFundTransferRequestValidationError{
					field:  "ClientRequestId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MakeB2CFundTransferRequestValidationError{
					field:  "ClientRequestId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientRequestId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MakeB2CFundTransferRequestValidationError{
				field:  "ClientRequestId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UiEntryPoint

	if utf8.RuneCountInString(m.GetPiFrom()) < 1 {
		err := MakeB2CFundTransferRequestValidationError{
			field:  "PiFrom",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPiTo()) < 1 {
		err := MakeB2CFundTransferRequestValidationError{
			field:  "PiTo",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Remarks

	// no validation rules for PreferredProtocol

	// no validation rules for Partner

	if all {
		switch v := interface{}(m.GetPostPaymentDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MakeB2CFundTransferRequestValidationError{
					field:  "PostPaymentDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MakeB2CFundTransferRequestValidationError{
					field:  "PostPaymentDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostPaymentDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MakeB2CFundTransferRequestValidationError{
				field:  "PostPaymentDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntityOwnership

	// no validation rules for RequestSource

	// no validation rules for ClaimedAccountName

	if len(errors) > 0 {
		return MakeB2CFundTransferRequestMultiError(errors)
	}

	return nil
}

// MakeB2CFundTransferRequestMultiError is an error wrapping multiple
// validation errors returned by MakeB2CFundTransferRequest.ValidateAll() if
// the designated constraints aren't met.
type MakeB2CFundTransferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MakeB2CFundTransferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MakeB2CFundTransferRequestMultiError) AllErrors() []error { return m }

// MakeB2CFundTransferRequestValidationError is the validation error returned
// by MakeB2CFundTransferRequest.Validate if the designated constraints aren't met.
type MakeB2CFundTransferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MakeB2CFundTransferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MakeB2CFundTransferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MakeB2CFundTransferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MakeB2CFundTransferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MakeB2CFundTransferRequestValidationError) ErrorName() string {
	return "MakeB2CFundTransferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MakeB2CFundTransferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMakeB2CFundTransferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MakeB2CFundTransferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MakeB2CFundTransferRequestValidationError{}

// Validate checks the field values on MakeB2CFundTransferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MakeB2CFundTransferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MakeB2CFundTransferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MakeB2CFundTransferResponseMultiError, or nil if none found.
func (m *MakeB2CFundTransferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MakeB2CFundTransferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MakeB2CFundTransferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MakeB2CFundTransferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MakeB2CFundTransferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MakeB2CFundTransferResponseMultiError(errors)
	}

	return nil
}

// MakeB2CFundTransferResponseMultiError is an error wrapping multiple
// validation errors returned by MakeB2CFundTransferResponse.ValidateAll() if
// the designated constraints aren't met.
type MakeB2CFundTransferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MakeB2CFundTransferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MakeB2CFundTransferResponseMultiError) AllErrors() []error { return m }

// MakeB2CFundTransferResponseValidationError is the validation error returned
// by MakeB2CFundTransferResponse.Validate if the designated constraints
// aren't met.
type MakeB2CFundTransferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MakeB2CFundTransferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MakeB2CFundTransferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MakeB2CFundTransferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MakeB2CFundTransferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MakeB2CFundTransferResponseValidationError) ErrorName() string {
	return "MakeB2CFundTransferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MakeB2CFundTransferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMakeB2CFundTransferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MakeB2CFundTransferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MakeB2CFundTransferResponseValidationError{}

// Validate checks the field values on GetFundTransferStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundTransferStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundTransferStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFundTransferStatusRequestMultiError, or nil if none found.
func (m *GetFundTransferStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundTransferStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	switch v := m.Identifier.(type) {
	case *GetFundTransferStatusRequest_ClientReqId:
		if v == nil {
			err := GetFundTransferStatusRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetClientReqId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFundTransferStatusRequestValidationError{
						field:  "ClientReqId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFundTransferStatusRequestValidationError{
						field:  "ClientReqId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetClientReqId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFundTransferStatusRequestValidationError{
					field:  "ClientReqId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetFundTransferStatusRequestMultiError(errors)
	}

	return nil
}

// GetFundTransferStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetFundTransferStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type GetFundTransferStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundTransferStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundTransferStatusRequestMultiError) AllErrors() []error { return m }

// GetFundTransferStatusRequestValidationError is the validation error returned
// by GetFundTransferStatusRequest.Validate if the designated constraints
// aren't met.
type GetFundTransferStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundTransferStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundTransferStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundTransferStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundTransferStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundTransferStatusRequestValidationError) ErrorName() string {
	return "GetFundTransferStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundTransferStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundTransferStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundTransferStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundTransferStatusRequestValidationError{}

// Validate checks the field values on GetFundTransferStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFundTransferStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFundTransferStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFundTransferStatusResponseMultiError, or nil if none found.
func (m *GetFundTransferStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFundTransferStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundTransferStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundTransferStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundTransferStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SubStatus

	// no validation rules for PayErrorCode

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFundTransferStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFundTransferStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFundTransferStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFundTransferStatusResponseMultiError(errors)
	}

	return nil
}

// GetFundTransferStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetFundTransferStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type GetFundTransferStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFundTransferStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFundTransferStatusResponseMultiError) AllErrors() []error { return m }

// GetFundTransferStatusResponseValidationError is the validation error
// returned by GetFundTransferStatusResponse.Validate if the designated
// constraints aren't met.
type GetFundTransferStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFundTransferStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFundTransferStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFundTransferStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFundTransferStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFundTransferStatusResponseValidationError) ErrorName() string {
	return "GetFundTransferStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFundTransferStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFundTransferStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFundTransferStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFundTransferStatusResponseValidationError{}

// Validate checks the field values on GetTotalTransactionAmountRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTotalTransactionAmountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTotalTransactionAmountRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTotalTransactionAmountRequestMultiError, or nil if none found.
func (m *GetTotalTransactionAmountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTotalTransactionAmountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetTotalTransactionAmountRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTotalTransactionAmountRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTotalTransactionAmountRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTotalTransactionAmountRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTotalTransactionAmountRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTotalTransactionAmountRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTotalTransactionAmountRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionType

	if len(errors) > 0 {
		return GetTotalTransactionAmountRequestMultiError(errors)
	}

	return nil
}

// GetTotalTransactionAmountRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetTotalTransactionAmountRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTotalTransactionAmountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTotalTransactionAmountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTotalTransactionAmountRequestMultiError) AllErrors() []error { return m }

// GetTotalTransactionAmountRequestValidationError is the validation error
// returned by GetTotalTransactionAmountRequest.Validate if the designated
// constraints aren't met.
type GetTotalTransactionAmountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTotalTransactionAmountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTotalTransactionAmountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTotalTransactionAmountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTotalTransactionAmountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTotalTransactionAmountRequestValidationError) ErrorName() string {
	return "GetTotalTransactionAmountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTotalTransactionAmountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTotalTransactionAmountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTotalTransactionAmountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTotalTransactionAmountRequestValidationError{}

// Validate checks the field values on GetTotalTransactionAmountResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTotalTransactionAmountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTotalTransactionAmountResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTotalTransactionAmountResponseMultiError, or nil if none found.
func (m *GetTotalTransactionAmountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTotalTransactionAmountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTotalTransactionAmountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTotalTransactionAmountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTotalTransactionAmountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTotalTransactionAmountResponseValidationError{
					field:  "TotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTotalTransactionAmountResponseValidationError{
					field:  "TotalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTotalTransactionAmountResponseValidationError{
				field:  "TotalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTotalTransactionAmountResponseMultiError(errors)
	}

	return nil
}

// GetTotalTransactionAmountResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTotalTransactionAmountResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTotalTransactionAmountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTotalTransactionAmountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTotalTransactionAmountResponseMultiError) AllErrors() []error { return m }

// GetTotalTransactionAmountResponseValidationError is the validation error
// returned by GetTotalTransactionAmountResponse.Validate if the designated
// constraints aren't met.
type GetTotalTransactionAmountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTotalTransactionAmountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTotalTransactionAmountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTotalTransactionAmountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTotalTransactionAmountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTotalTransactionAmountResponseValidationError) ErrorName() string {
	return "GetTotalTransactionAmountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTotalTransactionAmountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTotalTransactionAmountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTotalTransactionAmountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTotalTransactionAmountResponseValidationError{}

// Validate checks the field values on GetEligibleAccountsForPaymentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetEligibleAccountsForPaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEligibleAccountsForPaymentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetEligibleAccountsForPaymentRequestMultiError, or nil if none found.
func (m *GetEligibleAccountsForPaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEligibleAccountsForPaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for SecondActorId

	// no validation rules for PaymentScope

	// no validation rules for EligibleAccountsUiEntryPoint

	// no validation rules for PayeePiId

	if len(errors) > 0 {
		return GetEligibleAccountsForPaymentRequestMultiError(errors)
	}

	return nil
}

// GetEligibleAccountsForPaymentRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetEligibleAccountsForPaymentRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEligibleAccountsForPaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEligibleAccountsForPaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEligibleAccountsForPaymentRequestMultiError) AllErrors() []error { return m }

// GetEligibleAccountsForPaymentRequestValidationError is the validation error
// returned by GetEligibleAccountsForPaymentRequest.Validate if the designated
// constraints aren't met.
type GetEligibleAccountsForPaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEligibleAccountsForPaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEligibleAccountsForPaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEligibleAccountsForPaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEligibleAccountsForPaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEligibleAccountsForPaymentRequestValidationError) ErrorName() string {
	return "GetEligibleAccountsForPaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEligibleAccountsForPaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEligibleAccountsForPaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEligibleAccountsForPaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEligibleAccountsForPaymentRequestValidationError{}

// Validate checks the field values on GetEligibleAccountsForPaymentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetEligibleAccountsForPaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEligibleAccountsForPaymentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetEligibleAccountsForPaymentResponseMultiError, or nil if none found.
func (m *GetEligibleAccountsForPaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEligibleAccountsForPaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEligibleAccountsForPaymentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEligibleAccountsForPaymentResponseValidationError{
						field:  fmt.Sprintf("AccountInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEligibleAccountsForPaymentResponseValidationError{
						field:  fmt.Sprintf("AccountInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEligibleAccountsForPaymentResponseValidationError{
					field:  fmt.Sprintf("AccountInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetEligibleAccountsForPaymentResponseMultiError(errors)
	}

	return nil
}

// GetEligibleAccountsForPaymentResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetEligibleAccountsForPaymentResponse.ValidateAll() if the designated
// constraints aren't met.
type GetEligibleAccountsForPaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEligibleAccountsForPaymentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEligibleAccountsForPaymentResponseMultiError) AllErrors() []error { return m }

// GetEligibleAccountsForPaymentResponseValidationError is the validation error
// returned by GetEligibleAccountsForPaymentResponse.Validate if the
// designated constraints aren't met.
type GetEligibleAccountsForPaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEligibleAccountsForPaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEligibleAccountsForPaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEligibleAccountsForPaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEligibleAccountsForPaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEligibleAccountsForPaymentResponseValidationError) ErrorName() string {
	return "GetEligibleAccountsForPaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEligibleAccountsForPaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEligibleAccountsForPaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEligibleAccountsForPaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEligibleAccountsForPaymentResponseValidationError{}

// Validate checks the field values on UserIdentifier with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserIdentifier with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserIdentifierMultiError,
// or nil if none found.
func (m *UserIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *UserIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AccountId

	// no validation rules for AccountType

	// no validation rules for PiId

	// no validation rules for ActorName

	if len(errors) > 0 {
		return UserIdentifierMultiError(errors)
	}

	return nil
}

// UserIdentifierMultiError is an error wrapping multiple validation errors
// returned by UserIdentifier.ValidateAll() if the designated constraints
// aren't met.
type UserIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserIdentifierMultiError) AllErrors() []error { return m }

// UserIdentifierValidationError is the validation error returned by
// UserIdentifier.Validate if the designated constraints aren't met.
type UserIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserIdentifierValidationError) ErrorName() string { return "UserIdentifierValidationError" }

// Error satisfies the builtin error interface
func (e UserIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserIdentifierValidationError{}

// Validate checks the field values on GetTransactionAggregatesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionAggregatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionAggregatesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTransactionAggregatesRequestMultiError, or nil if none found.
func (m *GetTransactionAggregatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionAggregatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AccountingEntryType

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionAggregatesRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionAggregatesRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionAggregatesRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionAggregatesRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionAggregatesRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionAggregatesRequestValidationError{
				field:  "ToTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmountFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionAggregatesRequestValidationError{
					field:  "AmountFilter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionAggregatesRequestValidationError{
					field:  "AmountFilter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionAggregatesRequestValidationError{
				field:  "AmountFilter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DataFreshness

	if len(errors) > 0 {
		return GetTransactionAggregatesRequestMultiError(errors)
	}

	return nil
}

// GetTransactionAggregatesRequestMultiError is an error wrapping multiple
// validation errors returned by GetTransactionAggregatesRequest.ValidateAll()
// if the designated constraints aren't met.
type GetTransactionAggregatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionAggregatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionAggregatesRequestMultiError) AllErrors() []error { return m }

// GetTransactionAggregatesRequestValidationError is the validation error
// returned by GetTransactionAggregatesRequest.Validate if the designated
// constraints aren't met.
type GetTransactionAggregatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionAggregatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionAggregatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionAggregatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionAggregatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionAggregatesRequestValidationError) ErrorName() string {
	return "GetTransactionAggregatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionAggregatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionAggregatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionAggregatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionAggregatesRequestValidationError{}

// Validate checks the field values on GetTransactionAggregatesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTransactionAggregatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionAggregatesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTransactionAggregatesResponseMultiError, or nil if none found.
func (m *GetTransactionAggregatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionAggregatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionAggregatesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransactionAggregates()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionAggregatesResponseValidationError{
					field:  "TransactionAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionAggregatesResponseValidationError{
					field:  "TransactionAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAggregates()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionAggregatesResponseValidationError{
				field:  "TransactionAggregates",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTransactionAggregatesResponseMultiError(errors)
	}

	return nil
}

// GetTransactionAggregatesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTransactionAggregatesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionAggregatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionAggregatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionAggregatesResponseMultiError) AllErrors() []error { return m }

// GetTransactionAggregatesResponseValidationError is the validation error
// returned by GetTransactionAggregatesResponse.Validate if the designated
// constraints aren't met.
type GetTransactionAggregatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionAggregatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionAggregatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionAggregatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionAggregatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionAggregatesResponseValidationError) ErrorName() string {
	return "GetTransactionAggregatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionAggregatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionAggregatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionAggregatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionAggregatesResponseValidationError{}

// Validate checks the field values on TransactionAggregates with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransactionAggregates) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionAggregates with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionAggregatesMultiError, or nil if none found.
func (m *TransactionAggregates) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionAggregates) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSumAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionAggregatesValidationError{
					field:  "SumAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionAggregatesValidationError{
					field:  "SumAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSumAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionAggregatesValidationError{
				field:  "SumAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Count

	if len(errors) > 0 {
		return TransactionAggregatesMultiError(errors)
	}

	return nil
}

// TransactionAggregatesMultiError is an error wrapping multiple validation
// errors returned by TransactionAggregates.ValidateAll() if the designated
// constraints aren't met.
type TransactionAggregatesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionAggregatesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionAggregatesMultiError) AllErrors() []error { return m }

// TransactionAggregatesValidationError is the validation error returned by
// TransactionAggregates.Validate if the designated constraints aren't met.
type TransactionAggregatesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionAggregatesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionAggregatesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionAggregatesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionAggregatesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionAggregatesValidationError) ErrorName() string {
	return "TransactionAggregatesValidationError"
}

// Error satisfies the builtin error interface
func (e TransactionAggregatesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionAggregates.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionAggregatesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionAggregatesValidationError{}

// Validate checks the field values on GetEligibleAccountsForPaymentRequestV1
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetEligibleAccountsForPaymentRequestV1) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetEligibleAccountsForPaymentRequestV1 with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetEligibleAccountsForPaymentRequestV1MultiError, or nil if none found.
func (m *GetEligibleAccountsForPaymentRequestV1) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEligibleAccountsForPaymentRequestV1) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetEligibleAccountsForPaymentRequestV1MultiError(errors)
	}

	return nil
}

// GetEligibleAccountsForPaymentRequestV1MultiError is an error wrapping
// multiple validation errors returned by
// GetEligibleAccountsForPaymentRequestV1.ValidateAll() if the designated
// constraints aren't met.
type GetEligibleAccountsForPaymentRequestV1MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEligibleAccountsForPaymentRequestV1MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEligibleAccountsForPaymentRequestV1MultiError) AllErrors() []error { return m }

// GetEligibleAccountsForPaymentRequestV1ValidationError is the validation
// error returned by GetEligibleAccountsForPaymentRequestV1.Validate if the
// designated constraints aren't met.
type GetEligibleAccountsForPaymentRequestV1ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEligibleAccountsForPaymentRequestV1ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEligibleAccountsForPaymentRequestV1ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEligibleAccountsForPaymentRequestV1ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEligibleAccountsForPaymentRequestV1ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEligibleAccountsForPaymentRequestV1ValidationError) ErrorName() string {
	return "GetEligibleAccountsForPaymentRequestV1ValidationError"
}

// Error satisfies the builtin error interface
func (e GetEligibleAccountsForPaymentRequestV1ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEligibleAccountsForPaymentRequestV1.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEligibleAccountsForPaymentRequestV1ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEligibleAccountsForPaymentRequestV1ValidationError{}

// Validate checks the field values on GetEligibleAccountsForPaymentResponseV1
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetEligibleAccountsForPaymentResponseV1) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetEligibleAccountsForPaymentResponseV1 with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetEligibleAccountsForPaymentResponseV1MultiError, or nil if none found.
func (m *GetEligibleAccountsForPaymentResponseV1) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEligibleAccountsForPaymentResponseV1) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentResponseV1ValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEligibleAccountsForPaymentResponseV1ValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEligibleAccountsForPaymentResponseV1ValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEligibleAccountsForPaymentResponseV1ValidationError{
						field:  fmt.Sprintf("AccountInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEligibleAccountsForPaymentResponseV1ValidationError{
						field:  fmt.Sprintf("AccountInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEligibleAccountsForPaymentResponseV1ValidationError{
					field:  fmt.Sprintf("AccountInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetEligibleAccountsForPaymentResponseV1MultiError(errors)
	}

	return nil
}

// GetEligibleAccountsForPaymentResponseV1MultiError is an error wrapping
// multiple validation errors returned by
// GetEligibleAccountsForPaymentResponseV1.ValidateAll() if the designated
// constraints aren't met.
type GetEligibleAccountsForPaymentResponseV1MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEligibleAccountsForPaymentResponseV1MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEligibleAccountsForPaymentResponseV1MultiError) AllErrors() []error { return m }

// GetEligibleAccountsForPaymentResponseV1ValidationError is the validation
// error returned by GetEligibleAccountsForPaymentResponseV1.Validate if the
// designated constraints aren't met.
type GetEligibleAccountsForPaymentResponseV1ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEligibleAccountsForPaymentResponseV1ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEligibleAccountsForPaymentResponseV1ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEligibleAccountsForPaymentResponseV1ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEligibleAccountsForPaymentResponseV1ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEligibleAccountsForPaymentResponseV1ValidationError) ErrorName() string {
	return "GetEligibleAccountsForPaymentResponseV1ValidationError"
}

// Error satisfies the builtin error interface
func (e GetEligibleAccountsForPaymentResponseV1ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEligibleAccountsForPaymentResponseV1.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEligibleAccountsForPaymentResponseV1ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEligibleAccountsForPaymentResponseV1ValidationError{}

// Validate checks the field values on GetOrdersWithTransactionsForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetOrdersWithTransactionsForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetOrdersWithTransactionsForActorRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetOrdersWithTransactionsForActorRequestMultiError, or nil if none found.
func (m *GetOrdersWithTransactionsForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrdersWithTransactionsForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Status

	// no validation rules for TransactionType

	for idx, item := range m.GetOrderStatusAndWorkflowFilter() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrdersWithTransactionsForActorRequestValidationError{
						field:  fmt.Sprintf("OrderStatusAndWorkflowFilter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrdersWithTransactionsForActorRequestValidationError{
						field:  fmt.Sprintf("OrderStatusAndWorkflowFilter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrdersWithTransactionsForActorRequestValidationError{
					field:  fmt.Sprintf("OrderStatusAndWorkflowFilter[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContextRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersWithTransactionsForActorRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersWithTransactionsForActorRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersWithTransactionsForActorRequestValidationError{
				field:  "PageContextRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersWithTransactionsForActorRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersWithTransactionsForActorRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersWithTransactionsForActorRequestValidationError{
				field:  "ToTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountFilter() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrdersWithTransactionsForActorRequestValidationError{
						field:  fmt.Sprintf("AccountFilter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrdersWithTransactionsForActorRequestValidationError{
						field:  fmt.Sprintf("AccountFilter[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrdersWithTransactionsForActorRequestValidationError{
					field:  fmt.Sprintf("AccountFilter[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetOrdersWithTransactionsForActorRequestMultiError(errors)
	}

	return nil
}

// GetOrdersWithTransactionsForActorRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetOrdersWithTransactionsForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOrdersWithTransactionsForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrdersWithTransactionsForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrdersWithTransactionsForActorRequestMultiError) AllErrors() []error { return m }

// GetOrdersWithTransactionsForActorRequestValidationError is the validation
// error returned by GetOrdersWithTransactionsForActorRequest.Validate if the
// designated constraints aren't met.
type GetOrdersWithTransactionsForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrdersWithTransactionsForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrdersWithTransactionsForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrdersWithTransactionsForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrdersWithTransactionsForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrdersWithTransactionsForActorRequestValidationError) ErrorName() string {
	return "GetOrdersWithTransactionsForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrdersWithTransactionsForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrdersWithTransactionsForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrdersWithTransactionsForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrdersWithTransactionsForActorRequestValidationError{}

// Validate checks the field values on
// GetOrdersWithTransactionsForActorResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetOrdersWithTransactionsForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetOrdersWithTransactionsForActorResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetOrdersWithTransactionsForActorResponseMultiError, or nil if none found.
func (m *GetOrdersWithTransactionsForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrdersWithTransactionsForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersWithTransactionsForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersWithTransactionsForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersWithTransactionsForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOrderWithTransaction() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrdersWithTransactionsForActorResponseValidationError{
						field:  fmt.Sprintf("OrderWithTransaction[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrdersWithTransactionsForActorResponseValidationError{
						field:  fmt.Sprintf("OrderWithTransaction[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrdersWithTransactionsForActorResponseValidationError{
					field:  fmt.Sprintf("OrderWithTransaction[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContextResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrdersWithTransactionsForActorResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrdersWithTransactionsForActorResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrdersWithTransactionsForActorResponseValidationError{
				field:  "PageContextResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrdersWithTransactionsForActorResponseMultiError(errors)
	}

	return nil
}

// GetOrdersWithTransactionsForActorResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetOrdersWithTransactionsForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOrdersWithTransactionsForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrdersWithTransactionsForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrdersWithTransactionsForActorResponseMultiError) AllErrors() []error { return m }

// GetOrdersWithTransactionsForActorResponseValidationError is the validation
// error returned by GetOrdersWithTransactionsForActorResponse.Validate if the
// designated constraints aren't met.
type GetOrdersWithTransactionsForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrdersWithTransactionsForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrdersWithTransactionsForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrdersWithTransactionsForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrdersWithTransactionsForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrdersWithTransactionsForActorResponseValidationError) ErrorName() string {
	return "GetOrdersWithTransactionsForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrdersWithTransactionsForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrdersWithTransactionsForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrdersWithTransactionsForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrdersWithTransactionsForActorResponseValidationError{}

// Validate checks the field values on BatchUpdateOrderWithTransactionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BatchUpdateOrderWithTransactionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BatchUpdateOrderWithTransactionsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// BatchUpdateOrderWithTransactionsRequestMultiError, or nil if none found.
func (m *BatchUpdateOrderWithTransactionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchUpdateOrderWithTransactionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetUpdateOrderTxnRequests()) > 50 {
		err := BatchUpdateOrderWithTransactionsRequestValidationError{
			field:  "UpdateOrderTxnRequests",
			reason: "value must contain no more than 50 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetUpdateOrderTxnRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchUpdateOrderWithTransactionsRequestValidationError{
						field:  fmt.Sprintf("UpdateOrderTxnRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchUpdateOrderWithTransactionsRequestValidationError{
						field:  fmt.Sprintf("UpdateOrderTxnRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchUpdateOrderWithTransactionsRequestValidationError{
					field:  fmt.Sprintf("UpdateOrderTxnRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchUpdateOrderWithTransactionsRequestMultiError(errors)
	}

	return nil
}

// BatchUpdateOrderWithTransactionsRequestMultiError is an error wrapping
// multiple validation errors returned by
// BatchUpdateOrderWithTransactionsRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchUpdateOrderWithTransactionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchUpdateOrderWithTransactionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchUpdateOrderWithTransactionsRequestMultiError) AllErrors() []error { return m }

// BatchUpdateOrderWithTransactionsRequestValidationError is the validation
// error returned by BatchUpdateOrderWithTransactionsRequest.Validate if the
// designated constraints aren't met.
type BatchUpdateOrderWithTransactionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchUpdateOrderWithTransactionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchUpdateOrderWithTransactionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchUpdateOrderWithTransactionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchUpdateOrderWithTransactionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchUpdateOrderWithTransactionsRequestValidationError) ErrorName() string {
	return "BatchUpdateOrderWithTransactionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchUpdateOrderWithTransactionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchUpdateOrderWithTransactionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchUpdateOrderWithTransactionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchUpdateOrderWithTransactionsRequestValidationError{}

// Validate checks the field values on BatchUpdateOrderWithTransactionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BatchUpdateOrderWithTransactionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BatchUpdateOrderWithTransactionsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// BatchUpdateOrderWithTransactionsResponseMultiError, or nil if none found.
func (m *BatchUpdateOrderWithTransactionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchUpdateOrderWithTransactionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchUpdateOrderWithTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchUpdateOrderWithTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchUpdateOrderWithTransactionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BatchUpdateOrderWithTransactionsResponseMultiError(errors)
	}

	return nil
}

// BatchUpdateOrderWithTransactionsResponseMultiError is an error wrapping
// multiple validation errors returned by
// BatchUpdateOrderWithTransactionsResponse.ValidateAll() if the designated
// constraints aren't met.
type BatchUpdateOrderWithTransactionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchUpdateOrderWithTransactionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchUpdateOrderWithTransactionsResponseMultiError) AllErrors() []error { return m }

// BatchUpdateOrderWithTransactionsResponseValidationError is the validation
// error returned by BatchUpdateOrderWithTransactionsResponse.Validate if the
// designated constraints aren't met.
type BatchUpdateOrderWithTransactionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchUpdateOrderWithTransactionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchUpdateOrderWithTransactionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchUpdateOrderWithTransactionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchUpdateOrderWithTransactionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchUpdateOrderWithTransactionsResponseValidationError) ErrorName() string {
	return "BatchUpdateOrderWithTransactionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchUpdateOrderWithTransactionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchUpdateOrderWithTransactionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchUpdateOrderWithTransactionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchUpdateOrderWithTransactionsResponseValidationError{}

// Validate checks the field values on OrderWithFieldMasks with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrderWithFieldMasks) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrderWithFieldMasks with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrderWithFieldMasksMultiError, or nil if none found.
func (m *OrderWithFieldMasks) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderWithFieldMasks) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderWithFieldMasksValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderWithFieldMasksValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderWithFieldMasksValidationError{
				field:  "Order",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OrderWithFieldMasksMultiError(errors)
	}

	return nil
}

// OrderWithFieldMasksMultiError is an error wrapping multiple validation
// errors returned by OrderWithFieldMasks.ValidateAll() if the designated
// constraints aren't met.
type OrderWithFieldMasksMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderWithFieldMasksMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderWithFieldMasksMultiError) AllErrors() []error { return m }

// OrderWithFieldMasksValidationError is the validation error returned by
// OrderWithFieldMasks.Validate if the designated constraints aren't met.
type OrderWithFieldMasksValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderWithFieldMasksValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderWithFieldMasksValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderWithFieldMasksValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderWithFieldMasksValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderWithFieldMasksValidationError) ErrorName() string {
	return "OrderWithFieldMasksValidationError"
}

// Error satisfies the builtin error interface
func (e OrderWithFieldMasksValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderWithFieldMasks.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderWithFieldMasksValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderWithFieldMasksValidationError{}

// Validate checks the field values on TransactionWithFieldMasks with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransactionWithFieldMasks) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionWithFieldMasks with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionWithFieldMasksMultiError, or nil if none found.
func (m *TransactionWithFieldMasks) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionWithFieldMasks) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionWithFieldMasksValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionWithFieldMasksValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionWithFieldMasksValidationError{
				field:  "Transaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TransactionWithFieldMasksMultiError(errors)
	}

	return nil
}

// TransactionWithFieldMasksMultiError is an error wrapping multiple validation
// errors returned by TransactionWithFieldMasks.ValidateAll() if the
// designated constraints aren't met.
type TransactionWithFieldMasksMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionWithFieldMasksMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionWithFieldMasksMultiError) AllErrors() []error { return m }

// TransactionWithFieldMasksValidationError is the validation error returned by
// TransactionWithFieldMasks.Validate if the designated constraints aren't met.
type TransactionWithFieldMasksValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionWithFieldMasksValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionWithFieldMasksValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionWithFieldMasksValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionWithFieldMasksValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionWithFieldMasksValidationError) ErrorName() string {
	return "TransactionWithFieldMasksValidationError"
}

// Error satisfies the builtin error interface
func (e TransactionWithFieldMasksValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionWithFieldMasks.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionWithFieldMasksValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionWithFieldMasksValidationError{}

// Validate checks the field values on GetBulkOrdersAndTransactionsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetBulkOrdersAndTransactionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBulkOrdersAndTransactionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetBulkOrdersAndTransactionsRequestMultiError, or nil if none found.
func (m *GetBulkOrdersAndTransactionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBulkOrdersAndTransactionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTransactionWithOrder() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBulkOrdersAndTransactionsRequestValidationError{
						field:  fmt.Sprintf("TransactionWithOrder[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBulkOrdersAndTransactionsRequestValidationError{
						field:  fmt.Sprintf("TransactionWithOrder[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBulkOrdersAndTransactionsRequestValidationError{
					field:  fmt.Sprintf("TransactionWithOrder[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetBulkOrdersAndTransactionsRequestMultiError(errors)
	}

	return nil
}

// GetBulkOrdersAndTransactionsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetBulkOrdersAndTransactionsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetBulkOrdersAndTransactionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBulkOrdersAndTransactionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBulkOrdersAndTransactionsRequestMultiError) AllErrors() []error { return m }

// GetBulkOrdersAndTransactionsRequestValidationError is the validation error
// returned by GetBulkOrdersAndTransactionsRequest.Validate if the designated
// constraints aren't met.
type GetBulkOrdersAndTransactionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBulkOrdersAndTransactionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBulkOrdersAndTransactionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBulkOrdersAndTransactionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBulkOrdersAndTransactionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBulkOrdersAndTransactionsRequestValidationError) ErrorName() string {
	return "GetBulkOrdersAndTransactionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBulkOrdersAndTransactionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBulkOrdersAndTransactionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBulkOrdersAndTransactionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBulkOrdersAndTransactionsRequestValidationError{}

// Validate checks the field values on GetBulkOrdersAndTransactionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetBulkOrdersAndTransactionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBulkOrdersAndTransactionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetBulkOrdersAndTransactionsResponseMultiError, or nil if none found.
func (m *GetBulkOrdersAndTransactionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBulkOrdersAndTransactionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBulkOrdersAndTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBulkOrdersAndTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBulkOrdersAndTransactionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOrderWithTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBulkOrdersAndTransactionsResponseValidationError{
						field:  fmt.Sprintf("OrderWithTransactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBulkOrdersAndTransactionsResponseValidationError{
						field:  fmt.Sprintf("OrderWithTransactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBulkOrdersAndTransactionsResponseValidationError{
					field:  fmt.Sprintf("OrderWithTransactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetBulkOrdersAndTransactionsResponseMultiError(errors)
	}

	return nil
}

// GetBulkOrdersAndTransactionsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetBulkOrdersAndTransactionsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetBulkOrdersAndTransactionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBulkOrdersAndTransactionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBulkOrdersAndTransactionsResponseMultiError) AllErrors() []error { return m }

// GetBulkOrdersAndTransactionsResponseValidationError is the validation error
// returned by GetBulkOrdersAndTransactionsResponse.Validate if the designated
// constraints aren't met.
type GetBulkOrdersAndTransactionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBulkOrdersAndTransactionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBulkOrdersAndTransactionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBulkOrdersAndTransactionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBulkOrdersAndTransactionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBulkOrdersAndTransactionsResponseValidationError) ErrorName() string {
	return "GetBulkOrdersAndTransactionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBulkOrdersAndTransactionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBulkOrdersAndTransactionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBulkOrdersAndTransactionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBulkOrdersAndTransactionsResponseValidationError{}

// Validate checks the field values on OrderIdWithTransactionsId with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrderIdWithTransactionsId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrderIdWithTransactionsId with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrderIdWithTransactionsIdMultiError, or nil if none found.
func (m *OrderIdWithTransactionsId) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderIdWithTransactionsId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderId

	if len(errors) > 0 {
		return OrderIdWithTransactionsIdMultiError(errors)
	}

	return nil
}

// OrderIdWithTransactionsIdMultiError is an error wrapping multiple validation
// errors returned by OrderIdWithTransactionsId.ValidateAll() if the
// designated constraints aren't met.
type OrderIdWithTransactionsIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderIdWithTransactionsIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderIdWithTransactionsIdMultiError) AllErrors() []error { return m }

// OrderIdWithTransactionsIdValidationError is the validation error returned by
// OrderIdWithTransactionsId.Validate if the designated constraints aren't met.
type OrderIdWithTransactionsIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderIdWithTransactionsIdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderIdWithTransactionsIdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderIdWithTransactionsIdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderIdWithTransactionsIdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderIdWithTransactionsIdValidationError) ErrorName() string {
	return "OrderIdWithTransactionsIdValidationError"
}

// Error satisfies the builtin error interface
func (e OrderIdWithTransactionsIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderIdWithTransactionsId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderIdWithTransactionsIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderIdWithTransactionsIdValidationError{}

// Validate checks the field values on OrderWithTransactions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrderWithTransactions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrderWithTransactions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrderWithTransactionsMultiError, or nil if none found.
func (m *OrderWithTransactions) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderWithTransactions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderWithTransactionsValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderWithTransactionsValidationError{
					field:  "Order",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderWithTransactionsValidationError{
				field:  "Order",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OrderWithTransactionsValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OrderWithTransactionsValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OrderWithTransactionsValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OrderWithTransactionsMultiError(errors)
	}

	return nil
}

// OrderWithTransactionsMultiError is an error wrapping multiple validation
// errors returned by OrderWithTransactions.ValidateAll() if the designated
// constraints aren't met.
type OrderWithTransactionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderWithTransactionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderWithTransactionsMultiError) AllErrors() []error { return m }

// OrderWithTransactionsValidationError is the validation error returned by
// OrderWithTransactions.Validate if the designated constraints aren't met.
type OrderWithTransactionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderWithTransactionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderWithTransactionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderWithTransactionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderWithTransactionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderWithTransactionsValidationError) ErrorName() string {
	return "OrderWithTransactionsValidationError"
}

// Error satisfies the builtin error interface
func (e OrderWithTransactionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderWithTransactions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderWithTransactionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderWithTransactionsValidationError{}

// Validate checks the field values on BackupTxnBackfillDataToS3Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BackupTxnBackfillDataToS3Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BackupTxnBackfillDataToS3Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BackupTxnBackfillDataToS3RequestMultiError, or nil if none found.
func (m *BackupTxnBackfillDataToS3Request) ValidateAll() error {
	return m.validate(true)
}

func (m *BackupTxnBackfillDataToS3Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ScoopJobId

	// no validation rules for WorkflowId

	switch v := m.S3Payload.(type) {
	case *BackupTxnBackfillDataToS3Request_TxnBackfillWorkflowPayload:
		if v == nil {
			err := BackupTxnBackfillDataToS3RequestValidationError{
				field:  "S3Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTxnBackfillWorkflowPayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BackupTxnBackfillDataToS3RequestValidationError{
						field:  "TxnBackfillWorkflowPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BackupTxnBackfillDataToS3RequestValidationError{
						field:  "TxnBackfillWorkflowPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTxnBackfillWorkflowPayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BackupTxnBackfillDataToS3RequestValidationError{
					field:  "TxnBackfillWorkflowPayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *BackupTxnBackfillDataToS3Request_TxnBackfillEnrichedData:
		if v == nil {
			err := BackupTxnBackfillDataToS3RequestValidationError{
				field:  "S3Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTxnBackfillEnrichedData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BackupTxnBackfillDataToS3RequestValidationError{
						field:  "TxnBackfillEnrichedData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BackupTxnBackfillDataToS3RequestValidationError{
						field:  "TxnBackfillEnrichedData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTxnBackfillEnrichedData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BackupTxnBackfillDataToS3RequestValidationError{
					field:  "TxnBackfillEnrichedData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return BackupTxnBackfillDataToS3RequestMultiError(errors)
	}

	return nil
}

// BackupTxnBackfillDataToS3RequestMultiError is an error wrapping multiple
// validation errors returned by
// BackupTxnBackfillDataToS3Request.ValidateAll() if the designated
// constraints aren't met.
type BackupTxnBackfillDataToS3RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BackupTxnBackfillDataToS3RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BackupTxnBackfillDataToS3RequestMultiError) AllErrors() []error { return m }

// BackupTxnBackfillDataToS3RequestValidationError is the validation error
// returned by BackupTxnBackfillDataToS3Request.Validate if the designated
// constraints aren't met.
type BackupTxnBackfillDataToS3RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BackupTxnBackfillDataToS3RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BackupTxnBackfillDataToS3RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BackupTxnBackfillDataToS3RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BackupTxnBackfillDataToS3RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BackupTxnBackfillDataToS3RequestValidationError) ErrorName() string {
	return "BackupTxnBackfillDataToS3RequestValidationError"
}

// Error satisfies the builtin error interface
func (e BackupTxnBackfillDataToS3RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBackupTxnBackfillDataToS3Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BackupTxnBackfillDataToS3RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BackupTxnBackfillDataToS3RequestValidationError{}

// Validate checks the field values on BackupTxnBackfillDataToS3Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BackupTxnBackfillDataToS3Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BackupTxnBackfillDataToS3Response
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BackupTxnBackfillDataToS3ResponseMultiError, or nil if none found.
func (m *BackupTxnBackfillDataToS3Response) ValidateAll() error {
	return m.validate(true)
}

func (m *BackupTxnBackfillDataToS3Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BackupTxnBackfillDataToS3ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BackupTxnBackfillDataToS3ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BackupTxnBackfillDataToS3ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BackupTxnBackfillDataToS3ResponseMultiError(errors)
	}

	return nil
}

// BackupTxnBackfillDataToS3ResponseMultiError is an error wrapping multiple
// validation errors returned by
// BackupTxnBackfillDataToS3Response.ValidateAll() if the designated
// constraints aren't met.
type BackupTxnBackfillDataToS3ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BackupTxnBackfillDataToS3ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BackupTxnBackfillDataToS3ResponseMultiError) AllErrors() []error { return m }

// BackupTxnBackfillDataToS3ResponseValidationError is the validation error
// returned by BackupTxnBackfillDataToS3Response.Validate if the designated
// constraints aren't met.
type BackupTxnBackfillDataToS3ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BackupTxnBackfillDataToS3ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BackupTxnBackfillDataToS3ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BackupTxnBackfillDataToS3ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BackupTxnBackfillDataToS3ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BackupTxnBackfillDataToS3ResponseValidationError) ErrorName() string {
	return "BackupTxnBackfillDataToS3ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BackupTxnBackfillDataToS3ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBackupTxnBackfillDataToS3Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BackupTxnBackfillDataToS3ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BackupTxnBackfillDataToS3ResponseValidationError{}

// Validate checks the field values on TxnBackfillWorkflowPayloadForS3 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TxnBackfillWorkflowPayloadForS3) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TxnBackfillWorkflowPayloadForS3 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TxnBackfillWorkflowPayloadForS3MultiError, or nil if none found.
func (m *TxnBackfillWorkflowPayloadForS3) ValidateAll() error {
	return m.validate(true)
}

func (m *TxnBackfillWorkflowPayloadForS3) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetOrderTxnPayload() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TxnBackfillWorkflowPayloadForS3ValidationError{
						field:  fmt.Sprintf("OrderTxnPayload[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TxnBackfillWorkflowPayloadForS3ValidationError{
						field:  fmt.Sprintf("OrderTxnPayload[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TxnBackfillWorkflowPayloadForS3ValidationError{
					field:  fmt.Sprintf("OrderTxnPayload[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TxnBackfillWorkflowPayloadForS3MultiError(errors)
	}

	return nil
}

// TxnBackfillWorkflowPayloadForS3MultiError is an error wrapping multiple
// validation errors returned by TxnBackfillWorkflowPayloadForS3.ValidateAll()
// if the designated constraints aren't met.
type TxnBackfillWorkflowPayloadForS3MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TxnBackfillWorkflowPayloadForS3MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TxnBackfillWorkflowPayloadForS3MultiError) AllErrors() []error { return m }

// TxnBackfillWorkflowPayloadForS3ValidationError is the validation error
// returned by TxnBackfillWorkflowPayloadForS3.Validate if the designated
// constraints aren't met.
type TxnBackfillWorkflowPayloadForS3ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TxnBackfillWorkflowPayloadForS3ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TxnBackfillWorkflowPayloadForS3ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TxnBackfillWorkflowPayloadForS3ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TxnBackfillWorkflowPayloadForS3ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TxnBackfillWorkflowPayloadForS3ValidationError) ErrorName() string {
	return "TxnBackfillWorkflowPayloadForS3ValidationError"
}

// Error satisfies the builtin error interface
func (e TxnBackfillWorkflowPayloadForS3ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTxnBackfillWorkflowPayloadForS3.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TxnBackfillWorkflowPayloadForS3ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TxnBackfillWorkflowPayloadForS3ValidationError{}

// Validate checks the field values on TxnBackfillEnrichedDataForS3 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TxnBackfillEnrichedDataForS3) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TxnBackfillEnrichedDataForS3 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TxnBackfillEnrichedDataForS3MultiError, or nil if none found.
func (m *TxnBackfillEnrichedDataForS3) ValidateAll() error {
	return m.validate(true)
}

func (m *TxnBackfillEnrichedDataForS3) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetEnrichedPayData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TxnBackfillEnrichedDataForS3ValidationError{
						field:  fmt.Sprintf("EnrichedPayData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TxnBackfillEnrichedDataForS3ValidationError{
						field:  fmt.Sprintf("EnrichedPayData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TxnBackfillEnrichedDataForS3ValidationError{
					field:  fmt.Sprintf("EnrichedPayData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TxnBackfillEnrichedDataForS3MultiError(errors)
	}

	return nil
}

// TxnBackfillEnrichedDataForS3MultiError is an error wrapping multiple
// validation errors returned by TxnBackfillEnrichedDataForS3.ValidateAll() if
// the designated constraints aren't met.
type TxnBackfillEnrichedDataForS3MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TxnBackfillEnrichedDataForS3MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TxnBackfillEnrichedDataForS3MultiError) AllErrors() []error { return m }

// TxnBackfillEnrichedDataForS3ValidationError is the validation error returned
// by TxnBackfillEnrichedDataForS3.Validate if the designated constraints
// aren't met.
type TxnBackfillEnrichedDataForS3ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TxnBackfillEnrichedDataForS3ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TxnBackfillEnrichedDataForS3ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TxnBackfillEnrichedDataForS3ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TxnBackfillEnrichedDataForS3ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TxnBackfillEnrichedDataForS3ValidationError) ErrorName() string {
	return "TxnBackfillEnrichedDataForS3ValidationError"
}

// Error satisfies the builtin error interface
func (e TxnBackfillEnrichedDataForS3ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTxnBackfillEnrichedDataForS3.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TxnBackfillEnrichedDataForS3ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TxnBackfillEnrichedDataForS3ValidationError{}

// Validate checks the field values on GetPiDetailsFromVendorByTxnIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPiDetailsFromVendorByTxnIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPiDetailsFromVendorByTxnIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPiDetailsFromVendorByTxnIdRequestMultiError, or nil if none found.
func (m *GetPiDetailsFromVendorByTxnIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPiDetailsFromVendorByTxnIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnId

	if len(errors) > 0 {
		return GetPiDetailsFromVendorByTxnIdRequestMultiError(errors)
	}

	return nil
}

// GetPiDetailsFromVendorByTxnIdRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetPiDetailsFromVendorByTxnIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPiDetailsFromVendorByTxnIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPiDetailsFromVendorByTxnIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPiDetailsFromVendorByTxnIdRequestMultiError) AllErrors() []error { return m }

// GetPiDetailsFromVendorByTxnIdRequestValidationError is the validation error
// returned by GetPiDetailsFromVendorByTxnIdRequest.Validate if the designated
// constraints aren't met.
type GetPiDetailsFromVendorByTxnIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPiDetailsFromVendorByTxnIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPiDetailsFromVendorByTxnIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPiDetailsFromVendorByTxnIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPiDetailsFromVendorByTxnIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPiDetailsFromVendorByTxnIdRequestValidationError) ErrorName() string {
	return "GetPiDetailsFromVendorByTxnIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPiDetailsFromVendorByTxnIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPiDetailsFromVendorByTxnIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPiDetailsFromVendorByTxnIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPiDetailsFromVendorByTxnIdRequestValidationError{}

// Validate checks the field values on GetPiDetailsFromVendorByTxnIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPiDetailsFromVendorByTxnIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPiDetailsFromVendorByTxnIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPiDetailsFromVendorByTxnIdResponseMultiError, or nil if none found.
func (m *GetPiDetailsFromVendorByTxnIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPiDetailsFromVendorByTxnIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPiDetailsFromVendorByTxnIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPiDetailsFromVendorByTxnIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPiDetailsFromVendorByTxnIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayerPi()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPiDetailsFromVendorByTxnIdResponseValidationError{
					field:  "PayerPi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPiDetailsFromVendorByTxnIdResponseValidationError{
					field:  "PayerPi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayerPi()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPiDetailsFromVendorByTxnIdResponseValidationError{
				field:  "PayerPi",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayeePi()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPiDetailsFromVendorByTxnIdResponseValidationError{
					field:  "PayeePi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPiDetailsFromVendorByTxnIdResponseValidationError{
					field:  "PayeePi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayeePi()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPiDetailsFromVendorByTxnIdResponseValidationError{
				field:  "PayeePi",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPiDetailsFromVendorByTxnIdResponseMultiError(errors)
	}

	return nil
}

// GetPiDetailsFromVendorByTxnIdResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetPiDetailsFromVendorByTxnIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPiDetailsFromVendorByTxnIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPiDetailsFromVendorByTxnIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPiDetailsFromVendorByTxnIdResponseMultiError) AllErrors() []error { return m }

// GetPiDetailsFromVendorByTxnIdResponseValidationError is the validation error
// returned by GetPiDetailsFromVendorByTxnIdResponse.Validate if the
// designated constraints aren't met.
type GetPiDetailsFromVendorByTxnIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPiDetailsFromVendorByTxnIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPiDetailsFromVendorByTxnIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPiDetailsFromVendorByTxnIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPiDetailsFromVendorByTxnIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPiDetailsFromVendorByTxnIdResponseValidationError) ErrorName() string {
	return "GetPiDetailsFromVendorByTxnIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPiDetailsFromVendorByTxnIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPiDetailsFromVendorByTxnIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPiDetailsFromVendorByTxnIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPiDetailsFromVendorByTxnIdResponseValidationError{}

// Validate checks the field values on
// GetTransactionAggregatesBetweenActorsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetTransactionAggregatesBetweenActorsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTransactionAggregatesBetweenActorsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetTransactionAggregatesBetweenActorsRequestMultiError, or nil if none found.
func (m *GetTransactionAggregatesBetweenActorsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionAggregatesBetweenActorsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPrimaryActorId()) < 1 {
		err := GetTransactionAggregatesBetweenActorsRequestValidationError{
			field:  "PrimaryActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetSecondaryActorId()) < 1 {
		err := GetTransactionAggregatesBetweenActorsRequestValidationError{
			field:  "SecondaryActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AccountingEntryType

	if m.GetFromTime() == nil {
		err := GetTransactionAggregatesBetweenActorsRequestValidationError{
			field:  "FromTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetToTime() == nil {
		err := GetTransactionAggregatesBetweenActorsRequestValidationError{
			field:  "ToTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTransactionAggregatesBetweenActorsRequestMultiError(errors)
	}

	return nil
}

// GetTransactionAggregatesBetweenActorsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetTransactionAggregatesBetweenActorsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetTransactionAggregatesBetweenActorsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionAggregatesBetweenActorsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionAggregatesBetweenActorsRequestMultiError) AllErrors() []error { return m }

// GetTransactionAggregatesBetweenActorsRequestValidationError is the
// validation error returned by
// GetTransactionAggregatesBetweenActorsRequest.Validate if the designated
// constraints aren't met.
type GetTransactionAggregatesBetweenActorsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionAggregatesBetweenActorsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionAggregatesBetweenActorsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionAggregatesBetweenActorsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionAggregatesBetweenActorsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionAggregatesBetweenActorsRequestValidationError) ErrorName() string {
	return "GetTransactionAggregatesBetweenActorsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionAggregatesBetweenActorsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionAggregatesBetweenActorsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionAggregatesBetweenActorsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionAggregatesBetweenActorsRequestValidationError{}

// Validate checks the field values on
// GetTransactionAggregatesBetweenActorsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetTransactionAggregatesBetweenActorsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTransactionAggregatesBetweenActorsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetTransactionAggregatesBetweenActorsResponseMultiError, or nil if none found.
func (m *GetTransactionAggregatesBetweenActorsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionAggregatesBetweenActorsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionAggregatesBetweenActorsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionAggregatesBetweenActorsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionAggregatesBetweenActorsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransactionAggregates()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionAggregatesBetweenActorsResponseValidationError{
					field:  "TransactionAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionAggregatesBetweenActorsResponseValidationError{
					field:  "TransactionAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAggregates()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionAggregatesBetweenActorsResponseValidationError{
				field:  "TransactionAggregates",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTransactionAggregatesBetweenActorsResponseMultiError(errors)
	}

	return nil
}

// GetTransactionAggregatesBetweenActorsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetTransactionAggregatesBetweenActorsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetTransactionAggregatesBetweenActorsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionAggregatesBetweenActorsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionAggregatesBetweenActorsResponseMultiError) AllErrors() []error { return m }

// GetTransactionAggregatesBetweenActorsResponseValidationError is the
// validation error returned by
// GetTransactionAggregatesBetweenActorsResponse.Validate if the designated
// constraints aren't met.
type GetTransactionAggregatesBetweenActorsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionAggregatesBetweenActorsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionAggregatesBetweenActorsResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetTransactionAggregatesBetweenActorsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionAggregatesBetweenActorsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionAggregatesBetweenActorsResponseValidationError) ErrorName() string {
	return "GetTransactionAggregatesBetweenActorsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionAggregatesBetweenActorsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionAggregatesBetweenActorsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionAggregatesBetweenActorsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionAggregatesBetweenActorsResponseValidationError{}

// Validate checks the field values on ClientIdentificationTxnMetaData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClientIdentificationTxnMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClientIdentificationTxnMetaData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ClientIdentificationTxnMetaDataMultiError, or nil if none found.
func (m *ClientIdentificationTxnMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *ClientIdentificationTxnMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReqId

	// no validation rules for Workflow

	// no validation rules for EntityOwnership

	if all {
		switch v := interface{}(m.GetDomainOrderData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClientIdentificationTxnMetaDataValidationError{
					field:  "DomainOrderData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClientIdentificationTxnMetaDataValidationError{
					field:  "DomainOrderData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDomainOrderData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClientIdentificationTxnMetaDataValidationError{
				field:  "DomainOrderData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TargetActorId

	if len(errors) > 0 {
		return ClientIdentificationTxnMetaDataMultiError(errors)
	}

	return nil
}

// ClientIdentificationTxnMetaDataMultiError is an error wrapping multiple
// validation errors returned by ClientIdentificationTxnMetaData.ValidateAll()
// if the designated constraints aren't met.
type ClientIdentificationTxnMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClientIdentificationTxnMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClientIdentificationTxnMetaDataMultiError) AllErrors() []error { return m }

// ClientIdentificationTxnMetaDataValidationError is the validation error
// returned by ClientIdentificationTxnMetaData.Validate if the designated
// constraints aren't met.
type ClientIdentificationTxnMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClientIdentificationTxnMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClientIdentificationTxnMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClientIdentificationTxnMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClientIdentificationTxnMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClientIdentificationTxnMetaDataValidationError) ErrorName() string {
	return "ClientIdentificationTxnMetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e ClientIdentificationTxnMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClientIdentificationTxnMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClientIdentificationTxnMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClientIdentificationTxnMetaDataValidationError{}

// Validate checks the field values on AccountDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountDetailsMultiError,
// or nil if none found.
func (m *AccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	// no validation rules for IfscCode

	// no validation rules for Name

	// no validation rules for BankName

	if len(errors) > 0 {
		return AccountDetailsMultiError(errors)
	}

	return nil
}

// AccountDetailsMultiError is an error wrapping multiple validation errors
// returned by AccountDetails.ValidateAll() if the designated constraints
// aren't met.
type AccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountDetailsMultiError) AllErrors() []error { return m }

// AccountDetailsValidationError is the validation error returned by
// AccountDetails.Validate if the designated constraints aren't met.
type AccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountDetailsValidationError) ErrorName() string { return "AccountDetailsValidationError" }

// Error satisfies the builtin error interface
func (e AccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountDetailsValidationError{}

// Validate checks the field values on DomainOrderData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DomainOrderData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainOrderData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DomainOrderDataMultiError, or nil if none found.
func (m *DomainOrderData) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainOrderData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainOrderDataValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainOrderDataValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainOrderDataValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountsEligibleForPaymentFulfillment() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DomainOrderDataValidationError{
						field:  fmt.Sprintf("AccountsEligibleForPaymentFulfillment[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DomainOrderDataValidationError{
						field:  fmt.Sprintf("AccountsEligibleForPaymentFulfillment[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DomainOrderDataValidationError{
					field:  fmt.Sprintf("AccountsEligibleForPaymentFulfillment[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetAmount() == nil {
		err := DomainOrderDataValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainOrderDataValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainOrderDataValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainOrderDataValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientRequestIdExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainOrderDataValidationError{
					field:  "ClientRequestIdExpiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainOrderDataValidationError{
					field:  "ClientRequestIdExpiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientRequestIdExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainOrderDataValidationError{
				field:  "ClientRequestIdExpiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ToPi

	// no validation rules for ToActor

	// no validation rules for FromPi

	if len(errors) > 0 {
		return DomainOrderDataMultiError(errors)
	}

	return nil
}

// DomainOrderDataMultiError is an error wrapping multiple validation errors
// returned by DomainOrderData.ValidateAll() if the designated constraints
// aren't met.
type DomainOrderDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainOrderDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainOrderDataMultiError) AllErrors() []error { return m }

// DomainOrderDataValidationError is the validation error returned by
// DomainOrderData.Validate if the designated constraints aren't met.
type DomainOrderDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainOrderDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainOrderDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainOrderDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainOrderDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainOrderDataValidationError) ErrorName() string { return "DomainOrderDataValidationError" }

// Error satisfies the builtin error interface
func (e DomainOrderDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainOrderData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainOrderDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainOrderDataValidationError{}

// Validate checks the field values on GetPaymentsDomainOrderDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPaymentsDomainOrderDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentsDomainOrderDataRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPaymentsDomainOrderDataRequestMultiError, or nil if none found.
func (m *GetPaymentsDomainOrderDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentsDomainOrderDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return GetPaymentsDomainOrderDataRequestMultiError(errors)
	}

	return nil
}

// GetPaymentsDomainOrderDataRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetPaymentsDomainOrderDataRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPaymentsDomainOrderDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentsDomainOrderDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentsDomainOrderDataRequestMultiError) AllErrors() []error { return m }

// GetPaymentsDomainOrderDataRequestValidationError is the validation error
// returned by GetPaymentsDomainOrderDataRequest.Validate if the designated
// constraints aren't met.
type GetPaymentsDomainOrderDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentsDomainOrderDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentsDomainOrderDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentsDomainOrderDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentsDomainOrderDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentsDomainOrderDataRequestValidationError) ErrorName() string {
	return "GetPaymentsDomainOrderDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentsDomainOrderDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentsDomainOrderDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentsDomainOrderDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentsDomainOrderDataRequestValidationError{}

// Validate checks the field values on GetPaymentsDomainOrderDataResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPaymentsDomainOrderDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentsDomainOrderDataResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPaymentsDomainOrderDataResponseMultiError, or nil if none found.
func (m *GetPaymentsDomainOrderDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentsDomainOrderDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentsDomainOrderDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentsDomainOrderDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentsDomainOrderDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDomainOrderData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentsDomainOrderDataResponseValidationError{
					field:  "DomainOrderData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentsDomainOrderDataResponseValidationError{
					field:  "DomainOrderData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDomainOrderData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentsDomainOrderDataResponseValidationError{
				field:  "DomainOrderData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPaymentsDomainOrderDataResponseMultiError(errors)
	}

	return nil
}

// GetPaymentsDomainOrderDataResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetPaymentsDomainOrderDataResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPaymentsDomainOrderDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentsDomainOrderDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentsDomainOrderDataResponseMultiError) AllErrors() []error { return m }

// GetPaymentsDomainOrderDataResponseValidationError is the validation error
// returned by GetPaymentsDomainOrderDataResponse.Validate if the designated
// constraints aren't met.
type GetPaymentsDomainOrderDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentsDomainOrderDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentsDomainOrderDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentsDomainOrderDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentsDomainOrderDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentsDomainOrderDataResponseValidationError) ErrorName() string {
	return "GetPaymentsDomainOrderDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentsDomainOrderDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentsDomainOrderDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentsDomainOrderDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentsDomainOrderDataResponseValidationError{}

// Validate checks the field values on StartPennyDropRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartPennyDropRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartPennyDropRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartPennyDropRequestMultiError, or nil if none found.
func (m *StartPennyDropRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StartPennyDropRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetBankAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartPennyDropRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartPennyDropRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartPennyDropRequestValidationError{
				field:  "BankAccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientIdentificationMetadata

	// no validation rules for EntityOwnership

	// no validation rules for RequestSource

	if len(errors) > 0 {
		return StartPennyDropRequestMultiError(errors)
	}

	return nil
}

// StartPennyDropRequestMultiError is an error wrapping multiple validation
// errors returned by StartPennyDropRequest.ValidateAll() if the designated
// constraints aren't met.
type StartPennyDropRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartPennyDropRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartPennyDropRequestMultiError) AllErrors() []error { return m }

// StartPennyDropRequestValidationError is the validation error returned by
// StartPennyDropRequest.Validate if the designated constraints aren't met.
type StartPennyDropRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartPennyDropRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartPennyDropRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartPennyDropRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartPennyDropRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartPennyDropRequestValidationError) ErrorName() string {
	return "StartPennyDropRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StartPennyDropRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartPennyDropRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartPennyDropRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartPennyDropRequestValidationError{}

// Validate checks the field values on StartPennyDropResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartPennyDropResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartPennyDropResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartPennyDropResponseMultiError, or nil if none found.
func (m *StartPennyDropResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StartPennyDropResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartPennyDropResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartPennyDropResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartPennyDropResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartPennyDropResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartPennyDropResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartPennyDropResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StartPennyDropResponseMultiError(errors)
	}

	return nil
}

// StartPennyDropResponseMultiError is an error wrapping multiple validation
// errors returned by StartPennyDropResponse.ValidateAll() if the designated
// constraints aren't met.
type StartPennyDropResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartPennyDropResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartPennyDropResponseMultiError) AllErrors() []error { return m }

// StartPennyDropResponseValidationError is the validation error returned by
// StartPennyDropResponse.Validate if the designated constraints aren't met.
type StartPennyDropResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartPennyDropResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartPennyDropResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartPennyDropResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartPennyDropResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartPennyDropResponseValidationError) ErrorName() string {
	return "StartPennyDropResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StartPennyDropResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartPennyDropResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartPennyDropResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartPennyDropResponseValidationError{}

// Validate checks the field values on GetPennyDropStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPennyDropStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPennyDropStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPennyDropStatusRequestMultiError, or nil if none found.
func (m *GetPennyDropStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPennyDropStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	// no validation rules for RetryAttemptNumber

	// no validation rules for EntityOwnership

	if len(errors) > 0 {
		return GetPennyDropStatusRequestMultiError(errors)
	}

	return nil
}

// GetPennyDropStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetPennyDropStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type GetPennyDropStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPennyDropStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPennyDropStatusRequestMultiError) AllErrors() []error { return m }

// GetPennyDropStatusRequestValidationError is the validation error returned by
// GetPennyDropStatusRequest.Validate if the designated constraints aren't met.
type GetPennyDropStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPennyDropStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPennyDropStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPennyDropStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPennyDropStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPennyDropStatusRequestValidationError) ErrorName() string {
	return "GetPennyDropStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPennyDropStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPennyDropStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPennyDropStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPennyDropStatusRequestValidationError{}

// Validate checks the field values on GetPennyDropStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPennyDropStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPennyDropStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPennyDropStatusResponseMultiError, or nil if none found.
func (m *GetPennyDropStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPennyDropStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPennyDropStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPennyDropStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPennyDropStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPennyDropStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPennyDropStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPennyDropStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PennyDropStatus

	// no validation rules for OrderId

	// no validation rules for PaymentInstrumentId

	// no validation rules for VerifiedName

	// no validation rules for PennyDropTxnRequestId

	if len(errors) > 0 {
		return GetPennyDropStatusResponseMultiError(errors)
	}

	return nil
}

// GetPennyDropStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetPennyDropStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type GetPennyDropStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPennyDropStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPennyDropStatusResponseMultiError) AllErrors() []error { return m }

// GetPennyDropStatusResponseValidationError is the validation error returned
// by GetPennyDropStatusResponse.Validate if the designated constraints aren't met.
type GetPennyDropStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPennyDropStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPennyDropStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPennyDropStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPennyDropStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPennyDropStatusResponseValidationError) ErrorName() string {
	return "GetPennyDropStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPennyDropStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPennyDropStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPennyDropStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPennyDropStatusResponseValidationError{}

// Validate checks the field values on PennyDropClientIdentificationMetadata
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PennyDropClientIdentificationMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PennyDropClientIdentificationMetadata
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PennyDropClientIdentificationMetadataMultiError, or nil if none found.
func (m *PennyDropClientIdentificationMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *PennyDropClientIdentificationMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReqId

	// no validation rules for PennyDropProvenance

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetPostPaymentDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PennyDropClientIdentificationMetadataValidationError{
					field:  "PostPaymentDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PennyDropClientIdentificationMetadataValidationError{
					field:  "PostPaymentDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostPaymentDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PennyDropClientIdentificationMetadataValidationError{
				field:  "PostPaymentDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PennyDropClientIdentificationMetadataMultiError(errors)
	}

	return nil
}

// PennyDropClientIdentificationMetadataMultiError is an error wrapping
// multiple validation errors returned by
// PennyDropClientIdentificationMetadata.ValidateAll() if the designated
// constraints aren't met.
type PennyDropClientIdentificationMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PennyDropClientIdentificationMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PennyDropClientIdentificationMetadataMultiError) AllErrors() []error { return m }

// PennyDropClientIdentificationMetadataValidationError is the validation error
// returned by PennyDropClientIdentificationMetadata.Validate if the
// designated constraints aren't met.
type PennyDropClientIdentificationMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PennyDropClientIdentificationMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PennyDropClientIdentificationMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PennyDropClientIdentificationMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PennyDropClientIdentificationMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PennyDropClientIdentificationMetadataValidationError) ErrorName() string {
	return "PennyDropClientIdentificationMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e PennyDropClientIdentificationMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPennyDropClientIdentificationMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PennyDropClientIdentificationMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PennyDropClientIdentificationMetadataValidationError{}

// Validate checks the field values on GetOrderWithTransactionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderWithTransactionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderWithTransactionsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetOrderWithTransactionsRequestMultiError, or nil if none found.
func (m *GetOrderWithTransactionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderWithTransactionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetOrderId()) < 1 {
		err := GetOrderWithTransactionsRequestValidationError{
			field:  "OrderId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetOrderWithTransactionsRequestMultiError(errors)
	}

	return nil
}

// GetOrderWithTransactionsRequestMultiError is an error wrapping multiple
// validation errors returned by GetOrderWithTransactionsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetOrderWithTransactionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderWithTransactionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderWithTransactionsRequestMultiError) AllErrors() []error { return m }

// GetOrderWithTransactionsRequestValidationError is the validation error
// returned by GetOrderWithTransactionsRequest.Validate if the designated
// constraints aren't met.
type GetOrderWithTransactionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderWithTransactionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderWithTransactionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderWithTransactionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderWithTransactionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderWithTransactionsRequestValidationError) ErrorName() string {
	return "GetOrderWithTransactionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderWithTransactionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderWithTransactionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderWithTransactionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderWithTransactionsRequestValidationError{}

// Validate checks the field values on GetOrderWithTransactionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetOrderWithTransactionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderWithTransactionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetOrderWithTransactionsResponseMultiError, or nil if none found.
func (m *GetOrderWithTransactionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderWithTransactionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderWithTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderWithTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderWithTransactionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderWithTransactions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderWithTransactionsResponseValidationError{
					field:  "OrderWithTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderWithTransactionsResponseValidationError{
					field:  "OrderWithTransactions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderWithTransactions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderWithTransactionsResponseValidationError{
				field:  "OrderWithTransactions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOrderWithTransactionsResponseMultiError(errors)
	}

	return nil
}

// GetOrderWithTransactionsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetOrderWithTransactionsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetOrderWithTransactionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderWithTransactionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderWithTransactionsResponseMultiError) AllErrors() []error { return m }

// GetOrderWithTransactionsResponseValidationError is the validation error
// returned by GetOrderWithTransactionsResponse.Validate if the designated
// constraints aren't met.
type GetOrderWithTransactionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderWithTransactionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderWithTransactionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderWithTransactionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderWithTransactionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderWithTransactionsResponseValidationError) ErrorName() string {
	return "GetOrderWithTransactionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderWithTransactionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderWithTransactionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderWithTransactionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderWithTransactionsResponseValidationError{}

// Validate checks the field values on CreateOrderVendorOrderMapRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateOrderVendorOrderMapRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrderVendorOrderMapRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateOrderVendorOrderMapRequestMultiError, or nil if none found.
func (m *CreateOrderVendorOrderMapRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrderVendorOrderMapRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetOrderId()) < 1 {
		err := CreateOrderVendorOrderMapRequestValidationError{
			field:  "OrderId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetVendorOrderId()) < 1 {
		err := CreateOrderVendorOrderMapRequestValidationError{
			field:  "VendorOrderId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Vendor

	// no validation rules for EntityOwnership

	// no validation rules for DomainReferenceId

	if len(errors) > 0 {
		return CreateOrderVendorOrderMapRequestMultiError(errors)
	}

	return nil
}

// CreateOrderVendorOrderMapRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateOrderVendorOrderMapRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateOrderVendorOrderMapRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrderVendorOrderMapRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrderVendorOrderMapRequestMultiError) AllErrors() []error { return m }

// CreateOrderVendorOrderMapRequestValidationError is the validation error
// returned by CreateOrderVendorOrderMapRequest.Validate if the designated
// constraints aren't met.
type CreateOrderVendorOrderMapRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrderVendorOrderMapRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrderVendorOrderMapRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrderVendorOrderMapRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrderVendorOrderMapRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrderVendorOrderMapRequestValidationError) ErrorName() string {
	return "CreateOrderVendorOrderMapRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrderVendorOrderMapRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrderVendorOrderMapRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrderVendorOrderMapRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrderVendorOrderMapRequestValidationError{}

// Validate checks the field values on CreateOrderVendorOrderMapResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateOrderVendorOrderMapResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrderVendorOrderMapResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateOrderVendorOrderMapResponseMultiError, or nil if none found.
func (m *CreateOrderVendorOrderMapResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrderVendorOrderMapResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrderVendorOrderMapResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrderVendorOrderMapResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrderVendorOrderMapResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderVendorOrderMap()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrderVendorOrderMapResponseValidationError{
					field:  "OrderVendorOrderMap",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrderVendorOrderMapResponseValidationError{
					field:  "OrderVendorOrderMap",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderVendorOrderMap()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrderVendorOrderMapResponseValidationError{
				field:  "OrderVendorOrderMap",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateOrderVendorOrderMapResponseMultiError(errors)
	}

	return nil
}

// CreateOrderVendorOrderMapResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateOrderVendorOrderMapResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateOrderVendorOrderMapResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrderVendorOrderMapResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrderVendorOrderMapResponseMultiError) AllErrors() []error { return m }

// CreateOrderVendorOrderMapResponseValidationError is the validation error
// returned by CreateOrderVendorOrderMapResponse.Validate if the designated
// constraints aren't met.
type CreateOrderVendorOrderMapResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrderVendorOrderMapResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrderVendorOrderMapResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrderVendorOrderMapResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrderVendorOrderMapResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrderVendorOrderMapResponseValidationError) ErrorName() string {
	return "CreateOrderVendorOrderMapResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrderVendorOrderMapResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrderVendorOrderMapResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrderVendorOrderMapResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrderVendorOrderMapResponseValidationError{}

// Validate checks the field values on GetOrderVendorOrderMapRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderVendorOrderMapRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderVendorOrderMapRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetOrderVendorOrderMapRequestMultiError, or nil if none found.
func (m *GetOrderVendorOrderMapRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderVendorOrderMapRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetOrderId()) < 1 {
		err := GetOrderVendorOrderMapRequestValidationError{
			field:  "OrderId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetOrderVendorOrderMapRequestMultiError(errors)
	}

	return nil
}

// GetOrderVendorOrderMapRequestMultiError is an error wrapping multiple
// validation errors returned by GetOrderVendorOrderMapRequest.ValidateAll()
// if the designated constraints aren't met.
type GetOrderVendorOrderMapRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderVendorOrderMapRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderVendorOrderMapRequestMultiError) AllErrors() []error { return m }

// GetOrderVendorOrderMapRequestValidationError is the validation error
// returned by GetOrderVendorOrderMapRequest.Validate if the designated
// constraints aren't met.
type GetOrderVendorOrderMapRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderVendorOrderMapRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderVendorOrderMapRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderVendorOrderMapRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderVendorOrderMapRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderVendorOrderMapRequestValidationError) ErrorName() string {
	return "GetOrderVendorOrderMapRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderVendorOrderMapRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderVendorOrderMapRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderVendorOrderMapRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderVendorOrderMapRequestValidationError{}

// Validate checks the field values on GetOrderVendorOrderMapResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrderVendorOrderMapResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrderVendorOrderMapResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetOrderVendorOrderMapResponseMultiError, or nil if none found.
func (m *GetOrderVendorOrderMapResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrderVendorOrderMapResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOrderVendorOrderMapResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOrderVendorOrderMapResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOrderVendorOrderMapResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVendorOrderMaps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOrderVendorOrderMapResponseValidationError{
						field:  fmt.Sprintf("VendorOrderMaps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOrderVendorOrderMapResponseValidationError{
						field:  fmt.Sprintf("VendorOrderMaps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOrderVendorOrderMapResponseValidationError{
					field:  fmt.Sprintf("VendorOrderMaps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetOrderVendorOrderMapResponseMultiError(errors)
	}

	return nil
}

// GetOrderVendorOrderMapResponseMultiError is an error wrapping multiple
// validation errors returned by GetOrderVendorOrderMapResponse.ValidateAll()
// if the designated constraints aren't met.
type GetOrderVendorOrderMapResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrderVendorOrderMapResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrderVendorOrderMapResponseMultiError) AllErrors() []error { return m }

// GetOrderVendorOrderMapResponseValidationError is the validation error
// returned by GetOrderVendorOrderMapResponse.Validate if the designated
// constraints aren't met.
type GetOrderVendorOrderMapResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrderVendorOrderMapResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrderVendorOrderMapResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrderVendorOrderMapResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrderVendorOrderMapResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrderVendorOrderMapResponseValidationError) ErrorName() string {
	return "GetOrderVendorOrderMapResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrderVendorOrderMapResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrderVendorOrderMapResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrderVendorOrderMapResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrderVendorOrderMapResponseValidationError{}

// Validate checks the field values on SignalWorkflowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SignalWorkflowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignalWorkflowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SignalWorkflowRequestMultiError, or nil if none found.
func (m *SignalWorkflowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SignalWorkflowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.SignalIdentifierAndPayload.(type) {
	case *SignalWorkflowRequest_PgFundTransferCompleteSignalPayload:
		if v == nil {
			err := SignalWorkflowRequestValidationError{
				field:  "SignalIdentifierAndPayload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPgFundTransferCompleteSignalPayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SignalWorkflowRequestValidationError{
						field:  "PgFundTransferCompleteSignalPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SignalWorkflowRequestValidationError{
						field:  "PgFundTransferCompleteSignalPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPgFundTransferCompleteSignalPayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SignalWorkflowRequestValidationError{
					field:  "PgFundTransferCompleteSignalPayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SignalWorkflowRequestMultiError(errors)
	}

	return nil
}

// SignalWorkflowRequestMultiError is an error wrapping multiple validation
// errors returned by SignalWorkflowRequest.ValidateAll() if the designated
// constraints aren't met.
type SignalWorkflowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignalWorkflowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignalWorkflowRequestMultiError) AllErrors() []error { return m }

// SignalWorkflowRequestValidationError is the validation error returned by
// SignalWorkflowRequest.Validate if the designated constraints aren't met.
type SignalWorkflowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignalWorkflowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignalWorkflowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignalWorkflowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignalWorkflowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignalWorkflowRequestValidationError) ErrorName() string {
	return "SignalWorkflowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SignalWorkflowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignalWorkflowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignalWorkflowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignalWorkflowRequestValidationError{}

// Validate checks the field values on PgFundTransferCompleteSignalPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PgFundTransferCompleteSignalPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PgFundTransferCompleteSignalPayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PgFundTransferCompleteSignalPayloadMultiError, or nil if none found.
func (m *PgFundTransferCompleteSignalPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *PgFundTransferCompleteSignalPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderId

	// no validation rules for ClientReqId

	if all {
		switch v := interface{}(m.GetSignalPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PgFundTransferCompleteSignalPayloadValidationError{
					field:  "SignalPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PgFundTransferCompleteSignalPayloadValidationError{
					field:  "SignalPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSignalPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PgFundTransferCompleteSignalPayloadValidationError{
				field:  "SignalPayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PgFundTransferCompleteSignalPayloadMultiError(errors)
	}

	return nil
}

// PgFundTransferCompleteSignalPayloadMultiError is an error wrapping multiple
// validation errors returned by
// PgFundTransferCompleteSignalPayload.ValidateAll() if the designated
// constraints aren't met.
type PgFundTransferCompleteSignalPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PgFundTransferCompleteSignalPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PgFundTransferCompleteSignalPayloadMultiError) AllErrors() []error { return m }

// PgFundTransferCompleteSignalPayloadValidationError is the validation error
// returned by PgFundTransferCompleteSignalPayload.Validate if the designated
// constraints aren't met.
type PgFundTransferCompleteSignalPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PgFundTransferCompleteSignalPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PgFundTransferCompleteSignalPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PgFundTransferCompleteSignalPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PgFundTransferCompleteSignalPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PgFundTransferCompleteSignalPayloadValidationError) ErrorName() string {
	return "PgFundTransferCompleteSignalPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e PgFundTransferCompleteSignalPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPgFundTransferCompleteSignalPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PgFundTransferCompleteSignalPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PgFundTransferCompleteSignalPayloadValidationError{}

// Validate checks the field values on SignalWorkflowResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SignalWorkflowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignalWorkflowResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SignalWorkflowResponseMultiError, or nil if none found.
func (m *SignalWorkflowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SignalWorkflowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SignalWorkflowResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SignalWorkflowResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SignalWorkflowResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SignalWorkflowResponseMultiError(errors)
	}

	return nil
}

// SignalWorkflowResponseMultiError is an error wrapping multiple validation
// errors returned by SignalWorkflowResponse.ValidateAll() if the designated
// constraints aren't met.
type SignalWorkflowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignalWorkflowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignalWorkflowResponseMultiError) AllErrors() []error { return m }

// SignalWorkflowResponseValidationError is the validation error returned by
// SignalWorkflowResponse.Validate if the designated constraints aren't met.
type SignalWorkflowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignalWorkflowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignalWorkflowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignalWorkflowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignalWorkflowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignalWorkflowResponseValidationError) ErrorName() string {
	return "SignalWorkflowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SignalWorkflowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignalWorkflowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignalWorkflowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignalWorkflowResponseValidationError{}

// Validate checks the field values on
// EnrichOrderAndTransactionsRequest_DebitCardSwitchPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnrichOrderAndTransactionsRequest_DebitCardSwitchPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// EnrichOrderAndTransactionsRequest_DebitCardSwitchPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadMultiError, or nil
// if none found.
func (m *EnrichOrderAndTransactionsRequest_DebitCardSwitchPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *EnrichOrderAndTransactionsRequest_DebitCardSwitchPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetSwitchExtRefId()) < 1 {
		err := EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError{
			field:  "SwitchExtRefId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadMultiError(errors)
	}

	return nil
}

// EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadMultiError is an
// error wrapping multiple validation errors returned by
// EnrichOrderAndTransactionsRequest_DebitCardSwitchPayload.ValidateAll() if
// the designated constraints aren't met.
type EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadMultiError) AllErrors() []error {
	return m
}

// EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError is
// the validation error returned by
// EnrichOrderAndTransactionsRequest_DebitCardSwitchPayload.Validate if the
// designated constraints aren't met.
type EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError) ErrorName() string {
	return "EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnrichOrderAndTransactionsRequest_DebitCardSwitchPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnrichOrderAndTransactionsRequest_DebitCardSwitchPayloadValidationError{}

// Validate checks the field values on AuthoriseFundTransferRequest_AuthHeader
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AuthoriseFundTransferRequest_AuthHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AuthoriseFundTransferRequest_AuthHeader with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// AuthoriseFundTransferRequest_AuthHeaderMultiError, or nil if none found.
func (m *AuthoriseFundTransferRequest_AuthHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseFundTransferRequest_AuthHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Credential.(type) {
	case *AuthoriseFundTransferRequest_AuthHeader_PartnerSdkCreds:
		if v == nil {
			err := AuthoriseFundTransferRequest_AuthHeaderValidationError{
				field:  "Credential",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPartnerSdkCreds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AuthoriseFundTransferRequest_AuthHeaderValidationError{
						field:  "PartnerSdkCreds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AuthoriseFundTransferRequest_AuthHeaderValidationError{
						field:  "PartnerSdkCreds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPartnerSdkCreds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AuthoriseFundTransferRequest_AuthHeaderValidationError{
					field:  "PartnerSdkCreds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AuthoriseFundTransferRequest_AuthHeader_NpciCred:
		if v == nil {
			err := AuthoriseFundTransferRequest_AuthHeaderValidationError{
				field:  "Credential",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNpciCred()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AuthoriseFundTransferRequest_AuthHeaderValidationError{
						field:  "NpciCred",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AuthoriseFundTransferRequest_AuthHeaderValidationError{
						field:  "NpciCred",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNpciCred()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AuthoriseFundTransferRequest_AuthHeaderValidationError{
					field:  "NpciCred",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AuthoriseFundTransferRequest_AuthHeaderMultiError(errors)
	}

	return nil
}

// AuthoriseFundTransferRequest_AuthHeaderMultiError is an error wrapping
// multiple validation errors returned by
// AuthoriseFundTransferRequest_AuthHeader.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseFundTransferRequest_AuthHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseFundTransferRequest_AuthHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseFundTransferRequest_AuthHeaderMultiError) AllErrors() []error { return m }

// AuthoriseFundTransferRequest_AuthHeaderValidationError is the validation
// error returned by AuthoriseFundTransferRequest_AuthHeader.Validate if the
// designated constraints aren't met.
type AuthoriseFundTransferRequest_AuthHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseFundTransferRequest_AuthHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseFundTransferRequest_AuthHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseFundTransferRequest_AuthHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseFundTransferRequest_AuthHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseFundTransferRequest_AuthHeaderValidationError) ErrorName() string {
	return "AuthoriseFundTransferRequest_AuthHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseFundTransferRequest_AuthHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseFundTransferRequest_AuthHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseFundTransferRequest_AuthHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseFundTransferRequest_AuthHeaderValidationError{}

// Validate checks the field values on
// BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskMultiError,
// or nil if none found.
func (m *BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdateOrderRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError{
					field:  "UpdateOrderRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError{
					field:  "UpdateOrderRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateOrderRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError{
				field:  "UpdateOrderRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUpdateTxnRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError{
						field:  fmt.Sprintf("UpdateTxnRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError{
						field:  fmt.Sprintf("UpdateTxnRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError{
					field:  fmt.Sprintf("UpdateTxnRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskMultiError(errors)
	}

	return nil
}

// BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskMultiError
// is an error wrapping multiple validation errors returned by
// BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask.ValidateAll()
// if the designated constraints aren't met.
type BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskMultiError) AllErrors() []error {
	return m
}

// BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError
// is the validation error returned by
// BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask.Validate
// if the designated constraints aren't met.
type BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError) ErrorName() string {
	return "BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError"
}

// Error satisfies the builtin error interface
func (e BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMaskValidationError{}
