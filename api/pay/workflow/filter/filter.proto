syntax = "proto3";

package pay.workflow.filter;

import "api/pay/workflow/filter/stage_filter.proto";
import "api/pay/workflow/filter/status_filter.proto";

option go_package = "github.com/epifi/gamma/api/pay/workflow/filter";
option java_package = "com.github.epifi.gamma.api.pay.workflow.filter";

// Filter represent `one of` various possible filtering criteria possible for a workflow
message Filter {
  oneof Option {
    // filters workflows in a set of status
    pay.workflow.filter.StatusOption status_filter_option = 1;
    // filters workflows in a particular stage and status
    pay.workflow.filter.StageOption stage_filter_option = 2;
    // filters workflows stuck in a set of status beyond an SLA
    pay.workflow.filter.StatusWithSlaOption status_with_sla_filter_option = 3;
    // filters workflows stuck in a particular stage and status beyond an SLA
    pay.workflow.filter.StageWithSlaOption stage_with_sla_filter_option = 4;
  }
}

// FilterWithCount wraps filter with count of workflows after applying the filter
message FilterWithCount {
  pay.workflow.filter.Filter filter = 1;

  int64 count = 2;
}
