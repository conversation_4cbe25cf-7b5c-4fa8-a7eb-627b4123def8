// protolint:disable MAX_LINE_LENGTH
// This file defines different messages that are passed along for B2CFundTransfer business logic processing using temporal
syntax = "proto3";

package pay.payload;

import "api/order/payment/transaction.proto";
import "api/vendorgateway/request_source.proto";

option go_package = "github.com/epifi/gamma/api/pay/payload";
option java_package = "com.github.epifi.gamma.api.pay.payload";

message B2CFundTransfer {

  // Request source indicating the source or flow of the request. E.g. LoansFlow.
  // Currently used to segregate the vendor (Federal currently) api credentials based on the request source.
  // If not provided, the default request source will be used which corresponds to the default credentials.
  api.vendorgateway.RequestSource request_source = 1;

}

message B2CFundTransferCallbackSignal {
  // We are moving all the signals contract to pay/signal package going forward. Please refer to the new payload defined
  // there
  option deprecated = true;

  order.payment.TransactionStatus transaction_status = 1;
}
