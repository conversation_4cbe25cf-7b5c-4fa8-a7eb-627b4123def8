syntax = "proto3";

package vendorgateway.crm;

import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/crm/enums.proto";
import "api/vendorgateway/crm/risk/agent.proto";
import "api/vendorgateway/crm/risk/ticket.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/crm";
option java_package = "com.github.epifi.gamma.api.vendorgateway.crm";

// request header to be used in all CRM rpcs
message RequestHeader {
  // use case for which rpc call is being made
  UseCase use_case = 1;

  api.typesv2.common.Ownership ownership = 2;
}

// Ticket message is just wrapper over underlying use case specific objects
// ticket details can change based on the UseCase since we will add different fields and field values for ticket
message Ticket {
  // based on use case we will one of the ticket object below
  oneof ticket {
    // ticket object for risk use case
    risk.RiskTicket risk_ticket = 1;
  }
}

// message to represent Agent object in CRM
// we will need this to map a CRM agent to user of our internal tool and also get some additional ACL details
message Agent {
  // User ID of the agent
  // Mandatory for updating agent
  uint64 id = 1;

  // Set to true if this is an occasional agent (true => occasional, false => full-time)
  api.typesv2.common.BooleanEnum is_occasional = 2;

  // Ticket permission of the agent
  //(1 -> Global Access, 2 -> Group Access, 3 -> Restricted Access)
  // Mandatory for creating agent
  TicketScope ticket_scope = 5;

  // Skill ids associated with the agent
  repeated uint64 skill_ids = 6;

  // Groups that agent is part of
  repeated uint64 group_ids = 7 [deprecated = true];

  // Role IDs associated with the agent
  repeated uint64 role_ids = 8;

  // Agent creation timestamp
  google.protobuf.Timestamp created_at = 9;

  // Agent updated timestamp
  google.protobuf.Timestamp updated_at = 10;

  google.protobuf.Timestamp last_active_at = 11;

  // Email address of the agent
  // mandatory for creating agent
  string email = 12;

  // Name of the agent
  string name = 13;

  // phone number of the agent
  api.typesv2.common.PhoneNumber phone = 14;

  oneof additional_details {
    risk.RiskAgentAdditionalDetails risk_agent_additional_details = 15;
  }
}

// Message to represent User Contact object in crm
// Mandatory fields for creating contact: at least one of {unique_external_id, email, phone}
message CustomerContact {
  // Unique ID of customer contact in CRM
  uint64 id = 1;

  // Unique external id of contact to CRM
  string unique_external_id = 2;

  // Email address of customer
  string email = 3;

  // Phone number of customer
  api.typesv2.common.PhoneNumber phone = 4;

  // Contact creation timestamp
  google.protobuf.Timestamp created_at = 5;

  // Contact last updated timestamp
  google.protobuf.Timestamp updated_at = 6;
}
