// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/stocks/stocks.proto

package stocks

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Journal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id for the corresponding Journal
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// vendor account Id of source account
	FromAccount string `protobuf:"bytes,2,opt,name=from_account,json=fromAccount,proto3" json:"from_account,omitempty"`
	// vendor account Id of destination account
	ToAccount string `protobuf:"bytes,3,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	// type of journal
	// can be cash transfer or securities
	Type JournalType `protobuf:"varint,4,opt,name=type,proto3,enum=vendorgateway.stocks.JournalType" json:"type,omitempty"`
	// status of the journal request
	Status JournalStatus `protobuf:"varint,5,opt,name=status,proto3,enum=vendorgateway.stocks.JournalStatus" json:"status,omitempty"`
	// Journal request settlement time at vendor
	SettledAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=settled_at,json=settledAt,proto3" json:"settled_at,omitempty"`
	// Journal request creation time at vendor
	//
	// Deprecated: Marked as deprecated in api/vendorgateway/stocks/stocks.proto.
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Journal type specific data
	//
	// Types that are assignable to JournalData:
	//
	//	*Journal_CashJournalData
	//	*Journal_SecuritiesJournalData
	JournalData isJournal_JournalData `protobuf_oneof:"journal_data"`
	// currency code in which Journaling is done
	// eg: for us stocks broker currency will be always USD
	Currency string `protobuf:"bytes,10,opt,name=currency,proto3" json:"currency,omitempty"`
	// description is a remark associated with the journal request
	Description string `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"`
	// reason for journal transaction failure
	ErrorMessage string `protobuf:"bytes,12,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// timestamp at which journal was updated at vendor
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *Journal) Reset() {
	*x = Journal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_stocks_stocks_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Journal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Journal) ProtoMessage() {}

func (x *Journal) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_stocks_stocks_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Journal.ProtoReflect.Descriptor instead.
func (*Journal) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_stocks_stocks_proto_rawDescGZIP(), []int{0}
}

func (x *Journal) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Journal) GetFromAccount() string {
	if x != nil {
		return x.FromAccount
	}
	return ""
}

func (x *Journal) GetToAccount() string {
	if x != nil {
		return x.ToAccount
	}
	return ""
}

func (x *Journal) GetType() JournalType {
	if x != nil {
		return x.Type
	}
	return JournalType_JOURNAL_TYPE_UNSPECIFIED
}

func (x *Journal) GetStatus() JournalStatus {
	if x != nil {
		return x.Status
	}
	return JournalStatus_JOURNAL_STATUS_UNSPECIFIED
}

func (x *Journal) GetSettledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SettledAt
	}
	return nil
}

// Deprecated: Marked as deprecated in api/vendorgateway/stocks/stocks.proto.
func (x *Journal) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (m *Journal) GetJournalData() isJournal_JournalData {
	if m != nil {
		return m.JournalData
	}
	return nil
}

func (x *Journal) GetCashJournalData() *CashJournalData {
	if x, ok := x.GetJournalData().(*Journal_CashJournalData); ok {
		return x.CashJournalData
	}
	return nil
}

func (x *Journal) GetSecuritiesJournalData() *SecuritiesJournalData {
	if x, ok := x.GetJournalData().(*Journal_SecuritiesJournalData); ok {
		return x.SecuritiesJournalData
	}
	return nil
}

func (x *Journal) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Journal) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Journal) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *Journal) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type isJournal_JournalData interface {
	isJournal_JournalData()
}

type Journal_CashJournalData struct {
	CashJournalData *CashJournalData `protobuf:"bytes,8,opt,name=cash_journal_data,json=cashJournalData,proto3,oneof"`
}

type Journal_SecuritiesJournalData struct {
	SecuritiesJournalData *SecuritiesJournalData `protobuf:"bytes,9,opt,name=securities_journal_data,json=securitiesJournalData,proto3,oneof"`
}

func (*Journal_CashJournalData) isJournal_JournalData() {}

func (*Journal_SecuritiesJournalData) isJournal_JournalData() {}

type CashJournalData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// cash amount requested to be journaled
	Amount *money.Money `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *CashJournalData) Reset() {
	*x = CashJournalData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_stocks_stocks_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CashJournalData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CashJournalData) ProtoMessage() {}

func (x *CashJournalData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_stocks_stocks_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CashJournalData.ProtoReflect.Descriptor instead.
func (*CashJournalData) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_stocks_stocks_proto_rawDescGZIP(), []int{1}
}

func (x *CashJournalData) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

type SecuritiesJournalData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// symbol for which the securities transferred
	Symbol string `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// qty of the securities journaled
	Qty float64 `protobuf:"fixed64,2,opt,name=qty,proto3" json:"qty,omitempty"`
	// amount of the securities journaled
	Amount *money.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *SecuritiesJournalData) Reset() {
	*x = SecuritiesJournalData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_stocks_stocks_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecuritiesJournalData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecuritiesJournalData) ProtoMessage() {}

func (x *SecuritiesJournalData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_stocks_stocks_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecuritiesJournalData.ProtoReflect.Descriptor instead.
func (*SecuritiesJournalData) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_stocks_stocks_proto_rawDescGZIP(), []int{2}
}

func (x *SecuritiesJournalData) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SecuritiesJournalData) GetQty() float64 {
	if x != nil {
		return x.Qty
	}
	return 0
}

func (x *SecuritiesJournalData) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

// asset available for trade and data consumption from alpaca
type Asset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id for the corresponding Asset
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// class of the Asset request
	Class AssetClass `protobuf:"varint,2,opt,name=class,proto3,enum=vendorgateway.stocks.AssetClass" json:"class,omitempty"`
	// exchange on which asset is listed
	Exchange AssetExchange `protobuf:"varint,3,opt,name=exchange,proto3,enum=vendorgateway.stocks.AssetExchange" json:"exchange,omitempty"`
	// The symbol of the asset
	Symbol string `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// the official name of the asset
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// status of the Asset request
	Status AssetStatus `protobuf:"varint,6,opt,name=status,proto3,enum=vendorgateway.stocks.AssetStatus" json:"status,omitempty"`
	// asset is tradable on Alpaca or not
	IsTradable bool `protobuf:"varint,7,opt,name=is_tradable,json=isTradable,proto3" json:"is_tradable,omitempty"`
	// asset is marginable or not
	IsMarginable bool `protobuf:"varint,8,opt,name=is_marginable,json=isMarginable,proto3" json:"is_marginable,omitempty"`
	// asset is shortable or not
	IsShortable bool `protobuf:"varint,9,opt,name=is_shortable,json=isShortable,proto3" json:"is_shortable,omitempty"`
	// asset is easy-to-borrow or not (filtering for easy_to_borrow = True
	// is the best way to check whether the name is currently available to short at Alpaca).
	IsEasyToBorrow bool `protobuf:"varint,10,opt,name=is_easy_to_borrow,json=isEasyToBorrow,proto3" json:"is_easy_to_borrow,omitempty"`
	// asset is fractionable or not
	IsFractionable bool `protobuf:"varint,11,opt,name=is_fractionable,json=isFractionable,proto3" json:"is_fractionable,omitempty"`
	// minimum order size. Field available for crypto only.
	MinOrderSize string `protobuf:"bytes,12,opt,name=min_order_size,json=minOrderSize,proto3" json:"min_order_size,omitempty"`
	// amount a trade quantity can be incremented by. Field available for crypto only.
	MinTradeIncrement string `protobuf:"bytes,13,opt,name=min_trade_increment,json=minTradeIncrement,proto3" json:"min_trade_increment,omitempty"`
	// amount the price can be incremented by. Field available for crypto only.
	PriceIncrement string `protobuf:"bytes,14,opt,name=price_increment,json=priceIncrement,proto3" json:"price_increment,omitempty"`
	// shows the % margin requirement for the asset (equities only).
	MaintenanceMarginRequirement int32 `protobuf:"varint,15,opt,name=maintenance_margin_requirement,json=maintenanceMarginRequirement,proto3" json:"maintenance_margin_requirement,omitempty"`
	// attributes for Asset e.g. ("ptp_no_exception", "fractional_eh_enabled", "options_enabled")
	Attributes []string `protobuf:"bytes,16,rep,name=attributes,proto3" json:"attributes,omitempty"`
}

func (x *Asset) Reset() {
	*x = Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_stocks_stocks_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Asset) ProtoMessage() {}

func (x *Asset) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_stocks_stocks_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Asset.ProtoReflect.Descriptor instead.
func (*Asset) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_stocks_stocks_proto_rawDescGZIP(), []int{3}
}

func (x *Asset) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Asset) GetClass() AssetClass {
	if x != nil {
		return x.Class
	}
	return AssetClass_ASSET_CLASS_UNSPECIFIED
}

func (x *Asset) GetExchange() AssetExchange {
	if x != nil {
		return x.Exchange
	}
	return AssetExchange_ASSET_EXCHANGE_UNSPECIFIED
}

func (x *Asset) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Asset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Asset) GetStatus() AssetStatus {
	if x != nil {
		return x.Status
	}
	return AssetStatus_ASSET_STATUS_UNSPECIFIED
}

func (x *Asset) GetIsTradable() bool {
	if x != nil {
		return x.IsTradable
	}
	return false
}

func (x *Asset) GetIsMarginable() bool {
	if x != nil {
		return x.IsMarginable
	}
	return false
}

func (x *Asset) GetIsShortable() bool {
	if x != nil {
		return x.IsShortable
	}
	return false
}

func (x *Asset) GetIsEasyToBorrow() bool {
	if x != nil {
		return x.IsEasyToBorrow
	}
	return false
}

func (x *Asset) GetIsFractionable() bool {
	if x != nil {
		return x.IsFractionable
	}
	return false
}

func (x *Asset) GetMinOrderSize() string {
	if x != nil {
		return x.MinOrderSize
	}
	return ""
}

func (x *Asset) GetMinTradeIncrement() string {
	if x != nil {
		return x.MinTradeIncrement
	}
	return ""
}

func (x *Asset) GetPriceIncrement() string {
	if x != nil {
		return x.PriceIncrement
	}
	return ""
}

func (x *Asset) GetMaintenanceMarginRequirement() int32 {
	if x != nil {
		return x.MaintenanceMarginRequirement
	}
	return 0
}

func (x *Asset) GetAttributes() []string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

var File_api_vendorgateway_stocks_stocks_proto protoreflect.FileDescriptor

var file_api_vendorgateway_stocks_stocks_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x1a, 0x24, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd4, 0x05, 0x0a, 0x07, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x2a, 0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26,
	0x0a, 0x0a, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x74, 0x6f, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20,
	0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x4a,
	0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x3d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x53,
	0x0a, 0x11, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73,
	0x2e, 0x43, 0x61, 0x73, 0x68, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x0f, 0x63, 0x61, 0x73, 0x68, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x65, 0x0a, 0x17, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x5f, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x15, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x4a,
	0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x13, 0x0a, 0x0c, 0x6a, 0x6f, 0x75, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x3d, 0x0a,
	0x0f, 0x43, 0x61, 0x73, 0x68, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x6d, 0x0a, 0x15,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x61,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x10, 0x0a,
	0x03, 0x71, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x71, 0x74, 0x79, 0x12,
	0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x99, 0x05, 0x0a, 0x05,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x3f, 0x0a,
	0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x64,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x54, 0x72,
	0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x72,
	0x67, 0x69, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69,
	0x73, 0x4d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x29,
	0x0a, 0x11, 0x69, 0x73, 0x5f, 0x65, 0x61, 0x73, 0x79, 0x5f, 0x74, 0x6f, 0x5f, 0x62, 0x6f, 0x72,
	0x72, 0x6f, 0x77, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x45, 0x61, 0x73,
	0x79, 0x54, 0x6f, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x73, 0x5f,
	0x66, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x46, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x69, 0x6e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x69, 0x6e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x69, 0x6e, 0x5f,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d, 0x69, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x49,
	0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x44, 0x0a, 0x1e, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x6d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1c, 0x6d, 0x61, 0x69, 0x6e, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_stocks_stocks_proto_rawDescOnce sync.Once
	file_api_vendorgateway_stocks_stocks_proto_rawDescData = file_api_vendorgateway_stocks_stocks_proto_rawDesc
)

func file_api_vendorgateway_stocks_stocks_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_stocks_stocks_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_stocks_stocks_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_stocks_stocks_proto_rawDescData)
	})
	return file_api_vendorgateway_stocks_stocks_proto_rawDescData
}

var file_api_vendorgateway_stocks_stocks_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_vendorgateway_stocks_stocks_proto_goTypes = []interface{}{
	(*Journal)(nil),               // 0: vendorgateway.stocks.Journal
	(*CashJournalData)(nil),       // 1: vendorgateway.stocks.CashJournalData
	(*SecuritiesJournalData)(nil), // 2: vendorgateway.stocks.SecuritiesJournalData
	(*Asset)(nil),                 // 3: vendorgateway.stocks.Asset
	(JournalType)(0),              // 4: vendorgateway.stocks.JournalType
	(JournalStatus)(0),            // 5: vendorgateway.stocks.JournalStatus
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
	(*money.Money)(nil),           // 7: google.type.Money
	(AssetClass)(0),               // 8: vendorgateway.stocks.AssetClass
	(AssetExchange)(0),            // 9: vendorgateway.stocks.AssetExchange
	(AssetStatus)(0),              // 10: vendorgateway.stocks.AssetStatus
}
var file_api_vendorgateway_stocks_stocks_proto_depIdxs = []int32{
	4,  // 0: vendorgateway.stocks.Journal.type:type_name -> vendorgateway.stocks.JournalType
	5,  // 1: vendorgateway.stocks.Journal.status:type_name -> vendorgateway.stocks.JournalStatus
	6,  // 2: vendorgateway.stocks.Journal.settled_at:type_name -> google.protobuf.Timestamp
	6,  // 3: vendorgateway.stocks.Journal.created_at:type_name -> google.protobuf.Timestamp
	1,  // 4: vendorgateway.stocks.Journal.cash_journal_data:type_name -> vendorgateway.stocks.CashJournalData
	2,  // 5: vendorgateway.stocks.Journal.securities_journal_data:type_name -> vendorgateway.stocks.SecuritiesJournalData
	6,  // 6: vendorgateway.stocks.Journal.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 7: vendorgateway.stocks.CashJournalData.amount:type_name -> google.type.Money
	7,  // 8: vendorgateway.stocks.SecuritiesJournalData.amount:type_name -> google.type.Money
	8,  // 9: vendorgateway.stocks.Asset.class:type_name -> vendorgateway.stocks.AssetClass
	9,  // 10: vendorgateway.stocks.Asset.exchange:type_name -> vendorgateway.stocks.AssetExchange
	10, // 11: vendorgateway.stocks.Asset.status:type_name -> vendorgateway.stocks.AssetStatus
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_stocks_stocks_proto_init() }
func file_api_vendorgateway_stocks_stocks_proto_init() {
	if File_api_vendorgateway_stocks_stocks_proto != nil {
		return
	}
	file_api_vendorgateway_stocks_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_stocks_stocks_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Journal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_stocks_stocks_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CashJournalData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_stocks_stocks_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecuritiesJournalData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_stocks_stocks_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_vendorgateway_stocks_stocks_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Journal_CashJournalData)(nil),
		(*Journal_SecuritiesJournalData)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_stocks_stocks_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_stocks_stocks_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_stocks_stocks_proto_depIdxs,
		MessageInfos:      file_api_vendorgateway_stocks_stocks_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_stocks_stocks_proto = out.File
	file_api_vendorgateway_stocks_stocks_proto_rawDesc = nil
	file_api_vendorgateway_stocks_stocks_proto_goTypes = nil
	file_api_vendorgateway_stocks_stocks_proto_depIdxs = nil
}
