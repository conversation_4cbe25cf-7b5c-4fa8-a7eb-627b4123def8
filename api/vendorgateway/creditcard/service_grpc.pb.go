// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/creditcard/service.proto

package creditcard

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CreditCard_GenerateCreditCardSdkAuthToken_FullMethodName = "/vendorgateway.creditcard.CreditCard/GenerateCreditCardSdkAuthToken"
	CreditCard_UpdateCreditCardDeliveryState_FullMethodName  = "/vendorgateway.creditcard.CreditCard/UpdateCreditCardDeliveryState"
)

// CreditCardClient is the client API for CreditCard service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CreditCardClient interface {
	// RPC to generate an authentication token for client to auth with credit card SDK
	GenerateCreditCardSdkAuthToken(ctx context.Context, in *GenerateCreditCardSdkAuthTokenRequest, opts ...grpc.CallOption) (*GenerateCreditCardSdkAuthTokenResponse, error)
	// RPC to update credit card delivery info at credit card vendor.
	UpdateCreditCardDeliveryState(ctx context.Context, in *UpdateCreditCardDeliveryStateRequest, opts ...grpc.CallOption) (*UpdateCreditCardDeliveryStateResponse, error)
}

type creditCardClient struct {
	cc grpc.ClientConnInterface
}

func NewCreditCardClient(cc grpc.ClientConnInterface) CreditCardClient {
	return &creditCardClient{cc}
}

func (c *creditCardClient) GenerateCreditCardSdkAuthToken(ctx context.Context, in *GenerateCreditCardSdkAuthTokenRequest, opts ...grpc.CallOption) (*GenerateCreditCardSdkAuthTokenResponse, error) {
	out := new(GenerateCreditCardSdkAuthTokenResponse)
	err := c.cc.Invoke(ctx, CreditCard_GenerateCreditCardSdkAuthToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditCardClient) UpdateCreditCardDeliveryState(ctx context.Context, in *UpdateCreditCardDeliveryStateRequest, opts ...grpc.CallOption) (*UpdateCreditCardDeliveryStateResponse, error) {
	out := new(UpdateCreditCardDeliveryStateResponse)
	err := c.cc.Invoke(ctx, CreditCard_UpdateCreditCardDeliveryState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CreditCardServer is the server API for CreditCard service.
// All implementations should embed UnimplementedCreditCardServer
// for forward compatibility
type CreditCardServer interface {
	// RPC to generate an authentication token for client to auth with credit card SDK
	GenerateCreditCardSdkAuthToken(context.Context, *GenerateCreditCardSdkAuthTokenRequest) (*GenerateCreditCardSdkAuthTokenResponse, error)
	// RPC to update credit card delivery info at credit card vendor.
	UpdateCreditCardDeliveryState(context.Context, *UpdateCreditCardDeliveryStateRequest) (*UpdateCreditCardDeliveryStateResponse, error)
}

// UnimplementedCreditCardServer should be embedded to have forward compatible implementations.
type UnimplementedCreditCardServer struct {
}

func (UnimplementedCreditCardServer) GenerateCreditCardSdkAuthToken(context.Context, *GenerateCreditCardSdkAuthTokenRequest) (*GenerateCreditCardSdkAuthTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateCreditCardSdkAuthToken not implemented")
}
func (UnimplementedCreditCardServer) UpdateCreditCardDeliveryState(context.Context, *UpdateCreditCardDeliveryStateRequest) (*UpdateCreditCardDeliveryStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCreditCardDeliveryState not implemented")
}

// UnsafeCreditCardServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CreditCardServer will
// result in compilation errors.
type UnsafeCreditCardServer interface {
	mustEmbedUnimplementedCreditCardServer()
}

func RegisterCreditCardServer(s grpc.ServiceRegistrar, srv CreditCardServer) {
	s.RegisterService(&CreditCard_ServiceDesc, srv)
}

func _CreditCard_GenerateCreditCardSdkAuthToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateCreditCardSdkAuthTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditCardServer).GenerateCreditCardSdkAuthToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CreditCard_GenerateCreditCardSdkAuthToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditCardServer).GenerateCreditCardSdkAuthToken(ctx, req.(*GenerateCreditCardSdkAuthTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditCard_UpdateCreditCardDeliveryState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCreditCardDeliveryStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditCardServer).UpdateCreditCardDeliveryState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CreditCard_UpdateCreditCardDeliveryState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditCardServer).UpdateCreditCardDeliveryState(ctx, req.(*UpdateCreditCardDeliveryStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CreditCard_ServiceDesc is the grpc.ServiceDesc for CreditCard service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CreditCard_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.creditcard.CreditCard",
	HandlerType: (*CreditCardServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateCreditCardSdkAuthToken",
			Handler:    _CreditCard_GenerateCreditCardSdkAuthToken_Handler,
		},
		{
			MethodName: "UpdateCreditCardDeliveryState",
			Handler:    _CreditCard_UpdateCreditCardDeliveryState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/creditcard/service.proto",
}
