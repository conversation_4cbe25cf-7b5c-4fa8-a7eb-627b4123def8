// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/cryptor.proto

package vendorgateway

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Defines possible method to be used by the cryptor
// while constructing a vendor request
type RequestProcessingMethod int32

const (
	RequestProcessingMethod_REQUEST_PROCESSING_METHOD_UNSPECIFIED RequestProcessingMethod = 0
	// Digital signatures are the public-key primitives of message authentication.
	// In the physical world, it is common to use handwritten signatures on handwritten or typed messages.
	// They are used to bind signatory to the message.
	// Similarly, a digital signature is a technique that binds a person/entity to the digital data.
	// This binding can be independently verified by receiver as well as any third party.
	// Digital signature is a cryptographic value that is calculated from the data and a secret key known only by the
	// signer.
	//
	// The payload is digitally signed by the cryptor using epiFi's private key and vendor uses epiFi's public key
	// to verify the authenticity of the request
	RequestProcessingMethod_SIGN RequestProcessingMethod = 1
	// Encryption is the process of encoding information.
	// This process converts the original representation of the information, known as plaintext,
	// into an alternative form known as ciphertext. Only authorized parties can decipher a ciphertext back to plaintext
	// and access the original information. Encryption does not itself prevent interference but denies the intelligible
	// content to a would-be interceptor.
	// An authorized recipient can easily decrypt the message with the key provided by the originator to recipients
	// but not to unauthorized users.
	//
	// The payload is encrypted using vendor's public key before sending it to the vendors. Hence, only and only
	// the recipient vendor who has the private key can decode the payload
	RequestProcessingMethod_ENCRYPT RequestProcessingMethod = 2
	// With this approach, the sender(epiFi) digitally signs the data using the private key,
	// appends the signature to the data, and then encrypts the data and the digital signature using
	// the receiver's(vendor) public key.
	// This is considered a more secure scheme as compared to the other approaches.
	RequestProcessingMethod_SIGN_AND_ENCRYPT RequestProcessingMethod = 3
)

// Enum value maps for RequestProcessingMethod.
var (
	RequestProcessingMethod_name = map[int32]string{
		0: "REQUEST_PROCESSING_METHOD_UNSPECIFIED",
		1: "SIGN",
		2: "ENCRYPT",
		3: "SIGN_AND_ENCRYPT",
	}
	RequestProcessingMethod_value = map[string]int32{
		"REQUEST_PROCESSING_METHOD_UNSPECIFIED": 0,
		"SIGN":                                  1,
		"ENCRYPT":                               2,
		"SIGN_AND_ENCRYPT":                      3,
	}
)

func (x RequestProcessingMethod) Enum() *RequestProcessingMethod {
	p := new(RequestProcessingMethod)
	*p = x
	return p
}

func (x RequestProcessingMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestProcessingMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_cryptor_proto_enumTypes[0].Descriptor()
}

func (RequestProcessingMethod) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_cryptor_proto_enumTypes[0]
}

func (x RequestProcessingMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RequestProcessingMethod.Descriptor instead.
func (RequestProcessingMethod) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_cryptor_proto_rawDescGZIP(), []int{0}
}

// Defines possible methods to be used by the cryptor
// while processing a vendor response
// Usually the response processing goes in hand with the request processing
// e.g. if the request in ENCRYPTED then its corresponding response in DECRYPTED
// But there can be cases where vendor requests are signed but corresponding ack
// messages are not
type ResponseProcessingMethod int32

const (
	ResponseProcessingMethod_RESPONSE_PROCESSING_METHOD_UNSPECIFIED ResponseProcessingMethod = 0
	// The response sent back from the vendors is also digitally signed by the vendor using their private key.
	// The cryptor uses a signature verifying algorithm that, given the message, public key and signature,
	// either accepts or rejects the message's claim to authenticity.
	ResponseProcessingMethod_VERIFY ResponseProcessingMethod = 1
	// Decryption is the process of transforming encrypted information so that it is intelligible again.
	// The response sent back from the vendor is also encrypted, hence, the cryptor needs to decrypt it using epiFi's
	// private key
	ResponseProcessingMethod_DECRYPT ResponseProcessingMethod = 2
	// The response sent back from the vendors is signed first by vendor's private key and encrypted epiFi's public key.
	// The cryptor in this case using epiFi's private key to decrypt the payload first and then verify
	// signature with vendor's public key
	ResponseProcessingMethod_DECRYPT_AND_VERIFY ResponseProcessingMethod = 3
)

// Enum value maps for ResponseProcessingMethod.
var (
	ResponseProcessingMethod_name = map[int32]string{
		0: "RESPONSE_PROCESSING_METHOD_UNSPECIFIED",
		1: "VERIFY",
		2: "DECRYPT",
		3: "DECRYPT_AND_VERIFY",
	}
	ResponseProcessingMethod_value = map[string]int32{
		"RESPONSE_PROCESSING_METHOD_UNSPECIFIED": 0,
		"VERIFY":                                 1,
		"DECRYPT":                                2,
		"DECRYPT_AND_VERIFY":                     3,
	}
)

func (x ResponseProcessingMethod) Enum() *ResponseProcessingMethod {
	p := new(ResponseProcessingMethod)
	*p = x
	return p
}

func (x ResponseProcessingMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResponseProcessingMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_cryptor_proto_enumTypes[1].Descriptor()
}

func (ResponseProcessingMethod) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_cryptor_proto_enumTypes[1]
}

func (x ResponseProcessingMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResponseProcessingMethod.Descriptor instead.
func (ResponseProcessingMethod) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_cryptor_proto_rawDescGZIP(), []int{1}
}

// Cryptor type
type CryptorType int32

const (
	CryptorType_CRYPTOR_UNSPECIFIED CryptorType = 0
	// PGP encryption or Pretty Good Privacy encryption, is a data encryption computer program that gives cryptographic
	// privacy and authentication for online communication.
	//
	// It is often used to encrypt and decrypt texts, emails, and files to increase the security of emails.
	// PGP encryption uses a mix of data compression, hashing, and public-key cryptography.
	// It also uses symmetric and asymmetric keys to encrypt data that is transferred across networks.
	// It combines features of private and public key cryptography.
	//
	// PGP combines private-key and public-key encryption. The sender encrypts the message using a public encryption
	// algorithm provided by the receiver.
	// The receiver provides their personal public-key to whomever they would like to receive messages from.
	// This is done to protect the message during transmission. Once the recipient receives the message,
	// they use their own private-key to decode the message, while keeping their personal private-key a secret from outsiders.
	// PGP ensures message authentication and integrity checking.
	// Integrity checking is used to detect if a message has been altered after it was written and to determine if it was
	// actually sent by the claimed sender
	CryptorType_PGP CryptorType = 1
	// AES with Galois/Counter Mode (AES-GCM) provides both authenticated encryption (confidentiality and authentication)
	// and the ability to check the integrity and authentication of additional authenticated data (AAD) that is sent in the clear.
	// There are four inputs for authenticated encryption: the secret key, initialization vector (IV) (sometimes called a nonce),
	// the plaintext itself, and optional additional authentication data (AAD).The nonce and AAD are passed in the clear.
	// There are two outputs: the ciphertext, which is exactly the same length as the plaintext, and an authentication tag (the "tag").
	// The tag is sometimes called the message authentication code (MAC) or integrity check value (ICV).
	CryptorType_AES_GCM CryptorType = 2
	// In RSA cryptography, both the public and the private keys can encrypt a message; the opposite key from the one used
	// to encrypt a message is used to decrypt it. This attribute is one reason why RSA has become the most widely used
	// asymmetric algorithm: It provides a method to assure the confidentiality, integrity, authenticity, and
	// non-repudiation of electronic communications and data storage.
	CryptorType_RSA CryptorType = 3
)

// Enum value maps for CryptorType.
var (
	CryptorType_name = map[int32]string{
		0: "CRYPTOR_UNSPECIFIED",
		1: "PGP",
		2: "AES_GCM",
		3: "RSA",
	}
	CryptorType_value = map[string]int32{
		"CRYPTOR_UNSPECIFIED": 0,
		"PGP":                 1,
		"AES_GCM":             2,
		"RSA":                 3,
	}
)

func (x CryptorType) Enum() *CryptorType {
	p := new(CryptorType)
	*p = x
	return p
}

func (x CryptorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CryptorType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_cryptor_proto_enumTypes[2].Descriptor()
}

func (CryptorType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_cryptor_proto_enumTypes[2]
}

func (x CryptorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CryptorType.Descriptor instead.
func (CryptorType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_cryptor_proto_rawDescGZIP(), []int{2}
}

var File_api_vendorgateway_cryptor_proto protoreflect.FileDescriptor

var file_api_vendorgateway_cryptor_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2a, 0x71, 0x0a, 0x17, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x29, 0x0a, 0x25, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e,
	0x47, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x49, 0x47, 0x4e, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54, 0x10, 0x02, 0x12, 0x14, 0x0a,
	0x10, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50,
	0x54, 0x10, 0x03, 0x2a, 0x77, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12,
	0x2a, 0x0a, 0x26, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x59, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x43, 0x52, 0x59,
	0x50, 0x54, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x45, 0x43, 0x52, 0x59, 0x50, 0x54, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x10, 0x03, 0x2a, 0x45, 0x0a, 0x0b,
	0x43, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x43,
	0x52, 0x59, 0x50, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x47, 0x50, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x41, 0x45, 0x53, 0x5f, 0x47, 0x43, 0x4d, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x52, 0x53,
	0x41, 0x10, 0x03, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x62, 0x65, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x62, 0x65, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_cryptor_proto_rawDescOnce sync.Once
	file_api_vendorgateway_cryptor_proto_rawDescData = file_api_vendorgateway_cryptor_proto_rawDesc
)

func file_api_vendorgateway_cryptor_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_cryptor_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_cryptor_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_cryptor_proto_rawDescData)
	})
	return file_api_vendorgateway_cryptor_proto_rawDescData
}

var file_api_vendorgateway_cryptor_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_vendorgateway_cryptor_proto_goTypes = []interface{}{
	(RequestProcessingMethod)(0),  // 0: vendorgateway.RequestProcessingMethod
	(ResponseProcessingMethod)(0), // 1: vendorgateway.ResponseProcessingMethod
	(CryptorType)(0),              // 2: vendorgateway.CryptorType
}
var file_api_vendorgateway_cryptor_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_cryptor_proto_init() }
func file_api_vendorgateway_cryptor_proto_init() {
	if File_api_vendorgateway_cryptor_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_cryptor_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_cryptor_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_cryptor_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_cryptor_proto_enumTypes,
	}.Build()
	File_api_vendorgateway_cryptor_proto = out.File
	file_api_vendorgateway_cryptor_proto_rawDesc = nil
	file_api_vendorgateway_cryptor_proto_goTypes = nil
	file_api_vendorgateway_cryptor_proto_depIdxs = nil
}
