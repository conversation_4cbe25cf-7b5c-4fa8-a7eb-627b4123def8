// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/omegle/call_info.proto

package omegle

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/omegle/enums"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = enums.OverallStatus(0)

	_ = typesv2.Verdict(0)
)

// Validate checks the field values on CallInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CallInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CallInfoMultiError, or nil
// if none found.
func (m *CallInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CallInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CallId

	// no validation rules for ApplicationId

	// no validation rules for OverallCallStatus

	if all {
		switch v := interface{}(m.GetPreCallInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "PreCallInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "PreCallInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreCallInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallInfoValidationError{
				field:  "PreCallInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallDataDump()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "CallDataDump",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "CallDataDump",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallDataDump()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallInfoValidationError{
				field:  "CallDataDump",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuditorReview()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "AuditorReview",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "AuditorReview",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuditorReview()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallInfoValidationError{
				field:  "AuditorReview",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CallPriority

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CallSubStatus

	if all {
		switch v := interface{}(m.GetCallEnded()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "CallEnded",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallInfoValidationError{
					field:  "CallEnded",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallEnded()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallInfoValidationError{
				field:  "CallEnded",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CallInfoMultiError(errors)
	}

	return nil
}

// CallInfoMultiError is an error wrapping multiple validation errors returned
// by CallInfo.ValidateAll() if the designated constraints aren't met.
type CallInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallInfoMultiError) AllErrors() []error { return m }

// CallInfoValidationError is the validation error returned by
// CallInfo.Validate if the designated constraints aren't met.
type CallInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallInfoValidationError) ErrorName() string { return "CallInfoValidationError" }

// Error satisfies the builtin error interface
func (e CallInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallInfoValidationError{}

// Validate checks the field values on ReviewerDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewerDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewerDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewerDetailsMultiError, or nil if none found.
func (m *ReviewerDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewerDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewerDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewerDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewerDetailsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for Id

	// no validation rules for UserName

	if len(errors) > 0 {
		return ReviewerDetailsMultiError(errors)
	}

	return nil
}

// ReviewerDetailsMultiError is an error wrapping multiple validation errors
// returned by ReviewerDetails.ValidateAll() if the designated constraints
// aren't met.
type ReviewerDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewerDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewerDetailsMultiError) AllErrors() []error { return m }

// ReviewerDetailsValidationError is the validation error returned by
// ReviewerDetails.Validate if the designated constraints aren't met.
type ReviewerDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewerDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewerDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewerDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewerDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewerDetailsValidationError) ErrorName() string { return "ReviewerDetailsValidationError" }

// Error satisfies the builtin error interface
func (e ReviewerDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewerDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewerDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewerDetailsValidationError{}

// Validate checks the field values on CallDataDump with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CallDataDump) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallDataDump with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CallDataDumpMultiError, or
// nil if none found.
func (m *CallDataDump) ValidateAll() error {
	return m.validate(true)
}

func (m *CallDataDump) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentVerdict

	// no validation rules for AgentRemarks

	if all {
		switch v := interface{}(m.GetFailure()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "Failure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "Failure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFailure()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "Failure",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "CallStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "CallStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "CallStartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallDataDumpValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallDataDumpValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallDataDumpValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAgentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "AgentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "AgentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAgentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "AgentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAgentDashCallRecording()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "AgentDashCallRecording",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "AgentDashCallRecording",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAgentDashCallRecording()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "AgentDashCallRecording",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCapturedDocuments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallDataDumpValidationError{
						field:  fmt.Sprintf("CapturedDocuments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallDataDumpValidationError{
						field:  fmt.Sprintf("CapturedDocuments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallDataDumpValidationError{
					field:  fmt.Sprintf("CapturedDocuments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCallEndedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "CallEndedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "CallEndedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallEndedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "CallEndedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CallDuration

	for idx, item := range m.GetSummary() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallDataDumpValidationError{
						field:  fmt.Sprintf("Summary[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallDataDumpValidationError{
						field:  fmt.Sprintf("Summary[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallDataDumpValidationError{
					field:  fmt.Sprintf("Summary[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPanMatchResults()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "PanMatchResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "PanMatchResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanMatchResults()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "PanMatchResults",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPassportMatchResults()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "PassportMatchResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "PassportMatchResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPassportMatchResults()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "PassportMatchResults",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAadhaarMatchResults()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "AadhaarMatchResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "AadhaarMatchResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAadhaarMatchResults()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "AadhaarMatchResults",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCountryIdMatchResults()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "CountryIdMatchResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "CountryIdMatchResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCountryIdMatchResults()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "CountryIdMatchResults",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIPDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "IPDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "IPDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIPDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "IPDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserCoordinatesAtCallStart()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "UserCoordinatesAtCallStart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "UserCoordinatesAtCallStart",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserCoordinatesAtCallStart()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "UserCoordinatesAtCallStart",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SharedToVendor

	if all {
		switch v := interface{}(m.GetCkycDocumentMatchResults()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "CkycDocumentMatchResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallDataDumpValidationError{
					field:  "CkycDocumentMatchResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCkycDocumentMatchResults()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallDataDumpValidationError{
				field:  "CkycDocumentMatchResults",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetCallRemarksInfoMap()))
		i := 0
		for key := range m.GetCallRemarksInfoMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCallRemarksInfoMap()[key]
			_ = val

			// no validation rules for CallRemarksInfoMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, CallDataDumpValidationError{
							field:  fmt.Sprintf("CallRemarksInfoMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, CallDataDumpValidationError{
							field:  fmt.Sprintf("CallRemarksInfoMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return CallDataDumpValidationError{
						field:  fmt.Sprintf("CallRemarksInfoMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return CallDataDumpMultiError(errors)
	}

	return nil
}

// CallDataDumpMultiError is an error wrapping multiple validation errors
// returned by CallDataDump.ValidateAll() if the designated constraints aren't met.
type CallDataDumpMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallDataDumpMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallDataDumpMultiError) AllErrors() []error { return m }

// CallDataDumpValidationError is the validation error returned by
// CallDataDump.Validate if the designated constraints aren't met.
type CallDataDumpValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallDataDumpValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallDataDumpValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallDataDumpValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallDataDumpValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallDataDumpValidationError) ErrorName() string { return "CallDataDumpValidationError" }

// Error satisfies the builtin error interface
func (e CallDataDumpValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallDataDump.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallDataDumpValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallDataDumpValidationError{}

// Validate checks the field values on RemarksInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RemarksInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemarksInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RemarksInfoMultiError, or
// nil if none found.
func (m *RemarksInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RemarksInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Remarks

	if len(errors) > 0 {
		return RemarksInfoMultiError(errors)
	}

	return nil
}

// RemarksInfoMultiError is an error wrapping multiple validation errors
// returned by RemarksInfo.ValidateAll() if the designated constraints aren't met.
type RemarksInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemarksInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemarksInfoMultiError) AllErrors() []error { return m }

// RemarksInfoValidationError is the validation error returned by
// RemarksInfo.Validate if the designated constraints aren't met.
type RemarksInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemarksInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemarksInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemarksInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemarksInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemarksInfoValidationError) ErrorName() string { return "RemarksInfoValidationError" }

// Error satisfies the builtin error interface
func (e RemarksInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemarksInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemarksInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemarksInfoValidationError{}

// Validate checks the field values on CkycDocumentMatchResults with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CkycDocumentMatchResults) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CkycDocumentMatchResults with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CkycDocumentMatchResultsMultiError, or nil if none found.
func (m *CkycDocumentMatchResults) ValidateAll() error {
	return m.validate(true)
}

func (m *CkycDocumentMatchResults) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFace()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CkycDocumentMatchResultsValidationError{
					field:  "Face",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CkycDocumentMatchResultsValidationError{
					field:  "Face",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFace()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CkycDocumentMatchResultsValidationError{
				field:  "Face",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CkycDocumentMatchResultsMultiError(errors)
	}

	return nil
}

// CkycDocumentMatchResultsMultiError is an error wrapping multiple validation
// errors returned by CkycDocumentMatchResults.ValidateAll() if the designated
// constraints aren't met.
type CkycDocumentMatchResultsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CkycDocumentMatchResultsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CkycDocumentMatchResultsMultiError) AllErrors() []error { return m }

// CkycDocumentMatchResultsValidationError is the validation error returned by
// CkycDocumentMatchResults.Validate if the designated constraints aren't met.
type CkycDocumentMatchResultsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CkycDocumentMatchResultsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CkycDocumentMatchResultsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CkycDocumentMatchResultsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CkycDocumentMatchResultsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CkycDocumentMatchResultsValidationError) ErrorName() string {
	return "CkycDocumentMatchResultsValidationError"
}

// Error satisfies the builtin error interface
func (e CkycDocumentMatchResultsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCkycDocumentMatchResults.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CkycDocumentMatchResultsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CkycDocumentMatchResultsValidationError{}

// Validate checks the field values on AuditorReview with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AuditorReview) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuditorReview with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AuditorReviewMultiError, or
// nil if none found.
func (m *AuditorReview) ValidateAll() error {
	return m.validate(true)
}

func (m *AuditorReview) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Verdict

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetDecisionCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuditorReviewValidationError{
					field:  "DecisionCompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuditorReviewValidationError{
					field:  "DecisionCompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDecisionCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuditorReviewValidationError{
				field:  "DecisionCompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuditorDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuditorReviewValidationError{
					field:  "AuditorDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuditorReviewValidationError{
					field:  "AuditorDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuditorDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuditorReviewValidationError{
				field:  "AuditorDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuditorAssignedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuditorReviewValidationError{
					field:  "AuditorAssignedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuditorReviewValidationError{
					field:  "AuditorAssignedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuditorAssignedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuditorReviewValidationError{
				field:  "AuditorAssignedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SharedToVendor

	// no validation rules for RejectionReason

	if len(errors) > 0 {
		return AuditorReviewMultiError(errors)
	}

	return nil
}

// AuditorReviewMultiError is an error wrapping multiple validation errors
// returned by AuditorReview.ValidateAll() if the designated constraints
// aren't met.
type AuditorReviewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuditorReviewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuditorReviewMultiError) AllErrors() []error { return m }

// AuditorReviewValidationError is the validation error returned by
// AuditorReview.Validate if the designated constraints aren't met.
type AuditorReviewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuditorReviewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuditorReviewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuditorReviewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuditorReviewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuditorReviewValidationError) ErrorName() string { return "AuditorReviewValidationError" }

// Error satisfies the builtin error interface
func (e AuditorReviewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuditorReview.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuditorReviewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuditorReviewValidationError{}

// Validate checks the field values on PreCallInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PreCallInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreCallInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PreCallInfoMultiError, or
// nil if none found.
func (m *PreCallInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PreCallInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocationToken

	// no validation rules for IPAddress

	if all {
		switch v := interface{}(m.GetLocationRecordedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreCallInfoValidationError{
					field:  "LocationRecordedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreCallInfoValidationError{
					field:  "LocationRecordedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLocationRecordedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreCallInfoValidationError{
				field:  "LocationRecordedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsProxyDetected

	// no validation rules for IpOrigination

	// no validation rules for IpRisk

	// no validation rules for UserAgentString

	if all {
		switch v := interface{}(m.GetUserDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreCallInfoValidationError{
					field:  "UserDeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreCallInfoValidationError{
					field:  "UserDeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreCallInfoValidationError{
				field:  "UserDeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IPToken

	if len(errors) > 0 {
		return PreCallInfoMultiError(errors)
	}

	return nil
}

// PreCallInfoMultiError is an error wrapping multiple validation errors
// returned by PreCallInfo.ValidateAll() if the designated constraints aren't met.
type PreCallInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreCallInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreCallInfoMultiError) AllErrors() []error { return m }

// PreCallInfoValidationError is the validation error returned by
// PreCallInfo.Validate if the designated constraints aren't met.
type PreCallInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreCallInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreCallInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreCallInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreCallInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreCallInfoValidationError) ErrorName() string { return "PreCallInfoValidationError" }

// Error satisfies the builtin error interface
func (e PreCallInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreCallInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreCallInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreCallInfoValidationError{}

// Validate checks the field values on Question with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Question) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Question with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QuestionMultiError, or nil
// if none found.
func (m *Question) ValidateAll() error {
	return m.validate(true)
}

func (m *Question) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Question

	// no validation rules for Verdict

	// no validation rules for Remark

	// no validation rules for ExpectedAnswer

	// no validation rules for QuestionType

	if len(errors) > 0 {
		return QuestionMultiError(errors)
	}

	return nil
}

// QuestionMultiError is an error wrapping multiple validation errors returned
// by Question.ValidateAll() if the designated constraints aren't met.
type QuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionMultiError) AllErrors() []error { return m }

// QuestionValidationError is the validation error returned by
// Question.Validate if the designated constraints aren't met.
type QuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionValidationError) ErrorName() string { return "QuestionValidationError" }

// Error satisfies the builtin error interface
func (e QuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionValidationError{}

// Validate checks the field values on Failure with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Failure) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Failure with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FailureMultiError, or nil if none found.
func (m *Failure) ValidateAll() error {
	return m.validate(true)
}

func (m *Failure) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FailureReason

	// no validation rules for RawFailureReason

	if len(errors) > 0 {
		return FailureMultiError(errors)
	}

	return nil
}

// FailureMultiError is an error wrapping multiple validation errors returned
// by Failure.ValidateAll() if the designated constraints aren't met.
type FailureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FailureMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FailureMultiError) AllErrors() []error { return m }

// FailureValidationError is the validation error returned by Failure.Validate
// if the designated constraints aren't met.
type FailureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FailureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FailureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FailureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FailureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FailureValidationError) ErrorName() string { return "FailureValidationError" }

// Error satisfies the builtin error interface
func (e FailureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFailure.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FailureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FailureValidationError{}

// Validate checks the field values on CapturedDocument with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CapturedDocument) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CapturedDocument with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CapturedDocumentMultiError, or nil if none found.
func (m *CapturedDocument) ValidateAll() error {
	return m.validate(true)
}

func (m *CapturedDocument) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CapturedDocumentValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CapturedDocumentValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CapturedDocumentValidationError{
				field:  "File",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CapturedDocumentType

	// no validation rules for Remark

	// no validation rules for Verdict

	// no validation rules for MatchScore

	if len(errors) > 0 {
		return CapturedDocumentMultiError(errors)
	}

	return nil
}

// CapturedDocumentMultiError is an error wrapping multiple validation errors
// returned by CapturedDocument.ValidateAll() if the designated constraints
// aren't met.
type CapturedDocumentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CapturedDocumentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CapturedDocumentMultiError) AllErrors() []error { return m }

// CapturedDocumentValidationError is the validation error returned by
// CapturedDocument.Validate if the designated constraints aren't met.
type CapturedDocumentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CapturedDocumentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CapturedDocumentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CapturedDocumentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CapturedDocumentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CapturedDocumentValidationError) ErrorName() string { return "CapturedDocumentValidationError" }

// Error satisfies the builtin error interface
func (e CapturedDocumentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCapturedDocument.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CapturedDocumentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CapturedDocumentValidationError{}

// Validate checks the field values on PanMatchResults with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PanMatchResults) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PanMatchResults with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PanMatchResultsMultiError, or nil if none found.
func (m *PanMatchResults) ValidateAll() error {
	return m.validate(true)
}

func (m *PanMatchResults) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanMatchResultsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanMatchResultsValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanMatchResultsValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Id",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Id",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanMatchResultsValidationError{
				field:  "Id",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanMatchResultsValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhone()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhone()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanMatchResultsValidationError{
				field:  "Phone",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanMatchResultsValidationError{
				field:  "Email",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatusCheck()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "StatusCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "StatusCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusCheck()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanMatchResultsValidationError{
				field:  "StatusCheck",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFace()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Face",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanMatchResultsValidationError{
					field:  "Face",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFace()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanMatchResultsValidationError{
				field:  "Face",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PanMatchResultsMultiError(errors)
	}

	return nil
}

// PanMatchResultsMultiError is an error wrapping multiple validation errors
// returned by PanMatchResults.ValidateAll() if the designated constraints
// aren't met.
type PanMatchResultsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PanMatchResultsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PanMatchResultsMultiError) AllErrors() []error { return m }

// PanMatchResultsValidationError is the validation error returned by
// PanMatchResults.Validate if the designated constraints aren't met.
type PanMatchResultsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PanMatchResultsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PanMatchResultsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PanMatchResultsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PanMatchResultsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PanMatchResultsValidationError) ErrorName() string { return "PanMatchResultsValidationError" }

// Error satisfies the builtin error interface
func (e PanMatchResultsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPanMatchResults.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PanMatchResultsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PanMatchResultsValidationError{}

// Validate checks the field values on PassportMatchResults with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PassportMatchResults) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PassportMatchResults with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PassportMatchResultsMultiError, or nil if none found.
func (m *PassportMatchResults) ValidateAll() error {
	return m.validate(true)
}

func (m *PassportMatchResults) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportMatchResultsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNationality()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Nationality",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Nationality",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNationality()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportMatchResultsValidationError{
				field:  "Nationality",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportMatchResultsValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Id",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Id",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportMatchResultsValidationError{
				field:  "Id",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportMatchResultsValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhone()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhone()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportMatchResultsValidationError{
				field:  "Phone",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportMatchResultsValidationError{
				field:  "Email",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatusCheck()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "StatusCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "StatusCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusCheck()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportMatchResultsValidationError{
				field:  "StatusCheck",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGender()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Gender",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Gender",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGender()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportMatchResultsValidationError{
				field:  "Gender",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFace()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Face",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportMatchResultsValidationError{
					field:  "Face",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFace()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportMatchResultsValidationError{
				field:  "Face",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PassportMatchResultsMultiError(errors)
	}

	return nil
}

// PassportMatchResultsMultiError is an error wrapping multiple validation
// errors returned by PassportMatchResults.ValidateAll() if the designated
// constraints aren't met.
type PassportMatchResultsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PassportMatchResultsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PassportMatchResultsMultiError) AllErrors() []error { return m }

// PassportMatchResultsValidationError is the validation error returned by
// PassportMatchResults.Validate if the designated constraints aren't met.
type PassportMatchResultsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PassportMatchResultsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PassportMatchResultsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PassportMatchResultsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PassportMatchResultsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PassportMatchResultsValidationError) ErrorName() string {
	return "PassportMatchResultsValidationError"
}

// Error satisfies the builtin error interface
func (e PassportMatchResultsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPassportMatchResults.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PassportMatchResultsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PassportMatchResultsValidationError{}

// Validate checks the field values on AadhaarMatchResults with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AadhaarMatchResults) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AadhaarMatchResults with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AadhaarMatchResultsMultiError, or nil if none found.
func (m *AadhaarMatchResults) ValidateAll() error {
	return m.validate(true)
}

func (m *AadhaarMatchResults) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AadhaarMatchResultsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AadhaarMatchResultsValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "CurrentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "CurrentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AadhaarMatchResultsValidationError{
				field:  "CurrentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Id",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Id",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AadhaarMatchResultsValidationError{
				field:  "Id",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AadhaarMatchResultsValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AadhaarMatchResultsValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AadhaarMatchResultsValidationError{
				field:  "Email",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGenDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "GenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "GenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGenDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AadhaarMatchResultsValidationError{
				field:  "GenDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGender()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Gender",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AadhaarMatchResultsValidationError{
					field:  "Gender",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGender()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AadhaarMatchResultsValidationError{
				field:  "Gender",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AadhaarMatchResultsMultiError(errors)
	}

	return nil
}

// AadhaarMatchResultsMultiError is an error wrapping multiple validation
// errors returned by AadhaarMatchResults.ValidateAll() if the designated
// constraints aren't met.
type AadhaarMatchResultsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AadhaarMatchResultsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AadhaarMatchResultsMultiError) AllErrors() []error { return m }

// AadhaarMatchResultsValidationError is the validation error returned by
// AadhaarMatchResults.Validate if the designated constraints aren't met.
type AadhaarMatchResultsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AadhaarMatchResultsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AadhaarMatchResultsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AadhaarMatchResultsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AadhaarMatchResultsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AadhaarMatchResultsValidationError) ErrorName() string {
	return "AadhaarMatchResultsValidationError"
}

// Error satisfies the builtin error interface
func (e AadhaarMatchResultsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAadhaarMatchResults.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AadhaarMatchResultsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AadhaarMatchResultsValidationError{}

// Validate checks the field values on CountryIdMatchResults with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountryIdMatchResults) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountryIdMatchResults with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountryIdMatchResultsMultiError, or nil if none found.
func (m *CountryIdMatchResults) ValidateAll() error {
	return m.validate(true)
}

func (m *CountryIdMatchResults) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountryIdMatchResultsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNationality()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Nationality",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Nationality",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNationality()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountryIdMatchResultsValidationError{
				field:  "Nationality",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountryIdMatchResultsValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Id",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Id",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountryIdMatchResultsValidationError{
				field:  "Id",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountryIdMatchResultsValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhone()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhone()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountryIdMatchResultsValidationError{
				field:  "Phone",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountryIdMatchResultsValidationError{
				field:  "Email",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatusCheck()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "StatusCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "StatusCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusCheck()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountryIdMatchResultsValidationError{
				field:  "StatusCheck",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFace()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Face",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountryIdMatchResultsValidationError{
					field:  "Face",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFace()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountryIdMatchResultsValidationError{
				field:  "Face",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CountryIdMatchResultsMultiError(errors)
	}

	return nil
}

// CountryIdMatchResultsMultiError is an error wrapping multiple validation
// errors returned by CountryIdMatchResults.ValidateAll() if the designated
// constraints aren't met.
type CountryIdMatchResultsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountryIdMatchResultsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountryIdMatchResultsMultiError) AllErrors() []error { return m }

// CountryIdMatchResultsValidationError is the validation error returned by
// CountryIdMatchResults.Validate if the designated constraints aren't met.
type CountryIdMatchResultsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountryIdMatchResultsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountryIdMatchResultsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountryIdMatchResultsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountryIdMatchResultsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountryIdMatchResultsValidationError) ErrorName() string {
	return "CountryIdMatchResultsValidationError"
}

// Error satisfies the builtin error interface
func (e CountryIdMatchResultsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountryIdMatchResults.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountryIdMatchResultsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountryIdMatchResultsValidationError{}

// Validate checks the field values on UserDeviceDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserDeviceDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserDeviceDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserDeviceDetailsMultiError, or nil if none found.
func (m *UserDeviceDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *UserDeviceDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BrowserType

	// no validation rules for BrowserVersion

	// no validation rules for Os

	// no validation rules for Isp

	if len(errors) > 0 {
		return UserDeviceDetailsMultiError(errors)
	}

	return nil
}

// UserDeviceDetailsMultiError is an error wrapping multiple validation errors
// returned by UserDeviceDetails.ValidateAll() if the designated constraints
// aren't met.
type UserDeviceDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserDeviceDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserDeviceDetailsMultiError) AllErrors() []error { return m }

// UserDeviceDetailsValidationError is the validation error returned by
// UserDeviceDetails.Validate if the designated constraints aren't met.
type UserDeviceDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserDeviceDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserDeviceDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserDeviceDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserDeviceDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserDeviceDetailsValidationError) ErrorName() string {
	return "UserDeviceDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e UserDeviceDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserDeviceDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserDeviceDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserDeviceDetailsValidationError{}

// Validate checks the field values on StageDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StageDetailsMultiError, or
// nil if none found.
func (m *StageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *StageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsSuccess

	// no validation rules for Stage

	if len(errors) > 0 {
		return StageDetailsMultiError(errors)
	}

	return nil
}

// StageDetailsMultiError is an error wrapping multiple validation errors
// returned by StageDetails.ValidateAll() if the designated constraints aren't met.
type StageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageDetailsMultiError) AllErrors() []error { return m }

// StageDetailsValidationError is the validation error returned by
// StageDetails.Validate if the designated constraints aren't met.
type StageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageDetailsValidationError) ErrorName() string { return "StageDetailsValidationError" }

// Error satisfies the builtin error interface
func (e StageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageDetailsValidationError{}

// Validate checks the field values on MatchResults with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MatchResults) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MatchResults with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MatchResultsMultiError, or
// nil if none found.
func (m *MatchResults) ValidateAll() error {
	return m.validate(true)
}

func (m *MatchResults) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MatchScore

	// no validation rules for Value

	// no validation rules for Match

	if len(errors) > 0 {
		return MatchResultsMultiError(errors)
	}

	return nil
}

// MatchResultsMultiError is an error wrapping multiple validation errors
// returned by MatchResults.ValidateAll() if the designated constraints aren't met.
type MatchResultsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MatchResultsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MatchResultsMultiError) AllErrors() []error { return m }

// MatchResultsValidationError is the validation error returned by
// MatchResults.Validate if the designated constraints aren't met.
type MatchResultsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MatchResultsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MatchResultsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MatchResultsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MatchResultsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MatchResultsValidationError) ErrorName() string { return "MatchResultsValidationError" }

// Error satisfies the builtin error interface
func (e MatchResultsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMatchResults.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MatchResultsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MatchResultsValidationError{}

// Validate checks the field values on StatusCheck with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StatusCheck) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatusCheck with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StatusCheckMultiError, or
// nil if none found.
func (m *StatusCheck) ValidateAll() error {
	return m.validate(true)
}

func (m *StatusCheck) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Active

	// no validation rules for NameMatch

	// no validation rules for DobMatch

	if len(errors) > 0 {
		return StatusCheckMultiError(errors)
	}

	return nil
}

// StatusCheckMultiError is an error wrapping multiple validation errors
// returned by StatusCheck.ValidateAll() if the designated constraints aren't met.
type StatusCheckMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatusCheckMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatusCheckMultiError) AllErrors() []error { return m }

// StatusCheckValidationError is the validation error returned by
// StatusCheck.Validate if the designated constraints aren't met.
type StatusCheckValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatusCheckValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatusCheckValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatusCheckValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatusCheckValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatusCheckValidationError) ErrorName() string { return "StatusCheckValidationError" }

// Error satisfies the builtin error interface
func (e StatusCheckValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatusCheck.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatusCheckValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatusCheckValidationError{}

// Validate checks the field values on IPDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IPDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IPDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IPDetailsMultiError, or nil
// if none found.
func (m *IPDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *IPDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IPAddress

	// no validation rules for IPRisk

	// no validation rules for IPOrigination

	// no validation rules for IPProxyDetected

	if len(errors) > 0 {
		return IPDetailsMultiError(errors)
	}

	return nil
}

// IPDetailsMultiError is an error wrapping multiple validation errors returned
// by IPDetails.ValidateAll() if the designated constraints aren't met.
type IPDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IPDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IPDetailsMultiError) AllErrors() []error { return m }

// IPDetailsValidationError is the validation error returned by
// IPDetails.Validate if the designated constraints aren't met.
type IPDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IPDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IPDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IPDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IPDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IPDetailsValidationError) ErrorName() string { return "IPDetailsValidationError" }

// Error satisfies the builtin error interface
func (e IPDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIPDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IPDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IPDetailsValidationError{}

// Validate checks the field values on CallEnded with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CallEnded) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallEnded with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CallEndedMultiError, or nil
// if none found.
func (m *CallEnded) ValidateAll() error {
	return m.validate(true)
}

func (m *CallEnded) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCallEndedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallEndedValidationError{
					field:  "CallEndedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallEndedValidationError{
					field:  "CallEndedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallEndedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallEndedValidationError{
				field:  "CallEndedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsCallCompleted

	if all {
		switch v := interface{}(m.GetFailure()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallEndedValidationError{
					field:  "Failure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallEndedValidationError{
					field:  "Failure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFailure()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallEndedValidationError{
				field:  "Failure",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CallEndedMultiError(errors)
	}

	return nil
}

// CallEndedMultiError is an error wrapping multiple validation errors returned
// by CallEnded.ValidateAll() if the designated constraints aren't met.
type CallEndedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallEndedMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallEndedMultiError) AllErrors() []error { return m }

// CallEndedValidationError is the validation error returned by
// CallEnded.Validate if the designated constraints aren't met.
type CallEndedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallEndedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallEndedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallEndedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallEndedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallEndedValidationError) ErrorName() string { return "CallEndedValidationError" }

// Error satisfies the builtin error interface
func (e CallEndedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallEnded.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallEndedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallEndedValidationError{}
