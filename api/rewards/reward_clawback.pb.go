//go:generate gen_sql -types=ClawbackStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/rewards/reward_clawback.proto

package rewards

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// status of clawback
type ClawbackStatus int32

const (
	ClawbackStatus_CLAWBACK_STATUS_UNSPECIFIED ClawbackStatus = 0
	// PROCESSING_PENDING indicates that the clawback is waiting to be processed by the processor
	ClawbackStatus_CLAWBACK_STATUS_PROCESSING_PENDING ClawbackStatus = 1
	// PROCESSING_FAILED indicates that processing of clawback has failed and needs to be retried
	ClawbackStatus_CLAWBACK_STATUS_PROCESSING_FAILED ClawbackStatus = 2
	// PROCESSED indicates that clawback has successfully been processed
	ClawbackStatus_CLAWBACK_STATUS_PROCESSED ClawbackStatus = 3
)

// Enum value maps for ClawbackStatus.
var (
	ClawbackStatus_name = map[int32]string{
		0: "CLAWBACK_STATUS_UNSPECIFIED",
		1: "CLAWBACK_STATUS_PROCESSING_PENDING",
		2: "CLAWBACK_STATUS_PROCESSING_FAILED",
		3: "CLAWBACK_STATUS_PROCESSED",
	}
	ClawbackStatus_value = map[string]int32{
		"CLAWBACK_STATUS_UNSPECIFIED":        0,
		"CLAWBACK_STATUS_PROCESSING_PENDING": 1,
		"CLAWBACK_STATUS_PROCESSING_FAILED":  2,
		"CLAWBACK_STATUS_PROCESSED":          3,
	}
)

func (x ClawbackStatus) Enum() *ClawbackStatus {
	p := new(ClawbackStatus)
	*p = x
	return p
}

func (x ClawbackStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClawbackStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_clawback_proto_enumTypes[0].Descriptor()
}

func (ClawbackStatus) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_clawback_proto_enumTypes[0]
}

func (x ClawbackStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClawbackStatus.Descriptor instead.
func (ClawbackStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_clawback_proto_rawDescGZIP(), []int{0}
}

type ClawbackFieldMask int32

const (
	ClawbackFieldMask_CLAWBACK_FIELD_MASK_UNSPECIFIED ClawbackFieldMask = 0
	// field mask corresponding to "status" field in DB
	ClawbackFieldMask_CLAWBACK_FIELD_MASK_STATUS ClawbackFieldMask = 1
	// field mask corresponding to "processing_ref" field in DB
	ClawbackFieldMask_CLAWBACK_FIELD_MASK_PROCESSING_REF ClawbackFieldMask = 2
)

// Enum value maps for ClawbackFieldMask.
var (
	ClawbackFieldMask_name = map[int32]string{
		0: "CLAWBACK_FIELD_MASK_UNSPECIFIED",
		1: "CLAWBACK_FIELD_MASK_STATUS",
		2: "CLAWBACK_FIELD_MASK_PROCESSING_REF",
	}
	ClawbackFieldMask_value = map[string]int32{
		"CLAWBACK_FIELD_MASK_UNSPECIFIED":    0,
		"CLAWBACK_FIELD_MASK_STATUS":         1,
		"CLAWBACK_FIELD_MASK_PROCESSING_REF": 2,
	}
)

func (x ClawbackFieldMask) Enum() *ClawbackFieldMask {
	p := new(ClawbackFieldMask)
	*p = x
	return p
}

func (x ClawbackFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClawbackFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_reward_clawback_proto_enumTypes[1].Descriptor()
}

func (ClawbackFieldMask) Type() protoreflect.EnumType {
	return &file_api_rewards_reward_clawback_proto_enumTypes[1]
}

func (x ClawbackFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClawbackFieldMask.Descriptor instead.
func (ClawbackFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_reward_clawback_proto_rawDescGZIP(), []int{1}
}

type RewardClawback struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// id of reward which has been clawed back
	RewardId string `protobuf:"bytes,2,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	// type of reward that has been clawed back
	RewardType RewardType `protobuf:"varint,3,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	// reward units that are clawed back. they can be less than the reward units given initially in case of partial clawback
	ClawedBackRewardUnits float32 `protobuf:"fixed32,4,opt,name=clawed_back_reward_units,json=clawedBackRewardUnits,proto3" json:"clawed_back_reward_units,omitempty"`
	// type of action that led to clawback
	Action ClawbackEventType `protobuf:"varint,5,opt,name=action,proto3,enum=rewards.ClawbackEventType" json:"action,omitempty"`
	// ref id of the action that led to the clawback
	ActionRefId string `protobuf:"bytes,6,opt,name=action_ref_id,json=actionRefId,proto3" json:"action_ref_id,omitempty"`
	// timestamp of action that led to clawback
	ActionTimestamp *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=action_timestamp,json=actionTimestamp,proto3" json:"action_timestamp,omitempty"`
	// status of clawback
	Status ClawbackStatus `protobuf:"varint,8,opt,name=status,proto3,enum=rewards.ClawbackStatus" json:"status,omitempty"`
	// reference id of processing of clawback
	ProcessingRef string `protobuf:"bytes,9,opt,name=processing_ref,json=processingRef,proto3" json:"processing_ref,omitempty"`
	// standard timestamps
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *RewardClawback) Reset() {
	*x = RewardClawback{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_reward_clawback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardClawback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardClawback) ProtoMessage() {}

func (x *RewardClawback) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_reward_clawback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardClawback.ProtoReflect.Descriptor instead.
func (*RewardClawback) Descriptor() ([]byte, []int) {
	return file_api_rewards_reward_clawback_proto_rawDescGZIP(), []int{0}
}

func (x *RewardClawback) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RewardClawback) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *RewardClawback) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_REWARD_TYPE_UNSPECIFIED
}

func (x *RewardClawback) GetClawedBackRewardUnits() float32 {
	if x != nil {
		return x.ClawedBackRewardUnits
	}
	return 0
}

func (x *RewardClawback) GetAction() ClawbackEventType {
	if x != nil {
		return x.Action
	}
	return ClawbackEventType_UNSPECIFIED_EVENT_TYPE
}

func (x *RewardClawback) GetActionRefId() string {
	if x != nil {
		return x.ActionRefId
	}
	return ""
}

func (x *RewardClawback) GetActionTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ActionTimestamp
	}
	return nil
}

func (x *RewardClawback) GetStatus() ClawbackStatus {
	if x != nil {
		return x.Status
	}
	return ClawbackStatus_CLAWBACK_STATUS_UNSPECIFIED
}

func (x *RewardClawback) GetProcessingRef() string {
	if x != nil {
		return x.ProcessingRef
	}
	return ""
}

func (x *RewardClawback) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RewardClawback) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RewardClawback) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_rewards_reward_clawback_proto protoreflect.FileDescriptor

var file_api_rewards_reward_clawback_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x1a, 0x25, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x63, 0x6c, 0x61, 0x77, 0x62, 0x61,
	0x63, 0x6b, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd4,
	0x04, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63,
	0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x34,
	0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x63, 0x6c, 0x61, 0x77, 0x65, 0x64, 0x5f, 0x62,
	0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x63, 0x6c, 0x61, 0x77, 0x65, 0x64, 0x42, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x32, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x10, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x2f, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x77, 0x62, 0x61, 0x63, 0x6b, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a,
	0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x66, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x9f, 0x01, 0x0a, 0x0e, 0x43, 0x6c, 0x61, 0x77, 0x62, 0x61,
	0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4c, 0x41, 0x57,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4c, 0x41,
	0x57, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f,
	0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x01, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4c, 0x41, 0x57, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4c, 0x41, 0x57,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x80, 0x01, 0x0a, 0x11, 0x43, 0x6c, 0x61, 0x77,
	0x62, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x23, 0x0a,
	0x1f, 0x43, 0x4c, 0x41, 0x57, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4c, 0x41, 0x57, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4c, 0x41, 0x57, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x46, 0x10, 0x02, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_rewards_reward_clawback_proto_rawDescOnce sync.Once
	file_api_rewards_reward_clawback_proto_rawDescData = file_api_rewards_reward_clawback_proto_rawDesc
)

func file_api_rewards_reward_clawback_proto_rawDescGZIP() []byte {
	file_api_rewards_reward_clawback_proto_rawDescOnce.Do(func() {
		file_api_rewards_reward_clawback_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_rewards_reward_clawback_proto_rawDescData)
	})
	return file_api_rewards_reward_clawback_proto_rawDescData
}

var file_api_rewards_reward_clawback_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_rewards_reward_clawback_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_rewards_reward_clawback_proto_goTypes = []interface{}{
	(ClawbackStatus)(0),           // 0: rewards.ClawbackStatus
	(ClawbackFieldMask)(0),        // 1: rewards.ClawbackFieldMask
	(*RewardClawback)(nil),        // 2: rewards.RewardClawback
	(RewardType)(0),               // 3: rewards.RewardType
	(ClawbackEventType)(0),        // 4: rewards.ClawbackEventType
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_api_rewards_reward_clawback_proto_depIdxs = []int32{
	3, // 0: rewards.RewardClawback.reward_type:type_name -> rewards.RewardType
	4, // 1: rewards.RewardClawback.action:type_name -> rewards.ClawbackEventType
	5, // 2: rewards.RewardClawback.action_timestamp:type_name -> google.protobuf.Timestamp
	0, // 3: rewards.RewardClawback.status:type_name -> rewards.ClawbackStatus
	5, // 4: rewards.RewardClawback.created_at:type_name -> google.protobuf.Timestamp
	5, // 5: rewards.RewardClawback.updated_at:type_name -> google.protobuf.Timestamp
	5, // 6: rewards.RewardClawback.deleted_at:type_name -> google.protobuf.Timestamp
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_rewards_reward_clawback_proto_init() }
func file_api_rewards_reward_clawback_proto_init() {
	if File_api_rewards_reward_clawback_proto != nil {
		return
	}
	file_api_rewards_clawback_event_type_proto_init()
	file_api_rewards_reward_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_rewards_reward_clawback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardClawback); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_rewards_reward_clawback_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_rewards_reward_clawback_proto_goTypes,
		DependencyIndexes: file_api_rewards_reward_clawback_proto_depIdxs,
		EnumInfos:         file_api_rewards_reward_clawback_proto_enumTypes,
		MessageInfos:      file_api_rewards_reward_clawback_proto_msgTypes,
	}.Build()
	File_api_rewards_reward_clawback_proto = out.File
	file_api_rewards_reward_clawback_proto_rawDesc = nil
	file_api_rewards_reward_clawback_proto_goTypes = nil
	file_api_rewards_reward_clawback_proto_depIdxs = nil
}
