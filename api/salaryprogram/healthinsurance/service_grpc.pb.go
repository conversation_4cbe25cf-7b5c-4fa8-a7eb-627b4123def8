// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/salaryprogram/healthinsurance/service.proto

package healthinsurance

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HealthInsurance_InitiatePolicyPurchase_FullMethodName                    = "/salaryprogram.healthinsurance.HealthInsurance/InitiatePolicyPurchase"
	HealthInsurance_GetPolicyIssuanceRequestsForActor_FullMethodName         = "/salaryprogram.healthinsurance.HealthInsurance/GetPolicyIssuanceRequestsForActor"
	HealthInsurance_GetIssuedPoliciesRedirectionInfo_FullMethodName          = "/salaryprogram.healthinsurance.HealthInsurance/GetIssuedPoliciesRedirectionInfo"
	HealthInsurance_GetIssuedPoliciesForActor_FullMethodName                 = "/salaryprogram.healthinsurance.HealthInsurance/GetIssuedPoliciesForActor"
	HealthInsurance_CancelPolicyAutoRenewal_FullMethodName                   = "/salaryprogram.healthinsurance.HealthInsurance/CancelPolicyAutoRenewal"
	HealthInsurance_ProcessPolicyPurchaseVerificationCallback_FullMethodName = "/salaryprogram.healthinsurance.HealthInsurance/ProcessPolicyPurchaseVerificationCallback"
	HealthInsurance_IssueNewPolicy_FullMethodName                            = "/salaryprogram.healthinsurance.HealthInsurance/IssueNewPolicy"
)

// HealthInsuranceClient is the client API for HealthInsurance service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HealthInsuranceClient interface {
	// useful to create a request for purchasing a new health insurance policy.
	InitiatePolicyPurchase(ctx context.Context, in *InitiatePolicyPurchaseRequest, opts ...grpc.CallOption) (*InitiatePolicyPurchaseResponse, error)
	// useful to get policy issuance requests for a given actor
	GetPolicyIssuanceRequestsForActor(ctx context.Context, in *GetPolicyIssuanceRequestsForActorRequest, opts ...grpc.CallOption) (*GetPolicyIssuanceRequestsForActorResponse, error)
	// useful to get the info for re-directing the user to vendor web-app for viewing their issued policies.
	GetIssuedPoliciesRedirectionInfo(ctx context.Context, in *IssuedPoliciesRedirectionInfoRequest, opts ...grpc.CallOption) (*IssuedPoliciesRedirectionInfoResponse, error)
	// useful to get a list of policies issued for a given actor.
	GetIssuedPoliciesForActor(ctx context.Context, in *GetIssuedPoliciesForActorRequest, opts ...grpc.CallOption) (*GetIssuedPoliciesForActorResponse, error)
	// some policies get auto-renewed at vendor's end, rpc is useful to stop renewing a policy.
	CancelPolicyAutoRenewal(ctx context.Context, in *CancelPolicyAutoRenewalRequest, opts ...grpc.CallOption) (*CancelPolicyAutoRenewalResponse, error)
	// purchase verification callback is initiated by the policy vendor just before issuing a policy to re-confirm if the given policy purchase should be allowed or not.
	// useful to process policy purchase verification callback, it checks whether the given policy purchase (denoted by request id) should be allowed or not.
	ProcessPolicyPurchaseVerificationCallback(ctx context.Context, in *PolicyPurchaseVerificationCallbackRequest, opts ...grpc.CallOption) (*PolicyPurchaseVerificationCallbackResponse, error)
	// IssueNewPolicy rpc will purchase and issue new policy for a given actor
	IssueNewPolicy(ctx context.Context, in *IssueNewPolicyRequest, opts ...grpc.CallOption) (*IssueNewPolicyResponse, error)
}

type healthInsuranceClient struct {
	cc grpc.ClientConnInterface
}

func NewHealthInsuranceClient(cc grpc.ClientConnInterface) HealthInsuranceClient {
	return &healthInsuranceClient{cc}
}

func (c *healthInsuranceClient) InitiatePolicyPurchase(ctx context.Context, in *InitiatePolicyPurchaseRequest, opts ...grpc.CallOption) (*InitiatePolicyPurchaseResponse, error) {
	out := new(InitiatePolicyPurchaseResponse)
	err := c.cc.Invoke(ctx, HealthInsurance_InitiatePolicyPurchase_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthInsuranceClient) GetPolicyIssuanceRequestsForActor(ctx context.Context, in *GetPolicyIssuanceRequestsForActorRequest, opts ...grpc.CallOption) (*GetPolicyIssuanceRequestsForActorResponse, error) {
	out := new(GetPolicyIssuanceRequestsForActorResponse)
	err := c.cc.Invoke(ctx, HealthInsurance_GetPolicyIssuanceRequestsForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthInsuranceClient) GetIssuedPoliciesRedirectionInfo(ctx context.Context, in *IssuedPoliciesRedirectionInfoRequest, opts ...grpc.CallOption) (*IssuedPoliciesRedirectionInfoResponse, error) {
	out := new(IssuedPoliciesRedirectionInfoResponse)
	err := c.cc.Invoke(ctx, HealthInsurance_GetIssuedPoliciesRedirectionInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthInsuranceClient) GetIssuedPoliciesForActor(ctx context.Context, in *GetIssuedPoliciesForActorRequest, opts ...grpc.CallOption) (*GetIssuedPoliciesForActorResponse, error) {
	out := new(GetIssuedPoliciesForActorResponse)
	err := c.cc.Invoke(ctx, HealthInsurance_GetIssuedPoliciesForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthInsuranceClient) CancelPolicyAutoRenewal(ctx context.Context, in *CancelPolicyAutoRenewalRequest, opts ...grpc.CallOption) (*CancelPolicyAutoRenewalResponse, error) {
	out := new(CancelPolicyAutoRenewalResponse)
	err := c.cc.Invoke(ctx, HealthInsurance_CancelPolicyAutoRenewal_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthInsuranceClient) ProcessPolicyPurchaseVerificationCallback(ctx context.Context, in *PolicyPurchaseVerificationCallbackRequest, opts ...grpc.CallOption) (*PolicyPurchaseVerificationCallbackResponse, error) {
	out := new(PolicyPurchaseVerificationCallbackResponse)
	err := c.cc.Invoke(ctx, HealthInsurance_ProcessPolicyPurchaseVerificationCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthInsuranceClient) IssueNewPolicy(ctx context.Context, in *IssueNewPolicyRequest, opts ...grpc.CallOption) (*IssueNewPolicyResponse, error) {
	out := new(IssueNewPolicyResponse)
	err := c.cc.Invoke(ctx, HealthInsurance_IssueNewPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HealthInsuranceServer is the server API for HealthInsurance service.
// All implementations should embed UnimplementedHealthInsuranceServer
// for forward compatibility
type HealthInsuranceServer interface {
	// useful to create a request for purchasing a new health insurance policy.
	InitiatePolicyPurchase(context.Context, *InitiatePolicyPurchaseRequest) (*InitiatePolicyPurchaseResponse, error)
	// useful to get policy issuance requests for a given actor
	GetPolicyIssuanceRequestsForActor(context.Context, *GetPolicyIssuanceRequestsForActorRequest) (*GetPolicyIssuanceRequestsForActorResponse, error)
	// useful to get the info for re-directing the user to vendor web-app for viewing their issued policies.
	GetIssuedPoliciesRedirectionInfo(context.Context, *IssuedPoliciesRedirectionInfoRequest) (*IssuedPoliciesRedirectionInfoResponse, error)
	// useful to get a list of policies issued for a given actor.
	GetIssuedPoliciesForActor(context.Context, *GetIssuedPoliciesForActorRequest) (*GetIssuedPoliciesForActorResponse, error)
	// some policies get auto-renewed at vendor's end, rpc is useful to stop renewing a policy.
	CancelPolicyAutoRenewal(context.Context, *CancelPolicyAutoRenewalRequest) (*CancelPolicyAutoRenewalResponse, error)
	// purchase verification callback is initiated by the policy vendor just before issuing a policy to re-confirm if the given policy purchase should be allowed or not.
	// useful to process policy purchase verification callback, it checks whether the given policy purchase (denoted by request id) should be allowed or not.
	ProcessPolicyPurchaseVerificationCallback(context.Context, *PolicyPurchaseVerificationCallbackRequest) (*PolicyPurchaseVerificationCallbackResponse, error)
	// IssueNewPolicy rpc will purchase and issue new policy for a given actor
	IssueNewPolicy(context.Context, *IssueNewPolicyRequest) (*IssueNewPolicyResponse, error)
}

// UnimplementedHealthInsuranceServer should be embedded to have forward compatible implementations.
type UnimplementedHealthInsuranceServer struct {
}

func (UnimplementedHealthInsuranceServer) InitiatePolicyPurchase(context.Context, *InitiatePolicyPurchaseRequest) (*InitiatePolicyPurchaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiatePolicyPurchase not implemented")
}
func (UnimplementedHealthInsuranceServer) GetPolicyIssuanceRequestsForActor(context.Context, *GetPolicyIssuanceRequestsForActorRequest) (*GetPolicyIssuanceRequestsForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPolicyIssuanceRequestsForActor not implemented")
}
func (UnimplementedHealthInsuranceServer) GetIssuedPoliciesRedirectionInfo(context.Context, *IssuedPoliciesRedirectionInfoRequest) (*IssuedPoliciesRedirectionInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIssuedPoliciesRedirectionInfo not implemented")
}
func (UnimplementedHealthInsuranceServer) GetIssuedPoliciesForActor(context.Context, *GetIssuedPoliciesForActorRequest) (*GetIssuedPoliciesForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIssuedPoliciesForActor not implemented")
}
func (UnimplementedHealthInsuranceServer) CancelPolicyAutoRenewal(context.Context, *CancelPolicyAutoRenewalRequest) (*CancelPolicyAutoRenewalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelPolicyAutoRenewal not implemented")
}
func (UnimplementedHealthInsuranceServer) ProcessPolicyPurchaseVerificationCallback(context.Context, *PolicyPurchaseVerificationCallbackRequest) (*PolicyPurchaseVerificationCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessPolicyPurchaseVerificationCallback not implemented")
}
func (UnimplementedHealthInsuranceServer) IssueNewPolicy(context.Context, *IssueNewPolicyRequest) (*IssueNewPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IssueNewPolicy not implemented")
}

// UnsafeHealthInsuranceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HealthInsuranceServer will
// result in compilation errors.
type UnsafeHealthInsuranceServer interface {
	mustEmbedUnimplementedHealthInsuranceServer()
}

func RegisterHealthInsuranceServer(s grpc.ServiceRegistrar, srv HealthInsuranceServer) {
	s.RegisterService(&HealthInsurance_ServiceDesc, srv)
}

func _HealthInsurance_InitiatePolicyPurchase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiatePolicyPurchaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthInsuranceServer).InitiatePolicyPurchase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HealthInsurance_InitiatePolicyPurchase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthInsuranceServer).InitiatePolicyPurchase(ctx, req.(*InitiatePolicyPurchaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthInsurance_GetPolicyIssuanceRequestsForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPolicyIssuanceRequestsForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthInsuranceServer).GetPolicyIssuanceRequestsForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HealthInsurance_GetPolicyIssuanceRequestsForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthInsuranceServer).GetPolicyIssuanceRequestsForActor(ctx, req.(*GetPolicyIssuanceRequestsForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthInsurance_GetIssuedPoliciesRedirectionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IssuedPoliciesRedirectionInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthInsuranceServer).GetIssuedPoliciesRedirectionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HealthInsurance_GetIssuedPoliciesRedirectionInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthInsuranceServer).GetIssuedPoliciesRedirectionInfo(ctx, req.(*IssuedPoliciesRedirectionInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthInsurance_GetIssuedPoliciesForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIssuedPoliciesForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthInsuranceServer).GetIssuedPoliciesForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HealthInsurance_GetIssuedPoliciesForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthInsuranceServer).GetIssuedPoliciesForActor(ctx, req.(*GetIssuedPoliciesForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthInsurance_CancelPolicyAutoRenewal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelPolicyAutoRenewalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthInsuranceServer).CancelPolicyAutoRenewal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HealthInsurance_CancelPolicyAutoRenewal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthInsuranceServer).CancelPolicyAutoRenewal(ctx, req.(*CancelPolicyAutoRenewalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthInsurance_ProcessPolicyPurchaseVerificationCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PolicyPurchaseVerificationCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthInsuranceServer).ProcessPolicyPurchaseVerificationCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HealthInsurance_ProcessPolicyPurchaseVerificationCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthInsuranceServer).ProcessPolicyPurchaseVerificationCallback(ctx, req.(*PolicyPurchaseVerificationCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthInsurance_IssueNewPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IssueNewPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthInsuranceServer).IssueNewPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HealthInsurance_IssueNewPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthInsuranceServer).IssueNewPolicy(ctx, req.(*IssueNewPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HealthInsurance_ServiceDesc is the grpc.ServiceDesc for HealthInsurance service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HealthInsurance_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "salaryprogram.healthinsurance.HealthInsurance",
	HandlerType: (*HealthInsuranceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InitiatePolicyPurchase",
			Handler:    _HealthInsurance_InitiatePolicyPurchase_Handler,
		},
		{
			MethodName: "GetPolicyIssuanceRequestsForActor",
			Handler:    _HealthInsurance_GetPolicyIssuanceRequestsForActor_Handler,
		},
		{
			MethodName: "GetIssuedPoliciesRedirectionInfo",
			Handler:    _HealthInsurance_GetIssuedPoliciesRedirectionInfo_Handler,
		},
		{
			MethodName: "GetIssuedPoliciesForActor",
			Handler:    _HealthInsurance_GetIssuedPoliciesForActor_Handler,
		},
		{
			MethodName: "CancelPolicyAutoRenewal",
			Handler:    _HealthInsurance_CancelPolicyAutoRenewal_Handler,
		},
		{
			MethodName: "ProcessPolicyPurchaseVerificationCallback",
			Handler:    _HealthInsurance_ProcessPolicyPurchaseVerificationCallback_Handler,
		},
		{
			MethodName: "IssueNewPolicy",
			Handler:    _HealthInsurance_IssueNewPolicy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/salaryprogram/healthinsurance/service.proto",
}
