package cc_securitygroups_prod

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var mongodbSgRules = &securitygroups.SGIR{
	Name:        "mongodb-sg",
	Account:     cfg.Prod,
	VpcId:       "vpc-0d32af4daaaee9697",
	Description: "Managed by Terraform",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    9216,
			ToPort:      9216,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    9100,
			ToPort:      9100,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "allow icmp from security scanning servers",
			Protocol:    "icmp",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.Prod, SecurityGroupName: "icmp-sg", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    27017,
			ToPort:      27017,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{},
}
