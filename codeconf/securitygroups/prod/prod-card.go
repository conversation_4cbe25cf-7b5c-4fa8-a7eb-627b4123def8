package cc_securitygroups_prod

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var prodCardRules = &securitygroups.SGIR{
	Name:        "prod-card",
	Account:     cfg.Prod,
	VpcId:       "vpc-0d32af4daaaee9697",
	Description: "Security Group for card Service",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "allow icmp from security scanning servers",
			Protocol:    "icmp",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.Prod, SecurityGroupName: "icmp-sg", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "card Service HTTP Port",
			Protocol:    "tcp",
			FromPort:    8093,
			ToPort:      8093,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Prometheus node exporter",
			Protocol:    "tcp",
			FromPort:    9100,
			ToPort:      9100,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "Allow inbound from own security group",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.Prod, SecurityGroupName: "prod-card", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Prometheus for application metrics",
			Protocol:    "tcp",
			FromPort:    9999,
			ToPort:      9999,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Consul",
			Protocol:    "udp",
			FromPort:    8300,
			ToPort:      8301,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Consul",
			Protocol:    "tcp",
			FromPort:    8300,
			ToPort:      8301,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "card Service Secure Port",
			Protocol:    "tcp",
			FromPort:    9504,
			ToPort:      9504,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "segrule",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.Prod, SecurityGroupName: "non-cde-pci-sg", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to owlh node",
			Protocol:    "tcp",
			FromPort:    50010,
			ToPort:      50010,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "to apache pinot",
			Protocol:    "tcp",
			FromPort:    443,
			ToPort:      443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "card Service HTTP Port",
			Protocol:    "tcp",
			FromPort:    0,
			ToPort:      65535,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "prefixlist",
			Egress:      true,
			Description: "Outbound to Prefix lists",
			Protocol:    "tcp",
			FromPort:    443,
			ToPort:      443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: "pl-66a7420f"},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to rudder(data-prod)",
			Protocol:    "tcp",
			FromPort:    7001,
			ToPort:      7001,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "prefixlist",
			Egress:      true,
			Description: "Outbound to Prefix lists",
			Protocol:    "tcp",
			FromPort:    443,
			ToPort:      443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: "pl-78a54011"},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to wazuh(security)",
			Protocol:    "udp",
			FromPort:    1514,
			ToPort:      1515,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to CRDB",
			Protocol:    "tcp",
			FromPort:    26257,
			ToPort:      26257,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Postgres",
			Protocol:    "tcp",
			FromPort:    5432,
			ToPort:      5432,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Redis Cloud",
			Protocol:    "tcp",
			FromPort:    10246,
			ToPort:      10246,
			SourceDest:  &securitygroups.SrvObject{Cidr: "***********/24", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Consul",
			Protocol:    "tcp",
			FromPort:    8300,
			ToPort:      8301,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Consul",
			Protocol:    "udp",
			FromPort:    8300,
			ToPort:      8301,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Redis Cloud",
			Protocol:    "tcp",
			FromPort:    12231,
			ToPort:      12231,
			SourceDest:  &securitygroups.SrvObject{Cidr: "***********/24", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to log aggregator",
			Protocol:    "tcp",
			FromPort:    24224,
			ToPort:      24224,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to wazuh(security)",
			Protocol:    "tcp",
			FromPort:    1514,
			ToPort:      1515,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{"Name": "prod-card",
		"TFModVersion":  "V3",
		"sec:pci-scope": "non-cde-pci-scope",
	},
}
