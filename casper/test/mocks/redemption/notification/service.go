// Code generated by MockGen. DO NOT EDIT.
// Source: casper/redemption/notification/service.go

// Package mock_notification is a generated GoMock package.
package mock_notification

import (
	context "context"
	reflect "reflect"

	casper "github.com/epifi/gamma/api/casper"
	redemption "github.com/epifi/gamma/api/casper/redemption"
	gomock "github.com/golang/mock/gomock"
)

// MockINotificationService is a mock of INotificationService interface
type MockINotificationService struct {
	ctrl     *gomock.Controller
	recorder *MockINotificationServiceMockRecorder
}

// MockINotificationServiceMockRecorder is the mock recorder for MockINotificationService
type MockINotificationServiceMockRecorder struct {
	mock *MockINotificationService
}

// NewMockINotificationService creates a new mock instance
func NewMockINotificationService(ctrl *gomock.Controller) *MockINotificationService {
	mock := &MockINotificationService{ctrl: ctrl}
	mock.recorder = &MockINotificationServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockINotificationService) EXPECT() *MockINotificationServiceMockRecorder {
	return m.recorder
}

// SendSuccessfulRedemptionNotification mocks base method
func (m *MockINotificationService) SendSuccessfulRedemptionNotification(ctx context.Context, redeemedOffer *redemption.RedeemedOffer, catalogOffer *casper.Offer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendSuccessfulRedemptionNotification", ctx, redeemedOffer, catalogOffer)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendSuccessfulRedemptionNotification indicates an expected call of SendSuccessfulRedemptionNotification
func (mr *MockINotificationServiceMockRecorder) SendSuccessfulRedemptionNotification(ctx, redeemedOffer, catalogOffer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSuccessfulRedemptionNotification", reflect.TypeOf((*MockINotificationService)(nil).SendSuccessfulRedemptionNotification), ctx, redeemedOffer, catalogOffer)
}

// SendFailedRedemptionNotification mocks base method
func (m *MockINotificationService) SendFailedRedemptionNotification(ctx context.Context, redeemedOffer *redemption.RedeemedOffer, catalogOffer *casper.Offer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendFailedRedemptionNotification", ctx, redeemedOffer, catalogOffer)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendFailedRedemptionNotification indicates an expected call of SendFailedRedemptionNotification
func (mr *MockINotificationServiceMockRecorder) SendFailedRedemptionNotification(ctx, redeemedOffer, catalogOffer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendFailedRedemptionNotification", reflect.TypeOf((*MockINotificationService)(nil).SendFailedRedemptionNotification), ctx, redeemedOffer, catalogOffer)
}
