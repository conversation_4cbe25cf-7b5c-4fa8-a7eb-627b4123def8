package offervendor

import (
	"os"
	"testing"
	"time"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/casper/config"
	"github.com/epifi/gamma/casper/test"
)

var (
	conf                    *config.Config
	currentTimestamp        = timestamp.Now()
	timestampAfter10Minutes = timestamp.New(currentTimestamp.AsTime().Add(10 * time.Minute))
)

func TestMain(m *testing.M) {
	var teardown func()
	conf, _, _, teardown = test.InitTestServer(false)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
