package notification

import (
	"context"

	"go.uber.org/zap"

	"github.com/pkg/errors"

	queuePb "github.com/epifi/be-common/api/queue"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	notificationPb "github.com/epifi/gamma/api/casper/external_vendor_redemption/notification"
)

type NotificationConsumerService struct {
	notificationSvc INotificationSvc
}

func NewConsumerService(
	notificationSvc INotificationSvc,
) *NotificationConsumerService {
	return &NotificationConsumerService{
		notificationSvc: notificationSvc,
	}
}

var _ notificationPb.ConsumerServer = &NotificationConsumerService{}

var (
	getSuccessRes = func() *notificationPb.ConsumerResponse {
		return &notificationPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}
	}
	getPermanentFailureRes = func() *notificationPb.ConsumerResponse {
		return &notificationPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE}}
	}
	getTransientFailureRes = func() *notificationPb.ConsumerResponse {
		return &notificationPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}
	}
)

func (n *NotificationConsumerService) ProcessFiStoreOrderNotificationEvent(ctx context.Context, event *notificationPb.OrderNotificationEvent) (*notificationPb.ConsumerResponse, error) {
	notificationSent, err := n.notificationSvc.SendNotification(ctx, event.GetActorId(), event.GetRedemptionId(), event.GetMedium(), event.GetNotificationTrigger(), event.GetNotificationCategory())
	if err != nil {
		switch {
		case errors.Is(err, epifierrors.ErrFailedPrecondition):
			logger.Error(ctx, "precondition failed for sending order notification. won't be retrying",
				zap.String("medium", event.GetMedium().String()),
				zap.String(logger.ACTOR_ID_V2, event.GetActorId()),
				zap.String("notificationType", event.GetNotificationTrigger().String()),
				zap.String("notificationCategory", event.GetNotificationCategory().String()), zap.Error(err),
			)
			return getPermanentFailureRes(), nil
		case errors.Is(err, epifierrors.ErrPermanent):
			logger.Error(ctx, "permanent failure for sending order notification. won't be retrying",
				zap.String("medium", event.GetMedium().String()),
				zap.String(logger.ACTOR_ID_V2, event.GetActorId()),
				zap.String("notificationType", event.GetNotificationTrigger().String()),
				zap.String("notificationCategory", event.GetNotificationCategory().String()), zap.Error(err),
			)
			return getPermanentFailureRes(), nil
		default:
			logger.Error(ctx, "error while sending order notification to user",
				zap.String("medium", event.GetMedium().String()),
				zap.String(logger.ACTOR_ID_V2, event.GetActorId()),
				zap.String("notificationType", event.GetNotificationTrigger().String()),
				zap.String("notificationCategory", event.GetNotificationCategory().String()), zap.Error(err),
			)
			return getTransientFailureRes(), nil
		}
	}

	if !notificationSent {
		// not an error scenario
		logger.Info(ctx, "order notification not sent",
			zap.String("medium", event.GetMedium().String()),
			zap.String(logger.ACTOR_ID_V2, event.GetActorId()),
			zap.String("notificationType", event.GetNotificationTrigger().String()),
			zap.String("notificationCategory", event.GetNotificationCategory().String()),
		)
		return getPermanentFailureRes(), nil
	}

	return getSuccessRes(), nil
}
