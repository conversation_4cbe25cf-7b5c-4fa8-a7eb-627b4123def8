package developer

import (
	"context"
	"errors"

	"github.com/epifi/gamma/api/casper/developer"

	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/be-common/pkg/logger"
)

type CasperDevService struct {
	fac *DevFactory
}

func NewCasperDevService(fac *DevFactory) *CasperDevService {
	return &CasperDevService{fac: fac}
}

func (r *CasperDevService) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status: rpcPb.StatusOk(),
		EntityList: []string{developer.CasperEntity_OFFER.String(), developer.CasperEntity_OFFER_INVENTORY.String(),
			developer.CasperEntity_OFFER_LISTING.String(), developer.CasperEntity_REDEEMED_OFFER.String(),
			developer.CasperEntity_REDEMPTION_REQUEST.String(), developer.CasperEntity_LOYLTY_REDEMPTION.String(), developer.CasperEntity_OFFLINE_REDEMPTION.String(),
			developer.CasperEntity_LOYLTY_EGV_OFFER_CATALOG.String(), developer.CasperEntity_LOYLTY_EGV_PRODUCT_DETAIL.String(), developer.CasperEntity_QWIKCILVER_REDEMPTION.String(),
			developer.CasperEntity_QWIKCILVER_CATEGORY_DETAILS.String(), developer.CasperEntity_QWIKCILVER_PRODUCT_LIST.String(), developer.CasperEntity_QWIKCILVER_PRODUCT_DETAILS.String(),
			developer.CasperEntity_EXCHANGER_OFFER.String(), developer.CasperEntity_EXCHANGER_OFFER_ACTOR_ATTEMPT.String(),
			developer.CasperEntity_EXCHANGER_OFFER_LISTING.String(), developer.CasperEntity_EXCHANGER_OFFER_ORDER.String(),
			developer.CasperEntity_EXCHANGER_OFFER_ORDER_FULFILLMENT_REQUEST.String(), developer.CasperEntity_EXCHANGER_OFFER_GROUP.String(), developer.CasperEntity_OFFER_DISPLAY_RANK.String(),
			developer.CasperEntity_EXCHANGER_OFFER_INVENTORY.String(), developer.CasperEntity_EXCHANGER_OFFER_LEVEL_REWARD_UNITS_UTILISATION.String(), developer.CasperEntity_DISCOUNT.String(),
			developer.CasperEntity_IN_HOUSE_REDEMPTION.String(), developer.CasperEntity_EXTERNAL_VENDOR_REDEMPTION.String(), developer.CasperEntity_FI_STORE_REDEMPTION.String()},
	}, nil
}

func (r *CasperDevService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := developer.CasperEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	paramFetcher, err := r.fac.getParameterListProcessor(developer.CasperEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available", zap.Error(err))
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.CasperEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list", zap.Error(err))
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (r *CasperDevService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.CasperEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	dataFetcher, err := r.fac.getDataProcessor(developer.CasperEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available", zap.Error(err))
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.CasperEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data", zap.Error(err))
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return &cxDsPb.GetDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
