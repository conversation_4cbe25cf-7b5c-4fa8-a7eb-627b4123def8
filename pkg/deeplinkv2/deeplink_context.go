package deeplinkv2

import commontypes "github.com/epifi/be-common/api/typesv2/common"

// DeepLinkContext is to be used for deriving correct version of a screen for the client
// rn it has - deviceId, AppVersion -- which decides screen version to be used
// we can further enhance it to use experiment-id to send different screens for different experiments
type DeepLinkContext struct {
	DeviceType string
	AppVersion string
}

// TODO(shubhra): get device-id and
func NewDeepLinkContext(device *commontypes.Device) *DeepLinkContext {
	return &DeepLinkContext{DeviceType: device.GetModel(), AppVersion: device.GetSwVersion()}
}

// this function is only to be used when there is no versions specified for the screen
func DefaultDeepLinkCtx() *DeepLinkContext {
	return &DeepLinkContext{DeviceType: DEVICE_DEFAULT, AppVersion: VERSION_DEFAULT}
}
