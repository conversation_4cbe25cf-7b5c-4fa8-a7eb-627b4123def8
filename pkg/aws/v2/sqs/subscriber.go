package sqs

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"reflect"
	"strconv"
	"sync"
	"time"

	awsSqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	grpc_prometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	errorsPkg "github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/redis/go-redis/v9"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	pb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/async/goroutine"
	awsPkg "github.com/epifi/be-common/pkg/aws"
	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/epifi/be-common/pkg/cfg"
	globalcfg "github.com/epifi/be-common/pkg/cfg"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	logSamplingInterceptor "github.com/epifi/be-common/pkg/epifigrpc/interceptors/log_sampler"
	"github.com/epifi/be-common/pkg/epifigrpc/interceptors/request_client"
	"github.com/epifi/be-common/pkg/epifigrpc/interceptors/request_context"
	"github.com/epifi/be-common/pkg/epifigrpc/interceptors/status"
	"github.com/epifi/be-common/pkg/localstack"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	queuev2 "github.com/epifi/be-common/pkg/queue/v2"
	"github.com/epifi/be-common/pkg/ratelimiter"
	"github.com/epifi/be-common/pkg/ratelimiter/store"
	"github.com/epifi/be-common/pkg/retry"
	pkgTracing "github.com/epifi/be-common/pkg/tracing/opentelemetry"
)

// DisableFlagWaitTimeout defines constant timeout to sleep when a consumer is disabled.
// The dynamic configuration will be checked next after this timeout.
const DisableFlagWaitTimeout = time.Second

// Register various time series.
// Time series name may contain labels in Prometheus format - see below.
var (
	grpcHandledTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "queue_grpc_handled_total",
			Help: "How many grpc calls processed.",
		},
		[]string{"grpc_code", "grpc_method", "grpc_service", "queue_name"},
	)

	grpcHandledSeconds = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "queue_grpc_handled_seconds",
			Help:    "Time taken by grpc calls.",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"grpc_method", "grpc_service", "queue_name"},
	)

	queueDelayReAddTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "queue_delay_re_add_total",
			Help: "Total Number of times messages are re-added to the queue when delay exceeds 15 min",
		},
		[]string{"queue_name"},
	)

	subscriberRateLimiterAddErrorTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "subscriber_rate_limit_add_error_total",
			Help: "Number of errors encountered while booking slots in rate limiter for subscriber",
		}, []string{"grpc_method", "queue_name"})

	subscriberRateLimiterRemoveErrorTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "subscriber_rate_limit_remove_error_total",
			Help: "Number of errors encountered while removing unused slots in rate limiter for subscriber",
		}, []string{"grpc_method", "queue_name"})

	subscriberSlotAddTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "subscriber_rate_limit_add_total",
			Help: "Number of booked slots in rate limiter for subscriber",
		}, []string{"config_key", "queue_name"})

	subscriberSlotRemoveTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "subscriber_rate_limit_remove_total",
			Help: "Number of removed slots in rate limiter for subscriber",
		}, []string{"config_key", "queue_name"})

	subscriberSlotsAvlbl = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "subscriber_rate_limit_occupied_slots_total",
			Help: "slots occupied by subscriber at any point of time",
		}, []string{"config_key", "queue_name"})

	sqsWorkersRunning = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "subscriber_worker_total",
			Help: "Total number of SQS workers running currently",
		}, []string{"queue_name"})

	sqsConsumerDisabled = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "subscriber_consumer_disabled_total",
			Help: "Total number of times Consumers disabled",
		}, []string{"queue_name"})

	subscriberRetryAttemptTotal = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "subscriber_retry_attempt_total",
			Help:    "Total number of retries for consuming the packets in the queue is attempted",
			Buckets: prometheus.ExponentialBuckets(1, 2, 8),
		}, []string{"queue_name"})

	subscriberRetryIntervalSeconds = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "subscriber_retry_interval_seconds",
			Help:    "Interval between subscriber retry in seconds",
			Buckets: prometheus.ExponentialBuckets(1, 2.5, 10),
		}, []string{"queue_name"})

	subscriberRetryIntervalHours = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "subscriber_retry_interval_hours",
			Help:    "Interval between subscriber retry in hours",
			Buckets: prometheus.ExponentialBuckets(0.5, 2, 9),
		}, []string{"queue_name"})
)

func registerMetricIfNotExists(collector prometheus.Collector) {
	if !prometheus.DefaultRegisterer.Unregister(collector) {
		// The metric was not previously registered, so we register it now
		prometheus.MustRegister(collector)
	}
}

// Todo(shafi): Remove this function and directly register metrics without init once migration to sdk v2 completed
func registerSubscriberMetrics() {
	registerMetricIfNotExists(grpcHandledTotal)
	registerMetricIfNotExists(grpcHandledSeconds)
	registerMetricIfNotExists(queueDelayReAddTotal)
	registerMetricIfNotExists(subscriberRateLimiterAddErrorTotal)
	registerMetricIfNotExists(subscriberRateLimiterRemoveErrorTotal)
	registerMetricIfNotExists(subscriberSlotAddTotal)
	registerMetricIfNotExists(subscriberSlotRemoveTotal)
	registerMetricIfNotExists(subscriberSlotsAvlbl)
	registerMetricIfNotExists(sqsWorkersRunning)
	registerMetricIfNotExists(sqsConsumerDisabled)
	registerMetricIfNotExists(subscriberRetryAttemptTotal)
	registerMetricIfNotExists(subscriberRetryIntervalSeconds)
	registerMetricIfNotExists(subscriberRetryIntervalHours)
}

func init() {
	registerSubscriberMetrics()
}

var (
	attributeNotFoundErr = errors.New("message attribute not found")
)

// defaultGrpcInterceptor returns the default interceptor to be used in trigger of gRPC method to process queue messages
func defaultGrpcInterceptor(subConf *gencfg.GrpcServerConfig, customInterceptors ...grpc.UnaryServerInterceptor) grpc.UnaryServerInterceptor {
	interceptors := []grpc.UnaryServerInterceptor{
		pkgTracing.NewGRPCUnaryServerInterceptor(),
		grpc_recovery.UnaryServerInterceptor(epifigrpc.RecoveryHandlerOpts()...),
		status.ResponseOutUnaryServerInterceptor(),
		grpc_prometheus.UnaryServerInterceptor,
		logSamplingInterceptor.NewUnaryServerInterceptor(epifigrpc.GetRPCLogSamplingRatesMapInstance().GetRPCLogSamplingRate),
		request_context.RequestContextInterceptor(),
		// TODO(neeraj): Hook up an actual config so we can start using log suppression etc
		epifigrpc.NewCustomGrpcLoggingInterceptor(subConf),
		request_client.CustomRPCMetricsServerInterceptor(),
		status.ResponseInSubscriberServerInterceptor(),
	}
	interceptors = append(interceptors, customInterceptors...)
	return grpc_middleware.ChainUnaryServer(interceptors...)
}

type SqsSubscriber struct {
	numWorkers  int
	maxMessages int
	// Note: conf.BatchFetchTimeout defines the duration to wait until all the messages in previous batch is processed.
	// if all the messages are processed before this timeout, then subscriber will proceed with the next batch fetch.
	// else after the timeout, the subscriber will consume <MaxMessages - YetToBeProcessedFromPreviousBatch> and process them.
	// This is done to ensure that at any given time, a worker processes only MaxMessages at max.
	conf       *gencfg.SqsSubscriber
	queueSvc   queuev2.QueueService
	message    queue.SubscribeMessage
	methodDesc grpc.MethodDesc
	server     interface{}

	// Retry strategy will allow client to use subscriber with retry. It will push the message to the service method
	// based on the Retry strategy attributes until max retries reached
	// Delays in the retries will be accommodated using sqs Message visibility functionality
	// if a message is not processed within default message visibility delay or message is dropped by the thread, the
	// subscriber will send the message again to the service method and it will be considered as retry
	retryStrategy retry.RetryStrategy
	// isStopSignalReceived flag is set to true when StopWorkers method is called to notify the
	// subscriber to stop polling any new message from the queue.
	isStopSignalReceived *atomic.Bool
	// lock is used to synchronize concurrent state update
	lock sync.Mutex
	// runningWorkerCounter maintains an atomic counter of how many workers are running.
	// this makes sure we don't have over-provision workers on every start worker call
	runningWorkerCounter *atomic.Int32
	// rate limiter client
	rl              ratelimiter.RateLimiter
	grpcInterceptor grpc.UnaryServerInterceptor
}

// compile time check to make sure SqsSubscriber implements queue.Subscriber
var _ queue.Subscriber = &SqsSubscriber{}

// Deprecated: NewSubscriberWithConfig in favour of NewSubscriberWithConfigV1
func NewSubscriberWithConfig(ctx context.Context, cf *cfg.SqsSubscriber, svc *awsSqs.Client, message queue.SubscribeMessage, ratelimiterRedis *redis.Client, customInterceptors ...grpc.UnaryServerInterceptor) (*SqsSubscriber, error) {
	// Copy static conf to generated conf to support backward compatibility for legacy code
	genCfg, _ := gencfg.NewSqsSubscriber()
	subConf := &gencfg.GrpcServerConfig{}
	subConf.Init()
	err := genCfg.Set(cf, false, nil)
	if err != nil {
		return nil, err
	}
	return newSubscriberWithConfig(ctx, genCfg, svc, message, ratelimiterRedis, subConf, customInterceptors...)
}

func newSubscriberWithConfig(ctx context.Context, cf *gencfg.SqsSubscriber, svc *awsSqs.Client, message queue.SubscribeMessage, ratelimiterRedis *redis.Client, subConf *gencfg.GrpcServerConfig, customInterceptors ...grpc.UnaryServerInterceptor) (*SqsSubscriber, error) {
	if message == nil {
		message = queue.NewDefaultMessage()
	}

	retryStrategy, err := retry.NewStrategyFromConfig(cf.RetryStrategy())
	if err != nil {
		return nil, fmt.Errorf("failed to create retry strategy: %w", err)
	}
	queueOwnerAccountId := cf.QueueOwnerAccountId()
	if session.IsRemoteDebugEnabled() && session.IsConsumerEnabled() {
		queueOwnerAccountId = localstack.DefaultAccountID
	}
	queueSvc, err := NewQueueServiceWithResolver(ctx, svc, cf.GetQueueName(), queueOwnerAccountId)
	if err != nil {
		return nil, fmt.Errorf("failed to create queue service: %w", err)
	}

	numWorkers := cf.NumWorkers()
	maxMessages := cf.MaxMessages()
	if numWorkers == 0 {
		numWorkers = queue.DefaultNumWorkers
	}
	if maxMessages == 0 {
		maxMessages = queue.DefaultMaxMessage
	}
	if cf.BatchFetchTimeout() == 0 {
		cf.SetBatchFetchTimeout(defaultBatchFetchTimeout, true, nil)
	}
	sqsSubs := &SqsSubscriber{
		conf:                 cf,
		numWorkers:           numWorkers,
		maxMessages:          maxMessages,
		queueSvc:             queueSvc,
		message:              message,
		retryStrategy:        retryStrategy,
		isStopSignalReceived: atomic.NewBool(false),
		runningWorkerCounter: atomic.NewInt32(0),
		grpcInterceptor:      defaultGrpcInterceptor(subConf, customInterceptors...),
	}
	// check Namespace value to find out if the rate limit config object is not configured.
	// nil check on cf.RateLimitConfig() won't work since the generated config object will always be initialed with Zeroth values of the struct.
	if cf.RateLimitConfig().Namespace() != "" {
		sqsSubs.rl = ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(ratelimiterRedis), cf.RateLimitConfig())
	}
	return sqsSubs, nil
}

type RegisterConsumerMethodFn func(subscriber queue.Subscriber)

// NewSubscriberWithConfigV1 initializes queue.Subscriber implementation for AWS SQS. It also optionally starts
// polling workers based on the config flag StartOnServerStart and queue.IsWorkerInitializationEnabled
func NewSubscriberWithConfigV1(ctx context.Context, cf *cfg.SqsSubscriber, svc *awsSqs.Client, message queue.SubscribeMessage, registerConsumerMethod RegisterConsumerMethodFn, ratelimiterRedis *redis.Client, subConf *gencfg.GrpcServerConfig, customInterceptors ...grpc.UnaryServerInterceptor) (*SqsSubscriber, error) {
	// Copy static conf to generated conf to support backward compatibility for legacy code
	genCfg, _ := gencfg.NewSqsSubscriber()
	err := genCfg.Set(cf, false, nil)
	if err != nil {
		return nil, err
	}
	return NewSubscriberWithGenConfigV1(ctx, genCfg, svc, message, registerConsumerMethod, ratelimiterRedis, subConf, customInterceptors...)
}

// NewSubscriberWithConfigV1 initializes queue.Subscriber implementation for AWS SQS. It also optionally starts
// polling workers based on the config flag StartOnServerStart and queue.IsWorkerInitializationEnabled
func NewSubscriberWithGenConfigV1(ctx context.Context, cfg *gencfg.SqsSubscriber, svc *awsSqs.Client, message queue.SubscribeMessage, registerConsumerMethod RegisterConsumerMethodFn, ratelimiterRedis *redis.Client, subConf *gencfg.GrpcServerConfig, customInterceptors ...grpc.UnaryServerInterceptor) (*SqsSubscriber,
	error) {
	sub, err := newSubscriberWithConfig(ctx, cfg, svc, message, ratelimiterRedis, subConf, customInterceptors...)
	if err != nil {
		return nil, err
	}

	registerConsumerMethod(sub)

	if (!globalcfg.IsTestTenantEnabled() && !cfg.StartOnServerStart()) || (globalcfg.IsRemoteDebugEnabled() && !session.IsConsumerEnabled()) {
		return sub, nil
	}

	queue.RegisterSubscriber(sub)

	if queue.IsWorkerInitializationEnabled() {
		sub.StartWorkers()
	}

	return sub, nil
}

func (p *SqsSubscriber) queueGrpcHandledTotalCounter(grpcCode string) prometheus.Counter {
	grpcMethod := p.methodDesc.MethodName
	grpcService := reflect.TypeOf(p.server).Elem().Name()
	return grpcHandledTotal.WithLabelValues(grpcCode, grpcMethod, grpcService, p.queueSvc.GetQueueName())
}

func (p *SqsSubscriber) queueGrpcHandledSeconds() prometheus.Observer {
	grpcMethod := p.methodDesc.MethodName
	grpcService := reflect.TypeOf(p.server).Elem().Name()
	return grpcHandledSeconds.WithLabelValues(grpcMethod, grpcService, p.queueSvc.GetQueueName())
}

// StartWorkers function sets off 'numWorkers' go routines to poll the queue and further calls consumers to
// consume messages from the queue
func (p *SqsSubscriber) StartWorkers() {
	if cfg.IsRemoteDebugEnabled() && !session.IsConsumerEnabled() {
		// Queue consumption in remote debugging mode is disabled to avoid disruption in normal operation of any non prod environment.
		logger.ErrorNoCtx("Skipping queue initialization since the server is started in remote debugging mode and consumer is disabled")
		return
	}
	p.lock.Lock()
	defer p.lock.Unlock()

	if p.runningWorkerCounter.Load() == int32(p.numWorkers) {
		logger.InfoNoCtx("desired number of workers already running")
		return
	}

	logger.DebugNoCtx("starting worker", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()))
	// reset the isStopSignalReceived flag
	p.isStopSignalReceived.Store(false)

	// start the worker go routines
	runningWorkersCount := int(p.runningWorkerCounter.Load())
	for w := 1; w <= p.numWorkers-runningWorkersCount; w++ {
		goroutine.RunWithCtx(context.Background(), func(ctx context.Context) {
			batchProcessor := newBatchProcessor(p, p.maxMessages, p.conf.BatchFetchTimeout())
			consumer := newConsumer(p, batchProcessor)
			consumer.consume()
		})
		p.runningWorkerCounter.Inc()
		sqsWorkersRunning.WithLabelValues(p.queueSvc.GetQueueName()).Inc()
	}

	logger.InfoNoCtx("workers up and running", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()),
		zap.Int32("count", p.runningWorkerCounter.Load()))
}

// StopWorkers method sets the isStopSignalReceived flag to true to notify the workers to stop
// polling any new message from the queue.
func (p *SqsSubscriber) StopWorkers() {
	p.lock.Lock()
	defer p.lock.Unlock()
	logger.InfoNoCtx("stopping worker", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()))
	p.isStopSignalReceived.Store(true)
}

// processQueueMessage processes the queue message.
// contains the logic to add delay to the packets based on delivery time message attributes.
// calls consumer service method in case delivery attribute is missing from the packet.
// nolint: funlen
func (p *SqsSubscriber) processQueueMessage(m *types.Message) {
	ctx := context.Background()

	// This will put a hard limit on max retries possible per queue
	// As we are not deleting the messages from the queue, but changing the visibility timeout to 1 sec,
	// message will be received by the consumer even after max retries is reached
	approximateReceiveCount, err := p.getApproximateReceiveCount(m)
	if err != nil {
		logger.Error(ctx, "unable to parse approximateReceiveCount from message", zap.Error(err))
		return
	}

	if p.retryStrategy.IsRetryExhausted(approximateReceiveCount) {
		err := p.queueSvc.ChangeVisibility(ctx, int32(visibilityTimeoutAfterMaxRetries.Seconds()), m)
		if err != nil {
			logger.Warn("unable to change visibility timeout of the message", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()), zap.Error(err))
		}
		return
	}

	deliveryTime, err := p.getDeliveryTime(m)
	if err != nil {
		if !errors.Is(err, attributeNotFoundErr) {
			logger.Error(ctx, "message delivery attribute corrupted", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()), zap.Error(err))
		}

		resHeader := p.callConsumerMethod(ctx, m)
		if resHeader != nil {
			p.processResponse(ctx, m, resHeader)
		}
		return
	}

	// safety check to avoid killing of subscriber thread due to panic
	if deliveryTime == nil {
		logger.Error(ctx, "nil delivery time found after parsing")
		return
	}

	// calculate the time left and round of it till nearest seconds.
	timeLeft := time.Until(*deliveryTime).Round(time.Second)

	// Following case occur in two scenarios-
	// 1. System was down and subscriber couldn't consume packets, as a result queue message delivery time has
	// already elapsed.
	// 2. System was up there is 1 sec left in the expected delivery time in such scenarios we defer the delay and
	// send the packet to the consumer service.
	if timeLeft <= 1*time.Second {
		resHeader := p.callConsumerMethod(ctx, m)
		if resHeader != nil {
			p.processResponse(ctx, m, resHeader)
		}
		return
	}

	// In case more than 1 sec is left for delivery. Add a new packet to the queue with appropriate delay and
	// delete the current packet.
	// NOTE- it is possible that packet is re-added to the queue with a delay and couldn't be deleted. In such case,
	// there is a possibility that more than 1 packet will be delivered to the consumer at delivery time. But,
	// case doesn't break the core assumption of SQS which at least one delivery. Hence, it is probably ok as the
	// consumer service logic is always made idempotent against duplicate packets.
	delayInSecond := int32(math.Min(maxSQSDelayDuration.Seconds(), timeLeft.Seconds()))

	sqsMsgInp := &awsSqs.SendMessageInput{
		MessageBody:       m.Body,
		MessageAttributes: m.MessageAttributes,
	}

	_, err = p.queueSvc.AddToQueue(ctx, sqsMsgInp, &delayInSecond)
	if err != nil {
		logger.Error(ctx, "failed to re-add delayed message to the queue", zap.Error(err))
		return
	}

	queueDelayEventTotal.WithLabelValues(p.queueSvc.GetQueueName()).Inc()
	queueDelayReAddTotal.WithLabelValues(p.queueSvc.GetQueueName()).Inc()

	err = p.queueSvc.DeleteFromQueue(ctx, m)
	if err != nil {
		logger.Error(ctx, "unable to delete message from queue", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()), zap.Error(err))
	}

	queueDelayEventTotal.WithLabelValues(p.queueSvc.GetQueueName()).Dec()
}

// callConsumerMethod calls respective consumer service method and takes action based on service response
// nolint:funlen
func (p *SqsSubscriber) callConsumerMethod(ctx context.Context, m *types.Message) *pb.ConsumerResponseHeader {
	var (
		resHeader *pb.ConsumerResponseHeader
	)

	// Add values to this context object
	ctx, err := SetMessageAttributesInContext(ctx, m)
	if err != nil {
		logger.ErrorNoCtx("failed to set values in context from the message attributes", zap.Error(err))
		// to make sure in case of error, ctx isn't set to nil
		ctx = context.Background()
	}

	// Decode item
	protoMsgString, err := p.UnMarshal(m, p.message)
	if err != nil {
		logger.Error(ctx, "Unable to unmarshal sqs msg: %s", zap.Error(err))
		return nil
	}

	// Create unmarshaller
	df := func(v interface{}) error {
		um := protojson.UnmarshalOptions{DiscardUnknown: true}
		if err = um.Unmarshal([]byte(*protoMsgString), v.(proto.Message)); err != nil {
			return fmt.Errorf("failed to unmarshal request: %w", err)
		}

		request, ok := v.(queue.ConsumerRequest)
		if !ok {
			return fmt.Errorf("expected queue.ConsumerRequest got %T", v)
		}

		approximateReceiveCount, tErr := p.getApproximateReceiveCount(m)
		if tErr != nil {
			return fmt.Errorf("failed to get approximateReceiveCount: %w", err)
		}

		request.SetRequestHeader(&pb.ConsumerRequestHeader{IsLastAttempt: p.retryStrategy.IsMaxRetryMet(approximateReceiveCount)})
		logger.Debug(ctx, "Passing data to service method")
		return nil
	}

	var ctxCancel context.CancelFunc
	ctx = metadata.NewIncomingContext(ctx, metadata.MD{})
	ctx, ctxCancel = context.WithTimeout(ctx, 5*time.Minute)
	defer ctxCancel()
	// Invokes consumer service method to process the queue message
	start := time.Now()
	res, err := p.methodDesc.Handler(p.server, ctx, df, p.grpcInterceptor)
	p.queueGrpcHandledSeconds().Observe(time.Since(start).Seconds())
	if err != nil {
		p.queueGrpcHandledTotalCounter("UNKNOWN").Inc()
		logger.Error(ctx, fmt.Sprintf("consumer method returned error %v - %v", p.server, p.methodDesc), zap.Error(err))

		// any error while calling consumer service handler is treated as an transient failure
		// and after retries are exhausted the packet is moved to dead letter queue
		resHeader = &pb.ConsumerResponseHeader{Status: pb.MessageConsumptionStatus_TRANSIENT_FAILURE}
	} else {
		p.queueGrpcHandledTotalCounter("OK").Inc()
		val, ok := res.(queue.ConsumerMethodResponse)
		if !ok {
			logger.Error(ctx, fmt.Sprintf("expected queue.ConsumerMethodResponse got %T", res))

			// in case of in valid response we treat it as transient failure and
			// after retries are exhausted the packet is moved to dead letter queue
			resHeader = &pb.ConsumerResponseHeader{Status: pb.MessageConsumptionStatus_TRANSIENT_FAILURE}
		}

		resHeader = val.GetResponseHeader()
	}

	logger.Debug(ctx, "consumer method response",
		zap.String("consumption status", resHeader.GetStatus().String()),
		zap.Int64("next timeout", resHeader.GetNextTimeout()))

	return resHeader

}

// processResponse takes action based on consumer consumption status
// Deletes message from the queue in case of PERMANENT_FAILURE or SUCCESS
// In case of TRANSIENT_FAILURE-
//  1. Increases the visibility timeout of the message in case max retry is not met.
//  2. In case max retry is met message is deleted from the queue.
//
// the rate of increase visibility timeout is governed by retry strategy plugged into the subscriber
func (p *SqsSubscriber) processResponse(ctx context.Context, msg *types.Message, header *pb.ConsumerResponseHeader) {
	switch header.GetStatus() {
	case pb.MessageConsumptionStatus_SUCCESS, pb.MessageConsumptionStatus_PERMANENT_FAILURE:
		err := p.queueSvc.DeleteFromQueue(ctx, msg)
		if err != nil {
			logger.Error(ctx, "unable to delete message from queue", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()), zap.Error(err))
			return
		}

		approximateReceiveCount, err := p.getApproximateReceiveCount(msg)
		if err != nil {
			logger.Error(ctx, "unable to parse approximateReceiveCount from message", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()), zap.Error(err))
			return
		}

		recordFinalAttemptCount(p.queueSvc.GetQueueName(), approximateReceiveCount)
	case pb.MessageConsumptionStatus_TRANSIENT_FAILURE:
		p.processTransientFailure(ctx, msg, header)
	default:
		logger.Warn("consumer returned unknown status code")
	}
}

// processTransientFailure updates visibility timeout of the SQS msg in case of a transient failure.
// In case consumer service returns a next timeout in response then it is given preference else
// default subscriber retry strategy is used
func (p *SqsSubscriber) processTransientFailure(ctx context.Context, msg *types.Message, header *pb.ConsumerResponseHeader) {
	// This will put a hard limit on max retries possible per queue
	approximateReceiveCount, err := p.getApproximateReceiveCount(msg)
	if err != nil {
		logger.Error(ctx, "unable to parse approximateReceiveCount from message", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()), zap.Error(err))
		return
	}

	if p.retryStrategy.IsMaxRetryMet(approximateReceiveCount) {
		recordFinalAttemptCount(p.queueSvc.GetQueueName(), approximateReceiveCount)
		err := p.queueSvc.ChangeVisibility(ctx, int32(visibilityTimeoutAfterMaxRetries.Seconds()), msg)
		if err != nil {
			logger.Warn("unable to change visibility timeout of the message", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()), zap.Error(err))
		}
		logger.Info(ctx, "sqs message moved to dlq", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()))
		return
	}

	if header.GetNextTimeout() > 0 {
		recordRetryInterval(p.queueSvc.GetQueueName(), time.Duration(header.GetNextTimeout())*time.Second)
		// TODO(993): define a max threshold to be supported for visibility timeout
		// beyond a particular
		err := p.queueSvc.ChangeVisibility(ctx, int32(header.GetNextTimeout()), msg)
		if err != nil {
			logger.Warn("unable to change message timeout from queue", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()), zap.Error(err))
		}
		return
	}

	nextTimeout := p.retryStrategy.GetNextRetryInterval(approximateReceiveCount)
	if nextTimeout > 0 {
		recordRetryInterval(p.queueSvc.GetQueueName(), time.Duration(nextTimeout)*time.Second)
		// TODO(993): define a max threshold to be supported for visibility timeout
		// beyond a particular
		err := p.queueSvc.ChangeVisibility(ctx, int32(nextTimeout), msg)
		if err != nil {
			logger.Warn("unable to change message timeout from queue", zap.String(logger.QUEUE_NAME, p.queueSvc.GetQueueName()), zap.Error(err))
		}
	}
}

// RegisterService registers given grpc service to the subscriber using reflection.
// subscriber then uses the map to invoke consumer methods
func (p *SqsSubscriber) RegisterService(serviceDesc *grpc.ServiceDesc, service interface{}, methodName string) {
	handlerType := reflect.TypeOf(serviceDesc.HandlerType).Elem()
	serviceType := reflect.TypeOf(service)
	if !serviceType.Implements(handlerType) {
		logger.Fatal(fmt.Sprintf("subscriber.RegisterService found the handler of type %v that does not satisfy %v", serviceType, handlerType))
	}
	// we are going ahead with name based method identification, this may change in future if we have better way
	// to do this
	for _, m := range serviceDesc.Methods {
		if m.MethodName == methodName {
			p.methodDesc = m
		}
	}
	if p.methodDesc.Handler == nil {
		logger.Fatal(fmt.Sprintf("Subscriber can't find method with name %v in %v service", methodName, serviceType))
	}
	p.server = service
	// init rate limiter metric to 0
	ck := GetConfigKeyForSubscriberRateLimiting(p.queueSvc.GetQueueName(), methodName)

	subscriberSlotAddTotal.WithLabelValues(ck, p.queueSvc.GetQueueName())
	subscriberSlotRemoveTotal.WithLabelValues(ck, p.queueSvc.GetQueueName())
	subscriberSlotsAvlbl.WithLabelValues(ck, p.queueSvc.GetQueueName())
}

// UnMarshal returns message body string from the queue message.
func (p *SqsSubscriber) UnMarshal(message interface{}, sm queue.SubscribeMessage) (*string, error) {
	sqsMsg, ok := message.(*types.Message)
	if !ok {
		return nil, fmt.Errorf("expected sqs.Message got %T", message)
	}

	if sqsMsg == nil || sqsMsg.Body == nil {
		return nil, fmt.Errorf("empty message/body from queue")
	}

	ps, err := sm.GetProtoString(sqsMsg.Body)
	if err != nil {
		return nil, err
	}

	return ps, nil
}

// getApproximateReceiveCount returns approximate receive count of the current SQS message
func (p *SqsSubscriber) getApproximateReceiveCount(msg *types.Message) (uint, error) {
	approximateReceiveCountString := msg.Attributes[string(types.MessageSystemAttributeNameApproximateReceiveCount)]
	approximateReceiveCount, err := strconv.ParseInt(approximateReceiveCountString, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("unable to parse approximateReceiveCount from message: %w", err)
	}
	return uint(approximateReceiveCount), nil
}

// getDeliveryTime fetches delivery time message attribute from a queue message.
// Returns error in case attribute is not found or delivery time can't be parsed.
func (p *SqsSubscriber) getDeliveryTime(msg *types.Message) (*time.Time, error) {
	deliveryTimeMessageAttribute, ok := msg.MessageAttributes[deliveryTimeMessageAttributeName]
	if !ok {
		return nil, fmt.Errorf("delivery time attribute not found: %w", attributeNotFoundErr)
	}

	// fail safe to avoid runtime nil panics
	if deliveryTimeMessageAttribute.StringValue == nil {
		return nil, fmt.Errorf("delivery timestamp not found")
	}

	parsedTime, err := time.Parse(time.RFC3339, *deliveryTimeMessageAttribute.StringValue)
	if err != nil {
		return nil, fmt.Errorf("couldn't parse delivery time: :%v :%w", deliveryTimeMessageAttribute.StringValue,
			err)
	}

	return &parsedTime, nil
}

// Get map[string]string from message for all attributes which were passed from context
// use the underlying context Setter method to set those key value pairs in context
func SetMessageAttributesInContext(ctx context.Context, msg *types.Message) (context.Context, error) {
	contextKeyValueMap := make(map[string]string)

	contextMetadataAttribute, ok := msg.MessageAttributes[awsPkg.ContextMetadataMessageAttributeKey]
	if ok {
		b, err := base64.StdEncoding.DecodeString(string(contextMetadataAttribute.BinaryValue))
		if err != nil {
			return nil, errorsPkg.Wrap(err, fmt.Sprintf("failed to decode attribute %s value", awsPkg.ContextMetadataMessageAttributeKey))
		}

		err = json.Unmarshal(b, &contextKeyValueMap)
		if err != nil {
			return nil, errorsPkg.Wrap(err, fmt.Sprintf("failed to unmarshal attribute %s value", awsPkg.ContextMetadataMessageAttributeKey))
		}

		return epificontext.GetContextForAsyncFlow(ctx, contextKeyValueMap, context.WithValue), nil
	}

	for _, key := range epificontext.GetKeysForAsyncFlow() {
		attribute, ok := msg.MessageAttributes[key]
		if !ok || attribute.StringValue == nil {
			continue
		}
		contextKeyValueMap[key] = *attribute.StringValue
	}

	return epificontext.GetContextForAsyncFlow(ctx, contextKeyValueMap, context.WithValue), nil
}

func recordFinalAttemptCount(queueName string, attempt uint) {
	subscriberRetryAttemptTotal.WithLabelValues(queueName).Observe(float64(attempt))
}

func recordRetryInterval(queueName string, retryInterval time.Duration) {
	subscriberRetryIntervalSeconds.WithLabelValues(queueName).Observe(retryInterval.Seconds())
	subscriberRetryIntervalHours.WithLabelValues(queueName).Observe(retryInterval.Hours())
}
