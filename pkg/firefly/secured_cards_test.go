package firefly

import (
	"reflect"
	"testing"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	types "github.com/epifi/gamma/api/typesv2"
)

func TestGetDepositTermDetailsList(t *testing.T) {

	type args struct {
		depositInterestDetails []*types.DepositInterestDetails
		minTerm                *types.DepositTerm
		maxTerm                *types.DepositTerm
	}
	tests := []struct {
		name string
		args args
		want []*types.DepositInterestDetails
	}{
		{
			name: "No elements in deposit interest details",
			args: args{
				depositInterestDetails: nil,
				minTerm:                nil,
				maxTerm:                nil,
			},
			want: nil,
		},
		{
			name: "One elements in deposit interest details",
			args: args{
				depositInterestDetails: GetDepositInterestList()[0:1],
				minTerm:                nil,
				maxTerm:                nil,
			},
			want: nil,
		},
		{
			name: "Two elements in deposit interest details,min term lesser than first term",
			args: args{
				depositInterestDetails: GetDepositInterestList()[0:2],
				minTerm: &types.DepositTerm{
					Days:   6,
					Months: 0,
				},
				maxTerm: &types.DepositTerm{
					Days:   12,
					Months: 0,
				},
			},
			want: nil,
		},
		{
			name: "Two elements in deposit interest details, min term greater than last term",
			args: args{
				depositInterestDetails: GetDepositInterestList()[0:2],
				minTerm: &types.DepositTerm{
					Days:   100,
					Months: 0,
				},
				maxTerm: &types.DepositTerm{
					Days:   200,
					Months: 0,
				},
			},
			want: nil,
		},
		{
			name: "Two elements in deposit interest details, max term lesser than first term",
			args: args{
				depositInterestDetails: GetDepositInterestList()[0:2],
				minTerm: &types.DepositTerm{
					Days:   1,
					Months: 0,
				},
				maxTerm: &types.DepositTerm{
					Days:   2,
					Months: 0,
				},
			},
			want: nil,
		},
		{
			name: "Two elements in deposit interest details,min term greater than first term, max term lesser than second term",
			args: args{
				depositInterestDetails: GetDepositInterestList()[0:2],
				minTerm: &types.DepositTerm{
					Days:   8,
					Months: 0,
				},
				maxTerm: &types.DepositTerm{
					Days:   29,
					Months: 0,
				},
			},
			want: []*types.DepositInterestDetails{
				{
					InterestRate: "3.00",
					Term: &types.DepositTerm{
						Days:   8,
						Months: 0,
					},
				},
				{
					InterestRate: "3.25",
					Term: &types.DepositTerm{
						Days:   29,
						Months: 0,
					},
				},
			},
		},
		{
			name: "Two elements in deposit interest details,min term greater than first term, max term equal to second term",
			args: args{
				depositInterestDetails: GetDepositInterestList()[0:2],
				minTerm: &types.DepositTerm{
					Days:   8,
					Months: 0,
				},
				maxTerm: &types.DepositTerm{
					Days:   30,
					Months: 0,
				},
			},
			want: []*types.DepositInterestDetails{
				{
					InterestRate: "3.00",
					Term: &types.DepositTerm{
						Days:   8,
						Months: 0,
					},
				},
				{
					InterestRate: "3.25",
					Term: &types.DepositTerm{
						Days:   30,
						Months: 0,
					},
				},
			},
		},
		{
			name: "Two elements in deposit interest details,min term equal to first term, max term less than second term",
			args: args{
				depositInterestDetails: GetDepositInterestList()[0:2],
				minTerm: &types.DepositTerm{
					Days:   7,
					Months: 0,
				},
				maxTerm: &types.DepositTerm{
					Days:   29,
					Months: 0,
				},
			},
			want: []*types.DepositInterestDetails{
				{
					InterestRate: "3.00",
					Term: &types.DepositTerm{
						Days:   7,
						Months: 0,
					},
				},
				{
					InterestRate: "3.25",
					Term: &types.DepositTerm{
						Days:   29,
						Months: 0,
					},
				},
			},
		},
		{
			name: "Two elements in deposit interest details,min term equal to first term, max term equal to second term",
			args: args{
				depositInterestDetails: GetDepositInterestList()[0:2],
				minTerm: &types.DepositTerm{
					Days:   7,
					Months: 0,
				},
				maxTerm: &types.DepositTerm{
					Days:   30,
					Months: 0,
				},
			},
			want: []*types.DepositInterestDetails{
				{
					InterestRate: "3.00",
					Term: &types.DepositTerm{
						Days:   7,
						Months: 0,
					},
				},
				{
					InterestRate: "3.25",
					Term: &types.DepositTerm{
						Days:   30,
						Months: 0,
					},
				},
			},
		},
		{
			name: "15 elements in deposit interest list, min term is 181 days, max term is 60 months",
			args: args{
				depositInterestDetails: GetDepositInterestList(),
				minTerm: &types.DepositTerm{
					Days:   181,
					Months: 0,
				},
				maxTerm: &types.DepositTerm{
					Days:   0,
					Months: 60,
				},
			},
			want: GetDepositInterestList()[6:13],
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetDepositInterestDetailsList(tt.args.depositInterestDetails, tt.args.minTerm, tt.args.maxTerm); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDepositTermDetailsList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func GetDepositInterestList() []*types.DepositInterestDetails {
	return []*types.DepositInterestDetails{
		{
			InterestRate: "3.00",
			Term: &types.DepositTerm{
				Days:   7,
				Months: 0,
			},
		},
		{
			InterestRate: "3.25",
			Term: &types.DepositTerm{
				Days:   30,
				Months: 0,
			},
		},
		{
			InterestRate: "4.00",
			Term: &types.DepositTerm{
				Days:   46,
				Months: 0,
			},
		},
		{
			InterestRate: "4.25",
			Term: &types.DepositTerm{
				Days:   61,
				Months: 0,
			},
		},
		{
			InterestRate: "4.50",
			Term: &types.DepositTerm{
				Days:   91,
				Months: 0,
			},
		},
		{
			InterestRate: "4.75",
			Term: &types.DepositTerm{
				Days:   120,
				Months: 0,
			},
		},
		{
			InterestRate: "5.75",
			Term: &types.DepositTerm{
				Days:   181,
				Months: 0,
			},
		},
		{
			InterestRate: "6.00",
			Term: &types.DepositTerm{
				Days:   271,
				Months: 0,
			},
		},
		{
			InterestRate: "6.80",
			Term: &types.DepositTerm{
				Days:   0,
				Months: 12,
			},
		},
		{
			InterestRate: "7.25",
			Term: &types.DepositTerm{
				Days:   0,
				Months: 15,
			},
		},
		{
			InterestRate: "6.75",
			Term: &types.DepositTerm{
				Days:   1,
				Months: 24,
			},
		},
		{
			InterestRate: "6.60",
			Term: &types.DepositTerm{
				Days:   0,
				Months: 36,
			},
		},
		{
			InterestRate: "6.60",
			Term: &types.DepositTerm{
				Days:   0,
				Months: 60,
			},
		},
		{
			InterestRate: "6.60",
			Term: &types.DepositTerm{
				Days:   0,
				Months: 2222,
			},
		},
		{
			InterestRate: "6.60",
			Term: &types.DepositTerm{
				Days:   0,
				Months: 2223,
			},
		},
	}
}

func TestRoundAmountToNearestHundred(t *testing.T) {
	type args struct {
		amount *moneyPb.Money
	}
	tests := []struct {
		name string
		args args
		want *moneyPb.Money
	}{
		{
			name: "Given amount: 191.1, Expected Amount: 200.00",
			args: args{
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        191,
					Nanos:        1000000000,
				},
			},
			want: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        200,
				Nanos:        0,
			},
		},
		{
			name: "Given amount: 101.1, Expected Amount: 200.00",
			args: args{
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        101,
					Nanos:        1000000000,
				},
			},
			want: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        200,
				Nanos:        0,
			},
		},
		{
			name: "Given amount: 1968.2, Expected Amount: 2000.00",
			args: args{
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1968,
					Nanos:        2000000000,
				},
			},
			want: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        2000,
				Nanos:        0,
			},
		},
		{
			name: "Given amount: 1.2, Expected Amount: 100.00",
			args: args{
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1,
					Nanos:        2000000000,
				},
			},
			want: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100,
				Nanos:        0,
			},
		},
		{
			name: "Given amount: nil, Expected Amount: 0.00",
			args: args{
				amount: nil,
			},
			want: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        0,
				Nanos:        0,
			},
		},
		{
			name: "Given amount: 10001.2, Expected Amount: 10100.00",
			args: args{
				amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        10001,
					Nanos:        2000000000,
				},
			},
			want: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        10100,
				Nanos:        0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RoundAmountToNearestHundred(tt.args.amount); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RoundAmountToNearestHundred() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSetShouldDisplayValueForSlider(t *testing.T) {
	type args struct {
		visibleDurationForDurationSlider []int32
		sliderValues                     []*types.DurationSliderValue
	}
	tests := []struct {
		name string
		args args
		want []*types.DurationSliderValue
	}{
		{
			name: "success, with three value",
			args: args{
				visibleDurationForDurationSlider: []int32{30, 60, 90},
				sliderValues: []*types.DurationSliderValue{
					{
						Duration: &types.DepositTerm{
							Days: 20,
						},
					},
					{
						Duration: &types.DepositTerm{
							Days: 28,
						},
					},
					{
						Duration: &types.DepositTerm{
							Days: 29,
						},
					},
					{
						Duration: &types.DepositTerm{
							Days: 65,
						},
					},
					{
						Duration: &types.DepositTerm{
							Days: 68,
						},
					},
					{
						Duration: &types.DepositTerm{
							Days: 99,
						},
					},
				},
			},
			want: []*types.DurationSliderValue{
				{
					Duration: &types.DepositTerm{
						Days: 20,
					},
					ShouldDisplay: true,
				},
				{
					Duration: &types.DepositTerm{
						Days: 28,
					},
					ShouldDisplay: false,
				},
				{
					Duration: &types.DepositTerm{
						Days: 29,
					},
					ShouldDisplay: true,
				},
				{
					Duration: &types.DepositTerm{
						Days: 65,
					},
					ShouldDisplay: false,
				},
				{
					Duration: &types.DepositTerm{
						Days: 68,
					},
					ShouldDisplay: true,
				},
				{
					Duration: &types.DepositTerm{
						Days: 99,
					},
					ShouldDisplay: true,
				},
			},
		},
		{
			name: "success, with one value",
			args: args{
				visibleDurationForDurationSlider: []int32{60},
				sliderValues: []*types.DurationSliderValue{
					{
						Duration: &types.DepositTerm{
							Days: 20,
						},
					},
					{
						Duration: &types.DepositTerm{
							Days: 28,
						},
					},
					{
						Duration: &types.DepositTerm{
							Days: 29,
						},
					},
					{
						Duration: &types.DepositTerm{
							Days: 65,
						},
					},
					{
						Duration: &types.DepositTerm{
							Days: 68,
						},
					},
					{
						Duration: &types.DepositTerm{
							Days: 99,
						},
					},
				},
			},
			want: []*types.DurationSliderValue{
				{
					Duration: &types.DepositTerm{
						Days: 20,
					},
					ShouldDisplay: true,
				},
				{
					Duration: &types.DepositTerm{
						Days: 28,
					},
					ShouldDisplay: false,
				},
				{
					Duration: &types.DepositTerm{
						Days: 29,
					},
					ShouldDisplay: true,
				},
				{
					Duration: &types.DepositTerm{
						Days: 65,
					},
					ShouldDisplay: false,
				},
				{
					Duration: &types.DepositTerm{
						Days: 68,
					},
					ShouldDisplay: false,
				},
				{
					Duration: &types.DepositTerm{
						Days: 99,
					},
					ShouldDisplay: true,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SetShouldDisplayValueForSlider(tt.args.visibleDurationForDurationSlider, tt.args.sliderValues); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetShouldDisplayValueForSlider() = %v \n want %v", got, tt.want)
			}
		})
	}
}
