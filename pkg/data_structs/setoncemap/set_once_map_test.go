package setoncemap

import (
	"reflect"
	"sort"
	"sync"
	"testing"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/syncmap"
)

func TestSetOnceMap_Set(t *testing.T) {
	t.<PERSON>llel()
	type args[K comparable, V any] struct {
		key   K
		value V
	}
	type testCase[K comparable, V any] struct {
		name  string
		setup func(setOnceMap *SetOnceMap[K, V])
		args  args[K, V]
		want  V
	}
	tests := []testCase[string, string]{
		{
			name:  "should successfully set a key-value pair if the key hasn't been set before",
			setup: func(setOnceMap *SetOnceMap[string, string]) {},
			args:  args[string, string]{"k1", "v1"},
			want:  "v1",
		},
		{
			name: "should not set a key-value pair if the key has been set before",
			setup: func(setOnceMap *SetOnceMap[string, string]) {
				setOnceMap.Set("k1", "v1")
			},
			args: args[string, string]{"k1", "v2"},
			want: "v1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setOnceMap := NewSetOnceMap[string, string]()
			tt.setup(setOnceMap)

			setOnceMap.Set(tt.args.key, tt.args.value)
			got, _ := setOnceMap.Get(tt.args.key)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Get() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSetOnceMap_Get(t *testing.T) {
	t.Parallel()
	type args[K comparable] struct {
		key K
	}
	type testCase[K comparable, V any] struct {
		name  string
		setup func(setOnceMap *SetOnceMap[K, V])
		args  args[K]
		want  V
		want1 bool
	}
	tests := []testCase[string, string]{
		{
			name: "should return the value associated with the key and true if the key exists",
			setup: func(setOnceMap *SetOnceMap[string, string]) {
				setOnceMap.Set("k1", "v1")
			},
			args:  args[string]{"k1"},
			want:  "v1",
			want1: true,
		},
		{
			name: "should return false if the key doesn't exist",
			setup: func(setOnceMap *SetOnceMap[string, string]) {
			},
			args:  args[string]{"k1"},
			want:  "",
			want1: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setOnceMap := NewSetOnceMap[string, string]()
			tt.setup(setOnceMap)

			got, got1 := setOnceMap.Get(tt.args.key)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Get() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("Get() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestSetOnceMap_ConcurrentSet(t *testing.T) {
	t.Parallel()
	type args[K comparable, V any] struct {
		key   K
		value V
	}
	type testCase[K comparable, V any] struct {
		name  string
		setup func(setOnceMap *SetOnceMap[K, V])
		args  []args[K, V]
		want  []bool
	}
	tests := []testCase[string, string]{
		{
			name:  "should successfully set key-value pairs concurrently",
			setup: func(setOnceMap *SetOnceMap[string, string]) {},
			args: []args[string, string]{
				{"k1", "v1"},
				{"k1", "v2"},
				{"k1", "v3"},
				{"k1", "v4"},
				{"k1", "v5"},
			},
			want: []bool{true, false, false, false, false},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setOnceMap := NewSetOnceMap[string, string]()
			tt.setup(setOnceMap)

			gotChan := make(chan bool, len(tt.args))
			var wg sync.WaitGroup
			for _, arg := range tt.args {
				wg.Add(1)
				go func(arg args[string, string]) {
					defer wg.Done()
					gotChan <- setOnceMap.Set(arg.key, arg.value)
				}(arg)
			}
			wg.Wait()
			close(gotChan)

			got := lo.ChannelToSlice(gotChan)
			sort.Slice(got, func(i, j int) bool {
				return got[i] && !got[j]
			})
			sort.Slice(tt.want, func(i, j int) bool {
				return tt.want[i] && !tt.want[j]
			})
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Set() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSetOnceMap_ConcurrentGet(t *testing.T) {
	t.Parallel()
	type args[K comparable] struct {
		key K
	}
	type testCase[K comparable, V any] struct {
		name  string
		setup func(setOnceMap *SetOnceMap[K, V])
		args  []args[K]
		want  map[K]V
	}
	tests := []testCase[string, string]{
		{
			name: "should successfully get values concurrently",
			setup: func(setOnceMap *SetOnceMap[string, string]) {
				setOnceMap.Set("k1", "v1")
				setOnceMap.Set("k2", "v2")
			},
			args: []args[string]{
				{"k1"},
				{"k2"},
				{"k3"},
			},
			want: map[string]string{
				"k1": "v1",
				"k2": "v2",
				"k3": "",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setOnceMap := NewSetOnceMap[string, string]()
			tt.setup(setOnceMap)

			gotMap := syncmap.Map[string, string]{}
			var wg sync.WaitGroup
			for _, arg := range tt.args {
				wg.Add(1)
				go func(arg args[string]) {
					defer wg.Done()
					got, _ := setOnceMap.Get(arg.key)
					gotMap.Store(arg.key, got)
				}(arg)
			}
			wg.Wait()

			gotMap.Range(func(key string, value string) (continueRange bool) {
				if tt.want[key] != value {
					t.Errorf("Get() got = %v, want %v", value, tt.want[key])
					return false
				}
				return true
			})
		})
	}
}
