package hooks

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/rueidis"
	"github.com/samber/lo"
	"go.uber.org/zap"

	types "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage/metrics"
)

type (
	hooks struct {
		conf *cfg.RedisOptionsV2
	}
	RueidisCommand interface {
		Commands() []string
	}
)

func NewRueidisHooks(config *cfg.RedisOptionsV2) *hooks {
	return &hooks{
		conf: config,
	}
}

func (r *hooks) Do(cl rueidis.Client, ctx context.Context, cmd rueidis.Completed) rueidis.RedisResult {
	st := time.Now()
	redisCommand := r.singleCommandPreProcess(ctx, &cmd)
	resp := cl.Do(ctx, cmd)
	return r.handleRedisResult(resp, redisCommand, st)
}

func (r *hooks) DoMulti(cl rueidis.Client, ctx context.Context, multi ...rueidis.Completed) []rueidis.RedisResult {
	st := time.Now()
	redisCommands := r.pipelineCommandPreProcess(ctx, multi...)
	resp := cl.DoMulti(ctx, multi...)
	return r.handleMultiRedisResult(resp, redisCommands, st)
}

func (r *hooks) DoCache(cl rueidis.Client, ctx context.Context, cmd rueidis.Cacheable, ttl time.Duration) rueidis.RedisResult {
	st := time.Now()
	redisCommand := r.singleCommandPreProcess(ctx, &cmd)
	resp := cl.DoCache(ctx, cmd, ttl)
	return r.handleRedisResult(resp, redisCommand, st)
}

func (r *hooks) DoMultiCache(cl rueidis.Client, ctx context.Context, multi ...rueidis.CacheableTTL) []rueidis.RedisResult {
	st := time.Now()
	redisCommands := r.cachedPipelineCommandPreProcess(ctx, multi)
	resp := cl.DoMultiCache(ctx, multi...)
	return r.handleMultiRedisResult(resp, redisCommands, st)
}

func (r *hooks) Receive(cl rueidis.Client, ctx context.Context, subscribe rueidis.Completed, fn func(msg rueidis.PubSubMessage)) error {
	return cl.Receive(ctx, subscribe, fn)
}

func (r *hooks) DoStream(client rueidis.Client, ctx context.Context, cmd rueidis.Completed) rueidis.RedisResultStream {
	return client.DoStream(ctx, cmd)
}

func (r *hooks) DoMultiStream(client rueidis.Client, ctx context.Context, multi ...rueidis.Completed) rueidis.MultiRedisResultStream {
	return client.DoMultiStream(ctx, multi...)
}

func (r *hooks) singleCommandPreProcess(ctx context.Context, cmd RueidisCommand) string {
	r.pushRedisClientLogs(ctx, cmd)
	redisCommand := getRedisCommandName(cmd)
	metrics.RecordRedisCommandCount(redisCommand, r.conf.ClientName)
	return redisCommand
}

func (r *hooks) pipelineCommandPreProcess(ctx context.Context, cmds ...rueidis.Completed) []string {
	var redisCommands []string
	for _, cmd := range cmds {
		cmd := cmd
		redisCommands = append(redisCommands, r.singleCommandPreProcess(ctx, &cmd))
	}
	return redisCommands
}

func (r *hooks) cachedPipelineCommandPreProcess(ctx context.Context, cmds []rueidis.CacheableTTL) []string {
	var redisCommands []string
	for _, cmd := range cmds {
		cmd := cmd
		redisCommands = append(redisCommands, r.singleCommandPreProcess(ctx, &cmd.Cmd))
	}
	return redisCommands
}

func (r *hooks) handleRedisResult(resp rueidis.RedisResult, cmd string, startTime time.Time) rueidis.RedisResult {
	isCacheResult := types.BooleanEnum_FALSE
	if resp.IsCacheHit() {
		isCacheResult = types.BooleanEnum_TRUE
	}
	metrics.RecordRedisCommandLatency(cmd, r.conf.ClientName, startTime, isCacheResult)
	return resp
}

func (r *hooks) handleMultiRedisResult(resp []rueidis.RedisResult, cmd []string, startTime time.Time) []rueidis.RedisResult {
	for i := range resp {
		isCacheResult := types.BooleanEnum_FALSE
		if resp[i].IsCacheHit() {
			isCacheResult = types.BooleanEnum_TRUE
		}
		metrics.RecordRedisCommandLatency(cmd[i], r.conf.ClientName, startTime, isCacheResult)
	}
	return resp
}

func (r *hooks) pushRedisClientLogs(ctx context.Context, cmds ...RueidisCommand) {
	if !r.conf.EnableLogging {
		return
	}
	for _, cmd := range cmds {
		logger.Debug(ctx, "redis client command", zap.String("redisCommand", fmt.Sprint(cmd.Commands())))
	}
}

func getRedisCommandName(command RueidisCommand) string {
	cmds := command.Commands()
	if len(cmds) > 0 && lo.Contains([]string{
		// TODO(Shivam): Handle all redis commands
		"GET", "SET", "DEL", "ZPOPMIN", "ZADD", "WATCH", "FLUSHALL", "ZPOPMAX", "EXPIRE", "EXEC", "TTL", "MULTI", "RPUSH", "LRANGE", "EVAL", "EVALSHA",
	}, cmds[0]) {
		return cmds[0]
	}
	logger.ErrorNoCtx(fmt.Sprintf("unknown redis command found: %v", cmds))
	return ""
}
