package ip

import (
	"context"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"github.com/epifi/be-common/pkg/logger"
)

const (
	xForwardedForHeader = "x-forwarded-for"
)

// Interceptor handles IP whitelisting for gRPC requests.
type Interceptor struct {
	options *WhitelistOptions
}

// NewInterceptor creates a new IP whitelist interceptor with the given options.
func NewInterceptor(opts ...WhitelistOption) *Interceptor {
	options := &WhitelistOptions{} // Default options are zero values
	for _, opt := range opts {
		opt(options)
	}
	return &Interceptor{options: options}
}

// UnaryInterceptor returns a UnaryServerInterceptor that performs IP whitelisting.
func (i *Interceptor) UnaryInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		if i.options == nil || !i.options.EnableWhitelist {
			return handler(ctx, req) // Whitelisting not enabled or no config, proceed.
		}

		clientIP, found := getClientIPFromXForwardedFor(ctx, i.options.NumberOfHops, i.options.VPCCIDRPrefix)

		isWhitelisted := false
		if found {
			whitelistedIPsList := strings.Split(i.options.WhitelistedIPs, ",")
			for _, wlIP := range whitelistedIPsList {
				// Ensure wlIP is also trimmed in case of spaces in the config string like "ip1, ip2"
				if clientIP == strings.TrimSpace(wlIP) {
					isWhitelisted = true
					break
				}
			}
		}

		if isWhitelisted {
			logger.Debug(ctx, "Client IP is whitelisted. Allowing access.", zap.String("client_ip", clientIP))
			return handler(ctx, req)
		}

		logger.WarnWithCtx(ctx, "Client IP is not whitelisted. Forbidding access.", zap.String("client_ip", clientIP))
		return nil, status.Error(codes.PermissionDenied, "Access forbidden")
	}
}

// getClientIPFromXForwardedFor extracts the client IP address from the 'x-forwarded-for' header,
// considering the number of hops and VPC CIDR prefix.
func getClientIPFromXForwardedFor(ctx context.Context, numberOfHopsThatAddXForwardedFor int, vpcCidrIPPrefix string) (string, bool) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		logger.Error(ctx, "Failed to get metadata from incoming context for IP whitelist check")
		return "", false
	}

	headerVals := md.Get(xForwardedForHeader)
	if len(headerVals) == 0 || len(headerVals[0]) == 0 {
		logger.Debug(ctx, "Missing x-forwarded-for header for IP whitelist check")
		return "", false
	}

	ipListStr := headerVals[0]
	ipList := strings.Split(ipListStr, ",")

	var validIpList []string
	for _, ip := range ipList {
		ip = strings.TrimSpace(ip)
		if ip != "" {
			validIpList = append(validIpList, ip)
		}
	}
	ipList = validIpList

	if len(ipList) == 0 {
		logger.Debug(ctx, "Empty IP list after parsing x-forwarded-for header")
		return "", false
	}

	var rawClientIp string
	lenIpList := len(ipList)

	if lenIpList < numberOfHopsThatAddXForwardedFor || numberOfHopsThatAddXForwardedFor <= 0 {
		logger.Debug(ctx, "XFF IP list length issue or invalid hops; using rightmost IP from XFF list.")
		rawClientIp = ipList[lenIpList-1]
	} else {
		targetIndex := lenIpList - numberOfHopsThatAddXForwardedFor
		rawClientIp = ipList[targetIndex]

		if vpcCidrIPPrefix != "" && strings.HasPrefix(rawClientIp, vpcCidrIPPrefix) {
			if targetIndex > 0 {
				logger.Debug(ctx, "Determined IP is within VPC, attempting to step back one hop in XFF list.")
				rawClientIp = ipList[targetIndex-1]
			} else {
				logger.Debug(ctx, "Determined IP is within VPC, but it's the first IP in the list. Cannot step back further.")
			}
		}
	}

	clientIp := strings.TrimSpace(rawClientIp)
	if clientIp == "" {
		logger.Debug(ctx, "Extracted client IP is empty string after processing XFF header.")
		return "", false
	}
	logger.Debug(ctx, "Successfully extracted client IP for whitelist check.")
	return clientIp, true
}
