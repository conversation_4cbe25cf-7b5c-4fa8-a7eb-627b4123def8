// Package utils contains utility functions and tests for networth insights.
package networth

import (
	"context"
	"math"
	"testing"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	investment "github.com/epifi/gamma/api/analyser/investment"
	model "github.com/epifi/gamma/api/analyser/investment/model"
	mfAnalyserVariablePb "github.com/epifi/gamma/api/analyser/variables/mutualfund"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
)

// Use a higher tolerance for float comparison due to floating-point arithmetic
const floatTolerance = 0.1

// TestCalculateNavChangePercentage tests the CalculateNavChangePercentage function for various NAV and change scenarios.
func TestCalculateNavChangePercentage(t *testing.T) {
	ctx := context.Background()
	logger.Init(cfg.TestEnv)
	// Test cases for different NAV and daily change values
	tests := []struct {
		name string           // Description of the test case
		fund *mfPb.MutualFund // Input mutual fund
		want float64          // Expected output
	}{
		{
			name: "positive change",
			fund: &mfPb.MutualFund{
				Nav: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
					Nanos:        500000000, // 100.50
				},
				DailyNavChange: 2.5,
			},
			want: 2.55, // (2.5 * 100) / (100.50 - 2.5)
		},
		{
			name: "negative change",
			fund: &mfPb.MutualFund{
				Nav: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
					Nanos:        0,
				},
				DailyNavChange: -1.5,
			},
			want: -1.48, // (-1.5 * 100) / (100 - (-1.5))
		},
		{
			name: "zero change",
			fund: &mfPb.MutualFund{
				Nav: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
					Nanos:        0,
				},
				DailyNavChange: 0,
			},
			want: 0,
		},
		{
			name: "invalid NAV",
			fund: &mfPb.MutualFund{
				Nav:            nil,
				DailyNavChange: 1.5,
			},
			want: 0,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got := CalculateNavChangePercentage(ctx, tt.fund)
			if got != tt.want {
				t.Errorf("CalculateNavChangePercentage() \ngot : %v \nwant : %v", got, tt.want)
				return
			}
		})
	}
}

// TestGetAmountFromMoney tests the GetAmountFromMoney function for various money values.
func TestGetAmountFromMoney(t *testing.T) {
	ctx := context.Background()
	logger.Init(cfg.TestEnv)
	// Test cases for different money values
	tests := []struct {
		name    string         // Description of the test case
		nav     *moneyPb.Money // Input money
		want    float64        // Expected output
		wantErr bool           // Whether an error is expected
	}{
		{
			name: "valid positive amount",
			nav: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100,
				Nanos:        500000000, // 100.50
			},
			want:    100.50,
			wantErr: false,
		},
		{
			name: "zero amount",
			nav: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        0,
				Nanos:        0,
			},
			want:    0,
			wantErr: false,
		},
		{
			name:    "nil money",
			nav:     nil,
			want:    0,
			wantErr: true,
		},
		{
			name: "negative amount",
			nav: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        -50,
				Nanos:        -500000000, // -50.50
			},
			want:    -50.50,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := GetAmountFromMoney(ctx, tt.nav)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAmountFromMoney() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr == true {
				return
			}
			if got != tt.want {
				t.Errorf("GetAmountFromMoney() \ngot : %v \nwant : %v", got, tt.want)
				return
			}
		})
	}
}

// TestCalculateDailyChange tests the calculation of daily change for various scenarios.
func TestCalculateDailyChange(t *testing.T) {
	// Test cases for positive, negative, and zero daily NAV percentage changes
	tests := []struct {
		name    string
		details *DailyChangeDetails
		want    float64
	}{
		{
			name: "positive change",
			details: &DailyChangeDetails{
				DailyNavPercentageChange: 2.5,
				CurrentValue:             1000,
			},
			want: 24.39, // (1000 * 2.5) / (100 + 2.5)
		},
		{
			name: "negative change",
			details: &DailyChangeDetails{
				DailyNavPercentageChange: -1.5,
				CurrentValue:             1000,
			},
			want: -15.23, // (1000 * -1.5) / (100 + -1.5)
		},
		{
			name: "zero change",
			details: &DailyChangeDetails{
				DailyNavPercentageChange: 0,
				CurrentValue:             1000,
			},
			want: 0,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got := CalculateDailyChange(tt.details)
			if math.Abs(got-tt.want) > 0.01 {
				t.Errorf("CalculateDailyChange() \ngot : %v \nwant : %v", got, tt.want)
			}
		})
	}
}

// TestCalculateAggregatedMfValues tests aggregation of multiple mutual fund daily changes.
func TestCalculateAggregatedMfValues(t *testing.T) {
	// Test cases for multiple and empty daily change lists
	tests := []struct {
		name              string
		dailyChangeList   []*DailyChangeDetails
		wantCurrentValue  float64
		wantPreviousValue float64
		wantDailyChange   float64
	}{
		{
			name: "multiple schemes",
			dailyChangeList: []*DailyChangeDetails{
				{
					DailyNavPercentageChange: 2.5,
					CurrentValue:             1000,
				},
				{
					DailyNavPercentageChange: -1.5,
					CurrentValue:             2000,
				},
			},
			wantCurrentValue:  3000,
			wantPreviousValue: 3006.07,
			wantDailyChange:   -6.07,
		},
		{
			name:              "empty list",
			dailyChangeList:   []*DailyChangeDetails{},
			wantCurrentValue:  0,
			wantPreviousValue: 0,
			wantDailyChange:   0,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			gotCurrentValue, gotPreviousValue, gotDailyChange := CalculateAggregatedMfValues(tt.dailyChangeList)
			if math.Abs(gotCurrentValue-tt.wantCurrentValue) > floatTolerance {
				t.Errorf("CalculateAggregatedMfValues() currentValue = %v, want %v", gotCurrentValue, tt.wantCurrentValue)
			}
			if math.Abs(gotPreviousValue-tt.wantPreviousValue) > floatTolerance {
				t.Errorf("CalculateAggregatedMfValues() previousValue = %v, want %v", gotPreviousValue, tt.wantPreviousValue)
			}
			if math.Abs(gotDailyChange-tt.wantDailyChange) > floatTolerance {
				t.Errorf("CalculateAggregatedMfValues() dailyChange = %v, want %v", gotDailyChange, tt.wantDailyChange)
			}
		})
	}
}

// TestGetMfAggregatedValues tests the aggregation and percentage calculation for mutual fund analytics.
func TestGetMfAggregatedValues(t *testing.T) {
	ctx := context.Background()
	// Test cases for valid and empty scheme analytics
	tests := []struct {
		name                 string
		mfSchemeAnalytics    *mfAnalyserVariablePb.MfSchemeAnalytics
		wantCurrentValue     float64
		wantPreviousValue    float64
		wantDailyChange      float64
		wantPercentageChange float64
	}{
		{
			name: "valid scheme analytics",
			mfSchemeAnalytics: &mfAnalyserVariablePb.MfSchemeAnalytics{
				SchemeAnalytics: []*mfAnalyserVariablePb.SchemeAnalytics{
					{
						SchemeDetail: &mfPb.MutualFund{
							Nav: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100,
								Nanos:        0,
							},
							DailyNavChange: 2.5,
						},
						EnrichedAnalytics: &investment.EnrichedMfSchemeAnalytics{
							Analytics: &model.MfScheme{
								SchemeDetails: &model.MfSchemeDetails{
									CurrentValue: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        1000,
										Nanos:        0,
									},
									InvestedValue: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        1000,
										Nanos:        0,
									},
								},
							},
						},
					},
				},
			},
			wantCurrentValue:     1000,
			wantPreviousValue:    975.04,
			wantDailyChange:      24.96,
			wantPercentageChange: 2.56,
		},
		{
			name: "empty scheme analytics",
			mfSchemeAnalytics: &mfAnalyserVariablePb.MfSchemeAnalytics{
				SchemeAnalytics: []*mfAnalyserVariablePb.SchemeAnalytics{},
			},
			wantCurrentValue:     0,
			wantPreviousValue:    0,
			wantDailyChange:      0,
			wantPercentageChange: 0,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			gotCurrentValue, gotPreviousValue, gotDailyChange, gotPercentageChange := GetMfAggregatedValues(ctx, tt.mfSchemeAnalytics)
			if math.Abs(gotCurrentValue-tt.wantCurrentValue) > floatTolerance {
				t.Errorf("GetMfAggregatedValues() currentValue = %v, want %v", gotCurrentValue, tt.wantCurrentValue)
			}
			if math.Abs(gotPreviousValue-tt.wantPreviousValue) > floatTolerance {
				t.Errorf("GetMfAggregatedValues() previousValue = %v, want %v", gotPreviousValue, tt.wantPreviousValue)
			}
			if math.Abs(gotDailyChange-tt.wantDailyChange) > floatTolerance {
				t.Errorf("GetMfAggregatedValues() dailyChange = %v, want %v", gotDailyChange, tt.wantDailyChange)
			}
			if math.Abs(gotPercentageChange-tt.wantPercentageChange) > floatTolerance {
				t.Errorf("GetMfAggregatedValues() percentageChange = %v, want %v", gotPercentageChange, tt.wantPercentageChange)
			}
		})
	}
}

// TestGetSchemeAnalyticsBySortedDayChange tests sorting and filtering of scheme analytics by current value.
func TestGetSchemeAnalyticsBySortedDayChange(t *testing.T) {
	ctx := context.Background()
	// Test cases for valid and invalid scheme analytics
	tests := []struct {
		name           string
		schemeDetails  *mfAnalyserVariablePb.MfSchemeAnalytics
		wantListLength int
	}{
		{
			name: "valid schemes",
			schemeDetails: &mfAnalyserVariablePb.MfSchemeAnalytics{
				SchemeAnalytics: []*mfAnalyserVariablePb.SchemeAnalytics{
					{
						SchemeDetail: &mfPb.MutualFund{
							Nav: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100,
								Nanos:        0,
							},
							DailyNavChange: 2.5,
						},
						EnrichedAnalytics: &investment.EnrichedMfSchemeAnalytics{
							Analytics: &model.MfScheme{
								SchemeDetails: &model.MfSchemeDetails{
									CurrentValue: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        2000,
										Nanos:        0,
									},
									InvestedValue: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        2000,
										Nanos:        0,
									},
								},
							},
						},
					},
					{
						SchemeDetail: &mfPb.MutualFund{
							Nav: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100,
								Nanos:        0,
							},
							DailyNavChange: 1.5,
						},
						EnrichedAnalytics: &investment.EnrichedMfSchemeAnalytics{
							Analytics: &model.MfScheme{
								SchemeDetails: &model.MfSchemeDetails{
									CurrentValue: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        1000,
										Nanos:        0,
									},
									InvestedValue: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        1000,
										Nanos:        0,
									},
								},
							},
						},
					},
				},
			},
			wantListLength: 2,
		},
		{
			name: "invalid scheme",
			schemeDetails: &mfAnalyserVariablePb.MfSchemeAnalytics{
				SchemeAnalytics: []*mfAnalyserVariablePb.SchemeAnalytics{
					{
						SchemeDetail: &mfPb.MutualFund{
							Nav: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100,
								Nanos:        0,
							},
							DailyNavChange: 2.5,
						},
						EnrichedAnalytics: &investment.EnrichedMfSchemeAnalytics{
							Analytics: &model.MfScheme{
								SchemeDetails: &model.MfSchemeDetails{
									CurrentValue: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        0,
										Nanos:        0,
									},
									InvestedValue: &moneyPb.Money{
										CurrencyCode: "INR",
										Units:        0,
										Nanos:        0,
									},
								},
							},
						},
					},
				},
			},
			wantListLength: 0,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got := GetSchemeAnalyticsBySortedDayChange(ctx, tt.schemeDetails)
			if len(got) != tt.wantListLength {
				t.Errorf("GetSchemeAnalyticsBySortedDayChange() got length = %v, want %v", len(got), tt.wantListLength)
			}
			if len(got) > 1 {
				// Verify sorting by current value (descending)
				for i := 0; i < len(got)-1; i++ {
					if got[i].CurrentValue < got[i+1].CurrentValue {
						t.Errorf("GetSchemeAnalyticsBySortedDayChange() not sorted correctly at index %d", i)
					}
				}
			}
		})
	}
}

// TestValidateCurrentScheme tests the validation logic for scheme analytics.
func TestValidateCurrentScheme(t *testing.T) {
	// Test cases for valid, zero current value, and zero invested value
	tests := []struct {
		name   string
		scheme *mfAnalyserVariablePb.SchemeAnalytics
		want   bool
	}{
		{
			name: "valid scheme",
			scheme: &mfAnalyserVariablePb.SchemeAnalytics{
				EnrichedAnalytics: &investment.EnrichedMfSchemeAnalytics{
					Analytics: &model.MfScheme{
						SchemeDetails: &model.MfSchemeDetails{
							CurrentValue: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        1000,
								Nanos:        0,
							},
							InvestedValue: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        1000,
								Nanos:        0,
							},
						},
					},
				},
			},
			want: true,
		},
		{
			name: "zero current value",
			scheme: &mfAnalyserVariablePb.SchemeAnalytics{
				EnrichedAnalytics: &investment.EnrichedMfSchemeAnalytics{
					Analytics: &model.MfScheme{
						SchemeDetails: &model.MfSchemeDetails{
							CurrentValue: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        0,
								Nanos:        0,
							},
							InvestedValue: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        1000,
								Nanos:        0,
							},
						},
					},
				},
			},
			want: false,
		},
		{
			name: "zero invested value",
			scheme: &mfAnalyserVariablePb.SchemeAnalytics{
				EnrichedAnalytics: &investment.EnrichedMfSchemeAnalytics{
					Analytics: &model.MfScheme{
						SchemeDetails: &model.MfSchemeDetails{
							CurrentValue: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        1000,
								Nanos:        0,
							},
							InvestedValue: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        0,
								Nanos:        0,
							},
						},
					},
				},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got := validateCurrentScheme(tt.scheme)
			if got != tt.want {
				t.Errorf("validateCurrentScheme() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestWeeklyChangeMapCreation tests the creation of a map from a slice of AssetValueChange.
func TestWeeklyChangeMapCreation(t *testing.T) {
	// Test cases for valid and empty details
	tests := []struct {
		name    string
		details []*networthPb.AssetValueChange
		want    map[string]*networthPb.AssetValueChange
	}{
		{
			name: "valid changes",
			details: []*networthPb.AssetValueChange{
				{
					AssetId: "asset1",
					Change:  100,
					InitialDateValue: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				{
					AssetId: "asset2",
					Change:  200,
					InitialDateValue: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2000,
						Nanos:        0,
					},
				},
			},
			want: map[string]*networthPb.AssetValueChange{
				"asset1": {
					AssetId: "asset1",
					Change:  100,
					InitialDateValue: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				"asset2": {
					AssetId: "asset2",
					Change:  200,
					InitialDateValue: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2000,
						Nanos:        0,
					},
				},
			},
		},
		{
			name:    "empty details",
			details: []*networthPb.AssetValueChange{},
			want:    map[string]*networthPb.AssetValueChange{},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got := WeeklyChangeMapCreation(tt.details)
			if len(got) != len(tt.want) {
				t.Errorf("WeeklyChangeMapCreation() got length = %v, want %v", len(got), len(tt.want))
				return
			}
			for k, v := range tt.want {
				if got[k] == nil {
					t.Errorf("WeeklyChangeMapCreation() missing key %v", k)
					continue
				}
				if got[k].AssetId != v.AssetId {
					t.Errorf("WeeklyChangeMapCreation() AssetId = %v, want %v", got[k].AssetId, v.AssetId)
				}
				if got[k].Change != v.Change {
					t.Errorf("WeeklyChangeMapCreation() Change = %v, want %v", got[k].Change, v.Change)
				}
			}
		})
	}
}

// TestCalculateWeeklyPercentageChange tests the calculation of weekly percentage change for asset value changes.
func TestCalculateWeeklyPercentageChange(t *testing.T) {
	// Test cases for positive, negative, and zero changes
	tests := []struct {
		name   string
		detail *networthPb.AssetValueChange
		want   float64
	}{
		{
			name: "positive change",
			detail: &networthPb.AssetValueChange{
				Change: 100,
				InitialDateValue: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
			},
			want: 10, // (100 * 100) / 1000
		},
		{
			name: "negative change",
			detail: &networthPb.AssetValueChange{
				Change: -50,
				InitialDateValue: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
			},
			want: -5, // (-50 * 100) / 1000
		},
		{
			name: "zero change",
			detail: &networthPb.AssetValueChange{
				Change: 0,
				InitialDateValue: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
			},
			want: 0,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got := CalculateWeeklyPercentageChange(tt.detail)
			if got != tt.want {
				t.Errorf("CalculateWeeklyPercentageChange() = %v, want %v", got, tt.want)
			}
		})
	}
}
