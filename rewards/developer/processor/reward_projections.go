package processor

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/rewards/developer"
	developer2 "github.com/epifi/gamma/pkg/developer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/rewards/projector/dao"
	"github.com/epifi/gamma/rewards/projector/dao/model"
)

type RewardProjectionsProcessor struct {
	rewardProjectionsDao dao.RewardsProjectionDao
}

func NewRewardProjectionsProcessor(rewardProjectionsDao dao.RewardsProjectionDao) *RewardProjectionsProcessor {
	return &RewardProjectionsProcessor{rewardProjectionsDao: rewardProjectionsDao}
}

// nolint:dupl
func (r *RewardProjectionsProcessor) FetchParamList(ctx context.Context, entity developer.RewardsEntity) ([]*db_state.ParameterMeta, error) {

	paramList := []*db_state.ParameterMeta{
		{
			Name:            projectionId,
			Label:           "Reward projection Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            actorId,
			Label:           "Actor Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            rewardOfferId,
			Label:           "Reward Offer Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            actionRefId,
			Label:           "Action Ref Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            rewardId,
			Label:           "Generated Reward Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            fromDate,
			Label:           "Action time (from)",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            tillDate,
			Label:           "Action time (upto)",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            fetchDeletedRecords,
			Label:           "Do we want to fetch deleted entries?",
			Type:            db_state.ParameterDataType_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options: []string{
				boolStringYes,
				boolStringNo,
			},
		},
	}
	return paramList, nil
}

func (r *RewardProjectionsProcessor) FetchData(ctx context.Context, entity developer.RewardsEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}

	queryFilter := &model.AndProjectionFilter{}
	var fetchDeletedProjections bool
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case projectionId:
			queryFilter.Ids = []string{filter.GetStringValue()}
		case actorId:
			queryFilter.ActorId = filter.GetStringValue()
		case rewardOfferId:
			queryFilter.OfferIds = []string{filter.GetStringValue()}
		case actionRefId:
			queryFilter.RefIds = []string{filter.GetStringValue()}
		case rewardId:
			queryFilter.RefIds = []string{filter.GetStringValue()}
		case fromDate:
			queryFilter.FromTime = filter.GetTimestamp().AsTime()
		case tillDate:
			queryFilter.UptoTime = filter.GetTimestamp().AsTime()
		case fetchDeletedRecords:
			fetchDeletedProjections = filter.GetDropdownValue() == boolStringYes

		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	projections, _, err := r.rewardProjectionsDao.FetchPaginatedProjectionsByFilters(ctx, &model.QueryProjectionsFilter{AndFilter: queryFilter, FetchDeletedProjections: fetchDeletedProjections}, nil, 50, nil)
	if err != nil {
		logger.Error(ctx, "error fetching projections", zap.Error(err))
		return "", fmt.Errorf("error fetching projections, err: %w", err)
	}

	marshalledRes, err := developer2.JsonMarshalWithHumanReadableTimestamp(projections)
	if err != nil {
		logger.Error(ctx, "error marshalling projections proto to json", zap.Any("projections", projections))
		return "", errors.Wrap(err, "error marshalling projections proto to json")
	}

	return string(marshalledRes), nil
}
