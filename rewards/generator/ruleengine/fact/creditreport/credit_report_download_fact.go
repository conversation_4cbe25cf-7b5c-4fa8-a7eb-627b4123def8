package creditreport

import (
	"github.com/Knetic/govaluate"

	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/creditreportv2/notification"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
)

type CreditReportDownloadFact struct {
	*common.CommonFact
	creditReportDownloadEvent *notification.CreditReportDownloadEvent
}

func NewCreditReportDownloadFact(commonFact *common.CommonFact, downloadEvent *notification.CreditReportDownloadEvent) *CreditReportDownloadFact {
	return &CreditReportDownloadFact{
		CommonFact:                commonFact,
		creditReportDownloadEvent: downloadEvent,
	}
}

// Ensure CreditReportDownloadFact implements common.IFact interface
var _ common.IFact = &CreditReportDownloadFact{}

func (i *CreditReportDownloadFact) GetExpressionFunctionMap() map[string]govaluate.ExpressionFunction {
	return i.CommonFact.GetExpressionFunctionMap()
}

func (i *CreditReportDownloadFact) GetExpressionParametersMap() map[string]interface{} {
	return map[string]interface{}{
		"REPORT_FETCHED_WITH_NEW_CONSENT": i.creditReportDownloadEvent.GetFetchType() ==
			creditreportv2.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_NEW_CONSENT,

		"REPORT_FETCHED_THROUGH_ANALYSER": i.creditReportDownloadEvent.GetProvenance() ==
			creditreportv2.Provenance_PROVENANCE_ANALYSER,
	}
}
