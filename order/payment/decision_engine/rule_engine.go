package decision_engine

import (
	"context"
	"fmt"

	"github.com/Knetic/govaluate"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/logger"
)

// Struct to hold our condition expression and the error
type condition struct {
	expr        string
	onFailError error
}

// Struct for our rules
// Has all the information required to evaluate a rule
// The 'when' part of our rule is in conditions slide
// The 'then' part is in paymentProtocol
// Salience is the priority of this rule with respect to other rules
type rule struct {
	name                    string
	salience                int
	conditions              []*condition
	expressionParameterMap  map[string]interface{}
	expressionFunctionMap   map[string]govaluate.ExpressionFunction
	paymentProtocol         paymentPb.PaymentProtocol
	fetchPaymentInstruments func() (*piPb.PaymentInstrument, *piPb.PaymentInstrument, error)
}

func (r *rule) getPaymentInstruments() (*piPb.PaymentInstrument, *piPb.PaymentInstrument, error) {
	piFrom, piTo, err := r.fetchPaymentInstruments()
	return piFrom, piTo, err
}
func (r *rule) getPaymentProtocol() paymentPb.PaymentProtocol {
	return r.paymentProtocol
}

// Helper method on a rule to evaluate a single condition
// Will return false, nil in case the conditions fails.
// Will returns false,error in case there is processing error while evaluating a condition
func (r *rule) evaluateCondition(condition *condition) (bool, error) {
	var (
		res bool
	)
	evalExpr, err := govaluate.NewEvaluableExpressionWithFunctions(condition.expr, r.expressionFunctionMap)
	if err != nil {
		return false, fmt.Errorf("error while building expr, err: %w", err)
	}
	result, err := evalExpr.Evaluate(r.expressionParameterMap)
	if err != nil {
		return false, fmt.Errorf("error while evaluating expr, err: %w", err)
	}
	res, _ = result.(bool)
	return res, nil
}

// Helper method on a rule to evaluate all conditions
// Returns paymentProtocol if all the conditions satisfy
// Returns err if any one of them fails
// TODO(MahidharBandaru): Add priority for conditions to return the more appropriate error.
func (r *rule) evaluate(ctx context.Context) error {
	var failedConditionError error
	for _, condition := range r.conditions {
		res, err := r.evaluateCondition(condition)
		// return the error from first condition that fails
		if err != nil { // case where condition evaluation failed
			logger.Error(ctx, "error evaluating condition for rule", zap.String(logger.RULE_NAME, r.name), zap.Error(err))
			failedConditionError = errors.Unwrap(err)
			break
		}
		if !res { // case where condition failed
			logger.Debug(ctx, "condition failed for rule", zap.String(logger.RULE_NAME, r.name), zap.String(logger.CONDITION_DATA, condition.expr))
			failedConditionError = condition.onFailError
			break
		}
	}
	if failedConditionError != nil {
		return failedConditionError
	}
	return nil
}

// Our rule engine
// Holds a slice of rules
type ruleEngine struct {
	rules []*rule
}
type resWithErr struct {
	salience int
	res      *rule
	err      error
}

// Helper method on a rule to evaluate all rules in the order of salience
// Returns paymentProtocol if any of the rules satisfy
// Returns errors from each of the rules if all of them fail
func (d *ruleEngine) evaluateConcurrent(ctx context.Context) (*rule, []error) {
	var errors []error
	c := make(chan *resWithErr, len(d.rules))
	defer close(c)

	for _, r := range d.rules {
		rT := r
		goroutine.RunWithDefaultTimeout(ctx, func(goCtx context.Context) {
			err := rT.evaluate(goCtx)
			c <- &resWithErr{
				salience: rT.salience,
				res:      rT,
				err:      err,
			}
		})
	}
	var passedRule *rule
	var (
		maxSaliencePassedRule int
	)
	for i := 0; i < len(d.rules); i++ {
		val := <-c
		if val.err == nil {
			if passedRule == nil {
				maxSaliencePassedRule = val.salience
				passedRule = val.res
			} else {
				if maxSaliencePassedRule < val.salience {
					maxSaliencePassedRule = val.salience
					passedRule = val.res
				}
			}
		} else {
			errors = append(errors, val.err)
		}
	}
	if passedRule != nil {
		// logging the rules which failed in case we did find a rule that passed.
		// this will help with looking into cases as to why a particular protocol was selected over the other.
		// This will add roughly 1.4L logs per day.
		if len(errors) != 0 {
			logger.WarnWithCtx(ctx, "DE some rules failed", zap.Errors(logger.ERROR_LIST, errors))
		}

		return passedRule, nil
	}
	return nil, errors

}
