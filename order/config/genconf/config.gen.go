// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	money "google.golang.org/genproto/googleapis/type/money"

	pkgweb "github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	common "github.com/epifi/be-common/api/typesv2/common"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	v "github.com/epifi/be-common/pkg/cfg/v2"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	genconfig2 "github.com/epifi/be-common/quest/sdk/config/genconf"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/order/config"
	genconfig "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	pay "github.com/epifi/gamma/pkg/pay"
	sftp "github.com/epifi/gamma/pkg/sftp/aws/sftp"
)

type Config struct {
	callbacks                     *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                      questsdk.Client
	questFieldPath                string
	_AaEnrichedTxnUpdateBatchSize int64
	// Maximum Pause time for Remitter Info Workflow in minutes before the workflow starts activity executions
	_MaximumPauseTimeForRemitterInfoWorkflow int64
	// whether to show account balance in the notification or not
	_HideAccountBalanceInNotification              uint32
	_IsPushingPaymentsMetricsToHealthEngineEnabled uint32
	_IsEmailNotificationEnable                     uint32
	// flag to indicate whether to enable resource provider based db instance provision or whether to
	// go with a static db instance
	// Assumption : Once the flag is turned on, it will not be turned off again. Since it will go through a
	// CUG, any occurring issues will be fixed within the CUG itself. Also, all the systems using this
	// have been made backward compatible. So the older prod entities will not see any issues. Possible consequences
	// can be that the data got written with entity segregation but fetched from the default
	// epifi db but since that scale will be low at the time of CUG, we would be able to fix it before going external
	_EnableEntitySegregation uint32
	// RecentOrderEligibleDuration is the maximum duration difference allowed
	// between (updated_at - created_at, credited_at, debited_at)
	_RecentOrderEligibleDuration int64
	// TimeoutForGetOperationalStatus is the timeout to be used when calling GetOperationalStatus API
	// This allows us to handle scenarios where the API is taking longer than expected to respond or not responding at all without affecting the overall flow
	_TimeoutForGetOperationalStatus int64
	// PinotQueryTimeout is the timeout to be used when querying Pinot
	// This allows us to handle scenarios where the Pinot query is taking longer than expected to respond
	_PinotQueryTimeout int64
	// map between sms type and the sms option version to be used
	_SMSTypeToOptionVersionMap                          *syncmap.Map[string, *config.SMSOptionVersion]
	_PaymentOrchestrationSubscriber                     *gencfg.SqsSubscriber
	_IntraBankEnquirySubscriber                         *gencfg.SqsSubscriber
	_UPIEnquirySubscriber                               *gencfg.SqsSubscriber
	_IMPSEnquirySubscriber                              *gencfg.SqsSubscriber
	_NEFTEnquirySubscriber                              *gencfg.SqsSubscriber
	_RTGSEnquirySubscriber                              *gencfg.SqsSubscriber
	_OrderOrchestrationSubscriber                       *gencfg.SqsSubscriber
	_PaymentCallbackSubscriber                          *gencfg.SqsSubscriber
	_InboundTxnSubscriber                               *gencfg.SqsSubscriber
	_InboundUpiTxnSubscriber                            *gencfg.SqsSubscriber
	_OrderUpdateSubscriber                              *gencfg.SqsSubscriber
	_OrderCollectNotificationSubscriber                 *gencfg.SqsSubscriber
	_OrderNotificationFallbackSubscriber                *gencfg.SqsSubscriber
	_OrderWorkflowProcessingSubscriber                  *gencfg.SqsSubscriber
	_DisputeEventProcessingSubscriber                   *gencfg.SqsSubscriber
	_AATxnSubscriber                                    *gencfg.SqsSubscriber
	_PaymentProtocolDecisionParams                      *PaymentProtocolDecisionParams
	_RuleEngineParams                                   *RuleEngineParams
	_OrderReceipt                                       *OrderReceipt
	_FundTransferWorkflow                               *FundTransferWorkflow
	_CollectShortCircuitWorkflow                        *CollectShortCircuitWorkflow
	_DepositCreationWorkflow                            *DepositCreationWorkflow
	_UrnTransferWorkflow                                *UrnTransferWorkflow
	_PreCloseDepositWorkflow                            *PreCloseDepositWorkflow
	_P2PCollectWorkflow                                 *P2PCollectWorkflow
	_B2CFundTransferWorkflow                            *B2CFundTransferWorkflow
	_RewardsCreateSdWorkflow                            *RewardsCreateSdWorkflow
	_RewardsAddFundsSdWorkflow                          *RewardsAddFundsSdWorkflow
	_AddFundsSdWorkflow                                 *AddFundsSdWorkflow
	_AddFundsWorkflow                                   *AddFundsWorkflow
	_AddFundsCollectWorkflow                            *AddFundsCollectWorkflow
	_RecurringPaymentCreationWorkflow                   *RecurringPaymentCreationWorkflow
	_RecurringPaymentExecutionWorkflow                  *RecurringPaymentExecutionWorkflow
	_RecurringPaymentModifyWorkflow                     *RecurringPaymentModifyWorkflow
	_RecurringPaymentRevokeWorkflow                     *RecurringPaymentRevokeWorkflow
	_P2PInvestmentWorkflow                              *P2PInvestmentWorkflow
	_P2PWithdrawalWorkflow                              *P2PWithdrawalWorkflow
	_MutualFundsInvestmentPostPaid                      *MutualFundsInvestmentPostPaid
	_MutualFundsRedemption                              *MutualFundsRedemption
	_PaymentNotificationParams                          *PaymentNotificationParams
	_PaymentSMSParams                                   *PaymentSMSParams
	_IconUrls                                           *IconUrls
	_Flags                                              *Flags
	_CustomRuleDEParams                                 *CustomRuleDEParams
	_URNPaymentLimits                                   *URNPaymentLimits
	_SavingsLedgerReconSubscriber                       *gencfg.SqsSubscriber
	_AddFundsAlertParams                                *AddFundsAlertParams
	_AddFundsAlertMailingParams                         *AddFundsAlertMailingParams
	_ExecuteRecurringPaymentWithAuthWorkflow            *ExecuteRecurringPaymentWithAuthWorkflow
	_ExecuteRecurringPaymentWithNoAuthWorkflow          *ExecuteRecurringPaymentWithNoAuthWorkflow
	_RecurringPaymentPauseUnpauseWorkflow               *RecurringPaymentPauseUnpauseWorkflow
	_PurgeAaDataSubscriber                              *gencfg.SqsSubscriber
	_AaTxnPurgeSubscriber                               *gencfg.SqsSubscriber
	_CSIS                                               *CSIS
	_BankDownTime                                       *BankDownTime
	_ConnectedAccountUserGroupParams                    *ConnectedAccountUserGroupParams
	_AaDataPurgeOrchestrationSubscriber                 *gencfg.SqsSubscriber
	_AaParams                                           *AaParams
	_NameCheckParamsForAddFunds                         *NameCheckParamsForAddFunds
	_DeclineCardTransactionsProcessingSubscriber        *gencfg.SqsSubscriber
	_SavingsLedgerReconCacheConfig                      *SavingsLedgerReconCacheConfig
	_OrderCacheConfig                                   *OrderCacheConfig
	_TransactionCacheConfig                             *TransactionCacheConfig
	_FeatureFlags                                       *FeatureFlags
	_InboundNotificationParams                          *InboundNotificationParams
	_ErrorRespCodesForPermanentFailure                  *ErrorRespCodesForPermanentFailure
	_FeatureReleaseConfig                               *genconfig.FeatureReleaseConfig
	_DataReplicationParams                              *gencfg.DataReplicationParams
	_ReconRestrictedWindow                              *RestrictedWindow
	_TxnNotificationSubscriber                          *gencfg.SqsSubscriber
	_DeemedTransactionUpiEnquirySubscriber              *gencfg.SqsSubscriber
	_AAFirstDataPullTxnSubscriber                       *gencfg.SqsSubscriber
	_QuestSdk                                           *genconfig2.Config
	_RewardsInfo                                        *RewardsInfo
	_Application                                        *config.Application
	_Server                                             *config.Server
	_Logging                                            *cfg.Logging
	_EpifiDb                                            *cfg.DB
	_EpifiWealthDB                                      *cfg.DB
	_ConnectedAccountDB                                 *cfg.DB
	_RedisOptions                                       *cfg.RedisOptions
	_OrderOrchestrationPublisher                        *cfg.SqsPublisher
	_AATxnPublisher                                     *cfg.SnsPublisher
	_OrderUpdateEventPublisher                          *cfg.SnsPublisher
	_OrderMerchantMergeEventPublisher                   *cfg.SnsPublisher
	_TxnDetailedStatusUpdateSnsPublisher                *cfg.SnsPublisher
	_WorkflowProcessingPublisher                        *cfg.SqsPublisher
	_OrderNotificationPublisher                         *cfg.SqsPublisher
	_OrderCollectNotificationPublisher                  *cfg.SqsPublisher
	_OrderNotificationFallbackPublisher                 *cfg.SqsPublisher
	_OrderSearchPublisher                               *cfg.SqsPublisher
	_EventsCompletedTnCPublisher                        *cfg.SqsPublisher
	_ProcrastinatorWorkflowPublisher                    *cfg.SqsPublisher
	_AWS                                                *cfg.AWS
	_PayFundTransferStatusCodeJson                      string
	_PayUpiStatusCodeJson                               string
	_EnachTransactionStatusCodeJson                     string
	_ImpsIfscCodesCsv                                   string
	_RtgsBlockListIfscCodesCsv                          string
	_CollectVelocityLimit                               int64
	_CollectVelocityWindow                              *cfg.TimeDuration
	_CoolOffWindow                                      *cfg.TimeDuration
	_CoolOffAmountLimit                                 *money.Money
	_RudderStack                                        *config.RudderBroker
	_Secrets                                            *cfg.Secrets
	_PersonMCCCode                                      string
	_SavingsLedgerReconPublisher                        *cfg.SqsPublisher
	_OrderEventAmountCategories                         *[]config.OrderAmountCategory
	_AaAccountPiPurgePublisher                          *cfg.SqsPublisher
	_PaymentOrchestrationPublisher                      *cfg.SqsPublisher
	_IntraBankEnquiryPublisher                          *cfg.SqsPublisher
	_UPIEnquiryPublisher                                *cfg.SqsPublisher
	_IMPSEnquiryPublisher                               *cfg.SqsPublisher
	_NEFTEnquiryPublisher                               *cfg.SqsPublisher
	_RTGSEnquiryPublisher                               *cfg.SqsPublisher
	_ActorPiRelationPurgePublisher                      *cfg.SqsPublisher
	_SignalWorkflowPublisher                            *cfg.SqsPublisher
	_Events                                             *config.EventsConfig
	_PartnerBankEODDelayMap                             map[string]time.Duration
	_DynamicVPAParamsMap                                map[string]*config.DynamicVPAParams
	_DynamicVPAV1ParamsMap                              map[string]*config.DynamicVPAParams
	_CampaignParams                                     map[string]*config.CampaignParams
	_PaymentEnquiryParams                               *pay.PaymentEnquiryParams
	_DeclineDataAwsSftpBucket                           *sftp.AwsSftpBucketConfig
	_AaDataPurgeOrchestrationPublisher                  *cfg.SqsPublisher
	_CardTxnStatusCodeJson                              string
	_DefaultExpiryDuration                              int64
	_ReconParams                                        *config.ReconParams
	_Tracing                                            *cfg.Tracing
	_Profiling                                          *cfg.Profiling
	_UpiUtrCollisionRetryCount                          int32
	_RemitterInfoSyncDelayThreshold                     time.Duration
	_DelayRangeForBalanceV1                             *config.DelayRangeForBalanceV1
	_OrderVpaVerificationPublisher                      *cfg.SqsPublisher
	_ReVerifyAddressDelay                               time.Duration
	_VerifyVpaDeadlineForInboundNotification            time.Duration
	_B2CInitialEnquiryDelay                             time.Duration
	_TxnNotificationPublisher                           *cfg.SqsPublisher
	_MccsWithOnlyAllowedIntentPayments                  map[string][]string
	_DeemedTransactionUPIEnquiryPublisher               *cfg.SqsPublisher
	_B2CPaymentParams                                   *config.B2CPaymentParams
	_InPaymentOrderUpdatePublisher                      *cfg.SqsPublisher
	_NonTpapPspHandles                                  []string
	_AmountThresholdForNotification                     *money.Money
	_InternationalQrInfoPrefix                          string
	_DelimiterForRedisKey                               string
	_ForexRefundInfo                                    *config.DcForexRefundInfo
	_IsStatementDataComparison                          bool
	_QuestRedisOptions                                  *cfg.RedisOptions
	_IncidentManagerParams                              *pay.IncidentManagerParams
	_SecureLoggingV2                                    *v.SecureLogging
	_GrpcRatelimiterParams                              *cfg.GrpcRateLimiterParams
	_CommsConfig                                        *config.CommsConfig
	_ReportFraudConfig                                  *config.ReportFraudConfig
	_TransactionAttributesBasedStoryDetailsCombinations []*config.TransactionAttributesBasedStoryDetails
	_RupayCCRestrictedMCCs                              []string
}

func (obj *Config) AaEnrichedTxnUpdateBatchSize() int {
	return int(atomic.LoadInt64(&obj._AaEnrichedTxnUpdateBatchSize))
}

// Maximum Pause time for Remitter Info Workflow in minutes before the workflow starts activity executions
func (obj *Config) MaximumPauseTimeForRemitterInfoWorkflow() int {
	return int(atomic.LoadInt64(&obj._MaximumPauseTimeForRemitterInfoWorkflow))
}

// whether to show account balance in the notification or not
func (obj *Config) HideAccountBalanceInNotification() bool {
	if atomic.LoadUint32(&obj._HideAccountBalanceInNotification) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) IsPushingPaymentsMetricsToHealthEngineEnabled() bool {
	if atomic.LoadUint32(&obj._IsPushingPaymentsMetricsToHealthEngineEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) isEmailNotificationEnable() bool {
	if atomic.LoadUint32(&obj._IsEmailNotificationEnable) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) IsEmailNotificationEnable(ctx context.Context) bool {
	defVal := obj.isEmailNotificationEnable()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IsEmailNotificationEnable"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

// flag to indicate whether to enable resource provider based db instance provision or whether to
// go with a static db instance
// Assumption : Once the flag is turned on, it will not be turned off again. Since it will go through a
// CUG, any occurring issues will be fixed within the CUG itself. Also, all the systems using this
// have been made backward compatible. So the older prod entities will not see any issues. Possible consequences
// can be that the data got written with entity segregation but fetched from the default
// epifi db but since that scale will be low at the time of CUG, we would be able to fix it before going external
func (obj *Config) EnableEntitySegregation() bool {
	if atomic.LoadUint32(&obj._EnableEntitySegregation) == 0 {
		return false
	} else {
		return true
	}
}

// RecentOrderEligibleDuration is the maximum duration difference allowed
// between (updated_at - created_at, credited_at, debited_at)
func (obj *Config) RecentOrderEligibleDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._RecentOrderEligibleDuration))
}

// TimeoutForGetOperationalStatus is the timeout to be used when calling GetOperationalStatus API
// This allows us to handle scenarios where the API is taking longer than expected to respond or not responding at all without affecting the overall flow
func (obj *Config) TimeoutForGetOperationalStatus() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._TimeoutForGetOperationalStatus))
}

// PinotQueryTimeout is the timeout to be used when querying Pinot
// This allows us to handle scenarios where the Pinot query is taking longer than expected to respond
func (obj *Config) PinotQueryTimeout() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._PinotQueryTimeout))
}

// map between sms type and the sms option version to be used
func (obj *Config) SMSTypeToOptionVersionMap() *syncmap.Map[string, *config.SMSOptionVersion] {
	return obj._SMSTypeToOptionVersionMap
}
func (obj *Config) PaymentOrchestrationSubscriber() *gencfg.SqsSubscriber {
	return obj._PaymentOrchestrationSubscriber
}
func (obj *Config) IntraBankEnquirySubscriber() *gencfg.SqsSubscriber {
	return obj._IntraBankEnquirySubscriber
}
func (obj *Config) UPIEnquirySubscriber() *gencfg.SqsSubscriber {
	return obj._UPIEnquirySubscriber
}
func (obj *Config) IMPSEnquirySubscriber() *gencfg.SqsSubscriber {
	return obj._IMPSEnquirySubscriber
}
func (obj *Config) NEFTEnquirySubscriber() *gencfg.SqsSubscriber {
	return obj._NEFTEnquirySubscriber
}
func (obj *Config) RTGSEnquirySubscriber() *gencfg.SqsSubscriber {
	return obj._RTGSEnquirySubscriber
}
func (obj *Config) OrderOrchestrationSubscriber() *gencfg.SqsSubscriber {
	return obj._OrderOrchestrationSubscriber
}
func (obj *Config) PaymentCallbackSubscriber() *gencfg.SqsSubscriber {
	return obj._PaymentCallbackSubscriber
}
func (obj *Config) InboundTxnSubscriber() *gencfg.SqsSubscriber {
	return obj._InboundTxnSubscriber
}
func (obj *Config) InboundUpiTxnSubscriber() *gencfg.SqsSubscriber {
	return obj._InboundUpiTxnSubscriber
}
func (obj *Config) OrderUpdateSubscriber() *gencfg.SqsSubscriber {
	return obj._OrderUpdateSubscriber
}
func (obj *Config) OrderCollectNotificationSubscriber() *gencfg.SqsSubscriber {
	return obj._OrderCollectNotificationSubscriber
}
func (obj *Config) OrderNotificationFallbackSubscriber() *gencfg.SqsSubscriber {
	return obj._OrderNotificationFallbackSubscriber
}
func (obj *Config) OrderWorkflowProcessingSubscriber() *gencfg.SqsSubscriber {
	return obj._OrderWorkflowProcessingSubscriber
}
func (obj *Config) DisputeEventProcessingSubscriber() *gencfg.SqsSubscriber {
	return obj._DisputeEventProcessingSubscriber
}
func (obj *Config) AATxnSubscriber() *gencfg.SqsSubscriber {
	return obj._AATxnSubscriber
}
func (obj *Config) PaymentProtocolDecisionParams() *PaymentProtocolDecisionParams {
	return obj._PaymentProtocolDecisionParams
}
func (obj *Config) RuleEngineParams() *RuleEngineParams {
	return obj._RuleEngineParams
}
func (obj *Config) OrderReceipt() *OrderReceipt {
	return obj._OrderReceipt
}
func (obj *Config) FundTransferWorkflow() *FundTransferWorkflow {
	return obj._FundTransferWorkflow
}
func (obj *Config) CollectShortCircuitWorkflow() *CollectShortCircuitWorkflow {
	return obj._CollectShortCircuitWorkflow
}
func (obj *Config) DepositCreationWorkflow() *DepositCreationWorkflow {
	return obj._DepositCreationWorkflow
}
func (obj *Config) UrnTransferWorkflow() *UrnTransferWorkflow {
	return obj._UrnTransferWorkflow
}
func (obj *Config) PreCloseDepositWorkflow() *PreCloseDepositWorkflow {
	return obj._PreCloseDepositWorkflow
}
func (obj *Config) P2PCollectWorkflow() *P2PCollectWorkflow {
	return obj._P2PCollectWorkflow
}
func (obj *Config) B2CFundTransferWorkflow() *B2CFundTransferWorkflow {
	return obj._B2CFundTransferWorkflow
}
func (obj *Config) RewardsCreateSdWorkflow() *RewardsCreateSdWorkflow {
	return obj._RewardsCreateSdWorkflow
}
func (obj *Config) RewardsAddFundsSdWorkflow() *RewardsAddFundsSdWorkflow {
	return obj._RewardsAddFundsSdWorkflow
}
func (obj *Config) AddFundsSdWorkflow() *AddFundsSdWorkflow {
	return obj._AddFundsSdWorkflow
}
func (obj *Config) AddFundsWorkflow() *AddFundsWorkflow {
	return obj._AddFundsWorkflow
}
func (obj *Config) AddFundsCollectWorkflow() *AddFundsCollectWorkflow {
	return obj._AddFundsCollectWorkflow
}
func (obj *Config) RecurringPaymentCreationWorkflow() *RecurringPaymentCreationWorkflow {
	return obj._RecurringPaymentCreationWorkflow
}
func (obj *Config) RecurringPaymentExecutionWorkflow() *RecurringPaymentExecutionWorkflow {
	return obj._RecurringPaymentExecutionWorkflow
}
func (obj *Config) RecurringPaymentModifyWorkflow() *RecurringPaymentModifyWorkflow {
	return obj._RecurringPaymentModifyWorkflow
}
func (obj *Config) RecurringPaymentRevokeWorkflow() *RecurringPaymentRevokeWorkflow {
	return obj._RecurringPaymentRevokeWorkflow
}
func (obj *Config) P2PInvestmentWorkflow() *P2PInvestmentWorkflow {
	return obj._P2PInvestmentWorkflow
}
func (obj *Config) P2PWithdrawalWorkflow() *P2PWithdrawalWorkflow {
	return obj._P2PWithdrawalWorkflow
}
func (obj *Config) MutualFundsInvestmentPostPaid() *MutualFundsInvestmentPostPaid {
	return obj._MutualFundsInvestmentPostPaid
}
func (obj *Config) MutualFundsRedemption() *MutualFundsRedemption {
	return obj._MutualFundsRedemption
}
func (obj *Config) PaymentNotificationParams() *PaymentNotificationParams {
	return obj._PaymentNotificationParams
}
func (obj *Config) PaymentSMSParams() *PaymentSMSParams {
	return obj._PaymentSMSParams
}
func (obj *Config) IconUrls() *IconUrls {
	return obj._IconUrls
}
func (obj *Config) Flags() *Flags {
	return obj._Flags
}
func (obj *Config) CustomRuleDEParams() *CustomRuleDEParams {
	return obj._CustomRuleDEParams
}
func (obj *Config) URNPaymentLimits() *URNPaymentLimits {
	return obj._URNPaymentLimits
}
func (obj *Config) SavingsLedgerReconSubscriber() *gencfg.SqsSubscriber {
	return obj._SavingsLedgerReconSubscriber
}
func (obj *Config) AddFundsAlertParams() *AddFundsAlertParams {
	return obj._AddFundsAlertParams
}
func (obj *Config) AddFundsAlertMailingParams() *AddFundsAlertMailingParams {
	return obj._AddFundsAlertMailingParams
}
func (obj *Config) ExecuteRecurringPaymentWithAuthWorkflow() *ExecuteRecurringPaymentWithAuthWorkflow {
	return obj._ExecuteRecurringPaymentWithAuthWorkflow
}
func (obj *Config) ExecuteRecurringPaymentWithNoAuthWorkflow() *ExecuteRecurringPaymentWithNoAuthWorkflow {
	return obj._ExecuteRecurringPaymentWithNoAuthWorkflow
}
func (obj *Config) RecurringPaymentPauseUnpauseWorkflow() *RecurringPaymentPauseUnpauseWorkflow {
	return obj._RecurringPaymentPauseUnpauseWorkflow
}
func (obj *Config) PurgeAaDataSubscriber() *gencfg.SqsSubscriber {
	return obj._PurgeAaDataSubscriber
}
func (obj *Config) AaTxnPurgeSubscriber() *gencfg.SqsSubscriber {
	return obj._AaTxnPurgeSubscriber
}
func (obj *Config) CSIS() *CSIS {
	return obj._CSIS
}
func (obj *Config) BankDownTime() *BankDownTime {
	return obj._BankDownTime
}
func (obj *Config) ConnectedAccountUserGroupParams() *ConnectedAccountUserGroupParams {
	return obj._ConnectedAccountUserGroupParams
}
func (obj *Config) AaDataPurgeOrchestrationSubscriber() *gencfg.SqsSubscriber {
	return obj._AaDataPurgeOrchestrationSubscriber
}
func (obj *Config) AaParams() *AaParams {
	return obj._AaParams
}
func (obj *Config) NameCheckParamsForAddFunds() *NameCheckParamsForAddFunds {
	return obj._NameCheckParamsForAddFunds
}
func (obj *Config) DeclineCardTransactionsProcessingSubscriber() *gencfg.SqsSubscriber {
	return obj._DeclineCardTransactionsProcessingSubscriber
}
func (obj *Config) SavingsLedgerReconCacheConfig() *SavingsLedgerReconCacheConfig {
	return obj._SavingsLedgerReconCacheConfig
}
func (obj *Config) OrderCacheConfig() *OrderCacheConfig {
	return obj._OrderCacheConfig
}
func (obj *Config) TransactionCacheConfig() *TransactionCacheConfig {
	return obj._TransactionCacheConfig
}
func (obj *Config) FeatureFlags() *FeatureFlags {
	return obj._FeatureFlags
}
func (obj *Config) InboundNotificationParams() *InboundNotificationParams {
	return obj._InboundNotificationParams
}
func (obj *Config) ErrorRespCodesForPermanentFailure() *ErrorRespCodesForPermanentFailure {
	return obj._ErrorRespCodesForPermanentFailure
}
func (obj *Config) FeatureReleaseConfig() *genconfig.FeatureReleaseConfig {
	return obj._FeatureReleaseConfig
}
func (obj *Config) DataReplicationParams() *gencfg.DataReplicationParams {
	return obj._DataReplicationParams
}
func (obj *Config) ReconRestrictedWindow() *RestrictedWindow {
	return obj._ReconRestrictedWindow
}
func (obj *Config) TxnNotificationSubscriber() *gencfg.SqsSubscriber {
	return obj._TxnNotificationSubscriber
}
func (obj *Config) DeemedTransactionUpiEnquirySubscriber() *gencfg.SqsSubscriber {
	return obj._DeemedTransactionUpiEnquirySubscriber
}
func (obj *Config) AAFirstDataPullTxnSubscriber() *gencfg.SqsSubscriber {
	return obj._AAFirstDataPullTxnSubscriber
}
func (obj *Config) QuestSdk() *genconfig2.Config {
	return obj._QuestSdk
}
func (obj *Config) RewardsInfo() *RewardsInfo {
	return obj._RewardsInfo
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) EpifiDb() *cfg.DB {
	return obj._EpifiDb
}
func (obj *Config) EpifiWealthDB() *cfg.DB {
	return obj._EpifiWealthDB
}
func (obj *Config) ConnectedAccountDB() *cfg.DB {
	return obj._ConnectedAccountDB
}
func (obj *Config) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}
func (obj *Config) OrderOrchestrationPublisher() *cfg.SqsPublisher {
	return obj._OrderOrchestrationPublisher
}
func (obj *Config) AATxnPublisher() *cfg.SnsPublisher {
	return obj._AATxnPublisher
}
func (obj *Config) OrderUpdateEventPublisher() *cfg.SnsPublisher {
	return obj._OrderUpdateEventPublisher
}
func (obj *Config) OrderMerchantMergeEventPublisher() *cfg.SnsPublisher {
	return obj._OrderMerchantMergeEventPublisher
}
func (obj *Config) TxnDetailedStatusUpdateSnsPublisher() *cfg.SnsPublisher {
	return obj._TxnDetailedStatusUpdateSnsPublisher
}
func (obj *Config) WorkflowProcessingPublisher() *cfg.SqsPublisher {
	return obj._WorkflowProcessingPublisher
}
func (obj *Config) OrderNotificationPublisher() *cfg.SqsPublisher {
	return obj._OrderNotificationPublisher
}
func (obj *Config) OrderCollectNotificationPublisher() *cfg.SqsPublisher {
	return obj._OrderCollectNotificationPublisher
}
func (obj *Config) OrderNotificationFallbackPublisher() *cfg.SqsPublisher {
	return obj._OrderNotificationFallbackPublisher
}
func (obj *Config) OrderSearchPublisher() *cfg.SqsPublisher {
	return obj._OrderSearchPublisher
}
func (obj *Config) EventsCompletedTnCPublisher() *cfg.SqsPublisher {
	return obj._EventsCompletedTnCPublisher
}
func (obj *Config) ProcrastinatorWorkflowPublisher() *cfg.SqsPublisher {
	return obj._ProcrastinatorWorkflowPublisher
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) PayFundTransferStatusCodeJson() string {
	return obj._PayFundTransferStatusCodeJson
}
func (obj *Config) PayUpiStatusCodeJson() string {
	return obj._PayUpiStatusCodeJson
}
func (obj *Config) EnachTransactionStatusCodeJson() string {
	return obj._EnachTransactionStatusCodeJson
}
func (obj *Config) ImpsIfscCodesCsv() string {
	return obj._ImpsIfscCodesCsv
}
func (obj *Config) RtgsBlockListIfscCodesCsv() string {
	return obj._RtgsBlockListIfscCodesCsv
}
func (obj *Config) CollectVelocityLimit() int64 {
	return obj._CollectVelocityLimit
}
func (obj *Config) CollectVelocityWindow() *cfg.TimeDuration {
	return obj._CollectVelocityWindow
}
func (obj *Config) CoolOffWindow() *cfg.TimeDuration {
	return obj._CoolOffWindow
}
func (obj *Config) CoolOffAmountLimit() *money.Money {
	return obj._CoolOffAmountLimit
}
func (obj *Config) RudderStack() *config.RudderBroker {
	return obj._RudderStack
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) PersonMCCCode() string {
	return obj._PersonMCCCode
}
func (obj *Config) SavingsLedgerReconPublisher() *cfg.SqsPublisher {
	return obj._SavingsLedgerReconPublisher
}
func (obj *Config) OrderEventAmountCategories() *[]config.OrderAmountCategory {
	return obj._OrderEventAmountCategories
}
func (obj *Config) AaAccountPiPurgePublisher() *cfg.SqsPublisher {
	return obj._AaAccountPiPurgePublisher
}
func (obj *Config) PaymentOrchestrationPublisher() *cfg.SqsPublisher {
	return obj._PaymentOrchestrationPublisher
}
func (obj *Config) IntraBankEnquiryPublisher() *cfg.SqsPublisher {
	return obj._IntraBankEnquiryPublisher
}
func (obj *Config) UPIEnquiryPublisher() *cfg.SqsPublisher {
	return obj._UPIEnquiryPublisher
}
func (obj *Config) IMPSEnquiryPublisher() *cfg.SqsPublisher {
	return obj._IMPSEnquiryPublisher
}
func (obj *Config) NEFTEnquiryPublisher() *cfg.SqsPublisher {
	return obj._NEFTEnquiryPublisher
}
func (obj *Config) RTGSEnquiryPublisher() *cfg.SqsPublisher {
	return obj._RTGSEnquiryPublisher
}
func (obj *Config) ActorPiRelationPurgePublisher() *cfg.SqsPublisher {
	return obj._ActorPiRelationPurgePublisher
}
func (obj *Config) SignalWorkflowPublisher() *cfg.SqsPublisher {
	return obj._SignalWorkflowPublisher
}
func (obj *Config) Events() *config.EventsConfig {
	return obj._Events
}
func (obj *Config) PartnerBankEODDelayMap() map[string]time.Duration {
	return obj._PartnerBankEODDelayMap
}
func (obj *Config) DynamicVPAParamsMap() map[string]*config.DynamicVPAParams {
	return obj._DynamicVPAParamsMap
}
func (obj *Config) DynamicVPAV1ParamsMap() map[string]*config.DynamicVPAParams {
	return obj._DynamicVPAV1ParamsMap
}
func (obj *Config) CampaignParams() map[string]*config.CampaignParams {
	return obj._CampaignParams
}
func (obj *Config) PaymentEnquiryParams() *pay.PaymentEnquiryParams {
	return obj._PaymentEnquiryParams
}
func (obj *Config) DeclineDataAwsSftpBucket() *sftp.AwsSftpBucketConfig {
	return obj._DeclineDataAwsSftpBucket
}
func (obj *Config) AaDataPurgeOrchestrationPublisher() *cfg.SqsPublisher {
	return obj._AaDataPurgeOrchestrationPublisher
}
func (obj *Config) CardTxnStatusCodeJson() string {
	return obj._CardTxnStatusCodeJson
}
func (obj *Config) DefaultExpiryDuration() int64 {
	return obj._DefaultExpiryDuration
}
func (obj *Config) ReconParams() *config.ReconParams {
	return obj._ReconParams
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) UpiUtrCollisionRetryCount() int32 {
	return obj._UpiUtrCollisionRetryCount
}
func (obj *Config) RemitterInfoSyncDelayThreshold() time.Duration {
	return obj._RemitterInfoSyncDelayThreshold
}
func (obj *Config) DelayRangeForBalanceV1() *config.DelayRangeForBalanceV1 {
	return obj._DelayRangeForBalanceV1
}
func (obj *Config) OrderVpaVerificationPublisher() *cfg.SqsPublisher {
	return obj._OrderVpaVerificationPublisher
}
func (obj *Config) ReVerifyAddressDelay() time.Duration {
	return obj._ReVerifyAddressDelay
}
func (obj *Config) VerifyVpaDeadlineForInboundNotification() time.Duration {
	return obj._VerifyVpaDeadlineForInboundNotification
}
func (obj *Config) B2CInitialEnquiryDelay() time.Duration {
	return obj._B2CInitialEnquiryDelay
}
func (obj *Config) TxnNotificationPublisher() *cfg.SqsPublisher {
	return obj._TxnNotificationPublisher
}
func (obj *Config) MccsWithOnlyAllowedIntentPayments() map[string][]string {
	return obj._MccsWithOnlyAllowedIntentPayments
}
func (obj *Config) DeemedTransactionUPIEnquiryPublisher() *cfg.SqsPublisher {
	return obj._DeemedTransactionUPIEnquiryPublisher
}
func (obj *Config) B2CPaymentParams() *config.B2CPaymentParams {
	return obj._B2CPaymentParams
}
func (obj *Config) InPaymentOrderUpdatePublisher() *cfg.SqsPublisher {
	return obj._InPaymentOrderUpdatePublisher
}
func (obj *Config) NonTpapPspHandles() []string {
	return obj._NonTpapPspHandles
}
func (obj *Config) AmountThresholdForNotification() *money.Money {
	return obj._AmountThresholdForNotification
}
func (obj *Config) InternationalQrInfoPrefix() string {
	return obj._InternationalQrInfoPrefix
}
func (obj *Config) DelimiterForRedisKey() string {
	return obj._DelimiterForRedisKey
}
func (obj *Config) ForexRefundInfo() *config.DcForexRefundInfo {
	return obj._ForexRefundInfo
}
func (obj *Config) IsStatementDataComparison() bool {
	return obj._IsStatementDataComparison
}
func (obj *Config) QuestRedisOptions() *cfg.RedisOptions {
	return obj._QuestRedisOptions
}
func (obj *Config) IncidentManagerParams() *pay.IncidentManagerParams {
	return obj._IncidentManagerParams
}
func (obj *Config) SecureLoggingV2() *v.SecureLogging {
	return obj._SecureLoggingV2
}
func (obj *Config) GrpcRatelimiterParams() *cfg.GrpcRateLimiterParams {
	return obj._GrpcRatelimiterParams
}
func (obj *Config) CommsConfig() *config.CommsConfig {
	return obj._CommsConfig
}
func (obj *Config) ReportFraudConfig() *config.ReportFraudConfig {
	return obj._ReportFraudConfig
}
func (obj *Config) TransactionAttributesBasedStoryDetailsCombinations() []*config.TransactionAttributesBasedStoryDetails {
	return obj._TransactionAttributesBasedStoryDetailsCombinations
}
func (obj *Config) RupayCCRestrictedMCCs() []string {
	return obj._RupayCCRestrictedMCCs
}

type Application struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Environment                         string
	_Name                                string
	_IsSeparateEnquiryPerProtocolEnabled bool
}

func (obj *Application) Environment() string {
	return obj._Environment
}
func (obj *Application) Name() string {
	return obj._Name
}
func (obj *Application) IsSeparateEnquiryPerProtocolEnabled() bool {
	return obj._IsSeparateEnquiryPerProtocolEnabled
}

type Server struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Ports *cfg.ServerPorts
}

func (obj *Server) Ports() *cfg.ServerPorts {
	return obj._Ports
}

type PaymentProtocolDecisionParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_TotalTransactedAmountLimitInCoolDown    int32
	_BeneficiaryCoolDownAmountLimit          int32
	_INTRAParams                             *ProtocolParams
	_IMPSParams                              *ProtocolParams
	_NEFTParams                              *ProtocolParams
	_RTGSParams                              *ProtocolParams
	_UPIParams                               *UpiParams
	_UpiPreferredPaymentProtocolAmountParams *SoftPreferredPaymentProtocolAmountParams
	_UserPaymentParams                       *UserPaymentParams
	_UPILimitExceptions                      *config.UPILimitExceptions
	_ProfileUpdateCoolDownWindow             *cfg.TimeDuration
}

func (obj *PaymentProtocolDecisionParams) TotalTransactedAmountLimitInCoolDown() int64 {
	return int64(atomic.LoadInt32(&obj._TotalTransactedAmountLimitInCoolDown))
}
func (obj *PaymentProtocolDecisionParams) BeneficiaryCoolDownAmountLimit() int64 {
	return int64(atomic.LoadInt32(&obj._BeneficiaryCoolDownAmountLimit))
}
func (obj *PaymentProtocolDecisionParams) INTRAParams() *ProtocolParams {
	return obj._INTRAParams
}
func (obj *PaymentProtocolDecisionParams) IMPSParams() *ProtocolParams {
	return obj._IMPSParams
}
func (obj *PaymentProtocolDecisionParams) NEFTParams() *ProtocolParams {
	return obj._NEFTParams
}
func (obj *PaymentProtocolDecisionParams) RTGSParams() *ProtocolParams {
	return obj._RTGSParams
}
func (obj *PaymentProtocolDecisionParams) UPIParams() *UpiParams {
	return obj._UPIParams
}
func (obj *PaymentProtocolDecisionParams) UpiPreferredPaymentProtocolAmountParams() *SoftPreferredPaymentProtocolAmountParams {
	return obj._UpiPreferredPaymentProtocolAmountParams
}
func (obj *PaymentProtocolDecisionParams) UserPaymentParams() *UserPaymentParams {
	return obj._UserPaymentParams
}
func (obj *PaymentProtocolDecisionParams) UPILimitExceptions() *config.UPILimitExceptions {
	return obj._UPILimitExceptions
}
func (obj *PaymentProtocolDecisionParams) ProfileUpdateCoolDownWindow() *cfg.TimeDuration {
	return obj._ProfileUpdateCoolDownWindow
}

type ProtocolParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MinAmount int32
	_MaxAmount int32
	// maximum transacted amount allowed for a payer using given Protocol if the payer is in cooldown period
	_TotalTransactedAmountLimitInCoolDown int32
	// maximum number of transaction allowed for payer using payment protocol
	_TransactionCountLimit        int32
	_ProtocolRestrictedWindow     *RestrictedWindow
	_ProtocolDailyDowntime        *ProtocolDailyDowntime
	_ProfileUpdateCoolDownWindow  *cfg.TimeDuration
	_LimitWindow                  *cfg.TimeDuration
	_IntraCoolOffNotApplicablePis []string
}

func (obj *ProtocolParams) MinAmount() int64 {
	return int64(atomic.LoadInt32(&obj._MinAmount))
}
func (obj *ProtocolParams) MaxAmount() int64 {
	return int64(atomic.LoadInt32(&obj._MaxAmount))
}

// maximum transacted amount allowed for a payer using given Protocol if the payer is in cooldown period
func (obj *ProtocolParams) TotalTransactedAmountLimitInCoolDown() int64 {
	return int64(atomic.LoadInt32(&obj._TotalTransactedAmountLimitInCoolDown))
}

// maximum number of transaction allowed for payer using payment protocol
func (obj *ProtocolParams) TransactionCountLimit() int64 {
	return int64(atomic.LoadInt32(&obj._TransactionCountLimit))
}
func (obj *ProtocolParams) ProtocolRestrictedWindow() *RestrictedWindow {
	return obj._ProtocolRestrictedWindow
}
func (obj *ProtocolParams) ProtocolDailyDowntime() *ProtocolDailyDowntime {
	return obj._ProtocolDailyDowntime
}
func (obj *ProtocolParams) ProfileUpdateCoolDownWindow() *cfg.TimeDuration {
	return obj._ProfileUpdateCoolDownWindow
}
func (obj *ProtocolParams) LimitWindow() *cfg.TimeDuration {
	return obj._LimitWindow
}
func (obj *ProtocolParams) IntraCoolOffNotApplicablePis() []string {
	return obj._IntraCoolOffNotApplicablePis
}

type RestrictedWindow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled         uint32
	_FromDateTime      string
	_FromDateTimeMutex *sync.RWMutex
	_ToDateTime        string
	_ToDateTimeMutex   *sync.RWMutex
	_Timezone          string
	_DateTimeFormat    string
}

func (obj *RestrictedWindow) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RestrictedWindow) FromDateTime() string {
	obj._FromDateTimeMutex.RLock()
	defer obj._FromDateTimeMutex.RUnlock()
	return obj._FromDateTime
}
func (obj *RestrictedWindow) ToDateTime() string {
	obj._ToDateTimeMutex.RLock()
	defer obj._ToDateTimeMutex.RUnlock()
	return obj._ToDateTime
}
func (obj *RestrictedWindow) Timezone() string {
	return obj._Timezone
}
func (obj *RestrictedWindow) DateTimeFormat() string {
	return obj._DateTimeFormat
}

type ProtocolDailyDowntime struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled      uint32
	_StartTime      string
	_StartTimeMutex *sync.RWMutex
	_EndTime        string
	_EndTimeMutex   *sync.RWMutex
}

func (obj *ProtocolDailyDowntime) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ProtocolDailyDowntime) StartTime() string {
	obj._StartTimeMutex.RLock()
	defer obj._StartTimeMutex.RUnlock()
	return obj._StartTime
}
func (obj *ProtocolDailyDowntime) EndTime() string {
	obj._EndTimeMutex.RLock()
	defer obj._EndTimeMutex.RUnlock()
	return obj._EndTime
}

type UpiParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_UpiMinAmount int32
	_UpiMaxAmount int32
	// maximum total transacted amount allowed for a payer using UPI Protocol in the UpiLimitWindow
	_UpiTotalTransactedAmountLimit int32
	// maximum number of txns allowed for a payer using UPI Protocol in the UpiLimitWindow
	_UpiTransactionCountLimit int32
	// *Maximum total transacted amount allowed for a payer using UPI Protocol if payer is new user.
	// It'll be used to apply cool off for newly onboarded users as well for users doing re-registrations.
	// i.e. every time user does device registration cool off of this amount will be applied.
	_UpiNewUserTotalTransactedAmountLimit int32
	// maximum total transacted amount allowed for a payer using UPI Protocol if their device is in cooldown period
	_UpiCoolDownPeriodTotalTransactedAmountLimit int32
	// maximum transacted amount allowed for a payer after successful UPI pin reset in the UpiPinResetLimitWindow
	_UpiPinResetTotalTransactedAmountLimit        int32
	_UPIRestrictedWindow                          *RestrictedWindow
	_UpiLimitWindow                               *cfg.TimeDuration
	_UpiPinResetLimitWindow                       *cfg.TimeDuration
	_UpiProfileUpdateCoolDownWindow               map[string]*cfg.TimeDuration
	_UpiProfileUpdateCoolDownTriggerDurationLimit *cfg.TimeDuration
	_UpiProfileUpdateAfuSummariesFetchDuration    *cfg.TimeDuration
}

func (obj *UpiParams) UpiMinAmount() int64 {
	return int64(atomic.LoadInt32(&obj._UpiMinAmount))
}
func (obj *UpiParams) UpiMaxAmount() int64 {
	return int64(atomic.LoadInt32(&obj._UpiMaxAmount))
}

// maximum total transacted amount allowed for a payer using UPI Protocol in the UpiLimitWindow
func (obj *UpiParams) UpiTotalTransactedAmountLimit() int64 {
	return int64(atomic.LoadInt32(&obj._UpiTotalTransactedAmountLimit))
}

// maximum number of txns allowed for a payer using UPI Protocol in the UpiLimitWindow
func (obj *UpiParams) UpiTransactionCountLimit() int64 {
	return int64(atomic.LoadInt32(&obj._UpiTransactionCountLimit))
}

// *Maximum total transacted amount allowed for a payer using UPI Protocol if payer is new user.
// It'll be used to apply cool off for newly onboarded users as well for users doing re-registrations.
// i.e. every time user does device registration cool off of this amount will be applied.
func (obj *UpiParams) UpiNewUserTotalTransactedAmountLimit() int64 {
	return int64(atomic.LoadInt32(&obj._UpiNewUserTotalTransactedAmountLimit))
}

// maximum total transacted amount allowed for a payer using UPI Protocol if their device is in cooldown period
func (obj *UpiParams) UpiCoolDownPeriodTotalTransactedAmountLimit() int64 {
	return int64(atomic.LoadInt32(&obj._UpiCoolDownPeriodTotalTransactedAmountLimit))
}

// maximum transacted amount allowed for a payer after successful UPI pin reset in the UpiPinResetLimitWindow
func (obj *UpiParams) UpiPinResetTotalTransactedAmountLimit() int64 {
	return int64(atomic.LoadInt32(&obj._UpiPinResetTotalTransactedAmountLimit))
}
func (obj *UpiParams) UPIRestrictedWindow() *RestrictedWindow {
	return obj._UPIRestrictedWindow
}
func (obj *UpiParams) UpiLimitWindow() *cfg.TimeDuration {
	return obj._UpiLimitWindow
}
func (obj *UpiParams) UpiPinResetLimitWindow() *cfg.TimeDuration {
	return obj._UpiPinResetLimitWindow
}
func (obj *UpiParams) UpiProfileUpdateCoolDownWindow() map[string]*cfg.TimeDuration {
	return obj._UpiProfileUpdateCoolDownWindow
}
func (obj *UpiParams) UpiProfileUpdateCoolDownTriggerDurationLimit() *cfg.TimeDuration {
	return obj._UpiProfileUpdateCoolDownTriggerDurationLimit
}
func (obj *UpiParams) UpiProfileUpdateAfuSummariesFetchDuration() *cfg.TimeDuration {
	return obj._UpiProfileUpdateAfuSummariesFetchDuration
}

type UPILimitExceptions struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MCC               map[string]config.UPILimits
	_P2PInitiationMode map[string]config.UPILimits
	_P2MInitiationMode map[string]config.UPILimits
	_PurposeCode       map[string]config.UPILimits
}

func (obj *UPILimitExceptions) MCC() map[string]config.UPILimits {
	return obj._MCC
}
func (obj *UPILimitExceptions) P2PInitiationMode() map[string]config.UPILimits {
	return obj._P2PInitiationMode
}
func (obj *UPILimitExceptions) P2MInitiationMode() map[string]config.UPILimits {
	return obj._P2MInitiationMode
}
func (obj *UPILimitExceptions) PurposeCode() map[string]config.UPILimits {
	return obj._PurposeCode
}

type SoftPreferredPaymentProtocolAmountParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MinAmount int32
	_MaxAmount int32
	_Enable    uint32
}

func (obj *SoftPreferredPaymentProtocolAmountParams) MinAmount() int64 {
	return int64(atomic.LoadInt32(&obj._MinAmount))
}
func (obj *SoftPreferredPaymentProtocolAmountParams) MaxAmount() int64 {
	return int64(atomic.LoadInt32(&obj._MaxAmount))
}
func (obj *SoftPreferredPaymentProtocolAmountParams) Enable() bool {
	if atomic.LoadUint32(&obj._Enable) == 0 {
		return false
	} else {
		return true
	}
}

type UserPaymentParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_TotalTransactionAmountLimit      *LimitByDuration
	_NewAccountTransactionAmountLimit *NewAccountLimitByDuration
}

func (obj *UserPaymentParams) TotalTransactionAmountLimit() *LimitByDuration {
	return obj._TotalTransactionAmountLimit
}
func (obj *UserPaymentParams) NewAccountTransactionAmountLimit() *NewAccountLimitByDuration {
	return obj._NewAccountTransactionAmountLimit
}

type LimitByDuration struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// maximum Total Transacted amount allowed for a payer using all protocol.
	_AmountLimit int32
	_TimeWindow  *cfg.TimeDuration
}

// maximum Total Transacted amount allowed for a payer using all protocol.
func (obj *LimitByDuration) AmountLimit() int64 {
	return int64(atomic.LoadInt32(&obj._AmountLimit))
}
func (obj *LimitByDuration) TimeWindow() *cfg.TimeDuration {
	return obj._TimeWindow
}

type NewAccountLimitByDuration struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// maximum Total Transacted amount allowed for a new account
	_AmountLimit int32
	// Threshold from account creation to consider as new account
	_AccountAgeLimitThreshold int64
}

// maximum Total Transacted amount allowed for a new account
func (obj *NewAccountLimitByDuration) AmountLimit() int64 {
	return int64(atomic.LoadInt32(&obj._AmountLimit))
}

// Threshold from account creation to consider as new account
func (obj *NewAccountLimitByDuration) AccountAgeLimitThreshold() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._AccountAgeLimitThreshold))
}

type RuleEngineParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_SalienceINTRA               int64
	_SalienceUPI                 int64
	_SalienceIMPS                int64
	_SalienceRTGS                int64
	_SalienceNEFT                int64
	_BumpedSalience              int64
	_SecondHighestBumpedSalience int64
}

func (obj *RuleEngineParams) SalienceINTRA() int {
	return int(atomic.LoadInt64(&obj._SalienceINTRA))
}
func (obj *RuleEngineParams) SalienceUPI() int {
	return int(atomic.LoadInt64(&obj._SalienceUPI))
}
func (obj *RuleEngineParams) SalienceIMPS() int {
	return int(atomic.LoadInt64(&obj._SalienceIMPS))
}
func (obj *RuleEngineParams) SalienceRTGS() int {
	return int(atomic.LoadInt64(&obj._SalienceRTGS))
}
func (obj *RuleEngineParams) SalienceNEFT() int {
	return int(atomic.LoadInt64(&obj._SalienceNEFT))
}
func (obj *RuleEngineParams) BumpedSalience() int {
	return int(atomic.LoadInt64(&obj._BumpedSalience))
}
func (obj *RuleEngineParams) SecondHighestBumpedSalience() int {
	return int(atomic.LoadInt64(&obj._SecondHighestBumpedSalience))
}

type OrderReceipt struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ReceiptHead                        *config.ReceiptHead
	_ConsolidatedStages                 *config.ConsolidatedStages
	_FundTransferStages                 *config.Stages
	_AddFundsStages                     *config.Stages
	_FundTransferWithReversalStages     *config.Stages
	_AddFundsWithReversalStages         *config.Stages
	_PaymentDetailKey                   *config.PaymentDetailKey
	_DisputeDescription                 *config.DisputeDescription
	_OrderReceiptProtocolLevelBanner    *config.OrderReceiptProtocolLevelBanner
	_PaymentStatusPendingBanner         *config.PaymentStatusPendingBanner
	_FaqInfo                            *config.IconTextComponentInfo
	_GetHelpInfo                        *config.IconTextComponentInfo
	_RaiseDisputeInfo                   *config.IconTextComponentInfo
	_UserCautionInfo                    *config.IconTextComponentInfo
	_ChatWithUsInfo                     *config.IconTextComponentInfo
	_EcsEnachChargesBanner              *config.IconTextComponentInfo
	_OffAppEnachBanner                  *config.IconTextComponentInfo
	_RemarksForEcsEnachCharges          string
	_AtmWithdrawalUserCautionInfo       *config.IconTextComponentInfo
	_ForexMarkupFeesStages              *config.Stages
	_OrderReceiptDCAmcChargesInfoBanner *config.OrderReceiptDCAmcChargesInfoBanner
}

func (obj *OrderReceipt) ReceiptHead() *config.ReceiptHead {
	return obj._ReceiptHead
}
func (obj *OrderReceipt) ConsolidatedStages() *config.ConsolidatedStages {
	return obj._ConsolidatedStages
}
func (obj *OrderReceipt) FundTransferStages() *config.Stages {
	return obj._FundTransferStages
}
func (obj *OrderReceipt) AddFundsStages() *config.Stages {
	return obj._AddFundsStages
}
func (obj *OrderReceipt) FundTransferWithReversalStages() *config.Stages {
	return obj._FundTransferWithReversalStages
}
func (obj *OrderReceipt) AddFundsWithReversalStages() *config.Stages {
	return obj._AddFundsWithReversalStages
}
func (obj *OrderReceipt) PaymentDetailKey() *config.PaymentDetailKey {
	return obj._PaymentDetailKey
}
func (obj *OrderReceipt) DisputeDescription() *config.DisputeDescription {
	return obj._DisputeDescription
}
func (obj *OrderReceipt) OrderReceiptProtocolLevelBanner() *config.OrderReceiptProtocolLevelBanner {
	return obj._OrderReceiptProtocolLevelBanner
}
func (obj *OrderReceipt) PaymentStatusPendingBanner() *config.PaymentStatusPendingBanner {
	return obj._PaymentStatusPendingBanner
}
func (obj *OrderReceipt) FaqInfo() *config.IconTextComponentInfo {
	return obj._FaqInfo
}
func (obj *OrderReceipt) GetHelpInfo() *config.IconTextComponentInfo {
	return obj._GetHelpInfo
}
func (obj *OrderReceipt) RaiseDisputeInfo() *config.IconTextComponentInfo {
	return obj._RaiseDisputeInfo
}
func (obj *OrderReceipt) UserCautionInfo() *config.IconTextComponentInfo {
	return obj._UserCautionInfo
}
func (obj *OrderReceipt) ChatWithUsInfo() *config.IconTextComponentInfo {
	return obj._ChatWithUsInfo
}
func (obj *OrderReceipt) EcsEnachChargesBanner() *config.IconTextComponentInfo {
	return obj._EcsEnachChargesBanner
}
func (obj *OrderReceipt) OffAppEnachBanner() *config.IconTextComponentInfo {
	return obj._OffAppEnachBanner
}
func (obj *OrderReceipt) RemarksForEcsEnachCharges() string {
	return obj._RemarksForEcsEnachCharges
}
func (obj *OrderReceipt) AtmWithdrawalUserCautionInfo() *config.IconTextComponentInfo {
	return obj._AtmWithdrawalUserCautionInfo
}
func (obj *OrderReceipt) ForexMarkupFeesStages() *config.Stages {
	return obj._ForexMarkupFeesStages
}
func (obj *OrderReceipt) OrderReceiptDCAmcChargesInfoBanner() *config.OrderReceiptDCAmcChargesInfoBanner {
	return obj._OrderReceiptDCAmcChargesInfoBanner
}

type ReceiptHead struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_CreditTitle         string
	_DebitTitle          string
	_FixedDepositIconURL string
	_SmartDepositIconURL string
	_PayBGColourCode     string
	_DepositBGColourCode string
}

func (obj *ReceiptHead) CreditTitle() string {
	return obj._CreditTitle
}
func (obj *ReceiptHead) DebitTitle() string {
	return obj._DebitTitle
}
func (obj *ReceiptHead) FixedDepositIconURL() string {
	return obj._FixedDepositIconURL
}
func (obj *ReceiptHead) SmartDepositIconURL() string {
	return obj._SmartDepositIconURL
}
func (obj *ReceiptHead) PayBGColourCode() string {
	return obj._PayBGColourCode
}
func (obj *ReceiptHead) DepositBGColourCode() string {
	return obj._DepositBGColourCode
}

type ConsolidatedStages struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_PaymentSuccess    *config.ConsolidatedStage
	_PaymentInProgress *config.ConsolidatedStage
	_PaymentFailed     *config.ConsolidatedStage
	_CollectCancelled  *config.ConsolidatedStage
	_CollectDeclined   *config.ConsolidatedStage
	_PaymentInitiated  *config.ConsolidatedStage
	_CollectExpired    *config.ConsolidatedStage
	_PaymentReversed   *config.ConsolidatedStage
}

func (obj *ConsolidatedStages) PaymentSuccess() *config.ConsolidatedStage {
	return obj._PaymentSuccess
}
func (obj *ConsolidatedStages) PaymentInProgress() *config.ConsolidatedStage {
	return obj._PaymentInProgress
}
func (obj *ConsolidatedStages) PaymentFailed() *config.ConsolidatedStage {
	return obj._PaymentFailed
}
func (obj *ConsolidatedStages) CollectCancelled() *config.ConsolidatedStage {
	return obj._CollectCancelled
}
func (obj *ConsolidatedStages) CollectDeclined() *config.ConsolidatedStage {
	return obj._CollectDeclined
}
func (obj *ConsolidatedStages) PaymentInitiated() *config.ConsolidatedStage {
	return obj._PaymentInitiated
}
func (obj *ConsolidatedStages) CollectExpired() *config.ConsolidatedStage {
	return obj._CollectExpired
}
func (obj *ConsolidatedStages) PaymentReversed() *config.ConsolidatedStage {
	return obj._PaymentReversed
}

type ConsolidatedStage struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Description           string
	_IsTimeStampEnabled    bool
	_TimeLayout            string
	_TimeStampStringFormat string
}

func (obj *ConsolidatedStage) Description() string {
	return obj._Description
}
func (obj *ConsolidatedStage) IsTimeStampEnabled() bool {
	return obj._IsTimeStampEnabled
}
func (obj *ConsolidatedStage) TimeLayout() string {
	return obj._TimeLayout
}
func (obj *ConsolidatedStage) TimeStampStringFormat() string {
	return obj._TimeStampStringFormat
}

type Stages struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_StageDescriptions     []*config.StageDescription
	_DescriptionTimeLayout string
}

func (obj *Stages) StageDescriptions() []*config.StageDescription {
	return obj._StageDescriptions
}
func (obj *Stages) DescriptionTimeLayout() string {
	return obj._DescriptionTimeLayout
}

type PaymentDetailKey struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_FromActorKey                          string
	_ToActorKey                            string
	_ProtocolTransactionIdKey              string
	_FiOrderIdKey                          string
	_PaymentProtocolKey                    string
	_FromActorNameKey                      string
	_ToActorNameKey                        string
	_TransactionRemark                     string
	_InternationalPaymentMarkupKey         string
	_InternationalPaymentConversionRateKey string
	_RemarksKey                            string
}

func (obj *PaymentDetailKey) FromActorKey() string {
	return obj._FromActorKey
}
func (obj *PaymentDetailKey) ToActorKey() string {
	return obj._ToActorKey
}
func (obj *PaymentDetailKey) ProtocolTransactionIdKey() string {
	return obj._ProtocolTransactionIdKey
}
func (obj *PaymentDetailKey) FiOrderIdKey() string {
	return obj._FiOrderIdKey
}
func (obj *PaymentDetailKey) PaymentProtocolKey() string {
	return obj._PaymentProtocolKey
}
func (obj *PaymentDetailKey) FromActorNameKey() string {
	return obj._FromActorNameKey
}
func (obj *PaymentDetailKey) ToActorNameKey() string {
	return obj._ToActorNameKey
}
func (obj *PaymentDetailKey) TransactionRemark() string {
	return obj._TransactionRemark
}
func (obj *PaymentDetailKey) InternationalPaymentMarkupKey() string {
	return obj._InternationalPaymentMarkupKey
}
func (obj *PaymentDetailKey) InternationalPaymentConversionRateKey() string {
	return obj._InternationalPaymentConversionRateKey
}
func (obj *PaymentDetailKey) RemarksKey() string {
	return obj._RemarksKey
}

type DisputeDescription struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Desc       string
	_TimeLayout string
}

func (obj *DisputeDescription) Desc() string {
	return obj._Desc
}
func (obj *DisputeDescription) TimeLayout() string {
	return obj._TimeLayout
}

type OrderReceiptProtocolLevelBanner struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_NeftTxnInfoBanner   *config.PaymentProtocolInfoBanner
	_ChequeTxnInfoBanner *config.PaymentProtocolInfoBanner
}

func (obj *OrderReceiptProtocolLevelBanner) NeftTxnInfoBanner() *config.PaymentProtocolInfoBanner {
	return obj._NeftTxnInfoBanner
}
func (obj *OrderReceiptProtocolLevelBanner) ChequeTxnInfoBanner() *config.PaymentProtocolInfoBanner {
	return obj._ChequeTxnInfoBanner
}

type PaymentProtocolInfoBanner struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_InfoString string
	_Window     time.Duration
}

func (obj *PaymentProtocolInfoBanner) InfoString() string {
	return obj._InfoString
}
func (obj *PaymentProtocolInfoBanner) Window() time.Duration {
	return obj._Window
}

type PaymentStatusPendingBanner struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_InfoString string
	_FontColour string
	_BgColour   string
	_FontStyle  string
}

func (obj *PaymentStatusPendingBanner) InfoString() string {
	return obj._InfoString
}
func (obj *PaymentStatusPendingBanner) FontColour() string {
	return obj._FontColour
}
func (obj *PaymentStatusPendingBanner) BgColour() string {
	return obj._BgColour
}
func (obj *PaymentStatusPendingBanner) FontStyle() string {
	return obj._FontStyle
}

type IconTextComponentInfo struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ImageUrl               string
	_ImageHeight            int32
	_ImageWidth             int32
	_Description            []*config.TextObject
	_LeftImgTxtPadding      int32
	_ContainerLeftPadding   int32
	_ContainerRightPadding  int32
	_ContainerTopPadding    int32
	_ContainerBottomPadding int32
	_ContainerCornerRadius  int32
	_ContainerBorderColour  string
	_ContainerBgColour      string
	_Deeplink               []*config.DeeplinkForAction
}

func (obj *IconTextComponentInfo) ImageUrl() string {
	return obj._ImageUrl
}
func (obj *IconTextComponentInfo) ImageHeight() int32 {
	return obj._ImageHeight
}
func (obj *IconTextComponentInfo) ImageWidth() int32 {
	return obj._ImageWidth
}
func (obj *IconTextComponentInfo) Description() []*config.TextObject {
	return obj._Description
}
func (obj *IconTextComponentInfo) LeftImgTxtPadding() int32 {
	return obj._LeftImgTxtPadding
}
func (obj *IconTextComponentInfo) ContainerLeftPadding() int32 {
	return obj._ContainerLeftPadding
}
func (obj *IconTextComponentInfo) ContainerRightPadding() int32 {
	return obj._ContainerRightPadding
}
func (obj *IconTextComponentInfo) ContainerTopPadding() int32 {
	return obj._ContainerTopPadding
}
func (obj *IconTextComponentInfo) ContainerBottomPadding() int32 {
	return obj._ContainerBottomPadding
}
func (obj *IconTextComponentInfo) ContainerCornerRadius() int32 {
	return obj._ContainerCornerRadius
}
func (obj *IconTextComponentInfo) ContainerBorderColour() string {
	return obj._ContainerBorderColour
}
func (obj *IconTextComponentInfo) ContainerBgColour() string {
	return obj._ContainerBgColour
}
func (obj *IconTextComponentInfo) Deeplink() []*config.DeeplinkForAction {
	return obj._Deeplink
}

type OrderReceiptDCAmcChargesInfoBanner struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Title                     string
	_SubTitle                  string
	_BannerImage               string
	_OnePercentBottomSheetInfo *config.OnePercentBottomSheetInfo
}

func (obj *OrderReceiptDCAmcChargesInfoBanner) Title() string {
	return obj._Title
}
func (obj *OrderReceiptDCAmcChargesInfoBanner) SubTitle() string {
	return obj._SubTitle
}
func (obj *OrderReceiptDCAmcChargesInfoBanner) BannerImage() string {
	return obj._BannerImage
}
func (obj *OrderReceiptDCAmcChargesInfoBanner) OnePercentBottomSheetInfo() *config.OnePercentBottomSheetInfo {
	return obj._OnePercentBottomSheetInfo
}

type OnePercentBottomSheetInfo struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Title                string
	_TopImage             string
	_StepsImage           string
	_Subtitle             string
	_Note                 string
	_Tnc                  string
	_DoKeepInMind         string
	_DoKeepInMindPointers string
	_TermsAndConditions   string
}

func (obj *OnePercentBottomSheetInfo) Title() string {
	return obj._Title
}
func (obj *OnePercentBottomSheetInfo) TopImage() string {
	return obj._TopImage
}
func (obj *OnePercentBottomSheetInfo) StepsImage() string {
	return obj._StepsImage
}
func (obj *OnePercentBottomSheetInfo) Subtitle() string {
	return obj._Subtitle
}
func (obj *OnePercentBottomSheetInfo) Note() string {
	return obj._Note
}
func (obj *OnePercentBottomSheetInfo) Tnc() string {
	return obj._Tnc
}
func (obj *OnePercentBottomSheetInfo) DoKeepInMind() string {
	return obj._DoKeepInMind
}
func (obj *OnePercentBottomSheetInfo) DoKeepInMindPointers() string {
	return obj._DoKeepInMindPointers
}
func (obj *OnePercentBottomSheetInfo) TermsAndConditions() string {
	return obj._TermsAndConditions
}

type FundTransferWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_LowValueTransactionUpperLimit *money.Money
}

func (obj *FundTransferWorkflow) LowValueTransactionUpperLimit() *money.Money {
	return obj._LowValueTransactionUpperLimit
}

type CollectShortCircuitWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_LowValueTransactionUpperLimit *money.Money
	_CollectAmountLimit            *money.Money
	_CollectExpirationDuration     time.Duration
}

func (obj *CollectShortCircuitWorkflow) LowValueTransactionUpperLimit() *money.Money {
	return obj._LowValueTransactionUpperLimit
}
func (obj *CollectShortCircuitWorkflow) CollectAmountLimit() *money.Money {
	return obj._CollectAmountLimit
}
func (obj *CollectShortCircuitWorkflow) CollectExpirationDuration() time.Duration {
	return obj._CollectExpirationDuration
}

type DepositCreationWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Fulfillment     *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *DepositCreationWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DepositCreationWorkflow) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *DepositCreationWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type StageProcessingParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_RetryStrategy *cfg.RetryParams
	_Method        string
	_ServiceName   cfg.ServiceName
}

func (obj *StageProcessingParams) RetryStrategy() *cfg.RetryParams {
	return obj._RetryStrategy
}
func (obj *StageProcessingParams) Method() string {
	return obj._Method
}
func (obj *StageProcessingParams) ServiceName() cfg.ServiceName {
	return obj._ServiceName
}

type UrnTransferWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Payment         *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *UrnTransferWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *UrnTransferWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *UrnTransferWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type PreCloseDepositWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Fulfillment     *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *PreCloseDepositWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *PreCloseDepositWorkflow) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *PreCloseDepositWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type P2PCollectWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_CollectAmountLimit        *money.Money
	_CollectExpirationDuration time.Duration
}

func (obj *P2PCollectWorkflow) CollectAmountLimit() *money.Money {
	return obj._CollectAmountLimit
}
func (obj *P2PCollectWorkflow) CollectExpirationDuration() time.Duration {
	return obj._CollectExpirationDuration
}

type B2CFundTransferWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Payment         *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *B2CFundTransferWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *B2CFundTransferWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *B2CFundTransferWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type RewardsCreateSdWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Payment         *config.StageProcessingParams
	_Fulfillment     *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *RewardsCreateSdWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RewardsCreateSdWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *RewardsCreateSdWorkflow) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *RewardsCreateSdWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type RewardsAddFundsSdWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Payment         *config.StageProcessingParams
	_Fulfillment     *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *RewardsAddFundsSdWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RewardsAddFundsSdWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *RewardsAddFundsSdWorkflow) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *RewardsAddFundsSdWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type AddFundsSdWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Payment         *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *AddFundsSdWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AddFundsSdWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *AddFundsSdWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type AddFundsWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Payment         *config.StageProcessingParams
	_Settlement      *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *AddFundsWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AddFundsWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *AddFundsWorkflow) Settlement() *config.StageProcessingParams {
	return obj._Settlement
}
func (obj *AddFundsWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type AddFundsCollectWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Collect         *config.StageProcessingParams
	_Payment         *config.StageProcessingParams
	_Settlement      *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *AddFundsCollectWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AddFundsCollectWorkflow) Collect() *config.StageProcessingParams {
	return obj._Collect
}
func (obj *AddFundsCollectWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *AddFundsCollectWorkflow) Settlement() *config.StageProcessingParams {
	return obj._Settlement
}
func (obj *AddFundsCollectWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type RecurringPaymentCreationWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Fulfillment     *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *RecurringPaymentCreationWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RecurringPaymentCreationWorkflow) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *RecurringPaymentCreationWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type RecurringPaymentExecutionWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Payment         *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *RecurringPaymentExecutionWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RecurringPaymentExecutionWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *RecurringPaymentExecutionWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type RecurringPaymentModifyWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Fulfillment     *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *RecurringPaymentModifyWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RecurringPaymentModifyWorkflow) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *RecurringPaymentModifyWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type RecurringPaymentRevokeWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Fulfillment     *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *RecurringPaymentRevokeWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RecurringPaymentRevokeWorkflow) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *RecurringPaymentRevokeWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type P2PInvestmentWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Payment         *config.StageProcessingParams
	_Settlement      *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *P2PInvestmentWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *P2PInvestmentWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *P2PInvestmentWorkflow) Settlement() *config.StageProcessingParams {
	return obj._Settlement
}
func (obj *P2PInvestmentWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type P2PWithdrawalWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Fulfillment     *config.StageProcessingParams
	_Payment         *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *P2PWithdrawalWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *P2PWithdrawalWorkflow) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *P2PWithdrawalWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *P2PWithdrawalWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type MutualFundsInvestmentPostPaid struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Fulfillment     *config.StageProcessingParams
	_Payment         *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *MutualFundsInvestmentPostPaid) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *MutualFundsInvestmentPostPaid) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *MutualFundsInvestmentPostPaid) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *MutualFundsInvestmentPostPaid) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type MutualFundsRedemption struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Fulfillment     *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *MutualFundsRedemption) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *MutualFundsRedemption) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *MutualFundsRedemption) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type PaymentNotificationParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_HistoricPaymentSupportedTill time.Duration
	_Credit                       *config.NotificationTemplateParams
	_Debit                        *config.NotificationTemplateParams
	_CreditUPI                    *config.NotificationTemplateParams
	_DebitUPI                     *config.NotificationTemplateParams
	_CollectReceived              *config.NotificationTemplateParams
	_CollectReceivedInApp         *config.NotificationTemplateParams
	_DepositCreationFailed        *config.NotificationTemplateParams
	_DepositSdAddFundsFailed      *config.NotificationTemplateParams
	_TransactionFailed            *config.NotificationTemplateParams
	_ClosedDeposit                *config.NotificationTemplateParams
	_MaturedDeposit               *config.NotificationTemplateParams
	_CreditDeposit                *config.NotificationTemplateParams
	_CollectRequestCancelled      *config.NotificationTemplateParams
	_CreditInterest               *config.NotificationTemplateParams
	_TodChargesDebit              *config.NotificationTemplateParams
	_TransactionReversed          *config.NotificationTemplateParams
	_EcsReturnCharges             *config.NotificationTemplateParams
	_AtmDeclineFee                *config.NotificationTemplateParams
	_DuplicateCardFee             *config.NotificationTemplateParams
	_AtmPenalty                   *config.NotificationTemplateParams
	_IntATMcharges                *config.NotificationTemplateParams
	_OtherBankAtmTxnCharge        *config.NotificationTemplateParams
	_SkipFITOrders                bool
	_SkipMFOrders                 bool
	_EcsReturnChargesInApp        *config.NotificationTemplateParams
	_DcForexRefundTxn             *config.NotificationTemplateParams
	_ChequeCreditNotification     *config.NotificationTemplateParams
	_AddFundsSecondLegFailure     *config.NotificationTemplateParams
}

func (obj *PaymentNotificationParams) HistoricPaymentSupportedTill() time.Duration {
	return obj._HistoricPaymentSupportedTill
}
func (obj *PaymentNotificationParams) Credit() *config.NotificationTemplateParams {
	return obj._Credit
}
func (obj *PaymentNotificationParams) Debit() *config.NotificationTemplateParams {
	return obj._Debit
}
func (obj *PaymentNotificationParams) CreditUPI() *config.NotificationTemplateParams {
	return obj._CreditUPI
}
func (obj *PaymentNotificationParams) DebitUPI() *config.NotificationTemplateParams {
	return obj._DebitUPI
}
func (obj *PaymentNotificationParams) CollectReceived() *config.NotificationTemplateParams {
	return obj._CollectReceived
}
func (obj *PaymentNotificationParams) CollectReceivedInApp() *config.NotificationTemplateParams {
	return obj._CollectReceivedInApp
}
func (obj *PaymentNotificationParams) DepositCreationFailed() *config.NotificationTemplateParams {
	return obj._DepositCreationFailed
}
func (obj *PaymentNotificationParams) DepositSdAddFundsFailed() *config.NotificationTemplateParams {
	return obj._DepositSdAddFundsFailed
}
func (obj *PaymentNotificationParams) TransactionFailed() *config.NotificationTemplateParams {
	return obj._TransactionFailed
}
func (obj *PaymentNotificationParams) ClosedDeposit() *config.NotificationTemplateParams {
	return obj._ClosedDeposit
}
func (obj *PaymentNotificationParams) MaturedDeposit() *config.NotificationTemplateParams {
	return obj._MaturedDeposit
}
func (obj *PaymentNotificationParams) CreditDeposit() *config.NotificationTemplateParams {
	return obj._CreditDeposit
}
func (obj *PaymentNotificationParams) CollectRequestCancelled() *config.NotificationTemplateParams {
	return obj._CollectRequestCancelled
}
func (obj *PaymentNotificationParams) CreditInterest() *config.NotificationTemplateParams {
	return obj._CreditInterest
}
func (obj *PaymentNotificationParams) TodChargesDebit() *config.NotificationTemplateParams {
	return obj._TodChargesDebit
}
func (obj *PaymentNotificationParams) TransactionReversed() *config.NotificationTemplateParams {
	return obj._TransactionReversed
}
func (obj *PaymentNotificationParams) EcsReturnCharges() *config.NotificationTemplateParams {
	return obj._EcsReturnCharges
}
func (obj *PaymentNotificationParams) AtmDeclineFee() *config.NotificationTemplateParams {
	return obj._AtmDeclineFee
}
func (obj *PaymentNotificationParams) DuplicateCardFee() *config.NotificationTemplateParams {
	return obj._DuplicateCardFee
}
func (obj *PaymentNotificationParams) AtmPenalty() *config.NotificationTemplateParams {
	return obj._AtmPenalty
}
func (obj *PaymentNotificationParams) IntATMcharges() *config.NotificationTemplateParams {
	return obj._IntATMcharges
}
func (obj *PaymentNotificationParams) OtherBankAtmTxnCharge() *config.NotificationTemplateParams {
	return obj._OtherBankAtmTxnCharge
}
func (obj *PaymentNotificationParams) SkipFITOrders() bool {
	return obj._SkipFITOrders
}
func (obj *PaymentNotificationParams) SkipMFOrders() bool {
	return obj._SkipMFOrders
}
func (obj *PaymentNotificationParams) EcsReturnChargesInApp() *config.NotificationTemplateParams {
	return obj._EcsReturnChargesInApp
}
func (obj *PaymentNotificationParams) DcForexRefundTxn() *config.NotificationTemplateParams {
	return obj._DcForexRefundTxn
}
func (obj *PaymentNotificationParams) ChequeCreditNotification() *config.NotificationTemplateParams {
	return obj._ChequeCreditNotification
}
func (obj *PaymentNotificationParams) AddFundsSecondLegFailure() *config.NotificationTemplateParams {
	return obj._AddFundsSecondLegFailure
}

type NotificationTemplateParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Disable            bool
	_Title              string
	_Body               string
	_BgColour           string
	_IconAttr           *config.IconAttribute
	_NotificationExpiry time.Duration
	_NotificationType   config.NotificationType
	_TriggerAfter       time.Duration
	_ValidFrom          int32
	_ValidTill          int32
}

func (obj *NotificationTemplateParams) Disable() bool {
	return obj._Disable
}
func (obj *NotificationTemplateParams) Title() string {
	return obj._Title
}
func (obj *NotificationTemplateParams) Body() string {
	return obj._Body
}
func (obj *NotificationTemplateParams) BgColour() string {
	return obj._BgColour
}
func (obj *NotificationTemplateParams) IconAttr() *config.IconAttribute {
	return obj._IconAttr
}
func (obj *NotificationTemplateParams) NotificationExpiry() time.Duration {
	return obj._NotificationExpiry
}
func (obj *NotificationTemplateParams) NotificationType() config.NotificationType {
	return obj._NotificationType
}
func (obj *NotificationTemplateParams) TriggerAfter() time.Duration {
	return obj._TriggerAfter
}
func (obj *NotificationTemplateParams) ValidFrom() int32 {
	return obj._ValidFrom
}
func (obj *NotificationTemplateParams) ValidTill() int32 {
	return obj._ValidTill
}

type IconAttribute struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IconName   string
	_IconURL    string
	_ColourCode string
}

func (obj *IconAttribute) IconName() string {
	return obj._IconName
}
func (obj *IconAttribute) IconURL() string {
	return obj._IconURL
}
func (obj *IconAttribute) ColourCode() string {
	return obj._ColourCode
}

type PaymentSMSParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_HistoricPaymentSupportedTill time.Duration
	_Credit                       *config.SmsTemplateParams
	_Debit                        *config.SmsTemplateParams
	_CreditUPI                    *config.SmsTemplateParams
	_DebitUPI                     *config.SmsTemplateParams
	_CollectReceived              *config.SmsTemplateParams
	_TransactionFailed            *config.SmsTemplateParams
	_DebitGenericPI               *config.SmsTemplateParams
	_CreditGenericPI              *config.SmsTemplateParams
	_SkipFITOrders                bool
	_SkipDebitCardDuplicateFees   bool
}

func (obj *PaymentSMSParams) HistoricPaymentSupportedTill() time.Duration {
	return obj._HistoricPaymentSupportedTill
}
func (obj *PaymentSMSParams) Credit() *config.SmsTemplateParams {
	return obj._Credit
}
func (obj *PaymentSMSParams) Debit() *config.SmsTemplateParams {
	return obj._Debit
}
func (obj *PaymentSMSParams) CreditUPI() *config.SmsTemplateParams {
	return obj._CreditUPI
}
func (obj *PaymentSMSParams) DebitUPI() *config.SmsTemplateParams {
	return obj._DebitUPI
}
func (obj *PaymentSMSParams) CollectReceived() *config.SmsTemplateParams {
	return obj._CollectReceived
}
func (obj *PaymentSMSParams) TransactionFailed() *config.SmsTemplateParams {
	return obj._TransactionFailed
}
func (obj *PaymentSMSParams) DebitGenericPI() *config.SmsTemplateParams {
	return obj._DebitGenericPI
}
func (obj *PaymentSMSParams) CreditGenericPI() *config.SmsTemplateParams {
	return obj._CreditGenericPI
}
func (obj *PaymentSMSParams) SkipFITOrders() bool {
	return obj._SkipFITOrders
}
func (obj *PaymentSMSParams) SkipDebitCardDuplicateFees() bool {
	return obj._SkipDebitCardDuplicateFees
}

type SmsTemplateParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Disable      bool
	_Body         string
	_TriggerAfter time.Duration
}

func (obj *SmsTemplateParams) Disable() bool {
	return obj._Disable
}
func (obj *SmsTemplateParams) Body() string {
	return obj._Body
}
func (obj *SmsTemplateParams) TriggerAfter() time.Duration {
	return obj._TriggerAfter
}

type IconUrls struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_FDIconUrl            string
	_SDIconUrl            string
	_ATMWithdrawalIconUrl string
	_FiBankIconUrl        string
	_PendingClockIconUrl  string
	_SuccessStatusIconUrl string
	_FailedStatusIconUrl  string
	_PendingStatusIconUrl string
}

func (obj *IconUrls) FDIconUrl() string {
	return obj._FDIconUrl
}
func (obj *IconUrls) SDIconUrl() string {
	return obj._SDIconUrl
}
func (obj *IconUrls) ATMWithdrawalIconUrl() string {
	return obj._ATMWithdrawalIconUrl
}
func (obj *IconUrls) FiBankIconUrl() string {
	return obj._FiBankIconUrl
}
func (obj *IconUrls) PendingClockIconUrl() string {
	return obj._PendingClockIconUrl
}
func (obj *IconUrls) SuccessStatusIconUrl() string {
	return obj._SuccessStatusIconUrl
}
func (obj *IconUrls) FailedStatusIconUrl() string {
	return obj._FailedStatusIconUrl
}
func (obj *IconUrls) PendingStatusIconUrl() string {
	return obj._PendingStatusIconUrl
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// A flag to determine if the debug message in Status is to be trimmed
	_TrimDebugMessageFromStatus uint32
	// flag to determine if we want to skip upi debit transactions
	_SkipSmsForUpiDebitTransactions uint32
	// flag to enable evaluation of GamNotification ( BalanceV1 API)
	_EnableBalanceV1Evaluation uint32
}

// A flag to determine if the debug message in Status is to be trimmed
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	if atomic.LoadUint32(&obj._TrimDebugMessageFromStatus) == 0 {
		return false
	} else {
		return true
	}
}

// flag to determine if we want to skip upi debit transactions
func (obj *Flags) SkipSmsForUpiDebitTransactions() bool {
	if atomic.LoadUint32(&obj._SkipSmsForUpiDebitTransactions) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable evaluation of GamNotification ( BalanceV1 API)
func (obj *Flags) EnableBalanceV1Evaluation() bool {
	if atomic.LoadUint32(&obj._EnableBalanceV1Evaluation) == 0 {
		return false
	} else {
		return true
	}
}

type CustomRuleDEParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCustomRuleDERestricted  bool
	_AllowedUserGrpForCustomDE []common.UserGroup
}

func (obj *CustomRuleDEParams) IsCustomRuleDERestricted() bool {
	return obj._IsCustomRuleDERestricted
}
func (obj *CustomRuleDEParams) AllowedUserGrpForCustomDE() []common.UserGroup {
	return obj._AllowedUserGrpForCustomDE
}

type RudderBroker struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Host          string
	_Key           string
	_IntervalInSec time.Duration
	_BatchSize     int
	_Verbose       bool
}

func (obj *RudderBroker) Host() string {
	return obj._Host
}
func (obj *RudderBroker) Key() string {
	return obj._Key
}
func (obj *RudderBroker) IntervalInSec() time.Duration {
	return obj._IntervalInSec
}
func (obj *RudderBroker) BatchSize() int {
	return obj._BatchSize
}
func (obj *RudderBroker) Verbose() bool {
	return obj._Verbose
}

type URNPaymentLimits struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_P2PQRCodeLimit               *config.QRCodeLimit
	_P2MOnlineQRCodeLimit         *config.QRCodeLimit
	_P2MOfflineQRCodeLimit        *config.QRCodeLimit
	_P2PQRShareAndPayLimit        *config.QRCodeLimit
	_P2MOnlineQRShareAndPayLimit  *config.QRCodeLimitForMerchants
	_P2MOfflineQRShareAndPayLimit *config.QRCodeLimitForMerchants
	_P2PIntentLimit               *config.IntentLimit
	_P2MOnlineIntentLimit         *config.IntentLimit
	_P2MOfflineIntentLimit        *config.IntentLimit
}

func (obj *URNPaymentLimits) P2PQRCodeLimit() *config.QRCodeLimit {
	return obj._P2PQRCodeLimit
}
func (obj *URNPaymentLimits) P2MOnlineQRCodeLimit() *config.QRCodeLimit {
	return obj._P2MOnlineQRCodeLimit
}
func (obj *URNPaymentLimits) P2MOfflineQRCodeLimit() *config.QRCodeLimit {
	return obj._P2MOfflineQRCodeLimit
}
func (obj *URNPaymentLimits) P2PQRShareAndPayLimit() *config.QRCodeLimit {
	return obj._P2PQRShareAndPayLimit
}
func (obj *URNPaymentLimits) P2MOnlineQRShareAndPayLimit() *config.QRCodeLimitForMerchants {
	return obj._P2MOnlineQRShareAndPayLimit
}
func (obj *URNPaymentLimits) P2MOfflineQRShareAndPayLimit() *config.QRCodeLimitForMerchants {
	return obj._P2MOfflineQRShareAndPayLimit
}
func (obj *URNPaymentLimits) P2PIntentLimit() *config.IntentLimit {
	return obj._P2PIntentLimit
}
func (obj *URNPaymentLimits) P2MOnlineIntentLimit() *config.IntentLimit {
	return obj._P2MOnlineIntentLimit
}
func (obj *URNPaymentLimits) P2MOfflineIntentLimit() *config.IntentLimit {
	return obj._P2MOfflineIntentLimit
}

type QRCodeLimit struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_InsecureQRCodeLimit *config.QRCode
	_SecureQRCodeLimit   *config.QRCode
}

func (obj *QRCodeLimit) InsecureQRCodeLimit() *config.QRCode {
	return obj._InsecureQRCodeLimit
}
func (obj *QRCodeLimit) SecureQRCodeLimit() *config.QRCode {
	return obj._SecureQRCodeLimit
}

type QRCode struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DynamicQRLimit *money.Money
	_StaticQRLimit  *money.Money
}

func (obj *QRCode) DynamicQRLimit() *money.Money {
	return obj._DynamicQRLimit
}
func (obj *QRCode) StaticQRLimit() *money.Money {
	return obj._StaticQRLimit
}

type QRCodeLimitForMerchants struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_InsecureQRCodeLimit *config.MerchantQRCode
	_SecureQRCodeLimit   *config.MerchantQRCode
}

func (obj *QRCodeLimitForMerchants) InsecureQRCodeLimit() *config.MerchantQRCode {
	return obj._InsecureQRCodeLimit
}
func (obj *QRCodeLimitForMerchants) SecureQRCodeLimit() *config.MerchantQRCode {
	return obj._SecureQRCodeLimit
}

type MerchantQRCode struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_VerifiedMerchant    *config.QRCode
	_NonVerifiedMerchant *config.QRCode
}

func (obj *MerchantQRCode) VerifiedMerchant() *config.QRCode {
	return obj._VerifiedMerchant
}
func (obj *MerchantQRCode) NonVerifiedMerchant() *config.QRCode {
	return obj._NonVerifiedMerchant
}

type IntentLimit struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_InsecureIntent *money.Money
	_SecureIntent   *money.Money
}

func (obj *IntentLimit) InsecureIntent() *money.Money {
	return obj._InsecureIntent
}
func (obj *IntentLimit) SecureIntent() *money.Money {
	return obj._SecureIntent
}

type AddFundsAlertParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_StartAfter               time.Duration
	_EndAfter                 time.Duration
	_DistributedLockKeyPrefix string
	_DistributedLockDuration  time.Duration
}

func (obj *AddFundsAlertParams) StartAfter() time.Duration {
	return obj._StartAfter
}
func (obj *AddFundsAlertParams) EndAfter() time.Duration {
	return obj._EndAfter
}
func (obj *AddFundsAlertParams) DistributedLockKeyPrefix() string {
	return obj._DistributedLockKeyPrefix
}
func (obj *AddFundsAlertParams) DistributedLockDuration() time.Duration {
	return obj._DistributedLockDuration
}

type AddFundsAlertMailingParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled       bool
	_TimestampFormat string
	_FromAddress     string
	_ToAddress       string
	_FromName        string
	_ToName          string
}

func (obj *AddFundsAlertMailingParams) IsEnabled() bool {
	return obj._IsEnabled
}
func (obj *AddFundsAlertMailingParams) TimestampFormat() string {
	return obj._TimestampFormat
}
func (obj *AddFundsAlertMailingParams) FromAddress() string {
	return obj._FromAddress
}
func (obj *AddFundsAlertMailingParams) ToAddress() string {
	return obj._ToAddress
}
func (obj *AddFundsAlertMailingParams) FromName() string {
	return obj._FromName
}
func (obj *AddFundsAlertMailingParams) ToName() string {
	return obj._ToName
}

type ExecuteRecurringPaymentWithAuthWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Payment         *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *ExecuteRecurringPaymentWithAuthWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ExecuteRecurringPaymentWithAuthWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *ExecuteRecurringPaymentWithAuthWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type ExecuteRecurringPaymentWithNoAuthWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Payment         *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *ExecuteRecurringPaymentWithNoAuthWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ExecuteRecurringPaymentWithNoAuthWorkflow) Payment() *config.StageProcessingParams {
	return obj._Payment
}
func (obj *ExecuteRecurringPaymentWithNoAuthWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type RecurringPaymentPauseUnpauseWorkflow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAutoTriggered uint32
	_Fulfillment     *config.StageProcessingParams
	_InitialDelay    *cfg.TimeDuration
}

func (obj *RecurringPaymentPauseUnpauseWorkflow) IsAutoTriggered() bool {
	if atomic.LoadUint32(&obj._IsAutoTriggered) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RecurringPaymentPauseUnpauseWorkflow) Fulfillment() *config.StageProcessingParams {
	return obj._Fulfillment
}
func (obj *RecurringPaymentPauseUnpauseWorkflow) InitialDelay() *cfg.TimeDuration {
	return obj._InitialDelay
}

type EventsConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IncomingCreditMaxPublishDelay time.Duration
}

func (obj *EventsConfig) IncomingCreditMaxPublishDelay() time.Duration {
	return obj._IncomingCreditMaxPublishDelay
}

type CSIS struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCsisEnable bool
	_StartTime    string
	_EndTime      string
}

func (obj *CSIS) IsCsisEnable() bool {
	return obj._IsCsisEnable
}
func (obj *CSIS) StartTime() string {
	return obj._StartTime
}
func (obj *CSIS) EndTime() string {
	return obj._EndTime
}

type BankDownTime struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsBankDownTimeEnable bool
	_StartTime            string
	_EndTime              string
}

func (obj *BankDownTime) IsBankDownTimeEnable() bool {
	return obj._IsBankDownTimeEnable
}
func (obj *BankDownTime) StartTime() string {
	return obj._StartTime
}
func (obj *BankDownTime) EndTime() string {
	return obj._EndTime
}

type SMSOptionVersion struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Default      string
	_InternalUser string
	_FnfUser      string
}

func (obj *SMSOptionVersion) Default() string {
	return obj._Default
}
func (obj *SMSOptionVersion) InternalUser() string {
	return obj._InternalUser
}
func (obj *SMSOptionVersion) FnfUser() string {
	return obj._FnfUser
}

type ConnectedAccountUserGroupParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAARestricted        bool
	_AllowedUserGroupForAA []common.UserGroup
}

func (obj *ConnectedAccountUserGroupParams) IsAARestricted() bool {
	return obj._IsAARestricted
}
func (obj *ConnectedAccountUserGroupParams) AllowedUserGroupForAA() []common.UserGroup {
	return obj._AllowedUserGroupForAA
}

type AaParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// defines the time window to be considered while creating data purging batch
	_PurgingBatchWindow int64
	// lease duration to be for new data sync lock
	_NewDataSyncLeaseDuration int64
	// lease duration to be for data purging lock
	_DataPurgingLeaseDuration int64
	_DataPurgingStartTime     string
	_DataPurgingEndTime       string
	_DataSyncLockPrefix       string
	_PgdbConnAlias            string
}

// defines the time window to be considered while creating data purging batch
func (obj *AaParams) PurgingBatchWindow() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._PurgingBatchWindow))
}

// lease duration to be for new data sync lock
func (obj *AaParams) NewDataSyncLeaseDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._NewDataSyncLeaseDuration))
}

// lease duration to be for data purging lock
func (obj *AaParams) DataPurgingLeaseDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DataPurgingLeaseDuration))
}
func (obj *AaParams) DataPurgingStartTime() string {
	return obj._DataPurgingStartTime
}
func (obj *AaParams) DataPurgingEndTime() string {
	return obj._DataPurgingEndTime
}
func (obj *AaParams) DataSyncLockPrefix() string {
	return obj._DataSyncLockPrefix
}
func (obj *AaParams) PgdbConnAlias() string {
	return obj._PgdbConnAlias
}

type NameCheckParamsForAddFunds struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsNameCheckRestricted        bool
	_AllowedUserGroupForNameCheck []common.UserGroup
}

func (obj *NameCheckParamsForAddFunds) IsNameCheckRestricted() bool {
	return obj._IsNameCheckRestricted
}
func (obj *NameCheckParamsForAddFunds) AllowedUserGroupForNameCheck() []common.UserGroup {
	return obj._AllowedUserGroupForNameCheck
}

type SavingsLedgerReconCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled         bool
	_SavingsLedgerReconPrefix string
	_CacheTTl                 time.Duration
}

func (obj *SavingsLedgerReconCacheConfig) IsCachingEnabled() bool {
	return obj._IsCachingEnabled
}
func (obj *SavingsLedgerReconCacheConfig) SavingsLedgerReconPrefix() string {
	return obj._SavingsLedgerReconPrefix
}
func (obj *SavingsLedgerReconCacheConfig) CacheTTl() time.Duration {
	return obj._CacheTTl
}

type OrderCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// this boolean will turn on/off cache layer
	_IsCachingEnabled uint32
	// UseCaseToCacheConfigMap will have use case mapped to cache config (ie, IsCachingEnabled and CacheTTL)
	_UseCaseToCacheConfigMap *syncmap.Map[string, *CacheConfig]
	_RedisOptions            *cfg.RedisOptions
}

// this boolean will turn on/off cache layer
func (obj *OrderCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// UseCaseToCacheConfigMap will have use case mapped to cache config (ie, IsCachingEnabled and CacheTTL)
func (obj *OrderCacheConfig) UseCaseToCacheConfigMap() *syncmap.Map[string, *CacheConfig] {
	return obj._UseCaseToCacheConfigMap
}
func (obj *OrderCacheConfig) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}

type CacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// switch to enable/disable cache
	_IsCachingEnabled uint32
	// duration for which cache is stored
	_CacheTTL int64
}

// switch to enable/disable cache
func (obj *CacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// duration for which cache is stored
func (obj *CacheConfig) CacheTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTL))
}

type TransactionCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// this boolean will turn on/off cache layer
	_IsCachingEnabled uint32
	// UseCaseToCacheConfigMap will have use case mapped to cache config (ie, IsCachingEnabled and CacheTTL)
	_UseCaseToCacheConfigMap *syncmap.Map[string, *CacheConfig]
	_RedisOptions            *cfg.RedisOptions
}

// this boolean will turn on/off cache layer
func (obj *TransactionCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// UseCaseToCacheConfigMap will have use case mapped to cache config (ie, IsCachingEnabled and CacheTTL)
func (obj *TransactionCacheConfig) UseCaseToCacheConfigMap() *syncmap.Map[string, *CacheConfig] {
	return obj._UseCaseToCacheConfigMap
}
func (obj *TransactionCacheConfig) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}

type ReconParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_TimeLimitForExhaustedAttempt int64
	_IsReconRestricted            bool
	_AllowedUserGroupForRecon     []common.UserGroup
}

func (obj *ReconParams) TimeLimitForExhaustedAttempt() int64 {
	return obj._TimeLimitForExhaustedAttempt
}
func (obj *ReconParams) IsReconRestricted() bool {
	return obj._IsReconRestricted
}
func (obj *ReconParams) AllowedUserGroupForRecon() []common.UserGroup {
	return obj._AllowedUserGroupForRecon
}

type FeatureFlags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableHandleAddFundsWithoutFirstLegTxn uint32
	// IncludePartnerRefIdToDedupeTxnInRecon to check if we include partner_ref_id to dedupe txn or not
	_IncludePartnerRefIdToDedupeTxnInRecon uint32
	// Flag to disable comms via P2P fund transfer consumer as notifications would be initiated as part of celestial workflow
	_DisableCommsForP2PFundTransfer uint32
	_EnablePGDBDaoForAA             uint32
	// EnableOffAppUpiFlow is a dynamic flag which can be used downtime as well as to go via the fallback route instantly
	_EnableOffAppUpiFlow uint32
	// Flag to enable redirection to amount screen from collect request PNs
	_RedirectToAmountScreenFromCollectPN uint32
	// Flag to enable 1% cashback banner for DC AMC charge receipt
	_EnableDcAmcChargesCashbackBanner                           uint32
	_PercentageRollOutForTimelineOptimisation                   *genconfig.StickinessConstraintConfig
	_EnableOptimisationForGetTotalTransactionCountParams        *gencfg.FeatureReleaseConfig
	_EnableFallbackForGetTotalTransactionCountToCrdb            *gencfg.FeatureReleaseConfig
	_EnableOptimisationForGetTotalTransactionAmountParams       *gencfg.FeatureReleaseConfig
	_EnableFallbackForGetTotalTransactionAmountToCrdb           *gencfg.FeatureReleaseConfig
	_EnableParameterizedTimelineEvents                          *gencfg.FeatureReleaseConfig
	_BypassTPAPCoolDown                                         *gencfg.FeatureReleaseConfig
	_EnableAllTransactionsForNoOpFailures                       *gencfg.FeatureReleaseConfig
	_DedupeByDedupeIdEnable                                     bool
	_EnableB2CCallbackSignalToCelestial                         bool
	_EnableOnAppEnachExecutionInboundNotifProcessor             bool
	_EnableRemitterInfo                                         *config.EnableRemitterInfo
	_AllowedUserGroupForTimelineOptimization                    []common.UserGroup
	_AllowedUserGroupForSuccessfulOrderWithTxnFetchOptimization []common.UserGroup
	_EnableTotalAmountUserCheck                                 *cfg.FeatureReleaseConfig
	_EnableBypassCooldownForWhitelistedUser                     *cfg.FeatureReleaseConfig
	_EnableAllTransactionForSelectedOrderStatesParams           *cfg.FeatureReleaseConfig
	_EnableRecentActivityForDifferentTxns                       *cfg.FeatureReleaseConfig
	_EnableDbOptimisationForAllTransactionsFlow                 *cfg.FeatureReleaseConfig
}

func (obj *FeatureFlags) EnableHandleAddFundsWithoutFirstLegTxn() bool {
	if atomic.LoadUint32(&obj._EnableHandleAddFundsWithoutFirstLegTxn) == 0 {
		return false
	} else {
		return true
	}
}

// IncludePartnerRefIdToDedupeTxnInRecon to check if we include partner_ref_id to dedupe txn or not
func (obj *FeatureFlags) IncludePartnerRefIdToDedupeTxnInRecon() bool {
	if atomic.LoadUint32(&obj._IncludePartnerRefIdToDedupeTxnInRecon) == 0 {
		return false
	} else {
		return true
	}
}

// Flag to disable comms via P2P fund transfer consumer as notifications would be initiated as part of celestial workflow
func (obj *FeatureFlags) DisableCommsForP2PFundTransfer() bool {
	if atomic.LoadUint32(&obj._DisableCommsForP2PFundTransfer) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureFlags) EnablePGDBDaoForAA() bool {
	if atomic.LoadUint32(&obj._EnablePGDBDaoForAA) == 0 {
		return false
	} else {
		return true
	}
}

// EnableOffAppUpiFlow is a dynamic flag which can be used downtime as well as to go via the fallback route instantly
func (obj *FeatureFlags) EnableOffAppUpiFlow() bool {
	if atomic.LoadUint32(&obj._EnableOffAppUpiFlow) == 0 {
		return false
	} else {
		return true
	}
}

// Flag to enable redirection to amount screen from collect request PNs
func (obj *FeatureFlags) RedirectToAmountScreenFromCollectPN() bool {
	if atomic.LoadUint32(&obj._RedirectToAmountScreenFromCollectPN) == 0 {
		return false
	} else {
		return true
	}
}

// Flag to enable 1% cashback banner for DC AMC charge receipt
func (obj *FeatureFlags) EnableDcAmcChargesCashbackBanner() bool {
	if atomic.LoadUint32(&obj._EnableDcAmcChargesCashbackBanner) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureFlags) PercentageRollOutForTimelineOptimisation() *genconfig.StickinessConstraintConfig {
	return obj._PercentageRollOutForTimelineOptimisation
}
func (obj *FeatureFlags) EnableOptimisationForGetTotalTransactionCountParams() *gencfg.FeatureReleaseConfig {
	return obj._EnableOptimisationForGetTotalTransactionCountParams
}
func (obj *FeatureFlags) EnableFallbackForGetTotalTransactionCountToCrdb() *gencfg.FeatureReleaseConfig {
	return obj._EnableFallbackForGetTotalTransactionCountToCrdb
}
func (obj *FeatureFlags) EnableOptimisationForGetTotalTransactionAmountParams() *gencfg.FeatureReleaseConfig {
	return obj._EnableOptimisationForGetTotalTransactionAmountParams
}
func (obj *FeatureFlags) EnableFallbackForGetTotalTransactionAmountToCrdb() *gencfg.FeatureReleaseConfig {
	return obj._EnableFallbackForGetTotalTransactionAmountToCrdb
}
func (obj *FeatureFlags) EnableParameterizedTimelineEvents() *gencfg.FeatureReleaseConfig {
	return obj._EnableParameterizedTimelineEvents
}
func (obj *FeatureFlags) BypassTPAPCoolDown() *gencfg.FeatureReleaseConfig {
	return obj._BypassTPAPCoolDown
}
func (obj *FeatureFlags) EnableAllTransactionsForNoOpFailures() *gencfg.FeatureReleaseConfig {
	return obj._EnableAllTransactionsForNoOpFailures
}
func (obj *FeatureFlags) DedupeByDedupeIdEnable() bool {
	return obj._DedupeByDedupeIdEnable
}
func (obj *FeatureFlags) EnableB2CCallbackSignalToCelestial() bool {
	return obj._EnableB2CCallbackSignalToCelestial
}
func (obj *FeatureFlags) EnableOnAppEnachExecutionInboundNotifProcessor() bool {
	return obj._EnableOnAppEnachExecutionInboundNotifProcessor
}
func (obj *FeatureFlags) EnableRemitterInfo() *config.EnableRemitterInfo {
	return obj._EnableRemitterInfo
}
func (obj *FeatureFlags) AllowedUserGroupForTimelineOptimization() []common.UserGroup {
	return obj._AllowedUserGroupForTimelineOptimization
}
func (obj *FeatureFlags) AllowedUserGroupForSuccessfulOrderWithTxnFetchOptimization() []common.UserGroup {
	return obj._AllowedUserGroupForSuccessfulOrderWithTxnFetchOptimization
}
func (obj *FeatureFlags) EnableTotalAmountUserCheck() *cfg.FeatureReleaseConfig {
	return obj._EnableTotalAmountUserCheck
}
func (obj *FeatureFlags) EnableBypassCooldownForWhitelistedUser() *cfg.FeatureReleaseConfig {
	return obj._EnableBypassCooldownForWhitelistedUser
}
func (obj *FeatureFlags) EnableAllTransactionForSelectedOrderStatesParams() *cfg.FeatureReleaseConfig {
	return obj._EnableAllTransactionForSelectedOrderStatesParams
}
func (obj *FeatureFlags) EnableRecentActivityForDifferentTxns() *cfg.FeatureReleaseConfig {
	return obj._EnableRecentActivityForDifferentTxns
}
func (obj *FeatureFlags) EnableDbOptimisationForAllTransactionsFlow() *cfg.FeatureReleaseConfig {
	return obj._EnableDbOptimisationForAllTransactionsFlow
}

type EnableRemitterInfo struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsRestricted                     bool
	_AllowedUserGroupsForRemitterInfo []common.UserGroup
}

func (obj *EnableRemitterInfo) IsRestricted() bool {
	return obj._IsRestricted
}
func (obj *EnableRemitterInfo) AllowedUserGroupsForRemitterInfo() []common.UserGroup {
	return obj._AllowedUserGroupsForRemitterInfo
}

type DelayRangeForBalanceV1 struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MinimumDelay time.Duration
	_MaximumDelay time.Duration
}

func (obj *DelayRangeForBalanceV1) MinimumDelay() time.Duration {
	return obj._MinimumDelay
}
func (obj *DelayRangeForBalanceV1) MaximumDelay() time.Duration {
	return obj._MaximumDelay
}

type InboundNotificationParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_BalanceRefreshDelay time.Duration
}

func (obj *InboundNotificationParams) BalanceRefreshDelay() time.Duration {
	return obj._BalanceRefreshDelay
}

type ErrorRespCodesForPermanentFailure struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_FundTransferCallBackErrRespCodesForVendorMap        map[string]*config.FundTransferCallBackErrRespCodesForPermanentFailure
	_B2CFundTransferPermanentFailureRespCodesToVendorMap map[string]*config.B2CFundTransferPermanentFailureRespCodes
}

func (obj *ErrorRespCodesForPermanentFailure) FundTransferCallBackErrRespCodesForVendorMap() map[string]*config.FundTransferCallBackErrRespCodesForPermanentFailure {
	return obj._FundTransferCallBackErrRespCodesForVendorMap
}
func (obj *ErrorRespCodesForPermanentFailure) B2CFundTransferPermanentFailureRespCodesToVendorMap() map[string]*config.B2CFundTransferPermanentFailureRespCodes {
	return obj._B2CFundTransferPermanentFailureRespCodesToVendorMap
}

type B2CPaymentParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ProcessB2cPaymentLock              string
	_ProcessB2cPaymentLockLeaseDuration time.Duration
}

func (obj *B2CPaymentParams) ProcessB2cPaymentLock() string {
	return obj._ProcessB2cPaymentLock
}
func (obj *B2CPaymentParams) ProcessB2cPaymentLockLeaseDuration() time.Duration {
	return obj._ProcessB2cPaymentLockLeaseDuration
}

type DcForexRefundInfo struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_RefundInProgressLayout          string
	_RefundUnderReviewLayout         string
	_RefundCompletedLayout           string
	_RefundCompletedWithoutInfo      string
	_IncomingRefundLayout            string
	_IncomingRefundLayoutWithoutInfo string
}

func (obj *DcForexRefundInfo) RefundInProgressLayout() string {
	return obj._RefundInProgressLayout
}
func (obj *DcForexRefundInfo) RefundUnderReviewLayout() string {
	return obj._RefundUnderReviewLayout
}
func (obj *DcForexRefundInfo) RefundCompletedLayout() string {
	return obj._RefundCompletedLayout
}
func (obj *DcForexRefundInfo) RefundCompletedWithoutInfo() string {
	return obj._RefundCompletedWithoutInfo
}
func (obj *DcForexRefundInfo) IncomingRefundLayout() string {
	return obj._IncomingRefundLayout
}
func (obj *DcForexRefundInfo) IncomingRefundLayoutWithoutInfo() string {
	return obj._IncomingRefundLayoutWithoutInfo
}

type CommsConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ExcludeUsersForEmail map[string]bool
}

func (obj *CommsConfig) ExcludeUsersForEmail() map[string]bool {
	return obj._ExcludeUsersForEmail
}

type ReportFraudConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_TransactionProtocolToMaxDaysAvailableForReportFraud map[string]float64
}

func (obj *ReportFraudConfig) TransactionProtocolToMaxDaysAvailableForReportFraud() map[string]float64 {
	return obj._TransactionProtocolToMaxDaysAvailableForReportFraud
}

type RewardsInfo struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DpandaFiStoreOrderHistoryTargetUrl              string
	_DpandaFiStoreOrderHistoryTargetUrlMutex         *sync.RWMutex
	_PoshvineGiftCardStoreOrderHistoryTargetUrl      string
	_PoshvineGiftCardStoreOrderHistoryTargetUrlMutex *sync.RWMutex
}

func (obj *RewardsInfo) DpandaFiStoreOrderHistoryTargetUrl() string {
	obj._DpandaFiStoreOrderHistoryTargetUrlMutex.RLock()
	defer obj._DpandaFiStoreOrderHistoryTargetUrlMutex.RUnlock()
	return obj._DpandaFiStoreOrderHistoryTargetUrl
}
func (obj *RewardsInfo) PoshvineGiftCardStoreOrderHistoryTargetUrl() string {
	obj._PoshvineGiftCardStoreOrderHistoryTargetUrlMutex.RLock()
	defer obj._PoshvineGiftCardStoreOrderHistoryTargetUrlMutex.RUnlock()
	return obj._PoshvineGiftCardStoreOrderHistoryTargetUrl
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["aaenrichedtxnupdatebatchsize"] = _obj.SetAaEnrichedTxnUpdateBatchSize
	_setters["maximumpausetimeforremitterinfoworkflow"] = _obj.SetMaximumPauseTimeForRemitterInfoWorkflow
	_setters["hideaccountbalanceinnotification"] = _obj.SetHideAccountBalanceInNotification
	_setters["ispushingpaymentsmetricstohealthengineenabled"] = _obj.SetIsPushingPaymentsMetricsToHealthEngineEnabled
	_setters["isemailnotificationenable"] = _obj.SetIsEmailNotificationEnable
	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_setters["recentordereligibleduration"] = _obj.SetRecentOrderEligibleDuration
	_setters["timeoutforgetoperationalstatus"] = _obj.SetTimeoutForGetOperationalStatus
	_setters["pinotquerytimeout"] = _obj.SetPinotQueryTimeout

	_obj._SMSTypeToOptionVersionMap = &syncmap.Map[string, *config.SMSOptionVersion]{}
	_setters["smstypetooptionversionmap"] = _obj.SetSMSTypeToOptionVersionMap
	_PaymentOrchestrationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PaymentOrchestrationSubscriber = _PaymentOrchestrationSubscriber
	helper.AddFieldSetters("paymentorchestrationsubscriber", _fieldSetters, _setters)
	_IntraBankEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._IntraBankEnquirySubscriber = _IntraBankEnquirySubscriber
	helper.AddFieldSetters("intrabankenquirysubscriber", _fieldSetters, _setters)
	_UPIEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UPIEnquirySubscriber = _UPIEnquirySubscriber
	helper.AddFieldSetters("upienquirysubscriber", _fieldSetters, _setters)
	_IMPSEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._IMPSEnquirySubscriber = _IMPSEnquirySubscriber
	helper.AddFieldSetters("impsenquirysubscriber", _fieldSetters, _setters)
	_NEFTEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._NEFTEnquirySubscriber = _NEFTEnquirySubscriber
	helper.AddFieldSetters("neftenquirysubscriber", _fieldSetters, _setters)
	_RTGSEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RTGSEnquirySubscriber = _RTGSEnquirySubscriber
	helper.AddFieldSetters("rtgsenquirysubscriber", _fieldSetters, _setters)
	_OrderOrchestrationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderOrchestrationSubscriber = _OrderOrchestrationSubscriber
	helper.AddFieldSetters("orderorchestrationsubscriber", _fieldSetters, _setters)
	_PaymentCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PaymentCallbackSubscriber = _PaymentCallbackSubscriber
	helper.AddFieldSetters("paymentcallbacksubscriber", _fieldSetters, _setters)
	_InboundTxnSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._InboundTxnSubscriber = _InboundTxnSubscriber
	helper.AddFieldSetters("inboundtxnsubscriber", _fieldSetters, _setters)
	_InboundUpiTxnSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._InboundUpiTxnSubscriber = _InboundUpiTxnSubscriber
	helper.AddFieldSetters("inboundupitxnsubscriber", _fieldSetters, _setters)
	_OrderUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderUpdateSubscriber = _OrderUpdateSubscriber
	helper.AddFieldSetters("orderupdatesubscriber", _fieldSetters, _setters)
	_OrderCollectNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderCollectNotificationSubscriber = _OrderCollectNotificationSubscriber
	helper.AddFieldSetters("ordercollectnotificationsubscriber", _fieldSetters, _setters)
	_OrderNotificationFallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderNotificationFallbackSubscriber = _OrderNotificationFallbackSubscriber
	helper.AddFieldSetters("ordernotificationfallbacksubscriber", _fieldSetters, _setters)
	_OrderWorkflowProcessingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderWorkflowProcessingSubscriber = _OrderWorkflowProcessingSubscriber
	helper.AddFieldSetters("orderworkflowprocessingsubscriber", _fieldSetters, _setters)
	_DisputeEventProcessingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DisputeEventProcessingSubscriber = _DisputeEventProcessingSubscriber
	helper.AddFieldSetters("disputeeventprocessingsubscriber", _fieldSetters, _setters)
	_AATxnSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AATxnSubscriber = _AATxnSubscriber
	helper.AddFieldSetters("aatxnsubscriber", _fieldSetters, _setters)
	_PaymentProtocolDecisionParams, _fieldSetters := NewPaymentProtocolDecisionParams()
	_obj._PaymentProtocolDecisionParams = _PaymentProtocolDecisionParams
	helper.AddFieldSetters("paymentprotocoldecisionparams", _fieldSetters, _setters)
	_RuleEngineParams, _fieldSetters := NewRuleEngineParams()
	_obj._RuleEngineParams = _RuleEngineParams
	helper.AddFieldSetters("ruleengineparams", _fieldSetters, _setters)
	_OrderReceipt, _fieldSetters := NewOrderReceipt()
	_obj._OrderReceipt = _OrderReceipt
	helper.AddFieldSetters("orderreceipt", _fieldSetters, _setters)
	_FundTransferWorkflow, _fieldSetters := NewFundTransferWorkflow()
	_obj._FundTransferWorkflow = _FundTransferWorkflow
	helper.AddFieldSetters("fundtransferworkflow", _fieldSetters, _setters)
	_CollectShortCircuitWorkflow, _fieldSetters := NewCollectShortCircuitWorkflow()
	_obj._CollectShortCircuitWorkflow = _CollectShortCircuitWorkflow
	helper.AddFieldSetters("collectshortcircuitworkflow", _fieldSetters, _setters)
	_DepositCreationWorkflow, _fieldSetters := NewDepositCreationWorkflow()
	_obj._DepositCreationWorkflow = _DepositCreationWorkflow
	helper.AddFieldSetters("depositcreationworkflow", _fieldSetters, _setters)
	_UrnTransferWorkflow, _fieldSetters := NewUrnTransferWorkflow()
	_obj._UrnTransferWorkflow = _UrnTransferWorkflow
	helper.AddFieldSetters("urntransferworkflow", _fieldSetters, _setters)
	_PreCloseDepositWorkflow, _fieldSetters := NewPreCloseDepositWorkflow()
	_obj._PreCloseDepositWorkflow = _PreCloseDepositWorkflow
	helper.AddFieldSetters("preclosedepositworkflow", _fieldSetters, _setters)
	_P2PCollectWorkflow, _fieldSetters := NewP2PCollectWorkflow()
	_obj._P2PCollectWorkflow = _P2PCollectWorkflow
	helper.AddFieldSetters("p2pcollectworkflow", _fieldSetters, _setters)
	_B2CFundTransferWorkflow, _fieldSetters := NewB2CFundTransferWorkflow()
	_obj._B2CFundTransferWorkflow = _B2CFundTransferWorkflow
	helper.AddFieldSetters("b2cfundtransferworkflow", _fieldSetters, _setters)
	_RewardsCreateSdWorkflow, _fieldSetters := NewRewardsCreateSdWorkflow()
	_obj._RewardsCreateSdWorkflow = _RewardsCreateSdWorkflow
	helper.AddFieldSetters("rewardscreatesdworkflow", _fieldSetters, _setters)
	_RewardsAddFundsSdWorkflow, _fieldSetters := NewRewardsAddFundsSdWorkflow()
	_obj._RewardsAddFundsSdWorkflow = _RewardsAddFundsSdWorkflow
	helper.AddFieldSetters("rewardsaddfundssdworkflow", _fieldSetters, _setters)
	_AddFundsSdWorkflow, _fieldSetters := NewAddFundsSdWorkflow()
	_obj._AddFundsSdWorkflow = _AddFundsSdWorkflow
	helper.AddFieldSetters("addfundssdworkflow", _fieldSetters, _setters)
	_AddFundsWorkflow, _fieldSetters := NewAddFundsWorkflow()
	_obj._AddFundsWorkflow = _AddFundsWorkflow
	helper.AddFieldSetters("addfundsworkflow", _fieldSetters, _setters)
	_AddFundsCollectWorkflow, _fieldSetters := NewAddFundsCollectWorkflow()
	_obj._AddFundsCollectWorkflow = _AddFundsCollectWorkflow
	helper.AddFieldSetters("addfundscollectworkflow", _fieldSetters, _setters)
	_RecurringPaymentCreationWorkflow, _fieldSetters := NewRecurringPaymentCreationWorkflow()
	_obj._RecurringPaymentCreationWorkflow = _RecurringPaymentCreationWorkflow
	helper.AddFieldSetters("recurringpaymentcreationworkflow", _fieldSetters, _setters)
	_RecurringPaymentExecutionWorkflow, _fieldSetters := NewRecurringPaymentExecutionWorkflow()
	_obj._RecurringPaymentExecutionWorkflow = _RecurringPaymentExecutionWorkflow
	helper.AddFieldSetters("recurringpaymentexecutionworkflow", _fieldSetters, _setters)
	_RecurringPaymentModifyWorkflow, _fieldSetters := NewRecurringPaymentModifyWorkflow()
	_obj._RecurringPaymentModifyWorkflow = _RecurringPaymentModifyWorkflow
	helper.AddFieldSetters("recurringpaymentmodifyworkflow", _fieldSetters, _setters)
	_RecurringPaymentRevokeWorkflow, _fieldSetters := NewRecurringPaymentRevokeWorkflow()
	_obj._RecurringPaymentRevokeWorkflow = _RecurringPaymentRevokeWorkflow
	helper.AddFieldSetters("recurringpaymentrevokeworkflow", _fieldSetters, _setters)
	_P2PInvestmentWorkflow, _fieldSetters := NewP2PInvestmentWorkflow()
	_obj._P2PInvestmentWorkflow = _P2PInvestmentWorkflow
	helper.AddFieldSetters("p2pinvestmentworkflow", _fieldSetters, _setters)
	_P2PWithdrawalWorkflow, _fieldSetters := NewP2PWithdrawalWorkflow()
	_obj._P2PWithdrawalWorkflow = _P2PWithdrawalWorkflow
	helper.AddFieldSetters("p2pwithdrawalworkflow", _fieldSetters, _setters)
	_MutualFundsInvestmentPostPaid, _fieldSetters := NewMutualFundsInvestmentPostPaid()
	_obj._MutualFundsInvestmentPostPaid = _MutualFundsInvestmentPostPaid
	helper.AddFieldSetters("mutualfundsinvestmentpostpaid", _fieldSetters, _setters)
	_MutualFundsRedemption, _fieldSetters := NewMutualFundsRedemption()
	_obj._MutualFundsRedemption = _MutualFundsRedemption
	helper.AddFieldSetters("mutualfundsredemption", _fieldSetters, _setters)
	_PaymentNotificationParams, _fieldSetters := NewPaymentNotificationParams()
	_obj._PaymentNotificationParams = _PaymentNotificationParams
	helper.AddFieldSetters("paymentnotificationparams", _fieldSetters, _setters)
	_PaymentSMSParams, _fieldSetters := NewPaymentSMSParams()
	_obj._PaymentSMSParams = _PaymentSMSParams
	helper.AddFieldSetters("paymentsmsparams", _fieldSetters, _setters)
	_IconUrls, _fieldSetters := NewIconUrls()
	_obj._IconUrls = _IconUrls
	helper.AddFieldSetters("iconurls", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_CustomRuleDEParams, _fieldSetters := NewCustomRuleDEParams()
	_obj._CustomRuleDEParams = _CustomRuleDEParams
	helper.AddFieldSetters("customruledeparams", _fieldSetters, _setters)
	_URNPaymentLimits, _fieldSetters := NewURNPaymentLimits()
	_obj._URNPaymentLimits = _URNPaymentLimits
	helper.AddFieldSetters("urnpaymentlimits", _fieldSetters, _setters)
	_SavingsLedgerReconSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SavingsLedgerReconSubscriber = _SavingsLedgerReconSubscriber
	helper.AddFieldSetters("savingsledgerreconsubscriber", _fieldSetters, _setters)
	_AddFundsAlertParams, _fieldSetters := NewAddFundsAlertParams()
	_obj._AddFundsAlertParams = _AddFundsAlertParams
	helper.AddFieldSetters("addfundsalertparams", _fieldSetters, _setters)
	_AddFundsAlertMailingParams, _fieldSetters := NewAddFundsAlertMailingParams()
	_obj._AddFundsAlertMailingParams = _AddFundsAlertMailingParams
	helper.AddFieldSetters("addfundsalertmailingparams", _fieldSetters, _setters)
	_ExecuteRecurringPaymentWithAuthWorkflow, _fieldSetters := NewExecuteRecurringPaymentWithAuthWorkflow()
	_obj._ExecuteRecurringPaymentWithAuthWorkflow = _ExecuteRecurringPaymentWithAuthWorkflow
	helper.AddFieldSetters("executerecurringpaymentwithauthworkflow", _fieldSetters, _setters)
	_ExecuteRecurringPaymentWithNoAuthWorkflow, _fieldSetters := NewExecuteRecurringPaymentWithNoAuthWorkflow()
	_obj._ExecuteRecurringPaymentWithNoAuthWorkflow = _ExecuteRecurringPaymentWithNoAuthWorkflow
	helper.AddFieldSetters("executerecurringpaymentwithnoauthworkflow", _fieldSetters, _setters)
	_RecurringPaymentPauseUnpauseWorkflow, _fieldSetters := NewRecurringPaymentPauseUnpauseWorkflow()
	_obj._RecurringPaymentPauseUnpauseWorkflow = _RecurringPaymentPauseUnpauseWorkflow
	helper.AddFieldSetters("recurringpaymentpauseunpauseworkflow", _fieldSetters, _setters)
	_PurgeAaDataSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PurgeAaDataSubscriber = _PurgeAaDataSubscriber
	helper.AddFieldSetters("purgeaadatasubscriber", _fieldSetters, _setters)
	_AaTxnPurgeSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AaTxnPurgeSubscriber = _AaTxnPurgeSubscriber
	helper.AddFieldSetters("aatxnpurgesubscriber", _fieldSetters, _setters)
	_CSIS, _fieldSetters := NewCSIS()
	_obj._CSIS = _CSIS
	helper.AddFieldSetters("csis", _fieldSetters, _setters)
	_BankDownTime, _fieldSetters := NewBankDownTime()
	_obj._BankDownTime = _BankDownTime
	helper.AddFieldSetters("bankdowntime", _fieldSetters, _setters)
	_ConnectedAccountUserGroupParams, _fieldSetters := NewConnectedAccountUserGroupParams()
	_obj._ConnectedAccountUserGroupParams = _ConnectedAccountUserGroupParams
	helper.AddFieldSetters("connectedaccountusergroupparams", _fieldSetters, _setters)
	_AaDataPurgeOrchestrationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AaDataPurgeOrchestrationSubscriber = _AaDataPurgeOrchestrationSubscriber
	helper.AddFieldSetters("aadatapurgeorchestrationsubscriber", _fieldSetters, _setters)
	_AaParams, _fieldSetters := NewAaParams()
	_obj._AaParams = _AaParams
	helper.AddFieldSetters("aaparams", _fieldSetters, _setters)
	_NameCheckParamsForAddFunds, _fieldSetters := NewNameCheckParamsForAddFunds()
	_obj._NameCheckParamsForAddFunds = _NameCheckParamsForAddFunds
	helper.AddFieldSetters("namecheckparamsforaddfunds", _fieldSetters, _setters)
	_DeclineCardTransactionsProcessingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DeclineCardTransactionsProcessingSubscriber = _DeclineCardTransactionsProcessingSubscriber
	helper.AddFieldSetters("declinecardtransactionsprocessingsubscriber", _fieldSetters, _setters)
	_SavingsLedgerReconCacheConfig, _fieldSetters := NewSavingsLedgerReconCacheConfig()
	_obj._SavingsLedgerReconCacheConfig = _SavingsLedgerReconCacheConfig
	helper.AddFieldSetters("savingsledgerreconcacheconfig", _fieldSetters, _setters)
	_OrderCacheConfig, _fieldSetters := NewOrderCacheConfig()
	_obj._OrderCacheConfig = _OrderCacheConfig
	helper.AddFieldSetters("ordercacheconfig", _fieldSetters, _setters)
	_TransactionCacheConfig, _fieldSetters := NewTransactionCacheConfig()
	_obj._TransactionCacheConfig = _TransactionCacheConfig
	helper.AddFieldSetters("transactioncacheconfig", _fieldSetters, _setters)
	_FeatureFlags, _fieldSetters := NewFeatureFlags()
	_obj._FeatureFlags = _FeatureFlags
	helper.AddFieldSetters("featureflags", _fieldSetters, _setters)
	_InboundNotificationParams, _fieldSetters := NewInboundNotificationParams()
	_obj._InboundNotificationParams = _InboundNotificationParams
	helper.AddFieldSetters("inboundnotificationparams", _fieldSetters, _setters)
	_ErrorRespCodesForPermanentFailure, _fieldSetters := NewErrorRespCodesForPermanentFailure()
	_obj._ErrorRespCodesForPermanentFailure = _ErrorRespCodesForPermanentFailure
	helper.AddFieldSetters("errorrespcodesforpermanentfailure", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_DataReplicationParams, _fieldSetters := gencfg.NewDataReplicationParams()
	_obj._DataReplicationParams = _DataReplicationParams
	helper.AddFieldSetters("datareplicationparams", _fieldSetters, _setters)
	_ReconRestrictedWindow, _fieldSetters := NewRestrictedWindow()
	_obj._ReconRestrictedWindow = _ReconRestrictedWindow
	helper.AddFieldSetters("reconrestrictedwindow", _fieldSetters, _setters)
	_TxnNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TxnNotificationSubscriber = _TxnNotificationSubscriber
	helper.AddFieldSetters("txnnotificationsubscriber", _fieldSetters, _setters)
	_DeemedTransactionUpiEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DeemedTransactionUpiEnquirySubscriber = _DeemedTransactionUpiEnquirySubscriber
	helper.AddFieldSetters("deemedtransactionupienquirysubscriber", _fieldSetters, _setters)
	_AAFirstDataPullTxnSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AAFirstDataPullTxnSubscriber = _AAFirstDataPullTxnSubscriber
	helper.AddFieldSetters("aafirstdatapulltxnsubscriber", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig2.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_RewardsInfo, _fieldSetters := NewRewardsInfo()
	_obj._RewardsInfo = _RewardsInfo
	helper.AddFieldSetters("rewardsinfo", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["aaenrichedtxnupdatebatchsize"] = _obj.SetAaEnrichedTxnUpdateBatchSize
	_setters["maximumpausetimeforremitterinfoworkflow"] = _obj.SetMaximumPauseTimeForRemitterInfoWorkflow
	_setters["hideaccountbalanceinnotification"] = _obj.SetHideAccountBalanceInNotification
	_setters["ispushingpaymentsmetricstohealthengineenabled"] = _obj.SetIsPushingPaymentsMetricsToHealthEngineEnabled
	_setters["isemailnotificationenable"] = _obj.SetIsEmailNotificationEnable
	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_setters["recentordereligibleduration"] = _obj.SetRecentOrderEligibleDuration
	_setters["timeoutforgetoperationalstatus"] = _obj.SetTimeoutForGetOperationalStatus
	_setters["pinotquerytimeout"] = _obj.SetPinotQueryTimeout

	_obj._SMSTypeToOptionVersionMap = &syncmap.Map[string, *config.SMSOptionVersion]{}
	_setters["smstypetooptionversionmap"] = _obj.SetSMSTypeToOptionVersionMap
	_PaymentOrchestrationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PaymentOrchestrationSubscriber = _PaymentOrchestrationSubscriber
	helper.AddFieldSetters("paymentorchestrationsubscriber", _fieldSetters, _setters)
	_IntraBankEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._IntraBankEnquirySubscriber = _IntraBankEnquirySubscriber
	helper.AddFieldSetters("intrabankenquirysubscriber", _fieldSetters, _setters)
	_UPIEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UPIEnquirySubscriber = _UPIEnquirySubscriber
	helper.AddFieldSetters("upienquirysubscriber", _fieldSetters, _setters)
	_IMPSEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._IMPSEnquirySubscriber = _IMPSEnquirySubscriber
	helper.AddFieldSetters("impsenquirysubscriber", _fieldSetters, _setters)
	_NEFTEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._NEFTEnquirySubscriber = _NEFTEnquirySubscriber
	helper.AddFieldSetters("neftenquirysubscriber", _fieldSetters, _setters)
	_RTGSEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RTGSEnquirySubscriber = _RTGSEnquirySubscriber
	helper.AddFieldSetters("rtgsenquirysubscriber", _fieldSetters, _setters)
	_OrderOrchestrationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderOrchestrationSubscriber = _OrderOrchestrationSubscriber
	helper.AddFieldSetters("orderorchestrationsubscriber", _fieldSetters, _setters)
	_PaymentCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PaymentCallbackSubscriber = _PaymentCallbackSubscriber
	helper.AddFieldSetters("paymentcallbacksubscriber", _fieldSetters, _setters)
	_InboundTxnSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._InboundTxnSubscriber = _InboundTxnSubscriber
	helper.AddFieldSetters("inboundtxnsubscriber", _fieldSetters, _setters)
	_InboundUpiTxnSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._InboundUpiTxnSubscriber = _InboundUpiTxnSubscriber
	helper.AddFieldSetters("inboundupitxnsubscriber", _fieldSetters, _setters)
	_OrderUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderUpdateSubscriber = _OrderUpdateSubscriber
	helper.AddFieldSetters("orderupdatesubscriber", _fieldSetters, _setters)
	_OrderCollectNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderCollectNotificationSubscriber = _OrderCollectNotificationSubscriber
	helper.AddFieldSetters("ordercollectnotificationsubscriber", _fieldSetters, _setters)
	_OrderNotificationFallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderNotificationFallbackSubscriber = _OrderNotificationFallbackSubscriber
	helper.AddFieldSetters("ordernotificationfallbacksubscriber", _fieldSetters, _setters)
	_OrderWorkflowProcessingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderWorkflowProcessingSubscriber = _OrderWorkflowProcessingSubscriber
	helper.AddFieldSetters("orderworkflowprocessingsubscriber", _fieldSetters, _setters)
	_DisputeEventProcessingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DisputeEventProcessingSubscriber = _DisputeEventProcessingSubscriber
	helper.AddFieldSetters("disputeeventprocessingsubscriber", _fieldSetters, _setters)
	_AATxnSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AATxnSubscriber = _AATxnSubscriber
	helper.AddFieldSetters("aatxnsubscriber", _fieldSetters, _setters)
	_PaymentProtocolDecisionParams, _fieldSetters := NewPaymentProtocolDecisionParams()
	_obj._PaymentProtocolDecisionParams = _PaymentProtocolDecisionParams
	helper.AddFieldSetters("paymentprotocoldecisionparams", _fieldSetters, _setters)
	_RuleEngineParams, _fieldSetters := NewRuleEngineParams()
	_obj._RuleEngineParams = _RuleEngineParams
	helper.AddFieldSetters("ruleengineparams", _fieldSetters, _setters)
	_OrderReceipt, _fieldSetters := NewOrderReceipt()
	_obj._OrderReceipt = _OrderReceipt
	helper.AddFieldSetters("orderreceipt", _fieldSetters, _setters)
	_FundTransferWorkflow, _fieldSetters := NewFundTransferWorkflow()
	_obj._FundTransferWorkflow = _FundTransferWorkflow
	helper.AddFieldSetters("fundtransferworkflow", _fieldSetters, _setters)
	_CollectShortCircuitWorkflow, _fieldSetters := NewCollectShortCircuitWorkflow()
	_obj._CollectShortCircuitWorkflow = _CollectShortCircuitWorkflow
	helper.AddFieldSetters("collectshortcircuitworkflow", _fieldSetters, _setters)
	_DepositCreationWorkflow, _fieldSetters := NewDepositCreationWorkflow()
	_obj._DepositCreationWorkflow = _DepositCreationWorkflow
	helper.AddFieldSetters("depositcreationworkflow", _fieldSetters, _setters)
	_UrnTransferWorkflow, _fieldSetters := NewUrnTransferWorkflow()
	_obj._UrnTransferWorkflow = _UrnTransferWorkflow
	helper.AddFieldSetters("urntransferworkflow", _fieldSetters, _setters)
	_PreCloseDepositWorkflow, _fieldSetters := NewPreCloseDepositWorkflow()
	_obj._PreCloseDepositWorkflow = _PreCloseDepositWorkflow
	helper.AddFieldSetters("preclosedepositworkflow", _fieldSetters, _setters)
	_P2PCollectWorkflow, _fieldSetters := NewP2PCollectWorkflow()
	_obj._P2PCollectWorkflow = _P2PCollectWorkflow
	helper.AddFieldSetters("p2pcollectworkflow", _fieldSetters, _setters)
	_B2CFundTransferWorkflow, _fieldSetters := NewB2CFundTransferWorkflow()
	_obj._B2CFundTransferWorkflow = _B2CFundTransferWorkflow
	helper.AddFieldSetters("b2cfundtransferworkflow", _fieldSetters, _setters)
	_RewardsCreateSdWorkflow, _fieldSetters := NewRewardsCreateSdWorkflow()
	_obj._RewardsCreateSdWorkflow = _RewardsCreateSdWorkflow
	helper.AddFieldSetters("rewardscreatesdworkflow", _fieldSetters, _setters)
	_RewardsAddFundsSdWorkflow, _fieldSetters := NewRewardsAddFundsSdWorkflow()
	_obj._RewardsAddFundsSdWorkflow = _RewardsAddFundsSdWorkflow
	helper.AddFieldSetters("rewardsaddfundssdworkflow", _fieldSetters, _setters)
	_AddFundsSdWorkflow, _fieldSetters := NewAddFundsSdWorkflow()
	_obj._AddFundsSdWorkflow = _AddFundsSdWorkflow
	helper.AddFieldSetters("addfundssdworkflow", _fieldSetters, _setters)
	_AddFundsWorkflow, _fieldSetters := NewAddFundsWorkflow()
	_obj._AddFundsWorkflow = _AddFundsWorkflow
	helper.AddFieldSetters("addfundsworkflow", _fieldSetters, _setters)
	_AddFundsCollectWorkflow, _fieldSetters := NewAddFundsCollectWorkflow()
	_obj._AddFundsCollectWorkflow = _AddFundsCollectWorkflow
	helper.AddFieldSetters("addfundscollectworkflow", _fieldSetters, _setters)
	_RecurringPaymentCreationWorkflow, _fieldSetters := NewRecurringPaymentCreationWorkflow()
	_obj._RecurringPaymentCreationWorkflow = _RecurringPaymentCreationWorkflow
	helper.AddFieldSetters("recurringpaymentcreationworkflow", _fieldSetters, _setters)
	_RecurringPaymentExecutionWorkflow, _fieldSetters := NewRecurringPaymentExecutionWorkflow()
	_obj._RecurringPaymentExecutionWorkflow = _RecurringPaymentExecutionWorkflow
	helper.AddFieldSetters("recurringpaymentexecutionworkflow", _fieldSetters, _setters)
	_RecurringPaymentModifyWorkflow, _fieldSetters := NewRecurringPaymentModifyWorkflow()
	_obj._RecurringPaymentModifyWorkflow = _RecurringPaymentModifyWorkflow
	helper.AddFieldSetters("recurringpaymentmodifyworkflow", _fieldSetters, _setters)
	_RecurringPaymentRevokeWorkflow, _fieldSetters := NewRecurringPaymentRevokeWorkflow()
	_obj._RecurringPaymentRevokeWorkflow = _RecurringPaymentRevokeWorkflow
	helper.AddFieldSetters("recurringpaymentrevokeworkflow", _fieldSetters, _setters)
	_P2PInvestmentWorkflow, _fieldSetters := NewP2PInvestmentWorkflow()
	_obj._P2PInvestmentWorkflow = _P2PInvestmentWorkflow
	helper.AddFieldSetters("p2pinvestmentworkflow", _fieldSetters, _setters)
	_P2PWithdrawalWorkflow, _fieldSetters := NewP2PWithdrawalWorkflow()
	_obj._P2PWithdrawalWorkflow = _P2PWithdrawalWorkflow
	helper.AddFieldSetters("p2pwithdrawalworkflow", _fieldSetters, _setters)
	_MutualFundsInvestmentPostPaid, _fieldSetters := NewMutualFundsInvestmentPostPaid()
	_obj._MutualFundsInvestmentPostPaid = _MutualFundsInvestmentPostPaid
	helper.AddFieldSetters("mutualfundsinvestmentpostpaid", _fieldSetters, _setters)
	_MutualFundsRedemption, _fieldSetters := NewMutualFundsRedemption()
	_obj._MutualFundsRedemption = _MutualFundsRedemption
	helper.AddFieldSetters("mutualfundsredemption", _fieldSetters, _setters)
	_PaymentNotificationParams, _fieldSetters := NewPaymentNotificationParams()
	_obj._PaymentNotificationParams = _PaymentNotificationParams
	helper.AddFieldSetters("paymentnotificationparams", _fieldSetters, _setters)
	_PaymentSMSParams, _fieldSetters := NewPaymentSMSParams()
	_obj._PaymentSMSParams = _PaymentSMSParams
	helper.AddFieldSetters("paymentsmsparams", _fieldSetters, _setters)
	_IconUrls, _fieldSetters := NewIconUrls()
	_obj._IconUrls = _IconUrls
	helper.AddFieldSetters("iconurls", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_CustomRuleDEParams, _fieldSetters := NewCustomRuleDEParams()
	_obj._CustomRuleDEParams = _CustomRuleDEParams
	helper.AddFieldSetters("customruledeparams", _fieldSetters, _setters)
	_URNPaymentLimits, _fieldSetters := NewURNPaymentLimits()
	_obj._URNPaymentLimits = _URNPaymentLimits
	helper.AddFieldSetters("urnpaymentlimits", _fieldSetters, _setters)
	_SavingsLedgerReconSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SavingsLedgerReconSubscriber = _SavingsLedgerReconSubscriber
	helper.AddFieldSetters("savingsledgerreconsubscriber", _fieldSetters, _setters)
	_AddFundsAlertParams, _fieldSetters := NewAddFundsAlertParams()
	_obj._AddFundsAlertParams = _AddFundsAlertParams
	helper.AddFieldSetters("addfundsalertparams", _fieldSetters, _setters)
	_AddFundsAlertMailingParams, _fieldSetters := NewAddFundsAlertMailingParams()
	_obj._AddFundsAlertMailingParams = _AddFundsAlertMailingParams
	helper.AddFieldSetters("addfundsalertmailingparams", _fieldSetters, _setters)
	_ExecuteRecurringPaymentWithAuthWorkflow, _fieldSetters := NewExecuteRecurringPaymentWithAuthWorkflow()
	_obj._ExecuteRecurringPaymentWithAuthWorkflow = _ExecuteRecurringPaymentWithAuthWorkflow
	helper.AddFieldSetters("executerecurringpaymentwithauthworkflow", _fieldSetters, _setters)
	_ExecuteRecurringPaymentWithNoAuthWorkflow, _fieldSetters := NewExecuteRecurringPaymentWithNoAuthWorkflow()
	_obj._ExecuteRecurringPaymentWithNoAuthWorkflow = _ExecuteRecurringPaymentWithNoAuthWorkflow
	helper.AddFieldSetters("executerecurringpaymentwithnoauthworkflow", _fieldSetters, _setters)
	_RecurringPaymentPauseUnpauseWorkflow, _fieldSetters := NewRecurringPaymentPauseUnpauseWorkflow()
	_obj._RecurringPaymentPauseUnpauseWorkflow = _RecurringPaymentPauseUnpauseWorkflow
	helper.AddFieldSetters("recurringpaymentpauseunpauseworkflow", _fieldSetters, _setters)
	_PurgeAaDataSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PurgeAaDataSubscriber = _PurgeAaDataSubscriber
	helper.AddFieldSetters("purgeaadatasubscriber", _fieldSetters, _setters)
	_AaTxnPurgeSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AaTxnPurgeSubscriber = _AaTxnPurgeSubscriber
	helper.AddFieldSetters("aatxnpurgesubscriber", _fieldSetters, _setters)
	_CSIS, _fieldSetters := NewCSIS()
	_obj._CSIS = _CSIS
	helper.AddFieldSetters("csis", _fieldSetters, _setters)
	_BankDownTime, _fieldSetters := NewBankDownTime()
	_obj._BankDownTime = _BankDownTime
	helper.AddFieldSetters("bankdowntime", _fieldSetters, _setters)
	_ConnectedAccountUserGroupParams, _fieldSetters := NewConnectedAccountUserGroupParams()
	_obj._ConnectedAccountUserGroupParams = _ConnectedAccountUserGroupParams
	helper.AddFieldSetters("connectedaccountusergroupparams", _fieldSetters, _setters)
	_AaDataPurgeOrchestrationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AaDataPurgeOrchestrationSubscriber = _AaDataPurgeOrchestrationSubscriber
	helper.AddFieldSetters("aadatapurgeorchestrationsubscriber", _fieldSetters, _setters)
	_AaParams, _fieldSetters := NewAaParams()
	_obj._AaParams = _AaParams
	helper.AddFieldSetters("aaparams", _fieldSetters, _setters)
	_NameCheckParamsForAddFunds, _fieldSetters := NewNameCheckParamsForAddFunds()
	_obj._NameCheckParamsForAddFunds = _NameCheckParamsForAddFunds
	helper.AddFieldSetters("namecheckparamsforaddfunds", _fieldSetters, _setters)
	_DeclineCardTransactionsProcessingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DeclineCardTransactionsProcessingSubscriber = _DeclineCardTransactionsProcessingSubscriber
	helper.AddFieldSetters("declinecardtransactionsprocessingsubscriber", _fieldSetters, _setters)
	_SavingsLedgerReconCacheConfig, _fieldSetters := NewSavingsLedgerReconCacheConfig()
	_obj._SavingsLedgerReconCacheConfig = _SavingsLedgerReconCacheConfig
	helper.AddFieldSetters("savingsledgerreconcacheconfig", _fieldSetters, _setters)
	_OrderCacheConfig, _fieldSetters := NewOrderCacheConfig()
	_obj._OrderCacheConfig = _OrderCacheConfig
	helper.AddFieldSetters("ordercacheconfig", _fieldSetters, _setters)
	_TransactionCacheConfig, _fieldSetters := NewTransactionCacheConfig()
	_obj._TransactionCacheConfig = _TransactionCacheConfig
	helper.AddFieldSetters("transactioncacheconfig", _fieldSetters, _setters)
	_FeatureFlags, _fieldSetters := NewFeatureFlags()
	_obj._FeatureFlags = _FeatureFlags
	helper.AddFieldSetters("featureflags", _fieldSetters, _setters)
	_InboundNotificationParams, _fieldSetters := NewInboundNotificationParams()
	_obj._InboundNotificationParams = _InboundNotificationParams
	helper.AddFieldSetters("inboundnotificationparams", _fieldSetters, _setters)
	_ErrorRespCodesForPermanentFailure, _fieldSetters := NewErrorRespCodesForPermanentFailure()
	_obj._ErrorRespCodesForPermanentFailure = _ErrorRespCodesForPermanentFailure
	helper.AddFieldSetters("errorrespcodesforpermanentfailure", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_DataReplicationParams, _fieldSetters := gencfg.NewDataReplicationParams()
	_obj._DataReplicationParams = _DataReplicationParams
	helper.AddFieldSetters("datareplicationparams", _fieldSetters, _setters)
	_ReconRestrictedWindow, _fieldSetters := NewRestrictedWindow()
	_obj._ReconRestrictedWindow = _ReconRestrictedWindow
	helper.AddFieldSetters("reconrestrictedwindow", _fieldSetters, _setters)
	_TxnNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TxnNotificationSubscriber = _TxnNotificationSubscriber
	helper.AddFieldSetters("txnnotificationsubscriber", _fieldSetters, _setters)
	_DeemedTransactionUpiEnquirySubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DeemedTransactionUpiEnquirySubscriber = _DeemedTransactionUpiEnquirySubscriber
	helper.AddFieldSetters("deemedtransactionupienquirysubscriber", _fieldSetters, _setters)
	_AAFirstDataPullTxnSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AAFirstDataPullTxnSubscriber = _AAFirstDataPullTxnSubscriber
	helper.AddFieldSetters("aafirstdatapulltxnsubscriber", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig2.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_RewardsInfo, _fieldSetters := NewRewardsInfo()
	_obj._RewardsInfo = _RewardsInfo
	helper.AddFieldSetters("rewardsinfo", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *Config) Init(questFieldPath string) {
	newObj, _ := NewConfig()
	*obj = *newObj
}
func (obj *Config) InitWithQuest(questFieldPath string) {
	newObj, _ := NewConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IsEmailNotificationEnable",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "Pay", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "aaenrichedtxnupdatebatchsize":
		return obj.SetAaEnrichedTxnUpdateBatchSize(v.AaEnrichedTxnUpdateBatchSize, true, nil)
	case "maximumpausetimeforremitterinfoworkflow":
		return obj.SetMaximumPauseTimeForRemitterInfoWorkflow(v.MaximumPauseTimeForRemitterInfoWorkflow, true, nil)
	case "hideaccountbalanceinnotification":
		return obj.SetHideAccountBalanceInNotification(v.HideAccountBalanceInNotification, true, nil)
	case "ispushingpaymentsmetricstohealthengineenabled":
		return obj.SetIsPushingPaymentsMetricsToHealthEngineEnabled(v.IsPushingPaymentsMetricsToHealthEngineEnabled, true, nil)
	case "isemailnotificationenable":
		return obj.SetIsEmailNotificationEnable(v.IsEmailNotificationEnable, true, nil)
	case "enableentitysegregation":
		return obj.SetEnableEntitySegregation(v.EnableEntitySegregation, true, nil)
	case "recentordereligibleduration":
		return obj.SetRecentOrderEligibleDuration(v.RecentOrderEligibleDuration, true, nil)
	case "timeoutforgetoperationalstatus":
		return obj.SetTimeoutForGetOperationalStatus(v.TimeoutForGetOperationalStatus, true, nil)
	case "pinotquerytimeout":
		return obj.SetPinotQueryTimeout(v.PinotQueryTimeout, true, nil)
	case "smstypetooptionversionmap":
		return obj.SetSMSTypeToOptionVersionMap(v.SMSTypeToOptionVersionMap, true, path)
	case "paymentorchestrationsubscriber":
		return obj._PaymentOrchestrationSubscriber.Set(v.PaymentOrchestrationSubscriber, true, path)
	case "intrabankenquirysubscriber":
		return obj._IntraBankEnquirySubscriber.Set(v.IntraBankEnquirySubscriber, true, path)
	case "upienquirysubscriber":
		return obj._UPIEnquirySubscriber.Set(v.UPIEnquirySubscriber, true, path)
	case "impsenquirysubscriber":
		return obj._IMPSEnquirySubscriber.Set(v.IMPSEnquirySubscriber, true, path)
	case "neftenquirysubscriber":
		return obj._NEFTEnquirySubscriber.Set(v.NEFTEnquirySubscriber, true, path)
	case "rtgsenquirysubscriber":
		return obj._RTGSEnquirySubscriber.Set(v.RTGSEnquirySubscriber, true, path)
	case "orderorchestrationsubscriber":
		return obj._OrderOrchestrationSubscriber.Set(v.OrderOrchestrationSubscriber, true, path)
	case "paymentcallbacksubscriber":
		return obj._PaymentCallbackSubscriber.Set(v.PaymentCallbackSubscriber, true, path)
	case "inboundtxnsubscriber":
		return obj._InboundTxnSubscriber.Set(v.InboundTxnSubscriber, true, path)
	case "inboundupitxnsubscriber":
		return obj._InboundUpiTxnSubscriber.Set(v.InboundUpiTxnSubscriber, true, path)
	case "orderupdatesubscriber":
		return obj._OrderUpdateSubscriber.Set(v.OrderUpdateSubscriber, true, path)
	case "ordercollectnotificationsubscriber":
		return obj._OrderCollectNotificationSubscriber.Set(v.OrderCollectNotificationSubscriber, true, path)
	case "ordernotificationfallbacksubscriber":
		return obj._OrderNotificationFallbackSubscriber.Set(v.OrderNotificationFallbackSubscriber, true, path)
	case "orderworkflowprocessingsubscriber":
		return obj._OrderWorkflowProcessingSubscriber.Set(v.OrderWorkflowProcessingSubscriber, true, path)
	case "disputeeventprocessingsubscriber":
		return obj._DisputeEventProcessingSubscriber.Set(v.DisputeEventProcessingSubscriber, true, path)
	case "aatxnsubscriber":
		return obj._AATxnSubscriber.Set(v.AATxnSubscriber, true, path)
	case "paymentprotocoldecisionparams":
		return obj._PaymentProtocolDecisionParams.Set(v.PaymentProtocolDecisionParams, true, path)
	case "ruleengineparams":
		return obj._RuleEngineParams.Set(v.RuleEngineParams, true, path)
	case "orderreceipt":
		return obj._OrderReceipt.Set(v.OrderReceipt, true, path)
	case "fundtransferworkflow":
		return obj._FundTransferWorkflow.Set(v.FundTransferWorkflow, true, path)
	case "collectshortcircuitworkflow":
		return obj._CollectShortCircuitWorkflow.Set(v.CollectShortCircuitWorkflow, true, path)
	case "depositcreationworkflow":
		return obj._DepositCreationWorkflow.Set(v.DepositCreationWorkflow, true, path)
	case "urntransferworkflow":
		return obj._UrnTransferWorkflow.Set(v.UrnTransferWorkflow, true, path)
	case "preclosedepositworkflow":
		return obj._PreCloseDepositWorkflow.Set(v.PreCloseDepositWorkflow, true, path)
	case "p2pcollectworkflow":
		return obj._P2PCollectWorkflow.Set(v.P2PCollectWorkflow, true, path)
	case "b2cfundtransferworkflow":
		return obj._B2CFundTransferWorkflow.Set(v.B2CFundTransferWorkflow, true, path)
	case "rewardscreatesdworkflow":
		return obj._RewardsCreateSdWorkflow.Set(v.RewardsCreateSdWorkflow, true, path)
	case "rewardsaddfundssdworkflow":
		return obj._RewardsAddFundsSdWorkflow.Set(v.RewardsAddFundsSdWorkflow, true, path)
	case "addfundssdworkflow":
		return obj._AddFundsSdWorkflow.Set(v.AddFundsSdWorkflow, true, path)
	case "addfundsworkflow":
		return obj._AddFundsWorkflow.Set(v.AddFundsWorkflow, true, path)
	case "addfundscollectworkflow":
		return obj._AddFundsCollectWorkflow.Set(v.AddFundsCollectWorkflow, true, path)
	case "recurringpaymentcreationworkflow":
		return obj._RecurringPaymentCreationWorkflow.Set(v.RecurringPaymentCreationWorkflow, true, path)
	case "recurringpaymentexecutionworkflow":
		return obj._RecurringPaymentExecutionWorkflow.Set(v.RecurringPaymentExecutionWorkflow, true, path)
	case "recurringpaymentmodifyworkflow":
		return obj._RecurringPaymentModifyWorkflow.Set(v.RecurringPaymentModifyWorkflow, true, path)
	case "recurringpaymentrevokeworkflow":
		return obj._RecurringPaymentRevokeWorkflow.Set(v.RecurringPaymentRevokeWorkflow, true, path)
	case "p2pinvestmentworkflow":
		return obj._P2PInvestmentWorkflow.Set(v.P2PInvestmentWorkflow, true, path)
	case "p2pwithdrawalworkflow":
		return obj._P2PWithdrawalWorkflow.Set(v.P2PWithdrawalWorkflow, true, path)
	case "mutualfundsinvestmentpostpaid":
		return obj._MutualFundsInvestmentPostPaid.Set(v.MutualFundsInvestmentPostPaid, true, path)
	case "mutualfundsredemption":
		return obj._MutualFundsRedemption.Set(v.MutualFundsRedemption, true, path)
	case "paymentnotificationparams":
		return obj._PaymentNotificationParams.Set(v.PaymentNotificationParams, true, path)
	case "paymentsmsparams":
		return obj._PaymentSMSParams.Set(v.PaymentSMSParams, true, path)
	case "iconurls":
		return obj._IconUrls.Set(v.IconUrls, true, path)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "customruledeparams":
		return obj._CustomRuleDEParams.Set(v.CustomRuleDEParams, true, path)
	case "urnpaymentlimits":
		return obj._URNPaymentLimits.Set(v.URNPaymentLimits, true, path)
	case "savingsledgerreconsubscriber":
		return obj._SavingsLedgerReconSubscriber.Set(v.SavingsLedgerReconSubscriber, true, path)
	case "addfundsalertparams":
		return obj._AddFundsAlertParams.Set(v.AddFundsAlertParams, true, path)
	case "addfundsalertmailingparams":
		return obj._AddFundsAlertMailingParams.Set(v.AddFundsAlertMailingParams, true, path)
	case "executerecurringpaymentwithauthworkflow":
		return obj._ExecuteRecurringPaymentWithAuthWorkflow.Set(v.ExecuteRecurringPaymentWithAuthWorkflow, true, path)
	case "executerecurringpaymentwithnoauthworkflow":
		return obj._ExecuteRecurringPaymentWithNoAuthWorkflow.Set(v.ExecuteRecurringPaymentWithNoAuthWorkflow, true, path)
	case "recurringpaymentpauseunpauseworkflow":
		return obj._RecurringPaymentPauseUnpauseWorkflow.Set(v.RecurringPaymentPauseUnpauseWorkflow, true, path)
	case "purgeaadatasubscriber":
		return obj._PurgeAaDataSubscriber.Set(v.PurgeAaDataSubscriber, true, path)
	case "aatxnpurgesubscriber":
		return obj._AaTxnPurgeSubscriber.Set(v.AaTxnPurgeSubscriber, true, path)
	case "csis":
		return obj._CSIS.Set(v.CSIS, true, path)
	case "bankdowntime":
		return obj._BankDownTime.Set(v.BankDownTime, true, path)
	case "connectedaccountusergroupparams":
		return obj._ConnectedAccountUserGroupParams.Set(v.ConnectedAccountUserGroupParams, true, path)
	case "aadatapurgeorchestrationsubscriber":
		return obj._AaDataPurgeOrchestrationSubscriber.Set(v.AaDataPurgeOrchestrationSubscriber, true, path)
	case "aaparams":
		return obj._AaParams.Set(v.AaParams, true, path)
	case "namecheckparamsforaddfunds":
		return obj._NameCheckParamsForAddFunds.Set(v.NameCheckParamsForAddFunds, true, path)
	case "declinecardtransactionsprocessingsubscriber":
		return obj._DeclineCardTransactionsProcessingSubscriber.Set(v.DeclineCardTransactionsProcessingSubscriber, true, path)
	case "savingsledgerreconcacheconfig":
		return obj._SavingsLedgerReconCacheConfig.Set(v.SavingsLedgerReconCacheConfig, true, path)
	case "ordercacheconfig":
		return obj._OrderCacheConfig.Set(v.OrderCacheConfig, true, path)
	case "transactioncacheconfig":
		return obj._TransactionCacheConfig.Set(v.TransactionCacheConfig, true, path)
	case "featureflags":
		return obj._FeatureFlags.Set(v.FeatureFlags, true, path)
	case "inboundnotificationparams":
		return obj._InboundNotificationParams.Set(v.InboundNotificationParams, true, path)
	case "errorrespcodesforpermanentfailure":
		return obj._ErrorRespCodesForPermanentFailure.Set(v.ErrorRespCodesForPermanentFailure, true, path)
	case "featurereleaseconfig":
		return obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, true, path)
	case "datareplicationparams":
		return obj._DataReplicationParams.Set(v.DataReplicationParams, true, path)
	case "reconrestrictedwindow":
		return obj._ReconRestrictedWindow.Set(v.ReconRestrictedWindow, true, path)
	case "txnnotificationsubscriber":
		return obj._TxnNotificationSubscriber.Set(v.TxnNotificationSubscriber, true, path)
	case "deemedtransactionupienquirysubscriber":
		return obj._DeemedTransactionUpiEnquirySubscriber.Set(v.DeemedTransactionUpiEnquirySubscriber, true, path)
	case "aafirstdatapulltxnsubscriber":
		return obj._AAFirstDataPullTxnSubscriber.Set(v.AAFirstDataPullTxnSubscriber, true, path)
	case "questsdk":
		return obj._QuestSdk.Set(v.QuestSdk, true, path)
	case "rewardsinfo":
		return obj._RewardsInfo.Set(v.RewardsInfo, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetAaEnrichedTxnUpdateBatchSize(v.AaEnrichedTxnUpdateBatchSize, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaximumPauseTimeForRemitterInfoWorkflow(v.MaximumPauseTimeForRemitterInfoWorkflow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHideAccountBalanceInNotification(v.HideAccountBalanceInNotification, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsPushingPaymentsMetricsToHealthEngineEnabled(v.IsPushingPaymentsMetricsToHealthEngineEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsEmailNotificationEnable(v.IsEmailNotificationEnable, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableEntitySegregation(v.EnableEntitySegregation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRecentOrderEligibleDuration(v.RecentOrderEligibleDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTimeoutForGetOperationalStatus(v.TimeoutForGetOperationalStatus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPinotQueryTimeout(v.PinotQueryTimeout, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSMSTypeToOptionVersionMap(v.SMSTypeToOptionVersionMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PaymentOrchestrationSubscriber.Set(v.PaymentOrchestrationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IntraBankEnquirySubscriber.Set(v.IntraBankEnquirySubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UPIEnquirySubscriber.Set(v.UPIEnquirySubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IMPSEnquirySubscriber.Set(v.IMPSEnquirySubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NEFTEnquirySubscriber.Set(v.NEFTEnquirySubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RTGSEnquirySubscriber.Set(v.RTGSEnquirySubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderOrchestrationSubscriber.Set(v.OrderOrchestrationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PaymentCallbackSubscriber.Set(v.PaymentCallbackSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InboundTxnSubscriber.Set(v.InboundTxnSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InboundUpiTxnSubscriber.Set(v.InboundUpiTxnSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderUpdateSubscriber.Set(v.OrderUpdateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderCollectNotificationSubscriber.Set(v.OrderCollectNotificationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderNotificationFallbackSubscriber.Set(v.OrderNotificationFallbackSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderWorkflowProcessingSubscriber.Set(v.OrderWorkflowProcessingSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DisputeEventProcessingSubscriber.Set(v.DisputeEventProcessingSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AATxnSubscriber.Set(v.AATxnSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PaymentProtocolDecisionParams.Set(v.PaymentProtocolDecisionParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RuleEngineParams.Set(v.RuleEngineParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderReceipt.Set(v.OrderReceipt, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FundTransferWorkflow.Set(v.FundTransferWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CollectShortCircuitWorkflow.Set(v.CollectShortCircuitWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DepositCreationWorkflow.Set(v.DepositCreationWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UrnTransferWorkflow.Set(v.UrnTransferWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PreCloseDepositWorkflow.Set(v.PreCloseDepositWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._P2PCollectWorkflow.Set(v.P2PCollectWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._B2CFundTransferWorkflow.Set(v.B2CFundTransferWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsCreateSdWorkflow.Set(v.RewardsCreateSdWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsAddFundsSdWorkflow.Set(v.RewardsAddFundsSdWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AddFundsSdWorkflow.Set(v.AddFundsSdWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AddFundsWorkflow.Set(v.AddFundsWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AddFundsCollectWorkflow.Set(v.AddFundsCollectWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RecurringPaymentCreationWorkflow.Set(v.RecurringPaymentCreationWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RecurringPaymentExecutionWorkflow.Set(v.RecurringPaymentExecutionWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RecurringPaymentModifyWorkflow.Set(v.RecurringPaymentModifyWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RecurringPaymentRevokeWorkflow.Set(v.RecurringPaymentRevokeWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._P2PInvestmentWorkflow.Set(v.P2PInvestmentWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._P2PWithdrawalWorkflow.Set(v.P2PWithdrawalWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MutualFundsInvestmentPostPaid.Set(v.MutualFundsInvestmentPostPaid, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MutualFundsRedemption.Set(v.MutualFundsRedemption, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PaymentNotificationParams.Set(v.PaymentNotificationParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PaymentSMSParams.Set(v.PaymentSMSParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IconUrls.Set(v.IconUrls, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CustomRuleDEParams.Set(v.CustomRuleDEParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._URNPaymentLimits.Set(v.URNPaymentLimits, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SavingsLedgerReconSubscriber.Set(v.SavingsLedgerReconSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AddFundsAlertParams.Set(v.AddFundsAlertParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AddFundsAlertMailingParams.Set(v.AddFundsAlertMailingParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ExecuteRecurringPaymentWithAuthWorkflow.Set(v.ExecuteRecurringPaymentWithAuthWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ExecuteRecurringPaymentWithNoAuthWorkflow.Set(v.ExecuteRecurringPaymentWithNoAuthWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RecurringPaymentPauseUnpauseWorkflow.Set(v.RecurringPaymentPauseUnpauseWorkflow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PurgeAaDataSubscriber.Set(v.PurgeAaDataSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AaTxnPurgeSubscriber.Set(v.AaTxnPurgeSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CSIS.Set(v.CSIS, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BankDownTime.Set(v.BankDownTime, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ConnectedAccountUserGroupParams.Set(v.ConnectedAccountUserGroupParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AaDataPurgeOrchestrationSubscriber.Set(v.AaDataPurgeOrchestrationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AaParams.Set(v.AaParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NameCheckParamsForAddFunds.Set(v.NameCheckParamsForAddFunds, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeclineCardTransactionsProcessingSubscriber.Set(v.DeclineCardTransactionsProcessingSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SavingsLedgerReconCacheConfig.Set(v.SavingsLedgerReconCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderCacheConfig.Set(v.OrderCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TransactionCacheConfig.Set(v.TransactionCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureFlags.Set(v.FeatureFlags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InboundNotificationParams.Set(v.InboundNotificationParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ErrorRespCodesForPermanentFailure.Set(v.ErrorRespCodesForPermanentFailure, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DataReplicationParams.Set(v.DataReplicationParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ReconRestrictedWindow.Set(v.ReconRestrictedWindow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TxnNotificationSubscriber.Set(v.TxnNotificationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DeemedTransactionUpiEnquirySubscriber.Set(v.DeemedTransactionUpiEnquirySubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AAFirstDataPullTxnSubscriber.Set(v.AAFirstDataPullTxnSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QuestSdk.Set(v.QuestSdk, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsInfo.Set(v.RewardsInfo, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._EpifiDb = v.EpifiDb
	obj._EpifiWealthDB = v.EpifiWealthDB
	obj._ConnectedAccountDB = v.ConnectedAccountDB
	obj._RedisOptions = v.RedisOptions
	obj._OrderOrchestrationPublisher = v.OrderOrchestrationPublisher
	obj._AATxnPublisher = v.AATxnPublisher
	obj._OrderUpdateEventPublisher = v.OrderUpdateEventPublisher
	obj._OrderMerchantMergeEventPublisher = v.OrderMerchantMergeEventPublisher
	obj._TxnDetailedStatusUpdateSnsPublisher = v.TxnDetailedStatusUpdateSnsPublisher
	obj._WorkflowProcessingPublisher = v.WorkflowProcessingPublisher
	obj._OrderNotificationPublisher = v.OrderNotificationPublisher
	obj._OrderCollectNotificationPublisher = v.OrderCollectNotificationPublisher
	obj._OrderNotificationFallbackPublisher = v.OrderNotificationFallbackPublisher
	obj._OrderSearchPublisher = v.OrderSearchPublisher
	obj._EventsCompletedTnCPublisher = v.EventsCompletedTnCPublisher
	obj._ProcrastinatorWorkflowPublisher = v.ProcrastinatorWorkflowPublisher
	obj._AWS = v.AWS
	obj._PayFundTransferStatusCodeJson = v.PayFundTransferStatusCodeJson
	obj._PayUpiStatusCodeJson = v.PayUpiStatusCodeJson
	obj._EnachTransactionStatusCodeJson = v.EnachTransactionStatusCodeJson
	obj._ImpsIfscCodesCsv = v.ImpsIfscCodesCsv
	obj._RtgsBlockListIfscCodesCsv = v.RtgsBlockListIfscCodesCsv
	obj._CollectVelocityLimit = v.CollectVelocityLimit
	obj._CollectVelocityWindow = v.CollectVelocityWindow
	obj._CoolOffWindow = v.CoolOffWindow
	obj._CoolOffAmountLimit = v.CoolOffAmountLimit
	obj._RudderStack = v.RudderStack
	obj._Secrets = v.Secrets
	obj._PersonMCCCode = v.PersonMCCCode
	obj._SavingsLedgerReconPublisher = v.SavingsLedgerReconPublisher
	obj._OrderEventAmountCategories = v.OrderEventAmountCategories
	obj._AaAccountPiPurgePublisher = v.AaAccountPiPurgePublisher
	obj._PaymentOrchestrationPublisher = v.PaymentOrchestrationPublisher
	obj._IntraBankEnquiryPublisher = v.IntraBankEnquiryPublisher
	obj._UPIEnquiryPublisher = v.UPIEnquiryPublisher
	obj._IMPSEnquiryPublisher = v.IMPSEnquiryPublisher
	obj._NEFTEnquiryPublisher = v.NEFTEnquiryPublisher
	obj._RTGSEnquiryPublisher = v.RTGSEnquiryPublisher
	obj._ActorPiRelationPurgePublisher = v.ActorPiRelationPurgePublisher
	obj._SignalWorkflowPublisher = v.SignalWorkflowPublisher
	obj._Events = v.Events
	obj._PartnerBankEODDelayMap = v.PartnerBankEODDelayMap
	obj._DynamicVPAParamsMap = v.DynamicVPAParamsMap
	obj._DynamicVPAV1ParamsMap = v.DynamicVPAV1ParamsMap
	obj._CampaignParams = v.CampaignParams
	obj._PaymentEnquiryParams = v.PaymentEnquiryParams
	obj._DeclineDataAwsSftpBucket = v.DeclineDataAwsSftpBucket
	obj._AaDataPurgeOrchestrationPublisher = v.AaDataPurgeOrchestrationPublisher
	obj._CardTxnStatusCodeJson = v.CardTxnStatusCodeJson
	obj._DefaultExpiryDuration = v.DefaultExpiryDuration
	obj._ReconParams = v.ReconParams
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._UpiUtrCollisionRetryCount = v.UpiUtrCollisionRetryCount
	obj._RemitterInfoSyncDelayThreshold = v.RemitterInfoSyncDelayThreshold
	obj._DelayRangeForBalanceV1 = v.DelayRangeForBalanceV1
	obj._OrderVpaVerificationPublisher = v.OrderVpaVerificationPublisher
	obj._ReVerifyAddressDelay = v.ReVerifyAddressDelay
	obj._VerifyVpaDeadlineForInboundNotification = v.VerifyVpaDeadlineForInboundNotification
	obj._B2CInitialEnquiryDelay = v.B2CInitialEnquiryDelay
	obj._TxnNotificationPublisher = v.TxnNotificationPublisher
	obj._MccsWithOnlyAllowedIntentPayments = v.MccsWithOnlyAllowedIntentPayments
	obj._DeemedTransactionUPIEnquiryPublisher = v.DeemedTransactionUPIEnquiryPublisher
	obj._B2CPaymentParams = v.B2CPaymentParams
	obj._InPaymentOrderUpdatePublisher = v.InPaymentOrderUpdatePublisher
	obj._NonTpapPspHandles = v.NonTpapPspHandles
	obj._AmountThresholdForNotification = v.AmountThresholdForNotification
	obj._InternationalQrInfoPrefix = v.InternationalQrInfoPrefix
	obj._DelimiterForRedisKey = v.DelimiterForRedisKey
	obj._ForexRefundInfo = v.ForexRefundInfo
	obj._IsStatementDataComparison = v.IsStatementDataComparison
	obj._QuestRedisOptions = v.QuestRedisOptions
	obj._IncidentManagerParams = v.IncidentManagerParams
	obj._SecureLoggingV2 = v.SecureLoggingV2
	obj._GrpcRatelimiterParams = v.GrpcRatelimiterParams
	obj._CommsConfig = v.CommsConfig
	obj._ReportFraudConfig = v.ReportFraudConfig
	obj._TransactionAttributesBasedStoryDetailsCombinations = v.TransactionAttributesBasedStoryDetailsCombinations
	obj._RupayCCRestrictedMCCs = v.RupayCCRestrictedMCCs
	return nil
}

func (obj *Config) SetAaEnrichedTxnUpdateBatchSize(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.AaEnrichedTxnUpdateBatchSize", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AaEnrichedTxnUpdateBatchSize, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AaEnrichedTxnUpdateBatchSize")
	}
	return nil
}
func (obj *Config) SetMaximumPauseTimeForRemitterInfoWorkflow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MaximumPauseTimeForRemitterInfoWorkflow", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaximumPauseTimeForRemitterInfoWorkflow, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaximumPauseTimeForRemitterInfoWorkflow")
	}
	return nil
}
func (obj *Config) SetHideAccountBalanceInNotification(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.HideAccountBalanceInNotification", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._HideAccountBalanceInNotification, 1)
	} else {
		atomic.StoreUint32(&obj._HideAccountBalanceInNotification, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "HideAccountBalanceInNotification")
	}
	return nil
}
func (obj *Config) SetIsPushingPaymentsMetricsToHealthEngineEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.IsPushingPaymentsMetricsToHealthEngineEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsPushingPaymentsMetricsToHealthEngineEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsPushingPaymentsMetricsToHealthEngineEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsPushingPaymentsMetricsToHealthEngineEnabled")
	}
	return nil
}
func (obj *Config) SetIsEmailNotificationEnable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.IsEmailNotificationEnable", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEmailNotificationEnable, 1)
	} else {
		atomic.StoreUint32(&obj._IsEmailNotificationEnable, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEmailNotificationEnable")
	}
	return nil
}
func (obj *Config) SetEnableEntitySegregation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableEntitySegregation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableEntitySegregation")
	}
	return nil
}
func (obj *Config) SetRecentOrderEligibleDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.RecentOrderEligibleDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RecentOrderEligibleDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RecentOrderEligibleDuration")
	}
	return nil
}
func (obj *Config) SetTimeoutForGetOperationalStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.TimeoutForGetOperationalStatus", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._TimeoutForGetOperationalStatus, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "TimeoutForGetOperationalStatus")
	}
	return nil
}
func (obj *Config) SetPinotQueryTimeout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.PinotQueryTimeout", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._PinotQueryTimeout, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PinotQueryTimeout")
	}
	return nil
}
func (obj *Config) SetSMSTypeToOptionVersionMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.SMSOptionVersion)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.SMSTypeToOptionVersionMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._SMSTypeToOptionVersionMap, v, path)
}

func NewApplication() (_obj *Application, _setters map[string]dynconf.SetFunc) {
	_obj = &Application{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *Application) Init() {
	newObj, _ := NewApplication()
	*obj = *newObj
}

func (obj *Application) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Application) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Application)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Application) setDynamicField(v *config.Application, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Application) setDynamicFields(v *config.Application, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *Application) setStaticFields(v *config.Application) error {

	obj._Environment = v.Environment
	obj._Name = v.Name
	obj._IsSeparateEnquiryPerProtocolEnabled = v.IsSeparateEnquiryPerProtocolEnabled
	return nil
}

func NewServer() (_obj *Server, _setters map[string]dynconf.SetFunc) {
	_obj = &Server{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *Server) Init() {
	newObj, _ := NewServer()
	*obj = *newObj
}

func (obj *Server) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Server) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Server)
	if !ok {
		return fmt.Errorf("invalid data type %v *Server", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Server) setDynamicField(v *config.Server, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Server) setDynamicFields(v *config.Server, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *Server) setStaticFields(v *config.Server) error {

	obj._Ports = v.Ports
	return nil
}

func NewPaymentProtocolDecisionParams() (_obj *PaymentProtocolDecisionParams, _setters map[string]dynconf.SetFunc) {
	_obj = &PaymentProtocolDecisionParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["totaltransactedamountlimitincooldown"] = _obj.SetTotalTransactedAmountLimitInCoolDown
	_setters["beneficiarycooldownamountlimit"] = _obj.SetBeneficiaryCoolDownAmountLimit
	_INTRAParams, _fieldSetters := NewProtocolParams()
	_obj._INTRAParams = _INTRAParams
	helper.AddFieldSetters("intraparams", _fieldSetters, _setters)
	_IMPSParams, _fieldSetters := NewProtocolParams()
	_obj._IMPSParams = _IMPSParams
	helper.AddFieldSetters("impsparams", _fieldSetters, _setters)
	_NEFTParams, _fieldSetters := NewProtocolParams()
	_obj._NEFTParams = _NEFTParams
	helper.AddFieldSetters("neftparams", _fieldSetters, _setters)
	_RTGSParams, _fieldSetters := NewProtocolParams()
	_obj._RTGSParams = _RTGSParams
	helper.AddFieldSetters("rtgsparams", _fieldSetters, _setters)
	_UPIParams, _fieldSetters := NewUpiParams()
	_obj._UPIParams = _UPIParams
	helper.AddFieldSetters("upiparams", _fieldSetters, _setters)
	_UpiPreferredPaymentProtocolAmountParams, _fieldSetters := NewSoftPreferredPaymentProtocolAmountParams()
	_obj._UpiPreferredPaymentProtocolAmountParams = _UpiPreferredPaymentProtocolAmountParams
	helper.AddFieldSetters("upipreferredpaymentprotocolamountparams", _fieldSetters, _setters)
	_UserPaymentParams, _fieldSetters := NewUserPaymentParams()
	_obj._UserPaymentParams = _UserPaymentParams
	helper.AddFieldSetters("userpaymentparams", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *PaymentProtocolDecisionParams) Init() {
	newObj, _ := NewPaymentProtocolDecisionParams()
	*obj = *newObj
}

func (obj *PaymentProtocolDecisionParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PaymentProtocolDecisionParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PaymentProtocolDecisionParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *PaymentProtocolDecisionParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PaymentProtocolDecisionParams) setDynamicField(v *config.PaymentProtocolDecisionParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "totaltransactedamountlimitincooldown":
		return obj.SetTotalTransactedAmountLimitInCoolDown(v.TotalTransactedAmountLimitInCoolDown, true, nil)
	case "beneficiarycooldownamountlimit":
		return obj.SetBeneficiaryCoolDownAmountLimit(v.BeneficiaryCoolDownAmountLimit, true, nil)
	case "intraparams":
		return obj._INTRAParams.Set(v.INTRAParams, true, path)
	case "impsparams":
		return obj._IMPSParams.Set(v.IMPSParams, true, path)
	case "neftparams":
		return obj._NEFTParams.Set(v.NEFTParams, true, path)
	case "rtgsparams":
		return obj._RTGSParams.Set(v.RTGSParams, true, path)
	case "upiparams":
		return obj._UPIParams.Set(v.UPIParams, true, path)
	case "upipreferredpaymentprotocolamountparams":
		return obj._UpiPreferredPaymentProtocolAmountParams.Set(v.UpiPreferredPaymentProtocolAmountParams, true, path)
	case "userpaymentparams":
		return obj._UserPaymentParams.Set(v.UserPaymentParams, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PaymentProtocolDecisionParams) setDynamicFields(v *config.PaymentProtocolDecisionParams, dynamic bool, path []string) (err error) {

	err = obj.SetTotalTransactedAmountLimitInCoolDown(v.TotalTransactedAmountLimitInCoolDown, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBeneficiaryCoolDownAmountLimit(v.BeneficiaryCoolDownAmountLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._INTRAParams.Set(v.INTRAParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IMPSParams.Set(v.IMPSParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NEFTParams.Set(v.NEFTParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RTGSParams.Set(v.RTGSParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UPIParams.Set(v.UPIParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UpiPreferredPaymentProtocolAmountParams.Set(v.UpiPreferredPaymentProtocolAmountParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UserPaymentParams.Set(v.UserPaymentParams, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PaymentProtocolDecisionParams) setStaticFields(v *config.PaymentProtocolDecisionParams) error {

	obj._UPILimitExceptions = v.UPILimitExceptions
	obj._ProfileUpdateCoolDownWindow = v.ProfileUpdateCoolDownWindow
	return nil
}

func (obj *PaymentProtocolDecisionParams) SetTotalTransactedAmountLimitInCoolDown(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *PaymentProtocolDecisionParams.TotalTransactedAmountLimitInCoolDown", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._TotalTransactedAmountLimitInCoolDown, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "TotalTransactedAmountLimitInCoolDown")
	}
	return nil
}
func (obj *PaymentProtocolDecisionParams) SetBeneficiaryCoolDownAmountLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *PaymentProtocolDecisionParams.BeneficiaryCoolDownAmountLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._BeneficiaryCoolDownAmountLimit, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BeneficiaryCoolDownAmountLimit")
	}
	return nil
}

func NewProtocolParams() (_obj *ProtocolParams, _setters map[string]dynconf.SetFunc) {
	_obj = &ProtocolParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minamount"] = _obj.SetMinAmount
	_setters["maxamount"] = _obj.SetMaxAmount
	_setters["totaltransactedamountlimitincooldown"] = _obj.SetTotalTransactedAmountLimitInCoolDown
	_setters["transactioncountlimit"] = _obj.SetTransactionCountLimit
	_ProtocolRestrictedWindow, _fieldSetters := NewRestrictedWindow()
	_obj._ProtocolRestrictedWindow = _ProtocolRestrictedWindow
	helper.AddFieldSetters("protocolrestrictedwindow", _fieldSetters, _setters)
	_ProtocolDailyDowntime, _fieldSetters := NewProtocolDailyDowntime()
	_obj._ProtocolDailyDowntime = _ProtocolDailyDowntime
	helper.AddFieldSetters("protocoldailydowntime", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *ProtocolParams) Init() {
	newObj, _ := NewProtocolParams()
	*obj = *newObj
}

func (obj *ProtocolParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ProtocolParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ProtocolParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProtocolParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ProtocolParams) setDynamicField(v *config.ProtocolParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minamount":
		return obj.SetMinAmount(v.MinAmount, true, nil)
	case "maxamount":
		return obj.SetMaxAmount(v.MaxAmount, true, nil)
	case "totaltransactedamountlimitincooldown":
		return obj.SetTotalTransactedAmountLimitInCoolDown(v.TotalTransactedAmountLimitInCoolDown, true, nil)
	case "transactioncountlimit":
		return obj.SetTransactionCountLimit(v.TransactionCountLimit, true, nil)
	case "protocolrestrictedwindow":
		return obj._ProtocolRestrictedWindow.Set(v.ProtocolRestrictedWindow, true, path)
	case "protocoldailydowntime":
		return obj._ProtocolDailyDowntime.Set(v.ProtocolDailyDowntime, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ProtocolParams) setDynamicFields(v *config.ProtocolParams, dynamic bool, path []string) (err error) {

	err = obj.SetMinAmount(v.MinAmount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxAmount(v.MaxAmount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTotalTransactedAmountLimitInCoolDown(v.TotalTransactedAmountLimitInCoolDown, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTransactionCountLimit(v.TransactionCountLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._ProtocolRestrictedWindow.Set(v.ProtocolRestrictedWindow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProtocolDailyDowntime.Set(v.ProtocolDailyDowntime, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ProtocolParams) setStaticFields(v *config.ProtocolParams) error {

	obj._ProfileUpdateCoolDownWindow = v.ProfileUpdateCoolDownWindow
	obj._LimitWindow = v.LimitWindow
	obj._IntraCoolOffNotApplicablePis = v.IntraCoolOffNotApplicablePis
	return nil
}

func (obj *ProtocolParams) SetMinAmount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProtocolParams.MinAmount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinAmount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinAmount")
	}
	return nil
}
func (obj *ProtocolParams) SetMaxAmount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProtocolParams.MaxAmount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxAmount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxAmount")
	}
	return nil
}
func (obj *ProtocolParams) SetTotalTransactedAmountLimitInCoolDown(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProtocolParams.TotalTransactedAmountLimitInCoolDown", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._TotalTransactedAmountLimitInCoolDown, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "TotalTransactedAmountLimitInCoolDown")
	}
	return nil
}
func (obj *ProtocolParams) SetTransactionCountLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProtocolParams.TransactionCountLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._TransactionCountLimit, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "TransactionCountLimit")
	}
	return nil
}

func NewRestrictedWindow() (_obj *RestrictedWindow, _setters map[string]dynconf.SetFunc) {
	_obj = &RestrictedWindow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["fromdatetime"] = _obj.SetFromDateTime
	_obj._FromDateTimeMutex = &sync.RWMutex{}
	_setters["todatetime"] = _obj.SetToDateTime
	_obj._ToDateTimeMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *RestrictedWindow) Init() {
	newObj, _ := NewRestrictedWindow()
	*obj = *newObj
}

func (obj *RestrictedWindow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RestrictedWindow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RestrictedWindow)
	if !ok {
		return fmt.Errorf("invalid data type %v *RestrictedWindow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RestrictedWindow) setDynamicField(v *config.RestrictedWindow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "fromdatetime":
		return obj.SetFromDateTime(v.FromDateTime, true, nil)
	case "todatetime":
		return obj.SetToDateTime(v.ToDateTime, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RestrictedWindow) setDynamicFields(v *config.RestrictedWindow, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFromDateTime(v.FromDateTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetToDateTime(v.ToDateTime, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RestrictedWindow) setStaticFields(v *config.RestrictedWindow) error {

	obj._Timezone = v.Timezone
	obj._DateTimeFormat = v.DateTimeFormat
	return nil
}

func (obj *RestrictedWindow) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RestrictedWindow.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *RestrictedWindow) SetFromDateTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RestrictedWindow.FromDateTime", reflect.TypeOf(val))
	}
	obj._FromDateTimeMutex.Lock()
	defer obj._FromDateTimeMutex.Unlock()
	obj._FromDateTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FromDateTime")
	}
	return nil
}
func (obj *RestrictedWindow) SetToDateTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RestrictedWindow.ToDateTime", reflect.TypeOf(val))
	}
	obj._ToDateTimeMutex.Lock()
	defer obj._ToDateTimeMutex.Unlock()
	obj._ToDateTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ToDateTime")
	}
	return nil
}

func NewProtocolDailyDowntime() (_obj *ProtocolDailyDowntime, _setters map[string]dynconf.SetFunc) {
	_obj = &ProtocolDailyDowntime{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["starttime"] = _obj.SetStartTime
	_obj._StartTimeMutex = &sync.RWMutex{}
	_setters["endtime"] = _obj.SetEndTime
	_obj._EndTimeMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ProtocolDailyDowntime) Init() {
	newObj, _ := NewProtocolDailyDowntime()
	*obj = *newObj
}

func (obj *ProtocolDailyDowntime) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ProtocolDailyDowntime) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ProtocolDailyDowntime)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProtocolDailyDowntime", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ProtocolDailyDowntime) setDynamicField(v *config.ProtocolDailyDowntime, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "starttime":
		return obj.SetStartTime(v.StartTime, true, nil)
	case "endtime":
		return obj.SetEndTime(v.EndTime, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ProtocolDailyDowntime) setDynamicFields(v *config.ProtocolDailyDowntime, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetStartTime(v.StartTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEndTime(v.EndTime, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ProtocolDailyDowntime) setStaticFields(v *config.ProtocolDailyDowntime) error {

	return nil
}

func (obj *ProtocolDailyDowntime) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProtocolDailyDowntime.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *ProtocolDailyDowntime) SetStartTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProtocolDailyDowntime.StartTime", reflect.TypeOf(val))
	}
	obj._StartTimeMutex.Lock()
	defer obj._StartTimeMutex.Unlock()
	obj._StartTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "StartTime")
	}
	return nil
}
func (obj *ProtocolDailyDowntime) SetEndTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProtocolDailyDowntime.EndTime", reflect.TypeOf(val))
	}
	obj._EndTimeMutex.Lock()
	defer obj._EndTimeMutex.Unlock()
	obj._EndTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "EndTime")
	}
	return nil
}

func NewUpiParams() (_obj *UpiParams, _setters map[string]dynconf.SetFunc) {
	_obj = &UpiParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["upiminamount"] = _obj.SetUpiMinAmount
	_setters["upimaxamount"] = _obj.SetUpiMaxAmount
	_setters["upitotaltransactedamountlimit"] = _obj.SetUpiTotalTransactedAmountLimit
	_setters["upitransactioncountlimit"] = _obj.SetUpiTransactionCountLimit
	_setters["upinewusertotaltransactedamountlimit"] = _obj.SetUpiNewUserTotalTransactedAmountLimit
	_setters["upicooldownperiodtotaltransactedamountlimit"] = _obj.SetUpiCoolDownPeriodTotalTransactedAmountLimit
	_setters["upipinresettotaltransactedamountlimit"] = _obj.SetUpiPinResetTotalTransactedAmountLimit
	_UPIRestrictedWindow, _fieldSetters := NewRestrictedWindow()
	_obj._UPIRestrictedWindow = _UPIRestrictedWindow
	helper.AddFieldSetters("upirestrictedwindow", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *UpiParams) Init() {
	newObj, _ := NewUpiParams()
	*obj = *newObj
}

func (obj *UpiParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UpiParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UpiParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *UpiParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UpiParams) setDynamicField(v *config.UpiParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "upiminamount":
		return obj.SetUpiMinAmount(v.UpiMinAmount, true, nil)
	case "upimaxamount":
		return obj.SetUpiMaxAmount(v.UpiMaxAmount, true, nil)
	case "upitotaltransactedamountlimit":
		return obj.SetUpiTotalTransactedAmountLimit(v.UpiTotalTransactedAmountLimit, true, nil)
	case "upitransactioncountlimit":
		return obj.SetUpiTransactionCountLimit(v.UpiTransactionCountLimit, true, nil)
	case "upinewusertotaltransactedamountlimit":
		return obj.SetUpiNewUserTotalTransactedAmountLimit(v.UpiNewUserTotalTransactedAmountLimit, true, nil)
	case "upicooldownperiodtotaltransactedamountlimit":
		return obj.SetUpiCoolDownPeriodTotalTransactedAmountLimit(v.UpiCoolDownPeriodTotalTransactedAmountLimit, true, nil)
	case "upipinresettotaltransactedamountlimit":
		return obj.SetUpiPinResetTotalTransactedAmountLimit(v.UpiPinResetTotalTransactedAmountLimit, true, nil)
	case "upirestrictedwindow":
		return obj._UPIRestrictedWindow.Set(v.UPIRestrictedWindow, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UpiParams) setDynamicFields(v *config.UpiParams, dynamic bool, path []string) (err error) {

	err = obj.SetUpiMinAmount(v.UpiMinAmount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUpiMaxAmount(v.UpiMaxAmount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUpiTotalTransactedAmountLimit(v.UpiTotalTransactedAmountLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUpiTransactionCountLimit(v.UpiTransactionCountLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUpiNewUserTotalTransactedAmountLimit(v.UpiNewUserTotalTransactedAmountLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUpiCoolDownPeriodTotalTransactedAmountLimit(v.UpiCoolDownPeriodTotalTransactedAmountLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUpiPinResetTotalTransactedAmountLimit(v.UpiPinResetTotalTransactedAmountLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._UPIRestrictedWindow.Set(v.UPIRestrictedWindow, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UpiParams) setStaticFields(v *config.UpiParams) error {

	obj._UpiLimitWindow = v.UpiLimitWindow
	obj._UpiPinResetLimitWindow = v.UpiPinResetLimitWindow
	obj._UpiProfileUpdateCoolDownWindow = v.UpiProfileUpdateCoolDownWindow
	obj._UpiProfileUpdateCoolDownTriggerDurationLimit = v.UpiProfileUpdateCoolDownTriggerDurationLimit
	obj._UpiProfileUpdateAfuSummariesFetchDuration = v.UpiProfileUpdateAfuSummariesFetchDuration
	return nil
}

func (obj *UpiParams) SetUpiMinAmount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *UpiParams.UpiMinAmount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._UpiMinAmount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "UpiMinAmount")
	}
	return nil
}
func (obj *UpiParams) SetUpiMaxAmount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *UpiParams.UpiMaxAmount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._UpiMaxAmount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "UpiMaxAmount")
	}
	return nil
}
func (obj *UpiParams) SetUpiTotalTransactedAmountLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *UpiParams.UpiTotalTransactedAmountLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._UpiTotalTransactedAmountLimit, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "UpiTotalTransactedAmountLimit")
	}
	return nil
}
func (obj *UpiParams) SetUpiTransactionCountLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *UpiParams.UpiTransactionCountLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._UpiTransactionCountLimit, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "UpiTransactionCountLimit")
	}
	return nil
}
func (obj *UpiParams) SetUpiNewUserTotalTransactedAmountLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *UpiParams.UpiNewUserTotalTransactedAmountLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._UpiNewUserTotalTransactedAmountLimit, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "UpiNewUserTotalTransactedAmountLimit")
	}
	return nil
}
func (obj *UpiParams) SetUpiCoolDownPeriodTotalTransactedAmountLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *UpiParams.UpiCoolDownPeriodTotalTransactedAmountLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._UpiCoolDownPeriodTotalTransactedAmountLimit, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "UpiCoolDownPeriodTotalTransactedAmountLimit")
	}
	return nil
}
func (obj *UpiParams) SetUpiPinResetTotalTransactedAmountLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *UpiParams.UpiPinResetTotalTransactedAmountLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._UpiPinResetTotalTransactedAmountLimit, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "UpiPinResetTotalTransactedAmountLimit")
	}
	return nil
}

func NewUPILimitExceptions() (_obj *UPILimitExceptions, _setters map[string]dynconf.SetFunc) {
	_obj = &UPILimitExceptions{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *UPILimitExceptions) Init() {
	newObj, _ := NewUPILimitExceptions()
	*obj = *newObj
}

func (obj *UPILimitExceptions) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UPILimitExceptions) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UPILimitExceptions)
	if !ok {
		return fmt.Errorf("invalid data type %v *UPILimitExceptions", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UPILimitExceptions) setDynamicField(v *config.UPILimitExceptions, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UPILimitExceptions) setDynamicFields(v *config.UPILimitExceptions, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *UPILimitExceptions) setStaticFields(v *config.UPILimitExceptions) error {

	obj._MCC = v.MCC
	obj._P2PInitiationMode = v.P2PInitiationMode
	obj._P2MInitiationMode = v.P2MInitiationMode
	obj._PurposeCode = v.PurposeCode
	return nil
}

func NewSoftPreferredPaymentProtocolAmountParams() (_obj *SoftPreferredPaymentProtocolAmountParams, _setters map[string]dynconf.SetFunc) {
	_obj = &SoftPreferredPaymentProtocolAmountParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minamount"] = _obj.SetMinAmount
	_setters["maxamount"] = _obj.SetMaxAmount
	_setters["enable"] = _obj.SetEnable
	return _obj, _setters
}

func (obj *SoftPreferredPaymentProtocolAmountParams) Init() {
	newObj, _ := NewSoftPreferredPaymentProtocolAmountParams()
	*obj = *newObj
}

func (obj *SoftPreferredPaymentProtocolAmountParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SoftPreferredPaymentProtocolAmountParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SoftPreferredPaymentProtocolAmountParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *SoftPreferredPaymentProtocolAmountParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SoftPreferredPaymentProtocolAmountParams) setDynamicField(v *config.SoftPreferredPaymentProtocolAmountParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minamount":
		return obj.SetMinAmount(v.MinAmount, true, nil)
	case "maxamount":
		return obj.SetMaxAmount(v.MaxAmount, true, nil)
	case "enable":
		return obj.SetEnable(v.Enable, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SoftPreferredPaymentProtocolAmountParams) setDynamicFields(v *config.SoftPreferredPaymentProtocolAmountParams, dynamic bool, path []string) (err error) {

	err = obj.SetMinAmount(v.MinAmount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxAmount(v.MaxAmount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnable(v.Enable, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SoftPreferredPaymentProtocolAmountParams) setStaticFields(v *config.SoftPreferredPaymentProtocolAmountParams) error {

	return nil
}

func (obj *SoftPreferredPaymentProtocolAmountParams) SetMinAmount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *SoftPreferredPaymentProtocolAmountParams.MinAmount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinAmount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinAmount")
	}
	return nil
}
func (obj *SoftPreferredPaymentProtocolAmountParams) SetMaxAmount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *SoftPreferredPaymentProtocolAmountParams.MaxAmount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxAmount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxAmount")
	}
	return nil
}
func (obj *SoftPreferredPaymentProtocolAmountParams) SetEnable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *SoftPreferredPaymentProtocolAmountParams.Enable", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Enable, 1)
	} else {
		atomic.StoreUint32(&obj._Enable, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Enable")
	}
	return nil
}

func NewUserPaymentParams() (_obj *UserPaymentParams, _setters map[string]dynconf.SetFunc) {
	_obj = &UserPaymentParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_TotalTransactionAmountLimit, _fieldSetters := NewLimitByDuration()
	_obj._TotalTransactionAmountLimit = _TotalTransactionAmountLimit
	helper.AddFieldSetters("totaltransactionamountlimit", _fieldSetters, _setters)
	_NewAccountTransactionAmountLimit, _fieldSetters := NewNewAccountLimitByDuration()
	_obj._NewAccountTransactionAmountLimit = _NewAccountTransactionAmountLimit
	helper.AddFieldSetters("newaccounttransactionamountlimit", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *UserPaymentParams) Init() {
	newObj, _ := NewUserPaymentParams()
	*obj = *newObj
}

func (obj *UserPaymentParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UserPaymentParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UserPaymentParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserPaymentParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UserPaymentParams) setDynamicField(v *config.UserPaymentParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "totaltransactionamountlimit":
		return obj._TotalTransactionAmountLimit.Set(v.TotalTransactionAmountLimit, true, path)
	case "newaccounttransactionamountlimit":
		return obj._NewAccountTransactionAmountLimit.Set(v.NewAccountTransactionAmountLimit, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UserPaymentParams) setDynamicFields(v *config.UserPaymentParams, dynamic bool, path []string) (err error) {

	err = obj._TotalTransactionAmountLimit.Set(v.TotalTransactionAmountLimit, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NewAccountTransactionAmountLimit.Set(v.NewAccountTransactionAmountLimit, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UserPaymentParams) setStaticFields(v *config.UserPaymentParams) error {

	return nil
}

func NewLimitByDuration() (_obj *LimitByDuration, _setters map[string]dynconf.SetFunc) {
	_obj = &LimitByDuration{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["amountlimit"] = _obj.SetAmountLimit
	return _obj, _setters
}

func (obj *LimitByDuration) Init() {
	newObj, _ := NewLimitByDuration()
	*obj = *newObj
}

func (obj *LimitByDuration) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LimitByDuration) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.LimitByDuration)
	if !ok {
		return fmt.Errorf("invalid data type %v *LimitByDuration", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LimitByDuration) setDynamicField(v *config.LimitByDuration, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "amountlimit":
		return obj.SetAmountLimit(v.AmountLimit, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LimitByDuration) setDynamicFields(v *config.LimitByDuration, dynamic bool, path []string) (err error) {

	err = obj.SetAmountLimit(v.AmountLimit, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LimitByDuration) setStaticFields(v *config.LimitByDuration) error {

	obj._TimeWindow = v.TimeWindow
	return nil
}

func (obj *LimitByDuration) SetAmountLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *LimitByDuration.AmountLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._AmountLimit, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AmountLimit")
	}
	return nil
}

func NewNewAccountLimitByDuration() (_obj *NewAccountLimitByDuration, _setters map[string]dynconf.SetFunc) {
	_obj = &NewAccountLimitByDuration{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["amountlimit"] = _obj.SetAmountLimit
	_setters["accountagelimitthreshold"] = _obj.SetAccountAgeLimitThreshold
	return _obj, _setters
}

func (obj *NewAccountLimitByDuration) Init() {
	newObj, _ := NewNewAccountLimitByDuration()
	*obj = *newObj
}

func (obj *NewAccountLimitByDuration) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NewAccountLimitByDuration) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NewAccountLimitByDuration)
	if !ok {
		return fmt.Errorf("invalid data type %v *NewAccountLimitByDuration", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NewAccountLimitByDuration) setDynamicField(v *config.NewAccountLimitByDuration, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "amountlimit":
		return obj.SetAmountLimit(v.AmountLimit, true, nil)
	case "accountagelimitthreshold":
		return obj.SetAccountAgeLimitThreshold(v.AccountAgeLimitThreshold, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NewAccountLimitByDuration) setDynamicFields(v *config.NewAccountLimitByDuration, dynamic bool, path []string) (err error) {

	err = obj.SetAmountLimit(v.AmountLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAccountAgeLimitThreshold(v.AccountAgeLimitThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *NewAccountLimitByDuration) setStaticFields(v *config.NewAccountLimitByDuration) error {

	return nil
}

func (obj *NewAccountLimitByDuration) SetAmountLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *NewAccountLimitByDuration.AmountLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._AmountLimit, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AmountLimit")
	}
	return nil
}
func (obj *NewAccountLimitByDuration) SetAccountAgeLimitThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *NewAccountLimitByDuration.AccountAgeLimitThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AccountAgeLimitThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AccountAgeLimitThreshold")
	}
	return nil
}

func NewRuleEngineParams() (_obj *RuleEngineParams, _setters map[string]dynconf.SetFunc) {
	_obj = &RuleEngineParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["salienceintra"] = _obj.SetSalienceINTRA
	_setters["salienceupi"] = _obj.SetSalienceUPI
	_setters["salienceimps"] = _obj.SetSalienceIMPS
	_setters["saliencertgs"] = _obj.SetSalienceRTGS
	_setters["salienceneft"] = _obj.SetSalienceNEFT
	_setters["bumpedsalience"] = _obj.SetBumpedSalience
	_setters["secondhighestbumpedsalience"] = _obj.SetSecondHighestBumpedSalience
	return _obj, _setters
}

func (obj *RuleEngineParams) Init() {
	newObj, _ := NewRuleEngineParams()
	*obj = *newObj
}

func (obj *RuleEngineParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RuleEngineParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RuleEngineParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *RuleEngineParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RuleEngineParams) setDynamicField(v *config.RuleEngineParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "salienceintra":
		return obj.SetSalienceINTRA(v.SalienceINTRA, true, nil)
	case "salienceupi":
		return obj.SetSalienceUPI(v.SalienceUPI, true, nil)
	case "salienceimps":
		return obj.SetSalienceIMPS(v.SalienceIMPS, true, nil)
	case "saliencertgs":
		return obj.SetSalienceRTGS(v.SalienceRTGS, true, nil)
	case "salienceneft":
		return obj.SetSalienceNEFT(v.SalienceNEFT, true, nil)
	case "bumpedsalience":
		return obj.SetBumpedSalience(v.BumpedSalience, true, nil)
	case "secondhighestbumpedsalience":
		return obj.SetSecondHighestBumpedSalience(v.SecondHighestBumpedSalience, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RuleEngineParams) setDynamicFields(v *config.RuleEngineParams, dynamic bool, path []string) (err error) {

	err = obj.SetSalienceINTRA(v.SalienceINTRA, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSalienceUPI(v.SalienceUPI, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSalienceIMPS(v.SalienceIMPS, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSalienceRTGS(v.SalienceRTGS, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSalienceNEFT(v.SalienceNEFT, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBumpedSalience(v.BumpedSalience, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSecondHighestBumpedSalience(v.SecondHighestBumpedSalience, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RuleEngineParams) setStaticFields(v *config.RuleEngineParams) error {

	return nil
}

func (obj *RuleEngineParams) SetSalienceINTRA(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RuleEngineParams.SalienceINTRA", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SalienceINTRA, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SalienceINTRA")
	}
	return nil
}
func (obj *RuleEngineParams) SetSalienceUPI(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RuleEngineParams.SalienceUPI", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SalienceUPI, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SalienceUPI")
	}
	return nil
}
func (obj *RuleEngineParams) SetSalienceIMPS(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RuleEngineParams.SalienceIMPS", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SalienceIMPS, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SalienceIMPS")
	}
	return nil
}
func (obj *RuleEngineParams) SetSalienceRTGS(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RuleEngineParams.SalienceRTGS", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SalienceRTGS, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SalienceRTGS")
	}
	return nil
}
func (obj *RuleEngineParams) SetSalienceNEFT(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RuleEngineParams.SalienceNEFT", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SalienceNEFT, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SalienceNEFT")
	}
	return nil
}
func (obj *RuleEngineParams) SetBumpedSalience(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RuleEngineParams.BumpedSalience", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._BumpedSalience, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BumpedSalience")
	}
	return nil
}
func (obj *RuleEngineParams) SetSecondHighestBumpedSalience(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RuleEngineParams.SecondHighestBumpedSalience", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SecondHighestBumpedSalience, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SecondHighestBumpedSalience")
	}
	return nil
}

func NewOrderReceipt() (_obj *OrderReceipt, _setters map[string]dynconf.SetFunc) {
	_obj = &OrderReceipt{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *OrderReceipt) Init() {
	newObj, _ := NewOrderReceipt()
	*obj = *newObj
}

func (obj *OrderReceipt) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OrderReceipt) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OrderReceipt)
	if !ok {
		return fmt.Errorf("invalid data type %v *OrderReceipt", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OrderReceipt) setDynamicField(v *config.OrderReceipt, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OrderReceipt) setDynamicFields(v *config.OrderReceipt, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *OrderReceipt) setStaticFields(v *config.OrderReceipt) error {

	obj._ReceiptHead = v.ReceiptHead
	obj._ConsolidatedStages = v.ConsolidatedStages
	obj._FundTransferStages = v.FundTransferStages
	obj._AddFundsStages = v.AddFundsStages
	obj._FundTransferWithReversalStages = v.FundTransferWithReversalStages
	obj._AddFundsWithReversalStages = v.AddFundsWithReversalStages
	obj._PaymentDetailKey = v.PaymentDetailKey
	obj._DisputeDescription = v.DisputeDescription
	obj._OrderReceiptProtocolLevelBanner = v.OrderReceiptProtocolLevelBanner
	obj._PaymentStatusPendingBanner = v.PaymentStatusPendingBanner
	obj._FaqInfo = v.FaqInfo
	obj._GetHelpInfo = v.GetHelpInfo
	obj._RaiseDisputeInfo = v.RaiseDisputeInfo
	obj._UserCautionInfo = v.UserCautionInfo
	obj._ChatWithUsInfo = v.ChatWithUsInfo
	obj._EcsEnachChargesBanner = v.EcsEnachChargesBanner
	obj._OffAppEnachBanner = v.OffAppEnachBanner
	obj._RemarksForEcsEnachCharges = v.RemarksForEcsEnachCharges
	obj._AtmWithdrawalUserCautionInfo = v.AtmWithdrawalUserCautionInfo
	obj._ForexMarkupFeesStages = v.ForexMarkupFeesStages
	obj._OrderReceiptDCAmcChargesInfoBanner = v.OrderReceiptDCAmcChargesInfoBanner
	return nil
}

func NewReceiptHead() (_obj *ReceiptHead, _setters map[string]dynconf.SetFunc) {
	_obj = &ReceiptHead{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *ReceiptHead) Init() {
	newObj, _ := NewReceiptHead()
	*obj = *newObj
}

func (obj *ReceiptHead) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ReceiptHead) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ReceiptHead)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReceiptHead", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ReceiptHead) setDynamicField(v *config.ReceiptHead, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ReceiptHead) setDynamicFields(v *config.ReceiptHead, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *ReceiptHead) setStaticFields(v *config.ReceiptHead) error {

	obj._CreditTitle = v.CreditTitle
	obj._DebitTitle = v.DebitTitle
	obj._FixedDepositIconURL = v.FixedDepositIconURL
	obj._SmartDepositIconURL = v.SmartDepositIconURL
	obj._PayBGColourCode = v.PayBGColourCode
	obj._DepositBGColourCode = v.DepositBGColourCode
	return nil
}

func NewConsolidatedStages() (_obj *ConsolidatedStages, _setters map[string]dynconf.SetFunc) {
	_obj = &ConsolidatedStages{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *ConsolidatedStages) Init() {
	newObj, _ := NewConsolidatedStages()
	*obj = *newObj
}

func (obj *ConsolidatedStages) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ConsolidatedStages) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ConsolidatedStages)
	if !ok {
		return fmt.Errorf("invalid data type %v *ConsolidatedStages", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ConsolidatedStages) setDynamicField(v *config.ConsolidatedStages, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ConsolidatedStages) setDynamicFields(v *config.ConsolidatedStages, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *ConsolidatedStages) setStaticFields(v *config.ConsolidatedStages) error {

	obj._PaymentSuccess = v.PaymentSuccess
	obj._PaymentInProgress = v.PaymentInProgress
	obj._PaymentFailed = v.PaymentFailed
	obj._CollectCancelled = v.CollectCancelled
	obj._CollectDeclined = v.CollectDeclined
	obj._PaymentInitiated = v.PaymentInitiated
	obj._CollectExpired = v.CollectExpired
	obj._PaymentReversed = v.PaymentReversed
	return nil
}

func NewConsolidatedStage() (_obj *ConsolidatedStage, _setters map[string]dynconf.SetFunc) {
	_obj = &ConsolidatedStage{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *ConsolidatedStage) Init() {
	newObj, _ := NewConsolidatedStage()
	*obj = *newObj
}

func (obj *ConsolidatedStage) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ConsolidatedStage) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ConsolidatedStage)
	if !ok {
		return fmt.Errorf("invalid data type %v *ConsolidatedStage", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ConsolidatedStage) setDynamicField(v *config.ConsolidatedStage, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ConsolidatedStage) setDynamicFields(v *config.ConsolidatedStage, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *ConsolidatedStage) setStaticFields(v *config.ConsolidatedStage) error {

	obj._Description = v.Description
	obj._IsTimeStampEnabled = v.IsTimeStampEnabled
	obj._TimeLayout = v.TimeLayout
	obj._TimeStampStringFormat = v.TimeStampStringFormat
	return nil
}

func NewStages() (_obj *Stages, _setters map[string]dynconf.SetFunc) {
	_obj = &Stages{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *Stages) Init() {
	newObj, _ := NewStages()
	*obj = *newObj
}

func (obj *Stages) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Stages) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Stages)
	if !ok {
		return fmt.Errorf("invalid data type %v *Stages", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Stages) setDynamicField(v *config.Stages, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Stages) setDynamicFields(v *config.Stages, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *Stages) setStaticFields(v *config.Stages) error {

	obj._StageDescriptions = v.StageDescriptions
	obj._DescriptionTimeLayout = v.DescriptionTimeLayout
	return nil
}

func NewPaymentDetailKey() (_obj *PaymentDetailKey, _setters map[string]dynconf.SetFunc) {
	_obj = &PaymentDetailKey{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *PaymentDetailKey) Init() {
	newObj, _ := NewPaymentDetailKey()
	*obj = *newObj
}

func (obj *PaymentDetailKey) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PaymentDetailKey) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PaymentDetailKey)
	if !ok {
		return fmt.Errorf("invalid data type %v *PaymentDetailKey", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PaymentDetailKey) setDynamicField(v *config.PaymentDetailKey, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PaymentDetailKey) setDynamicFields(v *config.PaymentDetailKey, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *PaymentDetailKey) setStaticFields(v *config.PaymentDetailKey) error {

	obj._FromActorKey = v.FromActorKey
	obj._ToActorKey = v.ToActorKey
	obj._ProtocolTransactionIdKey = v.ProtocolTransactionIdKey
	obj._FiOrderIdKey = v.FiOrderIdKey
	obj._PaymentProtocolKey = v.PaymentProtocolKey
	obj._FromActorNameKey = v.FromActorNameKey
	obj._ToActorNameKey = v.ToActorNameKey
	obj._TransactionRemark = v.TransactionRemark
	obj._InternationalPaymentMarkupKey = v.InternationalPaymentMarkupKey
	obj._InternationalPaymentConversionRateKey = v.InternationalPaymentConversionRateKey
	obj._RemarksKey = v.RemarksKey
	return nil
}

func NewDisputeDescription() (_obj *DisputeDescription, _setters map[string]dynconf.SetFunc) {
	_obj = &DisputeDescription{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *DisputeDescription) Init() {
	newObj, _ := NewDisputeDescription()
	*obj = *newObj
}

func (obj *DisputeDescription) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DisputeDescription) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DisputeDescription)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisputeDescription", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DisputeDescription) setDynamicField(v *config.DisputeDescription, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DisputeDescription) setDynamicFields(v *config.DisputeDescription, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *DisputeDescription) setStaticFields(v *config.DisputeDescription) error {

	obj._Desc = v.Desc
	obj._TimeLayout = v.TimeLayout
	return nil
}

func NewOrderReceiptProtocolLevelBanner() (_obj *OrderReceiptProtocolLevelBanner, _setters map[string]dynconf.SetFunc) {
	_obj = &OrderReceiptProtocolLevelBanner{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *OrderReceiptProtocolLevelBanner) Init() {
	newObj, _ := NewOrderReceiptProtocolLevelBanner()
	*obj = *newObj
}

func (obj *OrderReceiptProtocolLevelBanner) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OrderReceiptProtocolLevelBanner) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OrderReceiptProtocolLevelBanner)
	if !ok {
		return fmt.Errorf("invalid data type %v *OrderReceiptProtocolLevelBanner", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OrderReceiptProtocolLevelBanner) setDynamicField(v *config.OrderReceiptProtocolLevelBanner, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OrderReceiptProtocolLevelBanner) setDynamicFields(v *config.OrderReceiptProtocolLevelBanner, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *OrderReceiptProtocolLevelBanner) setStaticFields(v *config.OrderReceiptProtocolLevelBanner) error {

	obj._NeftTxnInfoBanner = v.NeftTxnInfoBanner
	obj._ChequeTxnInfoBanner = v.ChequeTxnInfoBanner
	return nil
}

func NewPaymentProtocolInfoBanner() (_obj *PaymentProtocolInfoBanner, _setters map[string]dynconf.SetFunc) {
	_obj = &PaymentProtocolInfoBanner{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *PaymentProtocolInfoBanner) Init() {
	newObj, _ := NewPaymentProtocolInfoBanner()
	*obj = *newObj
}

func (obj *PaymentProtocolInfoBanner) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PaymentProtocolInfoBanner) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PaymentProtocolInfoBanner)
	if !ok {
		return fmt.Errorf("invalid data type %v *PaymentProtocolInfoBanner", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PaymentProtocolInfoBanner) setDynamicField(v *config.PaymentProtocolInfoBanner, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PaymentProtocolInfoBanner) setDynamicFields(v *config.PaymentProtocolInfoBanner, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *PaymentProtocolInfoBanner) setStaticFields(v *config.PaymentProtocolInfoBanner) error {

	obj._InfoString = v.InfoString
	obj._Window = v.Window
	return nil
}

func NewPaymentStatusPendingBanner() (_obj *PaymentStatusPendingBanner, _setters map[string]dynconf.SetFunc) {
	_obj = &PaymentStatusPendingBanner{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *PaymentStatusPendingBanner) Init() {
	newObj, _ := NewPaymentStatusPendingBanner()
	*obj = *newObj
}

func (obj *PaymentStatusPendingBanner) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PaymentStatusPendingBanner) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PaymentStatusPendingBanner)
	if !ok {
		return fmt.Errorf("invalid data type %v *PaymentStatusPendingBanner", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PaymentStatusPendingBanner) setDynamicField(v *config.PaymentStatusPendingBanner, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PaymentStatusPendingBanner) setDynamicFields(v *config.PaymentStatusPendingBanner, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *PaymentStatusPendingBanner) setStaticFields(v *config.PaymentStatusPendingBanner) error {

	obj._InfoString = v.InfoString
	obj._FontColour = v.FontColour
	obj._BgColour = v.BgColour
	obj._FontStyle = v.FontStyle
	return nil
}

func NewIconTextComponentInfo() (_obj *IconTextComponentInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &IconTextComponentInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *IconTextComponentInfo) Init() {
	newObj, _ := NewIconTextComponentInfo()
	*obj = *newObj
}

func (obj *IconTextComponentInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IconTextComponentInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IconTextComponentInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *IconTextComponentInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IconTextComponentInfo) setDynamicField(v *config.IconTextComponentInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IconTextComponentInfo) setDynamicFields(v *config.IconTextComponentInfo, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *IconTextComponentInfo) setStaticFields(v *config.IconTextComponentInfo) error {

	obj._ImageUrl = v.ImageUrl
	obj._ImageHeight = v.ImageHeight
	obj._ImageWidth = v.ImageWidth
	obj._Description = v.Description
	obj._LeftImgTxtPadding = v.LeftImgTxtPadding
	obj._ContainerLeftPadding = v.ContainerLeftPadding
	obj._ContainerRightPadding = v.ContainerRightPadding
	obj._ContainerTopPadding = v.ContainerTopPadding
	obj._ContainerBottomPadding = v.ContainerBottomPadding
	obj._ContainerCornerRadius = v.ContainerCornerRadius
	obj._ContainerBorderColour = v.ContainerBorderColour
	obj._ContainerBgColour = v.ContainerBgColour
	obj._Deeplink = v.Deeplink
	return nil
}

func NewOrderReceiptDCAmcChargesInfoBanner() (_obj *OrderReceiptDCAmcChargesInfoBanner, _setters map[string]dynconf.SetFunc) {
	_obj = &OrderReceiptDCAmcChargesInfoBanner{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *OrderReceiptDCAmcChargesInfoBanner) Init() {
	newObj, _ := NewOrderReceiptDCAmcChargesInfoBanner()
	*obj = *newObj
}

func (obj *OrderReceiptDCAmcChargesInfoBanner) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OrderReceiptDCAmcChargesInfoBanner) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OrderReceiptDCAmcChargesInfoBanner)
	if !ok {
		return fmt.Errorf("invalid data type %v *OrderReceiptDCAmcChargesInfoBanner", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OrderReceiptDCAmcChargesInfoBanner) setDynamicField(v *config.OrderReceiptDCAmcChargesInfoBanner, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OrderReceiptDCAmcChargesInfoBanner) setDynamicFields(v *config.OrderReceiptDCAmcChargesInfoBanner, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *OrderReceiptDCAmcChargesInfoBanner) setStaticFields(v *config.OrderReceiptDCAmcChargesInfoBanner) error {

	obj._Title = v.Title
	obj._SubTitle = v.SubTitle
	obj._BannerImage = v.BannerImage
	obj._OnePercentBottomSheetInfo = v.OnePercentBottomSheetInfo
	return nil
}

func NewOnePercentBottomSheetInfo() (_obj *OnePercentBottomSheetInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &OnePercentBottomSheetInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *OnePercentBottomSheetInfo) Init() {
	newObj, _ := NewOnePercentBottomSheetInfo()
	*obj = *newObj
}

func (obj *OnePercentBottomSheetInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OnePercentBottomSheetInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OnePercentBottomSheetInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnePercentBottomSheetInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OnePercentBottomSheetInfo) setDynamicField(v *config.OnePercentBottomSheetInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OnePercentBottomSheetInfo) setDynamicFields(v *config.OnePercentBottomSheetInfo, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *OnePercentBottomSheetInfo) setStaticFields(v *config.OnePercentBottomSheetInfo) error {

	obj._Title = v.Title
	obj._TopImage = v.TopImage
	obj._StepsImage = v.StepsImage
	obj._Subtitle = v.Subtitle
	obj._Note = v.Note
	obj._Tnc = v.Tnc
	obj._DoKeepInMind = v.DoKeepInMind
	obj._DoKeepInMindPointers = v.DoKeepInMindPointers
	obj._TermsAndConditions = v.TermsAndConditions
	return nil
}

func NewFundTransferWorkflow() (_obj *FundTransferWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &FundTransferWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *FundTransferWorkflow) Init() {
	newObj, _ := NewFundTransferWorkflow()
	*obj = *newObj
}

func (obj *FundTransferWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FundTransferWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FundTransferWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *FundTransferWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FundTransferWorkflow) setDynamicField(v *config.FundTransferWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FundTransferWorkflow) setDynamicFields(v *config.FundTransferWorkflow, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *FundTransferWorkflow) setStaticFields(v *config.FundTransferWorkflow) error {

	obj._LowValueTransactionUpperLimit = v.LowValueTransactionUpperLimit
	return nil
}

func NewCollectShortCircuitWorkflow() (_obj *CollectShortCircuitWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &CollectShortCircuitWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *CollectShortCircuitWorkflow) Init() {
	newObj, _ := NewCollectShortCircuitWorkflow()
	*obj = *newObj
}

func (obj *CollectShortCircuitWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CollectShortCircuitWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CollectShortCircuitWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *CollectShortCircuitWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CollectShortCircuitWorkflow) setDynamicField(v *config.CollectShortCircuitWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CollectShortCircuitWorkflow) setDynamicFields(v *config.CollectShortCircuitWorkflow, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *CollectShortCircuitWorkflow) setStaticFields(v *config.CollectShortCircuitWorkflow) error {

	obj._LowValueTransactionUpperLimit = v.LowValueTransactionUpperLimit
	obj._CollectAmountLimit = v.CollectAmountLimit
	obj._CollectExpirationDuration = v.CollectExpirationDuration
	return nil
}

func NewDepositCreationWorkflow() (_obj *DepositCreationWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &DepositCreationWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *DepositCreationWorkflow) Init() {
	newObj, _ := NewDepositCreationWorkflow()
	*obj = *newObj
}

func (obj *DepositCreationWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DepositCreationWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DepositCreationWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *DepositCreationWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DepositCreationWorkflow) setDynamicField(v *config.DepositCreationWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DepositCreationWorkflow) setDynamicFields(v *config.DepositCreationWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DepositCreationWorkflow) setStaticFields(v *config.DepositCreationWorkflow) error {

	obj._Fulfillment = v.Fulfillment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *DepositCreationWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DepositCreationWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewStageProcessingParams() (_obj *StageProcessingParams, _setters map[string]dynconf.SetFunc) {
	_obj = &StageProcessingParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *StageProcessingParams) Init() {
	newObj, _ := NewStageProcessingParams()
	*obj = *newObj
}

func (obj *StageProcessingParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *StageProcessingParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.StageProcessingParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *StageProcessingParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *StageProcessingParams) setDynamicField(v *config.StageProcessingParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *StageProcessingParams) setDynamicFields(v *config.StageProcessingParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *StageProcessingParams) setStaticFields(v *config.StageProcessingParams) error {

	obj._RetryStrategy = v.RetryStrategy
	obj._Method = v.Method
	obj._ServiceName = v.ServiceName
	return nil
}

func NewUrnTransferWorkflow() (_obj *UrnTransferWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &UrnTransferWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *UrnTransferWorkflow) Init() {
	newObj, _ := NewUrnTransferWorkflow()
	*obj = *newObj
}

func (obj *UrnTransferWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UrnTransferWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UrnTransferWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *UrnTransferWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UrnTransferWorkflow) setDynamicField(v *config.UrnTransferWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UrnTransferWorkflow) setDynamicFields(v *config.UrnTransferWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UrnTransferWorkflow) setStaticFields(v *config.UrnTransferWorkflow) error {

	obj._Payment = v.Payment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *UrnTransferWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *UrnTransferWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewPreCloseDepositWorkflow() (_obj *PreCloseDepositWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &PreCloseDepositWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *PreCloseDepositWorkflow) Init() {
	newObj, _ := NewPreCloseDepositWorkflow()
	*obj = *newObj
}

func (obj *PreCloseDepositWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PreCloseDepositWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PreCloseDepositWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreCloseDepositWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PreCloseDepositWorkflow) setDynamicField(v *config.PreCloseDepositWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PreCloseDepositWorkflow) setDynamicFields(v *config.PreCloseDepositWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PreCloseDepositWorkflow) setStaticFields(v *config.PreCloseDepositWorkflow) error {

	obj._Fulfillment = v.Fulfillment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *PreCloseDepositWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreCloseDepositWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewP2PCollectWorkflow() (_obj *P2PCollectWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &P2PCollectWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *P2PCollectWorkflow) Init() {
	newObj, _ := NewP2PCollectWorkflow()
	*obj = *newObj
}

func (obj *P2PCollectWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *P2PCollectWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.P2PCollectWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *P2PCollectWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *P2PCollectWorkflow) setDynamicField(v *config.P2PCollectWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *P2PCollectWorkflow) setDynamicFields(v *config.P2PCollectWorkflow, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *P2PCollectWorkflow) setStaticFields(v *config.P2PCollectWorkflow) error {

	obj._CollectAmountLimit = v.CollectAmountLimit
	obj._CollectExpirationDuration = v.CollectExpirationDuration
	return nil
}

func NewB2CFundTransferWorkflow() (_obj *B2CFundTransferWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &B2CFundTransferWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *B2CFundTransferWorkflow) Init() {
	newObj, _ := NewB2CFundTransferWorkflow()
	*obj = *newObj
}

func (obj *B2CFundTransferWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *B2CFundTransferWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.B2CFundTransferWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *B2CFundTransferWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *B2CFundTransferWorkflow) setDynamicField(v *config.B2CFundTransferWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *B2CFundTransferWorkflow) setDynamicFields(v *config.B2CFundTransferWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *B2CFundTransferWorkflow) setStaticFields(v *config.B2CFundTransferWorkflow) error {

	obj._Payment = v.Payment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *B2CFundTransferWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *B2CFundTransferWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewRewardsCreateSdWorkflow() (_obj *RewardsCreateSdWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardsCreateSdWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *RewardsCreateSdWorkflow) Init() {
	newObj, _ := NewRewardsCreateSdWorkflow()
	*obj = *newObj
}

func (obj *RewardsCreateSdWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RewardsCreateSdWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RewardsCreateSdWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsCreateSdWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RewardsCreateSdWorkflow) setDynamicField(v *config.RewardsCreateSdWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RewardsCreateSdWorkflow) setDynamicFields(v *config.RewardsCreateSdWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RewardsCreateSdWorkflow) setStaticFields(v *config.RewardsCreateSdWorkflow) error {

	obj._Payment = v.Payment
	obj._Fulfillment = v.Fulfillment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *RewardsCreateSdWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsCreateSdWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewRewardsAddFundsSdWorkflow() (_obj *RewardsAddFundsSdWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardsAddFundsSdWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *RewardsAddFundsSdWorkflow) Init() {
	newObj, _ := NewRewardsAddFundsSdWorkflow()
	*obj = *newObj
}

func (obj *RewardsAddFundsSdWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RewardsAddFundsSdWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RewardsAddFundsSdWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsAddFundsSdWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RewardsAddFundsSdWorkflow) setDynamicField(v *config.RewardsAddFundsSdWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RewardsAddFundsSdWorkflow) setDynamicFields(v *config.RewardsAddFundsSdWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RewardsAddFundsSdWorkflow) setStaticFields(v *config.RewardsAddFundsSdWorkflow) error {

	obj._Payment = v.Payment
	obj._Fulfillment = v.Fulfillment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *RewardsAddFundsSdWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsAddFundsSdWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewAddFundsSdWorkflow() (_obj *AddFundsSdWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &AddFundsSdWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *AddFundsSdWorkflow) Init() {
	newObj, _ := NewAddFundsSdWorkflow()
	*obj = *newObj
}

func (obj *AddFundsSdWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AddFundsSdWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AddFundsSdWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddFundsSdWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AddFundsSdWorkflow) setDynamicField(v *config.AddFundsSdWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AddFundsSdWorkflow) setDynamicFields(v *config.AddFundsSdWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AddFundsSdWorkflow) setStaticFields(v *config.AddFundsSdWorkflow) error {

	obj._Payment = v.Payment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *AddFundsSdWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddFundsSdWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewAddFundsWorkflow() (_obj *AddFundsWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &AddFundsWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *AddFundsWorkflow) Init() {
	newObj, _ := NewAddFundsWorkflow()
	*obj = *newObj
}

func (obj *AddFundsWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AddFundsWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AddFundsWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddFundsWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AddFundsWorkflow) setDynamicField(v *config.AddFundsWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AddFundsWorkflow) setDynamicFields(v *config.AddFundsWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AddFundsWorkflow) setStaticFields(v *config.AddFundsWorkflow) error {

	obj._Payment = v.Payment
	obj._Settlement = v.Settlement
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *AddFundsWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddFundsWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewAddFundsCollectWorkflow() (_obj *AddFundsCollectWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &AddFundsCollectWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *AddFundsCollectWorkflow) Init() {
	newObj, _ := NewAddFundsCollectWorkflow()
	*obj = *newObj
}

func (obj *AddFundsCollectWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AddFundsCollectWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AddFundsCollectWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddFundsCollectWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AddFundsCollectWorkflow) setDynamicField(v *config.AddFundsCollectWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AddFundsCollectWorkflow) setDynamicFields(v *config.AddFundsCollectWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AddFundsCollectWorkflow) setStaticFields(v *config.AddFundsCollectWorkflow) error {

	obj._Collect = v.Collect
	obj._Payment = v.Payment
	obj._Settlement = v.Settlement
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *AddFundsCollectWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddFundsCollectWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewRecurringPaymentCreationWorkflow() (_obj *RecurringPaymentCreationWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &RecurringPaymentCreationWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *RecurringPaymentCreationWorkflow) Init() {
	newObj, _ := NewRecurringPaymentCreationWorkflow()
	*obj = *newObj
}

func (obj *RecurringPaymentCreationWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RecurringPaymentCreationWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RecurringPaymentCreationWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecurringPaymentCreationWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RecurringPaymentCreationWorkflow) setDynamicField(v *config.RecurringPaymentCreationWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RecurringPaymentCreationWorkflow) setDynamicFields(v *config.RecurringPaymentCreationWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RecurringPaymentCreationWorkflow) setStaticFields(v *config.RecurringPaymentCreationWorkflow) error {

	obj._Fulfillment = v.Fulfillment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *RecurringPaymentCreationWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecurringPaymentCreationWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewRecurringPaymentExecutionWorkflow() (_obj *RecurringPaymentExecutionWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &RecurringPaymentExecutionWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *RecurringPaymentExecutionWorkflow) Init() {
	newObj, _ := NewRecurringPaymentExecutionWorkflow()
	*obj = *newObj
}

func (obj *RecurringPaymentExecutionWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RecurringPaymentExecutionWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RecurringPaymentExecutionWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecurringPaymentExecutionWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RecurringPaymentExecutionWorkflow) setDynamicField(v *config.RecurringPaymentExecutionWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RecurringPaymentExecutionWorkflow) setDynamicFields(v *config.RecurringPaymentExecutionWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RecurringPaymentExecutionWorkflow) setStaticFields(v *config.RecurringPaymentExecutionWorkflow) error {

	obj._Payment = v.Payment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *RecurringPaymentExecutionWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecurringPaymentExecutionWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewRecurringPaymentModifyWorkflow() (_obj *RecurringPaymentModifyWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &RecurringPaymentModifyWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *RecurringPaymentModifyWorkflow) Init() {
	newObj, _ := NewRecurringPaymentModifyWorkflow()
	*obj = *newObj
}

func (obj *RecurringPaymentModifyWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RecurringPaymentModifyWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RecurringPaymentModifyWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecurringPaymentModifyWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RecurringPaymentModifyWorkflow) setDynamicField(v *config.RecurringPaymentModifyWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RecurringPaymentModifyWorkflow) setDynamicFields(v *config.RecurringPaymentModifyWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RecurringPaymentModifyWorkflow) setStaticFields(v *config.RecurringPaymentModifyWorkflow) error {

	obj._Fulfillment = v.Fulfillment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *RecurringPaymentModifyWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecurringPaymentModifyWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewRecurringPaymentRevokeWorkflow() (_obj *RecurringPaymentRevokeWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &RecurringPaymentRevokeWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *RecurringPaymentRevokeWorkflow) Init() {
	newObj, _ := NewRecurringPaymentRevokeWorkflow()
	*obj = *newObj
}

func (obj *RecurringPaymentRevokeWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RecurringPaymentRevokeWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RecurringPaymentRevokeWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecurringPaymentRevokeWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RecurringPaymentRevokeWorkflow) setDynamicField(v *config.RecurringPaymentRevokeWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RecurringPaymentRevokeWorkflow) setDynamicFields(v *config.RecurringPaymentRevokeWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RecurringPaymentRevokeWorkflow) setStaticFields(v *config.RecurringPaymentRevokeWorkflow) error {

	obj._Fulfillment = v.Fulfillment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *RecurringPaymentRevokeWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecurringPaymentRevokeWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewP2PInvestmentWorkflow() (_obj *P2PInvestmentWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &P2PInvestmentWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *P2PInvestmentWorkflow) Init() {
	newObj, _ := NewP2PInvestmentWorkflow()
	*obj = *newObj
}

func (obj *P2PInvestmentWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *P2PInvestmentWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.P2PInvestmentWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *P2PInvestmentWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *P2PInvestmentWorkflow) setDynamicField(v *config.P2PInvestmentWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *P2PInvestmentWorkflow) setDynamicFields(v *config.P2PInvestmentWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *P2PInvestmentWorkflow) setStaticFields(v *config.P2PInvestmentWorkflow) error {

	obj._Payment = v.Payment
	obj._Settlement = v.Settlement
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *P2PInvestmentWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *P2PInvestmentWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewP2PWithdrawalWorkflow() (_obj *P2PWithdrawalWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &P2PWithdrawalWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *P2PWithdrawalWorkflow) Init() {
	newObj, _ := NewP2PWithdrawalWorkflow()
	*obj = *newObj
}

func (obj *P2PWithdrawalWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *P2PWithdrawalWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.P2PWithdrawalWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *P2PWithdrawalWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *P2PWithdrawalWorkflow) setDynamicField(v *config.P2PWithdrawalWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *P2PWithdrawalWorkflow) setDynamicFields(v *config.P2PWithdrawalWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *P2PWithdrawalWorkflow) setStaticFields(v *config.P2PWithdrawalWorkflow) error {

	obj._Fulfillment = v.Fulfillment
	obj._Payment = v.Payment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *P2PWithdrawalWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *P2PWithdrawalWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewMutualFundsInvestmentPostPaid() (_obj *MutualFundsInvestmentPostPaid, _setters map[string]dynconf.SetFunc) {
	_obj = &MutualFundsInvestmentPostPaid{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *MutualFundsInvestmentPostPaid) Init() {
	newObj, _ := NewMutualFundsInvestmentPostPaid()
	*obj = *newObj
}

func (obj *MutualFundsInvestmentPostPaid) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *MutualFundsInvestmentPostPaid) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.MutualFundsInvestmentPostPaid)
	if !ok {
		return fmt.Errorf("invalid data type %v *MutualFundsInvestmentPostPaid", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *MutualFundsInvestmentPostPaid) setDynamicField(v *config.MutualFundsInvestmentPostPaid, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *MutualFundsInvestmentPostPaid) setDynamicFields(v *config.MutualFundsInvestmentPostPaid, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *MutualFundsInvestmentPostPaid) setStaticFields(v *config.MutualFundsInvestmentPostPaid) error {

	obj._Fulfillment = v.Fulfillment
	obj._Payment = v.Payment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *MutualFundsInvestmentPostPaid) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *MutualFundsInvestmentPostPaid.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewMutualFundsRedemption() (_obj *MutualFundsRedemption, _setters map[string]dynconf.SetFunc) {
	_obj = &MutualFundsRedemption{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *MutualFundsRedemption) Init() {
	newObj, _ := NewMutualFundsRedemption()
	*obj = *newObj
}

func (obj *MutualFundsRedemption) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *MutualFundsRedemption) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.MutualFundsRedemption)
	if !ok {
		return fmt.Errorf("invalid data type %v *MutualFundsRedemption", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *MutualFundsRedemption) setDynamicField(v *config.MutualFundsRedemption, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *MutualFundsRedemption) setDynamicFields(v *config.MutualFundsRedemption, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *MutualFundsRedemption) setStaticFields(v *config.MutualFundsRedemption) error {

	obj._Fulfillment = v.Fulfillment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *MutualFundsRedemption) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *MutualFundsRedemption.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewPaymentNotificationParams() (_obj *PaymentNotificationParams, _setters map[string]dynconf.SetFunc) {
	_obj = &PaymentNotificationParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *PaymentNotificationParams) Init() {
	newObj, _ := NewPaymentNotificationParams()
	*obj = *newObj
}

func (obj *PaymentNotificationParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PaymentNotificationParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PaymentNotificationParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *PaymentNotificationParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PaymentNotificationParams) setDynamicField(v *config.PaymentNotificationParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PaymentNotificationParams) setDynamicFields(v *config.PaymentNotificationParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *PaymentNotificationParams) setStaticFields(v *config.PaymentNotificationParams) error {

	obj._HistoricPaymentSupportedTill = v.HistoricPaymentSupportedTill
	obj._Credit = v.Credit
	obj._Debit = v.Debit
	obj._CreditUPI = v.CreditUPI
	obj._DebitUPI = v.DebitUPI
	obj._CollectReceived = v.CollectReceived
	obj._CollectReceivedInApp = v.CollectReceivedInApp
	obj._DepositCreationFailed = v.DepositCreationFailed
	obj._DepositSdAddFundsFailed = v.DepositSdAddFundsFailed
	obj._TransactionFailed = v.TransactionFailed
	obj._ClosedDeposit = v.ClosedDeposit
	obj._MaturedDeposit = v.MaturedDeposit
	obj._CreditDeposit = v.CreditDeposit
	obj._CollectRequestCancelled = v.CollectRequestCancelled
	obj._CreditInterest = v.CreditInterest
	obj._TodChargesDebit = v.TodChargesDebit
	obj._TransactionReversed = v.TransactionReversed
	obj._EcsReturnCharges = v.EcsReturnCharges
	obj._AtmDeclineFee = v.AtmDeclineFee
	obj._DuplicateCardFee = v.DuplicateCardFee
	obj._AtmPenalty = v.AtmPenalty
	obj._IntATMcharges = v.IntATMcharges
	obj._OtherBankAtmTxnCharge = v.OtherBankAtmTxnCharge
	obj._SkipFITOrders = v.SkipFITOrders
	obj._SkipMFOrders = v.SkipMFOrders
	obj._EcsReturnChargesInApp = v.EcsReturnChargesInApp
	obj._DcForexRefundTxn = v.DcForexRefundTxn
	obj._ChequeCreditNotification = v.ChequeCreditNotification
	obj._AddFundsSecondLegFailure = v.AddFundsSecondLegFailure
	return nil
}

func NewNotificationTemplateParams() (_obj *NotificationTemplateParams, _setters map[string]dynconf.SetFunc) {
	_obj = &NotificationTemplateParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *NotificationTemplateParams) Init() {
	newObj, _ := NewNotificationTemplateParams()
	*obj = *newObj
}

func (obj *NotificationTemplateParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NotificationTemplateParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NotificationTemplateParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *NotificationTemplateParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NotificationTemplateParams) setDynamicField(v *config.NotificationTemplateParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NotificationTemplateParams) setDynamicFields(v *config.NotificationTemplateParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *NotificationTemplateParams) setStaticFields(v *config.NotificationTemplateParams) error {

	obj._Disable = v.Disable
	obj._Title = v.Title
	obj._Body = v.Body
	obj._BgColour = v.BgColour
	obj._IconAttr = v.IconAttr
	obj._NotificationExpiry = v.NotificationExpiry
	obj._NotificationType = v.NotificationType
	obj._TriggerAfter = v.TriggerAfter
	obj._ValidFrom = v.ValidFrom
	obj._ValidTill = v.ValidTill
	return nil
}

func NewIconAttribute() (_obj *IconAttribute, _setters map[string]dynconf.SetFunc) {
	_obj = &IconAttribute{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *IconAttribute) Init() {
	newObj, _ := NewIconAttribute()
	*obj = *newObj
}

func (obj *IconAttribute) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IconAttribute) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IconAttribute)
	if !ok {
		return fmt.Errorf("invalid data type %v *IconAttribute", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IconAttribute) setDynamicField(v *config.IconAttribute, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IconAttribute) setDynamicFields(v *config.IconAttribute, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *IconAttribute) setStaticFields(v *config.IconAttribute) error {

	obj._IconName = v.IconName
	obj._IconURL = v.IconURL
	obj._ColourCode = v.ColourCode
	return nil
}

func NewPaymentSMSParams() (_obj *PaymentSMSParams, _setters map[string]dynconf.SetFunc) {
	_obj = &PaymentSMSParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *PaymentSMSParams) Init() {
	newObj, _ := NewPaymentSMSParams()
	*obj = *newObj
}

func (obj *PaymentSMSParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PaymentSMSParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PaymentSMSParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *PaymentSMSParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PaymentSMSParams) setDynamicField(v *config.PaymentSMSParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PaymentSMSParams) setDynamicFields(v *config.PaymentSMSParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *PaymentSMSParams) setStaticFields(v *config.PaymentSMSParams) error {

	obj._HistoricPaymentSupportedTill = v.HistoricPaymentSupportedTill
	obj._Credit = v.Credit
	obj._Debit = v.Debit
	obj._CreditUPI = v.CreditUPI
	obj._DebitUPI = v.DebitUPI
	obj._CollectReceived = v.CollectReceived
	obj._TransactionFailed = v.TransactionFailed
	obj._DebitGenericPI = v.DebitGenericPI
	obj._CreditGenericPI = v.CreditGenericPI
	obj._SkipFITOrders = v.SkipFITOrders
	obj._SkipDebitCardDuplicateFees = v.SkipDebitCardDuplicateFees
	return nil
}

func NewSmsTemplateParams() (_obj *SmsTemplateParams, _setters map[string]dynconf.SetFunc) {
	_obj = &SmsTemplateParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *SmsTemplateParams) Init() {
	newObj, _ := NewSmsTemplateParams()
	*obj = *newObj
}

func (obj *SmsTemplateParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SmsTemplateParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SmsTemplateParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *SmsTemplateParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SmsTemplateParams) setDynamicField(v *config.SmsTemplateParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SmsTemplateParams) setDynamicFields(v *config.SmsTemplateParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *SmsTemplateParams) setStaticFields(v *config.SmsTemplateParams) error {

	obj._Disable = v.Disable
	obj._Body = v.Body
	obj._TriggerAfter = v.TriggerAfter
	return nil
}

func NewIconUrls() (_obj *IconUrls, _setters map[string]dynconf.SetFunc) {
	_obj = &IconUrls{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *IconUrls) Init() {
	newObj, _ := NewIconUrls()
	*obj = *newObj
}

func (obj *IconUrls) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IconUrls) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IconUrls)
	if !ok {
		return fmt.Errorf("invalid data type %v *IconUrls", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IconUrls) setDynamicField(v *config.IconUrls, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IconUrls) setDynamicFields(v *config.IconUrls, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *IconUrls) setStaticFields(v *config.IconUrls) error {

	obj._FDIconUrl = v.FDIconUrl
	obj._SDIconUrl = v.SDIconUrl
	obj._ATMWithdrawalIconUrl = v.ATMWithdrawalIconUrl
	obj._FiBankIconUrl = v.FiBankIconUrl
	obj._PendingClockIconUrl = v.PendingClockIconUrl
	obj._SuccessStatusIconUrl = v.SuccessStatusIconUrl
	obj._FailedStatusIconUrl = v.FailedStatusIconUrl
	obj._PendingStatusIconUrl = v.PendingStatusIconUrl
	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["trimdebugmessagefromstatus"] = _obj.SetTrimDebugMessageFromStatus
	_setters["skipsmsforupidebittransactions"] = _obj.SetSkipSmsForUpiDebitTransactions
	_setters["enablebalancev1evaluation"] = _obj.SetEnableBalanceV1Evaluation
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *config.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "trimdebugmessagefromstatus":
		return obj.SetTrimDebugMessageFromStatus(v.TrimDebugMessageFromStatus, true, nil)
	case "skipsmsforupidebittransactions":
		return obj.SetSkipSmsForUpiDebitTransactions(v.SkipSmsForUpiDebitTransactions, true, nil)
	case "enablebalancev1evaluation":
		return obj.SetEnableBalanceV1Evaluation(v.EnableBalanceV1Evaluation, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *config.Flags, dynamic bool, path []string) (err error) {

	err = obj.SetTrimDebugMessageFromStatus(v.TrimDebugMessageFromStatus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipSmsForUpiDebitTransactions(v.SkipSmsForUpiDebitTransactions, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableBalanceV1Evaluation(v.EnableBalanceV1Evaluation, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *config.Flags) error {

	return nil
}

func (obj *Flags) SetTrimDebugMessageFromStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.TrimDebugMessageFromStatus", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._TrimDebugMessageFromStatus, 1)
	} else {
		atomic.StoreUint32(&obj._TrimDebugMessageFromStatus, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "TrimDebugMessageFromStatus")
	}
	return nil
}
func (obj *Flags) SetSkipSmsForUpiDebitTransactions(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.SkipSmsForUpiDebitTransactions", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipSmsForUpiDebitTransactions, 1)
	} else {
		atomic.StoreUint32(&obj._SkipSmsForUpiDebitTransactions, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipSmsForUpiDebitTransactions")
	}
	return nil
}
func (obj *Flags) SetEnableBalanceV1Evaluation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableBalanceV1Evaluation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableBalanceV1Evaluation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableBalanceV1Evaluation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableBalanceV1Evaluation")
	}
	return nil
}

func NewCustomRuleDEParams() (_obj *CustomRuleDEParams, _setters map[string]dynconf.SetFunc) {
	_obj = &CustomRuleDEParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *CustomRuleDEParams) Init() {
	newObj, _ := NewCustomRuleDEParams()
	*obj = *newObj
}

func (obj *CustomRuleDEParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CustomRuleDEParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CustomRuleDEParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *CustomRuleDEParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CustomRuleDEParams) setDynamicField(v *config.CustomRuleDEParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CustomRuleDEParams) setDynamicFields(v *config.CustomRuleDEParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *CustomRuleDEParams) setStaticFields(v *config.CustomRuleDEParams) error {

	obj._IsCustomRuleDERestricted = v.IsCustomRuleDERestricted
	obj._AllowedUserGrpForCustomDE = v.AllowedUserGrpForCustomDE
	return nil
}

func NewRudderBroker() (_obj *RudderBroker, _setters map[string]dynconf.SetFunc) {
	_obj = &RudderBroker{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *RudderBroker) Init() {
	newObj, _ := NewRudderBroker()
	*obj = *newObj
}

func (obj *RudderBroker) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RudderBroker) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RudderBroker)
	if !ok {
		return fmt.Errorf("invalid data type %v *RudderBroker", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RudderBroker) setDynamicField(v *config.RudderBroker, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RudderBroker) setDynamicFields(v *config.RudderBroker, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *RudderBroker) setStaticFields(v *config.RudderBroker) error {

	obj._Host = v.Host
	obj._Key = v.Key
	obj._IntervalInSec = v.IntervalInSec
	obj._BatchSize = v.BatchSize
	obj._Verbose = v.Verbose
	return nil
}

func NewURNPaymentLimits() (_obj *URNPaymentLimits, _setters map[string]dynconf.SetFunc) {
	_obj = &URNPaymentLimits{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *URNPaymentLimits) Init() {
	newObj, _ := NewURNPaymentLimits()
	*obj = *newObj
}

func (obj *URNPaymentLimits) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *URNPaymentLimits) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.URNPaymentLimits)
	if !ok {
		return fmt.Errorf("invalid data type %v *URNPaymentLimits", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *URNPaymentLimits) setDynamicField(v *config.URNPaymentLimits, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *URNPaymentLimits) setDynamicFields(v *config.URNPaymentLimits, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *URNPaymentLimits) setStaticFields(v *config.URNPaymentLimits) error {

	obj._P2PQRCodeLimit = v.P2PQRCodeLimit
	obj._P2MOnlineQRCodeLimit = v.P2MOnlineQRCodeLimit
	obj._P2MOfflineQRCodeLimit = v.P2MOfflineQRCodeLimit
	obj._P2PQRShareAndPayLimit = v.P2PQRShareAndPayLimit
	obj._P2MOnlineQRShareAndPayLimit = v.P2MOnlineQRShareAndPayLimit
	obj._P2MOfflineQRShareAndPayLimit = v.P2MOfflineQRShareAndPayLimit
	obj._P2PIntentLimit = v.P2PIntentLimit
	obj._P2MOnlineIntentLimit = v.P2MOnlineIntentLimit
	obj._P2MOfflineIntentLimit = v.P2MOfflineIntentLimit
	return nil
}

func NewQRCodeLimit() (_obj *QRCodeLimit, _setters map[string]dynconf.SetFunc) {
	_obj = &QRCodeLimit{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *QRCodeLimit) Init() {
	newObj, _ := NewQRCodeLimit()
	*obj = *newObj
}

func (obj *QRCodeLimit) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *QRCodeLimit) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.QRCodeLimit)
	if !ok {
		return fmt.Errorf("invalid data type %v *QRCodeLimit", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *QRCodeLimit) setDynamicField(v *config.QRCodeLimit, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *QRCodeLimit) setDynamicFields(v *config.QRCodeLimit, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *QRCodeLimit) setStaticFields(v *config.QRCodeLimit) error {

	obj._InsecureQRCodeLimit = v.InsecureQRCodeLimit
	obj._SecureQRCodeLimit = v.SecureQRCodeLimit
	return nil
}

func NewQRCode() (_obj *QRCode, _setters map[string]dynconf.SetFunc) {
	_obj = &QRCode{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *QRCode) Init() {
	newObj, _ := NewQRCode()
	*obj = *newObj
}

func (obj *QRCode) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *QRCode) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.QRCode)
	if !ok {
		return fmt.Errorf("invalid data type %v *QRCode", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *QRCode) setDynamicField(v *config.QRCode, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *QRCode) setDynamicFields(v *config.QRCode, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *QRCode) setStaticFields(v *config.QRCode) error {

	obj._DynamicQRLimit = v.DynamicQRLimit
	obj._StaticQRLimit = v.StaticQRLimit
	return nil
}

func NewQRCodeLimitForMerchants() (_obj *QRCodeLimitForMerchants, _setters map[string]dynconf.SetFunc) {
	_obj = &QRCodeLimitForMerchants{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *QRCodeLimitForMerchants) Init() {
	newObj, _ := NewQRCodeLimitForMerchants()
	*obj = *newObj
}

func (obj *QRCodeLimitForMerchants) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *QRCodeLimitForMerchants) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.QRCodeLimitForMerchants)
	if !ok {
		return fmt.Errorf("invalid data type %v *QRCodeLimitForMerchants", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *QRCodeLimitForMerchants) setDynamicField(v *config.QRCodeLimitForMerchants, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *QRCodeLimitForMerchants) setDynamicFields(v *config.QRCodeLimitForMerchants, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *QRCodeLimitForMerchants) setStaticFields(v *config.QRCodeLimitForMerchants) error {

	obj._InsecureQRCodeLimit = v.InsecureQRCodeLimit
	obj._SecureQRCodeLimit = v.SecureQRCodeLimit
	return nil
}

func NewMerchantQRCode() (_obj *MerchantQRCode, _setters map[string]dynconf.SetFunc) {
	_obj = &MerchantQRCode{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *MerchantQRCode) Init() {
	newObj, _ := NewMerchantQRCode()
	*obj = *newObj
}

func (obj *MerchantQRCode) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *MerchantQRCode) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.MerchantQRCode)
	if !ok {
		return fmt.Errorf("invalid data type %v *MerchantQRCode", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *MerchantQRCode) setDynamicField(v *config.MerchantQRCode, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *MerchantQRCode) setDynamicFields(v *config.MerchantQRCode, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *MerchantQRCode) setStaticFields(v *config.MerchantQRCode) error {

	obj._VerifiedMerchant = v.VerifiedMerchant
	obj._NonVerifiedMerchant = v.NonVerifiedMerchant
	return nil
}

func NewIntentLimit() (_obj *IntentLimit, _setters map[string]dynconf.SetFunc) {
	_obj = &IntentLimit{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *IntentLimit) Init() {
	newObj, _ := NewIntentLimit()
	*obj = *newObj
}

func (obj *IntentLimit) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IntentLimit) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IntentLimit)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntentLimit", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IntentLimit) setDynamicField(v *config.IntentLimit, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IntentLimit) setDynamicFields(v *config.IntentLimit, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *IntentLimit) setStaticFields(v *config.IntentLimit) error {

	obj._InsecureIntent = v.InsecureIntent
	obj._SecureIntent = v.SecureIntent
	return nil
}

func NewAddFundsAlertParams() (_obj *AddFundsAlertParams, _setters map[string]dynconf.SetFunc) {
	_obj = &AddFundsAlertParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *AddFundsAlertParams) Init() {
	newObj, _ := NewAddFundsAlertParams()
	*obj = *newObj
}

func (obj *AddFundsAlertParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AddFundsAlertParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AddFundsAlertParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddFundsAlertParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AddFundsAlertParams) setDynamicField(v *config.AddFundsAlertParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AddFundsAlertParams) setDynamicFields(v *config.AddFundsAlertParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *AddFundsAlertParams) setStaticFields(v *config.AddFundsAlertParams) error {

	obj._StartAfter = v.StartAfter
	obj._EndAfter = v.EndAfter
	obj._DistributedLockKeyPrefix = v.DistributedLockKeyPrefix
	obj._DistributedLockDuration = v.DistributedLockDuration
	return nil
}

func NewAddFundsAlertMailingParams() (_obj *AddFundsAlertMailingParams, _setters map[string]dynconf.SetFunc) {
	_obj = &AddFundsAlertMailingParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *AddFundsAlertMailingParams) Init() {
	newObj, _ := NewAddFundsAlertMailingParams()
	*obj = *newObj
}

func (obj *AddFundsAlertMailingParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AddFundsAlertMailingParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AddFundsAlertMailingParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddFundsAlertMailingParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AddFundsAlertMailingParams) setDynamicField(v *config.AddFundsAlertMailingParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AddFundsAlertMailingParams) setDynamicFields(v *config.AddFundsAlertMailingParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *AddFundsAlertMailingParams) setStaticFields(v *config.AddFundsAlertMailingParams) error {

	obj._IsEnabled = v.IsEnabled
	obj._TimestampFormat = v.TimestampFormat
	obj._FromAddress = v.FromAddress
	obj._ToAddress = v.ToAddress
	obj._FromName = v.FromName
	obj._ToName = v.ToName
	return nil
}

func NewExecuteRecurringPaymentWithAuthWorkflow() (_obj *ExecuteRecurringPaymentWithAuthWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &ExecuteRecurringPaymentWithAuthWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *ExecuteRecurringPaymentWithAuthWorkflow) Init() {
	newObj, _ := NewExecuteRecurringPaymentWithAuthWorkflow()
	*obj = *newObj
}

func (obj *ExecuteRecurringPaymentWithAuthWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ExecuteRecurringPaymentWithAuthWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ExecuteRecurringPaymentWithAuthWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExecuteRecurringPaymentWithAuthWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ExecuteRecurringPaymentWithAuthWorkflow) setDynamicField(v *config.ExecuteRecurringPaymentWithAuthWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ExecuteRecurringPaymentWithAuthWorkflow) setDynamicFields(v *config.ExecuteRecurringPaymentWithAuthWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ExecuteRecurringPaymentWithAuthWorkflow) setStaticFields(v *config.ExecuteRecurringPaymentWithAuthWorkflow) error {

	obj._Payment = v.Payment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *ExecuteRecurringPaymentWithAuthWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExecuteRecurringPaymentWithAuthWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewExecuteRecurringPaymentWithNoAuthWorkflow() (_obj *ExecuteRecurringPaymentWithNoAuthWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &ExecuteRecurringPaymentWithNoAuthWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *ExecuteRecurringPaymentWithNoAuthWorkflow) Init() {
	newObj, _ := NewExecuteRecurringPaymentWithNoAuthWorkflow()
	*obj = *newObj
}

func (obj *ExecuteRecurringPaymentWithNoAuthWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ExecuteRecurringPaymentWithNoAuthWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ExecuteRecurringPaymentWithNoAuthWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExecuteRecurringPaymentWithNoAuthWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ExecuteRecurringPaymentWithNoAuthWorkflow) setDynamicField(v *config.ExecuteRecurringPaymentWithNoAuthWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ExecuteRecurringPaymentWithNoAuthWorkflow) setDynamicFields(v *config.ExecuteRecurringPaymentWithNoAuthWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ExecuteRecurringPaymentWithNoAuthWorkflow) setStaticFields(v *config.ExecuteRecurringPaymentWithNoAuthWorkflow) error {

	obj._Payment = v.Payment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *ExecuteRecurringPaymentWithNoAuthWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExecuteRecurringPaymentWithNoAuthWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewRecurringPaymentPauseUnpauseWorkflow() (_obj *RecurringPaymentPauseUnpauseWorkflow, _setters map[string]dynconf.SetFunc) {
	_obj = &RecurringPaymentPauseUnpauseWorkflow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isautotriggered"] = _obj.SetIsAutoTriggered
	return _obj, _setters
}

func (obj *RecurringPaymentPauseUnpauseWorkflow) Init() {
	newObj, _ := NewRecurringPaymentPauseUnpauseWorkflow()
	*obj = *newObj
}

func (obj *RecurringPaymentPauseUnpauseWorkflow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RecurringPaymentPauseUnpauseWorkflow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RecurringPaymentPauseUnpauseWorkflow)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecurringPaymentPauseUnpauseWorkflow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RecurringPaymentPauseUnpauseWorkflow) setDynamicField(v *config.RecurringPaymentPauseUnpauseWorkflow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isautotriggered":
		return obj.SetIsAutoTriggered(v.IsAutoTriggered, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RecurringPaymentPauseUnpauseWorkflow) setDynamicFields(v *config.RecurringPaymentPauseUnpauseWorkflow, dynamic bool, path []string) (err error) {

	err = obj.SetIsAutoTriggered(v.IsAutoTriggered, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RecurringPaymentPauseUnpauseWorkflow) setStaticFields(v *config.RecurringPaymentPauseUnpauseWorkflow) error {

	obj._Fulfillment = v.Fulfillment
	obj._InitialDelay = v.InitialDelay
	return nil
}

func (obj *RecurringPaymentPauseUnpauseWorkflow) SetIsAutoTriggered(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecurringPaymentPauseUnpauseWorkflow.IsAutoTriggered", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAutoTriggered, 1)
	} else {
		atomic.StoreUint32(&obj._IsAutoTriggered, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAutoTriggered")
	}
	return nil
}

func NewEventsConfig() (_obj *EventsConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EventsConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *EventsConfig) Init() {
	newObj, _ := NewEventsConfig()
	*obj = *newObj
}

func (obj *EventsConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EventsConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EventsConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EventsConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EventsConfig) setDynamicField(v *config.EventsConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EventsConfig) setDynamicFields(v *config.EventsConfig, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *EventsConfig) setStaticFields(v *config.EventsConfig) error {

	obj._IncomingCreditMaxPublishDelay = v.IncomingCreditMaxPublishDelay
	return nil
}

func NewCSIS() (_obj *CSIS, _setters map[string]dynconf.SetFunc) {
	_obj = &CSIS{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *CSIS) Init() {
	newObj, _ := NewCSIS()
	*obj = *newObj
}

func (obj *CSIS) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CSIS) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CSIS)
	if !ok {
		return fmt.Errorf("invalid data type %v *CSIS", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CSIS) setDynamicField(v *config.CSIS, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CSIS) setDynamicFields(v *config.CSIS, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *CSIS) setStaticFields(v *config.CSIS) error {

	obj._IsCsisEnable = v.IsCsisEnable
	obj._StartTime = v.StartTime
	obj._EndTime = v.EndTime
	return nil
}

func NewBankDownTime() (_obj *BankDownTime, _setters map[string]dynconf.SetFunc) {
	_obj = &BankDownTime{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *BankDownTime) Init() {
	newObj, _ := NewBankDownTime()
	*obj = *newObj
}

func (obj *BankDownTime) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *BankDownTime) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.BankDownTime)
	if !ok {
		return fmt.Errorf("invalid data type %v *BankDownTime", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *BankDownTime) setDynamicField(v *config.BankDownTime, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *BankDownTime) setDynamicFields(v *config.BankDownTime, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *BankDownTime) setStaticFields(v *config.BankDownTime) error {

	obj._IsBankDownTimeEnable = v.IsBankDownTimeEnable
	obj._StartTime = v.StartTime
	obj._EndTime = v.EndTime
	return nil
}

func NewSMSOptionVersion() (_obj *SMSOptionVersion, _setters map[string]dynconf.SetFunc) {
	_obj = &SMSOptionVersion{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *SMSOptionVersion) Init() {
	newObj, _ := NewSMSOptionVersion()
	*obj = *newObj
}

func (obj *SMSOptionVersion) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SMSOptionVersion) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SMSOptionVersion)
	if !ok {
		return fmt.Errorf("invalid data type %v *SMSOptionVersion", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SMSOptionVersion) setDynamicField(v *config.SMSOptionVersion, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SMSOptionVersion) setDynamicFields(v *config.SMSOptionVersion, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *SMSOptionVersion) setStaticFields(v *config.SMSOptionVersion) error {

	obj._Default = v.Default
	obj._InternalUser = v.InternalUser
	obj._FnfUser = v.FnfUser
	return nil
}

func NewConnectedAccountUserGroupParams() (_obj *ConnectedAccountUserGroupParams, _setters map[string]dynconf.SetFunc) {
	_obj = &ConnectedAccountUserGroupParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *ConnectedAccountUserGroupParams) Init() {
	newObj, _ := NewConnectedAccountUserGroupParams()
	*obj = *newObj
}

func (obj *ConnectedAccountUserGroupParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ConnectedAccountUserGroupParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ConnectedAccountUserGroupParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *ConnectedAccountUserGroupParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ConnectedAccountUserGroupParams) setDynamicField(v *config.ConnectedAccountUserGroupParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ConnectedAccountUserGroupParams) setDynamicFields(v *config.ConnectedAccountUserGroupParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *ConnectedAccountUserGroupParams) setStaticFields(v *config.ConnectedAccountUserGroupParams) error {

	obj._IsAARestricted = v.IsAARestricted
	obj._AllowedUserGroupForAA = v.AllowedUserGroupForAA
	return nil
}

func NewAaParams() (_obj *AaParams, _setters map[string]dynconf.SetFunc) {
	_obj = &AaParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["purgingbatchwindow"] = _obj.SetPurgingBatchWindow
	_setters["newdatasyncleaseduration"] = _obj.SetNewDataSyncLeaseDuration
	_setters["datapurgingleaseduration"] = _obj.SetDataPurgingLeaseDuration
	return _obj, _setters
}

func (obj *AaParams) Init() {
	newObj, _ := NewAaParams()
	*obj = *newObj
}

func (obj *AaParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AaParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AaParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *AaParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AaParams) setDynamicField(v *config.AaParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "purgingbatchwindow":
		return obj.SetPurgingBatchWindow(v.PurgingBatchWindow, true, nil)
	case "newdatasyncleaseduration":
		return obj.SetNewDataSyncLeaseDuration(v.NewDataSyncLeaseDuration, true, nil)
	case "datapurgingleaseduration":
		return obj.SetDataPurgingLeaseDuration(v.DataPurgingLeaseDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AaParams) setDynamicFields(v *config.AaParams, dynamic bool, path []string) (err error) {

	err = obj.SetPurgingBatchWindow(v.PurgingBatchWindow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNewDataSyncLeaseDuration(v.NewDataSyncLeaseDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDataPurgingLeaseDuration(v.DataPurgingLeaseDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AaParams) setStaticFields(v *config.AaParams) error {

	obj._DataPurgingStartTime = v.DataPurgingStartTime
	obj._DataPurgingEndTime = v.DataPurgingEndTime
	obj._DataSyncLockPrefix = v.DataSyncLockPrefix
	obj._PgdbConnAlias = v.PgdbConnAlias
	return nil
}

func (obj *AaParams) SetPurgingBatchWindow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *AaParams.PurgingBatchWindow", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._PurgingBatchWindow, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PurgingBatchWindow")
	}
	return nil
}
func (obj *AaParams) SetNewDataSyncLeaseDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *AaParams.NewDataSyncLeaseDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._NewDataSyncLeaseDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NewDataSyncLeaseDuration")
	}
	return nil
}
func (obj *AaParams) SetDataPurgingLeaseDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *AaParams.DataPurgingLeaseDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DataPurgingLeaseDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DataPurgingLeaseDuration")
	}
	return nil
}

func NewNameCheckParamsForAddFunds() (_obj *NameCheckParamsForAddFunds, _setters map[string]dynconf.SetFunc) {
	_obj = &NameCheckParamsForAddFunds{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *NameCheckParamsForAddFunds) Init() {
	newObj, _ := NewNameCheckParamsForAddFunds()
	*obj = *newObj
}

func (obj *NameCheckParamsForAddFunds) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NameCheckParamsForAddFunds) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NameCheckParamsForAddFunds)
	if !ok {
		return fmt.Errorf("invalid data type %v *NameCheckParamsForAddFunds", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NameCheckParamsForAddFunds) setDynamicField(v *config.NameCheckParamsForAddFunds, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NameCheckParamsForAddFunds) setDynamicFields(v *config.NameCheckParamsForAddFunds, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *NameCheckParamsForAddFunds) setStaticFields(v *config.NameCheckParamsForAddFunds) error {

	obj._IsNameCheckRestricted = v.IsNameCheckRestricted
	obj._AllowedUserGroupForNameCheck = v.AllowedUserGroupForNameCheck
	return nil
}

func NewSavingsLedgerReconCacheConfig() (_obj *SavingsLedgerReconCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SavingsLedgerReconCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *SavingsLedgerReconCacheConfig) Init() {
	newObj, _ := NewSavingsLedgerReconCacheConfig()
	*obj = *newObj
}

func (obj *SavingsLedgerReconCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SavingsLedgerReconCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SavingsLedgerReconCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *SavingsLedgerReconCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SavingsLedgerReconCacheConfig) setDynamicField(v *config.SavingsLedgerReconCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SavingsLedgerReconCacheConfig) setDynamicFields(v *config.SavingsLedgerReconCacheConfig, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *SavingsLedgerReconCacheConfig) setStaticFields(v *config.SavingsLedgerReconCacheConfig) error {

	obj._IsCachingEnabled = v.IsCachingEnabled
	obj._SavingsLedgerReconPrefix = v.SavingsLedgerReconPrefix
	obj._CacheTTl = v.CacheTTl
	return nil
}

func NewOrderCacheConfig() (_obj *OrderCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OrderCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled

	_obj._UseCaseToCacheConfigMap = &syncmap.Map[string, *CacheConfig]{}
	_setters["usecasetocacheconfigmap"] = _obj.SetUseCaseToCacheConfigMap
	return _obj, _setters
}

func (obj *OrderCacheConfig) Init() {
	newObj, _ := NewOrderCacheConfig()
	*obj = *newObj
}

func (obj *OrderCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OrderCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OrderCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OrderCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OrderCacheConfig) setDynamicField(v *config.OrderCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "usecasetocacheconfigmap":
		return obj.SetUseCaseToCacheConfigMap(v.UseCaseToCacheConfigMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OrderCacheConfig) setDynamicFields(v *config.OrderCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseCaseToCacheConfigMap(v.UseCaseToCacheConfigMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OrderCacheConfig) setStaticFields(v *config.OrderCacheConfig) error {

	obj._RedisOptions = v.RedisOptions
	return nil
}

func (obj *OrderCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OrderCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *OrderCacheConfig) SetUseCaseToCacheConfigMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.CacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OrderCacheConfig.UseCaseToCacheConfigMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._UseCaseToCacheConfigMap, v, dynamic, path)

}

func NewCacheConfig() (_obj *CacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_setters["cachettl"] = _obj.SetCacheTTL
	return _obj, _setters
}

func (obj *CacheConfig) Init() {
	newObj, _ := NewCacheConfig()
	*obj = *newObj
}

func (obj *CacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CacheConfig) setDynamicField(v *config.CacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "cachettl":
		return obj.SetCacheTTL(v.CacheTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CacheConfig) setDynamicFields(v *config.CacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTL(v.CacheTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CacheConfig) setStaticFields(v *config.CacheConfig) error {

	return nil
}

func (obj *CacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *CacheConfig) SetCacheTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CacheConfig.CacheTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTL")
	}
	return nil
}

func NewTransactionCacheConfig() (_obj *TransactionCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &TransactionCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled

	_obj._UseCaseToCacheConfigMap = &syncmap.Map[string, *CacheConfig]{}
	_setters["usecasetocacheconfigmap"] = _obj.SetUseCaseToCacheConfigMap
	return _obj, _setters
}

func (obj *TransactionCacheConfig) Init() {
	newObj, _ := NewTransactionCacheConfig()
	*obj = *newObj
}

func (obj *TransactionCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TransactionCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.TransactionCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *TransactionCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TransactionCacheConfig) setDynamicField(v *config.TransactionCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "usecasetocacheconfigmap":
		return obj.SetUseCaseToCacheConfigMap(v.UseCaseToCacheConfigMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TransactionCacheConfig) setDynamicFields(v *config.TransactionCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseCaseToCacheConfigMap(v.UseCaseToCacheConfigMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TransactionCacheConfig) setStaticFields(v *config.TransactionCacheConfig) error {

	obj._RedisOptions = v.RedisOptions
	return nil
}

func (obj *TransactionCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TransactionCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *TransactionCacheConfig) SetUseCaseToCacheConfigMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.CacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *TransactionCacheConfig.UseCaseToCacheConfigMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._UseCaseToCacheConfigMap, v, dynamic, path)

}

func NewReconParams() (_obj *ReconParams, _setters map[string]dynconf.SetFunc) {
	_obj = &ReconParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *ReconParams) Init() {
	newObj, _ := NewReconParams()
	*obj = *newObj
}

func (obj *ReconParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ReconParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ReconParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReconParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ReconParams) setDynamicField(v *config.ReconParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ReconParams) setDynamicFields(v *config.ReconParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *ReconParams) setStaticFields(v *config.ReconParams) error {

	obj._TimeLimitForExhaustedAttempt = v.TimeLimitForExhaustedAttempt
	obj._IsReconRestricted = v.IsReconRestricted
	obj._AllowedUserGroupForRecon = v.AllowedUserGroupForRecon
	return nil
}

func NewFeatureFlags() (_obj *FeatureFlags, _setters map[string]dynconf.SetFunc) {
	_obj = &FeatureFlags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablehandleaddfundswithoutfirstlegtxn"] = _obj.SetEnableHandleAddFundsWithoutFirstLegTxn
	_setters["includepartnerrefidtodedupetxninrecon"] = _obj.SetIncludePartnerRefIdToDedupeTxnInRecon
	_setters["disablecommsforp2pfundtransfer"] = _obj.SetDisableCommsForP2PFundTransfer
	_setters["enablepgdbdaoforaa"] = _obj.SetEnablePGDBDaoForAA
	_setters["enableoffappupiflow"] = _obj.SetEnableOffAppUpiFlow
	_setters["redirecttoamountscreenfromcollectpn"] = _obj.SetRedirectToAmountScreenFromCollectPN
	_setters["enabledcamcchargescashbackbanner"] = _obj.SetEnableDcAmcChargesCashbackBanner
	_PercentageRollOutForTimelineOptimisation, _fieldSetters := genconfig.NewStickinessConstraintConfig()
	_obj._PercentageRollOutForTimelineOptimisation = _PercentageRollOutForTimelineOptimisation
	helper.AddFieldSetters("percentagerolloutfortimelineoptimisation", _fieldSetters, _setters)
	_EnableOptimisationForGetTotalTransactionCountParams, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._EnableOptimisationForGetTotalTransactionCountParams = _EnableOptimisationForGetTotalTransactionCountParams
	helper.AddFieldSetters("enableoptimisationforgettotaltransactioncountparams", _fieldSetters, _setters)
	_EnableFallbackForGetTotalTransactionCountToCrdb, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._EnableFallbackForGetTotalTransactionCountToCrdb = _EnableFallbackForGetTotalTransactionCountToCrdb
	helper.AddFieldSetters("enablefallbackforgettotaltransactioncounttocrdb", _fieldSetters, _setters)
	_EnableOptimisationForGetTotalTransactionAmountParams, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._EnableOptimisationForGetTotalTransactionAmountParams = _EnableOptimisationForGetTotalTransactionAmountParams
	helper.AddFieldSetters("enableoptimisationforgettotaltransactionamountparams", _fieldSetters, _setters)
	_EnableFallbackForGetTotalTransactionAmountToCrdb, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._EnableFallbackForGetTotalTransactionAmountToCrdb = _EnableFallbackForGetTotalTransactionAmountToCrdb
	helper.AddFieldSetters("enablefallbackforgettotaltransactionamounttocrdb", _fieldSetters, _setters)
	_EnableParameterizedTimelineEvents, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._EnableParameterizedTimelineEvents = _EnableParameterizedTimelineEvents
	helper.AddFieldSetters("enableparameterizedtimelineevents", _fieldSetters, _setters)
	_BypassTPAPCoolDown, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._BypassTPAPCoolDown = _BypassTPAPCoolDown
	helper.AddFieldSetters("bypasstpapcooldown", _fieldSetters, _setters)
	_EnableAllTransactionsForNoOpFailures, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._EnableAllTransactionsForNoOpFailures = _EnableAllTransactionsForNoOpFailures
	helper.AddFieldSetters("enablealltransactionsfornoopfailures", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *FeatureFlags) Init() {
	newObj, _ := NewFeatureFlags()
	*obj = *newObj
}

func (obj *FeatureFlags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FeatureFlags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FeatureFlags)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FeatureFlags) setDynamicField(v *config.FeatureFlags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablehandleaddfundswithoutfirstlegtxn":
		return obj.SetEnableHandleAddFundsWithoutFirstLegTxn(v.EnableHandleAddFundsWithoutFirstLegTxn, true, nil)
	case "includepartnerrefidtodedupetxninrecon":
		return obj.SetIncludePartnerRefIdToDedupeTxnInRecon(v.IncludePartnerRefIdToDedupeTxnInRecon, true, nil)
	case "disablecommsforp2pfundtransfer":
		return obj.SetDisableCommsForP2PFundTransfer(v.DisableCommsForP2PFundTransfer, true, nil)
	case "enablepgdbdaoforaa":
		return obj.SetEnablePGDBDaoForAA(v.EnablePGDBDaoForAA, true, nil)
	case "enableoffappupiflow":
		return obj.SetEnableOffAppUpiFlow(v.EnableOffAppUpiFlow, true, nil)
	case "redirecttoamountscreenfromcollectpn":
		return obj.SetRedirectToAmountScreenFromCollectPN(v.RedirectToAmountScreenFromCollectPN, true, nil)
	case "enabledcamcchargescashbackbanner":
		return obj.SetEnableDcAmcChargesCashbackBanner(v.EnableDcAmcChargesCashbackBanner, true, nil)
	case "percentagerolloutfortimelineoptimisation":
		return obj._PercentageRollOutForTimelineOptimisation.Set(v.PercentageRollOutForTimelineOptimisation, true, path)
	case "enableoptimisationforgettotaltransactioncountparams":
		return obj._EnableOptimisationForGetTotalTransactionCountParams.Set(v.EnableOptimisationForGetTotalTransactionCountParams, true, path)
	case "enablefallbackforgettotaltransactioncounttocrdb":
		return obj._EnableFallbackForGetTotalTransactionCountToCrdb.Set(v.EnableFallbackForGetTotalTransactionCountToCrdb, true, path)
	case "enableoptimisationforgettotaltransactionamountparams":
		return obj._EnableOptimisationForGetTotalTransactionAmountParams.Set(v.EnableOptimisationForGetTotalTransactionAmountParams, true, path)
	case "enablefallbackforgettotaltransactionamounttocrdb":
		return obj._EnableFallbackForGetTotalTransactionAmountToCrdb.Set(v.EnableFallbackForGetTotalTransactionAmountToCrdb, true, path)
	case "enableparameterizedtimelineevents":
		return obj._EnableParameterizedTimelineEvents.Set(v.EnableParameterizedTimelineEvents, true, path)
	case "bypasstpapcooldown":
		return obj._BypassTPAPCoolDown.Set(v.BypassTPAPCoolDown, true, path)
	case "enablealltransactionsfornoopfailures":
		return obj._EnableAllTransactionsForNoOpFailures.Set(v.EnableAllTransactionsForNoOpFailures, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FeatureFlags) setDynamicFields(v *config.FeatureFlags, dynamic bool, path []string) (err error) {

	err = obj.SetEnableHandleAddFundsWithoutFirstLegTxn(v.EnableHandleAddFundsWithoutFirstLegTxn, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIncludePartnerRefIdToDedupeTxnInRecon(v.IncludePartnerRefIdToDedupeTxnInRecon, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableCommsForP2PFundTransfer(v.DisableCommsForP2PFundTransfer, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnablePGDBDaoForAA(v.EnablePGDBDaoForAA, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableOffAppUpiFlow(v.EnableOffAppUpiFlow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRedirectToAmountScreenFromCollectPN(v.RedirectToAmountScreenFromCollectPN, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableDcAmcChargesCashbackBanner(v.EnableDcAmcChargesCashbackBanner, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._PercentageRollOutForTimelineOptimisation.Set(v.PercentageRollOutForTimelineOptimisation, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableOptimisationForGetTotalTransactionCountParams.Set(v.EnableOptimisationForGetTotalTransactionCountParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableFallbackForGetTotalTransactionCountToCrdb.Set(v.EnableFallbackForGetTotalTransactionCountToCrdb, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableOptimisationForGetTotalTransactionAmountParams.Set(v.EnableOptimisationForGetTotalTransactionAmountParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableFallbackForGetTotalTransactionAmountToCrdb.Set(v.EnableFallbackForGetTotalTransactionAmountToCrdb, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableParameterizedTimelineEvents.Set(v.EnableParameterizedTimelineEvents, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BypassTPAPCoolDown.Set(v.BypassTPAPCoolDown, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableAllTransactionsForNoOpFailures.Set(v.EnableAllTransactionsForNoOpFailures, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FeatureFlags) setStaticFields(v *config.FeatureFlags) error {

	obj._DedupeByDedupeIdEnable = v.DedupeByDedupeIdEnable
	obj._EnableB2CCallbackSignalToCelestial = v.EnableB2CCallbackSignalToCelestial
	obj._EnableOnAppEnachExecutionInboundNotifProcessor = v.EnableOnAppEnachExecutionInboundNotifProcessor
	obj._EnableRemitterInfo = v.EnableRemitterInfo
	obj._AllowedUserGroupForTimelineOptimization = v.AllowedUserGroupForTimelineOptimization
	obj._AllowedUserGroupForSuccessfulOrderWithTxnFetchOptimization = v.AllowedUserGroupForSuccessfulOrderWithTxnFetchOptimization
	obj._EnableTotalAmountUserCheck = v.EnableTotalAmountUserCheck
	obj._EnableBypassCooldownForWhitelistedUser = v.EnableBypassCooldownForWhitelistedUser
	obj._EnableAllTransactionForSelectedOrderStatesParams = v.EnableAllTransactionForSelectedOrderStatesParams
	obj._EnableRecentActivityForDifferentTxns = v.EnableRecentActivityForDifferentTxns
	obj._EnableDbOptimisationForAllTransactionsFlow = v.EnableDbOptimisationForAllTransactionsFlow
	return nil
}

func (obj *FeatureFlags) SetEnableHandleAddFundsWithoutFirstLegTxn(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableHandleAddFundsWithoutFirstLegTxn", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableHandleAddFundsWithoutFirstLegTxn, 1)
	} else {
		atomic.StoreUint32(&obj._EnableHandleAddFundsWithoutFirstLegTxn, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableHandleAddFundsWithoutFirstLegTxn")
	}
	return nil
}
func (obj *FeatureFlags) SetIncludePartnerRefIdToDedupeTxnInRecon(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.IncludePartnerRefIdToDedupeTxnInRecon", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IncludePartnerRefIdToDedupeTxnInRecon, 1)
	} else {
		atomic.StoreUint32(&obj._IncludePartnerRefIdToDedupeTxnInRecon, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IncludePartnerRefIdToDedupeTxnInRecon")
	}
	return nil
}
func (obj *FeatureFlags) SetDisableCommsForP2PFundTransfer(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.DisableCommsForP2PFundTransfer", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableCommsForP2PFundTransfer, 1)
	} else {
		atomic.StoreUint32(&obj._DisableCommsForP2PFundTransfer, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableCommsForP2PFundTransfer")
	}
	return nil
}
func (obj *FeatureFlags) SetEnablePGDBDaoForAA(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnablePGDBDaoForAA", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnablePGDBDaoForAA, 1)
	} else {
		atomic.StoreUint32(&obj._EnablePGDBDaoForAA, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnablePGDBDaoForAA")
	}
	return nil
}
func (obj *FeatureFlags) SetEnableOffAppUpiFlow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableOffAppUpiFlow", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableOffAppUpiFlow, 1)
	} else {
		atomic.StoreUint32(&obj._EnableOffAppUpiFlow, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableOffAppUpiFlow")
	}
	return nil
}
func (obj *FeatureFlags) SetRedirectToAmountScreenFromCollectPN(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.RedirectToAmountScreenFromCollectPN", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._RedirectToAmountScreenFromCollectPN, 1)
	} else {
		atomic.StoreUint32(&obj._RedirectToAmountScreenFromCollectPN, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "RedirectToAmountScreenFromCollectPN")
	}
	return nil
}
func (obj *FeatureFlags) SetEnableDcAmcChargesCashbackBanner(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnableDcAmcChargesCashbackBanner", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableDcAmcChargesCashbackBanner, 1)
	} else {
		atomic.StoreUint32(&obj._EnableDcAmcChargesCashbackBanner, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableDcAmcChargesCashbackBanner")
	}
	return nil
}

func NewEnableRemitterInfo() (_obj *EnableRemitterInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &EnableRemitterInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *EnableRemitterInfo) Init() {
	newObj, _ := NewEnableRemitterInfo()
	*obj = *newObj
}

func (obj *EnableRemitterInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EnableRemitterInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EnableRemitterInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnableRemitterInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EnableRemitterInfo) setDynamicField(v *config.EnableRemitterInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EnableRemitterInfo) setDynamicFields(v *config.EnableRemitterInfo, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *EnableRemitterInfo) setStaticFields(v *config.EnableRemitterInfo) error {

	obj._IsRestricted = v.IsRestricted
	obj._AllowedUserGroupsForRemitterInfo = v.AllowedUserGroupsForRemitterInfo
	return nil
}

func NewDelayRangeForBalanceV1() (_obj *DelayRangeForBalanceV1, _setters map[string]dynconf.SetFunc) {
	_obj = &DelayRangeForBalanceV1{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *DelayRangeForBalanceV1) Init() {
	newObj, _ := NewDelayRangeForBalanceV1()
	*obj = *newObj
}

func (obj *DelayRangeForBalanceV1) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DelayRangeForBalanceV1) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DelayRangeForBalanceV1)
	if !ok {
		return fmt.Errorf("invalid data type %v *DelayRangeForBalanceV1", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DelayRangeForBalanceV1) setDynamicField(v *config.DelayRangeForBalanceV1, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DelayRangeForBalanceV1) setDynamicFields(v *config.DelayRangeForBalanceV1, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *DelayRangeForBalanceV1) setStaticFields(v *config.DelayRangeForBalanceV1) error {

	obj._MinimumDelay = v.MinimumDelay
	obj._MaximumDelay = v.MaximumDelay
	return nil
}

func NewInboundNotificationParams() (_obj *InboundNotificationParams, _setters map[string]dynconf.SetFunc) {
	_obj = &InboundNotificationParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *InboundNotificationParams) Init() {
	newObj, _ := NewInboundNotificationParams()
	*obj = *newObj
}

func (obj *InboundNotificationParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InboundNotificationParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.InboundNotificationParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *InboundNotificationParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InboundNotificationParams) setDynamicField(v *config.InboundNotificationParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InboundNotificationParams) setDynamicFields(v *config.InboundNotificationParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *InboundNotificationParams) setStaticFields(v *config.InboundNotificationParams) error {

	obj._BalanceRefreshDelay = v.BalanceRefreshDelay
	return nil
}

func NewErrorRespCodesForPermanentFailure() (_obj *ErrorRespCodesForPermanentFailure, _setters map[string]dynconf.SetFunc) {
	_obj = &ErrorRespCodesForPermanentFailure{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *ErrorRespCodesForPermanentFailure) Init() {
	newObj, _ := NewErrorRespCodesForPermanentFailure()
	*obj = *newObj
}

func (obj *ErrorRespCodesForPermanentFailure) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ErrorRespCodesForPermanentFailure) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ErrorRespCodesForPermanentFailure)
	if !ok {
		return fmt.Errorf("invalid data type %v *ErrorRespCodesForPermanentFailure", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ErrorRespCodesForPermanentFailure) setDynamicField(v *config.ErrorRespCodesForPermanentFailure, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ErrorRespCodesForPermanentFailure) setDynamicFields(v *config.ErrorRespCodesForPermanentFailure, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *ErrorRespCodesForPermanentFailure) setStaticFields(v *config.ErrorRespCodesForPermanentFailure) error {

	obj._FundTransferCallBackErrRespCodesForVendorMap = v.FundTransferCallBackErrRespCodesForVendorMap
	obj._B2CFundTransferPermanentFailureRespCodesToVendorMap = v.B2CFundTransferPermanentFailureRespCodesToVendorMap
	return nil
}

func NewB2CPaymentParams() (_obj *B2CPaymentParams, _setters map[string]dynconf.SetFunc) {
	_obj = &B2CPaymentParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *B2CPaymentParams) Init() {
	newObj, _ := NewB2CPaymentParams()
	*obj = *newObj
}

func (obj *B2CPaymentParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *B2CPaymentParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.B2CPaymentParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *B2CPaymentParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *B2CPaymentParams) setDynamicField(v *config.B2CPaymentParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *B2CPaymentParams) setDynamicFields(v *config.B2CPaymentParams, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *B2CPaymentParams) setStaticFields(v *config.B2CPaymentParams) error {

	obj._ProcessB2cPaymentLock = v.ProcessB2cPaymentLock
	obj._ProcessB2cPaymentLockLeaseDuration = v.ProcessB2cPaymentLockLeaseDuration
	return nil
}

func NewDcForexRefundInfo() (_obj *DcForexRefundInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &DcForexRefundInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *DcForexRefundInfo) Init() {
	newObj, _ := NewDcForexRefundInfo()
	*obj = *newObj
}

func (obj *DcForexRefundInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DcForexRefundInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DcForexRefundInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *DcForexRefundInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DcForexRefundInfo) setDynamicField(v *config.DcForexRefundInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DcForexRefundInfo) setDynamicFields(v *config.DcForexRefundInfo, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *DcForexRefundInfo) setStaticFields(v *config.DcForexRefundInfo) error {

	obj._RefundInProgressLayout = v.RefundInProgressLayout
	obj._RefundUnderReviewLayout = v.RefundUnderReviewLayout
	obj._RefundCompletedLayout = v.RefundCompletedLayout
	obj._RefundCompletedWithoutInfo = v.RefundCompletedWithoutInfo
	obj._IncomingRefundLayout = v.IncomingRefundLayout
	obj._IncomingRefundLayoutWithoutInfo = v.IncomingRefundLayoutWithoutInfo
	return nil
}

func NewCommsConfig() (_obj *CommsConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CommsConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *CommsConfig) Init() {
	newObj, _ := NewCommsConfig()
	*obj = *newObj
}

func (obj *CommsConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CommsConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CommsConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CommsConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CommsConfig) setDynamicField(v *config.CommsConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CommsConfig) setDynamicFields(v *config.CommsConfig, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *CommsConfig) setStaticFields(v *config.CommsConfig) error {

	obj._ExcludeUsersForEmail = v.ExcludeUsersForEmail
	return nil
}

func NewReportFraudConfig() (_obj *ReportFraudConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ReportFraudConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	return _obj, _setters
}

func (obj *ReportFraudConfig) Init() {
	newObj, _ := NewReportFraudConfig()
	*obj = *newObj
}

func (obj *ReportFraudConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ReportFraudConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ReportFraudConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReportFraudConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ReportFraudConfig) setDynamicField(v *config.ReportFraudConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ReportFraudConfig) setDynamicFields(v *config.ReportFraudConfig, dynamic bool, path []string) (err error) {

	return nil
}

func (obj *ReportFraudConfig) setStaticFields(v *config.ReportFraudConfig) error {

	obj._TransactionProtocolToMaxDaysAvailableForReportFraud = v.TransactionProtocolToMaxDaysAvailableForReportFraud
	return nil
}

func NewRewardsInfo() (_obj *RewardsInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardsInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["dpandafistoreorderhistorytargeturl"] = _obj.SetDpandaFiStoreOrderHistoryTargetUrl
	_obj._DpandaFiStoreOrderHistoryTargetUrlMutex = &sync.RWMutex{}
	_setters["poshvinegiftcardstoreorderhistorytargeturl"] = _obj.SetPoshvineGiftCardStoreOrderHistoryTargetUrl
	_obj._PoshvineGiftCardStoreOrderHistoryTargetUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *RewardsInfo) Init() {
	newObj, _ := NewRewardsInfo()
	*obj = *newObj
}

func (obj *RewardsInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RewardsInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RewardsInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RewardsInfo) setDynamicField(v *config.RewardsInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "dpandafistoreorderhistorytargeturl":
		return obj.SetDpandaFiStoreOrderHistoryTargetUrl(v.DpandaFiStoreOrderHistoryTargetUrl, true, nil)
	case "poshvinegiftcardstoreorderhistorytargeturl":
		return obj.SetPoshvineGiftCardStoreOrderHistoryTargetUrl(v.PoshvineGiftCardStoreOrderHistoryTargetUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RewardsInfo) setDynamicFields(v *config.RewardsInfo, dynamic bool, path []string) (err error) {

	err = obj.SetDpandaFiStoreOrderHistoryTargetUrl(v.DpandaFiStoreOrderHistoryTargetUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPoshvineGiftCardStoreOrderHistoryTargetUrl(v.PoshvineGiftCardStoreOrderHistoryTargetUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RewardsInfo) setStaticFields(v *config.RewardsInfo) error {

	return nil
}

func (obj *RewardsInfo) SetDpandaFiStoreOrderHistoryTargetUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsInfo.DpandaFiStoreOrderHistoryTargetUrl", reflect.TypeOf(val))
	}
	obj._DpandaFiStoreOrderHistoryTargetUrlMutex.Lock()
	defer obj._DpandaFiStoreOrderHistoryTargetUrlMutex.Unlock()
	obj._DpandaFiStoreOrderHistoryTargetUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DpandaFiStoreOrderHistoryTargetUrl")
	}
	return nil
}
func (obj *RewardsInfo) SetPoshvineGiftCardStoreOrderHistoryTargetUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsInfo.PoshvineGiftCardStoreOrderHistoryTargetUrl", reflect.TypeOf(val))
	}
	obj._PoshvineGiftCardStoreOrderHistoryTargetUrlMutex.Lock()
	defer obj._PoshvineGiftCardStoreOrderHistoryTargetUrlMutex.Unlock()
	obj._PoshvineGiftCardStoreOrderHistoryTargetUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PoshvineGiftCardStoreOrderHistoryTargetUrl")
	}
	return nil
}
