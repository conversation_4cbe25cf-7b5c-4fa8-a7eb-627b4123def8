package order_test

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	mocks2 "github.com/epifi/be-common/pkg/queue/mocks"
	config "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/dao/mocks"
	"github.com/epifi/gamma/order/internal/order"
	"github.com/epifi/gamma/order/test"
)

var (
	conf *config.Config
)

type mockedDependencies struct {
	orderDao             *mocks.MockOrderDao
	orderUpdatePublisher *mocks2.MockPublisher
}

func newProcessorWithMocks(t *testing.T) (*order.OrderProcessor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)
	mockOrderDao := mocks.NewMockOrderDao(ctr)
	mockOrderUpdatePublisher := mocks2.NewMockPublisher(ctr)

	md := &mockedDependencies{
		orderDao:             mockOrderDao,
		orderUpdatePublisher: mockOrderUpdatePublisher,
	}

	svc := order.NewOrderProcessor(
		mockOrderDao,
		mockOrderUpdatePublisher,
		nil,
	)

	return svc, md, func() {
		ctr.Finish()
	}
}

// nolint:dogsled
func TestMain(m *testing.M) {
	var teardown func()
	_, conf, _, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
