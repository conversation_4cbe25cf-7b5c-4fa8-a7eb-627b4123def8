package consumer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"testing"

	"github.com/epifi/be-common/api/queue"
	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/creditreportv2/consumer"
	providerMocks "github.com/epifi/gamma/creditreportv2/consumer/provider/mocks"
	daoMocks "github.com/epifi/gamma/creditreportv2/dao/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/golang/mock/gomock"
)

func TestCreditReportConsumerService_ProcessCreditReportFlattening(t *testing.T) {
	c := &CreditReportConsumerService{}
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	creditReportRawMock := daoMocks.NewMockCreditReportsRawDao(ctrl)
	c.creditReportRawDao = creditReportRawMock

	factoryMock := providerMocks.NewMockIFactory(ctrl)
	c.crFlattenFactory = factoryMock

	cibilProviderMock := providerMocks.NewMockIReportFlatteningProvider(ctrl)
	factoryMock.EXPECT().GetReportFlatteningProvider(gomock.Any(), gomock.Any()).Return(cibilProviderMock, nil).AnyTimes()
	cibilProviderMock.EXPECT().FlattenCreditReport(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	type args struct {
		ctx context.Context
		req *consumer.ProcessCreditReportFlatteningRequest
	}

	type mockDao struct {
		res *creditReportPb.CreditReportRaw
		err error
	}

	tests := []struct {
		name    string
		args    args
		want    *consumer.ProcessCreditReportFlatteningResponse
		wantErr bool
		mockDao *mockDao
	}{
		{
			name: "Flatten credit report successfully",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessCreditReportFlatteningRequest{
					RequestHeader: nil,
					RawReportId:   "raw-report-id-1",
				},
			},
			want: &consumer.ProcessCreditReportFlatteningResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
			mockDao: &mockDao{
				res: &creditReportPb.CreditReportRaw{
					Vendor: commonvgpb.Vendor_CIBIL,
				},
				err: nil,
			},
		},
		{
			name: "Failed to flatten credit report as no such report exists",
			args: args{
				ctx: context.Background(),
				req: &consumer.ProcessCreditReportFlatteningRequest{
					RequestHeader: nil,
					RawReportId:   "random-raw-report-id",
				},
			},
			want: &consumer.ProcessCreditReportFlatteningResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE},
			},
			wantErr: false,
			mockDao: &mockDao{
				res: nil,
				err: epifierrors.ErrRecordNotFound,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockDao != nil {
				creditReportRawMock.EXPECT().GetById(tt.args.ctx, tt.args.req.RawReportId).Return(tt.mockDao.res, tt.mockDao.err)
			}
			got, err := c.ProcessCreditReportFlattening(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessCreditReportFlattening() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessCreditReportFlattening() got = %v, want %v", got, tt.want)
			}
		})
	}
}
