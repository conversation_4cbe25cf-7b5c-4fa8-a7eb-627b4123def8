package model

import (
	"time"

	pb "github.com/epifi/gamma/api/rms/manager"
)

// RuleSubscription will contain all the subscriptions made for rules
// and user specific data and preferences that would be required for rule evaluation
type RuleSubscription struct {
	// subscription Identifier
	ID string `gorm:"type:uuid;default:gen_random_uuid();"`

	// Rule to which actor has subscribed
	RuleId string `gorm:"not null"`

	// Id of the actor who have subscribed to the rule
	ActorId string `gorm:"not null"`

	// State of subscription: Active/Inactive
	State pb.RuleSubscriptionState `gorm:"not null"`

	// rule activation timestamp
	ValidFrom *time.Time `gorm:"default:now();not null"`

	// time when the rule validation will expire
	ValidTill *time.Time

	// JSON of user defined values, provided on subscribing the rule
	RuleParamValues *pb.RuleParamValues `gorm:"not null;default:'{}'"`

	// rule creation timestamp
	CreatedAt *time.Time `gorm:"not null"`

	// last updated timestamp
	UpdatedAt *time.Time `gorm:"not null"`

	// timestamp at which rule was deleted
	// signifies the date of soft deletion of the entry.
	// non null value means the record is deleted.
	DeletedAt *time.Time

	SubscriptionExpiryData *pb.SubscriptionExpiryData

	// cutoff params are those params which affects execution of subscription
	// eg: player name, deposit amount, state of subscription (Active/Inactive)
	// SD Name is not a cutoff param
	// editing of cutoff params in cases where rewards are associated will be required to check for some deadline related stuff
	CutoffParamUpdatedAt *time.Time `gorm:"not null;default:now();"`

	// unique UUID for subscriptions
	VersionId string `gorm:"type:uuid;default:gen_random_uuid();"`

	// Defines whether a version is CURRENT or has EXPIRED
	VersionState pb.SubscriptionVersionState

	// Start timestamp of version
	VersionValidFrom *time.Time `gorm:"not null;default:now()"`

	// End timestamp of version
	// version with state CURRENT will have nil value for VersionValidTill
	VersionValidTill *time.Time

	// this field captures reason for changing subscription state
	// it is required to know the reason, as state can be changed by system as well
	// eg: SD pre-closure, Insufficient balance etc.
	// for such cases we should know the reason in order to perform to perform some action on UI
	// select new SD, Create SD, Add funds etc
	StateChangeReason pb.SubscriptionStateChangeReason

	StateChangeProvenance pb.SubscriptionStateChangeProvenance

	// current execution state of the subscription
	ExecutionState pb.SubscriptionExecutionState `gorm:"not null"`
	// this is an optional field
	// if not nil, then unique constraint will be applied on this field
	ClientRequestId string `gorm:"default:null"`
}

func (RuleSubscription) TableName() string {
	return "rule_subscriptions"
}

func (RuleSubscription) GetCutoffParamUpdatedAtColumnName() string {
	return "cutoff_param_updated_at"
}

func VersionValidTillColumnName() string {
	return "version_valid_till"
}

func VersionValidFromColumnName() string {
	return "version_valid_from"
}

func VersionStateColumnName() string {
	return "version_state"
}
