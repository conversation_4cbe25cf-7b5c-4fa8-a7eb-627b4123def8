package fact

import (
	pb "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/rms/orchestrator/event"
)

type IGetExpressionParameterFact interface {
	GetActions() *pb.Actions
	GetCondition() *pb.Condition
	GetEventData() *event.RmsEvent
	GetEventId() string
	GetUserExpressionMap() (map[string]interface{}, error)
}

type GetExpressionParameterFact struct {
	actions           *pb.Actions
	condition         *pb.Condition
	eventData         *event.RmsEvent
	eventId           string
	userExpressionMap func() (map[string]interface{}, error)
}

func (g *GetExpressionParameterFact) GetActions() *pb.Actions {
	return g.actions
}
func (g *GetExpressionParameterFact) GetCondition() *pb.Condition {
	return g.condition
}
func (g *GetExpressionParameterFact) GetEventData() *event.RmsEvent {
	return g.eventData
}
func (g *GetExpressionParameterFact) GetEventId() string {
	return g.eventId
}
func (g *GetExpressionParameterFact) GetUserExpressionMap() (map[string]interface{}, error) {
	return g.userExpressionMap()
}

func ConvertCommonFactToGetExpressionParameterFact(commonFact *CommonFact) IGetExpressionParameterFact {
	return NewIGetExpressionParameterFact(commonFact.Actions, commonFact.GetConditionData().Condition, commonFact.GetConditionData().EventData, commonFact.GetEventId(), commonFact.GetExpressionUserParamValues)
}

func NewIGetExpressionParameterFact(actions *pb.Actions, condition *pb.Condition, eventData *event.RmsEvent, eventId string, userExpressionMap func() (map[string]interface{}, error)) IGetExpressionParameterFact {
	return &GetExpressionParameterFact{
		actions:           actions,
		condition:         condition,
		eventData:         eventData,
		eventId:           eventId,
		userExpressionMap: userExpressionMap,
	}
}
