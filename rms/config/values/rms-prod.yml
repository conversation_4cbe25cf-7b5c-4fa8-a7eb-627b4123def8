Application:
  Environment: "prod"
  Name: "rms"

Server:
  Ports:
    GrpcPort: 8098
    GrpcSecurePort: 9510
    HttpPort: 9999
    HttpPProfPort: 9990

RMSDb:
  AppName: "rms"
  DbType: "PGDB"
  StatementTimeout: 10s
  Name: "rms"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/epifimetis/rms_dev_user"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"

Aws:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: false
  UseReflectToPopulateParamMap: true
  FilterExpiredRuleSubscriptions: true

RMSEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rms-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1s
    Namespace: "fittt"

ActionExecutionUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rms-fittt-action-execution-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

CommandProcessorSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rms-command-processor-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "fittt"

CommandProcessorPublisher:
  QueueName: "prod-rms-command-processor-queue"

ActionProcessingPublisher:
  QueueName: "prod-fittt-rms-action-queue"

FitttRewardsExecutionUpdatePublisher:
  QueueName: "prod-fittt-execution-update-queue"

RmsEventPublisher:
  QueueName: "prod-rms-event-queue"

ActionProcessingPublishDelay:
  Enable: false
  DelayRangeStart: 1
  DelayRangeEnd: 900
  DisableForPurchaseAction: true

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

ExecutionsCountMaterializedViewRefreshScheduler:
  RefreshInterval: 10800 # 3hours in seconds
  RandomiseWindow: 600 # 10 minutes

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

InvestmentInstrumentEventPublisher:
  QueueName: "prod-investment-event-queue"

RmsBudgetingPublisher:
  QueueName: "prod-budgeting-reminder-queue"


ProactiveFundAdditionNotificationParams:
  EnableProactiveFundAdditionNotificationPN: false
  BatchSizeForWaitGroup: 10000

# This variable is rms orchestrator level variable used to determine the max size of rule_subscriptions
# to fetch from the rule_subscriptions table. The value of this variable should always be less than
# 'MaxPageSizeToFetchSubscriptionsForExecution' variable defined above or else it will get overwritten by the above variable in dao function.
OrchestratorPageSizeToFetchSubscriptions: 2000

RmsOrchestratorBatchPublishDelayDuration: "2m"

  # If NoOfDaysDifferenceForConsideringEventAsValid is set to NEGATIVE value, validity check for the event will NOT be performed
  # If NoOfDaysDifferenceForConsideringEventAsValid is set to a positive value, event older than T-NoOfDaysDifferenceForConsideringEventAsValid are dropped from processing
  # eg: If NoOfDaysDifferenceForConsideringEventAsValid = 1, current date is 14 June.
  # Event generated on 13 June will be processed
  # Whereas, event generated on 12 June will be dropped and order will not be created

NoOfDaysDifferenceForConsideringEventAsValid: -1
