// Code generated by MockGen. DO NOT EDIT.
// Source: factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	agg_action_info_processor "github.com/epifi/gamma/rms/activity/agg_action_info_processor"
	gomock "github.com/golang/mock/gomock"
)

// MockIAggActionInfoProcessor is a mock of IAggActionInfoProcessor interface.
type MockIAggActionInfoProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockIAggActionInfoProcessorMockRecorder
}

// MockIAggActionInfoProcessorMockRecorder is the mock recorder for MockIAggActionInfoProcessor.
type MockIAggActionInfoProcessorMockRecorder struct {
	mock *MockIAggActionInfoProcessor
}

// NewMockIAggActionInfoProcessor creates a new mock instance.
func NewMockIAggActionInfoProcessor(ctrl *gomock.Controller) *MockIAggActionInfoProcessor {
	mock := &MockIAggActionInfoProcessor{ctrl: ctrl}
	mock.recorder = &MockIAggActionInfoProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAggActionInfoProcessor) EXPECT() *MockIAggActionInfoProcessorMockRecorder {
	return m.recorder
}

// ProcessAggActionInfo mocks base method.
func (m *MockIAggActionInfoProcessor) ProcessAggActionInfo(ctx context.Context, req *agg_action_info_processor.ProcessAggActionInfoReq) (*agg_action_info_processor.ProcessAggActionInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAggActionInfo", ctx, req)
	ret0, _ := ret[0].(*agg_action_info_processor.ProcessAggActionInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAggActionInfo indicates an expected call of ProcessAggActionInfo.
func (mr *MockIAggActionInfoProcessorMockRecorder) ProcessAggActionInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAggActionInfo", reflect.TypeOf((*MockIAggActionInfoProcessor)(nil).ProcessAggActionInfo), ctx, req)
}
